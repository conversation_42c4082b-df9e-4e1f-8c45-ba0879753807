{"name": "inndex-web-ui", "private": true, "version": "0.0.2", "description": "An Angular application", "keywords": [], "dependencies": {"@angular/service-worker": "^11.2.14", "angularx-qrcode": "^11.0.0", "dayjs": "1.10.4", "exceljs": "<=1.14.0", "lottie-web": "^5.12.2", "ng2-pdfjs-viewer": "^6.0.1", "ngx-lottie": "^7.0.4", "pdf-lib": "^1.17.1", "pdfjs-dist": "2.5.207", "qrcode": "^1.3.3", "tslib": "^2.7.0"}, "devDependencies": {"@agm/core": "1.1.*", "@agm/js-marker-clusterer": "~1.1.0", "@angular-devkit/build-angular": "~0.1102.19", "@angular/animations": "^11.2.14", "@angular/cdk": "^10.2.5", "@angular/cli": "^11.2.19", "@angular/common": "^11.2.14", "@angular/compiler": "^11.2.14", "@angular/compiler-cli": "^11.2.14", "@angular/core": "^11.2.14", "@angular/forms": "^11.2.14", "@angular/language-service": "^11.2.14", "@angular/localize": "^11.2.14", "@angular/platform-browser": "^11.2.14", "@angular/platform-browser-dynamic": "^11.2.14", "@angular/router": "^11.2.14", "@fortawesome/fontawesome-free": "^5.8.0", "@microsoft/clarity": "^1.0.0", "@ng-bootstrap/ng-bootstrap": "^9.1.3", "@ng-plus/signature-pad": "1.0.7", "@ng-select/ng-select": "^6.1.0", "@sentry/browser": "5.1.0", "@swimlane/ngx-datatable": "^18.0.0", "@types/dragula": "^3.7.1", "@types/file-saver": "^2.0.1", "@types/googlemaps": "^3.40.3", "@types/jasmine": "^2.8.16", "@types/jasminewd2": "^2.0.6", "@types/node": "^12.11.1", "@videogular/ngx-videogular": "^4.2.0", "agm-spiderfier": "^1.0.1", "bootstrap": "^4.5.3", "cookieconsent": "^3.1.0", "file-saver": "^2.0.2", "husky": "^7.0.4", "jasmine-core": "~2.99.1", "jasmine-spec-reporter": "~4.2.1", "js-marker-clusterer": "^1.0.0", "karma": "^6.4.4", "karma-chrome-launcher": "~2.2.0", "karma-coverage": "^2.2.1", "karma-firefox-launcher": "^2.1.3", "karma-jasmine": "~1.1.1", "karma-jasmine-html-reporter": "^0.2.2", "ng2-dragula": "^2.1.1", "ng2-file-upload": "^1.3.0", "ngx-cookieconsent": "^2.2.3", "ngx-image-cropper": "3.1.9", "ngx-quill": "^14.3.0", "ngx-skeleton-loader": "^2.9.1", "ngx-treeview": "^10.0.2", "protractor": "^7.0.0", "puppeteer": "^13.7.0", "quill": "^1.3.7", "rxjs": "6.6.3", "ts-node": "~5.0.1", "ts-overlapping-marker-spiderfier": "~1.0.3", "tslint": "^6.*", "typescript": "~4.0.8", "zone.js": "~0.10.2"}, "scripts": {"ng": "ng", "start": "ng serve temp-angular-app --aot=false --port 8080", "start:aot": "ng serve temp-angular-app --aot=true --port 8080", "build": "npm run build:prod", "build:dev": "ng build", "build:stage": "node --max_old_space_size=6000 node_modules/@angular/cli/bin/ng build --configuration=stage", "build:dev:watch": "ng build --watch", "build:prod": "node --max_old_space_size=6000 node_modules/@angular/cli/bin/ng build --prod", "build:stats": "ng build --stats-json", "dev:watch": "node --max_old_space_size=2500 ./node_modules/@angular/cli/bin/ng serve temp-angular-app --port 8080 --watch --aot=false", "test": "ng test --watch=false --browsers=ChromeHeadlessNoSandbox --code-coverage", "fetch-blog-data": "node fetch-blog-data.js"}, "main": "app.js", "nodemonConfig": {"ignore": ["*.json"]}, "scarfSettings": {"enabled": false}, "repository": {"type": "git", "url": "git+https://bitbucket.org/inndexhq/inndex-web-ui.git"}, "author": "<PERSON><PERSON> patel", "license": "", "engines": {"node": ">=12.22"}}
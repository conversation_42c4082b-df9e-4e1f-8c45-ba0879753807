{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"temp-angular-app": {"root": "", "sourceRoot": "frontend", "projectType": "application", "prefix": "app", "schematics": {}, "i18n": {"sourceLocale": {"baseHref": "", "code": "en-GB"}, "locales": {"en-US": {"baseHref": "us", "translation": "frontend/locale/messages.us.xlf"}, "en-CA": {"baseHref": "ca", "translation": "frontend/locale/messages.ca.xlf"}, "en-AE": {"baseHref": "ae", "translation": "frontend/locale/messages.ae.xlf"}}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"aot": true, "outputPath": "dist/inndex-web-ui", "index": "frontend/index.html", "main": "frontend/main.ts", "allowedCommonJsDependencies": ["dragula", "file-saver", "lodash", "pako", "qrcode"], "polyfills": "frontend/polyfills.ts", "tsConfig": "frontend/tsconfig.app.json", "assets": ["frontend/favicon.ico", "frontend/assets", {"glob": "**/*", "input": "frontend/assets-from-sails", "output": "/"}, {"glob": "**/*", "input": "node_modules/ng2-pdfjs-viewer/pdfjs", "output": "/assets/pdfjs"}, {"glob": "pdf.worker*", "input": "frontend/assets/js/", "output": "/assets/pdfjs/build/"}], "stylePreprocessorOptions": {"includePaths": ["frontend/assets/scss"]}, "styles": ["frontend/assets/scss/im-bootstrap.scss", "node_modules/@fortawesome/fontawesome-free/css/all.min.css", "node_modules/@videogular/ngx-videogular/fonts/videogular.css", "node_modules/cookieconsent/build/cookieconsent.min.css", "frontend/styles.scss"], "scripts": ["frontend/assets/js/llqrcode.js", "frontend/assets/js/webqr.js", "node_modules/cookieconsent/build/cookieconsent.min.js"], "i18nMissingTranslation": "warning", "serviceWorker": true, "ngswConfigPath": "ngsw-config.json"}, "configurations": {"stage": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "fileReplacements": [{"replace": "frontend/environments/environment.ts", "with": "frontend/environments/environment.stage.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "localize": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true}, "production": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "fileReplacements": [{"replace": "frontend/environments/environment.ts", "with": "frontend/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "localize": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "temp-angular-app:build"}, "configurations": {"production": {"browserTarget": "temp-angular-app:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "temp-angular-app:build", "format": "xlf", "outputPath": "locale"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "frontend/test.ts", "polyfills": "frontend/polyfills.ts", "tsConfig": "frontend/tsconfig.spec.json", "karmaConfig": "frontend/karma.conf.js", "styles": ["frontend/styles.scss"], "scripts": [], "assets": ["frontend/favicon.ico", "frontend/assets"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["frontend/tsconfig.app.json", "frontend/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "temp-angular-app-e2e": {"root": "e2e/", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "temp-angular-app:serve"}, "configurations": {"production": {"devServerTarget": "temp-angular-app:serve:production"}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": "e2e/tsconfig.e2e.json", "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "temp-angular-app", "cli": {"analytics": false}}
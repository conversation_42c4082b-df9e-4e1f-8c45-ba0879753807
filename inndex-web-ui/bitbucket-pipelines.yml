image: node:12

pipelines:
  branches:
    master:
      - step:
          name: Build & Release artifacts to prod server
          deployment: production
          max-time: 20
          size: 2x
          caches:
            - node
          script:
            - npm --version && node --version
            - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
            - unzip awscliv2.zip
            - ./aws/install
            - aws --version
            - MESSAGE=$(git log -1 --pretty=%B)
            - npm i --no-audit --no-fund
            - npm run fetch-blog-data
            - npm run build:prod
            - mv ./dist/${MODULE_NAME}/en-GB/ ./dist/${MODULE_NAME}/gb/
            - mv ./dist/${MODULE_NAME}/en-US/ ./dist/${MODULE_NAME}/us/
            - mv ./dist/${MODULE_NAME}/en-CA/ ./dist/${MODULE_NAME}/ca/
            - mv ./dist/${MODULE_NAME}/en-AE/ ./dist/${MODULE_NAME}/ae/
            - 'echo "{\"hash\":\"$(git rev-parse --short HEAD)\", \"now\": \"$(date -R)\"}" > ./dist/${MODULE_NAME}/gb/version.json'
            - aws s3 sync ./dist/${MODULE_NAME} s3://${TARGET_BUCKET}/prod/ --delete
            - aws cloudfront create-invalidation --distribution-id $PROD_CDN_DISTRIBUTION_ID --paths "/*"
            - echo "Success"
            - pipe: atlassian/slack-notify:2.3.0
              variables:
                WEBHOOK_URL: '*******************************************************************************'
                MESSAGE: "prod UI: $MESSAGE"
          artifacts:
            - dist/
    staging:
      - step:
          name: Staging Unit Tests
          image: node:16
          caches:
            - node
          script:
            - sed -i 's|http://deb.debian.org/debian|http://archive.debian.org/debian|g' /etc/apt/sources.list
            - sed -i 's|http://security.debian.org|http://archive.debian.org/debian-security|g' /etc/apt/sources.list
            - echo 'Acquire::Check-Valid-Until "false";' > /etc/apt/apt.conf.d/10no-check-valid-until
            - apt-get update && apt-get install -y wget
            - wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add -
            - echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | tee /etc/apt/sources.list.d/google-chrome.list
            - apt-get update && apt-get install -y google-chrome-stable
            - npm --version && node --version && google-chrome --version
            - npm i --no-audit --no-fund
            - npm run test
            - ls -lrt && ls -lrt coverage/
          artifacts:
            - coverage/
      - step:
          name: Build & Release artifacts to stage server
          deployment: staging
          max-time: 20
          size: 2x
          caches:
            - node
          script:
            - npm --version && node --version
            - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
            - unzip awscliv2.zip
            - ./aws/install
            - aws --version
            - MESSAGE=$(git log -1 --pretty=%B)
            - npm i --no-audit --no-fund
            - npm run fetch-blog-data
            - npm run build:stage
            - mv ./dist/${MODULE_NAME}/en-GB/ ./dist/${MODULE_NAME}/gb/
            - mv ./dist/${MODULE_NAME}/en-US/ ./dist/${MODULE_NAME}/us/
            - mv ./dist/${MODULE_NAME}/en-CA/ ./dist/${MODULE_NAME}/ca/
            - mv ./dist/${MODULE_NAME}/en-AE/ ./dist/${MODULE_NAME}/ae/
            - 'echo "{\"hash\":\"$(git rev-parse --short HEAD)\", \"now\": \"$(date -R)\"}" > ./dist/${MODULE_NAME}/gb/version.json'
            - aws s3 sync ./dist/${MODULE_NAME} s3://${TARGET_BUCKET}/stage/ --delete
            - aws cloudfront create-invalidation --distribution-id $STAGE_CDN_DISTRIBUTION_ID --paths "/*"
            - echo "Success"
            - pipe: atlassian/slack-notify:2.3.0
              variables:
                WEBHOOK_URL: '*******************************************************************************'
                MESSAGE: "stage UI: $MESSAGE"
          artifacts:
            - dist/

import {BrowserModule, Title} from '@angular/platform-browser';
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NgModule} from '@angular/core';
import {NgbModule} from '@ng-bootstrap/ng-bootstrap';

import {AppComponent} from './app.component';
import {
    FooterComponent,
    NavComponent,
    ToastContainerComponent
} from './shared';
import {AppRoutingModule} from "./app-routing.module";
import {CoreModule} from "./core/core.module";
import {HomeModule} from "@app/modules/home.module";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";

import {NgcCookieConsentModule, NgcCookieConsentConfig} from 'ngx-cookieconsent';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import {SentryErrorHandler, ScriptLoaderService} from "@app/core";
import {ProjectPortalModule} from "@app/modules/project-portal/project-portal.module";
import { OnBoardingModule } from './modules/on-boarding/on-boarding.module';
import {AppConstant, environment} from '@env/environment';
import { DocumentsModule } from '@app/modules/documents/documents.module';
import { AnalyticsService } from './core/services/analytics.service';
import {ServiceWorkerModule} from '@angular/service-worker';

const cookieConfig: NgcCookieConsentConfig = {
    cookie: {
        domain: window && window.location && window.location.hostname // or 'your.domain.com' // it is mandatory to set a domain, for cookies to work properly (see https://goo.gl/S2Hy2A)
    },
    // suppress for live tv URL
    enabled: !(window && window.location && (window.location.pathname).match(/\/project-portal\/project\/[\d]+\/live-tv/)),
    "position": "bottom",
    "theme": "edgeless",
    onStatusChange: (a,b) => {
        console.log('Status changed', a, 'before', b);
        // AnalyticsService.consent(a);
    },
    "palette": {
        "popup": {
            "background": "#000000",
            "text": "#ffffff",
            "link": "#ffffff"
        },
        "button": {
            "background": "#edb61d",
            "text": "#000000",
            "border": "transparent"
        }
    },
    "type": "info",
    "content": {
        "message": "This website uses cookies to ensure you get the best experience on our website.",
        "dismiss": "Got it!",
        "deny": "Refuse cookies",
        "link": "Learn more",
        "href": AppConstant.cookiesPolicyUrl,
        "policy": "Cookie Policy"
    }
};


@NgModule({
    declarations: [
        AppComponent,
        NavComponent,
        FooterComponent,
        ToastContainerComponent,
    ],
    imports: [

        NgbModule,
        BrowserModule,
        BrowserAnimationsModule,
        NgcCookieConsentModule.forRoot(cookieConfig),

        // Ng Modules are imported here
        CoreModule,
        HomeModule,
        AppRoutingModule,
        ProjectPortalModule,
        OnBoardingModule,
        DocumentsModule,
        // ServiceWorkerModule.register('ngsw-worker.js', {
        //     enabled: !AppConstant.developmentEnv,
        //     registrationStrategy: 'registerWhenStable:30000' // Waits 30s for stability, then registers
        // }),
    ],
    providers: [
        Title,
        { provide: ErrorHandler, useClass: SentryErrorHandler },
        NgbMomentjsAdapter,
        ScriptLoaderService,
        AnalyticsService,
    ],
    bootstrap: [AppComponent]
})
export class AppModule {
}

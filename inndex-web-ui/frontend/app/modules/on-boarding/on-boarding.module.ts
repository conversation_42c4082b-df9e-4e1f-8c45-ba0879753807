import {CommonModule, TitleCasePipe, UpperCasePipe} from "@angular/common";
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { CompetenciesComponent } from "./competencies/competencies.component";
import { ContactDetailsComponent } from "./contact-details/contact-details.component";
import { EmploymentDetailsComponent } from "./employment-details/employment-details.component";
import { HealthAssessmentComponent } from "./health-assessment/health-assessment.component";
// import { ManageProfilePictureComponent } from "./manage-profile-picture/manage-profile-picture.component";
import { MedicalAssessmentComponent } from "./medical-assessment/medical-assessment.component";
import { OnBoardComponent } from "./on-board/on-board.component";
import { OnBoardingRoutingModule } from "./on-boarding-routing.module";
import { OnBoardingSidebarComponent } from "./on-boarding-sidebar/on-boarding-sidebar.component";
import { PersonalDetailsComponent } from "./personal-details/personal-details.component";
import { CompanySelectorComponent, CompanySelectorV2Component, CountrySelectorComponent, ON_BOARD_FORMS } from "../common";
import { FileUploadModule } from "ng2-file-upload";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { NgSelectModule } from "@ng-select/ng-select";
import {UserDocUploaderComponent} from "@app/modules/user/sign-up-stages/competencies/doc-sector-row/doc-sector.component";
import {
    BlockLoaderComponent,
    JsonAnimator,
    CropImageUploaderComponent,
    FileUploaderComponent,
    FileUploaderV2Component,
    GenericConfirmationModalComponent,
    IModalComponent,
    WebcamComponent
} from "@app/shared";
import { ImageCropperModule } from "ngx-image-cropper";
import { NgxSkeletonLoaderModule } from "ngx-skeleton-loader";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import {LottieModule} from "ngx-lottie";
import { CapitalizeFirstCharPipe } from "@app/shared/pipe-and-directive";

export function playerFactory() {
    return import('lottie-web');
}
@NgModule({
    providers: [TitleCasePipe, UpperCasePipe],
    declarations: [
        // ManageProfilePictureComponent,
        PersonalDetailsComponent,
        ContactDetailsComponent,
        HealthAssessmentComponent,
        MedicalAssessmentComponent,
        EmploymentDetailsComponent,
        CompetenciesComponent,
        OnBoardingSidebarComponent,
        OnBoardComponent,
        CapitalizeFirstCharPipe,

        ...ON_BOARD_FORMS,
        UserDocUploaderComponent,
        FileUploaderComponent,
        FileUploaderV2Component,
        CountrySelectorComponent,
        CompanySelectorComponent,
        CompanySelectorV2Component,
        CropImageUploaderComponent,
        BlockLoaderComponent,
        JsonAnimator,
        IModalComponent,
        GenericConfirmationModalComponent,
        WebcamComponent
    ],
    imports: [
        CommonModule,
        OnBoardingRoutingModule,
        NgbModule,
        LottieModule.forRoot({ player: playerFactory }),

        FileUploadModule,
        NgxDatatableModule,
        //SharedModule,
        NgSelectModule,
        FormsModule,
        ReactiveFormsModule,
        ImageCropperModule,
        NgxSkeletonLoaderModule,
    ],
    exports: [
        ...ON_BOARD_FORMS,
        CapitalizeFirstCharPipe,
        UserDocUploaderComponent,
        FileUploaderComponent,
        FileUploaderV2Component,
        CountrySelectorComponent,
        CompanySelectorComponent,
        CompanySelectorV2Component,
        CropImageUploaderComponent,
        BlockLoaderComponent,
        JsonAnimator,
        IModalComponent,
        GenericConfirmationModalComponent,
        WebcamComponent
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class OnBoardingModule {}

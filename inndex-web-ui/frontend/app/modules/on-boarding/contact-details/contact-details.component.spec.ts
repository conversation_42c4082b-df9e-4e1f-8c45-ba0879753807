import {ComponentFixture, fakeAsync, TestBed, tick, waitForAsync} from '@angular/core/testing';

import { ContactDetailsComponent } from './contact-details.component';
import { RouterTestingModule } from '@angular/router/testing';
import { Router } from '@angular/router';
import {
  AuthService,
  FeatureExclusionUtility,
  HttpService,
  UserService
} from '@app/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
dayjs.extend(timezone);

describe('ContactDetailsComponent', () => {
  let component: ContactDetailsComponent;
  let fixture: ComponentFixture<ContactDetailsComponent>;
  let router: Router;
  let mockHttpService: any;
  let mockAuthService: any;
  let mockUserService: any;
  let mockFeatureExclusionUtility: any;
  let modalService: NgbModal;

  beforeEach(waitForAsync(() => {
    mockHttpService = {
      isMobileDevice: jasmine.createSpy('isMobileDevice').and.returnValue(false)
    };

    mockAuthService = {
      authUser: of({
        id: 12345,
        user_onboard_status: { health_assessment: false, medical_assessments: true }
      }),
      updateLocalUser: jasmine.createSpy('updateLocalUser')
    };

    mockUserService = {
      getMyContactDetail: jasmine.createSpy('getMyContactDetail').and.returnValue(of({
        contact_detail: { id: 1, home_number: {}, mobile_number: {}, emergency_contact_number: {} }
      })),
      addMyContactDetail: jasmine.createSpy('addMyContactDetail').and.returnValue(of({ success: true, contact_detail: {} }))
    };

    mockFeatureExclusionUtility = {
      showProfileMedicalAssessment: jasmine.createSpy('showProfileMedicalAssessment').and.returnValue(true),
      showProfileHealthAssessment: jasmine.createSpy('showProfileHealthAssessment').and.returnValue(false)
    };


    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [ ContactDetailsComponent ],
      providers: [
        { provide: HttpService, useValue: mockHttpService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: FeatureExclusionUtility, useValue: mockFeatureExclusionUtility },
        { provide: UserService, useValue: mockUserService },
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ContactDetailsComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router);
    modalService = TestBed.inject(NgbModal);
    spyOn(router, 'navigate');
    spyOn(modalService, 'open').and.returnValue({ result: Promise.resolve(true) });
    fixture.detectChanges();
    if (!dayjs.tz) {
      Object.defineProperty(dayjs, 'tz', {
        value: {},
        writable: true,
        configurable: true
      });
    }
    dayjs.tz.guess = () => 'Europe/London';
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call ngOnInit', () => {
    spyOn(component, 'getAuthUser');
    spyOn<any>(component, 'getMyContactDetail');
    component.ngOnInit();
    expect(component.getAuthUser).toHaveBeenCalled();
    expect((component as any).getMyContactDetail).toHaveBeenCalledWith();
  })

  it('should call getMyContactDetail and set title correctly', () => {
    component.getMyContactDetail();
    expect(component.title).toBe('EDIT YOUR PROFILE');
    expect(component.contact_detail).toBeDefined();
  });


  it('should call getAuthUser and set user data to authUser$', () => {
    component.getAuthUser();
    expect(component.isDataLoading).toBeTruthy();
    expect(component.authUser$).toBeDefined();
    expect(component.authUser$.id).toBe(12345);
  });

  it('should update local user on saveHandler', () => {
    const mockData = {
      success: true,
      contact_detail: {},
      partial_user: { user_onboard_status: { contact_detail: true } }
    };

    component.saveHandler(mockData);
    expect(component['authService'].updateLocalUser).toHaveBeenCalledWith(
        jasmine.objectContaining({ user_onboard_status: { contact_detail: true } })
    );
  });

  describe('getMyContactDetail()', () => {

    it('should set contact_detail and title when data is returned', fakeAsync(() => {
      const mockResponse = {
        success: true,
        contact_detail: { id: 1, answer: 'Yes' }
      };
      mockUserService.getMyContactDetail.and.returnValue(of(mockResponse));

      (component as any).getMyContactDetail();
      tick();

      expect(component.contact_detail).toEqual(mockResponse.contact_detail);
      expect(component.title).toBe('EDIT YOUR PROFILE');
      expect(component.isDataLoading).toBeFalsy();
    }));

    it('should set title and contact_detail with empty state when no contact_detail exist', fakeAsync(() => {
      const mockResponse = {
        success: true,
        contact_detail: {
          home_number: { code: null, number: null },
          mobile_number: { code: null, number: null },
          emergency_contact_number: { code: null, number: null }
        }
      };
      mockUserService.getMyContactDetail.and.returnValue(of(mockResponse));

      (component as any).getMyContactDetail();
      tick();

      expect(component.title).toBe('CREATE YOUR PROFILE');
      expect(component.contact_detail).toEqual({
        home_number: { code: null, number: null },
        mobile_number: { code: null, number: null },
        emergency_contact_number: { code: null, number: null }
      });
      expect(component.isDataLoading).toBeFalsy();
    }));
  });

  describe('save()', () => {
    let mockEvent: jasmine.SpyObj<any>;
    beforeEach(() => {
      mockEvent = {
        targetUrl: '/',
        contact_detail:{}
      }
      component.authUser$ = {
        user_onboard_status: {
          address: true
        }
      } as any;

      (component as any).confirmationModalRef = {
        openConfirmationPopup: jasmine.createSpy('openConfirmationPopup')
      } as any;

      component.is_changed = true;
      spyOn(component, 'saveInfo');
    });

    it('should return saveInfo when is_changed & address are false',() => {
      component.is_changed = false;
      component.authUser$.user_onboard_status.address = false;
      component.save(mockEvent);
      expect(component.saveInfo).toHaveBeenCalledWith({});
    })

    it('should run openConfirmationPopup method when is_changed is true and contact_detail exists',() => {
      component.save(mockEvent);
      expect((component as any).confirmationModalRef.openConfirmationPopup).toHaveBeenCalledWith(jasmine.objectContaining({
        title: jasmine.any(String),
        cancelLabel: 'No',
        onConfirm: jasmine.any(Function),
        onClose: jasmine.any(Function),
      }));
    })

    it('should call saveInfo with second param true when onConfirm is triggered', () =>{
      component.save(mockEvent);
      mockEvent.contact_detail = {};
      const args = (component as any).confirmationModalRef.openConfirmationPopup.calls.mostRecent().args[0];
      args.onConfirm(); // simulate confirm
      expect(component.saveInfo).toHaveBeenCalledWith(mockEvent.contact_detail, true);
    })

    it('should call saveInfo only with contact_detail when onClose is triggered', () =>{
      component.save(mockEvent);
      const args = (component as any).confirmationModalRef.openConfirmationPopup.calls.mostRecent().args[0];
      args.onClose(); // simulate close
      expect(component.saveInfo).toHaveBeenCalledWith(mockEvent.contact_detail);
    })
  })

  describe('saveInfo()', () => {

    beforeEach(() => {
      component.saveHandler = jasmine.createSpy('saveHandler');
    });

    it('should set isProcessing to true and call addMyContactDetail with updated contact detail', () => {
      const contactDetail = {
        name: 'John Doe',
        email: '<EMAIL>',
        mobile_no: '1234567890',
        emergency_contact_no: '0987654321',
        home_no: '1112223333',
        country: 'India',
      };
      component.contact_detail = contactDetail;

      const expectedContactDetail = {
        name: 'John Doe',
        email: '<EMAIL>',
        country: 'India',
        timezone: dayjs.tz.guess(),
      };

      mockUserService.addMyContactDetail.and.returnValue(of({}));
      component.saveInfo(contactDetail, true);

      expect(component.isProcessing).toBeTruthy();

      expect(mockUserService.addMyContactDetail).toHaveBeenCalledWith(
          expectedContactDetail,
          { update_induction: true }
      );

      expect(component.saveHandler).toHaveBeenCalled();
    });


    it('should not add timezone if country is UK', () => {
      const contactDetail = {
        name: 'Jane Smith',
        email: '<EMAIL>',
        mobile_no: '9876543210',
        emergency_contact_no: '0123456789',
        home_no: '4445556666',
        country: 'United Kingdom',
      };
      component.contact_detail = contactDetail;

      const expectedContactDetail = {
        name: 'Jane Smith',
        email: '<EMAIL>',
        country: 'United Kingdom',
      };

      mockUserService.addMyContactDetail.and.returnValue(of({}));

      component.saveInfo(contactDetail); // passing it, but only this.contact_detail is used inside

      expect(component.isProcessing).toBeTruthy();

      expect(mockUserService.addMyContactDetail).toHaveBeenCalledWith(
          expectedContactDetail,
          { update_induction: false }
      );

      expect(component.saveHandler).toHaveBeenCalled();
    });
  });

  describe('saveHandler()', () => {
    beforeEach(() => {
      component.contact_detail = {};
      component.isProcessing = true;
      component.targetUrl = null;

      // Mock dependencies
      (component as any).toastService = {
        show: jasmine.createSpy('show'),
        types: { ERROR: 'error' }
      };
      spyOn(component, 'goToNext');
      spyOn(component, 'updateLocalUser');
    });

    it('should update contact_detail, call updateLocalUser and navigate to targetUrl if provided', () => {
      const mockData = {
        success: true,
        contact_detail: { id: 1, answer: 'yes' }
      };
      component.targetUrl = '/next-page';

      component.saveHandler(mockData);

      expect(component.contact_detail).toEqual(mockData.contact_detail);
      expect(component.updateLocalUser).toHaveBeenCalledWith(mockData);
      expect(component.isProcessing).toBeFalsy();
      expect((component as any).router.navigate).toHaveBeenCalledWith(['/next-page']);
    });

    it('should call goToNext if no targetUrl is set', () => {
      const mockData = {
        success: true,
        contact_detail: { id: 2, answer: 'no' }
      };

      component.saveHandler(mockData);

      expect(component.goToNext).toHaveBeenCalled();
      expect((component as any).router.navigate).not.toHaveBeenCalled();
    });

    it('should show error toast if success is false or contact_detail missing', () => {
      const errorData = { success: false, message: 'Something went wrong' };

      component.saveHandler(errorData);

      expect(component.isProcessing).toBeFalsy();
      expect((component as any).toastService.show).toHaveBeenCalledWith(
          'error',
          'Something went wrong',
          { data: errorData }
      );
    });

    it('should show default error message if message is not provided', () => {
      const errorData = { success: false };

      component.saveHandler(errorData);

      expect((component as any).toastService.show).toHaveBeenCalledWith(
          'error',
          'Failed to store data.',
          { data: errorData }
      );
    });
  });

  describe('goToNext()', () => {
    beforeEach(() => {
      component.authUser$ = {
        user_onboard_status: {
          medical_assessments: true
        }
      } as any;
    })

    it('should navigate to /on-board/health-assessment when only health assessments is enabled and completed', () => {
      component.authUser$.user_onboard_status.health_assessment = true;
      (component as any).featureExclusionUtility.showProfileHealthAssessment.and.returnValue(true);

      component.goToNext();
      expect(router.navigate).toHaveBeenCalledWith(['/on-board/health-assessment']);
    })

    it('should navigate to /on-board/medical-assessment when only medical assessments is enabled and completed', () => {
      component.authUser$.user_onboard_status.medical_assessments = true;
      (component as any).featureExclusionUtility.showProfileHealthAssessment.and.returnValue(true);

      component.goToNext();
      expect(router.navigate).toHaveBeenCalledWith(['/on-board/medical-assessment']);
    })

    it('should navigate to /on-board/competencies when medical & health assessments is disable', () => {
      component.authUser$.user_onboard_status.medical_assessments = false;
      component.authUser$.user_onboard_status.health_assessment = false;
      (component as any).featureExclusionUtility.showProfileMedicalAssessment.and.returnValue(false);

      component.goToNext();

      expect(router.navigate).toHaveBeenCalledWith(['/on-board/competencies']);
    });
  })


  describe('onFormChanged()', () => {
    it('should update is_changed based on the event', () => {
      component.onFormChanged(true);
      expect(component.is_changed).toBeTruthy();

      component.onFormChanged(false);
      expect(component.is_changed).toBeFalsy();
    });
  });

  it('should navigate to the correct route on goBack()', () => {
    component.goBack();
    expect(router.navigate).toHaveBeenCalledWith(['/on-board/employment-details']);
  });

});

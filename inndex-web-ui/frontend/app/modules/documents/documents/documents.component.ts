import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
    AuthService,
    DocumentsActionButtons,
    Project,
    UACLevels,
    User,
    UserService,
    DocumentItem,
    FileService,
    FileType,
    DocumentsPage,
    DocumentPermissionsService,
    DocumentPermission,
    DocumentPermissionRequest,
} from '@app/core';
import {FileUploaderComponent, GenericConfirmationModalComponent, IModalComponent} from '@app/shared';
import { forkJoin, of } from 'rxjs';
import { delay, switchMap } from 'rxjs/operators';
import { DocumentAssets } from '@app/modules/documents/document-assets/document-assets';
import * as dayjs from 'dayjs';
import {saveAs} from "file-saver";

@Component({
    selector: 'documents',
    templateUrl: './documents.component.html',
    styleUrls: ['./documents.component.scss'],
})
export class DocumentsComponent implements OnInit {
    authUser$: User;
    isMobileDevice: boolean = false;
    actionButtonMetaData = {
        isTraining: false,
        actionList: [],
        class: 'material-symbols-outlined xx-large-font',
    };
    projectInfo: Project = new Project;
    isProjectPortal = false;
    projectResolverResponse: any = {};
    selectedFolder: DocumentItem;
    documentPaths: DocumentItem[] = [];
    mode = 'active';
    FileType = FileType;
    nameLimit = 30;

    permissions: DocumentPermission[] = [];
    permissionToEdit: DocumentPermission = {
        title: undefined,
        access_level: undefined,
        perm_type: undefined,
        company_ref: undefined,
        user_ref: undefined,
        documents: []
    };
    newDocument: {
        open?: boolean;
        name?: string;
        destination?: string;
        document?: any;
        error?: string
        destinationError?: string
    } = {};
    newFolder: {
        open?: boolean;
        name?: string;
        destination?: string;
        submitted?: boolean;
        error?: string;
    } = {};

    foldersToSelect: DocumentItem[] = [];
    companiesToSelect: Array<{ id: number; name: string; }> = [];
    usersToSelect: Array<{ id: number; name: string; }> = [];
    accessLevelsToSelect = UACLevels;
    permissionTypesToSelect = [
        {
            label: 'Read Only',
            value: 'read_only'
        },
        {
            label: 'Editor: oragnise, add and edit files',
            value: 'create_read_update_delete'
        },
    ];

    folders: DocumentItem[] = [];
    searchFolders: DocumentItem[] = [];
    selectedDocuments: string[] = [];
    deletedDocuments: DocumentItem[] = [];
    hasDeleteDocument = false;
    allowedMimeType = [
        'application/pdf',
        'application/x-pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'video/mp4',
        'video/quicktime',
    ];
    loading = true;
    loadingTree = true;
    loadingUsers = false;
    loadingPermissions = false;
    isProcessing = false;
    loaded = false;
    folderIcon = DocumentAssets.icons.openFolder;
    openMap: Record<number, boolean> = {};
    searchTreeTerm = ''
    lastEvaluatedKey: Record<number, { SK: string, PK: string } | null> = {}
    pageInfo: DocumentsPage = new DocumentsPage()

    @ViewChild('permissionsModal') private permissionsModal: IModalComponent;

    @ViewChild('newPermissionModal') private newPermissionModal: IModalComponent;

    @ViewChild('createEditPermissionModal') private createEditPermissionModal: IModalComponent;

    @ViewChild('newPermissionSelectFoldersModal') private newPermissionSelectFoldersModal: IModalComponent;

    @ViewChild('newDocumentModal') private newDocumentModal: IModalComponent;

    @ViewChild('newFolderModal') private newFolderModal: IModalComponent;

    @ViewChild('attachmentUploader') private attachmentUploader: FileUploaderComponent;

    @ViewChild('editDocumentModal') private editDocumentModal: IModalComponent;

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;

    editDocument: DocumentItem = {}
    nameMaxLengthMsg = 'Name should not be more than 255 characters.';

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private authService: AuthService,
        private userService: UserService,
        private fileService: FileService,
        private permissionsService: DocumentPermissionsService
    ) {
    }

    ngOnInit() {
        this.isProjectPortal = this.route.snapshot.data.is_project_portal || false;
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.route.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
        }

        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
            }
        });

        localStorage.removeItem('fs_token');
        localStorage.removeItem('fs_project_id');
        this.fileService.auth(this.projectInfo.id).subscribe((res: { token: string }) => {
            if (res.token) {
                localStorage.setItem('fs_token', res.token);
                localStorage.setItem('fs_project_id', this.projectInfo.id.toString())
                this.init();
            }
        });

        const openMapString = localStorage.getItem('tree-open-map');
        if (openMapString) {
            try {
                this.openMap = JSON.parse(openMapString) || {}
               } catch (err) {
                this.openMap = {}
            }
        }
    }

    getPermissions() {
        this.loadingPermissions = true;
        return this.permissionsService.getDocumentPermissions(this.projectInfo.id).pipe(
            switchMap((res: any) => {
                this.permissions = res.rows;
                const fileRequests = res.rows.map(permission => {
                    permission.company_name = this.companiesToSelect.find(el => el.id === permission.company_ref)?.name || '';
                    permission.user_name = this.usersToSelect.find(el => el.id === permission.user_ref)?.name || 'All';

                    return this.fileService.getFilesByIds(this.projectInfo.id, permission.documents).pipe(
                        switchMap((files: any) => {
                            permission.files = files.rows.map(el => el.name).join(', ');
                            return of(permission);
                        })
                    );
                });
                return fileRequests.length > 0 ? forkJoin(fileRequests) : of([]);
            }),
            switchMap(() => {
                this.loadingPermissions = false;
                return of(this.permissions);
            })
        );
    }

    init() {
        const getCompaniesRequest = this.userService.userCompanies();
        const getDeletedFilesRequest = this.fileService.getDeletedFiles(this.projectInfo.id)

        this.loading = true;
        forkJoin([
            getCompaniesRequest,
            getDeletedFilesRequest,
        ]).subscribe(([companiesRes, filesRes]: any) => {
            this.companiesToSelect = companiesRes.companies.map(el => ({
                id: el.id,
                name: el.name
            }));
            if (filesRes.count) {
                this.hasDeleteDocument = true;
            }
            this.loading = false;
        }, () => {
            this.loading = false;
        });
        this.getFoldersByProject();
    }

    updatePermissionsForFolder(folder: DocumentItem) {
        if (folder.type !== FileType.FOLDER) {
            return;
        }

        const foundPermissions = this.permissions.filter(el => el.documents.includes(folder.id));
        if (foundPermissions.length) {
            folder.permissions = foundPermissions;
        } else {
            folder.permissions = [];
        }
    }

    setSelectedFolder(folder: DocumentItem) {
        if (folder.type !== FileType.FOLDER || this.selectedFolder?.id === folder.id) {
            return;
        }

        this.selectedFolder = folder;
        this.updatePermissionsForFolder(this.selectedFolder);
        this.updateActionMenus();
    }

    getFoldersByProject() {
        this.loadingTree = true;
        
        const getFoldersRequest = this.fileService.getFoldersTree(this.projectInfo.id, {});
        const getPermissionsRequest = this.getPermissions();

        forkJoin([
            getFoldersRequest,
            getPermissionsRequest
        ]).subscribe(([foldersRes]: any) => {
            // Handle folders response
            this.folders = foldersRes;
            this.searchFolders = this.folders;

            // Update folder permissions
            for (const folder of this.folders) {
               this.updatePermissionsForFolder(folder);
            }

            this.updateActionMenus();

            if (this.folders.length) {
                if (!this.selectedFolder) {
                    const selectedFolderId = this.route.snapshot.queryParams.selectedFolder;
                    if (selectedFolderId) {
                        this.calculatePaths(this.folders, [], selectedFolderId);
                    } else {
                        this.documentPaths = this.folders.slice(0, 1);
                        this.setSelectedFolder(this.folders[0]);
                    }
                } else if (this.documentPaths.length && this.selectedFolder.id !== this.documentPaths[this.documentPaths.length - 1].id) {
                    this.calculatePaths(this.folders, [], this.selectedFolder.id);
                    for (const path of this.documentPaths) {
                        if (!this.openMap[path.id]) {
                            this.openMap[path.id] = true;
                        }
                    }
                    localStorage.setItem('tree-open-map', JSON.stringify(this.openMap));
                }
                this.lastEvaluatedKey = {};
                this.getFilesUnderFolder(this.selectedFolder);
            } else if (this.mode === 'active') {
                this.lastEvaluatedKey = {};
                this.getFilesUnderFolder(undefined);
            }
            
            this.loadingTree = false;
        }, () => {
            this.loadingTree = false;
            this.loadingPermissions = false;
        });
    }

    public onChangeCompany(companyId: number) {
        this.loadingUsers = true;
        this.userService.getUsersByEmployer(companyId).subscribe((res: any) => {
            this.usersToSelect = res.employees_details?.map((el) => ({
                id: el.id,
                name: el.user_ref?.name || ''
            }))?.sort((a, b) => a.name.localeCompare(b.name)) || [];
            this.usersToSelect.unshift({ id: -1, name: 'All' })
            this.loadingUsers = false;
        }, () => {
            this.loadingUsers = false;
        })
    }

    initPermissionsModal() {
        const getFoldersTreeWithFiles = this.fileService.getFoldersTree(this.projectInfo.id, {
            withFiles: true
        })
        const getPermissionsRequest = this.permissionsService.getDocumentPermissions(this.projectInfo.id)
        this.loadingPermissions = true
        forkJoin([
            getFoldersTreeWithFiles,
            getPermissionsRequest
        ]).subscribe(([foldersRes, permissionsRes]: any) => {
            this.foldersToSelect = foldersRes
            this.permissions = permissionsRes.rows
            for (const permission of permissionsRes.rows) {
                permission.company_name = this.companiesToSelect.find(el => el.id === permission.company_ref)?.name || '';
                permission.user_name = this.usersToSelect.find(el => el.id === permission.user_ref)?.name || 'All';
                this.fileService.getFilesByIds(this.projectInfo.id, permission.documents).subscribe((files: any) => {
                    permission.files = files.rows.map(el => el.name).join(', ')
                })
            }
            this.loadingPermissions = false;
        }, () => {
            this.loadingPermissions = false;
        })

    }

    public get canAddNewDocument() {
        const isOwner = this.selectedFolder?.createdBy?.id === this.authUser$.id
        return isOwner || !!this.selectedFolder?.permissions?.find(el => el.perm_type === 'create_read_update_delete')
    }

    public onActionSelection(actionVal: any) {
        if (actionVal.code === DocumentsActionButtons.PERMISSIONS) {
            this.initPermissionsModal();
            this.permissionsModal.open();
        } else if (actionVal.code === DocumentsActionButtons.ADD_NEW_DOCUMENT) {
            this.newDocument.open = true;
            this.newDocument.destination = this.selectedFolder?.id
            this.newDocumentModal.open();
        } else if (actionVal.code === DocumentsActionButtons.ADD_NEW_FOLDER) {
            this.newFolder.open = true;
            this.newFolder.destination = this.selectedFolder?.id
            this.newFolderModal.open();
        }
    }
    openCreateEditPermissionModal() {
        this.createEditPermissionModal.open();
    }
    openNewDocumentModal() {
        this.newDocument.open = true;
        this.newDocument.destination = this.selectedFolder?.id
        this.newDocumentModal.open();
    }
    openNewFolderModal() {
        this.newFolder.destination = this.selectedFolder?.id
        this.newFolder.open = true;
        this.newFolderModal.open();
    }
    attachmentUploadDone(event) {
        if (event && event.userFile && event.userFile?.length) {
            this.newDocument.document = event.userFile[event.userFile.length - 1];
            if (!this.newDocument.name) {
                this.newDocument.name = this.newDocument.document?.name;
            }
        }
    }
    removeNewDocument() {
        this.newDocument.document = null;
        this.newDocument.name = '';
    }
    searchDocuments(term: string, items: DocumentItem[]): DocumentItem[] {
        const results: DocumentItem[] = [];

        for (const item of items) {
            let matchedChildren: DocumentItem[] = [];

            // Check if the current item's name matches the search term
            if (item.name.toLowerCase().includes(term.toLowerCase())) {
                matchedChildren.push(item);
            }

            // If the item has children, search them recursively
            if (item.children) {
                const childResults = this.searchDocuments(term, item.children);
                if (childResults.length > 0) {
                    matchedChildren.push({
                        ...item,
                        children: childResults // Include only matching children
                    });
                }
            }

            // If there are any matches, add to results
            if (matchedChildren.length > 0) {
                results.push(...matchedChildren);
            }
        }

        return results;
    }
    searchFunction(event) {
        this.searchTreeTerm = event.search;

        if (!this.searchTreeTerm) {
            this.searchFolders = this.folders
        } else {
            this.searchFolders = this.searchDocuments(this.searchTreeTerm, this.folders)
        }
    }
    onSelectDocument({ folder, paths }: { folder: DocumentItem, paths: DocumentItem[] }) {
        if (this.selectedFolder?.id === folder.id) {
            return;
        }
        this.setSelectedFolder(folder);
        this.changeReferenceUrl();
        this.documentPaths = paths;
        this.lastEvaluatedKey = {}
        this.getFilesUnderFolder(folder);
    }
    onToggleTree({ folder }: { folder: DocumentItem }) {
        this.openMap[folder.id] = !this.openMap[folder.id];
        localStorage.setItem('tree-open-map', JSON.stringify(this.openMap));
    }

    getFilesUnderFolder(folder) {
        this.loading = true;
        of(null).pipe(
          delay(300),
          switchMap(() => this.fileService.getFiles(this.projectInfo.id, {
              ...folder?.id ? { parentId: folder?.id } : { parentId: null },
              pageSize: this.pageInfo.pageSize,
              ...this.lastEvaluatedKey[this.pageInfo.pageNumber] ? { lastEvaluatedKey: JSON.stringify(this.lastEvaluatedKey[this.pageInfo.pageNumber]) } : {}
          }))
        ).subscribe((res: any) => {
            this.lastEvaluatedKey[this.pageInfo.pageNumber + 1] = res.lastEvaluatedKey
            this.pageInfo.totalCount = res.count
            this.pageInfo.records = res.rows
            this.selectedDocuments = [];
            this.loading = false;
            this.loaded = true;
        }, () => {
            this.loading = false;
        }, () => {
            this.loading = false;
        });
    }
    getDeletedFiles() {
        this.loading = true;
        this.fileService.getDeletedFiles(this.projectInfo.id).subscribe((res: any) => {
            this.deletedDocuments = res.rows;
            this.loading = false;
        }, () => {
            this.loading = false
        })
    }
    changeReferenceUrl() {
        this.router.navigate(
            [],
            {
                relativeTo: this.route,
                queryParams: {
                    selectedFolder: this.selectedFolder.id
                },
                queryParamsHandling: 'merge',
            }
        );
    }
    onSelectDocumentFromBreadcrumb(folder: DocumentItem) {
        if (this.selectedFolder?.id === folder.id) {
            return;
        }
        this.setSelectedFolder(folder);
        this.changeReferenceUrl();
        const newPaths = [];
        for (const path of this.documentPaths) {
            newPaths.push(path);
            if (path.id === folder.id) {
                break;
            }
        }
        this.documentPaths = newPaths;
        this.lastEvaluatedKey = {}
        this.getFilesUnderFolder(folder);
    }
    onSelectDocumentFromTable(folder: DocumentItem) {
        this.setSelectedFolder(folder);
        this.documentPaths = [...this.documentPaths, folder];

        this.changeReferenceUrl();
        this.lastEvaluatedKey = {}
        this.getFilesUnderFolder(folder);
    }
    onDeleteDocument(doc: DocumentItem) {
        this.isProcessing = true;
        this.fileService.deleteFileOrFolder(this.projectInfo.id, doc.id).subscribe(() => {
            this.hasDeleteDocument = true;
            this.isProcessing = false;

            if (doc.type === FileType.FOLDER) {
                this.getFoldersByProject();
            } else {
                this.lastEvaluatedKey = {}
                this.getFilesUnderFolder(this.selectedFolder);
            }
        }, () => {
            this.isProcessing = false
        });
    }
    onEditDocument(doc: DocumentItem) {
        this.editDocument = {...doc};
        this.editDocumentModal.open();
    }
    onRestoreDocument(doc: DocumentItem) {
        this.isProcessing = true;
        this.fileService.restoreFileOrFolder(this.projectInfo.id, doc.id).subscribe(() => {
            this.isProcessing = false;
            this.getDeletedFiles();
        }, () => {
            this.isProcessing = false;
        });
    }
    onAddNewDocument() {
        if (!this.newDocument.name) {
            this.newDocument.error = 'Document name should be required.'
            return;
        }
        if (!this.newDocument.destination) {
            this.newDocument.destinationError = 'Document destination should be required.'
            return;
        }
        if (this.newDocument.name.length > 255) {
            this.newDocument.error = this.nameMaxLengthMsg;
            return;
        }

        this.isProcessing = true;
        const file_extension = this.newDocument.document.name.split('.').pop()?.toLowerCase()
        const permission = this.selectedFolder.permissions?.find(el => el.perm_type === 'create_read_update_delete')

        this.fileService.createFileOrFolder(this.projectInfo.id, {
            name: this.newDocument.name,
            type: "file",
            file_key: this.newDocument.document?.fileKey,
            size: this.newDocument.document?.size,
            extension: this.newDocument.document?.type,
            file_extension,
            createdBy: {
                id: this.authUser$.id,
                user_name: this.authUser$.name,
            },
            parentId: this.newDocument.destination,
            permissionId: permission?.id,
        }).subscribe(() => {
            this.isProcessing = false;
            if (this.newDocument.destination === this.selectedFolder?.id) {
                this.lastEvaluatedKey = {}
                this.getFilesUnderFolder(this.selectedFolder);
            }
            this.newDocument = {
                destination: this.newDocument.destination,
                document: undefined,
            };
            this.attachmentUploader.completed = false
            this.newDocumentModal.close();
        }, (err) => {
            this.newDocument.error = err.error.message;
            this.isProcessing = false;
        });
    }
    cancelAddNewDocument() {
        this.newDocument = {
            destination: this.selectedFolder.id,
        };
        this.attachmentUploader.completed = false
    }
    calculatePaths(folders: DocumentItem[], paths: DocumentItem[], selectedFolderId) {
        for (const folder of folders) {
            if (folder.id === selectedFolderId) {
                this.setSelectedFolder(folder);
                this.documentPaths = [...paths, this.selectedFolder];
                return;
            }
            if (folder.children?.length) {
                this.calculatePaths(folder.children, [...paths, folder], selectedFolderId)
            }
        }
    }
    onAddNewFolder() {
        if (!this.newFolder.name) {
            this.newFolder.error = 'Folder name should be required.'
            return;
        }
        if (this.newFolder.name.length > 255) {
            this.newFolder.error = this.nameMaxLengthMsg;
            return;
        }

        this.isProcessing = true;
        this.fileService.createFileOrFolder(this.projectInfo.id, {
            name: this.newFolder.name,
            type: 'folder',
            createdBy: {
                id: this.authUser$.id,
                user_name: this.authUser$.name,
            },
            parentId: this.newFolder.destination,
        }).subscribe((res: DocumentItem) => {
            this.isProcessing = false;
            this.selectedFolder = res;
            this.getFoldersByProject();
            this.updateActionMenus();
            this.pageInfo.records = [];
            this.newFolder = {
                destination: this.newFolder.destination
            };
            this.newFolderModal.close();
        }, (err) => {
            this.newFolder.error = err.error.message;
            this.isProcessing = false;
        });
    }
    cancelAddNewFolder() {
        this.newFolder = {
            destination: this.selectedFolder?.id,
        };
    }
    toggleMode() {
        this.mode = this.mode === 'active' ? 'delete' : 'active';
        if (this.mode == 'delete') {
            this.getDeletedFiles();
        } else {
            this.getFoldersByProject();
        }
    }

    onSort(records, event) {
        return records.sort((a, b) => {
            const direct = event.dir === 'asc' ? 1 : -1;

            if (event.prop === 'createdAt') {
                const aDate = dayjs(a.createdAt);
                const bDate = dayjs(b.createdAt);

                return (aDate.isAfter(bDate) ? 1 : -1) * direct
            } else {
                return a.name.localeCompare(b.name) * direct
            }
        })
    }
    onSortChange(event) {
        const folders = this.pageInfo.records.filter(el => el.type === 'folder')
        const attachments = this.pageInfo.records.filter(el => el.type !== 'folder')
        this.pageInfo.records = [
          ...this.onSort(folders, event),
          ...this.onSort(attachments, event)
        ]
    }
    onSaveDocument() {
        if (!this.editDocument.id) return;

        if (this.editDocument.name.length > 255) {
            this.editDocument.error = this.nameMaxLengthMsg
            return;
        }

        this.isProcessing = true;
        this.fileService.updateFileOrFolder(this.projectInfo.id, this.editDocument.id, {
            name: this.editDocument.name,
            parentId: this.editDocument.parent_id,
        }).subscribe((res: DocumentItem) => {
            this.isProcessing = false;
            this.editDocumentModal.close();
            if (this.editDocument.type === FileType.FOLDER) {
                this.getFoldersByProject();
            } else {
                this.lastEvaluatedKey = {}
                this.getFilesUnderFolder(this.selectedFolder);
            }
            this.editDocument = {}
        }, (err) => {
            this.isProcessing = false;
            this.editDocument.error = err.error.message;
        });
    }
    cancelEditDocument() {
        this.editDocument = {};
        this.editDocumentModal.close();
    }
    onChangeNewDocumentDestination(event) {
        this.newDocument.destination = event;
        this.newDocument.destinationError = '';
    }

    onSelectDocuments(event) {
        this.selectedDocuments = event
    }

    onDeleteSelectedDocuments() {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete Items',
            title: `Do you wish to delete these items? Deleted items can be found in your deleted files. They will be permanently deleted after 30 days.`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.isProcessing = true;
                this.fileService.bulkDelete(this.projectInfo.id, this.selectedDocuments).subscribe((res) => {
                    this.hasDeleteDocument = true;
                    this.isProcessing = false;
                    this.selectedDocuments = [];
                    this.getFoldersByProject();
                })
            }
        });
    }

    onDownloadSelectedDocuments() {
        this.isProcessing = true;
        this.fileService.bulkDownload(this.projectInfo.id, this.selectedDocuments).subscribe((res) => {
            saveAs(res.body, 'files.zip')
            this.isProcessing = false;
            this.selectedDocuments = [];
        }, () => {
            this.isProcessing = false;
        })
    }

    onPageChange(pageInfo) {
        if (this.pageInfo.pageNumber === pageInfo.offset) {
            return
        }
        this.pageInfo = {
            ...this.pageInfo,
            pageNumber: pageInfo.offset,
            pageSize: pageInfo.pageSize
        }
        this.getFilesUnderFolder(this.selectedFolder)
    }

    openNewPermissionFolderModal() {
        this.createEditPermissionModal.close();
        this.newPermissionSelectFoldersModal.open()
    }

    onSavePermission() {
        const body: DocumentPermissionRequest = {
            documents: this.permissionToEdit.documents,
            access_level: this.permissionToEdit.access_level,
            company_ref: this.permissionToEdit.company_ref,
            user_ref: this.permissionToEdit.user_ref,
            perm_type: this.permissionToEdit.perm_type,
            title: this.permissionToEdit.title,
            projectId: this.projectInfo.id,
            createdBy: {
                id: this.authUser$.id,
                user_name: this.authUser$.name,
            },
        }
        const callback = () => {
            this.getPermissions().subscribe();
            this.permissionToEdit = {
                title: undefined,
                access_level: undefined,
                perm_type: undefined,
                company_ref: undefined,
                user_ref: undefined,
                documents: []
            }
            this.newPermissionSelectFoldersModal.close()
        }
        console.log('folders are selected', this.permissionToEdit.documents)
        if (this.permissionToEdit.id) {
            this.permissionsService.updateDocumentPermissions(this.projectInfo.id, this.permissionToEdit.id, body).subscribe(callback)
        } else {
            this.permissionsService.addDocumentPermission(this.projectInfo.id, body).subscribe(callback)
        }
    }

    removePermission(permission: DocumentPermission) {
        this.permissionsService.deleteDocumentPermissions(this.projectInfo.id, permission.id).subscribe(() => {
            this.getPermissions().subscribe();
        })
    }

    onEditPermission(permission: DocumentPermission) {
        this.permissionToEdit = {
            id: permission.id,
            title: permission.title,
            access_level: permission.access_level,
            perm_type: permission.perm_type,
            company_ref: permission.company_ref,
            user_ref: permission.user_ref,
            documents: permission.documents,
        }
        this.onChangeCompany(this.permissionToEdit.company_ref as number);
        this.createEditPermissionModal.open()
    }

    onCloseNewPermissionModal() {
        this.permissionToEdit = {
            title: undefined,
            access_level: undefined,
            perm_type: undefined,
            company_ref: undefined,
            user_ref: undefined,
            documents: []
        }
        this.createEditPermissionModal.close();
    }

    onReloadPermissions() {
        this.init();
        this.permissionsModal.close();
    }

    updateActionMenus() {
        this.actionButtonMetaData.actionList = [
            {
                code: DocumentsActionButtons.PERMISSIONS,
                name: `Permissions`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: "folder_supervised",
                enabled: true,
            },
            ...(this.folders.length
                ? [
                    {
                        code: DocumentsActionButtons.ADD_NEW_DOCUMENT,
                        name: `Add New Document`,
                        iconClass: this.actionButtonMetaData.class,
                        iconClassLabel: "draft",
                        enabled: this.canAddNewDocument,
                    },
                ]
                : []),
            {
                code: DocumentsActionButtons.ADD_NEW_FOLDER,
                name: `Add New Folder`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: "folder",
                enabled: true,
            },
        ];
    }
}

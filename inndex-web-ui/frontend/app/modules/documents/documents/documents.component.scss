@use "assets/scss/variables";

@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

.wrapper {
    height: calc(100vh - 64px);
    overflow: hidden;

    .max-name-width {
        max-width: 200px;
    }
    .gap-8 {
        gap: 32px;
    }
    .gap-2 {
        gap: 8px;
    }
    .text-primary {
        color: #0066FF !important;
    }
    .font-weight-regular {
        font-weight: 500;
    }
    .animation-wrapper {
        animation: fadeIn 1s;
    }
    .tree-wrapper {
        max-width: 280px;
    }
    .overflow-y-auto {
        overflow-y: auto;
    }
    .overflow-x-hidden {
        overflow-x: hidden;
    }
    .page-content {
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;

        ::ng-deep {
            .placeholder-tile {
                height: calc(100vh - 400px);
            }
        }

        .custom-button {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            color: variables.$primary-brand-color;

            &:hover {
                color: variables.$primary-text-color;
            }
        }

        .bottom-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            padding: 8px 20px;
            box-shadow: -2px -14px 31px 0px #0000000F;
            transition: all ease-in 1s;
        }

        .content-wrapper {
            max-height: calc(100vh - 335px);
        }
        .h-6 {
            height: 24px;
        }

        .gap-4 {
            gap: 16px;
        }

        .grow {
            flex-grow: 1;
        }
    }
}

::ng-deep {
    .permissions-modal {
        .modal-body {
            padding: 32px;

            .gap-16 {
                gap: 16px;
            }

            .gap-4 {
                gap: 4px;
            }

            .btn-icon {
                background: none;
                border: none;
                padding: 0.25rem;
                border-radius: 0.25rem;

                span {
                    font-size: 20px;
                }

                &:hover {
                    background: var(--bright-gray);
                }
            }

            table {
                width: 100%;

                td {
                    padding: 8px 12px;
                    border: 1px solid var(--platinum);
                    font-size: 14px;

                    &:last-child {
                        width: 100%;
                    }
                }
                .gray-cell {
                    background: var(--cultured);
                    white-space: nowrap;
                }
            }
        }
    }
    .new-permission-modal {
        .modal-body {
            padding: 24px;

            .desc {
                font-size: 14px;
                margin-bottom: 0;
            }
            .custom-input {
                height: 40px;
                border: 1px solid var(--spanish-gray);
                color: variables.$primary-brand-color;

                &::placeholder {
                    color: variables.$primary-brand-color;
                }
            }
        }
    }
    .document-modal {
        .modal-dialog {
            max-width: 640px;
        }
        .modal-body {
            overflow: visible !important;
        }
    }
    .new-document-modal {
        .attachment {
            border: 1px solid #9D9D9D;
            border-radius: 5px;
            padding: 12px 8px;

            .text {
                color: #0066FF;
                line-height: 1;
                font-size: 12px;
            }
        }
    }
}

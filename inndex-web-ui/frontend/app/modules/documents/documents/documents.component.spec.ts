import { ComponentFix<PERSON>, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { DocumentsComponent } from './documents.component';
import { ActivatedRoute, Router } from '@angular/router';
import {
  AuthService,
  UserService,
  FileService,
  DoNotAskAgainService,
  OptimaService,
  ProjectService,
  HttpService,
  DocumentsActionButtons,
  DocumentPermissionsService,
  DocumentsPage,
  DocumentItem,
  DocumentPermission,
} from '@app/core';
import { of } from 'rxjs';
import { FileUploaderComponent, IModalComponent, BlockLoaderComponent, GenericConfirmationModalComponent } from '@app/shared';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule, TitleCasePipe, UpperCasePipe } from "@angular/common";
import { ProjectHeaderComponent } from '@app/modules/site-admin/view-project/project-header/project-header.component';
import { ActionButtonComponent } from '@app/modules/common/action-button/action-button.component';
import { NgSelectModule } from "@ng-select/ng-select";
import { GenericModalComponent } from "@app/modules/common";

describe('DocumentsComponent', () => {
  let component: DocumentsComponent;
  let fixture: ComponentFixture<DocumentsComponent>;
  let mockUserService: jasmine.SpyObj<UserService>;
  let mockFileService: jasmine.SpyObj<FileService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;
  let mockPermissionsService: jasmine.SpyObj<DocumentPermissionsService>;

  beforeEach(async () => {
    let mockHttpService = {
      isMobileDevice: jasmine.createSpy('isMobileDevice').and.returnValue(false)
    };
    let mockAuthService = {
      authUser: of({ id: '12345', user_onboard_status: {} })
    };
    mockUserService = jasmine.createSpyObj('UserService', ['userCompanies', 'getUsersByEmployer']);
    mockFileService = jasmine.createSpyObj('FileService', [
      'auth',
      'getFoldersTree',
      'getFiles',
      'getDeletedFiles',
      'deleteFileOrFolder',
      'restoreFileOrFolder',
      'createFileOrFolder',
      'updateFileOrFolder',
      'bulkDelete',
      'bulkDownload',
      'getFilesByIds'
    ]);
    mockPermissionsService = jasmine.createSpyObj('DocumentPermissionsService', ['getDocumentPermissions']);
    let mockOptimaService = {
      getProjectMembers: jasmine.createSpy('getProjectMembers').and.returnValue([])
    };
    let mockProjectService = {
      getProjectInductedUsersNames: jasmine.createSpy('getProjectMembers').and.returnValue([])
    };

    // Setup default return values for spies
    mockFileService.bulkDelete.and.returnValue(of({ success: true }));
    mockFileService.getDeletedFiles.and.returnValue(of({ count: 0, rows: [] }));
    mockFileService.getFoldersTree.and.returnValue(of([]));
    mockFileService.getFilesByIds.and.returnValue(of({ rows: [] }));
    mockPermissionsService.getDocumentPermissions.and.returnValue(of({
      rows: [],
      permissions: []
    }));
    mockUserService.getUsersByEmployer.and.returnValue(of({
      employees_details: [
        { id: 1, user_ref: { name: 'Test User' } }
      ]
    }));
    mockUserService.userCompanies.and.returnValue(of({ companies: [] }));
    mockFileService.auth.and.returnValue(of({ token: 'test-token' }));

    // Set up getFiles to return a default response
    mockFileService.getFiles.and.callFake(() => {
      return of({
        count: 0,
        rows: [],
        lastEvaluatedKey: null
      });
    });

    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockActivatedRoute = {
      snapshot: {
        data: {
          is_project_portal: true,
          projectResolverResponse: {
            project: {
              id: 1,
              name: 'Test Project'
            }
          }
        },
        queryParams: {}
      }
    };

    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
        RouterTestingModule,
        NgSelectModule
      ],
      declarations: [
        DocumentsComponent,
        FileUploaderComponent,
        GenericConfirmationModalComponent,
        GenericModalComponent,
        IModalComponent,
        BlockLoaderComponent,
        ProjectHeaderComponent,
        ActionButtonComponent,
      ],
      providers: [
        { provide: HttpService, useValue: mockHttpService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: UserService, useValue: mockUserService },
        { provide: FileService, useValue: mockFileService },
        { provide: DocumentPermissionsService, useValue: mockPermissionsService },
        { provide: OptimaService, useValue: mockOptimaService },
        { provide: ProjectService, useValue: mockProjectService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        TitleCasePipe,
        UpperCasePipe,
        DoNotAskAgainService,
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(DocumentsComponent);
    component = fixture.componentInstance;

    // Mock the confirmation modal component
    const mockConfirmationModal = {
      openConfirmationPopup: jasmine.createSpy('openConfirmationPopup').and.callFake((config) => {
        // Immediately call the onConfirm callback
        config.onConfirm();
      })
    };
    component['confirmationModalRef'] = mockConfirmationModal as any;

    fixture.detectChanges();
  });

  afterEach(() => {
    // Reset all spies after each test
    mockFileService.getFiles.calls.reset();
    mockFileService.getFiles.and.callFake(() => {
      return of({
        count: 0,
        rows: [],
        lastEvaluatedKey: null
      });
    });
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component with default values', fakeAsync(() => {
    tick(3000); // Wait for async operations to complete

    expect(component.mode).toBe('active');
    expect(component.loading).toBe(true);
    expect(component.loadingTree).toBe(false);
    expect(component.isProcessing).toBe(false);
    expect(component.loaded).toBe(false);
    expect(component.selectedDocuments).toEqual([]);
    expect(component.documentPaths).toEqual([]);
    expect(component.pageInfo).toEqual(new DocumentsPage());
    expect(component.lastEvaluatedKey).toEqual({});
  }));

  // it('should call init() on ngOnInit', () => {
  //   spyOn(component, 'init');
  //   component.ngOnInit();
  //   expect(component.init).toHaveBeenCalled();
  // });

  it('should load folders, companies and permissions on init', fakeAsync(() => {
    const mockCompanies = { companies: [{ id: 1, name: 'Test Company' }] };
    const mockDeletedFiles = { count: 0, rows: [] };
    const mockFolders = [{ id: '1', name: 'Test Folder', type: 'folder' }];
    const mockPermissions = {
      rows: [],
      permissions: []
    };

    // Set up mocks with proper structure
    mockUserService.userCompanies.and.returnValue(of(mockCompanies));
    mockFileService.getDeletedFiles.and.returnValue(of(mockDeletedFiles));
    mockFileService.getFoldersTree.and.returnValue(of(mockFolders));
    mockPermissionsService.getDocumentPermissions.and.returnValue(of(mockPermissions));
    mockFileService.getFilesByIds.and.returnValue(of({ rows: [] }));

    component.init();
    tick(3000);

    expect(mockUserService.userCompanies).toHaveBeenCalled();
    expect(mockFileService.getDeletedFiles).toHaveBeenCalledWith(component.projectInfo.id);
    expect(mockFileService.getFoldersTree).toHaveBeenCalledWith(component.projectInfo.id, {});
    expect(mockPermissionsService.getDocumentPermissions).toHaveBeenCalledWith(component.projectInfo.id);
  }));

  it('should handle folder selection', fakeAsync(() => {
    const mockFolder = { id: '11', name: 'Test Folder', type: 'folder' };
    const mockPaths = [{ id: '11', name: 'Test Folder' }];
    const mockFilesResponse = {
      count: 2,
      rows: [
        { id: '1', name: 'File 1', type: 'file' },
        { id: '2', name: 'File 2', type: 'file' }
      ],
      lastEvaluatedKey: null
    };

    // Set up component state
    component.projectInfo = { id: 1, name: 'Test Project' };
    component.pageInfo = new DocumentsPage();
    component.pageInfo.pageNumber = 1;
    component.pageInfo.pageSize = 10;

    // Override the mock for this specific test
    mockFileService.getFiles.and.returnValue(of(mockFilesResponse));

    // Call the method
    component.onSelectDocument({ folder: mockFolder, paths: mockPaths });
    tick(3000); // Wait for the delayed getFiles call

    // Verify component state
    expect(component.selectedFolder).toEqual(mockFolder);
    expect(component.documentPaths).toEqual(mockPaths);
    expect(component.lastEvaluatedKey).toEqual({
      2: null // The next page's lastEvaluatedKey is set to null
    });
    expect(component.pageInfo.records).toEqual(mockFilesResponse.rows);
    expect(component.pageInfo.totalCount).toBe(mockFilesResponse.count);
    expect(component.loading).toBe(false);
    expect(component.loaded).toBe(true);
  }));

  it('should handle document deletion', () => {
    const mockDoc = { id: '1', name: 'Test Doc', type: 'file' };
    mockFileService.deleteFileOrFolder.and.returnValue(of({}));
    spyOn(component, 'getFilesUnderFolder');
    spyOn(component, 'getFoldersByProject');

    component.onDeleteDocument(mockDoc);

    expect(mockFileService.deleteFileOrFolder).toHaveBeenCalledWith(component.projectInfo.id, mockDoc.id);
    expect(component.getFilesUnderFolder).toHaveBeenCalledWith(component.selectedFolder);
    expect(component.hasDeleteDocument).toBe(true);
  });

  it('should handle folder deletion', () => {
    const mockFolder = { id: '1', name: 'Test Folder', type: 'folder' };
    mockFileService.deleteFileOrFolder.and.returnValue(of({}));
    spyOn(component, 'getFoldersByProject');

    component.onDeleteDocument(mockFolder);

    expect(mockFileService.deleteFileOrFolder).toHaveBeenCalledWith(component.projectInfo.id, mockFolder.id);
    expect(component.getFoldersByProject).toHaveBeenCalled();
    expect(component.hasDeleteDocument).toBe(true);
  });

  it('should handle document restoration', () => {
    const mockDoc = { id: '1', name: 'Test Doc' };
    mockFileService.restoreFileOrFolder.and.returnValue(of({}));
    spyOn(component, 'getDeletedFiles');

    component.onRestoreDocument(mockDoc);

    expect(mockFileService.restoreFileOrFolder).toHaveBeenCalledWith(component.projectInfo.id, mockDoc.id);
    expect(component.getDeletedFiles).toHaveBeenCalled();
  });

  it('should toggle between active and delete modes', () => {
    spyOn(component, 'getDeletedFiles');
    spyOn(component, 'getFoldersByProject');

    component.toggleMode();
    expect(component.mode).toBe('delete');
    expect(component.getDeletedFiles).toHaveBeenCalled();

    component.toggleMode();
    expect(component.mode).toBe('active');
    expect(component.getFoldersByProject).toHaveBeenCalled();
  });

  it('should handle document selection from breadcrumb', () => {
    const mockFolder = { id: '1', name: 'Test Folder', type: 'folder' };
    component.documentPaths = [
      { id: '1', name: 'Root' },
      { id: '2', name: 'Parent' },
      { id: '3', name: 'Current' }
    ];

    spyOn(component, 'getFilesUnderFolder');
    component.onSelectDocumentFromBreadcrumb(mockFolder);

    expect(component.selectedFolder).toEqual(mockFolder);
    expect(component.documentPaths).toEqual([{ id: '1', name: 'Root' }]);
    expect(component.getFilesUnderFolder).toHaveBeenCalledWith(mockFolder);
    expect(component.lastEvaluatedKey).toEqual({});
  });

  it('should handle document selection from table', () => {
    const mockFolder = { id: '1', name: 'Test Folder', type: 'folder' };
    component.documentPaths = [{ id: '1', name: 'Root' }];

    spyOn(component, 'getFilesUnderFolder');
    component.onSelectDocumentFromTable(mockFolder);

    expect(component.selectedFolder).toEqual(mockFolder);
    expect(component.documentPaths).toEqual([{ id: '1', name: 'Root' }, mockFolder]);
    expect(component.getFilesUnderFolder).toHaveBeenCalledWith(mockFolder);
    expect(component.lastEvaluatedKey).toEqual({});
  });

  it('should handle action selection', () => {
    const mockAction = { code: DocumentsActionButtons.ADD_NEW_DOCUMENT };
    component.newDocument = { open: false };
    spyOn(component, 'openNewDocumentModal');

    component.onActionSelection(mockAction);

    expect(component.newDocument.open).toBe(true);
    expect(component.newDocument.destination).toBe(component.selectedFolder?.id);
  });

  it('should handle permission editing', () => {
    const mockPermission = {
      id: 1,
      title: 'Test Permission',
      access_level: 'read',
      perm_type: 'user',
      company_ref: 1,
      user_ref: 1,
      documents: ['1', '2', '3']
    } as DocumentPermission;

    spyOn(component, 'onChangeCompany');
    component.onEditPermission(mockPermission);

    expect(component.permissionToEdit).toEqual({
      id: 1,
      title: 'Test Permission',
      access_level: 'read',
      perm_type: 'user',
      company_ref: mockPermission.company_ref,
      user_ref: mockPermission.user_ref,
      documents: ['1', '2', '3']
    });
    expect(component.onChangeCompany).toHaveBeenCalledWith(mockPermission.company_ref);
  });

  it('should handle document selection', () => {
    const mockSelectedDocs = ['1', '2', '3'];
    component.onSelectDocuments(mockSelectedDocs);
    expect(component.selectedDocuments).toEqual(mockSelectedDocs);
  });

  it('should handle document search functionality', () => {
    const mockFolders = [
      { id: '1', name: 'Test Folder', type: 'folder', children: [
        { id: '2', name: 'Sub Folder', type: 'folder', children: [] }
      ]},
      { id: '3', name: 'Another Folder', type: 'folder', children: [] }
    ];
    component.folders = mockFolders;

    // Test search with matching term
    component.searchFunction({ search: 'Test' });
    expect(component.searchFolders.length).toBe(1);
    expect(component.searchFolders[0].name).toBe('Test Folder');

    // Test search with non-matching term
    component.searchFunction({ search: 'Nonexistent' });
    expect(component.searchFolders.length).toBe(0);

    // Test search with empty term
    component.searchFunction({ search: '' });
    expect(component.searchFolders).toEqual(mockFolders);
  });

  it('should handle document sorting', () => {
    const mockRecords: DocumentItem[] = [
      { id: '1', name: 'B Folder', type: 'folder', createdAt: 1704153600000 }, // 2024-01-02
      { id: '2', name: 'A Folder', type: 'folder', createdAt: 1704067200000 }, // 2024-01-01
      { id: '3', name: 'C File', type: 'file', createdAt: 1704240000000 } // 2024-01-03
    ];
    component.pageInfo.records = [...mockRecords];

    // Test name sorting ascending
    component.onSortChange({ prop: 'name', dir: 'asc' });
    // Folders should be first, sorted by name, then files
    expect(component.pageInfo.records[0].name).toBe('A Folder');
    expect(component.pageInfo.records[1].name).toBe('B Folder');
    expect(component.pageInfo.records[2].name).toBe('C File');

    // Test date sorting descending
    component.onSortChange({ prop: 'createdAt', dir: 'desc' });
    // Folders should be first, sorted by date, then files
    // Folders: B (2024-01-02) comes before A (2024-01-01)
    expect(component.pageInfo.records[0].name).toBe('B Folder');
    expect(component.pageInfo.records[1].name).toBe('A Folder');
    expect(component.pageInfo.records[2].name).toBe('C File');

    // Test date sorting ascending
    component.onSortChange({ prop: 'createdAt', dir: 'asc' });
    // Folders should be first, sorted by date, then files
    // Folders: A (2024-01-01) comes before B (2024-01-02)
    expect(component.pageInfo.records[0].name).toBe('A Folder');
    expect(component.pageInfo.records[1].name).toBe('B Folder');
    expect(component.pageInfo.records[2].name).toBe('C File');
  });

  it('should handle document preview', fakeAsync(() => {
    const mockDoc: DocumentItem = {
      id: '1',
      name: 'Test Doc.pdf',
      type: 'file'
    };
    const mockFilesResponse = {
      count: 1,
      rows: [
        { id: '2', name: 'Child File.pdf', type: 'file' }
      ],
      lastEvaluatedKey: null
    };

    // Set up component state
    component.projectInfo = { id: 1, name: 'Test Project' };
    component.pageInfo = new DocumentsPage();
    component.pageInfo.pageSize = 10;
    component.pageInfo.pageNumber = 1;
    component.lastEvaluatedKey = {};
    component.selectedFolder = {
      id: '11',
      name: 'test folder'
    }

    // Override the mock for this specific test
    mockFileService.getFiles.and.returnValue(of(mockFilesResponse));

    // Call the method
    component.onSelectDocumentFromTable(mockDoc);
    tick(300); // Wait for the delayed getFiles call

    // Verify getFiles was called with correct parameters
    expect(mockFileService.getFiles).toHaveBeenCalledWith(component.projectInfo.id, {
      parentId: mockDoc.id,
      pageSize: component.pageInfo.pageSize
    });
    expect(component.pageInfo.records).toEqual(mockFilesResponse.rows);
    expect(component.pageInfo.totalCount).toBe(mockFilesResponse.count);
  }));

  it('should handle document permissions', () => {
    const mockPermission = {
      id: 1,
      title: 'Test Permission',
      access_level: 'read',
      perm_type: 'user',
      company_ref: 1,
      user_ref: 1,
      documents: ['1', '2']
    } as DocumentPermission;

    // Update the mock to return proper structure
    mockPermissionsService.getDocumentPermissions.and.returnValue(of({
      rows: [mockPermission]
    }));
    spyOn(component, 'getPermissions');

    component.onEditPermission(mockPermission);
    expect(component.permissionToEdit).toEqual({
      ...mockPermission,
      company_ref: mockPermission.company_ref,
      user_ref: mockPermission.user_ref
    });
  });

  it('should handle company change', fakeAsync(() => {
    const mockCompanyId = 1;
    const mockUsers = {
      employees_details: [
        { id: 1, user_ref: { name: 'Test User' } }
      ]
    };

    // Set up the mock response
    mockUserService.getUsersByEmployer.and.returnValue(of(mockUsers));

    // Call the method
    component.onChangeCompany(mockCompanyId);
    tick(); // Wait for async operation to complete

    // Verify the service was called and component state was updated
    expect(mockUserService.getUsersByEmployer).toHaveBeenCalledWith(mockCompanyId);
    expect(component.usersToSelect).toEqual([
      { id: -1, name: 'All' },
      { id: 1, name: 'Test User' }
    ]);
    expect(component.loadingUsers).toBe(false);
  }));

  it('should handle document tree toggle state', () => {
    const mockFolderId = '1'; // Changed to string to match DocumentItem id type

    // Test opening folder
    component.onToggleTree({ folder: { id: mockFolderId, name: 'Test Folder', type: 'folder' } });
    expect(component.openMap[mockFolderId]).toBe(true);
    expect(localStorage.getItem('tree-open-map')).toContain(`"${mockFolderId}":true`);

    // Test closing folder
    component.onToggleTree({ folder: { id: mockFolderId, name: 'Test Folder', type: 'folder' } });
    expect(component.openMap[mockFolderId]).toBe(false);
    expect(localStorage.getItem('tree-open-map')).toContain(`"${mockFolderId}":false`);
  });

  it('should handle document selection and bulk actions', fakeAsync(() => {
    const mockSelectedDocs = ['1', '2', '3'];
    const mockRecords: DocumentItem[] = [
      { id: '1', name: 'Doc 1', type: 'file' },
      { id: '2', name: 'Doc 2', type: 'file' },
      { id: '3', name: 'Doc 3', type: 'file' }
    ];

    // Set up component state
    component.projectInfo = { id: 1, name: 'Test Project' };
    component.pageInfo.records = mockRecords;
    component.selectedDocuments = mockSelectedDocs;

    // Set up spies
    spyOn(component, 'getFilesUnderFolder');
    spyOn(component, 'getFoldersByProject');

    // Mock the confirmation modal to immediately trigger the callback
    const mockConfirmationModal = {
      openConfirmationPopup: jasmine.createSpy('openConfirmationPopup').and.callFake((config) => {
        // Immediately call the onConfirm callback
        config.onConfirm();
      })
    };
    component['confirmationModalRef'] = mockConfirmationModal as any;

    // Set up the mock response for bulkDelete
    mockFileService.bulkDelete.and.returnValue(of({ success: true }));

    // Call the method
    component.onDeleteSelectedDocuments();
    tick(); // Wait for the async operation to complete

    // Verify service calls and component state
    expect(mockFileService.bulkDelete).toHaveBeenCalledWith(
      component.projectInfo.id,
      mockSelectedDocs
    );
    expect(component.getFoldersByProject).toHaveBeenCalled();
    expect(component.selectedDocuments).toEqual([]); // Should be cleared after deletion
    expect(component.hasDeleteDocument).toBe(true);
  }));
});

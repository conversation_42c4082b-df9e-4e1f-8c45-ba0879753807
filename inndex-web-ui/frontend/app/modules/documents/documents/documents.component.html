<div class="wrapper">
    <block-loader [show]="isProcessing" [showBackdrop]="true"></block-loader>
    <div class="page-content px-2">
        <project-header [projectData]="projectInfo" class="mt-3 px-3 nav-tabs"></project-header>

        <div class="mx-3 my-3 d-flex flex-grow-1 overflow-hidden">
            <div class="outer-border-radius mt-3 d-flex flex-column flex-grow-1">
                <div class="mb-2 pb-2 d-flex justify-content-between flex-wrap gap-8">
                    <h5 class="px-3 mb-0">Documents</h5>
                    <action-button
                        class="pr-3"
                        [actionList]="actionButtonMetaData.actionList"
                        (selectedActionEmmiter)="onActionSelection($event)"
                        [hideNewFeatureBtn]="true">
                    </action-button>
                </div>
               
                <div class="mb-3 flex-grow-1 d-flex gap-4 px-3 overflow-hidden" *ngIf="folders.length else noFolder">
                    <div *ngIf="mode === 'active'" class="d-flex flex-column justify-content-between" style="min-width: 260px; overflow: hidden">
                        <ngx-skeleton-loader *ngIf="loadingTree" count="8" [theme]="{'border-radius': '0', height: '50px'}"></ngx-skeleton-loader>
                        <div class="animation-wrapper grow overflow-auto tree-wrapper" *ngIf="!loadingTree">
                            <documents-tree
                                    *ngIf="!loadingTree"
                                    [documents]="searchFolders"
                                    (onChange)="onSelectDocument($event)"
                                    [activeDocument]="selectedFolder"
                                    [openMap]="openMap"
                                    (onToggle)="onToggleTree($event)"
                            ></documents-tree>
                        </div>

                        <div *ngIf="hasDeleteDocument && !loadingTree">
                            <button class="shadow-none btn btn-sm custom-button font-weight-regular" (click)="toggleMode()">
                                {{mode === 'active' ? 'Deleted' : 'Home'}}
                            </button>
                        </div>
                    </div>
                    <div *ngIf="mode === 'active'"  class="grow">
                        <div class="animation-wrapper">
                            <div class="d-flex align-items-center gap-2 mb-3 medium-font h-6">
                                <div *ngFor="let folder of documentPaths; let last=last;" class="d-flex align-items-center">
                                    <span (click)="onSelectDocumentFromBreadcrumb(folder)" class="cursor-pointer" [ngClass]="last ? 'font-weight-bold' : ''" [ngbTooltip]="folder.name.length > nameLimit ? folder.name : ''">{{folder.name.length > nameLimit ? folder.name.slice(0, nameLimit) + '...' : folder.name}}</span>
                                    <span *ngIf="!last" class="material-symbols-outlined ml-2">keyboard_arrow_right</span>
                                </div>
                            </div>
                            <placeholder-page
                                    *ngIf="!loading && !pageInfo.records.length && loaded && canAddNewDocument"
                                    class="animation-wrapper"
                                    [iconUrl]="folderIcon"
                                    title="No documents added"
                                    [description]="['The document management tool lets you organise, store and access files, documents and photos. Setup permissions for groups and individuals.']"
                                    actionTitle="Add new document"
                                    (actionClicked)="openNewDocumentModal()"
                            ></placeholder-page>

                            <ngx-skeleton-loader *ngIf="!loaded" count="8" [theme]="{'border-radius': '0', height: '50px'}"></ngx-skeleton-loader>
                            <documents-table
                                    *ngIf="!!selectedFolder && pageInfo.records.length && loaded"
                                    class="animation-wrapper"
                                    [pageInfo]="pageInfo"
                                    [selectedDocuments]="selectedDocuments"
                                    [project]="projectInfo"
                                    [loadingDocuments]="loading"
                                    (onChange)="onSelectDocumentFromTable($event)"
                                    (onDelete)="onDeleteDocument($event)"
                                    (onSortChange)="onSortChange($event)"
                                    (onEdit)="onEditDocument($event)"
                                    (onSelect)="onSelectDocuments($event)"
                                    (onPageChange)="onPageChange($event)"
                            ></documents-table>
                        </div>
                    </div>
                    <div *ngIf="mode === 'delete'" class="grow">
                        <div class="animation-wrapper h-100 d-flex flex-column">
                            <div class="mb-2 medium-font font-weight-bold">Deleted Files</div>
                            <div class="mb-2 medium-font">You can restore any file deleted in the last 30 days</div>
                            <ngx-skeleton-loader *ngIf="loading" count="8" [theme]="{'border-radius': '0', height: '50px'}"></ngx-skeleton-loader>
                            <div class="flex-grow-1 overflow-auto pb-2">
                                <deleted-documents-table
                                        *ngIf="!!selectedFolder && !loading"
                                        [documents]="deletedDocuments"
                                        [project]="projectInfo"
                                        (onRestore)="onRestoreDocument($event)"
                                ></deleted-documents-table>
                            </div>
                            <div>
                                <button class="shadow-none btn btn-sm custom-button font-weight-regular" (click)="toggleMode()">
                                    {{'Home'}}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <ng-template #noFolder>
                    <placeholder-page
                            *ngIf="loaded && !loading && !folders.length && !pageInfo.records.length"
                            [iconUrl]="folderIcon"
                            title="No folders added"
                            [description]="['The document management tool lets you organise, store and access files, documents and photos. Setup permissions for groups and individuals.']"
                            actionTitle="Add new folder"
                            (actionClicked)="openNewFolderModal()"
                    ></placeholder-page>

                    <ngx-skeleton-loader *ngIf="!loaded || (loading && !folders.length)" count="8" [theme]="{'border-radius': '0', height: '50px'}"></ngx-skeleton-loader>
                    <documents-table
                            *ngIf="!folders.length && pageInfo.records.length"
                            [pageInfo]="pageInfo"
                            [project]="projectInfo"
                            (onChange)="onSelectDocumentFromTable($event)"
                            (onDelete)="onDeleteDocument($event)"
                    ></documents-table>
                </ng-template>
            </div>
        </div>
        <div *ngIf="selectedDocuments.length" class="bottom-bar d-flex justify-content-between align-items-center">
            <div>Items Selected: {{selectedDocuments.length}}/{{pageInfo.records.length}}</div>
            <div class="d-flex align-items-center gap-8">
                <button class="custom-button" (click)="onDeleteSelectedDocuments()">
                    Delete
                </button>
                <button class="custom-button" (click)="onDownloadSelectedDocuments()">
                    <span class="material-symbols-outlined mr-2">download</span>
                    <span>Download</span>
                </button>
            </div>
        </div>
    </div>
</div>

<i-modal
    #newDocumentModal
    title="Add New Document"
    size="lg"
    windowClass='document-modal new-document-modal'
    rightPrimaryBtnTxt="Add"
    [rightPrimaryBtnDisabled]="!newDocument.name || !newDocument.document"
    (onClickRightPB)="onAddNewDocument()"
    (onCancel)="cancelAddNewDocument()"
>
    <div *ngIf="newDocument.open" class="new-document-modal-content">
        <div class="mt-2 mb-3">
            <input
              #fileNameInput="ngModel"
              type="text"
              class="form-control d-block w-100"
              placeholder="File Name *"
              [(ngModel)]="newDocument.name"
              [maxlength]="255"
              name="fileName"
              required
            >
            <div class="small text-danger mt-1" [hidden]="!newDocument.error">{{newDocument.error}}</div>
        </div>

        <div class="mb-3">
            <folder-dropdown
                placeholder="Document Destination *"
                [folders]="folders"
                (onChange)="onChangeNewDocumentDestination($event)"
                [defaultDestination]="selectedFolder"
            ></folder-dropdown>
            <div class="small text-danger mt-1" [hidden]="!newDocument.destinationError">{{newDocument.destinationError}}</div>
        </div>

        <div>
            <p class="mb-2">Document</p>
            <file-uploader
                #attachmentUploader
                class="mt-1 pt-1 mb-1"
                [init]="[]"
                dragnDropTxt="Drag and drop file here"
                [multipleUpload]="false"
                [category]="'project-folder-doc'"
                (uploadDone)="attachmentUploadDone($event)"
                (deleteFileDone)="removeNewDocument()"
                [allowedMimeType]="allowedMimeType"
                [showDeleteBtn]="true"
                [showFileName]="true"
                [showMaxFileSize]="false"
                [hasImgAndDoc]="true"
                [disabled]="isProcessing"
                [path]="selectedFolder?.file_key"
                [isFileServerUpload]="true"
            >
            </file-uploader>
        </div>
    </div>
</i-modal>
<i-modal
        #permissionsModal
        title="Permissions"
        size="lg"
        windowClass='document-modal permissions-modal'
        [showCancel]="false"
        rightPrimaryBtnTxt="Close"
        (onClickRightPB)="onReloadPermissions()"
    >
    <div class="permissions-modal-content">
        <div class="mb-3">
            <button
                class="btn btn-outline-brandeis-blue justify-content-center d-flex align-items-center pointer-cursor"
                (click)="openCreateEditPermissionModal()"
            >
                + New Permission Template
            </button>
        </div>

        <div *ngIf="!loadingPermissions">
            <div *ngFor="let permission of permissions; let i = index" class="mb-4">
                <div class="d-flex gap-16 align-items-center">
                    <table>
                        <tbody>
                        <tr>
                            <td class="gray-cell">
                                Permission Title
                            </td>
                            <td>
                                {{permission.title}}
                            </td>
                        </tr>
                        <tr>
                            <td class="gray-cell">
                                Folder(s)
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 320px">
                                    {{permission.files}}
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="gray-cell">
                                Admin Access Level
                            </td>
                            <td>
                                {{permission.access_level}}
                            </td>
                        </tr>
                        <tr *ngIf="permission.company_name">
                            <td class="gray-cell">
                                Company
                            </td>
                            <td>
                                {{permission.company_name}}
                            </td>
                        </tr>
                        <tr *ngIf="permission.user_name">
                            <td class="gray-cell">
                                User(s)
                            </td>
                            <td>
                                {{permission.user_name}}
                            </td>
                        </tr>
                        <tr>
                            <td class="gray-cell">
                                Permission Type
                            </td>
                            <td>
                                {{permission.type}}
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="d-flex gap-4">
                        <button class="d-flex align-items-center justify-content-center btn-icon" (click)="onEditPermission(permission)">
                            <span class="material-symbols-outlined">edit_note</span>
                        </button>
                        <button class="d-flex align-items-center justify-content-center btn-icon" (click)="removePermission(permission)">
                            <span class="material-symbols-outlined">delete</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</i-modal>
<i-modal #createEditPermissionModal [title]="permissionToEdit.id ? 'Edit Permission' : 'New Permission'" size="lg" windowClass='document-modal new-permission-modal' rightPrimaryBtnTxt="Save"
         (onClickRightPB)="openNewPermissionFolderModal()" [rightPrimaryBtnDisabled]="newPermissionsForm.invalid" (onCancel)="onCloseNewPermissionModal()">
    <div class="new-permission-modal-content">
        <div class="d-flex gap-8 align-items-center mb-3">
            <div class="material-symbols-outlined">info</div>
            <p class="desc">
                Give different permission types to users to allow them to have either full access or read only access.
            </p>
        </div>

        <form novalidate #newPermissionsForm="ngForm">
            <div class="input-group mb-3">
                <input
                        type="text"
                        class="form-control custom-input"
                        placeholder="Permission Title *"
                        [(ngModel)]="permissionToEdit.title"
                        name="permissionTitle"
                        required
                >
            </div>

            <div class="input-group mb-3">
                <ng-select
                    style="width: 100%;"
                    class="dropdown-list sm-select filter-v2-select h-auto w-100"
                    placeholder="Admin Access Level *"
                    [items]="accessLevelsToSelect"
                    name="access_level"
                    [closeOnSelect]="true"
                    [(ngModel)]="permissionToEdit.access_level"
                    [bindLabel]="'label'"
                    [bindValue]="'designation'"
                    dropdownPosition="bottom"
                    required
                >
                    <ng-template ng-option-tmp let-item="item" class="horizontal-center" let-item$="item$" let-index="index" let-search="searchTerm">
                        <label>
                            <span>{{item.label}}</span>
                        </label>
                    </ng-template>
                </ng-select>
            </div>

            <div class="input-group mb-3">
                <ng-select
                  #selectCompany
                  style="width: 100%;"
                  class="dropdown-list sm-select filter-v2-select h-auto w-100"
                  placeholder="Select Company"
                  [items]="companiesToSelect"
                  name="selectedCompany"
                  [closeOnSelect]="true"
                  (ngModelChange)="onChangeCompany($event)"
                  [(ngModel)]="permissionToEdit.company_ref"
                  [bindLabel]="'name'"
                  [bindValue]="'id'"
                  [multiple]="false"
                  [searchable] = 'false'
                  dropdownPosition="bottom"
                >
                    <ng-template ng-header-tmp >
                        <div style="width:100%; max-width:100%;"  class="text-search horizontal-center px-2">
                            <span class="material-symbols-outlined large-font">search</span>
                            <input style="width: 100%; line-height: 24px" placeholder="Search" class="ngx-search " type="search" (input)="selectCompany.filter($event.target.value)"/>
                        </div>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" class="horizontal-center" let-item$="item$" let-index="index" let-search="searchTerm">
                        <label>
                            <span>{{item.name}}</span>
                        </label>
                    </ng-template>
                </ng-select>
            </div>

            <div class="input-group mb-3">
                <ng-select
                  #selectUser
                  style="width: 100%;"
                  class="dropdown-list sm-select filter-v2-select h-auto w-100"
                  placeholder="User"
                  [items]="usersToSelect"
                  name="selectedUser"
                  [closeOnSelect]="true"
                  [(ngModel)]="permissionToEdit.user_ref"
                  [bindLabel]="'name'"
                  [bindValue]="'id'"
                  [multiple]="false"
                  [searchable] = 'false'
                  [loading]="loadingUsers"
                  dropdownPosition="bottom"
                >
                    <ng-template ng-header-tmp >
                        <div style="width:100%; max-width:100%;"  class="text-search horizontal-center px-2">
                            <span class="material-symbols-outlined large-font">search</span>
                            <input style="width: 100%; line-height: 24px" placeholder="Search" class="ngx-search " type="search" (input)="selectUser.filter($event.target.value)"/>
                        </div>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" class="horizontal-center" let-item$="item$" let-index="index" let-search="searchTerm">
                        <label>
                            <span>{{item.name}}</span>
                        </label>
                    </ng-template>
                </ng-select>
            </div>

            <div class="input-group mb-3">
                <ng-select
                    style="width: 100%;"
                    class="dropdown-list sm-select filter-v2-select h-auto w-100"
                    placeholder="Permission Type *"
                    [items]="permissionTypesToSelect"
                    name="permissionType"
                    [closeOnSelect]="true"
                    [(ngModel)]="permissionToEdit.perm_type"
                    [bindLabel]="'label'"
                    [bindValue]="'value'"
                    dropdownPosition="top"
                    required
                >
                    <ng-template ng-option-tmp let-item="item" class="horizontal-center" let-item$="item$" let-index="index" let-search="searchTerm">
                        <label>
                            <span>{{item.label}}</span>
                        </label>
                    </ng-template>
                </ng-select>
            </div>
        </form>
    </div>
</i-modal>

<i-modal #newPermissionSelectFoldersModal
    title="New Permission"
    size="lg" windowClass='document-modal new-permission-modal'
    rightPrimaryBtnTxt="Save"
    (onClickRightPB)="onSavePermission()"
    [rightPrimaryBtnDisabled]="newPermissionSelectFoldersForm.invalid"
>
    <div class="new-permission-modal-content">
        <div class="d-flex gap-8 align-items-center mb-3">
            <div class="material-symbols-outlined">info</div>
            <p class="desc">
                Now that you have created a new permission type, you can select the specific documents and folders to which this permission will apply. Simply choose from the dropdown list of documents and folders below, to link them to the newly created permission type.
            </p>
        </div>

        <form novalidate #newPermissionSelectFoldersForm="ngForm">
            <div class="mb-3">
                <documents-dropdown
                    placeholder="Folder *"
                    [documents]="foldersToSelect"
                    [required]="true"
                    name="selectedFolders"
                    (onChange)="permissionToEdit.documents = $event"
                >
                </documents-dropdown>
            </div>
        </form>
    </div>
</i-modal>

<i-modal
    #newFolderModal
    title="Add New Folder"
    size="lg"
    windowClass='document-modal new-folder-modal'
    rightPrimaryBtnTxt="Add"
    (onClickRightPB)="onAddNewFolder()"
    (onCancel)="cancelAddNewFolder()"
    [rightPrimaryBtnDisabled]="!newFolder.name"
>
    <div *ngIf="newFolder.open" class="new-folder-modal-content">
        <div class="mb-3">
            <input
              #folderNameInput="ngModel"
              type="text"
              class="form-control d-block w-100"
              placeholder="Folder Name *"
              [(ngModel)]="newFolder.name"
              name="folderName"
              [maxlength]="255"
              required
            >
            <div class="small text-danger mt-1" [hidden]="!newFolder.error">{{newFolder.error}}</div>
        </div>

        <div class="mb-3" *ngIf="folders.length">
            <folder-dropdown
                placeholder="Folder Destination *"
                [folders]="folders"
                (onChange)="newFolder.destination = $event"
                [defaultDestination]="selectedFolder"
            ></folder-dropdown>
        </div>
    </div>
</i-modal>


<i-modal
    #editDocumentModal
    [title]="editDocument.type === FileType.FOLDER ? 'Edit Folder' : 'Edit Document'"
    size="lg"
    windowClass='document-modal new-folder-modal'
    rightPrimaryBtnTxt="Save"
    (onClickRightPB)="onSaveDocument()"
    (onCancel)="cancelEditDocument()"
    [rightPrimaryBtnDisabled]="!editDocument.name"
>
    <div *ngIf="editDocument.id" class="new-folder-modal-content">
        <div class="mb-3">
            <input
                #documentNameInput="ngModel"
                type="text"
                class="form-control d-block w-100"
                [placeholder]="editDocument.type === FileType.FOLDER ? 'Folder Name *' : 'File Name *'"
                [(ngModel)]="editDocument.name"
                [maxlength]="255"
                name="editDocumentName"
                required
            >
            <div class="small text-danger mt-1" [hidden]="!editDocument.error">{{editDocument.error}}</div>
        </div>

        <div class="mb-3" *ngIf="folders.length">
            <folder-dropdown
                [placeholder]="editDocument.type === FileType.FOLDER ? 'Folder Destination *' : 'Document Destination *'"
                [folders]="folders"
                (onChange)="editDocument.parent_id = $event"
                [defaultDestination]="selectedFolder"
            ></folder-dropdown>
        </div>
    </div>
</i-modal>

<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

@use "./variables";

.wrapper {
    gap: 6px;
    height: 40px;
    width: 100%;
    padding-left: 8px;

    &:hover {
        background-color: #0066FF0D;
        border-left: 3px solid #0066FF;
        border-radius: 2.5px;
    }
}

.children-content {
    transition: all ease-in 1s;
}

.permissions-folder-dropdown-treeview .ngx-search {
    width: 100%;
}

// Form validation
.custom-dropdown.is-invalid .permissions-folder-dropdown-treeview button {
    border-color: variables.$error-red-color !important;
}

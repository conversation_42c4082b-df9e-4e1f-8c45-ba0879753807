import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import { ControlContainer, NgForm } from '@angular/forms';
import { TreeviewItem, TreeviewConfig, DropdownTreeviewComponent, TreeviewI18n } from 'ngx-treeview';
import { DocumentItem, DropdownTreeviewSelectI18n } from '@app/core';

@Component({
  selector: 'documents-dropdown',
  templateUrl: './documents-dropdown.component.html',
  styleUrls: ['./documents-dropdown.component.scss'],
  viewProviders: [{provide: ControlContainer, useExisting: NgForm}],
  providers: [{ provide: TreeviewI18n, useClass: DropdownTreeviewSelectI18n }]
})
export class DocumentsDropdownComponent implements OnInit, AfterViewInit, OnChanges {
  @Input() documents: DocumentItem[] = [];
  @Input() placeholder = 'Select Folder';
  @Input() required = false;
  @Input() name = 'selectedDocuments';
  @Input() requiredMessage = 'Please select at least one folder';
  @Input() errorClasses = '';
  @Output() onChange = new EventEmitter<any[]>();

  @ViewChild(DropdownTreeviewComponent, { static: false }) dropdownTreeviewComponent: DropdownTreeviewComponent;
  private dropdownTreeviewSelectI18n: DropdownTreeviewSelectI18n;
  @ViewChild('backdrop') backdrop;

  treeConfig = TreeviewConfig.create({
    hasAllCheckBox: true,
    hasCollapseExpand: true,
    hasFilter: true,
    maxHeight: 500
  });
  treeItems: TreeviewItem[] = [];
  selectedFolder: any;
  filterText = '';
  
  selectedItems: any[] = [];

  constructor(public i18n: TreeviewI18n,) {
    this.dropdownTreeviewSelectI18n = i18n as DropdownTreeviewSelectI18n;
  }

  generateTreeItem(document: DocumentItem) {
    return {
      text: document.name,
      value: document.id,
      checked: false,
      children: document.children?.map(el => this.generateTreeItem(el))
    };
  }
  generateTree(documents: DocumentItem[]) {
    this.treeItems = [];
    for (const folder of documents) {
      const treeItem = new TreeviewItem(this.generateTreeItem(folder));
      this.treeItems.push(treeItem);
    }
  }
  ngOnInit(): void {
    this.generateTree(this.documents);
  }
  ngOnChanges(changes: SimpleChanges) {
    if (changes['documents']?.currentValue) {
      this.generateTree(changes['documents'].currentValue);
    }
    if (changes['defaultDestination']?.currentValue) {
      this.generateTree(this.documents);
      const treeItem = new TreeviewItem(this.generateTreeItem(changes['defaultDestination'].currentValue));
      this.selectedFolder = treeItem;
      this.dropdownTreeviewSelectI18n.selectedItem = treeItem;

      if (this.dropdownTreeviewComponent) {
        this.dropdownTreeviewComponent.onSelectedChange([treeItem]);
        this.dropdownTreeviewComponent.buttonLabel = treeItem?.text || 'Document Destination *';
        this.onChange.emit(treeItem.value);
      }
    }
  }

  ngAfterViewInit() {
    if (this.dropdownTreeviewComponent) {
      this.dropdownTreeviewSelectI18n.selectedItem = new TreeviewItem({
        text: this.placeholder,
        value: ''
      });
    }
  }

  select(item: TreeviewItem): void {
    if(item.value == this.selectedFolder?.value) {
      this.selectItem(null);
      this.dropdownTreeviewSelectI18n.selectedItem = null;
      this.selectedFolder = null;
      this.dropdownTreeviewComponent.buttonLabel = 'Select Folder';
    } else {
      this.selectItem(item);
      this.selectedFolder = item;
    }
  }

  private selectItem(item: TreeviewItem): void {
    if (!item) {
      this.onChange.emit(null);
    }

    if (item && this.dropdownTreeviewSelectI18n.selectedItem !== item) {
      this.dropdownTreeviewSelectI18n.selectedItem = item;
      if (this.dropdownTreeviewComponent) {
        this.dropdownTreeviewComponent.onSelectedChange([item]);
        this.dropdownTreeviewComponent.buttonLabel = item?.text || 'Document Destination *';
      }

      this.onChange.emit(item.value);
    }
    this.backdrop.nativeElement.click();
  }

  onCheckedChange(event) {
    const id = event.target.id.split('tree_')[1]
    const item: TreeviewItem = this.findTreeItem(this.treeItems, id)

    item.checked = event.target.checked
    item.setCheckedRecursive(event.target.checked)

    const selectedItems = this.getSelectedItems(this.treeItems)
    console.log('selectedItems', selectedItems)
    
    // Update selectedItems for form validation
    this.selectedItems = selectedItems;
    
    this.onChange.emit(selectedItems)
  }

  findTreeItem(items: TreeviewItem[], id: string) {
    for (const item of items) {
      if (item.value === id) return item

      if (item.children?.length) {
        const node = this.findTreeItem(item.children, id)
        if (node) return node
      }
    }
  }

  getSelectedItems(items: TreeviewItem[]) {
    let selectedIds = items.filter(item => item.checked).map(item => item.value)

    for (const item of items) {
      if (item.children?.length) {
        const childSelectedIds = this.getSelectedItems(item.children)
        selectedIds = selectedIds.concat(childSelectedIds)
      }
    }

    return selectedIds
  }
}

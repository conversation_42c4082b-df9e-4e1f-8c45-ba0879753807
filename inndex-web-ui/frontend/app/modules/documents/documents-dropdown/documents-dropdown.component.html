<div class="custom-dropdown" [ngClass]="{'is-invalid': documentsModel.errors && documentsModel.errors.required}">
    <ng-template #itemTemplate let-item="item" let-onCollapseExpand="onCollapseExpand">
        <div class="cursor-pointer d-flex align-items-center py-1 px-2 hover:bg-gray-100">
            <span *ngIf="item.children" (click)="onCollapseExpand()" class="me-2" aria-hidden="true">
                <span *ngIf="item.collapsed" class="material-symbols-outlined">chevron_right</span>
                <span *ngIf="!item.collapsed" class="material-symbols-outlined">expand_more</span>
            </span>
            <div [ngClass]="{'custom-control custom-checkbox': item.text !== 'All Workspace Documents', 'd-flex align-items-center flex-grow-1': true}">
                <input type="checkbox"
                       class="custom-control-input me-2"
                       [checked]="item.checked"
                       [indeterminate]="item.indeterminate"
                       [id]="'tree_'+item.value"
                       (change)="onCheckedChange($event)">
                <label [ngClass]="{'custom-control-label': item.text !== 'All Workspace Documents', 'mb-0 cursor-pointer': true}"
                       [for]="'tree_'+item.value">
                    {{ item.text }}
                </label>
            </div>
        </div>
    </ng-template>
    <ng-template #headerTemplate let-config="config" let-item="item" let-onCollapseExpand="onCollapseExpand"
                 let-onCheckedChange="onCheckedChange" let-onFilterTextChange="onFilterTextChange">
        <div *ngIf="config.hasFilter" class="row row-filter">
            <div style="width: 96%; max-width:100%; margin-left: 10px;"  class="text-search horizontal-center px-2 mb-3">
                <span class="material-symbols-outlined font-small">search</span>
                <input placeholder="Search" class="ngx-search py-2" type="search" name = 'filterText'  [(ngModel)]="filterText" (ngModelChange)="filterText && onFilterTextChange($event)" />
            </div>
        </div>
    </ng-template>
    <ngx-dropdown-treeview
        class="permissions-folder-dropdown-treeview"
        [config]="treeConfig"
        [items]="treeItems"
        [itemTemplate]="itemTemplate"
        [headerTemplate]="headerTemplate"
    >
    </ngx-dropdown-treeview>
    <div #backdrop></div>
</div>

<input type="hidden"
    [name]="name" 
    [required]="required" 
    [ngModel]="selectedItems.length ? 'valid' : ''" 
    #documentsModel="ngModel"
/>

<div class="alert alert-danger small mt-1" [class]="errorClasses" [hidden]="!(documentsModel.errors && documentsModel.errors.required)">
    {{requiredMessage}}
</div>

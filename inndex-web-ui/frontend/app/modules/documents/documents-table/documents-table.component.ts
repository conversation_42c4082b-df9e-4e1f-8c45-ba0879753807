import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import {DocumentItem, FileService, ToastService, FileType, Project, DocumentsPage, AuthService} from '@app/core';
import { GenericModalComponent } from '@app/modules/common';
import { User } from '@app/core/models/user.model';
import { AssetsUrl, GenericConfirmationModalComponent } from '@app/shared';
import * as dayjs from 'dayjs';
import {AppConstant} from "@env/environment";
import { PdfJsViewerComponent } from 'ng2-pdfjs-viewer';
import {saveAs} from "file-saver";

@Component({
  selector: 'documents-table',
  templateUrl: './documents-table.component.html',
  styleUrls: ['./documents-table.component.scss']
})
export class DocumentsTableComponent implements OnInit {
  @Input() project: Project;
  @Input() selectedDocuments: string[] = [];
  @Input() loadingDocuments: boolean = false
  @Input() pageInfo: DocumentsPage;

  @Output() onChange = new EventEmitter();
  @Output() onDelete = new EventEmitter();
  @Output() onEdit = new EventEmitter();
  @Output() onSortChange = new EventEmitter();
  @Output() onSelect = new EventEmitter();
  @Output() onPageChange = new EventEmitter();

  @ViewChild('documentDetailModal') private documentDetailModal: GenericModalComponent
  @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
  @ViewChild('pdfViewer') private pdfViewer: PdfJsViewerComponent;

  AppConstant = AppConstant;
  documentsIcons = AssetsUrl.documentsIcons;
  FileType = FileType;
  nameLimit = 30;
  isOpened = false

  modalTitle = 'Document Detail'
  selectedDoc: DocumentItem;
  signedUrl = '';
  sorts: {
    dir:string, prop: string
  } = {
    dir:'desc', prop: ''
  };
  isProcessing = false;
  isHovered = false;

  constructor(
    private fileService: FileService,
    private toastService: ToastService,
    private authService: AuthService
  ) {}
  
  authUser$: User;

  ngOnInit(): void {
    this.authService.authUser.subscribe(data => {
        if (data && data.id) {
            this.authUser$ = data;
        }
    });
  }

  dayjs(n: number) {
    let tz = this.project?.custom_field?.timezone;
    return dayjs(n).tz(tz);
  }
  openDetailModal(document: DocumentItem) {
    if (this.isOpened) return
    this.isOpened = true

    if (document.type !== FileType.FOLDER) {
      this.selectedDoc = document;
      this.fileService.getFileUrl(this.project.id, document.id).subscribe((res: any) => {
        this.signedUrl = this.selectedDoc.extension === 'application/pdf' ? encodeURIComponent(res.url) : res.url;
        this.modalTitle = document.name;
        this.documentDetailModal.open();
        setTimeout(() => {
          if (this.pdfViewer) {
            this.pdfViewer.refresh();
          }
        }, 0)
      }, () => {
        this.isOpened = false
      });
    } else {
      this.onChange.emit(document);
      this.isOpened = false
    }
  }
  showDeleteConfirmModal(document: DocumentItem) {
    this.confirmationModalRef.openConfirmationPopup({
      headerTitle: 'Delete Item',
      title: `Do you wish to delete ${document.name.length > 30 ? document.name.slice(0, 30) + '...' : document.name }? <br /><br />Deleted items can be found in your deleted files. They will be permanently deleted after 30 days.`,
      confirmLabel: 'Delete',
      onConfirm: () => {
        this.onDelete.emit(document);
      }
    });
  }
  onEditDocument(document: DocumentItem) {
    this.onEdit.emit(document);
  }
  onCancel() {
    this.signedUrl = ''
    this.selectedDoc = null
    this.isOpened = false
    this.documentDetailModal.close()
  }

  get showFooterInModal() {
    return this.selectedDoc?.type === FileType.IMAGE || this.selectedDoc?.type === FileType.VIDEO;
  }

  onSort($event){
    let {sorts} = $event;
    let{ dir, prop } = sorts[0];
    this.sorts = { dir, prop };
    this.onSortChange.emit(this.sorts)
  }
  downloadDocument(row: DocumentItem) {
    this.isProcessing = true;
    this.fileService.getFileUrl(this.project.id, row.id).subscribe((res: any) => {
      fetch(res.url).then(response => response.blob()).then((blob) => {
        saveAs(blob, row.name)
      }).finally(() => {
        this.isProcessing = false;
      });
    }, error => {
      this.isProcessing = false;
      const message = (error.headers && error.headers.get('message')) ? error.headers.get('message') : 'Failed to get document file.';
      this.toastService.show(this.toastService.types.ERROR, message, { data: error });
    });
  }

  onSelectAllDocuments(e) {
    const newSelectedDocuments = e.target.checked ? this.pageInfo.records.map((el => el.id)) : []
    this.onSelect.emit(newSelectedDocuments)
  }
  onSelectDocument(row: DocumentItem) {
    let newSelectedDocuments: string[]
    if (this.selectedDocuments.includes(row.id)) {
      newSelectedDocuments = this.selectedDocuments.filter(el => el !== row.id)
    } else {
      newSelectedDocuments = [...this.selectedDocuments, row.id]
    }
    this.onSelect.emit(newSelectedDocuments)
  }

  canDelete(row: DocumentItem) {
    const isOwner = row?.createdBy?.id === this.authUser$.id
    return isOwner || !!row.permissions?.find(el => el.perm_type === 'create_read_update_delete')
  }

  pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isArchived, isPageChange) {
    this.onPageChange.emit(pageInfo)
  }
}

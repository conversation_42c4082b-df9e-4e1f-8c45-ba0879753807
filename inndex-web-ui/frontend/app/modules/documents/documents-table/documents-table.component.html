<block-loader [show]="isProcessing" [showBackdrop]="true"></block-loader>
<div class="documents-table table-responsive-sm w-100" (mouseenter)="isHovered = true" (mouseleave)="isHovered = false">
    <ngx-datatable
      #table class="bootstrap table table-hover table-sm ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h mb-0"
      [scrollbarV]="true"
      [virtualization]="false"
      [rows]="pageInfo.records"
      [columnMode]="'force'"
      [rowHeight]="'auto'"
      [footerHeight]="40"
      (sort)="onSort($event)"
      [sorts]="[sorts]"
      [externalSorting]="true"
      [externalPaging]="true"
      [count]="pageInfo.totalCount"
      [offset]="0"
      [limit]="pageInfo.pageSize"
      (page)="pageCallback($event, false, true)"
      [loadingIndicator]="loadingDocuments"
    >
        <ngx-datatable-column prop="checkbox" headerClass="font-weight-bold" [sortable]="false" [width]="60" [minWidth]="60" [maxWidth]="60">
            <ng-template let-column="column" ngx-datatable-header-template>
                <div *ngIf="isHovered || !!selectedDocuments.length" class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="select_all" [checked]="selectedDocuments.length === pageInfo.records.length" (change)="onSelectAllDocuments($event)">
                    <label class="custom-control-label pr-5" for="select_all">
                    </label>
                </div>
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                <div *ngIf="isHovered || !!selectedDocuments.length" class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" [id]="'select_'+ row.id" [checked]="selectedDocuments.includes(row.id)" (change)="onSelectDocument(row)">
                    <label class="custom-control-label pr-5" [for]="'select_'+ row.id">
                    </label>
                </div>
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column prop="name" headerClass="font-weight-bold" [sortable]="true">
            <ng-template let-column="column" ngx-datatable-header-template>
                Name
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                <div class="d-flex align-items-center">
                    <img *ngIf="row.type === FileType.FOLDER" [src]="documentsIcons.folder" class="mr-2"/>
                    <img *ngIf="row.type === FileType.IMAGE" [src]="documentsIcons.image" class="file-icon mr-2" />
                    <img *ngIf="row.type === FileType.VIDEO" [src]="documentsIcons.video" class="file-icon mr-2" />
                    <img *ngIf="row.type === FileType.DOCUMENT" [src]="documentsIcons.pdf" class="file-icon mr-2" />
                    <span
                        class="hover-primary-text font-weight-regular"
                        [ngbTooltip]="row.name.length > nameLimit ? row.name : ''"
                        (click)="openDetailModal(row)"
                    >
                        {{row.name.length > nameLimit ? row.name.slice(0, nameLimit) + '...' : row.name}}
                    </span>
                </div>
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column prop="createdAt" headerClass="font-weight-bold" [sortable]="true">
            <ng-template let-column="column" ngx-datatable-header-template>
                Date Created
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                {{dayjs(row?.createdAt).format(AppConstant.dateTime_DD_MM_YYYY_hh_mm_A)}} {{row.createdBy?.id ? 'by' : ''}} {{row.createdBy?.user_name}}
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false">
            <ng-template let-column="column" ngx-datatable-header-template>
                Action
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                <div class="d-flex align-items-center" style="gap: 8px">
                    <button *ngIf="row.type !== FileType.FOLDER" class="btn btn-sm btn-action mr-1 d-flex align-items-center justify-content-center" (click)="downloadDocument(row)">
                        <span class="material-symbols-outlined">download</span>
                    </button>
                    <button class="btn btn-sm btn-action mr-1 d-flex align-items-center justify-content-center" (click)="onEditDocument(row)">
                        <span class="material-symbols-outlined">edit_note</span>
                    </button>
                    <button
                        class="btn btn-sm btn-action mr-1 d-flex align-items-center justify-content-center"
                        (click)="showDeleteConfirmModal(row)"
                        *ngIf="canDelete(row)"
                    >
                        <span class="material-symbols-outlined">delete</span>
                    </button>
                </div>
            </ng-template>
        </ngx-datatable-column>
    </ngx-datatable>
</div>

<i-modal
  #documentDetailModal
  [title]="modalTitle"
  size="lg"
  windowClass='document-detail-modal'
  [showFooter]="showFooterInModal"
  rightPrimaryBtnTxt="Done"
  (onClickRightPB)="onCancel()"
  (onCancel)="onCancel()"
  [showCancel]="false">
    <div class="modal-content" [ngClass]="showFooterInModal ? 'radius-0 border-0' : ''">
        <div class="wrapper" [ngClass]="selectedDoc?.type === FileType.IMAGE || selectedDoc?.type === FileType.VIDEO ? 'py-0' : 'p-3'">
            <img *ngIf="selectedDoc?.type === FileType.IMAGE" [src]="signedUrl" />
            <video *ngIf="selectedDoc?.type === FileType.VIDEO" [src]="signedUrl" controls></video>
            <ng2-pdfjs-viewer
                *ngIf="signedUrl && selectedDoc?.type === FileType.DOCUMENT"
                class="w-full"
                [pdfSrc]="signedUrl"
                [fullScreen]="true"
                [page]="1"
                [print]="false"
                [openFile]="false"
                [viewBookmark]="false"
                viewerId="viewSignedDocument"
                #pdfViewer
            >
            </ng2-pdfjs-viewer>
        </div>
    </div>
</i-modal>

<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

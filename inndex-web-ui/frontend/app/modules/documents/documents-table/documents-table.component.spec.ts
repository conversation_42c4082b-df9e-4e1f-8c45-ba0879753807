import { ComponentFixture, fakeAsync, TestBed, tick, waitForAsync } from '@angular/core/testing';
import { DocumentsTableComponent } from './documents-table.component';
import { FileService, ToastService, FileType, Project, DocumentItem, DoNotAskAgainService, DocumentsPage, AuthService } from '@app/core';
import { GenericConfirmationModalComponent, IModalComponent } from '@app/shared';
import { of } from 'rxjs';
import { AppConstant } from '@env/environment';
import { CommonModule, TitleCasePipe, UpperCasePipe } from "@angular/common";
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';

describe('DocumentsTableComponent', () => {
  let component: DocumentsTableComponent;
  let fixture: ComponentFixture<DocumentsTableComponent>;
  let mockFileService: jasmine.SpyObj<FileService>;
  let toastService: jasmine.SpyObj<ToastService>;
  let mockAuthService: jasmine.SpyObj<AuthService>;

  const mockProject: Project = {
    id: 123,
    name: 'Test Project',
    custom_field: {
      timezone: 'UTC'
    }
  };

  const mockDocument: DocumentItem = {
    id: '1',
    name: 'test.pdf',
    type: FileType.DOCUMENT,
    extension: 'application/pdf',
    createdAt: Date.now()
  };

  beforeEach(waitForAsync(() => {
    const toastServiceSpy = jasmine.createSpyObj("ToastService", [
      "close",
      "show",
      "pauseToast",
      "resumeToast",
    ]);

    const fileServiceSpy = jasmine.createSpyObj("FileService", [
      "getFile",
      "getFileUrl"
    ]);
    fileServiceSpy.getFile.and.returnValue(of({ id: '2', name: 'Parent', parent_id: null }));
    fileServiceSpy.getFileUrl.and.returnValue(of({ url: 'https://imageurl' }));

    const authServiceSpy = jasmine.createSpyObj("AuthService", ["getUser"]);
    authServiceSpy.authUser = of({ id: 1, email: '<EMAIL>' });

    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        RouterTestingModule,
        HttpClientTestingModule,
      ],
      declarations: [
        DocumentsTableComponent,
        IModalComponent,
        GenericConfirmationModalComponent,
      ],
      providers: [
        { provide: FileService, useValue: fileServiceSpy },
        { provide: ToastService, useValue: toastServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        TitleCasePipe,
        UpperCasePipe,
        DoNotAskAgainService,
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DocumentsTableComponent);
    component = fixture.componentInstance;
    component.AppConstant = AppConstant;
    component.project = mockProject;
    component.pageInfo = new DocumentsPage();
    toastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockFileService = TestBed.inject(FileService) as jasmine.SpyObj<FileService>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.pageInfo.records).toEqual([]);
    expect(component.pageInfo.totalCount).toBe(0);
    expect(component.selectedDocuments).toEqual([]);
    expect(component.loadingDocuments).toBeFalsy();
    expect(component.isOpened).toBeFalsy();
    expect(component.isProcessing).toBeFalsy();
    expect(component.isHovered).toBeFalsy();
  });

  it('should format date using dayjs with project timezone', () => {
    const timestamp = Date.now();
    const formattedDate = component.dayjs(timestamp);
    expect(formattedDate).toBeDefined();
  });

  it('should open detail modal for non-folder documents', () => {
    const mockUrl = 'https://test.com/document.pdf';
    mockFileService.getFileUrl.and.returnValue(of({ url: mockUrl }));

    component.openDetailModal(mockDocument);
    expect(component.isOpened).toBeTruthy();
    expect(component.selectedDoc).toEqual(mockDocument);
    expect(component.signedUrl).toBe(encodeURIComponent(mockUrl));
  });

  it('should handle folder document selection', () => {
    const folderDocument = { ...mockDocument, type: FileType.FOLDER };
    spyOn(component.onChange, 'emit');

    component.openDetailModal(folderDocument);
    expect(component.isOpened).toBeFalsy();
    expect(component.onChange.emit).toHaveBeenCalledWith(folderDocument);
  });

  it('should handle document download successfully', () => {
    const mockUrl = 'https://test.com/document.pdf';
    const mockBlob = new Blob(['test']);
    mockFileService.getFileUrl.and.returnValue(of({ url: mockUrl }));

    spyOn(window, 'fetch').and.returnValue(Promise.resolve({
      blob: () => Promise.resolve(mockBlob)
    } as Response));

    component.downloadDocument(mockDocument);
    expect(component.isProcessing).toBeTruthy();
    expect(mockFileService.getFileUrl).toHaveBeenCalledWith(mockProject.id, mockDocument.id);
  });

  it('should handle document selection', fakeAsync(() => {
    spyOn(component.onSelect, 'emit');
    component.selectedDocuments = ['1'];

    component.onSelectDocument(mockDocument);
    tick(300)

    expect(component.onSelect.emit).toHaveBeenCalledWith([]);
  }));

  it('should handle select all documents', () => {
    spyOn(component.onSelect, 'emit');
    component.pageInfo.records = [mockDocument];

    component.onSelectAllDocuments({ target: { checked: true } });
    expect(component.onSelect.emit).toHaveBeenCalledWith([mockDocument.id]);
  });

  it('should handle sort change', () => {
    spyOn(component.onSortChange, 'emit');
    const sortEvent = {
      sorts: [{ dir: 'asc', prop: 'name' }]
    };

    component.onSort(sortEvent);
    expect(component.sorts).toEqual({ dir: 'asc', prop: 'name' });
    expect(component.onSortChange.emit).toHaveBeenCalledWith({ dir: 'asc', prop: 'name' });
  });

  it('should handle page change', () => {
    spyOn(component.onPageChange, 'emit');
    const pageInfo = { count: 10, pageSize: 5, limit: 5, offset: 0 };

    component.pageCallback(pageInfo, false, true);
    expect(component.onPageChange.emit).toHaveBeenCalledWith(pageInfo);
  });

  it('should determine if footer should be shown in modal', () => {
    component.selectedDoc = { ...mockDocument, type: FileType.IMAGE };
    expect(component.showFooterInModal).toBeTruthy();

    component.selectedDoc = { ...mockDocument, type: FileType.VIDEO };
    expect(component.showFooterInModal).toBeTruthy();

    component.selectedDoc = { ...mockDocument, type: FileType.DOCUMENT };
    expect(component.showFooterInModal).toBeFalsy();
  });

  it('should handle loadingDocuments input', () => {
    component.loadingDocuments = true;
    fixture.detectChanges();
    expect(component.loadingDocuments).toBeTruthy();
  });
});

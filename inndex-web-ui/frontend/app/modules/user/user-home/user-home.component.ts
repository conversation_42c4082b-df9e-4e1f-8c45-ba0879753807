import { ChangeDetectorRef, Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { DomSanitizer } from "@angular/platform-browser";
import { Router } from "@angular/router";
import { AuthService, Comment, Common, InductionRequest, ProjectService, ToastService, User, UserService } from "@app/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import * as dayjs from 'dayjs';
import { forkJoin } from "rxjs";
import { take } from "rxjs/operators";
import {AppConstant} from "@env/environment";
import { GenericConfirmationModalComponent, IModalComponent } from '@app/shared';

@Component({
    selector: 'user-home',
    templateUrl: './user-home.component.html',
    //styleUrls: ['./use-home.component.css']
    encapsulation: ViewEncapsulation.None,
})
export class UserHomeComponent implements OnInit {

    // previewURL: any;
    editURL: any;

    authUser$: User;
    dateDisplayFormat: string = AppConstant.displayDateFormat;
    dayjs(n: number){
        return dayjs(n);
    };

    STATUS_COLORS: any = {0 : "danger", 2 : "success", 4: "none text-black-50 disabled", 5: "none text-black-50 disabled"};

    iframe_content_loading: boolean = false;
    induction_requests_loading: boolean = false;
    request_resubmitting: boolean = false;
    induction_requests: Array<InductionRequest> = [];
    inductionRequest: InductionRequest;

    //new_ref_no: string;
    public query: any = {
        search : null
    };
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;

    constructor(
        private userService: UserService,
        private toastService: ToastService,
        private authService: AuthService,
        private projectService: ProjectService,
        private router: Router,
        private ref: ChangeDetectorRef,
        private modalService: NgbModal,
        private sanitizer: DomSanitizer,
    ) {
    }

    ngOnInit() {
        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
            }
        });
        this.fetchMyInductionRequests();
    }

    fetchMyInductionRequests(){
        this.induction_requests_loading = true;
        this.userService.getMyInductionRequests().subscribe((data:any) =>{
            this.induction_requests_loading = false;
            if(data.success && data.induction_requests){
                this.induction_requests = data.induction_requests;
                // this.induction_requests = [{ createdAt:1538379352519 }];
            }else{
                const message = data.message || 'Failed to fetch data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    trackById(index, record) {
        return record.id;
    }

    onQRValueChange(data:any){
        if(data){
            this.query.search = data;
            this.ref.detectChanges();
        }
    }

    @ViewChild('content')
    private content: IModalComponent;
    openLg(inductionRequest, content, editRouteLink) {
        this.editURL = editRouteLink;
        this.inductionRequest = inductionRequest;
        this.content.open();

        this.iframe_content_loading = true;
        this.openInductionForm(inductionRequest, 'html', (html) => {

            let iframe = document.getElementById('previewFrame') as HTMLIFrameElement;

            let doc =  iframe.contentDocument;// || iframe.contentWindow;
            // doc.open();
            doc.write(html);
            this.iframe_content_loading = false;
            // doc.close();
            // this.previewURL = this.sanitizer.bypassSecurityTrustHtml(html); //this.sanitizer.bypassSecurityTrustResourceUrl(url + '?embed=frame');

        }, false);
    }

    resubmitInductionRequest(event){
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Induction Resubmission',
            title: `Are you sure to resubmit your induction with updated profile information?`,
            confirmLabel: 'Resubmit',
            onConfirm: () => {
                event.closeFn();
                this.request_resubmitting = true;
                this.refreshInductionRequestData(this.inductionRequest, (error, inductionRequest) => {
                    if(inductionRequest){
                        if(!inductionRequest.comments){
                            inductionRequest.comments = [];
                        }
                        inductionRequest.status_code = 1;
                        let comment = new Comment();
                        comment.timestamp = dayjs().valueOf();
                        comment.user_id = inductionRequest.additional_data.user_info.id;
                        comment.name = inductionRequest.additional_data.user_info.name;
                        comment.origin = 'system'; // as comment was system generated
                        comment.note = 'Updated Existing Request';
                        inductionRequest.comments.push(comment);
                        this.updateExistingRequest(inductionRequest, () => {
                            this.request_resubmitting = false;
                        });
                    }else{
                        this.request_resubmitting = false;
                    }
                });
            }
        });
    }

    refreshInductionRequestData(inductionRequest, cb){

        if(!inductionRequest.additional_data){
            inductionRequest.additional_data = {};
        }
        this.authService.authUser
            .pipe(take(1))
                .subscribe(data =>{
                if(data && data.id){
                    inductionRequest.additional_data.user_info = data;

                    //let userInfoRequest = this.authService.authUser;
                    let contactRequest = this.userService.getMyContactDetail();
                    let employmentDetailRequest = this.userService.getMyEmploymentDetail();
                    let healthAssessmentAnswersRequest = this.userService.getMyHealthAssessmentAnswers();
                    //let projectRequest = this.projectService.getProject(inductionRequest.project_ref.id);
                    // Observable.forkJoin (RxJS 5) changes to just forkJoin() in RxJS 6
                    forkJoin([
                        contactRequest,
                        employmentDetailRequest,
                        healthAssessmentAnswersRequest,
                        //userInfoRequest,
                        //projectRequest,
                    ]).subscribe(responseList => {
                        let error = responseList.filter(r => !r.success);
                        if(error && error.length){
                            // Error in one of n/w call
                            return cb('Failed to Load User Data');
                        }
                        inductionRequest.additional_data.platform_type = 'web';
                        inductionRequest.additional_data.contact_detail = responseList[0].contact_detail;
                        inductionRequest.additional_data.employment_detail = responseList[1].employment_detail;
                        inductionRequest.additional_data.health_assessment_answers = responseList[2].health_assessments;
                        //inductionRequest.additional_data.user_info = responseList[3];
                        //inductionRequest.additional_data.project = responseList[3].project;
                        cb(null, inductionRequest);
                    });
                }else{
                    cb('Failed to Load User information');
                }
            })

    }

    private updateExistingRequest(induction_request, cb){
        console.log('Save it', induction_request);
        this.userService.updateInductionRequest(induction_request.id, induction_request).subscribe(data => {
            cb();
            if (data.success) {
                console.log('Saved...!!');
                this.fetchMyInductionRequests();
            } else {
                const message = data.message || 'Failed to store data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    openInductionForm(row, target = 'html', cb = (data) => {}, openWindow = true){
        let params: any = {
            nowMs: dayjs().valueOf(),
        };
        if (this.authUser$.timezone) {
            params.tz = this.authUser$.timezone;
        }
        if (!openWindow) {
            params.embed = 'frame';
        }
        this.induction_requests_loading = true;
        this.userService.downloadInductionForm(row.id, row.createdAt, params, target, (data) => {
            this.induction_requests_loading = false;
            cb(data);
        }, openWindow);
    }

    editSiteSpecifics(event) {
        event.closeFn();
        this.router.navigate(this.editURL);
    }
    
    editProfile(event) {
        event.closeFn();
        this.router.navigateByUrl("/on-board/personal-details");
    }

    openModalInfo() {
        let title =`Your induction has been submitted and is currently pending review by a site administrator. You’ll be contacted if anything further is needed before your site attendance. You do not need to take any further action at this time.`;
        this.confirmationModalRef.openConfirmationPopup({
            title: title,
            confirmLabel: 'Ok',
            hasCancel: false,
            hideHeader: true,
        });
    }
}

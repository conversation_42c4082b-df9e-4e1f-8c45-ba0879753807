<div class="container detail-page-header-margin">
    <h4 class="text-center">User Dashboard</h4>
  <form role="form" #startNewForm="ngForm" novalidate>
      <div class="row">
          <h5 class="col-md-3">Start New Induction</h5>
      </div>
      <div class="row">
          <div class="col-md-3 form-group">
              <input class="form-control input-md" required
                     #refNo="ngModel" (change)="1+1"
                     ng-value="query.search" [(ngModel)]="query.search"
                     name="ref_no" type="text" placeholder="Enter new project ref. No." />
          </div>
          <!--<div class="col-md-1 m-0 col-form-label d-none"><i>OR</i></div>
          <div class="col-md-2 d-none">
            <qr-code-scanner (valueChanged)="onQRValueChange($event)" [enabled]="true"></qr-code-scanner>
          </div>-->
          <div class="col-md-1 form-group">
              <button type="submit" [routerLink]="['/site-user/search-induction']"
                      [queryParams]="query" class="btn btn-primary"
                      [disabled]="!(startNewForm.valid || query.search)">Search</button>
          </div>
      </div>
  </form>
    <div class="row">
        <div class="col-sm-12">
            <h5>Projects</h5>
        </div>
        <div class="col-sm-12 table-responsive-sm">
            <ngx-skeleton-loader *ngIf="induction_requests_loading" count="8" [theme]="{ 'border-radius': '0', height: '50px', width: '100%' }"></ngx-skeleton-loader>
            <ngx-datatable *ngIf="!induction_requests_loading" class="bootstrap table table-hover table-sm"
                [rows]="induction_requests"
                [columnMode]="'force'"
                [limit]="30"
                [footerHeight]="40"
                [rowHeight]="'auto'">
                <ngx-datatable-column headerClass="py-2 font-weight-bold" [width]="50" prop="record_id">
                    <ng-template let-column="column" ngx-datatable-header-template>
                        Date
                    </ng-template>
                    <ng-template let-row="row" ngx-datatable-cell-template>
                        <span *ngIf="row.createdAt"> {{ dayjs(+row.createdAt).format(dateDisplayFormat) }} </span>
                    </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column headerClass="py-2 font-weight-bold" [width]="50" prop="record_id">
                    <ng-template let-column="column" ngx-datatable-header-template>
                        Site
                    </ng-template>
                    <ng-template let-row="row" ngx-datatable-cell-template>
                        <span appTooltip>{{ row.project_ref && row.project_ref.name }}</span>
                    </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column headerClass="py-2 font-weight-bold" [width]="50" prop="record_id">
                    <ng-template let-column="column" ngx-datatable-header-template>
                        Principal Contractor
                    </ng-template>
                    <ng-template let-row="row" ngx-datatable-cell-template>
                        <span appTooltip>{{ row.project_ref && row.project_ref.contractor }}</span>
                    </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column headerClass="py-2 font-weight-bold" [width]="50" prop="record_id">
                    <ng-template let-column="column" ngx-datatable-header-template>
                        Inductor
                    </ng-template>
                    <ng-template let-row="row" ngx-datatable-cell-template>
                        <span appTooltip>{{ row.inductor_ref && row.inductor_ref.name }}</span>
                    </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column headerClass="py-2 font-weight-bold" [width]="50" prop="record_id">
                    <ng-template let-column="column" ngx-datatable-header-template>
                        Status
                    </ng-template>
                    <ng-template let-row="row" ngx-datatable-cell-template>
                        <a *ngIf="row.status_code === 3" class="btn btn-link p-0" tabindex="0"
                            (click)="openLg(row, content, ['/site-user/add-request', row.project_ref ? row.project_ref.id : '-', row.id])">
                            {{ row.status_message }}
                        </a>
                        <span *ngIf="!([1, 3, 6].includes(row.status_code))"
                            [class]="'btn btn-sm btn-outline-'+STATUS_COLORS[row.status_code]+' bg-'+STATUS_COLORS[row.status_code]+' text-white'">
                            {{ row.status_message }}
                        </span>
                        <span role="button" (click)="openModalInfo()" *ngIf="([1, 6].includes(row.status_code))">{{ row.status_message }}</span>
                    </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column headerClass="py-2 font-weight-bold" [sortable]="false" [width]="50" prop="record_id">
                    <ng-template let-column="column" ngx-datatable-header-template>
                    </ng-template>
                    <ng-template let-row="row" ngx-datatable-cell-template>
                        <a *ngIf="[1, 6].includes(row.status_code)" class="btn btn-sm btn-outline-brandeis-blue"
                            href="javascript:void(0)" (click)="openInductionForm(row)">
                            <i class="fa fa-search"></i> Preview
                        </a>
                    </ng-template>
                </ngx-datatable-column>
            </ngx-datatable>
            <block-loader [show]="(request_resubmitting)" [showBackdrop]="true" alwaysInCenter="true"></block-loader>
        </div>
    </div>
</div>

<i-modal #content title="Review Form" size="xl" [showCancel]="false" (onClickRightPB)="resubmitInductionRequest($event)" rightPrimaryBtnTxt="Resubmit Induction" [windowClass]="'xl-modal'"
    rightSecondaryBtnTxt="Edit Site Specifics" (onClickRightSB)="editSiteSpecifics($event)" leftPrimaryBtnTxt="Edit Profile" (onClickLeftPB)="editProfile($event)" leftPrimaryBtnClass="text-brandeis-blue fw-500">
        <iframe #previewFrame id="previewFrame" class="border-0" style="width: 98%;height: 100%;position: absolute;"></iframe>
        <block-loader [show]="(iframe_content_loading)"></block-loader>
</i-modal>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

<style>
    .border-top { border-top: 1px solid var(--light-silver) !important; }
    .modal-container { max-height: 70vh; overflow-y: auto; }
    .back-btn { color: var(--brandeis-blue); }
    .light-bold { font-weight:500;}
</style>
<ng-container *ngIf="!newCompetenciesUI; else competenciesUINew">
    <!-- Only being used from additional doc uploader -->
<form role="form" #competencyForm="ngForm" class="rowForm" [attr.yash]="'type-a'" novalidate>
    <fieldset class="list-group-item px-3" [class.rounded]="isNewDesign">
    <div class="row my-1">
        <div [ngClass]="{'col-sm-6': !twoRowDesign, 'pt-3 col-sm-12': twoRowDesign}">
            <div class="row" [ngClass]="{'flex-column': isNewDesign}">
                <div class="pr-1 col-md-4 form-group" *ngIf="!autoSelected" [ngClass]="{'col-md-12 px-3': isNewDesign}">
                    <ng-select [items]="knownCompetencies"
                               bindLabel="name"
                               bindValue="name"
                               [virtualScroll]="true" [class.v-scroll]="true"
                               class="v-scroll-dropdown-list"
                               appendTo="body"
                               placeholder="Choose Type"
                               name="title" required
                               ng-value="userDocument.name"
                               [(ngModel)]="userDocument.name"
                               #competencyName="ngModel"
                               #competencyDD
                               (change)="resetOther(competencyName.value, competencyDD)"
                               [disabled]="(saved || autoSelected) ? true: null">
                        <ng-template ng-option-tmp let-item="item">
                            <span [class]="(item.name === 'Other') ? 'font-weight-bold font-italic' : ''" [title]="item.name">{{ item.name }}</span>
                        </ng-template>
                    </ng-select>
                    <div class="alert alert-danger" [hidden]="competencyName.disabled || (competencyName.valid && competencyName.value != 'false')">Competency Name is required</div>
                </div>
                <div class="pr-1 col-md-4 form-group" [class.col-md-6]="autoSelected" [ngClass]="{'col-md-12 px-3': isNewDesign}">
                    <ng-container *ngTemplateOutlet="documentInputTemplate"></ng-container>
                </div>
                <div class="pr-1 col-md-4 form-group" [class.col-md-6]="autoSelected" [ngClass]="{'col-md-12 px-3': isNewDesign}">
                    <label class="p-0 m-0" style="position: absolute;top: -16px; font-size: .7em;">Expiry Date</label>
                    <div class="input-group">
                        <input class="form-control" placeholder="dd-mm-yyyy" readonly
                               name="selectedDob" [(ngModel)]="userDocument.expiry"
                               ngbDatepicker #d="ngbDatepicker" ng-value="userDocument.expiry"
                               [minDate]="minDate"
                        />
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary calendar" (click)="d.toggle()" type="button" [disabled]="(saved && !editMode) ? true: (userDocument.is_verified ? true: null)">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div [ngClass]="{'col-md-6': true, 'form-group': !otherTypeOfCompNotRequired()}">
                    <input class="form-control input-md" #other_toe="ngModel" [required]="!otherTypeOfCompNotRequired()" name="other_toe"
                           type="text" placeholder="Competency Name Here" [hidden]="otherTypeOfCompNotRequired()"
                           [(ngModel)]="userDocument.description"
                           [disabled]="saved ? true: null"
                           ng-value="userDocument.description">
                    <div class="alert alert-danger mb-0" [hidden]="(otherTypeOfCompNotRequired() || other_toe.valid)">Competency Name is required</div>
                </div>
            </div>

        </div>
        <div [ngClass]="{'pr-0 pl-md-4 pt-3 pt-md-0': true, 'col-sm-6': !twoRowDesign, 'pt-3 col-sm-12': twoRowDesign}" *ngIf="!isNewDesign;">
            <div class="row">
                <div class="pr-1 col-md-8" *ngIf="!childOfCompetency">
                    <div *ngFor="let selection of userDocument.user_files; let fileIndex = index; trackBy : trackById;" class="row mb-1 w-100">
                        <div class="float-left pl-3 col-md-9 pr-0">
                            <file-uploader-v2
                                    [compactDesign]="twoRowDesign"
                                    [category]="'user-doc'"
                                    [disabled]="(!userDocument.name || userDocument.name == 'false') || fileIndex > userDocument.user_files.length"
                                    [uploaderTxtPostFix]="((fileIndex == 0) ? 'Front' : ((fileIndex == 1) ? 'Back' : 'Additional Image '.concat((fileIndex-1).toString()) ))"
                                    [init]="selection"
                                    (uploadDone)="uploadDone($event, competencyForm, fileIndex)"
                                    [showThumbnail]="false" [showDragnDrop]="false"
                                    [isBlueText]="false"
                            >
                            </file-uploader-v2>
                        </div>
                        <a
                            target="_blank"
                            class="dropdown-item-text cursor-pointer ml-2 pt-2 pl-1"
                            *ngIf="selection.id" (click)="downloadImage(selection.file_url, selection.name)">
                            <i class="fa fa-download"></i>
                        </a>
                    </div>

                    <button class="btn btn-link" (click)="addExtraImages()" *ngIf="(allowAddMore && userDocument.user_files.length) || saved">
                        <i class="fa fa-plus-circle"></i> Add Additional Images
                    </button>
                </div>
                <div class="col-md-4 d-flex align-self-start">

                    <button class="btn btn-primary btn-sm mr-1" (click)="saveUserDocument()" *ngIf="!saved || editMode"
                        [disabled]="!competencyForm.valid || userDocumentsValid()">Save</button>
                    <div class="float-left" *ngIf="userDocument.user_id === auth_user_id">
                        <button *ngIf="userDocument.id && !editMode" class="btn btn-outline-primary btn-sm mr-1" (click)="enableEditDocument()">
                            <i class="fa fa-pencil-alt small-font"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-sm px-1 pb-0" (click)="deleteUserDocument()">
                            <span class="material-symbols-outlined xx-large-font"> delete</span>
                        </button>
                    </div>
                    <div *ngIf="userDocument.is_verified" class="float-left text-success ml-1" title="CITB Verified">
                        <i class="fas fa-check-square fa-2x"></i>
                    </div>

                </div>
            </div>
        </div>

        <button type="button" class="mt-md-n2 -ml-4 btn btn-link" (click)="addChildCompetency(userDocument)" *ngIf="userDocument.id && group_competencies.includes(userDocument.name)">
            <i class="fa fa-plus"></i> Add other Sentinel Competency
        </button>
        <ng-container *ngIf="userDocument.children">
            <div *ngFor="let child of userDocument.children;trackBy : trackById;" class="position-relative list-group w-100 pl-5">
                <user-doc-uploader [params]="child"
                                   [twoRowDesign]="twoRowDesign"
                                   [notifyUserOnUpload]="notifyUserOnUpload"
                                   [childOfCompetency]="userDocument"
                                   (save)="onSaveChildDocument($event)"
                                   (delete)="onDeleteChildDocument($event)"
                                   [knownCompetencies]="nonGroupKnownCompetencies"
                                   [userInfo]="userInfo"
                ></user-doc-uploader>
            </div>
        </ng-container>
        <block-loader [show]="(is_verifying)" [showBlockBackdrop]="true"></block-loader>
    </div>
    </fieldset>
</form>
</ng-container>


<ng-template #competenciesUINew>
    <div class="px-4 py-4" [attr.yash]="'type-b'" [ngClass]="{'modal-container': isScrollable && userDocument.user_files.length}">
    <form role="form" #competencyForm="ngForm" class="rowForm" novalidate>
        <div *ngIf="showBackBtn">
            <span (click)="goBack()" class="back-btn fw-500 cursor-pointer d-flex align-items-center mx-3 mb-3"> 
                <span class="material-symbols-outlined fw-600" style="font-size: 15px;"> arrow_back_ios </span> Back
            </span>
        </div>
        <div *ngIf="competencyTitle" class="font-weight-bold mb-2 mx-3">
            <ng-container> {{ competencyTitle }} </ng-container>
        </div>
        <div class="form-group row mx-0 mb-1">
            <label class="col-md-12 col-form-label form-control-label">
                Document Type<small class="required-asterisk ">*</small>
            </label>
            <div class="col-md-12">
                <ng-select
                    [virtualScroll]="true" [class.v-scroll]="true"
                    class="v-scroll-dropdown-list"
                    appendTo="body"
                    bindLabel="name"
                    bindValue="name"
                    placeholder="Choose Type"
                    name="title" 
                    ng-value="userDocument.name"
                    #competencyName="ngModel"
                    #competencyDD
                    [(ngModel)]="userDocument.name"
                    [items]="knownCompetencies"
                    [disabled]="(saved || autoSelected) ? true: null"
                    (change)="resetOther(competencyName.value, competencyDD)" required>
                    <ng-template ng-option-tmp let-item="item">
                        <span [class]="(item.name === 'Other') ? 'font-weight-bold font-italic' : ''" [title]="item.name">{{ item.name }}</span>
                    </ng-template>
                </ng-select>
                <div class="alert alert-danger" [hidden]="competencyName.disabled || (competencyName.valid && competencyName.value != 'false')">
                    Select Document Type to continue
                </div>
            </div>
        </div>
        <ng-container *ngIf="userDocument.name">
            <div class="form-group row mx-0 mb-1" *ngIf="!otherTypeOfCompNotRequired()">
                <div class="col-md-12">
                    <input class="form-control input-md" #other_toe="ngModel" [required]="!otherTypeOfCompNotRequired()" name="other_toe"
                       type="text" placeholder="Competency Name Here" [hidden]="otherTypeOfCompNotRequired()"
                       [(ngModel)]="userDocument.description"
                       ng-value="userDocument.description">
                    <div class="alert alert-danger mb-0" [hidden]="(otherTypeOfCompNotRequired() || other_toe.valid)">Competency Name is required</div>
                </div>
            </div>
            <div class="form-group row mx-0 mb-1" @slideUpDown>
                <label class="col-md-12 col-form-label form-control-label">
                    Document Number<small class="required-asterisk ">*</small>
                </label>
                <div class="col-md-12">
                    <ng-container *ngTemplateOutlet="documentInputTemplate"></ng-container>
                    <input type="hidden" name="doc-number" id="doc-number" required [ngModel]="userDocument.doc_number" />
                    <input type="hidden"
                           name="verification-done" id="verification-done"
                           [required]="verification_data.required"
                           [ngModel]="verification_data.pending ? '' : 'done'" />
                </div>
            </div>

            <ng-container *ngIf="(verification_data.required && !verification_data.pending) || (!verification_data.required && userDocument.doc_number)">
                <div @slideUpDown>
                    <div class="form-group row mx-0 mb-1">
                        <label class="col-md-12 col-form-label form-control-label">
                            Expiry Date<small class="required-asterisk ">*</small>
                        </label>
                        <div class="col-md-12">
                            <div class="input-group">
                                <input class="form-control"
                                       container="body"
                                    placeholder="dd-mm-yyyy"
                                    name="selectedDob"
                                    ngbDatepicker #d="ngbDatepicker"
                                    ng-value="userDocument.expiry"
                                    [(ngModel)]="userDocument.expiry"
                                    [minDate]="minDate" readonly/>
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary calendar" [disabled]="userDocument.is_verified && userDocument.verification_info?.dateOfExpiry" (click)="d.toggle()" type="button">
                                        <i class="fa fa-calendar"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mx-0 mt-2">
                    <div class="col-sm-12">
                        <div class="row mx-0">
                            <div class="col-md-12 px-0" *ngIf="!childOfCompetency">
                                <div class="row mx-0">
                                    <ng-container *ngFor="let selection of userDocument.user_files; let fileIndex = index; trackBy : trackById;">
                                        <div class="col-md-6 px-0 pt-1" [ngClass]="{'pr-1': fileIndex%2 == 0, 'pl-1': fileIndex%2 != 0}">
                                            <file-uploader-v2
                                                [compactDesign]="twoRowDesign"
                                                [category]="'user-doc'"
                                                [disabled]="(!userDocument.name || userDocument.name == 'false') || fileIndex > userDocument.user_files.length"
                                                [init]="selection"
                                                (uploadDone)="uploadDone($event, competencyForm, fileIndex)"
                                                (deleteFileDone)="deleteFile($event, userDocument, fileIndex)"
                                                [buttonSquareLg]="true"
                                                [showDragnDrop]="false"
                                                [showThumbnail]="true"
                                                [showDeleteBtn]="true"
                                                [showFileName]="false"
                                                [showContantAsColumn]="true"
                                                [imageFitSquare]="true"
                                                [chooseFileBtnText]="((fileIndex == 0) ? 'Front' : ((fileIndex == 1) ? 'Back' : 'Additional Image '.concat((fileIndex-1).toString()) ))">
                                            </file-uploader-v2>
                                        </div>
                                    </ng-container>
                                </div>
                                <div class="w-50 pr-1">
                                    <button class="btn btn-sm btn-outline-brandeis-blue w-100 my-2" (click)="addExtraImages()" *ngIf="allowAddMore && userDocument.user_files.length">
                                        + Add Additional Images
                                    </button>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                </div>
            </ng-container>
        </ng-container>
    </form>
    </div>
    <div *ngIf="!isAutoUpload" class="modal-footer">
        <div style="position: absolute;left: 10px;">
            <div class="horizontal-center" *ngIf="userDocument.is_verified && userDocument.verification_info && userDocument.verification_info.fetchedAt">
                <span class="material-symbols-outlined text-success mr-1">check_circle</span>
                <span class="light-bold mr-1">Date Verified: </span> <span>{{ showDateFromMs(userDocument.verification_info.fetchedAt) }}</span>
            </div>
            <div class="horizontal-center text-danger" *ngIf="!userDocument.is_verified && userDocument.doc_number && userDocument.verification_info && verification_data.required">
                <span class="material-symbols-outlined mr-1" *ngIf="userDocument.verification_info.fetchedAt">warning</span>
                <span class="light-bold">Unverified</span>
            </div>
        </div>
        <button class="btn btn-link my-0" (click)="closeModal()"> Cancel </button>
        <button type="submit" class="btn btn-brandeis-blue px-4 ml-1 my-0" 
            (click)="saveUserDocument()" [disabled]="!competencyForm.valid || userDocumentsValid()"> Save </button>
    </div>
</ng-template>

<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

<ng-template #documentInputTemplate>
    <div class="input-group">
        <input
                type="text"
                class="form-control rounded-right"
                placeholder="Doc Number"
                name="docNum"
                (change)="docNumberChanged()"
                required #docNum="ngModel"
                [(ngModel)]="userDocument.doc_number"
                pattern="^[^\s]+(\s.*)?$"
                [disabled]="((!newCompetenciesUI && saved && !editMode) || (onProfileArea && userDocument.id && userDocument.is_verified && !userDocument._is_expired)) ? true: null"
        >
        <div class="input-group-append ml-2" *ngIf="verification_data.required">
            <div class="input-group-text p-0 border-0">
                <button
                        class="btn-brandeis-blue btn horizontal-center"
                        [disabled]="!userDocument.name || !userDocument.doc_number"
                        (click)="getCitbStatus()">
                    <span class="material-symbols-outlined">sync</span> Verify
                </button>
            </div>

        </div>
    </div>
    <div class="alert alert-danger" [hidden]="docNum.disabled || !docNum.touched || (docNum.valid)">Document Number is required</div>
</ng-template>

<i-modal #verifyingIndicator
         [size]="'sm'"
         windowClass="modal_v2 verifying-modal"
         [showTitle]="false"
         [showFooter]="false"
>
    <div style="margin-bottom: 40px;" *ngIf="verification_data.state === 'processing'">
        <div class="font-weight-bold">Competency Verification</div>
        <div class="mb-3">Hold tight while we verify your competency card</div>
        <animator [srcJson]="verification_data.animationSrc"></animator>
    </div>
    <div *ngIf="verification_data.state === 'done'">
        <div class="font-weight-bold">Competency Verified</div>
        <div class="mb-3">Great news, your {{userDocument.name}} has been verified!</div>
        <animator [srcJson]="verification_data.animationSrc"></animator>
    </div>
    <div *ngIf="verification_data.state === 'expired'">
        <div class="font-weight-bold">Competency Expired</div>
        <div class="mb-3">Your {{userDocument.name}} has been expired!</div>
        <animator [srcJson]="verification_data.animationSrc"></animator>
    </div>
    <div *ngIf="verification_data.state === 'failed'">
        <div class="font-weight-bold">Competency Not Verified</div>
        <div class="mb-3">Unfortunately we weren't able to verify your competency.
            Review your details and try again or continue without verifying</div>
        <animator [srcJson]="verification_data.animationSrc"></animator>
    </div>
    <button
            *ngIf="verification_data.state !== 'processing'"
            type="button"
            class="btn modal-btn mt-3 px-3 btn-brandeis-blue float-right"
            (click)="verifyingIndicator.close()">
        {{ verification_data.state === 'done' ? 'Continue' : 'OK'}}
    </button>
</i-modal>
<div class="d-flex bg-ghost-white" id="wrapper" style="min-height: calc(100vh - 64px);">
<div *ngIf="is_mobile_nav && is_drawer_open" class="mask position-absolute" (click)="is_drawer_open = !is_drawer_open;"></div>
<div class="app-side-nav pt-0" [ngClass]="{'mobile-nav': is_mobile_nav, 'side-nav-drawer-closed': !is_drawer_open, 'side-nav-drawer-open': is_drawer_open}">
    <aside>
        <div class="sidebar left siteAdminSideBar">
            <div class="side-nav-drawer text-right" *ngIf="is_mobile_nav">
                <span (click)="is_drawer_open = !is_drawer_open;" class="fa fa-bars drawer-icon"></span>
            </div>
            <ul *ngIf="(!is_mobile_nav) || is_drawer_open" class="list-sidebar bg-default">
                <ng-template ngFor let-item [ngForOf]="formStages" let-i="index">
                    <li>
                        <a href="javascript:void(0)" class="btn last-menu d-flex flex-start align-items-center" (click)="activeRoute(i)"
                            [ngClass]="{'active-route': (i === activeStage), 'disabled': (activeStage < i) && isNotThisStage(item.name)}">
                            <div class="sidebar-icon"> <svg><use [attr.xlink:href]="item.icon"> </use></svg> </div>
                            <span class="w-100 nav-label text-left ml-2">{{ item.name }}</span>
                        </a>
                    </li>
                </ng-template>
            </ul>
        </div>
    </aside>
</div>
<div class="w-100 detail-page-header-margin" [ngStyle]="{'margin-left': is_mobile_nav ? '0' : '250px'}">
    <!-- Nav -->
    <!-- <div class="stepwizard col-md-offset-3 equal">
        <div class="stepwizard-row setup-panel pb-5">
            <ng-template ngFor let-item [ngForOf]="formStages" let-i="index">
                <div class="stepwizard-step" [title]="item.name">
                    <button type="button" [ngClass]="{
                    'btn': true,
                    'btn-outline-im-helmet active': (i === activeStage),
                    'btn-primary': isNotThisStage(item.name),
                    'pending-step': (activeStage < i) && isNotThisStage(item.name),
                    'btn-circle': true
                    }" [disabled]="isNotThisStage(item.name)">
                        <i [class]="item.icon" *ngIf="item.icon"></i>
                        <svg style="margin-bottom: 2px;" height="14px" viewBox="0 0 512 512.001" width="14px"
                             [attr.fill]="(isNotThisStage(item.name) ? '#fff' : undefined)" xmlns="http://www.w3.org/2000/svg"
                             [innerHTML]="item.svg" *ngIf="!item.icon"
                        ></svg>
                        <i class="fa fa-check step-done" aria-hidden="true" *ngIf="(activeStage > i) && isNotThisStage(item.name)"></i>
                    </button>
                    <p class="d-none d-lg-block1">{{ item.name }}</p>
                </div>
            </ng-template>
        </div>

        <div class="clearfix"></div>
    </div> -->
    <div class="p-3 h-100">
    <div class="d-flex align-items-center justify-content-center bg-white h-100 rounded-10">
        <ng-container *ngIf="project">
            <div [ngClass]="is_mobile_nav ? 'w-100 p-4' : 'p-5 w-50'">
                <div [ngClass]="{'d-none': isNotThisStage('Before you start')}">
                    <div class="w-100 mb-4 d-flex flex-column justify-content-center align-items-center">
                        <p class="h6 fw-500 mb-0 text-quick-silver"> {{ inductionTitle }}: {{ project.name | uppercase }} </p>
                        <p class="h4 fw-500 mb-0"> BEFORE YOU START </p>
                    </div>
                    <before-induction-start
                        #beforeInductionStartComponentRef
                        [user]="authUser$"
                        [locales]="this.locales"
                        [project]="additional_data.project"
                        [employment_detail]="employment_detail"
                        [userDocuments]="user_documents"
                        [cscs_config]="contractor_cscs_config"
                        [knownCompetencies]="knownCompetencies"
                        (onEmploymentDetailUpdate)="onEmploymentDetailUpdate($event)"
                        (onDocRefresh)="onUserDocsUpdated($event)"
                        (onSave)="onProfileDetailConfirmation($event)"
                    ></before-induction-start>
                </div>

                <div *ngIf="!isNotThisStage(STAGELIST.rightToWork)">
                    <ng-container *ngTemplateOutlet="rightToWorkTemplate"></ng-container>
                </div>

                <!-- <div [ngClass]="{'d-none': isNotThisStage('Travel')}"> -->
                <div *ngIf="!isNotThisStage(STAGELIST.travel)">
                    <ng-container *ngTemplateOutlet="travelFormTemplate"></ng-container>
                </div>

                <div *ngIf="!isNotThisStage(STAGELIST.safetyAssessment)">
                    <ng-container *ngTemplateOutlet="safAssessFormTemplate"></ng-container>
                </div>

                <div *ngIf="showInductionHealthAssessment && !isNotThisStage(STAGELIST.healthAssessment)">
                    <ng-container *ngTemplateOutlet="healthAssessFormTemplate"></ng-container>
                </div>

                <div *ngIf="!isNotThisStage(this.additionalSectionTitle)">
                    <ng-container *ngTemplateOutlet="additionalSectionFormTemplate"></ng-container>
                </div>

                <div *ngIf="!isNotThisStage(STAGELIST.competencies)">
                    <ng-container *ngTemplateOutlet="competenciesFormTemplate"></ng-container>
                </div>

                <div *ngIf="!isNotThisStage(STAGELIST.eMedia)">
                    <ng-container *ngTemplateOutlet="eMediaFormTemplate"></ng-container>
                </div>

                <div *ngIf="!isNotThisStage(STAGELIST.media)">
                    <ng-container *ngTemplateOutlet="projectMediaFormTemplate"></ng-container>
                </div>

                <div *ngIf="!isNotThisStage(STAGELIST.supervisorDeclaration)">
                    <ng-container *ngTemplateOutlet="supervisorDecFormTemplate"></ng-container>
                </div>

                <div *ngIf="!isNotThisStage(STAGELIST.plantMachineryOperatorDeclaration)">
                    <ng-container *ngTemplateOutlet="operatorDecFormTemplate"></ng-container>
                </div>

                <div *ngIf="!isNotThisStage(STAGELIST.declaration)">
                    <ng-container *ngTemplateOutlet="declarationFormTemplate"></ng-container>
                </div>

                <div *ngIf="!isNotThisStage(ramsPhrase)">
                    <ng-container *ngTemplateOutlet="ramsFormTemplate"></ng-container>
                </div>

                <div *ngIf="!isNotThisStage(STAGELIST.review)">
                    <ng-container *ngTemplateOutlet="reviewFormTemplate"></ng-container>
                </div>
            </div>
            <ng-template #loading>
                <block-loader [show]="(true)"></block-loader>
            </ng-template>
            <div class="col-sm-12 d-none">
                <pre>{{ induction_request | json }}</pre>
            </div>
        </ng-container>
        <div *ngIf="!is_mobile_nav" class="company-logo-wrapper">
            <img class="company-logo img-fluid float-right" [src]="companyLogoFileUrl">
        </div>
    </div>
    </div>
</div>
</div>

<ng-template #rightToWorkTemplate>
    <form id="rightToWorkForm" novalidate #rightToWorkForm="ngForm" style="min-height: 470px;" [ngClass]="{'p-5 scrollable-content': (editStep === STAGELIST.rightToWork)}">
        <div *ngIf="!editStep" class="w-100 mb-4 d-flex flex-column justify-content-center align-items-center">
            <p class="h6 fw-500 mb-0 text-quick-silver"> {{ inductionTitle }}: {{ project.name | uppercase }} </p>
            <p class="h4 fw-500 mb-0"> {{ STAGELIST.rightToWork | uppercase }} </p>
        </div>
        <div class="form-group row mb-0">
            <div class="col-sm-8 mb-0">
                <label>Do you have a valid PPAC ID?</label>
            </div>
            <div class="col-sm-4 font-weight-bold d-flex justify-content-md-end py-2">
                <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="hasPpac-yes"> Yes</label>
                    <input class="cursor-pointer" type="radio" name="hasPpac" id="hasPpac-yes" (change)="onFormChange(rightToWorkForm.valid)"
                           required [(ngModel)]="induction_request._has_rtw_doc_code"  [value]="'yes'" />
                </div>
                <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="hasPpac-no"> No </label>
                    <input class="cursor-pointer" type="radio" name="hasPpac" id="hasPpac-no" (change)="rtwAnswerChanged(rightToWorkForm.valid)"
                           required [(ngModel)]="induction_request._has_rtw_doc_code"  [value]="'no'" />
                </div>
            </div>
        </div>

        <ng-container *ngIf="induction_request._has_rtw_doc_code === 'no'">
            <!-- If user selects NO && `allowed_on` array is empty, let the user continue -->
            <input type="hidden"
                   name="ppac-verification_mandatory-no" id="ppac-verification_mandatory-no"
                   [required]="true"
                   [ngModel]="contractor_rtw_config.induction_allowed_on.length ? '' : 'done'" />
            <p class="">
                If you do not have a valid PPAC code, please <a class="text-info" target="_blank" rel="noreferrer" [href]="contractor_rtw_config.link">click here</a> to obtain one
            </p>
        </ng-container>
        <ng-container *ngIf="induction_request._has_rtw_doc_code === 'yes'">
            <input type="hidden"
                   name="ppac-verification_mandatory" id="ppac-verification_mandatory"
                   [required]="true"
                   [ngModel]="induction_request._need_rtw_check || !induction_request.rtw_check_result?.fetchedAt ? '' : 'done'" />

            <div>
                <input type="hidden"
                       name="ppac-verification-done" id="ppac-verification-done"
                       required
                       [ngModel]="induction_request._need_rtw_check ? '' : 'done'" />

                <ppac-status-card
                        [authUser$]="authUser$"
                        [parent_company]="project.parent_company"
                        [contractor_rtw_config]="contractor_rtw_config"
                        [induction_phrase]="project.custom_field.induction_phrase_singlr"
                        [ppac_data]="{doc_id: induction_request.rtw_doc_code, rtw_result: induction_request.rtw_check_result}"
                        (docIdChanged)="induction_request._need_rtw_check = true"
                        (onResult)="onRtwValidation($event)"
                ></ppac-status-card>
            </div>

        </ng-container>
    </form>

    <div *ngIf="!editStep else rtwModalFooter" class="d-flex align-self-center justify-content-between mt-5">
        <button type="button" *ngIf="activeStage" class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="movePrev()">Back</button>
        <button type="submit" *ngIf="!isNotThisStage(STAGELIST.rightToWork)" [disabled]="!rightToWorkForm.valid"
                class="btn btn-brandeis-blue ml-2 w-50" (click)="moveNext()" form="rightToWorkForm">Next</button>
    </div>
    <ng-template #rtwModalFooter>
        <div class="modal-footer-custom">
            <button type="button" class="btn btn-link btn-sm px-4 mx-1" (click)="closeModal(rightToWorkForm.valid)" #cancelBtn>Cancel</button>
            <button class="btn btn-brandeis-blue btn-sm px-4 mx-1" (click)="closeModal(rightToWorkForm.valid)" [disabled]="!rightToWorkForm.valid">Save</button>
        </div>
    </ng-template>
</ng-template>

<ng-template #travelFormTemplate>
    <form id="travelForm" novalidate #travelForm="ngForm" [ngClass]="{'p-5 scrollable-content': (editStep === STAGELIST.travel)}">
        <div *ngIf="!editStep" class="w-100 mb-4 d-flex flex-column justify-content-center align-items-center">
            <p class="h6 fw-500 mb-0 text-quick-silver"> {{ inductionTitle }}: {{ project.name | uppercase }} </p>
            <p class="h4 fw-500 mb-0"> TRAVEL DETAILS </p>
        </div>
        <div class="form-group row mb-0">
            <div class="col-sm-8 mb-0">
                <div class="pb-2">
                    <label class="mb-0">Will you be staying at the home address listed in your profile while working on this project?</label>
                    <small class="faded" *ngIf="contactDetail"><b>Address</b>: {{removeLastComma(contactDetail?.street)}}, {{contactDetail?.city}}</small>
                </div>
             </div>
            <div class="col-sm-4 font-weight-bold d-flex justify-content-md-end py-2">
                <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="postcodeIsCorrect-yes"> Yes</label>
                    <input class="cursor-pointer" type="radio" name="postcodeIsCorrect" id="postcodeIsCorrect-yes" (keyup)="onFormChange(travelForm.valid)"
                           required [(ngModel)]="induction_request._postcode_is_correct"  [value]="'yes'" />
                </div>
                <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="postcodeIsCorrect-no"> No </label>
                    <input class="cursor-pointer" type="radio" name="postcodeIsCorrect" id="postcodeIsCorrect-no" (keyup)="onFormChange(travelForm.valid)"
                           required [(ngModel)]="induction_request._postcode_is_correct"  [value]="'no'" (change)="enableTravelTimeOverride($event.target.checked)" />
                </div>
            </div>
        </div>
        <ng-container *ngIf="induction_request._postcode_is_correct === 'no'">
            <div [hidden]="postcodeInput?.type !== 'address-lookup'">
                <div class="form-group">
                    <label>Travelling from (Address)</label>
                    <input class="form-control"
                           #searchFrom autocorrect="off" autocapitalize="off" spellcheck="off" #fromAddress="ngModel" [required]="induction_request._postcode_is_correct === 'no' && postcodeInput?.type === 'address-lookup'"
                           name="fromAddress" type="text" placeholder="Search Address"
                           [(ngModel)]="from_address"
                           (change)="(sameAsFromChekBx.checked ? to_address = from_address : true)">
                    <div class="alert alert-danger" *ngIf="fromAddress.invalid && fromAddress.touched">From address is required</div>
                </div>
                <div class="form-group">
                    <label>Traveling to (Address)</label>
                    <input #searchTo autocorrect="off" autocapitalize="off" spellcheck="off" #toAddress="ngModel" [required]="induction_request._postcode_is_correct === 'no' && postcodeInput?.type === 'address-lookup'"
                           name="toAddress" type="text" [placeholder]="labelData.placeholder"
                           [(ngModel)]="to_address" [disabled]="sameAsFromChekBx.checked" class="form-control">
                    <div class="alert alert-danger" *ngIf="toAddress.invalid && toAddress.touched">To address is required</div>
                </div>
            </div>
            <div *ngIf="postcodeInput?.type === 'postcode-lookup'">
                <div class="form-group">
                    <label>
                        <span i18n="@@tfpc">Travelling from ({{ labelData.label }})</span><small class="text-danger">*</small>
                    </label>
                    <input class="form-control"
                           #fromPostcode="ngModel" required
                           (focusout)="travel_time_override.from_postcode.trim().length > 3 && validatePostcode(travel_time_override.from_postcode, fromPostcode)"
                           name="fromPostcode" type="text" [placeholder]="labelData.placeholder" (change)="(sameAsFromChekBx.checked ? travel_time_override.to_postcode = travel_time_override.from_postcode : true)"
                           [(ngModel)]="travel_time_override.from_postcode"
                           ng-value="travel_time_override.from_postcode" (keyup)="onFormChange(travelForm.valid)">
                    <div class="alert alert-danger" [hidden]="(fromPostcode.valid)">From {{labelData.error}} is invalid</div>
                </div>

                <div class="form-group">
                    <label>
                        <span i18n="@@ttpc">Traveling to ({{labelData.label}})</span> <small class="text-danger">*</small>
                    </label>
                    <input class="form-control"
                           #toPostcode="ngModel" required
                           (focusout)="travel_time_override.to_postcode.trim().length > 3 && validatePostcode(travel_time_override.to_postcode, toPostcode)"
                           name="toPostcode" type="text" [readOnly]="sameAsFromChekBx.checked"
                           [placeholder]="labelData.placeholder"
                           [(ngModel)]="travel_time_override.to_postcode"
                           ng-value="travel_time_override.to_postcode" (keyup)="onFormChange(travelForm.valid)">
                    <div class="alert alert-danger mb-0" [hidden]="(toPostcode.valid)">To {{labelData.error}} is invalid</div>
                </div>
            </div>
            <div class="col-sm-12 px-0 mt-2">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input cursor-pointer" (click)="preFillTravelingToPostcode($event)" #sameAsFromChekBx
                           id="toggleDefaultAddress">
                    <label class="custom-control-label small cursor-pointer" for="toggleDefaultAddress"><span i18n="@@satfpc">Same as 'Travelling from' {{labelData.label}}</span></label>
                </div>
            </div>
        </ng-container>
        <div class="form-group">
            <label>Travel time to work (minutes) <small class="text-danger">*</small></label>
            <input type="number" class="form-control" #travelToWork="ngModel" name="travelToWork" min="0" oninput="validity.valid || (value='');"
                [(ngModel)]="travelTimeToWork" (change)="storeTravelInfo()" (keyup)="onFormChange(travelForm.valid)"
                placeholder="Enter travel time to work ( In Minuts )" required/>
            <div class="alert alert-danger" [hidden]="(travelToWork.valid)">Travel time to work is required</div>
        </div>
        <div class="form-group">
            <label>Travel time to home from work (minutes) <small class="text-danger">*</small></label>
            <input type="number" class="form-control" #travelToHome="ngModel" name="travelToHome" min="0" oninput="validity.valid || (value='');"
                [(ngModel)]="travelTimeToHome" (change)="storeTravelInfo()" (keyup)="onFormChange(travelForm.valid)"
                placeholder="Enter travel time to Home ( In Minuts )" required/>
            <div class="alert alert-danger" [hidden]="(travelToHome.valid)">Travel Time to Home is required</div>
        </div>
        <div class="form-group">
            <label>Method of travel <small class="text-danger">*</small></label>
            <ng-select [items]="available_travel_methods" class="dropdown-list" [appendTo]="editStep ? 'body' : null"
                       placeholder="Select Travel Method"
                       name="travelMethod" #travelMethod="ngModel"
                       [(ngModel)]="induction_request.travel_method" required>
            </ng-select>
            <div class="alert alert-danger" [hidden]="(travelMethod.valid)">Travel Method is required</div>
        </div>
        <div class="form-group" *ngIf="isVehicleRegRequired(induction_request.travel_method)">
            <label><span i18n="@@vrn">Vehicle Registration Number</span> <small class="text-danger">*</small></label>
            <input type="text" class="form-control" #vehicleRegNum="ngModel" [(ngModel)]="induction_request.vehicle_reg_number" name="vehicleRegNum"
                   (change)="fetchVehicleInfo()" (keyup)="onFormChange(travelForm.valid)"
                   placeholder="Enter Your Vehicle Registration Number" i18n-placeholder="@@eyvrn" required />
            <div class="alert alert-danger" [hidden]="(vehicleRegNum.valid)">Vehicle Registration # is required</div>
        </div>
        <small class="form-text text-muted" *ngIf="induction_request?.travel_time?.vehicle_info?.errorMessage">{{ induction_request.travel_time.vehicle_info.errorMessage }}</small>
        <div class="form-group row" *ngIf="induction_request?.travel_time?.vehicle_info?.make">
            <div class="col-sm-4 mb-2">
                <label class="mb-1">Make</label>
                <div class="font-italic">{{induction_request.travel_time.vehicle_info?.make}}</div>
            </div>
            <div class="col-sm-4 mb-2">
                <label class="mb-1">Fuel Type</label>
                <div class="font-italic">{{induction_request.travel_time.vehicle_info?.fuelType}}</div>
            </div>
            <div class="col-sm-4 mb-2">
                <label class="mb-1">CO2 Emissions</label>
                <div class="font-italic">{{induction_request.travel_time.vehicle_info?.co2Emissions}} g/km</div>
            </div>
        </div>
    </form>
    <div *ngIf="!editStep else modalFooter" class="d-flex align-self-center justify-content-between mt-5">
        <button type="button" *ngIf="activeStage" class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="movePrev()">Back</button>
        <button type="submit" *ngIf="!isNotThisStage(STAGELIST.travel)" [disabled]="!travelForm.valid"
            class="btn btn-brandeis-blue ml-2 w-50" (click)="moveNext()" form="travelForm">Next</button>
    </div>
    <ng-template #modalFooter>
        <div class="modal-footer-custom">
            <button type="button" class="btn btn-link btn-sm px-4 mx-1" (click)="closeModal(travelForm.valid)" #cancelBtn>Cancel</button>
            <button class="btn btn-brandeis-blue btn-sm px-4 mx-1" (click)="closeModal(travelForm.valid)" [disabled]="!travelForm.valid">Save</button>
        </div>
    </ng-template>
</ng-template>

<ng-template #safAssessFormTemplate>
    <form id="safAssessForm" novalidate #safAssessForm="ngForm" [ngClass]="{'p-5 scrollable-content': (editStep === STAGELIST.safetyAssessment)}">
        <div *ngIf="!editStep" class="w-100 mb-4 d-flex flex-column justify-content-center align-items-center">
            <p class="h6 fw-500 mb-0 text-quick-silver"> {{ inductionTitle }}: {{ project.name | uppercase }} </p>
            <p class="h4 fw-500 mb-0"> SAFETY ASSESSMENT </p>
        </div>
        <div class="form-group row mb-0">
            <div class="col-sm-8 mb-0">
                <label>I confirm, I am fit and able to undertake the work activities my role requires?</label>
            </div>
            <div class="col-sm-4 font-weight-bold d-flex justify-content-md-end">
                <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="fit_undertake_role-yes"> Yes</label>
                    <input class="cursor-pointer" type="radio" name="fit_undertake_role" id="fit_undertake_role-yes"
                        [value]="true" [(ngModel)]="induction_request.fit_undertake_role" required/>
                </div>
                <!-- <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="fit_undertake_role-no"> No </label>
                    <input class="cursor-pointer" type="radio" name="fit_undertake_role" id="fit_undertake_role-no"
                        [value]="false" [(ngModel)]="induction_request.fit_undertake_role" required/>
                </div> -->
            </div>
        </div>
        <hr class="my-3 mx-0 hr-light-silver" />
        <div class="form-group row mb-0">
            <div class="col-sm-8 mb-0">
                <label>I confirm, I am fit to work safely</label>
            </div>
            <div class="col-sm-4 font-weight-bold d-flex justify-content-md-end">
                <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="fit_to_work-yes"> Yes</label>
                    <input class="cursor-pointer" type="radio" name="fit_to_work" id="fit_to_work-yes"
                        [value]="true" [(ngModel)]="induction_request.fit_to_work" required/>
                </div>
                <!-- <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="fit_to_work-no"> No </label>
                    <input class="cursor-pointer" type="radio" name="fit_to_work" id="fit_to_work-no"
                        [value]="false" [(ngModel)]="induction_request.fit_to_work" required/>
                </div>  -->
            </div>
        </div>
        <hr class="my-3 mx-0 hr-light-silver" *ngIf="(working_hr_agreement.policy_name)"/>
        <div class="form-group row mb-0" *ngIf="(working_hr_agreement.policy_name)">
            <div class="col-sm-8 mb-0">
                <label>I confirm, I will comply with the <span class="clickable-link" (click)="working_hr_agreement.is_text ? toggleImModal(hr_policy_modal): openPolicyPdfViewer(working_hr_agreement.policy_ref[0])">Working Hours Agreement</span> on site.</label>
            </div>
            <div class="col-sm-4 font-weight-bold d-flex justify-content-md-end">
                <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="comply_hour_agreement-yes"> Yes</label>
                    <input class="cursor-pointer" type="radio" name="comply_hour_agreement" id="comply_hour_agreement-yes"
                        [value]="true" [(ngModel)]="induction_request.comply_hour_agreement" required/>
                </div>
                <!-- <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="comply_hour_agreement-no"> No </label>
                    <input class="cursor-pointer" type="radio" name="comply_hour_agreement" id="comply_hour_agreement-no"
                        [value]="false" [(ngModel)]="induction_request.comply_hour_agreement" required/>
                </div>  -->
            </div>
            <im-modal [isShowing]="false"
                      [title]="working_hr_agreement.policy_name"
                      [innerHTML]="working_hr_agreement.policy"
                      #hr_policy_modal></im-modal>
        </div>

        <hr *ngIf="(d_and_a_policy.policy_name)" class="my-3 mx-0 hr-light-silver" />
        <div *ngIf="(d_and_a_policy.policy_name)" class="form-group row mb-0">
            <div class="col-sm-8 mb-0">
                <label>Do you accept the Drugs and Alcohol Policy, specified <span class="clickable-link" (click)="d_and_a_policy.is_text ? toggleImModal(d_and_a_policy_modal): openPolicyPdfViewer(d_and_a_policy.policy_ref[0])">here</span>?</label>
            </div>
            <div class="col-sm-4 font-weight-bold d-flex justify-content-md-end">
                <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="accept_drug_alcohol_pol-yes"> Yes</label>
                    <input class="cursor-pointer" type="radio" name="accept_drug_alcohol_pol" id="accept_drug_alcohol_pol-yes"
                        [value]="true" [(ngModel)]="induction_request.accept_drug_alcohol_pol" required/>
                </div>
                <!-- <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="accept_drug_alcohol_pol-no"> No </label>
                    <input class="cursor-pointer" type="radio" name="accept_drug_alcohol_pol" id="accept_drug_alcohol_pol-no"
                        [value]="false" [(ngModel)]="induction_request.accept_drug_alcohol_pol" required/>
                </div>  -->
            </div>
            <im-modal [isShowing]="false"
                      [title]="d_and_a_policy.policy_name"
                      [innerHTML]="d_and_a_policy.policy"
                      #d_and_a_policy_modal></im-modal>
        </div>

        <ng-template ngFor let-item [ngForOf]="(project && project.further_policies || [])" let-i="index">
            <hr *ngIf="!item.is_default" class="my-3 mx-0 hr-light-silver" />
            <div *ngIf="!item.is_default" class="form-group row mb-0">
                <div class="col-sm-8 mb-0">
                    <label>Do you agree with the {{item.policy_name}} policy, specified <span class="clickable-link" (click)="item.is_text ? toggleImModal(furtherPolicy): openPolicyPdfViewer(item.policy_ref[0])">here</span>?</label>
                </div>
                <div class="col-sm-4 font-weight-bold d-flex justify-content-md-end">
                    <div class="ml-3 radio-button-custom">
                        <label class="mr-2 cursor-pointer" for="{{'fp_txt_' + i}}-yes"> Yes</label>
                        <input class="cursor-pointer" type="radio" name="{{'fp_txt_' + i}}" id="{{'fp_txt_' + i}}-yes"
                            [checked]="induction_request.accepted_further_policies.includes(i)"
                            [ngModel]="induction_request.accepted_further_policies.includes(i) ? 'Y' : null"
                            [value]="'Y'"
                            (change)="changeModel(null, induction_request.accepted_further_policies, i, true)" required />
                    </div>
                </div>
                <im-modal [isShowing]="false"
                          [title]="item.policy_name"
                          [innerHTML]="item.policy"
                          #furtherPolicy></im-modal>
            </div>
        </ng-template>

    </form>
    <div *ngIf="!editStep else modalFooter" class="d-flex align-self-center justify-content-between mt-5">
        <button type="button" *ngIf="activeStage" class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="movePrev()">Back</button>
        <button type="submit" *ngIf="!isNotThisStage(STAGELIST.safetyAssessment)" [disabled]="!safAssessForm.valid"
            class="btn btn-brandeis-blue ml-2 w-50" (click)="moveNext()" form="safAssessForm">Next</button>
    </div>
    <ng-template #modalFooter>
        <div class="modal-footer-custom">
            <button type="button" class="btn btn-link btn-sm px-4 mx-1" (click)="closeModal(safAssessForm.valid)" #cancelBtn>Cancel</button>
            <button class="btn btn-brandeis-blue btn-sm px-4 mx-1" (click)="closeModal(safAssessForm.valid)" [disabled]="!safAssessForm.valid">Save</button>
        </div>
    </ng-template>
</ng-template>

<ng-template #healthAssessFormTemplate>
    <form id="healthAssessForm" novalidate #healthAssessForm="ngForm" [ngClass]="{'p-5 scrollable-content': (editStep === STAGELIST.healthAssessment)}">
        <div *ngIf="!editStep" class="w-100 mb-4 d-flex flex-column justify-content-center align-items-center">
            <p class="h6 fw-500 mb-0 text-quick-silver"> {{ inductionTitle }}: {{ project.name | uppercase }} </p>
            <p class="h4 fw-500 mb-0"> HEALTH ASSESSMENT </p>
        </div>
        <div class="form-group row mb-0">
            <div class="col-sm-8 mb-0">
                <label>Do you have any reportable medical conditions?</label>
            </div>
            <div class="col-sm-4 font-weight-bold d-flex justify-content-md-end">
                <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="rmc-yes"> Yes</label>
                    <input class="cursor-pointer" type="radio" name="rmc" id="rmc-yes"
                        [value]="'yes'" [(ngModel)]="induction_request.reportable_medical_conditions" required/>
                </div>
                <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="rmc-no"> No </label>
                    <input class="cursor-pointer" type="radio" name="rmc" id="rmc-no"
                        [value]="'no'" [(ngModel)]="induction_request.reportable_medical_conditions" required/>
                </div>
            </div>
        </div>
        <div class="form-group" *ngIf="(!isDisabled('reportable_medical_conditions'))">
            <label>Please specify <small class="text-danger">*</small></label>
            <textarea name="rmc_detail" style="min-height: 40px;"
                [required]="!isDisabled('reportable_medical_conditions')"
                class="form-control" #rmc_detail="ngModel" [(ngModel)]="induction_request.rmc_detail"
                placeholder="Specify medical condition details here"
                [disabled]="isDisabled('reportable_medical_conditions')"
                ng-value="induction_request.rmc_detail"></textarea>
            <div class="alert alert-danger m-0" [hidden]="(isDisabled('reportable_medical_conditions') || rmc_detail.valid)">Medical Condition details is required</div>
        </div>
        <hr class="my-3 mx-0 hr-light-silver" />
        <div class="form-group row mb-0">
            <div class="col-sm-8 mb-0">
                <label>Are you taking any regular medication, prescribed or otherwise?</label>
            </div>
            <div class="col-sm-4 font-weight-bold d-flex justify-content-md-end">
                <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="olm-yes"> Yes</label>
                    <input class="cursor-pointer" type="radio" name="on_long_medication" id="olm-yes" (change)="onLongMedicationChanged()"
                        [value]="'yes'" [(ngModel)]="induction_request.on_long_medication" required/>
                </div>
                <div class="ml-3 radio-button-custom">
                    <label class="mr-2 cursor-pointer" for="olm-no"> No </label>
                    <input class="cursor-pointer" type="radio" name="on_long_medication" id="olm-no" (change)="onLongMedicationChanged()"
                        [value]="'no'" [(ngModel)]="induction_request.on_long_medication" required/>
                </div>
            </div>
            <div class="col-sm-12" *ngIf="induction_request?.on_long_medication === 'yes'">
                <ng-template ngFor let-item [ngForOf]="(induction_request.medications)" let-i="index">
                    <div class="form-group row item-medication mx-0 mb-3">
                        <div class="col-md-11 px-0">
                            <div class="item rounded">
                                <div class="row mx-0 h-100">
                                    <div class="col-md-11 px-0 d-flex align-items-center">
                                        <span class="h6 fw-500 mb-0">{{ item.medication_name }}</span>
                                    </div>
                                    <div class="col-md-1 px-0 d-flex align-items-center justify-content-end">
                                        <!-- <i (click)="openMedicationDetailModal(item, i, true)" class="fa fa-edit fw-500 cursor-pointer h5 mb-0" aria-hidden="true"></i> -->
                                        <img (click)="openMedicationDetailModal(item, i, true)" class="cursor-pointer pr-3 ml-1 mb-1" [src]="editIconBlack" height="20">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 trash-delete">
                            <i (click)="removeMedicationRow(null, i)" class="fa fa-trash text-danger cursor-pointer h5 mb-0"></i>
                        </div>
                    </div>
                </ng-template>
                <div class="alert alert-danger" [hidden]="isDisabled('on_long_medication') || !medicationValid()">Medication details are required</div>
            </div>
            <div class="col-sm-12" *ngIf="induction_request.on_long_medication == 'yes'">
                <button type="button" class="btn btn-link" (click)="openMedicationDetailModal()">+ Add Medication</button>
            </div>
        </div>

        <ng-container *ngIf="!isDisabled('on_long_medication')">
            <hr class="mb-4 mt-3 mx-0 hr-light-silver" />
            <div class="form-group row mb-0">
                <div class="col-sm-7 mb-0">
                    <label>Do you suffer from any side effects?</label>
                </div>
                <div class="col-sm-5 font-weight-bold d-flex justify-content-md-end">
                    <div class="ml-3 radio-button-custom">
                        <label class="mr-2 cursor-pointer" for="ase-yes"> Yes</label>
                        <input class="cursor-pointer" type="radio" name="any_side_effects" id="ase-yes"
                            [value]="'yes'" [(ngModel)]="induction_request.any_side_effects" required/>
                    </div>
                    <div class="ml-3 radio-button-custom">
                        <label class="mr-2 cursor-pointer" for="ase-no"> No </label>
                        <input class="cursor-pointer" type="radio" name="any_side_effects" id="ase-no"
                            [value]="'no'" [(ngModel)]="induction_request.any_side_effects" required/>
                    </div>
                    <div class="ml-3 radio-button-custom">
                        <label class="mr-2 cursor-pointer" for="ase-na"> N/A </label>
                        <input class="cursor-pointer" type="radio" name="any_side_effects" id="ase-na"
                            [value]="'n/a'" [(ngModel)]="induction_request.any_side_effects" required/>
                    </div>
                </div>
            </div>
            <div class="form-group" *ngIf="(!isDisabled('any_side_effects'))">
                <label>Please specify <small class="text-danger">*</small></label>
                <input class="form-control input-md"
                    [disabled]="isDisabled('any_side_effects')"
                    #ase_detail="ngModel"
                    [required]="!isDisabled('any_side_effects')"
                    name="ase_detail" type="text"
                    placeholder="migraines, stomach pain etc"
                    [(ngModel)]="induction_request.any_side_effect_detail"
                    ng-value="induction_request.any_side_effect_detail" />
                <div class="alert alert-danger" [hidden]="(isDisabled('any_side_effects') || ase_detail.valid)">Side effects details is required</div>
            </div>
        </ng-container>
    </form>
    <div *ngIf="!editStep else modalFooter" class="d-flex align-self-center justify-content-between mt-5">
        <button type="button" *ngIf="activeStage" class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="movePrev()">Back</button>
        <button type="submit" *ngIf="!isNotThisStage(STAGELIST.healthAssessment)" [disabled]="!healthAssessForm.valid || medicationValid()"
            class="btn btn-brandeis-blue ml-2 w-50" (click)="moveNext()" form="healthAssessForm">Next</button>
    </div>
    <ng-template #modalFooter>
        <div class="modal-footer-custom">
            <button type="button" class="btn btn-link btn-sm px-4 mx-1" (click)="closeModal(healthAssessForm.valid)" #cancelBtn>Cancel</button>
            <button class="btn btn-brandeis-blue btn-sm px-4 mx-1" (click)="closeModal(healthAssessForm.valid)" [disabled]="!healthAssessForm.valid">Save</button>
        </div>
    </ng-template>
</ng-template>

<ng-template #additionalSectionFormTemplate>
    <form id="additionalSectionForm" novalidate #additionalSectionForm="ngForm" [ngClass]="{'p-5 scrollable-content': (editStep === this.additionalSectionTitle)}">
        <div *ngIf="!editStep" class="w-100 mb-4 d-flex flex-column justify-content-center align-items-center">
            <p class="h6 fw-500 mb-0 text-quick-silver"> {{ inductionTitle }}: {{ project.name | uppercase }} </p>
            <p class="h4 fw-500 mb-0"> {{ this.additionalSectionTitle }} </p>
        </div>
        <ng-template ngFor let-item [ngForOf]="additionalInductionQuestions?.induction_questions || []" let-i="index">
            <div class="form-group" [ngClass]="{'row mb-0': additionalInductionQuestions.induction_questions[i].ans_field_type == 'radio_yn'}">
                <label [ngClass]="{'col-sm-8 mb-0': additionalInductionQuestions.induction_questions[i].ans_field_type == 'radio_yn'}">
                    {{additionalInductionQuestions.induction_questions[i].question}}
                    <small class="required-asterisk" *ngIf="additionalInductionQuestions.induction_questions[i].is_mandatory">*</small>
                </label>
                <input *ngIf="additionalInductionQuestions.induction_questions[i].ans_field_type == 'textbox'" type="text" class="form-control"
                       [name]="'textbox_' + i" [id]="'textbox_' + i"
                       [(ngModel)]="additionalInductionQuestions.induction_questions[i].answer"
                       [required]="additionalInductionQuestions.induction_questions[i].is_mandatory"/>
                <textarea *ngIf="additionalInductionQuestions.induction_questions[i].ans_field_type == 'textarea'" type="text" rows="5" class="form-control"
                       [name]="'textarea_' + i" [id]="'textarea_' + i"
                       [(ngModel)]="additionalInductionQuestions.induction_questions[i].answer"
                       [required]="additionalInductionQuestions.induction_questions[i].is_mandatory"></textarea>
                <div class="input-group p-0 col-12" *ngIf="additionalInductionQuestions.induction_questions[i].ans_field_type == 'date'">
                    <input class="form-control" placeholder="DD/MM/YYYY" readonly
                           [name]="'date_' + i" [id]="'date_' + i" ngbDatepicker #d="ngbDatepicker"
                           [(ngModel)]="additionalInductionQuestions.induction_questions[i].answer"
                           [required]="additionalInductionQuestions.induction_questions[i].is_mandatory"/>
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary calendar"
                                (click)="d.toggle()"
                                type="button">
                            <i class="fa fa-calendar"></i>
                        </button>
                    </div>
                </div>
                <input *ngIf="additionalInductionQuestions.induction_questions[i].ans_field_type == 'number'" type="number" class="form-control"
                       [name]="'number_' + i" [id]="'number_' + i" min="0" oninput="validity.valid || (value='');"
                       [(ngModel)]="additionalInductionQuestions.induction_questions[i].answer"
                       [required]="additionalInductionQuestions.induction_questions[i].is_mandatory"/>
                <ng-select *ngIf="additionalInductionQuestions.induction_questions[i].ans_field_type == 'dropdown'"
                        [name]="'dropdown_' + i" [id]="'dropdown_' + i" class="w-100"
                        placeholder="No option selected" [searchable]="false" [clearable]="false"
                        [ngModel]="additionalInductionQuestions.induction_questions[i].answer"
                        [required]="additionalInductionQuestions.induction_questions[i].is_mandatory"
                        (ngModelChange)="additionalInductionQuestions.induction_questions[i].answer = $event">
                    <ng-option [value]="" disabled selected>Choose option</ng-option>
                    <ng-option *ngFor="let option of additionalInductionQuestions.induction_questions[i].options" [value]="option.label">{{ option.label }}</ng-option>
                </ng-select>
                <div class="col-sm-4 font-weight-bold d-flex justify-content-md-end" *ngIf="additionalInductionQuestions.induction_questions[i].ans_field_type == 'radio_yn'">
                    <ng-template ngFor let-item [ngForOf]="additionalInductionQuestions.induction_questions[i].options" let-k="index">
                        <div class="ml-3 radio-button-custom">
                            <label class="mr-2 cursor-pointer" [for]="'option_'+i+k">{{item.label}}</label>
                            <input class="cursor-pointer" type="radio" [name]="'radio_yn'+i" [id]="'option_'+i+k"
                                   [required]="additionalInductionQuestions.induction_questions[i].is_mandatory"
                                   [(ngModel)]="additionalInductionQuestions.induction_questions[i].answer"
                                   [value]="item.label" (change)="onAdditionalAnswerChange(i, item.label)">
                        </div>
                    </ng-template>
                </div>
            </div>

            <ng-template *ngIf="additionalInductionQuestions.induction_questions[i].ans_field_type == 'radio_yn' &&
                additionalInductionQuestions.induction_questions[i].answer &&
                additionalInductionQuestions.induction_questions[i].sub_questions &&
                additionalInductionQuestions.induction_questions[i].sub_questions.length"
                ngFor let-item [ngForOf]="additionalInductionQuestions.induction_questions[i].sub_questions" let-j="index">
                <div class="form-group" *ngIf="additionalInductionQuestions.induction_questions[i].sub_questions[j].que_condition == 'If '+additionalInductionQuestions.induction_questions[i].answer">
                    <label>
                        {{additionalInductionQuestions.induction_questions[i].sub_questions[j].question}}
                        <small class="required-asterisk" *ngIf="additionalInductionQuestions.induction_questions[i].sub_questions[j].is_mandatory">*</small>
                    </label>
                    <div class="col-md-12 p-0 d-flex justify-content-md-end">
                        <input *ngIf="additionalInductionQuestions.induction_questions[i].sub_questions[j].ans_field_type == 'textbox'" type="text" class="form-control"
                               [name]="'textbox_'+i+j" [id]="'textbox_'+i+j"
                               [(ngModel)]="additionalInductionQuestions.induction_questions[i].sub_questions[j].answer"
                               [required]="additionalInductionQuestions.induction_questions[i].sub_questions[j].is_mandatory"
                        />

                        <textarea *ngIf="additionalInductionQuestions.induction_questions[i].sub_questions[j].ans_field_type == 'textarea'" type="text" rows="5"class="form-control"
                                  [name]="'textarea_'+i+j" [id]="'textarea_'+i+j"
                                  [(ngModel)]="additionalInductionQuestions.induction_questions[i].sub_questions[j].answer"
                                  [required]="additionalInductionQuestions.induction_questions[i].sub_questions[j].is_mandatory"
                        ></textarea>

                        <div class="input-group p-0 col-12" *ngIf="additionalInductionQuestions.induction_questions[i].sub_questions[j].ans_field_type == 'date'">
                            <input class="form-control" placeholder="DD/MM/YYYY" readonly
                                   [name]="'date_'+i+j" [id]="'date_'+i+j" ngbDatepicker #d="ngbDatepicker"
                                   [(ngModel)]="additionalInductionQuestions.induction_questions[i].sub_questions[j].answer"
                                   [required]="additionalInductionQuestions.induction_questions[i].sub_questions[j].is_mandatory"/>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary calendar"
                                        (click)="d.toggle()"
                                        type="button">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </div>
                        </div>

                        <input *ngIf="additionalInductionQuestions.induction_questions[i].sub_questions[j].ans_field_type == 'number'" type="number" class="form-control"
                               [name]="'number_'+i+j" [id]="'number_'+i+j" min="0" oninput="validity.valid || (value='');"
                               [(ngModel)]="additionalInductionQuestions.induction_questions[i].sub_questions[j].answer"
                               [required]="additionalInductionQuestions.induction_questions[i].sub_questions[j].is_mandatory"
                        />

                        <ng-select *ngIf="additionalInductionQuestions.induction_questions[i].sub_questions[j].ans_field_type == 'dropdown'"
                                [name]="'dropdown_'+i+j" [id]="'dropdown_'+i+j" class="w-100"
                                placeholder="No option selected" [searchable]="false" [clearable]="false"
                                [ngModel]="additionalInductionQuestions.induction_questions[i].sub_questions[j].answer"
                                [required]="additionalInductionQuestions.induction_questions[i].sub_questions[j].is_mandatory"
                                (ngModelChange)="additionalInductionQuestions.induction_questions[i].sub_questions[j].answer = $event">
                            <ng-option [value]="" disabled selected>Choose option</ng-option>
                            <ng-option *ngFor="let option of additionalInductionQuestions.induction_questions[i].sub_questions[j].options" [value]="option.label">{{ option.label }}</ng-option>
                        </ng-select>
                    </div>
                </div>
            </ng-template>
        </ng-template>
    </form>
    <div *ngIf="!editStep else modalFooter" class="d-flex align-self-center justify-content-between mt-5">
        <button type="button" *ngIf="activeStage" class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="movePrev()">Back</button>
        <button type="submit" *ngIf="!isNotThisStage(this.additionalSectionTitle)" [disabled]="!additionalSectionForm.valid"
            class="btn btn-brandeis-blue ml-2 w-50" (click)="moveNext()" form="additionalSectionForm">Next</button>
    </div>
    <ng-template #modalFooter>
        <div class="modal-footer-custom">
            <button type="button" class="btn btn-link btn-sm px-4 mx-1" (click)="closeModal(additionalSectionForm.valid)" #cancelBtn>Cancel</button>
            <button class="btn btn-brandeis-blue btn-sm px-4 mx-1" (click)="closeModal(additionalSectionForm.valid)" [disabled]="!additionalSectionForm.valid">Save</button>
        </div>
    </ng-template>
</ng-template>

<ng-template #competenciesFormTemplate>
    <form id="competenciesForm" novalidate #competenciesForm="ngForm" [ngClass]="{'p-5 scrollable-content': (editStep === STAGELIST.competencies)}">
        <div *ngIf="!editStep" class="w-100 mb-4 d-flex flex-column justify-content-center align-items-center">
            <p class="h6 fw-500 mb-0 text-quick-silver"> {{ inductionTitle }}: {{ project.name | uppercase }} </p>
            <p class="h4 fw-500 mb-0"> ADD YOUR COMPETENCIES & CERTIFICATION </p>
        </div>
        <div *ngIf="user_documents">
            <ng-container *ngFor="let c of user_documents; trackBy : trackByRowIndex; let i = index;">
                <div class="row mx-0 mb-3">
                    <div class="col-md-11 border rounded p-3" (click)="openEditIfExpired($event, c)" [ngClass]="{'border-disabled': c._is_required, 'cursor-pointer': (c._is_expired || c._verification_mandatory)}">
                        <div class="row m-0">
                            <div class="col-md-11 px-0 d-flex flex-column" [ngClass]="{'opacity-50': c._is_required && (!c._is_expired && !c._verification_mandatory)}">
                                <p class="d-flex align-items-center h5 mb-0 fw-500">
                                    {{c.name}}
                                    <span *ngIf="c.is_verified" class="ml-1 font-italic verified horizontal-center">Verified <span class="material-symbols-outlined x-large-font ml-1">check_circle</span></span>
                                    <span *ngIf="!c.is_verified && contractor_cscs_config.enabled && c._citb_enabled_doc" class="ml-1 font-italic verified text-danger horizontal-center">Not Verified <span class="material-symbols-outlined x-large-font ml-1">warning</span></span>
                                </p>
                                <p class="mb-0 small">Doc. Number: <span class="fw-400">{{ c.doc_number }}</span></p>
                                <p class="mb-0 small">Exipry: <span class="fw-400">{{ dayjsDisplayDate(+c.expiry_date) }}</span></p>
                                <b class="mb-0 medium-font text-danger" *ngIf="c._is_expired">Expired</b>
                                <p class="mb-0">
                                    <button class="btn btn-im-helmet btn-sm fw-500 mt-1" *ngIf="c.id && group_competencies.includes(c.name)"
                                        (click)="openAddCompetenciesModal(c, true, $event)"> + Add New Sentinel Competency/Certification
                                    </button>
                                </p>
                            </div>
                            <div class="col-md-1 px-0 d-flex align-items-center justify-content-end">
                                <img (click)="openEditCompetenciesModal(c, {}, false, $event)" class="cursor-pointer pr-3 ml-1 mb-1" [src]="editIconBlack" height="20">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1 p-3 d-flex align-items-center justify-content-center">
                        <span *ngIf="!c._is_required" class="material-symbols-outlined text-danger cursor-pointer" (click)="deleteUserDocument(c)"> delete</span>
                    </div>
                </div>
                <!-- If commented, it will disable next button until all required docs are not expired  -->
                <!--

                <input type="hidden" class="d-none"
                       [name]="'required_expired_'+c.id"
                       [required]="c._is_required && c._is_expired" [ngModel]="null" />
                   -->
                <!-- If commented, it will disable next button until all verification enabled docs are not verified  -->
                <!--
                <input type="hidden" class="d-none"
                       [name]="'verification_pending_'+c.id"
                       [required]="c._verification_mandatory" [ngModel]="c._verification_mandatory ? null : 'all-ok'" />
                -->
                <!-- children competencies -->
                <ng-container *ngFor="let cc of c?.children">
                    <div class="row mx-0 mb-3">
                        <div class="col-md-11 border rounded p-3" [ngClass]="{'border-disabled': c._is_required}">
                            <div class="row m-0">
                                <div class="col-md-11 px-0 d-flex flex-column">
                                    <p class="d-flex align-items-center h5 mb-0 fw-500">
                                        {{cc.name}}
                                        <span *ngIf="cc.is_verified" class="ml-1 font-italic verified">
                                            Verified <i class="fa fa-check-circle" aria-hidden="true"></i>
                                        </span>
                                    </p>
                                    <p class="mb-0 small">Doc. Number: <span class="fw-400">{{ cc.doc_number }}</span></p>
                                    <p class="mb-0 small">Exipry: <span class="fw-400">{{ dayjsDisplayDate(+cc.expiry_date) }}</span></p>
                                </div>
                                <div class="col-md-1 px-0 d-flex align-items-center justify-content-end">
                                    <img (click)="openEditCompetenciesModal(c, cc, true)" class="cursor-pointer pr-3 ml-1 mb-1" [src]="editIconBlack" height="20">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 p-3 d-flex align-items-center justify-content-center">
                            <span *ngIf="!c._is_required" class="material-symbols-outlined text-danger cursor-pointer" (click)="deleteUserDocument(c, cc)"> delete</span>
                        </div>
                    </div>
                </ng-container>
            </ng-container>
            <div class="row mx-0 mb-3">
                <div class="col-md-11 px-0">
                    <div class="d-flex align-items-center justify-content-center">
                        <button class="btn btn-link" (click)="openAddCompetenciesModal()"> + Add new Competency/Certification </button>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="missingDocumentMessages.length" class="row mx-0 mt-3">
            <div class="col-md-11 px-0">
                <ng-container *ngFor="let line of missingDocumentMessages;">
                    <div class="alert alert-danger">Please add {{line}}.</div>
                </ng-container>
            </div>
        </div>
        <input type="text" class="d-none" name="invalid_ruleset" [required]="invalid_sets.length > 0" [ngModel]="invalid_sets.length ? undefined : 'some-missing'" />
        <label class="small m-0">Please submit all required competencies/certifications here.</label>
    </form>
    <div class="row mx-0" *ngIf="!editStep else modalFooter">
        <div class="col-md-11 px-0 d-flex align-self-center justify-content-between">
            <button type="button" *ngIf="activeStage" class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="movePrev()">Back</button>
            <button type="submit" *ngIf="!isNotThisStage(STAGELIST.competencies)" [disabled]="!competenciesForm.valid || missingDocumentMessages.length > 0"
                class="btn btn-brandeis-blue ml-2 w-50" (click)="moveNext()" form="competenciesForm">Next</button>
        </div>
    </div>
    <ng-template #modalFooter>
        <div class="modal-footer-custom">
            <button type="button" class="btn btn-link btn-sm px-4 mx-1" (click)="closeModal(competenciesForm.valid)" #cancelBtn>Cancel</button>
            <button class="btn btn-brandeis-blue btn-sm px-4 mx-1" (click)="closeModal(competenciesForm.valid)" [disabled]="!competenciesForm.valid">Save</button>
        </div>
    </ng-template>
</ng-template>

<ng-template #projectMediaFormTemplate>
    <form id="projectMediaForm" novalidate #projectMediaForm="ngForm" [ngClass]="{'p-5 scrollable-content': (editStep === project.custom_field.induction_phrase_singlr)}">
        <div *ngIf="!editStep" class="w-100 mb-4 d-flex flex-column justify-content-center align-items-center">
            <p class="h6 fw-500 mb-0 text-quick-silver"> {{ inductionTitle }}: {{ project.name | uppercase }} </p>
            <p class="h4 fw-500 mb-0"> MEDIA </p>
        </div>
        <div *ngIf="project.has_media_content && media_files && media_files.length && !editStep">
            <label>Kindly {{ projectHasVideoMedia(media_files) ? 'watch' : 'read' }} below media content, to continue:</label>
            <input type="text" class="d-none" required name="all_media_watched" [ngModel]="!!induction_request.all_media_watched ? true : undefined" />
            <div *ngIf="projectHasVideoMedia(media_files);else media_else_part">
                <vg-player (onPlayerReady)="onMediaPlayerReady($event)">
                    <vg-overlay-play></vg-overlay-play>
                    <vg-buffering></vg-buffering>

                    <vg-controls>
                        <vg-play-pause></vg-play-pause>
                        <!-- <vg-playback-button></vg-playback-button> -->

                        <vg-time-display vgProperty="current" vgFormat="mm:ss"></vg-time-display>

                        <!--<vg-scrub-bar>
                            <vg-scrub-bar-current-time></vg-scrub-bar-current-time>
                            <vg-scrub-bar-buffering-time></vg-scrub-bar-buffering-time>
                        </vg-scrub-bar>

                        <vg-time-display vgProperty="left" vgFormat="mm:ss"></vg-time-display>-->
                        <vg-time-display vgProperty="total" vgFormat="mm:ss"></vg-time-display>

                        <!--<vg-track-selector></vg-track-selector>-->
                        <vg-mute></vg-mute>
                        <vg-volume></vg-volume>

                        <vg-fullscreen></vg-fullscreen>
                    </vg-controls>
                    <ng-template ngFor let-item [ngForOf]="(media_files)" let-i="index">
                        <video [vgMedia]="master" #master [vgMaster]="(media_files.length > 1) ? true : null" [id]="'video_' + i" preload="auto" *ngIf="i === 0 && item.file_ref">
                            <source [src]="item.file_ref.file_url" [type]="item.file_ref.file_mime">
                        </video>
                        <!-- https://stackoverflow.com/questions/44301213/videogular2-set-next-video-and-play-automatically -->
                        <video [vgMedia]="slave" #slave preload="auto" [id]="'video_' + i" *ngIf="i !== 0 && item.file_ref">
                            <source [src]="item.file_ref.file_url" [type]="item.file_ref.file_mime">
                        </video>
                    </ng-template>
                </vg-player>
            </div>
            <ng-template #media_else_part>
                <!--<div>
                    <button class="pl-0 btn btn-sm btn-link" (click)="openPdfViewer(project.media_file_ids[0])">View PDF</button>
                </div>-->
                <div class="pdf-viewer" style="height: 50vh" *ngIf="!isNotThisStage(STAGELIST.media) && media_files[0].file_ref && media_files[0].file_ref.file_url">
                    <ng2-pdfjs-viewer
                        [pdfSrc]="media_files[0].file_ref.file_url"
                        [print]="false"
                        [page]="1"
                        [openFile]="false"
                        [viewBookmark]="false"
                        viewerId="viewMediaUpload"
                        (onDocumentLoad)="pdfLoaded($event)"
                        (onPageChange)="pageChange($event)"
                    >
                    </ng2-pdfjs-viewer>
                </div>
                <hr>
            </ng-template>
            <div class="mt-2">
                <label>Kindly accept below declaration:</label>
                <ul class="list-group">
                    <li class="list-group-item border-0 px-0" *ngIf="project.has_media_content && project.media_declaration_content">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input cursor-pointer"
                                   [(ngModel)]="induction_request.accepting_media_declaration"
                                   [checked]="induction_request.accepting_media_declaration"
                                   name="confirm_accepting_media_declaration"
                                   id="confirm_accepting_media_declaration" />
                            <label class="custom-control-label d-block cursor-pointer" for="confirm_accepting_media_declaration">
                                {{ project.media_declaration_content }}
                            </label>
                        </div>
                        <input type="text" class="d-none" required name="accepting_media_declaration" [ngModel]="!!induction_request.accepting_media_declaration ? true: undefined"  *ngIf="!induction_request.accepting_media_declaration"/>
                    </li>
                </ul>
            </div>
            <br>
        </div>
        <div *ngIf="inductionQuestions?.status">
            <div class="form-group">
                <h5> {{project.custom_field.induction_phrase_singlr}} Quiz </h5>
                <small class="text-muted">This section tests your knowledge of the information from the previous step. You must answer all questions correctly to proceed</small>
            </div>
            <ng-template ngFor let-item [ngForOf]="inductionQuestions.induction_questions" let-i="index">
                <div>
                    <label><b>Q. {{inductionQuestions.induction_questions[i].question}}</b>
                    </label>
                    <ng-template ngFor let-item2="item" [ngForOf]="inductionQuestions.induction_questions[i].options" let-j="index">
                        <div class="form-check">
                            <input class="form-check-input cursor-pointer" type="checkbox"
                                (change)="checkAnswer($event, i, j, item.id)"
                                [checked]="induction_request.induction_answers[i] && induction_request.induction_answers[i].includes(inductionQuestions.induction_questions[i].options[j])"/>
                            <label>
                                {{inductionQuestions.induction_questions[i].options[j]}}
                            </label>
                        </div>
                    </ng-template>
                </div>
            </ng-template>
            <!-- <label class="text-danger" *ngIf="checkIfAnswersCorrect()">Please answer all questions correctly to continue</label> -->
            <label class="text-danger" *ngIf="this.correctAnswer.includes(false)">Please answer all questions correctly to continue</label>
        </div>
    </form>
    <div class="d-flex align-self-center justify-content-between mt-5" *ngIf="!editStep else modalFooter">
        <button type="button" *ngIf="activeStage" class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="movePrev()">Back</button>
        <button type="submit" *ngIf="!isNotThisStage(STAGELIST.media)"  [class.disabled]="!projectMediaForm.valid || checkIfAnswersCorrect()"
            class="btn btn-brandeis-blue ml-2 w-50" (click)="moveNextAfterQuiz(projectMediaForm.valid)" form="projectMediaForm">Next</button>
    </div>
    <ng-template #modalFooter>
        <div class="modal-footer-custom">
            <button type="button" class="btn btn-link btn-sm px-4 mx-1" (click)="closeModal(projectMediaForm.valid  || !checkIfAnswersCorrect())" #cancelBtn>Cancel</button>
            <button class="btn btn-brandeis-blue btn-sm px-4 mx-1"
                (click)="closeModal(projectMediaForm.valid  || !checkIfAnswersCorrect())" [disabled]="!projectMediaForm.valid  || checkIfAnswersCorrect()">Save</button>
        </div>
    </ng-template>
</ng-template>

<ng-template #eMediaFormTemplate>
    <form id="eMediaForm" novalidate #eMediaForm="ngForm">
        <div class="w-100 mb-4 d-flex flex-column justify-content-center align-items-center">
            <p class="h6 fw-500 mb-0 text-quick-silver"> {{ inductionTitle }}: {{ project.name | uppercase }} </p>
            <p class="h4 fw-500 mb-0"> E-MEDIA </p>
        </div>
        <ng-container *ngIf="project.has_media_content && e_media_url">
            <div class="form-group">
                <label>Kindly view below media content, to continue:</label>
            </div>
            <div class="form-group mb-0">
                <iframe [src]="e_media_url" id="embeddedVideoFrame" class="border-0"
                        allow="fullscreen; encrypted-media;" style="width: 100%;"
                ></iframe>
                <input type="text" class="d-none" required name="watched_html_media" [ngModel]="!!watched_html_media ? true: undefined"  *ngIf="!watched_html_media"/>
            </div>
            <hr class="mt-0">
            <div class="mt-2">
                <label>Kindly accept below declaration:</label>
                <ul class="list-group">
                    <li class="list-group-item border-0 px-0" *ngIf="project.has_media_content && project.media_declaration_content">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input cursor-pointer"
                                   [(ngModel)]="induction_request.accepting_media_declaration"
                                   [checked]="induction_request.accepting_media_declaration"
                                   name="confirm_accepting_e_media_declaration"
                                   id="confirm_accepting_e_media_declaration" />
                            <label class="custom-control-label d-block cursor-pointer" for="confirm_accepting_e_media_declaration">
                                {{ project.media_declaration_content }}
                            </label>
                        </div>
                        <input type="text" class="d-none" required name="accepting_e_media_declaration" [ngModel]="!!induction_request.accepting_media_declaration ? true: undefined"  *ngIf="!induction_request.accepting_media_declaration"/>
                    </li>
                </ul>
            </div>
        </ng-container>
    </form>
    <div class="d-flex align-self-center justify-content-between mt-5">
        <button type="button" *ngIf="activeStage" class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="movePrev()">Back</button>
        <button type="submit" *ngIf="!isNotThisStage(STAGELIST.eMedia)" [disabled]="!eMediaForm.valid"
            class="btn btn-brandeis-blue ml-2 w-50" (click)="moveNext()" form="eMediaForm">Next</button>
    </div>
</ng-template>

<ng-template #supervisorDecFormTemplate>
    <form id="supervisorDecForm" novalidate #supervisorDecForm="ngForm" [ngClass]="{'p-5 scrollable-content': (editStep === SUPERVISOR_STAGE.name)}">
        <div *ngIf="!editStep" class="w-100 mb-4 d-flex flex-column justify-content-center align-items-center">
            <p class="h6 fw-500 mb-0 text-quick-silver"> {{ inductionTitle }}: {{ project.name | uppercase }} </p>
            <p class="h4 fw-500 mb-0"> DECLARATIONS </p>
        </div>
        <label class="small">Kindly read & accept below declarations:</label>
        <ul class="list-group">
            <ng-template ngFor let-item [ngForOf]="(extraDeclarations[this.SUPERVISOR_STAGE.meta_key] || [])" let-i="index">
                <li class="list-group-item border-0 px-0">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input cursor-pointer" #superDeclareCheck
                               (change)="storeDeclaration('supervisor', item, (superDeclareCheck.checked ? 1 : 0))"
                               [checked]="checkDeclarationValue('supervisor', item.id) === 1"
                               [id]="'superCheck' + i" ng-value="1" />
                        <label class="custom-control-label d-block cursor-pointer" [attr.for]="'superCheck' + i">
                            {{ item.question }}
                        </label>
                        <input type="text" class="d-none" required [name]="'superCheck' + i"
                               [ngModel]="checkDeclarationValue('supervisor', item.id) ? true: undefined"
                               *ngIf="checkDeclarationValue('supervisor', item.id) !== 1"/>
                    </div>
                </li>
            </ng-template>
        </ul>
    </form>
    <div class="d-flex align-self-center justify-content-between mt-5" *ngIf="!editStep else modalFooter">
        <button type="button" *ngIf="activeStage" class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="movePrev()">Back</button>
        <button type="submit" *ngIf="!isNotThisStage(STAGELIST.supervisorDeclaration)" [disabled]="!supervisorDecForm.valid"
            class="btn btn-brandeis-blue ml-2 w-50" (click)="moveNext()" form="supervisorDecForm">Next</button>
    </div>
    <ng-template #modalFooter>
        <div class="modal-footer-custom">
            <button type="button" class="btn btn-link btn-sm px-4 mx-1" (click)="closeModal(supervisorDecForm.valid)" #cancelBtn>Cancel</button>
            <button class="btn btn-brandeis-blue btn-sm px-4 mx-1"
                (click)="closeModal(supervisorDecForm.valid)" [disabled]="!supervisorDecForm.valid">Save</button>
        </div>
    </ng-template>
</ng-template>

<ng-template #operatorDecFormTemplate>
    <form id="operatorDecForm" novalidate #operatorDecForm="ngForm" [ngClass]="{'p-5 scrollable-content': (editStep === OPERATOR_STAGE.name)}">
        <div *ngIf="!editStep" class="w-100 mb-4 d-flex flex-column justify-content-center align-items-center">
            <p class="h6 fw-500 mb-0 text-quick-silver"> {{ inductionTitle }}: {{ project.name | uppercase }} </p>
            <p class="h4 fw-500 mb-0"> PLANT/MACHINE OPERATOR DECLARATION </p>
        </div>
        <label class="row small mx-0">Please read and answer all the questions below:</label>
        <ul class="list-group">
            <ng-template ngFor let-item [ngForOf]="(extraDeclarations[this.OPERATOR_STAGE.meta_key] || [])" let-i="index">
                <!-- <li class="list-group-item border-0 px-0 row d-flex" *ngIf="item.type === 'radio'">
                    <label class="col-md-9 col-form-label">{{ item.question }}</label>
                    <div class="col-md-3 pl-0 form-inline mb-4 d-flex justify-content-md-end">
                        <div class="custom-control custom-radio mr-3">
                            <input type="radio" class="custom-control-input cursor-pointer" [name]="'operator' + item.id" [id]="'operator-yes-' + i"
                                   (change)="storeDeclaration('operator', item, 'yes')"
                                   [checked]="checkDeclarationValue('operator', item.id) === 'yes'"
                                   [value]="'yes'" />
                            <label class="custom-control-label cursor-pointer" [for]="'operator-yes-' + i"> Yes</label>
                        </div>
                        <div class="custom-control custom-radio">
                            <input type="radio" class="custom-control-input cursor-pointer" [name]="'operator' + item.id" [id]="'operator-no-' + i"
                                   (change)="storeDeclaration('operator', item, 'no')"
                                   [checked]="checkDeclarationValue('operator', item.id) === 'no'"
                                   [value]="'no'" />
                            <label class="custom-control-label cursor-pointer" [for]="'operator-no-' + i"> No </label>
                        </div>
                        <input type="text" class="d-none" required [name]="'operatorCheck' + i"
                               [ngModel]="checkDeclarationValue('operator', item.id) ? true: undefined"
                               *ngIf="checkDeclarationValue('operator', item.id) === undefined"/>
                    </div>
                </li> -->
                <ng-container *ngIf="item.type === 'radio'">
                    <div class="form-group row mb-0">
                        <div class="col-sm-8 mb-0">
                            <label>{{ item.question }}</label>
                        </div>
                        <div class="col-sm-4 font-weight-bold d-flex justify-content-md-end">
                            <div class="ml-3 radio-button-custom">
                                <label class="mr-2 cursor-pointer" [for]="'operator-yes-' + i"> Yes</label>
                                <input class="cursor-pointer" type="radio" [name]="'operator' + item.id" [id]="'operator-yes-' + i"
                                    (change)="storeDeclaration('operator', item, 'yes')" [checked]="checkDeclarationValue('operator', item.id) === 'yes'"
                                    [value]="'yes'"/>
                            </div>
                            <div class="ml-3 radio-button-custom">
                                <label class="mr-2 cursor-pointer" [for]="'operator-no-' + i"> No </label>
                                <input class="cursor-pointer" type="radio" [name]="'operator' + item.id" [id]="'operator-no-' + i"
                                    (change)="storeDeclaration('operator', item, 'no')" [checked]="checkDeclarationValue('operator', item.id) === 'no'"
                                    [value]="'no'"/>
                            </div>
                            <input type="text" class="d-none" required [name]="'operatorCheck' + i"
                                   [ngModel]="checkDeclarationValue('operator', item.id) ? true: undefined"
                                   *ngIf="checkDeclarationValue('operator', item.id) === undefined"/>
                        </div>
                    </div>
                    <hr *ngIf="(extraDeclarations[this.OPERATOR_STAGE.meta_key][i+1].type !== 'text')" class="my-3 mx-0 hr-light-silver" />
                </ng-container>
                <li class="list-group-item border-0 px-0 row d-flex" *ngIf="item.type === 'text'">
                    <label class="col-md-12 col-form-label">{{ item.question }}</label>
                    <div class="col-md-12">
                        <textarea [name]="'operator' + item.id" style="min-height: 100px;"
                                  required class="form-control" (change)="storeDeclaration('operator', item, operator_txt_detail.value)"
                                  #operator_txt_detail="ngModel" [ngModel]="checkDeclarationValue('operator', item.id)"
                                  placeholder="Please provide details here"
                                  [value]="checkDeclarationValue('operator', item.id)"></textarea>
                        <div class="alert alert-danger m-0" [hidden]="(checkDeclarationValue('operator', item.id) || operator_txt_detail.valid)">This field is required</div>
                    </div>
                </li>
            </ng-template>
        </ul>
    </form>
    <div class="d-flex align-self-center justify-content-between mt-5" *ngIf="!editStep else modalFooter">
        <button type="button" *ngIf="activeStage" class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="movePrev()">Back</button>
        <button type="submit" *ngIf="!isNotThisStage(STAGELIST.plantMachineryOperatorDeclaration)" [disabled]="!operatorDecForm.valid"
            class="btn btn-brandeis-blue ml-2 w-50" (click)="moveNext()" form="operatorDecForm">Next</button>
    </div>
    <ng-template #modalFooter>
        <div class="modal-footer-custom">
            <button type="button" class="btn btn-link btn-sm px-4 mx-1" (click)="closeModal(operatorDecForm.valid)" #cancelBtn>Cancel</button>
            <button class="btn btn-brandeis-blue btn-sm px-4 mx-1"
                (click)="closeModal(operatorDecForm.valid)" [disabled]="!operatorDecForm.valid">Save</button>
        </div>
    </ng-template>
</ng-template>

<ng-template #declarationFormTemplate>
    <form id="declarationForm" novalidate #declarationForm="ngForm">
        <div class="w-100 mb-4 d-flex flex-column justify-content-center align-items-center">
            <p class="h6 fw-500 mb-0 text-quick-silver"> {{ inductionTitle }}: {{ project.name | uppercase }} </p>
            <p class="h4 fw-500 mb-0"> SITE DECLARATIONS </p>
        </div>
        <label class="small">Please read & accept the declarations below:</label>
        <!--<input type="text" class="d-none" required #declarationsFields="ngModel" [(ngModel)]="induction_request.accepted_declarations" name="declarations" *ngIf="!induction_request.accepted_declarations || induction_request.accepted_declarations.length === 0"/>-->
        <ul class="list-group">
            <ng-template ngFor let-item [ngForOf]="(project && project.declarations || [])" let-i="index">
                <li class="list-group-item border-0 px-0">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input cursor-pointer" (change)="changeModel($event, induction_request.accepted_declarations, item.id)"
                               [checked]="induction_request.accepted_declarations.includes(item.id)"
                               [id]="'customCheck' + item.id" ng-value="item.id" />
                        <label class="custom-control-label d-block cursor-pointer" [attr.for]="'customCheck' + item.id">
                            {{ item.content }}
                        </label>
                        <input type="text" class="d-none" required name="declarations{{i}}" [ngModel]="induction_request.accepted_declarations.indexOf(item.id) !== -1 ? true: undefined"  *ngIf="!induction_request.accepted_declarations || induction_request.accepted_declarations.indexOf(item.id) === -1"/>
                    </div>
                </li>
            </ng-template>
            <li class="list-group-item border-0 px-0">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input cursor-pointer"
                           [(ngModel)]="induction_request.confirm_detail_valid"
                           [checked]="induction_request.confirm_detail_valid"
                           name="confirm_detail_valid_customCheck"
                           id="confirm_detail_valid_customCheck" />
                    <label class="custom-control-label d-block cursor-pointer" for="confirm_detail_valid_customCheck">
                        I confirm that I have reviewed all information provided in my profile and on this form and agree it is correct and up to date.
                    </label>
                </div>
                <input type="text" class="d-none" required name="confirm_detail_valid" [ngModel]="!!induction_request.confirm_detail_valid ? true: undefined"  *ngIf="!induction_request.confirm_detail_valid"/>
            </li>
        </ul>
    </form>
    <div class="d-flex align-self-center justify-content-between mt-5">
        <button type="button" *ngIf="activeStage" class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="movePrev()">Back</button>
        <button type="submit" *ngIf="!isNotThisStage(STAGELIST.declaration)" [disabled]="!declarationForm.valid"
            class="btn btn-brandeis-blue ml-2 w-50" (click)="moveNext()" form="declarationForm">Next</button>
    </div>
</ng-template>

<ng-template #ramsFormTemplate>
    <form id="ramsForm" novalidate #ramsForm="ngForm" [ngClass]="{'p-5 scrollable-content': editStep === ramsPhrase}">
        <div class="w-100 mb-4 d-flex flex-column justify-content-center align-items-center">
            <p class="h6 fw-500 mb-0 text-quick-silver"> {{ inductionTitle }}: {{ project.name | uppercase }} </p>
            <p class="h4 fw-500 mb-0"> {{ ramsPhrase }} </p>
        </div>
        <div class="form-group" *ngIf="induction_rams.length > 1">
            <label>Here you are required to review and sign a {{ramsPhrase}} that is specific to the work that you're undertaking. Select the relevant document from the list below</label>
            <ng-select [items]="induction_rams"
                       [bindLabel]="'briefing_title'"
                       [bindValue]="'id'"
                       [placeholder]="'Select '+ramsPhrase"
                       name="rams_selector"
                       required="true"
                       (change)="onSelectRams($event)"
                       [(ngModel)]="selectedRamsId">
            </ng-select>
        </div>
        <div class="pdf-viewer" style="height: 50vh" *ngIf="!isNotThisStage(ramsPhrase) && selectedRams.id">
            <ng-container *ngIf="isPdfViewerVisible">
                <ng2-pdfjs-viewer
                    [pdfSrc]="selectedRams.briefing_file_ref.file_url"
                    [print]="false"
                    [page]="1"
                    [openFile]="false"
                    [viewBookmark]="false"
                    viewerId="ramsBriefing"
                >
                </ng2-pdfjs-viewer>
            </ng-container>
        </div>
        <hr>
        <!-- <div class="form-group" *ngIf="!isNotThisStage(ramsPhrase) && selectedRams.id">
            <label>Sign below to confirm that you`ve read and understood everything in the {{ramsPhrase}} document</label>
            <div class="col-md-12 form-inline p-0">
                <span class="sign-here-text" *ngIf="!validRamsBriefingSignature">Sign Here</span>
                <ng-signature-pad
                    *ngIf="!isNotThisStage(ramsPhrase)"
                    [emitOnDragEnd]="true"
                    format="base64"
                    [width]="700"
                    [height]="100"
                    (cleared)="clearRamsBriefingSignature()"
                    [showDoneButton]="false"
                    clearButtonClass="btn btn-primary btn-sm clear-sign-btn mr-2"
                    (done)="saveRamsBriefingSignature($event)"
                    (pointsChange)="pointsChanged($event)"
                ></ng-signature-pad>
            </div>
            <input type="text" class="d-none" required name="ramsBriefingSignature" [ngModel]="validRamsBriefingSignature ? true: undefined"/>
        </div> -->
        <div *ngIf="ramsSignatureStatus !== 0">
            <div class="form-group" *ngIf="!isNotThisStage(ramsPhrase) && selectedRams.id">
                <label>Sign below to confirm that you`ve read and understood everything in the {{ramsPhrase}} document</label>
                <div class="col-md-12 form-inline p-0">
                    <signature-pad-selector class="w-100" *ngIf="!ramsBriefingSignature"
                        [height]="130"
                        [width]="700"
                        (signChanged)="saveRamsBriefingSignature($event)"
                        (clear)="clearRamsBriefingSignature()"
                        (signPointsChanged)="pointsChanged($event)">
                    </signature-pad-selector>
                    <div *ngIf="ramsBriefingSignature"  class="img-file-wrap">
                        <span (click)="clearRamsBriefingSignature()" type="button" class="close-icon text-white delete" title="Clear Signature">
                            <span class="bg-danger" aria-hidden="true">×</span>
                        </span>
                        <img class="w-100" [src]="ramsBriefingSignature" alt="signature">
                    </div>
                </div>
                <input type="text" class="d-none" required name="ramsBriefingSignature" [ngModel]="(ramsBriefingSignature || ramsSignatureStatus !== 2) ? true: undefined"/>
            </div>
        </div>
    </form>
    <div class="d-flex align-self-center justify-content-between mt-5" *ngIf="!editStep else modalFooter">
        <button type="button" *ngIf="activeStage" class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="movePrev()">Back</button>
        <button type="submit" *ngIf="!isNotThisStage(ramsPhrase)" [disabled]="!ramsForm.valid" class="btn btn-brandeis-blue ml-2 w-50" (click)="moveNext()" form="ramsForm">Next</button>
    </div>
    <ng-template #modalFooter>
        <div class="modal-footer-custom">
            <button type="button" class="btn btn-link btn-sm px-4 mx-1" (click)="closeModal(ramsForm.valid)" #cancelBtn>Cancel</button>
            <button class="btn btn-brandeis-blue btn-sm px-4 mx-1" (click)="closeModal(ramsForm.valid)" [disabled]="!ramsForm.valid">Save</button>
        </div>
    </ng-template>
</ng-template>

<ng-template #reviewFormTemplate>
    <form [ngClass]="{'preview-container': true}" id="reviewForm" novalidate #reviewForm="ngForm">
        <div class="row m-0">
            <div class="col-1 px-0"></div>
            <div class="col px-0 mb-4 d-flex flex-column justify-content-center align-items-center">
                <p class="h6 fw-500 mb-0 text-quick-silver"> {{ inductionTitle }}: {{ project.name | uppercase }} </p>
                <p class="h4 fw-500 mb-0"> REVIEW </p>
            </div>
        </div>
        <div class="">
            <!-- <label class="h6 font-weight-bold">Kindly Review all the information before submitting</label> -->
            <div class="row m-0">
                <div class="col-1 px-0"></div>
                <table class="col table no-spacing table-borderless mt-1 mb-2">
                    <tbody>
                    <tr> <td class="fw-500 h5 pl-0 pb-3"> Personal Details </td> </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0 pr-3" colspan="3">Title: <span class="fw-500 line-height-min">{{ authUser$.title }}</span></td>
                        <td rowspan="8" class="img-container-sm pb-1">
                            <img height="140" width="140" class="rounded-circle float-right"
                                [src]="(authUser$?.profile_pic_ref?.sm_url ? authUser$.profile_pic_ref.sm_url : authUser$.profile_pic_ref?.file_url)" alt="Profile pic">
                        </td>
                    </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">First Name: <span class="fw-500 line-height-min">{{ authUser$.first_name }}</span></td>
                    </tr>
                    <tr *ngIf="authUser$?.middle_name">
                        <td class="d-flex flex-column pb-3 pl-0">Middle Name(s): <span class="fw-500 line-height-min">{{ authUser$.middle_name }}</span></td>
                    </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">Last Name: <span class="fw-500 line-height-min">{{ authUser$.last_name }}</span></td>
                    </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">Date of Birth: <span class="fw-500 line-height-min">{{ dayjs(authUser$.dob).format(dateFormat) }}</span></td>
                    </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">Gender: <span class="fw-500 line-height-min">{{ authUser$.gender }}</span></td>
                    </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">Nationality: <span class="fw-500 line-height-min">{{ authUser$.country }}</span></td>
                    </tr>
                    <tr *ngIf="showNIN">
                        <td class="d-flex flex-column pb-3 pl-0" colspan="3"><span i18n="@@nin">National Insurance Number:</span> <span class="fw-500 line-height-min">{{ authUser$.nin ? authUser$.nin : 'N/A'}}</span></td>
                    </tr>
                    <tr *ngIf="showEmpNbr">
                        <td class="d-flex flex-column pb-3 pl-0" colspan="3"><span>Employee Number:</span> <span class="fw-500 line-height-min">{{ employment_detail.employee_number ? employment_detail.employee_number : 'N/A'}}</span></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="row m-0 mb-1" *ngIf="contactDetail">
                <div class="col-1 px-0"></div>
                <div class="col px-0"> <hr class=""> </div>
            </div>
            <div class="row m-0" *ngIf="contactDetail">
                <div class="col-1 px-0"></div>
                <table class="col table no-spacing table-borderless mt-1 mb-2">
                    <tbody>
                    <tr> <td class="fw-500 h5 pl-0 pb-3">Contact Details</td> </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">City: <span class="fw-500 line-height-min">{{ contactDetail.city }}</span></td>
                    </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">
                            <span i18n="@@pc">Post Code:</span> <span class="fw-500 line-height-min">{{ contactDetail.post_code ? contactDetail.post_code : 'N/A' }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">Home No.: <span class="fw-500 line-height-min">{{ contactDetail?.home_number?.code ? '(+' + contactDetail.home_number.code + ')' : '' }} {{contactDetail?.home_number?.number}}</span></td>
                    </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">Mobile No.: <span class="fw-500 line-height-min">{{ contactDetail?.mobile_number?.code ? '(+' + contactDetail.mobile_number.code + ')' : '' }} {{contactDetail?.mobile_number?.number}}</span></td>
                    </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">Email: <span class="fw-500 line-height-min">{{ authUser$.email }}</span></td>
                    </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">Emergency Contact: <span class="fw-500 line-height-min">{{ contactDetail.emergency_contact }}</span></td>
                    </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">Emergency Contact No.: <span class="fw-500 line-height-min">{{ contactDetail?.emergency_contact_number?.code ? '(+' + contactDetail.emergency_contact_number.code + ')' : '' }} {{contactDetail?.emergency_contact_number?.number}}</span></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="row m-0 mb-1" *ngIf="employment_detail?.id">
                <div class="col-1 px-0"></div>
                <div class="col px-0"> <hr class=""> </div>
            </div>
            <div class="row m-0" *ngIf="employment_detail?.id">
                <div class="col-1 px-0"></div>
                <table class="col table no-spacing table-borderless mt-1 mb-2">
                    <tbody>
                    <tr> <td class="fw-500 h5 pl-0 pb-3">Employment Details</td> </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">Company:
                            <span class="fw-500 line-height-min">{{ employment_detail.employer }} <ng-container *ngIf="employment_detail.employment_company">({{ employment_detail.employment_company }})</ng-container></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">Job Role: <span class="fw-500 line-height-min">{{ employment_detail.job_role }}</span></td>
                    </tr>
                    <tr *ngIf="showTypeOfEmployment">
                        <td class="d-flex flex-column pb-3 pl-0">Type of employment: <span class="fw-500 line-height-min">{{ employment_detail.type_of_employment }}</span></td>
                    </tr>
                    <tr *ngIf="showEmploymentStartDate">
                        <td class="d-flex flex-column pb-3 pl-0">Time with employer: <span class="fw-500 line-height-min">{{ createEmploymentTime(employment_detail.start_date_with_employer) }}</span></td>
                    </tr>
                    <tr *ngIf="showMinWage">
                        <td class="d-flex flex-column pb-3 pl-0">Do you earn above min. living wage: <span class="fw-500 line-height-min">{{ numberToYesNo(employment_detail.earn_mlw_e783) }}</span></td>
                    </tr>
                    <tr *ngIf="showEmpNbr">
                        <td class="d-flex flex-column pb-3 pl-0">Employee Number: <span class="fw-500 line-height-min">{{ employment_detail.employee_number}}</span></td>
                    </tr>
                    <tr *ngIf="employment_detail?.comment">
                        <td class="d-flex flex-column pb-3 pl-0">Comments: <span class="fw-500"><span [innerHTML]="employment_detail.comment" style="white-space: pre-line"></span>&nbsp;</span></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="row m-0 mb-1">
                <div class="col-1 px-0"></div>
                <div class="col px-0"> <hr class=""> </div>
            </div>
            <!-- Travel -->
            <div class="row m-0">
                <div class="col-1 px-0 d-flex justify-content-center pt-2"> <i (click)="openEditReviewModal(STAGELIST.travel)" class="fa fa-edit fw-500 cursor-pointer"></i> </div>
                <table class="col table no-spacing table-borderless mt-1 mb-2">
                    <tbody>
                    <tr> <td class="fw-500 h5 pl-0 pb-3">Travel</td> </tr>
                    <tr>
                        <td class="pb-3 pl-0 pr-3">
                            <div class="pb-2">
                                <label class="mb-0">Will you be staying at the home address listed in your profile while working on this project?</label>
                                <small class="faded" *ngIf="contactDetail"><b>Address</b>: {{removeLastComma(contactDetail?.street)}}, {{contactDetail?.city}}</small>
                            </div>
                        </td>
                        <td class="text-capitalize fw-500 pb-3 pl-0 text-right">{{ induction_request._postcode_is_correct }}</td>
                    </tr>
                    <ng-container *ngIf="induction_request._postcode_is_correct === 'no'">
                        <tr>
                            <td class="d-flex flex-column pb-3 pl-0"><span i18n="@@tfpc">Travelling from ({{labelData.label}}):</span> <b>{{ travel_time_override.from_postcode }}</b></td>
                        </tr>
                        <tr>
                            <td class="d-flex flex-column pb-3 pl-0"><span i18n="@@ttpc">Traveling to ({{labelData.label}}):</span> <b>{{ travel_time_override.to_postcode }}</b></td>
                        </tr>
                    </ng-container>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">
                            Travel time to work:
                            <span class="fw-500 line-height-min">{{ createTime(induction_request.travel_time_to_work) }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">
                            Travel time to home from work:
                            <span class="fw-500 line-height-min">{{ createTime(induction_request.travel_time_to_home) }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="d-flex flex-column pb-3 pl-0">Method of travel: <span class="fw-500 line-height-min">{{ induction_request.travel_method }}</span></td>
                    </tr>
                    <tr>
                        <td class="pb-3 pl-0">
                            <div class="d-flex flex-column" *ngIf="isVehicleRegRequired(induction_request.travel_method)">
                                Vehicle Reg. No.: <span class="fw-500 line-height-min">{{ induction_request.vehicle_reg_number }}</span>
                            </div>
                        </td>
                    </tr>
                    <ng-container *ngIf="induction_request?.travel_time?.vehicle_info?.make">
                        <tr>
                            <td class="d-flex flex-column pb-3 pl-0">Vehicle Make: <span class="fw-500 line-height-min">{{ induction_request.travel_time.vehicle_info.make }}</span></td>
                        </tr>
                        <tr>
                            <td class="d-flex flex-column pb-3 pl-0">Fuel Type: <span class="fw-500 line-height-min">{{ induction_request.travel_time.vehicle_info?.fuelType }}</span></td>
                        </tr>
                        <tr>
                            <td class="d-flex flex-column pb-3 pl-0">CO2 Emissions: <span class="fw-500 line-height-min">{{ induction_request.travel_time.vehicle_info?.co2Emissions }} g/km</span></td>
                        </tr>
                    </ng-container>
                    </tbody>
                </table>
            </div>
            <div class="row m-0 mb-1">
                <div class="col-1 px-0"></div>
                <div class="col px-0"> <hr class=""> </div>
            </div>
            <!-- Safety Assessment -->
            <div class="row m-0">
                <div class="col-1 px-0 d-flex justify-content-center pt-2"> <i (click)="openEditReviewModal(STAGELIST.safetyAssessment)" class="fa fa-edit fw-500 cursor-pointer"></i> </div>
                <table class="col table no-spacing table-borderless mt-1 mb-2">
                    <tbody>
                        <tr> <td class="fw-500 h5 pl-0 pb-3">Safety Assessment</td> </tr>
                        <tr>
                            <td class="pb-3 pl-0 pr-3">I confirm I am fit and able to undertake the work activities my role requires?:</td>
                            <td class="text-right pb-3 pl-0"><span class="fw-500 line-height-min">{{ numberToYesNo(induction_request.fit_undertake_role) }}</span></td>
                        </tr>
                        <tr>
                            <td class="pb-3 pl-0 pr-3">I confirm I am fit to work safely?</td>
                            <td class="text-right pb-3 pl-0"><span class="fw-500 line-height-min">{{ numberToYesNo(induction_request.fit_to_work) }}</span></td>
                        </tr>
                        <tr *ngIf="project && working_hr_agreement.policy_name">
                            <td class="pb-3 pl-0 pr-3">I will comply with the working hours agreement on site?</td>
                            <td class="text-right pb-3 pl-0"><span class="fw-500 line-height-min">{{ numberToYesNo(induction_request.comply_hour_agreement) }}</span></td>
                        </tr>
                        <tr *ngIf="project && d_and_a_policy.policy_name">
                            <td class="pb-3 pl-0 pr-3">Do you accept the drugs and alcohol policy?</td>
                            <td class="text-right pb-3 pl-0"><span class="fw-500 line-height-min">{{ numberToYesNo(induction_request.accept_drug_alcohol_pol) }}</span></td>
                        </tr>
                        <ng-template ngFor let-policy [ngForOf]="project.further_policies" let-i="index">
                            <tr *ngIf="!policy.is_default && policy.policy_name">
                                <td class="pb-3 pl-0 pr-3">Do you agree with the {{policy.policy_name}} policy?</td>
                                <td class="text-right pb-3 pl-0"><span class="fw-500 line-height-min">{{ numberToYesNo(induction_request.accepted_further_policies.includes(i)) }}</span></td>
                            </tr>
                        </ng-template>
                    </tbody>
                </table>
            </div>
            <div class="row m-0 mb-1" *ngIf="showProfileHealthAssessment && enabledProfileAssessment.health_answers_required && healthAssessment">
                <div class="col-1 px-0"></div>
                <div class="col px-0"> <hr class=""> </div>
            </div>
            <!-- Health Assessment -->
            <div class="row m-0" *ngIf="showProfileHealthAssessment && enabledProfileAssessment.health_answers_required && healthAssessment">
                <div class="col-1 px-0 d-flex justify-content-center pt-1"></div>
                <div class="col px-0">
                    <div class="table-outer">
                        <p class="fw-500 h5 pl-0 pb-3 mb-3">Health Assessment</p>
                        <ng-template ngFor let-item [ngForOf]="(getKeys(healthAssessment))" let-i="index">
                            <p class="fw-500 line-height-min">{{ item }}</p>
                            <table class="table no-spacing table-borderless mt-1 mb-2">
                                <tbody>
                                <tr *ngFor="let row of (healthAssessment[item])">
                                    <td class="pb-3 pl-0 pr-3">{{ row.question }}</td>
                                    <td class="pb-3 pl-0 text-right"><span class="fw-500 line-height-min">{{ numberToYesNo(row.answer) }}</span></td>
                                </tr>
                                </tbody>
                            </table>
                        </ng-template>
                    </div>
                </div>
            </div>
            <div class="row m-0 mb-1" *ngIf="showProfileMedicalAssessment && enabledProfileAssessment.medical_answers_required && medicalAssessment.length">
                <div class="col-1 px-0"></div>
                <div class="col px-0"> <hr class=""> </div>
            </div>
            <!-- Medical Assessment -->
            <div class="row m-0" *ngIf="showProfileMedicalAssessment && enabledProfileAssessment.medical_answers_required && medicalAssessment.length">
                <div class="col-1 px-0 pt-2"></div>
                <table class="col table no-spacing table-borderless mt-1 mb-2">
                    <tbody>
                        <tr><td class="fw-500 h5 pl-0 pb-3">Medical Assessment</td></tr>
                        <ng-template ngFor let-row [ngForOf]="medicalAssessment" let-i="index">
                            <tr>
                                <td class="pl-0 pr-3" [ngClass]="{'pb-3': !row?.ans_details}">{{ row.question }}</td>
                                <td class="pl-0 text-right" [ngClass]="{'pb-3': !row?.ans_details}">
                                    <span class="fw-500 line-height-min">{{ numberToYesNo(row.answer) }}</span>
                                </td>
                            </tr>
                            <tr *ngIf="row?.ans_details">
                                <td colspan="2" class="pb-3 px-0 border-0"><strong>Details:</strong> {{ row.ans_details }}</td>
                            </tr>
                        </ng-template>
                    </tbody>
                </table>
            </div>
            <div class="row m-0 mb-1" *ngIf="showInductionHealthAssessment">
                <div class="col-1 px-0"></div>
                <div class="col px-0"> <hr class=""> </div>
            </div>
            <!-- Site Health Assessment -->
            <div class="row m-0" *ngIf="showInductionHealthAssessment">
                <div class="col-1 px-0 d-flex justify-content-center pt-1"><i (click)="openEditReviewModal(STAGELIST.healthAssessment)" class="fa fa-edit fw-500 cursor-pointer"></i></div>
                <table class="col table no-spacing table-borderless mt-1 mb-2">
                    <tbody>
                    <tr><td class="fw-500 h5 pl-0 pb-3">Site Health Assessment</td></tr>
                    <tr>
                        <td class="pb-3 pl-0 pr-3">Do you have any reportable medical conditions?</td>
                        <td class="pb-3 pl-0 text-capitalize text-right fw-500">{{ induction_request.reportable_medical_conditions }}</td>
                    </tr>
                    <tr *ngIf="!isDisabled('reportable_medical_conditions')">
                        <td class="pb-3 pl-0">
                            <div class="d-flex flex-column">Detail: <span class="fw-500 line-height-min">{{ induction_request.rmc_detail }}</span></div>
                        </td>
                    </tr>
                    <tr>
                        <td class="pb-3 pl-0 pr-3">Are you taking any regular medication, prescribed or otherwise?</td>
                        <td class="pb-3 pl-0 text-capitalize text-right fw-500">{{ induction_request.on_long_medication }}</td>
                    </tr>
                    <tr *ngFor="let medication of induction_request.medications">
                        <td class="pb-3 pl-0" *ngIf="!isDisabled('on_long_medication')">
                            <div class="d-flex flex-column pb-3 pl-0 pr-3">
                                Name: <span class="fw-500 line-height-min">{{ medication.medication_name }}</span>
                            </div>
                            <div class="d-flex flex-column pb-3 pl-0 pr-3">
                                Dosage: <span class="fw-500 line-height-min">{{ medication.medication_dosage }}</span>
                            </div>
                            <div class="d-flex flex-column pb-3 pl-0 pr-3">
                                Frequency: <span class="fw-500 line-height-min">{{ medication.medication_frequency }}</span>
                            </div>
                            <div class="d-flex flex-column pb-3 pl-0 pr-3">
                                commenced date: <span class="fw-500 line-height-min">{{ dayjs(medication.medication_date_commenced, this.dateStorageFormat).format(this.dateFormat) }}</span>
                            </div>
                            <div class="d-flex flex-column pb-3 pl-0 pr-3">
                                completion date: <span class="fw-500 line-height-min">
                                    {{ medication.infinite_completion ? "Indefinite" : dayjs(medication.medication_date_of_completion, this.dateStorageFormat).format(this.dateFormat) }}
                                </span>
                            </div>
                        </td>
                    </tr>
                    <ng-container *ngIf="!isDisabled('on_long_medication')">
                        <tr>
                            <td class="pl-0 pr-3" [ngClass]="{'pb-3': !row?.ans_details}">Do you suffer from any side effects?</td>
                            <td class="pl-0 text-capitalize text-right fw-500" [ngClass]="{'pb-3': !row?.ans_details}"> <b>{{ induction_request.any_side_effects }}</b></td>
                        </tr>
                        <tr *ngIf="!isDisabled('any_side_effects')">
                            <td class="pb-3 px-0 pr-3">Details: </td>
                            <td class="pb-3 px-0 text-right"> <span class="fw-500 line-height-min">{{ induction_request.any_side_effect_detail }}</span></td>
                        </tr>
                    </ng-container>
                    </tbody>
                </table>
            </div>
            <div class="row m-0 mb-1" *ngIf="isAdditionalQuestionStageActivated()">
                <div class="col-1 px-0"></div>
                <div class="col px-0"> <hr class=""> </div>
            </div>
            <!-- additional Section -->
            <div class="row m-0" *ngIf="isAdditionalQuestionStageActivated()">
                <div class="col-1 px-0 d-flex justify-content-center pt-1"><i (click)="openEditReviewModal(this.additionalSectionTitle)" class="fa fa-edit fw-500 cursor-pointer"></i></div>
                <table class="col table no-spacing table-borderless mt-1 mb-2">
                    <tbody>
                    <tr><td class="fw-500 h5 pl-0 pb-3"> {{ this.additionalSectionTitle }} </td></tr>
                    <ng-template ngFor let-answer [ngForOf]=" additionalInductionQuestions.induction_questions" let-i="index">
                        <tr>
                            <td class="pb-3 pl-0 pr-3"> {{ additionalInductionQuestions.induction_questions[i].question }} </td>
                            <td class="pb-3 pl-0 text-capitalize text-right fw-500">
                                <b class="m-0" style="white-space: pre-line;">
                                    <span *ngIf="!['date'].includes(additionalInductionQuestions.induction_questions[i].ans_field_type)">
                                        {{ additionalInductionQuestions.induction_questions[i].answer }}
                                    </span>
                                    <span *ngIf="additionalInductionQuestions.induction_questions[i].ans_field_type === 'date'">
                                        {{ additionalInductionQuestions.induction_questions[i].answer ? dayjs(+dateToEpoch(i)).format('DD/MM/YYYY') : ''}}
                                    </span>
                                </b>
                            </td>
                        </tr>
                        <ng-template ngFor let-item [ngForOf]="additionalInductionQuestions.induction_questions[i].sub_questions" let-j="index">
                            <tr *ngIf="additionalInductionQuestions.induction_questions[i].sub_questions[j].que_condition == 'If '+additionalInductionQuestions.induction_questions[i].answer">
                                <td class="pb-3 pl-0 pr-3"> {{ additionalInductionQuestions.induction_questions[i].sub_questions[j].question }} </td>
                                <td class="pb-3 pl-0 text-capitalize text-right fw-500">
                                    <b class="m-0" style="white-space: pre-line;">
                                        <span *ngIf="!['date'].includes(additionalInductionQuestions.induction_questions[i].sub_questions[j].ans_field_type)">
                                            {{ additionalInductionQuestions.induction_questions[i].sub_questions[j].answer }}
                                        </span>
                                        <span *ngIf="additionalInductionQuestions.induction_questions[i].sub_questions[j].ans_field_type === 'date'">
                                            {{ additionalInductionQuestions.induction_questions[i].sub_questions[j].answer ? dayjs(+dateToEpoch(i, j)).format('DD/MM/YYYY') : ''}}
                                        </span>
                                    </b>
                                </td>
                            </tr>
                        </ng-template>
                    </ng-template>
                    </tbody>
                </table>
            </div>
            <div class="row m-0 mb-1" *ngIf="user_documents?.length">
                <div class="col-1 px-0"></div>
                <div class="col px-0"> <hr class=""> </div>
            </div>
            <!-- Competencies -->
            <div class="row m-0" *ngIf="user_documents?.length">
                <div class="col-1 px-0 d-flex justify-content-center pt-1"><i (click)="openEditReviewModal(STAGELIST.competencies)" class="fa fa-edit fw-500 cursor-pointer"></i></div>
                <div class="col-11 px-0">
                    <p class="fw-500 h5 pl-0 pb-3">Competencies</p>
                    <div class="row border rounded p-3 m-0 mb-3" *ngFor="let user_doc of getFilteredRecords(user_documents, induction_request.user_doc_ids, 'id')">
                        <div class="col-md-12 px-0 d-flex justify-content-between">
                            <div>
                                <p class="d-flex align-items-center h5 mb-0 fw-500">
                                    {{user_doc.name}}
                                    <span *ngIf="user_doc.is_verified" class="ml-1 font-italic verified horizontal-center">Verified <span class="material-symbols-outlined x-large-font ml-1">check_circle</span></span>
                                    <span *ngIf="!user_doc.is_verified && contractor_cscs_config.enabled && user_doc._citb_enabled_doc" class="ml-1 font-italic verified text-danger horizontal-center">Not Verified <span class="material-symbols-outlined x-large-font ml-1">warning</span></span>
                                </p>
                                <p class="mb-0 small">Doc. Number: <span class="fw-400">{{ user_doc.doc_number }}</span></p>
                                <p class="mb-0 small">Exipry: <span class="fw-400">{{ dayjsDisplayDate(+user_doc.expiry_date) }}</span></p>
                            </div>
                            <div class="d-flex flex-wrap">
                                <ng-container *ngFor="let user_file of user_doc.user_files">
                                    <div class="user-doc-img-wraper ml-2">
                                        <img class="user-doc-img rounded cursor-pointer" (click)="openViewImageModal(user_file.file_url)" [src]="user_file.file_url">
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Right to Work -->
            <div class="row m-0" *ngIf="contractor_rtw_config?.enabled">
                <div class="col-1 px-0 d-flex justify-content-center pt-2"> <i (click)="openEditReviewModal(STAGELIST.rightToWork)" class="fa fa-edit fw-500 cursor-pointer"></i> </div>
                <table class="col table no-spacing table-borderless mt-1 mb-0">
                    <tbody>
                    <tr> <td class="fw-500 h5 pl-0 pb-3">Right to Work</td> </tr>
                    <tr>
                        <ng-container *ngIf="induction_request._has_rtw_doc_code === 'no'">
                            <td class="pb-3 pl-0 pr-3">Do you have a valid PPAC ID?</td>
                            <td class="text-right pb-3 pl-0"><span class="fw-500 line-height-min">No</span></td>
                        </ng-container>
                        <td class="d-flex flex-column pl-0" *ngIf="induction_request._has_rtw_doc_code === 'yes'">
                            <ppac-status-card
                                    [showInput]="false"
                                    [onReviewStep]="true"
                                    [authUser$]="authUser$"
                                    [induction_phrase]="project.custom_field.induction_phrase_singlr"
                                    [parent_company]="project.parent_company"
                                    [contractor_rtw_config]="contractor_rtw_config"
                                    [ppac_data]="{doc_id: induction_request.rtw_doc_code, rtw_result: induction_request.rtw_check_result}"
                            ></ppac-status-card>
                        </td>
                    </tr>

                    </tbody>
                </table>
            </div>
            <div class="row m-0 mb-1" *ngIf="inductionQuestions?.status">
                <div class="col-1 px-0"></div>
                <div class="col px-0"> <hr class=""> </div>
            </div>
            <div class="row m-0" *ngIf="inductionQuestions?.status">
                <div class="col-1 px-0 d-flex justify-content-center pt-2"><i (click)="openEditReviewModal(project.custom_field.induction_phrase_singlr)" class="fa fa-edit fw-500 cursor-pointer"></i></div>
                <table class="col table no-spacing table-borderless mt-1 mb-2">
                    <tbody>
                        <tr><td class="fw-500 h5 pl-0 pb-3">{{ project.custom_field.induction_phrase_singlr }} Quiz</td></tr>
                        <tr *ngFor="let answer of induction_request.induction_answers; let i = index;">
                            <!-- <span> <b>Q. {{ inductionQuestions.induction_questions[i].question }}</b> </span> <br>
                            <span> <b>A.</b> {{answer}}</span> -->
                            <td class="pb-3 pl-0 pr-3"> {{ inductionQuestions.induction_questions[i].question }} </td>
                            <td class="pb-3 pl-0 text-capitalize text-right fw-500"> <b> {{answer}} </b></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="row m-0 mb-1" *ngIf="induction_request.declarations?.supervisor && induction_request.declarations.supervisor.length">
                <div class="col-1 px-0"></div>
                <div class="col px-0"> <hr class=""> </div>
            </div>
            <div class="row m-0" *ngIf="induction_request.declarations?.supervisor && induction_request.declarations.supervisor.length">
                <div class="col-1 px-0 d-flex justify-content-center pt-2"><i (click)="openEditReviewModal(this.SUPERVISOR_STAGE.name)" class="fa fa-edit fw-500 cursor-pointer"></i></div>
                <table class="col table no-spacing table-borderless mt-1 mb-2">
                    <tbody>
                    <tr><td class="fw-500 h5 pl-0 pb-3">{{ this.SUPERVISOR_STAGE.name }}</td></tr>
                    <tr *ngFor="let accepted of induction_request.declarations.supervisor; let i = index;">
                        <td class="pb-3 pl-0 pr-3">{{ (i+1)+'.' }} {{ accepted.question }}</td>
                        <td class="pb-3">
                            <img style="max-width: 18px;" src="data:image/png;base64,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" />
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="row m-0 mb-1" *ngIf="induction_request.declarations?.operator && induction_request.declarations.operator.length">
                <div class="col-1 px-0"></div>
                <div class="col px-0"> <hr class=""> </div>
            </div>
            <div class="row m-0" *ngIf="induction_request.declarations?.operator && induction_request.declarations.operator.length">
                <div class="col-1 px-0 d-flex justify-content-center pt-2"><i (click)="openEditReviewModal(this.OPERATOR_STAGE.name)" class="fa fa-edit fw-500 cursor-pointer"></i></div>
                <table class="col table no-spacing table-borderless mt-1 mb-2">
                    <tbody>
                    <tr><td class="fw-500 h5 pl-0 pb-3">{{ this.OPERATOR_STAGE.name }}</td></tr>
                    <tr *ngFor="let accepted of induction_request.declarations.operator; let i = index;">
                        <td class="pb-3 pl-0 pr-3" [attr.colspan]="(accepted.type === 'radio') ? 11 : 7">{{ (i+1)+'.' }} {{ accepted.question }}</td>
                        <td [attr.colspan]="(accepted.type === 'radio') ? undefined : 5" [style.width.%]="(accepted.type == 'radio') ? 10 : 40"
                            class="text-right pb-3 pl-0" [ngClass]="{'text-capitalize': (accepted.type == 'radio')}">
                            <span class="fw-500 line-height-min">{{ accepted.answer }}</span>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="row m-0 mb-1" *ngIf="induction_rams.length && selectedRams.id">
                <div class="col-1 px-0"></div>
                <div class="col px-0"> <hr class=""> </div>
            </div>
            <!-- Induction RAMS -->
            <div class="row m-0" *ngIf="induction_rams.length && selectedRams.id">
                <div class="col-1 px-0 d-flex justify-content-center pt-2"> <!-- <i (click)="openEditReviewModal(ramsPhrase)" class="fa fa-edit fw-500 cursor-pointer"></i> --> </div>
                <table class="col table no-spacing table-borderless mt-1 mb-2">
                    <tbody>
                    <tr><td class="fw-500 h5 pl-0 pb-3">{{ ramsPhrase }}</td></tr>
                    <tr>
                        <td class="pl-0">{{ selectedRams.briefing_title }}: <a [href]="selectedRams.briefing_file_ref.file_url" target="_blank">{{ selectedRams.briefing_file_ref.name }}</a></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="row m-0 mb-1" *ngIf="induction_rams.length && selectedRams.id">
                <div class="col-1 px-0"></div>
                <div class="col px-0"> <hr class=""> </div>
            </div>
            <div class="row m-0">
                <div class="col-1 px-0 pt-2"></div>
                <table class="col table no-spacing table-borderless mt-1 mb-2">
                    <tbody>
                    <tr><td class="fw-500 h5 pl-0 pb-3">Declarations</td></tr>
                    <tr *ngFor="let accepted of induction_request.accepted_declarations; let i = index;">
                        <td class="pb-3 pl-0 pr-3">
                            {{ (i+1)+'.' }} {{ find(project.declarations, accepted).content }}
                        </td>
                        <td class="pb-3">
                            <img style="max-width: 18px;" src="data:image/png;base64,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" />
                        </td>
                    </tr>
                    <tr>
                        <td class="pb-3 pl-0 pr-3">
                            {{ (induction_request.accepted_declarations.length + 1)+'.' }}
                            {{ DEFAULT_DECLARATION }}
                        </td>
                        <td class="pb-3">
                            <img style="max-width: 18px;" src="data:image/png;base64,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" />
                        </td>
                    </tr>
                    <tr *ngIf="induction_request.accepting_media_declaration">
                        <td class="pb-3 pl-0 pr-3">
                            {{ (induction_request.accepted_declarations.length + 2)+'.' }}
                            {{ project.media_declaration_content }}
                        </td>
                        <td class="pb-3">
                            <img style="max-width: 18px;" src="data:image/png;base64,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" />
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="row m-0">
                <!-- <label class="font-weight-bold col-sm-12 col-form-label form-control-label">Signature:</label> -->
                <div class="col-1 px-0 pt-2"></div>
                <div class="col-11 px-0 form-inline">
                    <signature-pad-selector class="w-100" *ngIf="!isNotThisStage('Review') && !induction_request.user_sign"
                        [height]="130"
                        [width]="700"
                        (signChanged)="saveSignature($event)"
                        (clear)="clearSignature()"
                    ></signature-pad-selector>
                    <div *ngIf="induction_request.user_sign" class="img-file-wrap">
                        <span (click)="clearSignature()" type="button" class="close-icon text-white delete" title="Clear Signature">
                            <span class="bg-danger" aria-hidden="true">×</span>
                        </span>
                        <img [src]="induction_request.user_sign" alt="signature">
                    </div>
                </div>
            </div>
            <hr class="my-1" *ngIf="induction_request.comments && induction_request.comments.length" />
        </div>
        <project-comments
                [projectComments]="induction_request.comments"
                [currentUser]="authUser$"
        ></project-comments>
    </form>
    <div class="row m-0">
        <div class="col-1 px-0"></div>
        <div class="col d-flex align-self-center justify-content-between mt-5 px-0">
            <button type="button" *ngIf="activeStage" class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="movePrev()">Back</button>
            <button type="submit" *ngIf="activeStage === formStages.length - 1" [disabled]="(!signImageIsValid() || slotRequestInProgress)"
                class="btn btn-brandeis-blue ml-2 w-50" (click)="inductionSlotPicker()">Submit</button>
        </div>
    </div>
</ng-template>

<ng-template #cscsRequiredModal let-modal let-d="dismiss">
    <div class="modal-body p-4">
        <!-- To submit your {{additional_data.project.custom_field.induction_phrase_singlr}} please include the following: -->
        <div class="mb-3 text-center">
            <span class="font-weight-bold">Competency/Certification Requirement</span><br>
        </div>
        <div class="mb-3 text-center">
            <span>To submit your induction please include the following:</span><br>
        </div>
        <ng-container *ngFor="let str of missingDocumentMessages let i=index">
            <div class="d-flex align-items-start justify-content-center" [ngClass]="{'mb-3': (missingDocumentMessages.length - 1) !== i}">
                <ng-container  *ngIf="missingDocumentMessages && missingDocumentMessages.length">
                    <i class="fa fa-circle mr-2 pt-2" style="font-size: 5px;" aria-hidden="true"></i>
                    <p class="d-flex align-items-center text-center mb-0"> {{ str }} </p>
                </ng-container>
            </div>
        </ng-container>
        <!-- <ul class="mb-0">
            <ng-container  *ngIf="missingDocumentMessages && missingDocumentMessages.length">
                <li *ngFor="let str of missingDocumentMessages">{{ str }}</li>
            </ng-container>
        </ul> -->
    </div>
    <div class="modal-footer row mx-0">
        <div (click)="d()" class="text-center d-flex justify-content-center align-items-center cursor-pointer w-100 h-100 m-0">
            <span class="fw-500 line-height-min">OK</span>
        </div>
    </div>
</ng-template>

<i-modal #chooseInductionSlotModal title="Induction Booking" size="md" [showCancel]="false" (onCancel)="closeInductionSlotSelection(inductionBookingForm)" (onClickRightPB)="submitInductionSlotSelection($event)" rightPrimaryBtnTxt="Submit" 
    [rightPrimaryBtnDisabled]="inductionBookingForm.invalid">
    <div class="row mx-auto">
        <form novalidate #inductionBookingForm="ngForm" class="w-100">
            <ng-container *ngIf="showChooseInductionSlotModal">
                <div class="form-group row px-3">
                    <label class="col-form-label">Please use the dropdown list below to choose a date and time for your on-site
                        {{additional_data.project.custom_field.induction_phrase_singlr}}.</label>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 col-form-label">Date:</label>
                    <div class="col-sm-10">
                        <select [(ngModel)]="induction_slot_selection.day" (ngModelChange)="slotDayChanged()" name="selected_day"
                            required class="form-control">
                            <option *ngFor="let o of induction_slots_days" [disabled]="o.all_booked"
                                [class.disabled-option]="o.all_booked" [ngValue]="o.day"> {{ o.day }} <i *ngIf="o.all_booked">(Fully
                                    Booked)</i></option>
                        </select>
                    </div>
                </div>
                <div class="form-group row" *ngIf="induction_slot_selection.day">
                    <label class="col-sm-2 col-form-label">Time:</label>
                    <div class="col-sm-10">
                        <select [(ngModel)]="induction_slot_selection.slot" name="selected_time" required class="form-control">
                            <option [value]="null" disabled>Select a time</option>
                            <option *ngFor="let s of induction_slots[induction_slot_selection.day]"
                                [ngValue]="s" [disabled]="s.all_booked || s.is_slot_blocked" [class.disabled-option]="s.all_booked">
                                {{ s.time }} ({{ s.location}})</option>
                        </select>
                    </div>
                </div>
                <small class="form-text text-muted mb-3" *ngIf="induction_slot_selection.slot">You are about to book your induction slot on <b>{{displaySecondsAsDateTime(induction_slot_selection.slot.seconds)}}</b> at {{induction_slot_selection.slot.location}}.</small>
                <pre class="d-none">{{induction_slot_selection | json }}</pre>
            </ng-container>
        </form>
        <block-loader [show]="(slotRequestInProgress)" alwaysInCenter="true"></block-loader>
    </div>
</i-modal>

<pdf-flip-book #pdfFlipBookComponentRef></pdf-flip-book>

<ng-template #medicationDetailModal let-c="close" let-d="dismiss">
    <div class="modal-header">
        <span class="modal-title">{{ isEditMedication ? "Edit" : "Add" }} Medication</span>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span type="button" class="close-icon small">
                <span aria-hidden="true" style="padding: 1px 7px;">×</span>
            </span>
        </button>
    </div>
    <div class="modal-body p-0">
        <medication-detail-component
            [initialValue]="selectedMedicationDetails"
            [isDisabled]="false"
            [borderAround]="false"
            [showSaveButton]="true"
            [datepickerPlacement]="'top'"
            (cancel)="closeModal(true)"
            (store)="updateMedicationRow($event)"
            (finalSave)="saveMedication($event)"
            (isInvalid)="updateValidationStatus($event)">
        </medication-detail-component>
    </div>
</ng-template>

<block-loader [show]="(processingLoader || saveInProgress)" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>

<ng-template #competenciesModal let-modal let-d="dismiss">
    <div class="modal-header">
        <span class="modal-title">{{ isEditCompetency ? selectedCompetencie.name : isChildOfCompetency ? 'Add New Sentinel Competency' : 'Add New Competency' }}</span>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span type="button" class="close-icon small">
                <span aria-hidden="true" style="padding: 1px 7px;">×</span>
            </span>
        </button>
    </div>
    <div class="modal-body competencies-modal p-0">
        <ng-container *ngIf="!isChildOfCompetency; else clildCompetencies">
            <user-doc-uploader
                [params]="selectedCompetencie"
                [knownCompetencies]="knownCompetencies"
                [userInfo]="authUser$"
                [my_docs]="user_documents"
                (editExistingDocument)="editExistingDocument($event, false)"
                [isAutoUpload]="false"
                [newCompetenciesUI]="true"
                [allowAddMore]="true"
                [isScrollable]="true"
                [cscs_config]="contractor_cscs_config"
                [isAdditionalImageMandatory]="false"
                (save)="onSaveUserDocument($event);"
                (modalDismissAll)="closeModal(true)">
            </user-doc-uploader>
        </ng-container>
        <ng-template #clildCompetencies>
            <user-doc-uploader
                [params]="childCompetency"
                [childOfCompetency]="selectedCompetencie"
                [knownCompetencies]="nonGroupKnownCompetencies"
                [userInfo]="authUser$"
                [my_docs]="user_documents"
                (editExistingDocument)="closeModal(true)"
                [isAutoUpload]="false"
                [newCompetenciesUI]="true"
                [allowAddMore]="true"
                [isScrollable]="true"
                [cscs_config]="contractor_cscs_config"
                (save)="onSaveChildDocument($event)"
                (delete)="onDeleteChildDocument($event)"
                (hasUnsaved)="onHasUnsavedChanges($event)"
                (modalDismissAll)="closeModal(true)">
            </user-doc-uploader>
        </ng-template>
    </div>
</ng-template>

<ng-template #viewImageModal let-c="close" let-d="dismiss">
    <div class="modal-body p-0">
        <span (click)="d('Cross click')" class="close-icon btn-cross" type="button" title="Close">
            <span aria-hidden="true">×</span>
        </span>
        <div class="row m-0">
            <div class="col-sm-12 text-center pl-0 pr-0">
                <div class="picsum-img-wrapper">
                    <img [src]="selectedUserDocFile" class="img-cover" />
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-brandeis-blue btn-sm px-4" (click)="d('Cross click')">Ok</button>
    </div>
</ng-template>

<ng-template #editReviewModal let-modal let-d="dismiss">
    <div class="modal-header">
        <span class="modal-title">Edit {{ editStep }}</span>
        <button type="button" class="close" aria-label="Close" (click)="triggerClick()">
            <span type="button" class="close-icon small">
                <span aria-hidden="true" style="padding: 1px 7px;">×</span>
            </span>
        </button>
    </div>
    <div class="modal-body edit-modal-body p-0">
        <div *ngIf="editStep === STAGELIST.rightToWork">
            <ng-container *ngTemplateOutlet="rightToWorkTemplate"></ng-container>
        </div>
        <div *ngIf="editStep === STAGELIST.travel">
            <ng-container *ngTemplateOutlet="travelFormTemplate"></ng-container>
        </div>
        <div *ngIf="editStep === STAGELIST.safetyAssessment">
            <ng-container *ngTemplateOutlet="safAssessFormTemplate"></ng-container>
        </div>
        <div *ngIf="editStep === STAGELIST.healthAssessment">
            <ng-container *ngTemplateOutlet="healthAssessFormTemplate"></ng-container>
        </div>
        <div *ngIf="editStep === STAGELIST.competencies">
            <ng-container *ngTemplateOutlet="competenciesFormTemplate"></ng-container>
        </div>
        <div *ngIf="editStep === additionalSectionTitle">
            <ng-container *ngTemplateOutlet="additionalSectionFormTemplate"></ng-container>
        </div>
        <div *ngIf="editStep === ramsPhrase">
            <ng-container *ngTemplateOutlet="ramsFormTemplate"></ng-container>
        </div>
        <div *ngIf="editStep === project.custom_field.induction_phrase_singlr">
            <ng-container *ngTemplateOutlet="projectMediaFormTemplate"></ng-container>
        </div>
        <div *ngIf="editStep === SUPERVISOR_STAGE.name">
            <ng-container *ngTemplateOutlet="supervisorDecFormTemplate"></ng-container>
        </div>
        <div *ngIf="editStep === OPERATOR_STAGE.name">
            <ng-container *ngTemplateOutlet="operatorDecFormTemplate"></ng-container>
        </div>
    </div>
</ng-template>

<ng-template #warningModal let-modal let-d="dismiss">
    <div class="modal-body p-4">
        <p class="text-center mb-0 h6">
            <small>
                Oops! It seems there are some errors in your form.<span class="fw-500">Please review your inputs and make sure all required fields are filled correctly before submitting.</span>
            </small>
        </p>
    </div>
    <div class="modal-footer justify-content-center cursor-pointer" (click)="d(false)" style="height: unset;">
        <span class="fw-500">Ok</span>
    </div>
</ng-template>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

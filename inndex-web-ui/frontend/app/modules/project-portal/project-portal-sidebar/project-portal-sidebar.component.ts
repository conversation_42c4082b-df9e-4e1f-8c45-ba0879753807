import {Component, EventEmitter, Input, OnChanges, OnInit, Output} from '@angular/core';
import {Router, ActivatedRoute} from '@angular/router';
import {ProjectService, AuthService, User, UACProjectDesignations, HttpService, PermissionUtility, OPERATIONS, BioMetricSettingResponse} from "@app/core";
import {Observable} from "rxjs";
import { NavigationAccess } from '@app/core/models/user-access.model';
import { AssetsUrl } from '@app/shared/assets-urls/assets-urls';

@Component({
    selector: 'project-portal-sidebar',
    templateUrl: './project-portal-sidebar.component.html',
    styleUrls: ['./project-portal-sidebar.component.scss']
})
export class ProjectPortalSidebarComponent implements OnInit {
    is_mobile_nav: boolean = false;
    is_drawer_open: boolean = false;
    authUser$: any;
    hasAccessRights: boolean = true;
    submenus ={
        dashboard:[
            { title:"Live", url:"live" },
            { title:"Project", url:"dashboard"},
        ],
        timeManagementSubmenus :[
            {title:"Members",url:"members"},
            {title:"All Events",url:"all-events"},
            {title:"Calendar",url:"calendar"},
            {title:"Fatigue",url:"fatigue"},
            {title:"Planner",url:"planner"},
            {title:"Vehicles",url:"vehicles"},
            {title:"Download Records",url:"download-records"},
            {title:"Completed Roll Calls",url:"rollcall-records"},
            {title:"Timesheets",url:"timesheets"},
        ],
        progressPhotosSubmenus : [
            {title:"Timeline",url:"timeline",},
            {title:"Submissions",url:"submissions"},
            {title:"Albums",url:"albums"}
        ],
        assetSubmenu : [
            {title:"Vehicles",url:"vehicles",},
            {title:"Equipment",url:"equipment"},
            {title:"Temporary Works",url:"temporary-works"}
        ],
    };

    openSubmenu:string = '';
    sidebarIcons = AssetsUrl.sidebarIcons;
    constructor(
        private activatedRoute: ActivatedRoute,
        private router: Router,
        private authService: AuthService,
        private permissionUtility: PermissionUtility,
        private projectService: ProjectService,
        private httpService: HttpService,
    ) {
        this.is_mobile_nav = this.httpService.isMobileDevice();
    }

    routeRelation: any = {
        "assets": "assets",
        "progress-photo": "progress-photo",
        "live": "dashboard",
        "dashboard": "dashboard",
        "time-management": "time-management"
    };

    project: Observable<{}>;

    @Input()
    projectInfo: any = {};
    @Input()
    contractorInfo: any = {};
    @Input()
    biometricMeta:BioMetricSettingResponse;
    projectId: number;
    navAccess: NavigationAccess = new NavigationAccess;//  = JSON.parse(localStorage.getItem('projectAcess')) ===null ? new NavigationAccess : JSON.parse(localStorage.getItem('projectAcess'));
    @Output()
    isSidebarLoading = new EventEmitter<any>(true);
    @Output()
    onProjectInfo: any = new EventEmitter<any>();
    sidebarInProgress: boolean = true;

    ngOnInit() {
        this.openSubmenu = this.isChildRoute();
        this.activatedRoute.params.subscribe(params => {
            this.projectId = +params['projectId'] || null;
        });

        this.authService.authUser.subscribe( (user:User) => {
            if(user && user.id) {
                this.authUser$ = user;
            }
        });

            let data = {project: this.projectInfo};
            if(data.project && data.project.id) {
                this.projectInfo = data.project;
                if (data.project._my_designations && data.project._my_designations.length === 1 && data.project._my_designations.includes(UACProjectDesignations.DELIVERY_MANAGER)) {
                    this.hasAccessRights = this.authService.isProjectAdminV1(this.authUser$, this.projectInfo); //default false
                }

                this.navAccess.induction = this.getAccessControl(data, 'induction');
                if (data.project._my_designations && data.project._my_designations.includes(UACProjectDesignations.RESTRICTED)) {
                    this.navAccess.induction = false;
                }

                if (data.project.delivery_management_status && data.project._my_access_id && this.getAccessControl(data, 'transport_management')) {
                    this.navAccess.hasDeliveryManagment = true;
                }

                if (data.project.project_section_access && Object.keys(data.project.project_section_access).length) {
                    //Satyam @todo: Find a better way of assigning access variables instead just duplicating same code over.
                    let sectnAccess = data.project.project_section_access;
                    this.navAccess.hasDashboards = sectnAccess.dashboards && this.getAccessControl(data, 'dashboards');
                    this.navAccess.hasCloseCall = sectnAccess.close_calls && this.getAccessControl(data, 'close_calls');
                    this.navAccess.hasTake5 = sectnAccess.take_5s && this.getAccessControl(data, 'take_5s');
                    this.navAccess.hasToolboxTalks = sectnAccess.toolbox_talks && this.getAccessControl(data, 'toolbox_talks');
                    this.navAccess.hasProgressPhotos = sectnAccess.progress_photos && this.getAccessControl(data, 'progress_photos');
                    this.navAccess.hasCollectionNotes = sectnAccess.collection_notes && this.getAccessControl(data, 'collection_notes');
                    this.navAccess.hasDeliveryNotes = sectnAccess.delivery_notes && this.getAccessControl(data, 'delivery_notes');
                    this.navAccess.hasIncidentReport = sectnAccess.incident_report && this.getAccessControl(data, 'incident_report');
                    this.navAccess.hasGoodCalls = sectnAccess.good_calls && this.getAccessControl(data, 'good_calls');
                    this.navAccess.hasObservations = sectnAccess.observations && this.getAccessControl(data, 'observations');
                    this.navAccess.hasSiteMessaging = sectnAccess.site_messaging && this.getAccessControl(data, 'site_messaging');
                    this.navAccess.hasTimeManagement = sectnAccess.time_management && this.getAccessControl(data, 'time_management');
                    this.navAccess.hasClerkOfWorks = sectnAccess.clerk_of_works && this.getAccessControl(data, 'clerk_of_works');
                    this.navAccess.hasTaskBriefings  = sectnAccess.task_briefings && this.getAccessControl(data, 'task_briefings');
                    this.navAccess.hasWorkPackagePlan = sectnAccess.work_package_plan && this.getAccessControl(data, 'work_package_plan');
                    this.navAccess.hasRams = sectnAccess.rams && this.getAccessControl(data, 'rams');
                    this.navAccess.hasQualityChecklist = sectnAccess.quality_checklist && this.getAccessControl(data, 'quality_checklist');
                    this.navAccess.hasDailyActivities = sectnAccess.daily_activities && this.getAccessControl(data, 'daily_activities');
                    this.navAccess.hasInspections = (
                            (sectnAccess.inspection_tour || sectnAccess.ib_checklist)
                            && this.getAccessControl(data, 'inspections')
                        ) ? true : false;
                    this.navAccess.hasInspectionTour = sectnAccess.inspection_tour && this.getAccessControl(data, 'inspections');
                    this.navAccess.hasPowra = sectnAccess.powra && this.getAccessControl(data, 'powra');
                    this.navAccess.hasAssets = (sectnAccess.asset_vehicles || sectnAccess.asset_equipment || sectnAccess.asset_temporary_works ) && this.getAccessControl(data, 'assets') ? true : false;
                    this.navAccess.hasPermits = sectnAccess.permit && this.getAccessControl(data, 'permit');
                    this.navAccess.hasDocuments = sectnAccess.documents_tool && this.getAccessControl(data, 'documents');
                }
                this.navAccess.cc_phrase = data.project.custom_field.cc_phrase;
                this.navAccess.cow_phrase = data.project.cow_setting.cow_phrase;
                this.navAccess.qcl_phrase = data.project.custom_field.qcl_phrase;
                this.navAccess.tb_phrase = data.project.custom_field.tb_phrase;
                this.navAccess.wpp_phrase = data.project.custom_field.wpp_phrase;
                this.navAccess.rams_phrase = data.project.custom_field.rams_phrase;
                this.navAccess.take5_phrase = data.project.custom_field.take5_phrase;
                this.navAccess.da_phrase = data.project.custom_field.da_phrase;
                this.navAccess.cn_phrase = data.project.custom_field.cn_phrase;
                this.navAccess.dn_phrase = data.project.custom_field.dn_phrase;
                this.navAccess.gc_phrase = data.project.custom_field.gc_phrase;
                this.navAccess.obrs_phrase = data.project.custom_field.obrs_phrase;
                this.navAccess.powra_phrase = data.project.custom_field.powra_phrase;
                this.navAccess.induction_phrase = data.project.custom_field.induction_phrase;
                this.navAccess.assets_phrase = data.project.custom_field.assets_phrase;
                this.navAccess.permit_phrase = data.project.custom_field.permit_phrase;
                this.navAccess.doc_phrase = data.project.custom_field.doc_phrase || 'Documents';

                this.onProjectInfo.emit(data.project);
            }
            this.isSidebarLoading.emit(false);
            this.sidebarInProgress = false;
            setTimeout(() => {
                let element = document.querySelector('.active-route');
                if (element) {element.scrollIntoView({block: "center", inline: "nearest"});}
                this.toggleDrawer();
            }, 0);
            this.checkTimeManagementPermission();
            this.checkAssetRenderingPermission();
    }

    getAccessControl(data, feature){
        if(!data.project._my_designations.includes(UACProjectDesignations.CUSTOM)) {
            return true;
        }
        let permission = data.project._my_permission || [];
        return this.permissionUtility.isAllowedFor(permission, feature, OPERATIONS.LIST)
    }

    isChildRoute() {
        let urlParts = (this.router.url || '').split('/');
        if(urlParts.length && urlParts[4] && Object.keys(this.routeRelation).includes(urlParts[4])) {
            return this.routeRelation[urlParts[4]];
        }
    }
    checkTimeManagementPermission(){
        let  blockedUrls=[];
        if (!this.projectInfo.project_section_access.timesheet) {
            blockedUrls.push("timesheets");
        }
        if(!this.projectInfo.fatigue_management_status){
            blockedUrls.push("fatigue")
        }
        if(!this.projectInfo.project_section_access.resource_planner){
            blockedUrls.push("planner");

        }
        if(!this.biometricMeta.has_vehicle_clocking){
            blockedUrls.push("vehicles");

       }
        this.submenus.timeManagementSubmenus=this.submenus.timeManagementSubmenus.filter(a=> !blockedUrls.includes(a.url));
    }

    checkAssetRenderingPermission(){
        let blockedUrls = []
        if (!this.projectInfo.project_section_access.asset_vehicles){
            blockedUrls.push('vehicles')
        }
        if (!this.projectInfo.project_section_access.asset_equipment){
            blockedUrls.push('equipment')
        }
        if (!this.projectInfo.project_section_access.asset_temporary_works){
            blockedUrls.push('temporary-works');
        }
        this.submenus.assetSubmenu=this.submenus.assetSubmenu.filter(a=> !blockedUrls.includes(a.url))
    }

    isSubmenuOpened(menu=''){
        return (this.openSubmenu === menu);
    }

    toggleDropdown(menu = ""){
        if(this.openSubmenu == menu){
            this.openSubmenu = "";
        } else {
            this.openSubmenu = menu;
            if(menu == ""){return};
            //added timeout to allow assets submenu to be visible fully in viewport when dynamical height change for submenu open is completed
            setTimeout(()=>{
                let elem: HTMLElement = <HTMLElement>document.querySelectorAll(`#${menu} ul li:last-child`)[0];
                elem && elem.scrollIntoView({ behavior: 'smooth', block: 'center'});
            }, 150);// timeout value set in pairing with transition value of sidebar tool opening.
        }
    }

    toggleDrawer(){
        let menuItems = document.querySelectorAll('.list-sidebar li');
        for (let i = 0; i < menuItems.length; i++) {
            menuItems[i].addEventListener('click', event => this.closeSidebar(event, menuItems[i]));
            
        }
    }
    closeSidebar(event, root){
        let subMenuItems = root.querySelectorAll('li');
        if(subMenuItems.length){
            return false;
        }
        this.is_drawer_open = !this.is_drawer_open;
    }
}

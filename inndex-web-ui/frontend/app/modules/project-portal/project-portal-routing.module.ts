import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import {ProjectPortalComponent} from "@app/modules/project-portal";
import {
    AddNewProjectComponent,
    DeliveryManagementComponent,
    LiveDashboardComponent,
    ProjectDashboardComponent,
    ProjectSettingComponent,
    Take5sComponent,
    TimeManagementHomeComponent,
    ViewProjectComponent,
} from "@app/modules/site-admin";
import {SiteAdminAuthGuard, SiteAdminProjectResolver, ValidateRouteAccessResolver} from "@app/core";
import {
    AssetsComponent,
    ClerkofWorksComponent,
    CloseCallComponent,
    DailyActivitiesComponent,
    DeliveryNotesComponent,
    GoodCallComponent,
    IncidentReportComponent,
    InspectionsComponent,
    ProgressPhotosComponent,
    ProjectRamsComponent,
    QualityChecklistsComponent,
    SiteMessagingComponent,
    TaskBriefingsComponent,
    ToolboxTalksComponent,
    PermitRequestComponent,
    WorkPackagePlanComponent
} from "@app/modules/common";
import {ProjectPowraComponent} from "@app/modules/company";
import { DocumentsComponent } from '@app/modules/documents';

let dataVariable = {
    is_project_portal: true
}
export const routes: Routes = [
    {
        path: '',
        component: ProjectPortalComponent, // this is the component with the <router-outlet> in the template
        data: {toolkey: 'parent', is_project_portal: true, attach_optima: true},    //Adding additional key to identify if route is project portal or not
        resolve:{ projectResolverResponse : SiteAdminProjectResolver },
        children: [
            {
                path: 'live', // child route path
                component: LiveDashboardComponent,
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'dashboards', ...dataVariable}
            },
            {
                path: 'live-tv', // child route path
                component: LiveDashboardComponent,
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'dashboards', isLiveTv: true}
            },
            {
                path: 'dashboard',
                component: ProjectDashboardComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'dashboards', ...dataVariable}
            },
            {
                path: 'induction',
                component: ViewProjectComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ projectResolverResponse : SiteAdminProjectResolver, valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'induction', ...dataVariable, project_args: {expand_gates: false, include_admins: false, from_ir: true}}
            },
            {
                path: 'time-management/:view',
                component: TimeManagementHomeComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ projectResolverResponse : SiteAdminProjectResolver, valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'time_management', ...dataVariable}
            },
            {
                path: 'site-messaging',
                component: SiteMessagingComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'site_messaging', ...dataVariable}
            },
            {
                path: 'good-call',
                component: GoodCallComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'good_calls', ...dataVariable}
            },
            {
                path: 'observation',
                component: GoodCallComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'observations', ...dataVariable}
            },
            {
                path: 'close-call',
                component: CloseCallComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'close_calls', ...dataVariable}
            },
            {
                path: 'take5s',
                component: Take5sComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'take_5s', ...dataVariable}
            },
            {
                path: 'delivery-management',
                component: DeliveryManagementComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'transport_management', ...dataVariable}
            },
            {
                path: 'toolbox-talk',
                component: ToolboxTalksComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'toolbox_talks', ...dataVariable}
            },
            {
                path: 'permit',
                component: PermitRequestComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'permit', ...dataVariable}
            },
            {
                path: 'progress-photo/:view',
                component: ProgressPhotosComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'progress_photos', ...dataVariable}
            },
            {
                path: 'delivery-note',
                component: DeliveryNotesComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'delivery_notes', ...dataVariable}
            },
            {
                path: 'collection-note',
                component: DeliveryNotesComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'collection_note', ...dataVariable}
            },
            {
                path: 'incident-report',
                component: IncidentReportComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'incident_report', ...dataVariable}
            },
            {
                path: 'inspection',
                component: InspectionsComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'inspections', ...dataVariable}
            },
            {
                path: 'clerk-of-work',
                component: ClerkofWorksComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'clerk_of_works', ...dataVariable}
            },
            {
                path: 'powra',
                component: ProjectPowraComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'powra', ...dataVariable}
            },
            {
                path: 'task-briefing',
                component: TaskBriefingsComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'task_briefings', ...dataVariable}
            },
            {
                path: 'rams',
                component: ProjectRamsComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'rams', ...dataVariable}
            },
            {
                path: 'work-package-plan',
                component: WorkPackagePlanComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'work_package_plan', ...dataVariable}
            },
            {
                path: 'daily-activity',
                component: DailyActivitiesComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'daily_activities', ...dataVariable}
            },
            {
                path: 'assets/:view',
                component: AssetsComponent,
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'assets', ...dataVariable}
            },
            {
                path: 'quality-checklist',
                component: QualityChecklistsComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'quality_checklist', ...dataVariable}
            },
            {
                path: 'edit',
                component: AddNewProjectComponent,
                canActivate: [SiteAdminAuthGuard],
                data: {validate: ['full_access_site_admin']},
                resolve: {valid: ValidateRouteAccessResolver}
            },
            {
                path: 'settings',
                component: ProjectSettingComponent,
                canActivate: [SiteAdminAuthGuard],
                data: {validate: ['full_access_site_admin']},
                resolve: {valid: ValidateRouteAccessResolver}
            },
            {
                path: 'documents',
                component: DocumentsComponent,
                canActivate: [SiteAdminAuthGuard],
                resolve:{ projectResolverResponse : SiteAdminProjectResolver, valid : ValidateRouteAccessResolver },
                data: {validate: ['custom_access_site_admin'], toolkey: 'documents', ...dataVariable, project_args: {expand_gates: false}}
            },
        ],
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class ProjectPortalRoutingModule {}

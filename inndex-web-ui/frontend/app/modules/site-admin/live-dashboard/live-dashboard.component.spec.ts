import {ComponentFixture, TestBed} from '@angular/core/testing';
import {LiveDashboardComponent} from '@app/modules/site-admin';
import {FormsModule} from '@angular/forms';
import {NgbModal, NgbModalConfig} from '@ng-bootstrap/ng-bootstrap';
import {
    AuthService, CookieService,
    DashboardUtil, HttpService,
    OptimaService,
    PermissionUtility,
    ProjectService,
    ReportService,
    ResourceService, ScriptLoaderService, TimeUtility,
    ToastService, UserService
} from '@app/core';
import {ActivatedRoute, Router} from '@angular/router';
import {of, throwError} from 'rxjs';
import {HttpClientTestingModule} from '@angular/common/http/testing';

describe("LiveDashboardComponent", () => {
    let component: LiveDashboardComponent;
    let fixture: ComponentFixture<LiveDashboardComponent>;
    let mockProjectService: jasmine.SpyObj<ProjectService>;
    let mockToastService:any;
    let mockActivatedRoute: any;
    let mockRouter: any;
    let mockAuthService: jasmine.SpyObj<AuthService>;



    beforeEach(async () => {
        mockProjectService = jasmine.createSpyObj('ProjectService', ['getProjectInductedUsers', 'updateProjectLiveTvFooter']);
        mockToastService = jasmine.createSpyObj('ToastService', ['show']);
        mockToastService.types = { ERROR: 'error' };
        mockAuthService = jasmine.createSpyObj('AuthService', ['isLiveTvLogin']);
        mockActivatedRoute = {
            parent: {
                params: of({ projectId: '123' }),
                snapshot: {
                    data: {
                        projectResolverResponse: {
                            project: {
                                id: 123,
                                name: 'Test Project',
                                project_section_access: {
                                    dashboards: true,
                                    time_management: false
                                }
                            }
                        },
                        is_project_portal: true,
                    }
                }
            },
            snapshot: {
                data: {
                    is_project_portal: true,
                    isLiveTv: true
                }
            },
            queryParams: of({
                full_screen: 'true',
                pi: 'false'
            })
        };

        mockRouter = jasmine.createSpyObj('Router', ['navigate', 'navigateByUrl']);
        await TestBed.configureTestingModule({
            declarations: [LiveDashboardComponent],
            imports: [FormsModule, HttpClientTestingModule],
            providers: [
                NgbModalConfig,
                NgbModal,
                ToastService,
                PermissionUtility,
                Router,
                ReportService,
                OptimaService,
                ScriptLoaderService,
                TimeUtility,
                DashboardUtil,
                HttpService,
                ResourceService,
                UserService,
                CookieService,
                {provide: Router, useValue: mockRouter},
                {provide: ActivatedRoute, useValue: mockActivatedRoute},
                { provide: ProjectService, useValue: mockProjectService },
                { provide: ToastService, useValue: mockToastService },
                {provide: AuthService, useValue: mockAuthService},
            ]
        }).compileComponents();
    });

    beforeEach(async () => {
        fixture = TestBed.createComponent(LiveDashboardComponent);
        component = fixture.componentInstance;
        spyOn<any>(component, 'checkRouteAccess').and.callThrough();
        fixture.detectChanges();
        await fixture.whenStable();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize projectId and call checkRouteAccess in constructor', () => {
        expect(component.projectId).toBe(123);
        expect(component.isProjectPortal).toBeTruthy();
        expect(component.projectInfo.name).toBe('Test Project');
        // expect((component as any).checkRouteAccess).toHaveBeenCalled();
    });

    it('should set isFullScreenMode and isPiDevice based on query params', async () => {
        component.ngOnInit();
        fixture.detectChanges();
        await fixture.whenStable();

        expect(component.isFullScreenMode).toBeTruthy();
        expect(component.isPiDevice).toBeFalsy();
    });

    it('should call onDataLoadingComplete() and set dashInfoLoading value', () => {
        const mockData = false;
        component.onDataLoadingComplete(mockData);
        expect(component.dashInfoLoading).toBeTruthy();
    });

    describe('saveProjectDesc()', () => {
        it('should call updateProjectLiveTvFooter and close the modal on success when logged in as Live TV', () => {
            (mockAuthService.isLiveTvLogin as jasmine.Spy).and.returnValue(true);
            const response = { success: true };
            (mockProjectService.updateProjectLiveTvFooter as jasmine.Spy).and.returnValue(of(response));

            component.projectInfo = { id: 1, name: 'Test Project' } as any; // mock projectInfo
            component.is_project_dOpen = true;

            component.saveProjectDesc();

            expect(mockAuthService.isLiveTvLogin).toHaveBeenCalled();
            expect(mockProjectService.updateProjectLiveTvFooter).toHaveBeenCalledWith(component.projectInfo);
            expect(component.is_project_dOpen).toBeFalsy();
        });

        it('should show error toast with fallback message when error does not have headers', () => {
            (mockAuthService.isLiveTvLogin as jasmine.Spy).and.returnValue(true);

            const errorResponse = () => {};

            (mockProjectService.updateProjectLiveTvFooter as jasmine.Spy).and.returnValue(
                throwError(() => errorResponse)
            );

            component.projectInfo = { id: 1, name: 'Test Project' } as any;

            component.saveProjectDesc();

            expect(mockToastService.show).toHaveBeenCalledWith(
                mockToastService.types.ERROR,
                'Something went wrong, please try again later.',
                { data: jasmine.any(Function) }
            );
        });
    })
})

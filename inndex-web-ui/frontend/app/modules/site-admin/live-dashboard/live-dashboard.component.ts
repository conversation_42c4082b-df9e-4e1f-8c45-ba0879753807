/**
 * Created by spatel on 24/7/19.
 */
 import {Inject, Component, ElementRef, OnInit, ViewChild, OnDestroy} from '@angular/core';
 import {DOCUMENT} from '@angular/common';
 import {
     DashboardUtil,
     OptimaService,
     ReportService,
     ScriptLoaderService,
     TimeUtility,
     AuthService,
     ToastService,
     ProjectService,
     Project,
     User,
     UACProjectDesignations
 } from "@app/core";
 import {ActivatedRoute, Router} from "@angular/router";
 import { takeUntil } from 'rxjs/operators';
import { fromEvent, Subject } from 'rxjs';
import { GenericConfirmationModalComponent } from '@app/shared';

 @Component({
     selector: 'live-dashboard',
     templateUrl: './live-dashboard.component.html',
 })
 export class LiveDashboardComponent implements OnInit, OnDestroy {

     isFullScreenMode: boolean = false;
     isPiDevice: boolean = false;
     dashInfoLoading: boolean = true;
     is_project_dOpen: boolean = false;
     projectResolverResponse: any = {};
     projectInfo: Project = new Project;
     projectId: number;
     isProjectPortal: boolean = false;
     private unSubscriber$ : Subject<void> = new Subject<void>();
     @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
     constructor(
         @Inject(DOCUMENT) private document,
         private route: ActivatedRoute,
         private router: Router,
         private toastService: ToastService,
         private reportService: ReportService,
         private optimaService: OptimaService,
         private scriptLoaderService: ScriptLoaderService,
         private timeUtility: TimeUtility,
         private dashboardUtil: DashboardUtil,
         private authService: AuthService,
         private projectService: ProjectService
     ) {
        if (this.route.snapshot.data.isLiveTv) {
            history.pushState(null, '');
            fromEvent(window, 'popstate')
                .pipe(takeUntil(this.unSubscriber$))
                .subscribe((_) => {
                    history.pushState(null, '');
                    this.confirmationModalRef.openConfirmationPopup({
                        headerTitle: 'Warning',
                        title: `This action is unavailable.`,
                        confirmLabel: 'Ok',
                        hasCancel: false,
                        onConfirm: () => {
                            this.authService.logout();
                        }
                    });
                });
        }
         this.route.parent.params.subscribe(params => {
             this.projectId = params['projectId'];
         });
         this.isProjectPortal = this.route.snapshot.data.is_project_portal || false;
         if (this.isProjectPortal) {
             this.projectResolverResponse = this.route.parent.snapshot.data.projectResolverResponse;
             this.projectInfo = this.projectResolverResponse.project;
             this.projectId = this.projectInfo.id;
             console.log("live-dash projectId: ", this.projectId);
             this.checkRouteAccess();
         }
     }

     ngOnInit(): void {
         this.route.queryParams
             .subscribe(params => {
                 this.isFullScreenMode = (params['full_screen'] === 'true') || false;
                 this.isPiDevice = (params['pi'] === 'true') || false;
             });
     }

     onDataLoadingComplete($onCompleteEvent){
         this.dashInfoLoading = !$onCompleteEvent;
     }

     ngOnDestroy(): void{
        this.unSubscriber$.next();
        this.unSubscriber$.complete();
     }

     saveProjectDesc() {
         if(this.authService.isLiveTvLogin()) {
             this.projectService.updateProjectLiveTvFooter(this.projectInfo).subscribe(
                 response => {
                     console.log(response, "response");
                     this.is_project_dOpen = false;
                 },
                 error => {
                    const message = (error.headers && error.headers.get('message')) ? error.headers.get('message') : 'Something went wrong, please try again later.';
                    this.toastService.show(this.toastService.types.ERROR, message, { data: error });
                 }
             );
         } else {
             // Below block is error pron, disabling it, till the time a fix is added.

             /*this.projectService.updateProject(this.project).subscribe(
                 response => {
                     console.log(response, "response");
                     this.is_project_dOpen = false;
                 },
                 error => {
                     console.error(`Error: `,  error);
                     alert((error.headers && error.headers.get('message')) ? error.headers.get('message') : 'Something went wrong, please try again later.');
                 }
             );*/
         }
     }

     //function for check dashboard access
    private checkRouteAccess() {
        if (this.isProjectPortal || this.route.snapshot.data.isLiveTv) {
            if (
                this.projectInfo.project_section_access &&
                Object.keys(this.projectInfo.project_section_access)
                    .length
            ) {
                if (
                    !this.projectInfo.project_section_access.dashboards
                ) {
                    /*return this.router.navigate([
                        "/project-portal/project/"+ this.projectId +"/induction",
                    ]);*/
                }
            }
        }
    }
 }

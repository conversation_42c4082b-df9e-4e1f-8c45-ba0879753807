<style>
    .fullscreen-container {
        margin: -28px auto 0;
    }
    .pi-device{
        cursor: none;
    }
    /*
    .bottom-text {
        position: absolute;
        margin-bottom: 10px;
    }

    .b-box-edit-button {
        position: absolute;
        top: 0;
        right: 0;
        display: none;
    }

    .b-box-view-mode:hover>button {
        display: block !important;
    }*/
</style>
<div
    [ngClass]="{'pl-4 pr-4 col-12': !isFullScreenMode, 'col-md-12 col-lg-12 px-4': isFullScreenMode}">
    <div [ngClass]="{'row': !isFullScreenMode, 'fullscreen-container': isFullScreenMode, 'pi-device': isPiDevice}">
        <project-live-dashboard
                [isFullScreenMode]="isFullScreenMode"
                [projectId]="projectId"
                (onDataLoadingCompleted)="onDataLoadingComplete($event)"
                class="col-12"
        ></project-live-dashboard>
    </div>
    <!-- Disabling, as it is of no use -->
    <!--
    <div *ngIf="isFullScreenMode && !dashInfoLoading" class="bottom-text container">
        <div class="row">
            <div class="col-md-12">
                <div *ngIf="is_project_dOpen" class="b-box-edit-mode">
                    <textarea [(ngModel)]="projectInfo.project_footer" placeholder="Add some content here..."
                        class="form-control" type="text"></textarea>
                    <div class="text-right pt-2">
                        <button (click)="saveProjectDesc()" class="btn btn-primary mr-2"><i class="fa fa-check"
                                aria-hidden="true"></i></button>
                        <button (click)="is_project_dOpen = !is_project_dOpen" class="btn btn-danger"><i
                                class="fa fa-times" aria-hidden="true"></i></button>
                    </div>
                </div>
                <div *ngIf="!is_project_dOpen" class="b-box-view-mode">
                    <p class="text-visible" *ngIf="projectInfo.project_footer" [innerHTML]="projectInfo.project_footer"></p>
                    <p class="text-muted" *ngIf="!projectInfo.project_footer">Add some content here...</p>
                    <button (click)="is_project_dOpen = !is_project_dOpen" *ngIf="authService.isLiveTvLogin()"
                        class="btn btn-primary pull-right b-box-edit-button"><i class="fa fa-edit"
                            aria-hidden="true"></i></button>
                </div>
            </div>
        </div>
    </div>-->
</div>

<!-- Show warning modal when user clicks on browser back button  -->
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
import {Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild} from "@angular/core";
import {DomSanitizer} from "@angular/platform-browser";
import { ScriptLoaderService, ToastService } from "@app/core";
import {GenericConfirmationModalComponent, IModalComponent} from "@app/shared";

import {
    PowerbiService
} from "@app/core";
import {ActivatedRoute} from "@angular/router";
import {ShareToolReportToEmailComponent} from "@app/modules/common";
import {saveAs} from "file-saver";

@Component({
    selector: 'power-bi-dashboard',
    templateUrl: './power-bi-dashboard.component.html',
})
export class PowerBiDashboardComponent implements OnInit {
    isProjectPortal: boolean = false;
    projectResolverResponse: any = {};
    projectInfo: any = {};
    projectId: number = null;
    employerId: number = null;
    processingLoader: boolean = false;
    @Input()
    toolName: string = '';

    @Input()
    toolLabel: string = '';

    @Input()
    toolId: number = null;

    @Input()
    portalType: string = 'project';

    @Input()
    modalTitle: string = 'Dashboard';

    @Output()
    powerBiDashboardClose = new EventEmitter<any>();

    reportEventListener: any;

    exportJobId: number = null;

    constructor(
        private scriptLoaderService: ScriptLoaderService,
        public sanitizer: DomSanitizer,
        public toastService: ToastService,
        private powerbiService: PowerbiService,
        private activatedRoute: ActivatedRoute,
    ) {
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
        } else {
            this.activatedRoute.params.subscribe(params => {
                if (params['projectId']) {
                    this.projectId = params['projectId'];
                }
                this.employerId = params['employerId'];
            });
        }
    }

    ngOnInit(): void {
        console.log('Initializing Power BI Report.');
        this.initializePowerBi();
    }

    @ViewChild('embedContainer', { static: true }) embedContainer: ElementRef;
    initializePowerBi() {
        this.processingLoader = true;
        if (this.portalType === 'project') {
            let params = {};
            if (this.toolId) {
                params = {"toolId": this.toolId}
            }
            this.powerbiService.getProjectToolReport(this.projectId, this.toolName, params).subscribe(this.responseHandler.bind(this))
        } else {
            this.powerbiService.getCompanyToolReport(this.employerId, this.toolName).subscribe(this.responseHandler.bind(this))
        }
    }

    @ViewChild('alertModal') private alertModalRef: GenericConfirmationModalComponent;
    @ViewChild('dashboardHtml') private dashboardHtmlRef: IModalComponent;
    responseHandler(out: any) {
        if (out.success && out.report_meta) {
            this.dashboardHtmlRef.open();
            let loadScript = this.scriptLoaderService.loadScript('power-bi');
            Promise.all([loadScript]).then(res => {
                let pbi = window['powerbi-client'];
                let reportMeta = out.report_meta;
                const embedConfiguration = {
                    accessToken: reportMeta.embedToken,
                    embedUrl: reportMeta.embedUrl,
                    id: reportMeta.reportId,
                    tokenType: pbi.models.TokenType.Embed,
                    type: 'report',
                    settings: {
                        filterPaneEnabled: false,
                        navContentPaneEnabled: false,
                    },
                    permissions: pbi.models.Permissions.All,
                };

                const service = new pbi.service.Service(
                    pbi.factories.hpmFactory,
                    pbi.factories.wpmpFactory,
                    pbi.factories.routerFactory
                );
                const embedContainerNativeElement = this.embedContainer.nativeElement;
                service.reset(embedContainerNativeElement);
                this.reportEventListener = service.embed(embedContainerNativeElement, embedConfiguration);

                this.reportEventListener.on("rendered", () => {
                    this.processingLoader = false;
                });

            }).catch(e=>console.log(e));
        } else if (out.error) {
            this.alertModalRef.openConfirmationPopup({
                title: out.message,
                confirmLabel: 'OK',
                hasCancel: false,
                hideHeader: true,
                onConfirm: () => {
                    this.powerBiDashboardClose.emit();
                }
            });
        } else {
            this.powerBiDashboardClose.emit();
            const message = 'Something went wrong while fetching report.';
            this.toastService.show(this.toastService.types.ERROR, message);
        }
    }

    closeDashboard(event?) {
        this.powerBiDashboardClose.emit();
        if(event) {
            event.closeFn();
        }
    }

    async initializeReportExport() {
        // Capture the current state
        let bookmarkState = await this.reportEventListener.bookmarksManager.capture();

        // Apply the captured state
        await this.reportEventListener.bookmarksManager.applyState(bookmarkState.state);

        let payload: any = {
            bookmarkState: bookmarkState.state
        }
        this.processingLoader = true;
        if (this.portalType === 'project') {
            if (this.toolId) {
                payload.toolId = this.toolId
            }
            this.powerbiService.exportProjectToolReport(this.projectId, this.toolName, payload).subscribe(this.exportReportResponseHandler.bind(this))
        } else {
            this.powerbiService.exportCompanyToolReport(this.employerId, this.toolName, payload).subscribe(this.exportReportResponseHandler.bind(this))
        }
    }


    @ViewChild('shareReportModal') shareReportModalRef: ShareToolReportToEmailComponent;
    openShareReportModal(row) {
        this.shareReportModalRef.openEmailFormModal(row);
    }

    exportReportResponseHandler(out: any) {
        this.exportJobId = null;
        this.processingLoader = false;
        if (out.success && out.report_file) {
            saveAs(out.report_file.file_url, out.report_file.filename);
        } else if (out.success && out.queue_item_id) {
            this.exportJobId = +out.queue_item_id;
            this.openShareReportModal({id: this.exportJobId});
        } else if (out.error) {
            this.alertModalRef.openConfirmationPopup({
                title: out.message,
                confirmLabel: 'OK',
                hasCancel: false,
                hideHeader: true,
                onConfirm: () => {
                    this.powerBiDashboardClose.emit();
                }
            });
        } else {
            this.powerBiDashboardClose.emit();
            const message = 'Something went wrong while fetching report.';
            this.toastService.show(this.toastService.types.ERROR, message);
        }
    }

    updateExportJob($event) {
        let request = {
            emails: $event.req.email
        };
        if (this.portalType === 'project') {
            this.powerbiService.updateProjectExportQueue(this.projectId, this.exportJobId, request).subscribe((response:any)=> {
                $event.cb(response);
            });
        } else {
            this.powerbiService.updateCompanyExportQueue(this.employerId, this.exportJobId, request).subscribe((response:any)=> {
                $event.cb(response);
            });
        }
    }
}

import {ComponentFixture, fakeAsync, flush, TestBed, tick} from '@angular/core/testing';
import { PowerBiDashboardComponent } from './power-bi-dashboard.component';
import { ScriptLoaderService, ToastService } from '@app/core';
import { PowerbiService } from '@app/core';
import { ActivatedRoute } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { of } from 'rxjs';

describe('PowerBiDashboardComponent', () => {
    let component: PowerBiDashboardComponent;
    let fixture: ComponentFixture<PowerBiDashboardComponent>;
    let powerbiService: jasmine.SpyObj<PowerbiService>;
    let scriptLoaderService: jasmine.SpyObj<ScriptLoaderService>;

    let mockToastService = jasmine.createSpyObj('ToastService', ['show']);
    mockToastService.types = { ERROR: 'error' };
    let activatedRouteMock = {
        snapshot: {
            data: {
                is_project_portal: true
            }
        },
        parent: {
            snapshot: {
                data: {
                    projectResolverResponse: {
                        project: { id: 123 }
                    }
                }
            }
        },
        params: of({ projectId: 123, employerId: 456 })
    };

    beforeEach(async () => {
        const mockServiceInstance = {
            reset: jasmine.createSpy('reset'),
            embed: jasmine.createSpy('embed'),
        };

        window['powerbi-client'] = {
            models: {
                TokenType: {
                    Embed: 'Embed'
                },
                Permissions: {
                    All: 'All'
                }
            },
            service: {
                Service: jasmine.createSpy('Service').and.returnValue(mockServiceInstance)
            },
            factories: {
                hpmFactory: jasmine.createSpy('hpmFactory'),
                wpmpFactory: jasmine.createSpy('wpmpFactory'),
                routerFactory: jasmine.createSpy('routerFactory')
            }
        };

        powerbiService = jasmine.createSpyObj('PowerbiService', ['getProjectToolReport', 'getCompanyToolReport', 'exportProjectToolReport', 'exportCompanyToolReport']);
        scriptLoaderService = jasmine.createSpyObj('ScriptLoaderService', ['loadScript']);

        await TestBed.configureTestingModule({
            declarations: [PowerBiDashboardComponent],
            providers: [
                { provide: PowerbiService, useValue: powerbiService },
                { provide: ToastService, useValue: mockToastService },
                { provide: ScriptLoaderService, useValue: scriptLoaderService },
                { provide: ActivatedRoute, useValue: activatedRouteMock },
                DomSanitizer
            ],
        }).compileComponents();
        powerbiService.getProjectToolReport.and.returnValue(of({ success: true, report_meta: {} }));
        fixture = TestBed.createComponent(PowerBiDashboardComponent);
        component = fixture.componentInstance;

        (component as any).dashboardHtmlRef = {
            open: jasmine.createSpy('open')
        } as any;

        (component as any).alertModalRef = {
            openConfirmationPopup: jasmine.createSpy('openConfirmationPopup')
        } as any;

        powerbiService.getProjectToolReport.and.returnValue(of({
            success: true,
            report_meta: {
                embedToken: 'dummy-token',
                embedUrl: 'https://dummy-url',
                reportId: 'report-id'
            }
        }));

        scriptLoaderService.loadScript.and.returnValue(Promise.resolve());
        // Mock reportEventListener and bookmarksManager
        component.reportEventListener = {
            bookmarksManager: {
                capture: jasmine.createSpy().and.returnValue(Promise.resolve({ state: 'mockState' })),
                applyState: jasmine.createSpy().and.returnValue(Promise.resolve())
            }
        };

        // Stub exportReportResponseHandler
        spyOn(component, 'exportReportResponseHandler');
        fixture.detectChanges();
    });

    it('should create component', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize with project portal route data', () => {
        expect(component.projectId).toBe(123);
        expect(component.isProjectPortal).toBeTruthy();
    });


    it('should call getCompanyToolReport on non-project portal', () => {
        component.portalType = 'company';
        component.toolName = 'company-tool';
        component.employerId = 202;

        powerbiService.getCompanyToolReport.and.returnValue(of({ success: true, report_meta: {} }));
        spyOn(component, 'responseHandler');

        component.initializePowerBi();

        expect(powerbiService.getCompanyToolReport).toHaveBeenCalledWith(202, 'company-tool');
        expect(component.responseHandler).toHaveBeenCalled();
    });

    it('should show toast and emit on unknown error', () => {
        const emitSpy = spyOn(component.powerBiDashboardClose, 'emit');
        const output = { unknown: true };

        component.responseHandler(output);

        expect(emitSpy).toHaveBeenCalled();
        expect(mockToastService.show).toHaveBeenCalledWith('error', 'Something went wrong while fetching report.');
    });

    it('should emit and call closeFn on closeDashboard', () => {
        const emitSpy = spyOn(component.powerBiDashboardClose, 'emit');
        const event = { closeFn: jasmine.createSpy('closeFn') };

        component.closeDashboard(event);

        expect(emitSpy).toHaveBeenCalled();
        expect(event.closeFn).toHaveBeenCalled();
    });

    it('should call getProjectToolReport on project portal', () => {
        component.toolId = 1;
        component.portalType = 'project';
        component.toolName = 'project-tool';
        component.projectId = 10;

        powerbiService.getProjectToolReport.and.returnValue(of({ success: true, report_meta: {} }));
        spyOn(component, 'responseHandler');

        component.initializePowerBi();

        expect(powerbiService.getProjectToolReport).toHaveBeenCalledWith(10, 'project-tool', {toolId: 1});
        expect(component.responseHandler).toHaveBeenCalled();
    })

    it('should initialize report export for project portal', fakeAsync(() => {
        component.portalType = 'project';
        component.toolId = 123;
        component.projectId = 1;
        component.toolName = 'toolA';

        const expectedPayload = {
            bookmarkState: 'mockState',
            toolId: 123
        };

        powerbiService.exportProjectToolReport.and.returnValue(of('mockProjectExport'));

        component.initializeReportExport();
        tick(); // Resolve Promises
        fixture.detectChanges();

        expect(component.reportEventListener.bookmarksManager.capture).toHaveBeenCalled();
        expect(component.reportEventListener.bookmarksManager.applyState).toHaveBeenCalledWith('mockState');
        expect(powerbiService.exportProjectToolReport).toHaveBeenCalledWith(1, 'toolA', expectedPayload);
        expect(component.exportReportResponseHandler).toHaveBeenCalledWith('mockProjectExport');
        expect(component.processingLoader).toBeTruthy();

        flush();
    }));

    it('should initialize report export for company portal', fakeAsync(() => {
        component.portalType = 'company';
        component.employerId = 1;
        component.toolName = 'toolB';

        const expectedPayload = {
            bookmarkState: 'mockState'
        };

        powerbiService.exportCompanyToolReport.and.returnValue(of('mockCompanyExport'));

        component.initializeReportExport();
        tick(); // Resolve Promises
        fixture.detectChanges();

        expect(component.reportEventListener.bookmarksManager.capture).toHaveBeenCalled();
        expect(component.reportEventListener.bookmarksManager.applyState).toHaveBeenCalledWith('mockState');
        expect(powerbiService.exportCompanyToolReport).toHaveBeenCalledWith(1, 'toolB', expectedPayload);
        expect(component.exportReportResponseHandler).toHaveBeenCalledWith('mockCompanyExport');
        expect(component.processingLoader).toBeTruthy();

        flush();
    }));
});


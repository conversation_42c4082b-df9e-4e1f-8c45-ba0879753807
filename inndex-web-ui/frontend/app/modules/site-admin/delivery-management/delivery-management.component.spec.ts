import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DeliveryManagementComponent } from './delivery-management.component';
import {NgbModalModule, NgbCalendar, NgbNavModule, NgbDateStruct, NgbDropdownModule} from '@ng-bootstrap/ng-bootstrap';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { ProjectService } from '@app/core';
import { NgbMomentjsAdapter } from '@app/core/ngb-moment-adapter';


class MockProjectService {
    getProjectInductedUsersNames(projectId, options) {
        return of({ success: true, records: [{ name: 'User1', email: '<EMAIL>' }] });
    }
}

class MockNgbCalendar {
    getToday(): NgbDateStruct {
        return { year: 2025, month: 5, day: 1 };
    }
}


describe('DeliveryManagementComponent', () => {
    let component: DeliveryManagementComponent;
    let fixture: ComponentFixture<DeliveryManagementComponent>;
    let mockActivatedRoute: any;

    mockActivatedRoute = {
        snapshot: {
            data: {
                is_project_portal: true
            }
        },
        parent: {
            snapshot: {
                data: {
                    projectResolverResponse: {
                        project: {
                            id: 123,
                            _my_designations: ['DELIVERY_MANAGER']
                        }
                    }
                }
            }
        }
    };


    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [DeliveryManagementComponent],
            imports: [NgbModalModule, NgbNavModule, NgbDropdownModule],
            providers: [
                NgbCalendar,
                NgbMomentjsAdapter,
                { provide: ActivatedRoute, useValue: mockActivatedRoute },
                { provide: ProjectService, useClass: MockProjectService },
                { provide: NgbCalendar, useClass: MockNgbCalendar },
            ],
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(DeliveryManagementComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize selectedDay to today\'s date', () => {
        const today = (component as any).calendar.getToday();
        expect(component.selectedDay).toEqual(today);
    });

    it('should load projectId from route data', () => {
        expect(component.projectId).toBe(123);
    });

    it('should call initTabs() on init', () => {
        spyOn(component, 'initTabs');
        component.ngOnInit();
        expect(component.initTabs).toHaveBeenCalled();
    });

    it('should call getProjectInductedUsers if user has multiple roles', () => {
        component.projectInfo._my_designations = ['DELIVERY_MANAGER', 'OTHER_ROLE'];
        const spy = spyOn(component, 'getProjectInductedUsers');
        component.ngOnInit();
        expect(spy).toHaveBeenCalled();
    });

    it('should set navActivetab on tabChange', () => {
        component.tabChange('tab2');
        expect(component.navActivetab).toBe('tab2');
    });

    it('should prevent nav change if nextId is 0', () => {
        const mockEvent: any = {
            nextId: 0,
            preventDefault: jasmine.createSpy('preventDefault')
        };
        component.onNavChange(mockEvent);
        expect(mockEvent.preventDefault).toHaveBeenCalled();
    });

    it('should set selectedDay in exportBookingsInit', () => {
        const newDate = { year: 2025, month: 5, day: 1 };
        component.exportBookingsInit(newDate);
        expect(component.selectedDay).toEqual(newDate);
    });

    it('should refresh tabs and reset nav tab on date selection', () => {
        component.viewBookingComponent = { onDateSelection: jasmine.createSpy('onDateSelection') } as any;
        component.onDateSelection();
        expect(component.navActivetab).toBe('tab0');
        expect(component.viewBookingComponent.onDateSelection).toHaveBeenCalledWith(component.selectedDay);
    });

    it('should fetch inducted users successfully', () => {
        component.getProjectInductedUsers();
        expect(component.project_inducted_users$?.length).toBeGreaterThan(0);
    });

    it('should return formatted date string from NgbDateStruct', () => {
        const selectedDay = { year: 2025, month: 5, day: 1 };
        const format = 'YYYY-MM-DD';

        const formattedDate = component.getDateInfo(selectedDay, format);

        expect(formattedDate).toBe('2025-05-01');
    });
});

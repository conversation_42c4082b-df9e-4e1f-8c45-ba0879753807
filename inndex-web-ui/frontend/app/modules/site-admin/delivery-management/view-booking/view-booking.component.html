<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <div class="col-12">
        <div class="row mt-2">
            <div class="col-sm-12 px-0 my-2">
                <div class="col-md-12 flex-wrap p-0 pb-3">
                    <div class="col-md-12 p-0 mt-1 d-flex justify-content-end">
                        <action-button
                            *ngIf="bookingApprovalProcessStatus else actionSelector"
                            [actionList]="actionButtonMetaData.actionList"
                            [iconText]="'list'"
                            (selectedActionEmmiter)="onActionSelection($event)"
                            [newFeatureTitle]="'Pending Bookings'"
                            (onOpenAddNew)="viewAllPendingBookingsPopup()">
                        </action-button>
                        <ng-template #actionSelector>
                            <action-button
                                [hideNewFeatureBtn]="true"
                                [actionList]="actionButtonMetaData.actionList"
                                [iconText]="'list'"
                                (selectedActionEmmiter)="onActionSelection($event)">
                            </action-button>
                        </ng-template>

                        <i-modal #gateSuperVisorModal [title]="'Gate Supervisors'" [rightPrimaryBtnTxt]="'Save'" (onClickRightPB)="saveGateSuperVisors($event)">
                            <div class="drop-menu p-0" aria-labelledby="dropdownDlReport13" [style.z-index]="1045">
                                <div class="p-4">
                                    <form #gateSupervisorForm="ngForm" class="form-container setup-content" novalidate>
                                        <div class="d-flex align-items-center row">
                                            <div class="col-11">
                                                <ng-select name="userSelector" class="w-100 dropdown-list" appendTo="body"
                                                    placeholder="Select User" [(ngModel)]="selectedUser" (change)="onChangeUser($event)" required>
                                                    <ng-option *ngFor="let user of project_inducted_users" [value]="user"
                                                    [disabled]="saved_gate_supervisor_ids.includes(user.user_ref)">{{user.name}}</ng-option>
                                                </ng-select>
                                            </div>
                                            <i class="col-1 fa fa-plus-circle float-right cursor-pointer" [ngClass]="selectedUser ? 'text-brandeis-blue' : 'text-blue-jeans'" (click)="addSupervisorRow(selectedUser)"></i>
                                        </div>
                                        <ng-container *ngFor="let item of gate_supervisors; trackBy : trackByRowIndex; let i = index">
                                            <div class="row align-items-center border-left-0 mt-3" [ngModelGroup]="'d-admin'+i">
                                                <div class="col-11">
                                                    <input class="w-100 form-control" type="text" [name]="'input-supervisor'+i"
                                                        [value]="item._name + ' (' + item._email + ')'" />
                                                </div>
                                                <div  class="col-1 d-flex align-items-end">
                                                    <span class="material-symbols-outlined cursor-pointer text-danger font-larger" (click)="removeSupervisorRow(item.id, i, item._name)">delete</span>
                                                </div>
                                            </div>
                                        </ng-container>
                                    </form>
                                </div>
                            </div>
                        </i-modal>
                    </div>
                </div>
                <div class="col-md-12">
                    <input class="form-control d-none" placeholder="dd-mm-yyyy" readonly
                           name="booking_date" [(ngModel)]="selectedDay" ngbDatepicker
                           [maxDate]="maxDate"
                           #d="ngbDatepicker" ng-value="selectedDay">
                </div>
                <div class="clearfix"></div>
                <block-loader [show]="(loadingGates && !availableGates.length)" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>

                <div class="row overflow-auto custom-booking-height" *ngIf="(!loadingGates && availableGates.length)">
                    <div *ngIf="isTimeSlotAvailable()" class="col-md-2 text-center pr-0" style="width: 16%;">
                        <table class="table table-borderless bg-white" style="table-layout: fixed;">
                            <thead>
                                <tr>
                                    <td scope="col">
                                        <div>&nbsp; </div>
                                    </td>
                                </tr>
                            </thead>
                            <tbody *ngIf="deactivatedTimeSlots.length < timeSlots.length">
                                <ng-template ngFor let-item [ngForOf]="timeSlots" let-k="index" [ngForTrackBy]="trackByRowIndex"
                                    style="font-size: 13px;">
                                    <tr [ngClass]="{'d-none': deactivatedTimeSlots.includes(timeSlots[k])}">
                                        <td class="text-center" scope="col" style="height: 54px;">{{ timeSlots[k] }}</td>
                                    </tr>
                                </ng-template>
                            </tbody>
                            <tbody *ngIf="!(deactivatedTimeSlots.length < timeSlots.length)">
                                <ng-template ngFor let-item [ngForOf]="availableSlotsInPastDate" let-k="index"
                                    [ngForTrackBy]="trackByRowIndex" style="font-size: 13px;">
                                    <tr scope="row">
                                        <td scope="col" style="height: 54px;">{{ availableSlotsInPastDate[k] }}</td>
                                    </tr>
                                </ng-template>
                            </tbody>
                        </table>
                    </div>
                    <div *ngIf="isTimeSlotAvailable()" class="col-md-10 pl-0 pr-1">
                        <table class="table table-borderless bg-white w-100" style="table-layout: fixed;">
                            <thead>
                                <tr class="text-center">
                                    <!-- <td [ngClass]="{'d-none': (!(deactivatedTimeSlots.length < timeSlots.length) && !availableSlotsInPastDate.length) }" style="height: 30px;"></td> -->
                                    <td *ngFor="let item of availableGates; trackBy : trackByRowIndex; let j = index;"  class="p-1" scope="col" >
                                        <div class="text-xl p-1">
                                            {{ availableGates[j].gate_name }}
                                            <span class="ml-1 routeMapIcon">
                                                <i title="View Route Map" (click)="gateRouteMap(availableGates[j])" *ngIf="availableGates[j].route_map_file_id" class="fas fa-map-marked-alt cursor-pointer"></i>
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            </thead>
                            <tbody *ngIf="deactivatedTimeSlots.length < timeSlots.length">
                                <ng-template ngFor let-item [ngForOf]="timeSlots" let-k="index" [ngForTrackBy]="trackByRowIndex">
                                    <tr [ngClass]="{'d-none': deactivatedTimeSlots.includes(timeSlots[k])}" scope="row">
                                        <!-- <td class="d-none"></td> -->
                                        <!-- <td style="width: 16%;" class="text-center" style="height: 30px;">{{ timeSlots[k] }}</td> -->
                                        <ng-template ngFor let-item [ngForOf]="availableGates" let-l="index" [ngForTrackBy]="trackByRowIndex" style="font-size: 13px;">
                                            <td (click)="slotClicked(false, availableGates[l], timeSlots[k], {}, false)"
                                                class="timeSlotSelectedss" title="Available Slot"
                                                *ngIf="checkIsSlotSelected(availableGates[l], timeSlots[k]) && !checkIsSlotBooked(availableGates[l].project_gate_booking, timeSlots[k]) && !isPastDate &&isPastTime(timeSlots[k]);">
                                                <div class="available-booking cursor-pointer d-flex align-items-center"> <span class="pl-2 text-gray"> Available </span> </div>
                                            </td>
                                            <td class="timeSlotUnavailabless"
                                                *ngIf="!(checkIsSlotSelected(availableGates[l], timeSlots[k]) && !checkIsSlotBooked(availableGates[l].project_gate_booking, timeSlots[k]) && !isPastDate && isPastTime(timeSlots[k]))">
                                                <div *ngIf="isBookingSuccess(availableGates[l].project_gate_booking, timeSlots[k]); else bookingNotSuccess"
                                                    (click)="viewBookingSuccess(availableGates[l], timeSlots[k])">
                                                    <div class="successful-booking cursor-pointer d-flex align-items-center p-0">
                                                        <span class="pl-2 font-weight-bold small-text">
                                                            {{ getbookingTitle(availableGates[l].project_gate_booking, timeSlots[k]) }}
                                                        </span>
                                                        <div class="d-flex align-items-center pr-3 ml-auto" *ngIf="isBookingConfirmArrival; else noShow">
                                                            <span class="pr-2 font-weight-bold small-text">Arrival Confirmed</span>
                                                            <i class="fa fa-check-circle" aria-hidden="true"></i>
                                                        </div>
                                                        <ng-template #noShow>
                                                            <div class="d-flex align-items-center pr-3 ml-auto">
                                                                <span class="pr-2 font-weight-bold small-text">No Show</span>
                                                                <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
                                                            </div>
                                                        </ng-template>
                                                    </div>
                                                    <!-- <i *ngIf="isBookingConfirmArrival" class="fa fa-check-square text-success"></i>
                                                    <i *ngIf="!isBookingConfirmArrival" class="fa fa-window-close text-danger"></i> -->
                                                </div>

                                                <ng-template #bookingNotSuccess>
                                                    <div *ngIf="checkIsSlotBooked(availableGates[l].project_gate_booking, timeSlots[k]); else bookingNotAvailable">
                                                        <div *ngIf="checkIsBookingApproved(availableGates[l].project_gate_booking, timeSlots[k]); else bookingPending"
                                                            class="successful-booking cursor-pointer d-flex align-items-center p-0"
                                                            (click)="editSlotBooking(bookedSlotOptionsHtml, availableGates[l], timeSlots[k])" [title]="getNameEmployerSupplierForToolTip(availableGates[l], timeSlots[k])">
                                                            <p class="m-0 text-elips text-primary">
                                                                <span class="pl-2 pr-1 small-text font-weight-bold">{{ getbookingTitle(availableGates[l].project_gate_booking, timeSlots[k]) }}</span>
                                                                <span class="small-text"> {{ getbookingSupplier(availableGates[l].project_gate_booking, timeSlots[k]) }} </span>
                                                            </p>
                                                        </div>

                                                        <ng-template #bookingPending>
                                                            <div *ngIf="isUserDeliveryManager; else bookingAvailableForOthers">
                                                                <div class="pending-booking cursor-pointer d-flex align-items-center p-0" *ngIf="bookingStatuses[l][k] === 'Pending'"
                                                                     (click)="pendingBookings(availableGates[l], timeSlots[k], l)" title="Booking Pending">
                                                                    <span class="pl-2 font-weight-bold small-text"> Booking Pending</span>
                                                                </div>
                                                                <div class="available-booking cursor-pointer d-flex align-items-center p-0" title="Available Slot"
                                                                     *ngIf="bookingStatuses[l][k] === 'Available'"
                                                                     (click)="slotClicked(false, availableGates[l], timeSlots[k], {}, false)">
                                                                    <span class="pl-2 text-gray"> Available </span>
                                                                </div>
                                                            </div>
                                                        </ng-template>

                                                        <ng-template #bookingAvailableForOthers>
                                                            <div class="pending-booking cursor-pointer d-flex align-items-center p-0"
                                                                (click)="viewOrCreateNewBookingRequest(bookedSlotOptionsHtml, availableGates[l], timeSlots[k])"
                                                                *ngIf="bookingStatuses[l][k] === 'Pending'" title="Booking Pending">
                                                                <span class="pl-2 font-weight-bold small-text"> Booking Pending </span>
                                                            </div>

                                                            <div class="available-booking cursor-pointer d-flex align-items-center p-0"
                                                                *ngIf="bookingStatuses[l][k] === 'Available'"
                                                                (click)="slotClicked(false, availableGates[l], timeSlots[k], {}, false)" title="Available Slot">
                                                                <span class="pl-2 text-gray"> Available </span>
                                                            </div>
                                                        </ng-template>
                                                    </div>
                                                </ng-template>
                                                <ng-template #bookingNotAvailable>
                                                    <div class="unavailable-booking d-flex align-items-center p-0"> <span class="pl-2"> N/A </span> </div>
                                                </ng-template>
                                            </td>
                                        </ng-template>
                                    </tr>
                                </ng-template>
                            </tbody>
                            <tbody *ngIf="!(deactivatedTimeSlots.length < timeSlots.length)">
                                <ng-template ngFor let-item [ngForOf]="availableSlotsInPastDate" let-k="index" [ngForTrackBy]="trackByRowIndex">
                                    <tr scope="row" >
                                        <td class="font-weight-bold" style="width: 16%;" style="height: 30px;">{{ availableSlotsInPastDate[k] }}</td>
                                        <ng-template ngFor let-item [ngForOf]="availableGates" let-l="index" [ngForTrackBy]="trackByRowIndex" style="font-size: 13px;">
                                            <td class="timeSlotUnavailable">
                                                <div *ngIf="isBookingSuccess(availableGates[l].project_gate_booking, availableSlotsInPastDate[k]); else bookingNotSuccess"
                                                    (click)="viewBookingSuccess(availableGates[l], availableSlotsInPastDate[k])">
                                                    <div class="successful-booking cursor-pointer d-flex align-items-center p-0">
                                                        <span class="pl-2 font-weight-bold small-text">
                                                            {{ getbookingTitle(availableGates[l].project_gate_booking, timeSlots[k]) }}
                                                        </span>
                                                        <div class="d-flex align-items-center pr-3 ml-auto" *ngIf="isBookingConfirmArrival; else noShow">
                                                            <span class="pr-2 font-weight-bold small-text">Arrival Confirmed</span>
                                                            <i class="fa fa-check-circle" aria-hidden="true"></i>
                                                        </div>
                                                        <ng-template #noShow>
                                                            <div class="d-flex align-items-center pr-3 ml-auto">
                                                                <span class="pr-2 font-weight-bold small-text">No Show</span>
                                                                <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
                                                            </div>
                                                        </ng-template>
                                                    </div>
                                                </div>
                                                <ng-template #bookingNotSuccess>
                                                    <div *ngIf="checkIsSlotBooked(availableGates[l].project_gate_booking, availableSlotsInPastDate[k])" class="p-0">
                                                            <div class="successful-booking cursor-pointer d-flex align-items-center" [title]="getNameEmployerSupplierForToolTip(availableGates[l], timeSlots[k])"
                                                                *ngIf="checkIsBookingApproved(availableGates[l].project_gate_booking, availableSlotsInPastDate[k]); else bookingPending"
                                                                (click)="editSlotBooking(bookedSlotOptionsHtml, availableGates[l], availableSlotsInPastDate[k])">
                                                                <p class="m-0 text-elips text-gray ">
                                                                    <span class="pl-2 pr-1 text-primary small-text font-weight-bold"> {{ getbookingTitle(availableGates[l].project_gate_booking, timeSlots[k]) }} </span>
                                                                    <span class="text-gray small-text"> {{ getbookingSupplier(availableGates[l].project_gate_booking, timeSlots[k]) }} </span>
                                                                </p>
                                                            </div>
                                                        <ng-template #bookingPending>
                                                            <div class="pending-booking text-gray cursor-pointer" style="padding:0px" title="Booking Pending" (click)="pendingBookings(availableGates[l], availableSlotsInPastDate[k], l)"> <span class="pl-2 font-weight-bold small-text"> Booking Pending </span> </div>
                                                        </ng-template>
                                                    </div>
                                                </ng-template>

                                            </td>
                                        </ng-template>
                                    </tr>
                                </ng-template>

                                <tr *ngIf="!availableSlotsInPastDate.length">
                                    <td [attr.colspan]="availableGates.length">
                                        <div class="timeSlotUnavailable">N/A</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div *ngIf="!isTimeSlotAvailable()">
                    <div class="w-100">
                        <div class="d-flex flex-column justify-content-center align-items-center my-5">
                            <img class="w-50 mb-2" [src]="emptyStatePlaceholder">
                            <h4 class="mt-2"><b>No Availability</b></h4>
                        </div>
                    </div>
                </div>

                <ng-template #bookedSlotOptionsHtml let-c="close" let-d="dismiss">
                    <div class="modal-header justify-content-end">
                        <span type="button" class="close-icon" ria-label="Close" (click)="d('Cross click')">
                            <span aria-hidden="true">×</span>
                        </span>
                    </div>
                    <div class="modal-body text-center">
                        <div class="card-body">
                            <div>
                                <button (click)="viewBooking(c)" class="btn btn-primary mb-3 rounded-pill">View Booking</button>
                            </div>
                            <div *ngIf="canUserAmendAndDeleteBooking && (booking.user_id == authUser$.id || isUserDeliveryManager)">
                                <button (click)="amendBooking(c)" class="btn btn-primary mb-3 rounded-pill">Amend Booking</button>
                            </div>
                            <div *ngIf="canUserAmendAndDeleteBooking && (booking.user_id == authUser$.id || isUserDeliveryManager)">
                                <button (click)="deleteBooking(c)" class="btn btn-primary mb-3 rounded-pill">Delete Booking</button>
                            </div>
                        </div>
                    </div>
                </ng-template>

                <i-modal #bulkBookingsCloseRef title="Pending Bookings" rightPrimaryBtnTxt="Decline" size="xl" [rightPrimaryBtnDisabled]="!selectedbookings.length" 
                    (onClickRightPB)="rejectMultipleBookingsPopup()">
                        <ng-container *ngFor="let gate of availableGates;let i = index">
                            <h5 class="heading pt-4 mt-2 mb-3"> {{gate.gate_name}} </h5>
                            <div class="tableV2">
                                <table class="w-100 mb-0" style="table-layout: fixed;">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th><strong>Booked By</strong></th>
                                        <th><strong>Booking Slot</strong></th>
                                        <th><strong>Supplier</strong></th>
                                        <th><strong>Booking Details</strong></th>
                                        <th><strong>Status</strong></th>
                                    </tr>
                                </thead>
                                <tbody style="font-size:14px;">
                                    <tr *ngFor="let t of pBookings[gate.gate_name]">
                                        <td> <div class="custom-control custom-checkbox p-right form-group">
                                            <input type="checkbox" class="custom-control-input"
                                                   name="{{'booking ' + t?.id}}"
                                                   id="{{'booking ' + t?.id}}" ng-value="t?.id"
                                                   (click)="toggleBookingSelection(t?.id, $event)" [checked]="selectedbookings.includes(t?.id)">
                                            <label class="custom-control-label" for="{{'booking ' +t?.id}}"></label>
                                        </div></td>
                                        <td>{{ t.created_by_user.name }}</td>
                                        <td>{{ t.booking_date }} {{ t.booking_slots }}</td>
                                        <td>{{ t.supplier }}</td>
                                        <td>{{ t.booking_details }}</td>
                                        <td>{{ getBookingStatus(t.status) }}</td>
                                    </tr>
                                    <tr *ngIf="!pBookings[gate.gate_name]?.length">
                                        <td colspan="6">
                                            <span class="w-100 d-flex justify-content-center align-items-center">
                                                No pending bookings.
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            </div>
                        </ng-container>
                </i-modal>

                <i-modal #allPendingBookingsRef title="Pending Bookings" [showCancel]="false" rightPrimaryBtnTxt="Done" (onCancel)="closeAllPendingBookingModal()" (onClickRightPB)="doneAllPendingBookingModal($event)" size="xl">
                    <div class="px-4 pb-4 pt-0 mx-3 mb-3 mt-0">
                        <ng-container *ngFor="let gate of availableGates">
                            <h5 class="heading pt-4 mt-2 mb-3"> {{gate.gate_name}} </h5>
                            <div class="tableV2">
                                <table class="w-100 mb-0" style="table-layout: fixed;">
                                <thead>
                                    <tr>
                                        <th><strong>Booked By</strong></th>
                                        <th><strong>Booking Slot</strong></th>
                                        <th><strong>Supplier</strong></th>
                                        <th><strong>Booking Details</strong></th>
                                        <th><strong>Status</strong></th>
                                        <th width="122">
                                            <strong>Actions</strong>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody style="font-size:14px;">
                                    <tr *ngFor="let t of pBookings[gate.gate_name]">
                                        <td>{{ t.created_by_user.name }}</td>
                                        <td>{{ t.booking_date }} {{ t.booking_slots }}</td>
                                        <td>{{ t.supplier }}</td>
                                        <td>{{ t.booking_details }}</td>
                                        <td>{{ getBookingStatus(t.status) }}</td>
                                        <td>
                                            <button title="View Booking" class="btn btn-sm btn-outline-primary btn-action1 mr-1"
                                                (click)="viewPendingBooking(t)">
                                                <i class="fa fa-search"></i>
                                            </button>
                                            <button title="Approve Booking" class="btn btn-sm btn-outline-primary btn-action1 mr-1"
                                                (click)="approveBooking(t)" *ngIf="isUserDeliveryManager && checkIfBookableForAllPendingBookingsPopup(t) && !isPastDate && isPastTime(t.booking_slots[t.booking_slots.length-1])">
                                                <i class="fa fa fa-check" aria-hidden="true"></i>
                                            </button>
                                            <button title="Delete Booking" class="btn btn-sm btn-outline-primary btn-action1"
                                                (click)="rejectBookingPopup(t)"  *ngIf="isUserDeliveryManager && t.status == 1 && !isPastDate">
                                                <i class="fa fa-times" aria-hidden="true"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr *ngIf="!pBookings[gate.gate_name]?.length">
                                        <td colspan="6">
                                            <span class="w-100 d-flex justify-content-center align-items-center">
                                                No pending bookings.
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            </div>
                        </ng-container>
                    </div>
                </i-modal>

                <i-modal #pendingBookingsHtml [title]="'Bookings ' + ngbMomentjsAdapter.ngbDateToDayJs(selectedDay).format('DD/MM/YY')" size="xl" rightPrimaryBtnTxt="Add Booking" [showCancel]="false"
                    (onClickRightPB)="slotClicked($event, {}, {}, pendingGateBookings[0], true)">
                    <div class="text-center">
                        <div class="card-body">
                            <div class="tableV2">
                                <table class="mb-0" style="table-layout: fixed; width: 100%;">
                                    <thead>
                                        <tr>
                                            <th><strong>Booked By</strong> </th>
                                            <th><strong>Booking Slot</strong></th>
                                            <th><strong>Supplier</strong> </th>
                                            <th><strong>Booking Details</strong> </th>
                                            <th width="122">
                                                <strong>Actions</strong>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody style="font-size:14px;">
                                        <tr *ngFor="let t of pendingGateBookings">
                                            <td> {{ t.created_by_user.name }} </td>
                                            <td> {{ t.booking_date }} {{ t.booking_slots }} </td>
                                            <td> {{ t.supplier }} </td>
                                            <td> {{ t.booking_details }} </td>
                                            <td>
                                                <ng-container *ngIf="isUserDeliveryManager">
                                                    <button title="View Booking" class="btn btn-sm btn-outline-primary btn-action1 mr-1"
                                                        (click)="viewPendingBooking(t)">
                                                        <i class="fa fa-search"></i>
                                                    </button>
                                                    <button *ngIf="!approvedBookingAvailable && t.status != 0 && !isPastDate && isPastTime(t.booking_slots[t.booking_slots.length-1])" title="Approve Booking"
                                                        class="btn btn-sm btn-outline-primary btn-action1  mr-1" (click)="approveBooking(t)">
                                                        <i class="fa fa fa-check" aria-hidden="true"></i>
                                                    </button>
                                                    <button *ngIf="t.status == 1 && !isPastDate" title="Delete Booking" class="btn btn-sm btn-outline-primary btn-action1"
                                                        (click)="rejectBookingPopup(t)">
                                                        <i class="fa fa-times" aria-hidden="true"></i>
                                                    </button>
                                                </ng-container>
                                            </td>
                                        </tr>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </i-modal>

                <i-modal #declineBookingFormRef title="" rightPrimaryBtnTxt="Decline" (onClickRightPB)="rejectBooking($event)" [rightPrimaryBtnDisabled]="!declineForm.valid">
                    <div class="text-center">
                        <form novalidate #declineForm="ngForm">
                            <div class="mb-2">
                                <p class="mb-1">Comment: </p>
                                <textarea class="form-control" name="declined_comment"
                                          [(ngModel)]="declined_comment"
                                          placeholder="Comment"
                                          #declinedComment="ngModel" required></textarea>
                                <div class="alert alert-danger" [hidden]="(!declinedComment || declinedComment.valid)">Comment is required</div>
                            </div>
                        </form>
                    </div>
                </i-modal>

                <i-modal #declineMultipleBookingFormRef title="" rightPrimaryBtnTxt="Decline" (onClickRightPB)="rejectMultipleBookings($event)" [rightPrimaryBtnDisabled]="!declineForm.valid">
                    <div class="text-center">
                        <form novalidate #declineForm="ngForm">
                            <div class="mb-2">
                                <p class="mb-1">Comment: </p>
                                <textarea class="form-control" name="declined_comment"
                                          [(ngModel)]="declined_comment"
                                          placeholder="Comment"
                                          #declinedComment="ngModel" required></textarea>
                                <div class="alert alert-danger" [hidden]="(!declinedComment || declinedComment.valid)">Comment is required</div>
                            </div>
                        </form>
                    </div>
                </i-modal>

                <i-modal #viewBookedSlotRef title="Booking Details" [rightPrimaryBtnTxt]="((canUserAmendAndDeleteBooking && (booking.user_id == authUser$.id)) && (booking.status != 0)) ? 'Amend Booking' : ''" 
                    cancelBtnText="OK" (onClickRightPB)="amendBooking($event.closeFn)" size="xl">
                        <table class="table table-sm table-bordered">
                            <tr *ngIf="bookingApprovalProcessStatus">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Booking Status:</strong>
                                </td>
                                <td colspan="5" [ngStyle]="{'color': getColorByStatus(booking.status)}">
                                    {{ getBookingStatus(booking.status) }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.status_details?.induction_request">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>{{ booking.status_details.status }} By:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.status_details.induction_request.user_name }} ({{ booking.status_details.induction_request.user_employer }})
                                </td>
                            </tr>
                            <tr *ngIf="booking.status_details?.timestamp && booking.status_details.status">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Date {{ booking.status_details.status }}:</strong>
                                </td>
                                <td colspan="5">
                                    {{ dayjs(booking.status_details.timestamp).tz(projectInfo?.custom_field?.timezone).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.booking_type">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Booked Type:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.booking_type | titlecase }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.created_by_user?.induction_request">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Booked by:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.created_by_user.induction_request.user_name }} ({{ booking.created_by_user.induction_request.user_employer }})
                                </td>
                            </tr>
                            <tr *ngIf="booking.createdAt">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Booked:</strong>
                                </td>
                                <td colspan="5">
                                    {{  dayjs(booking.createdAt).tz(projectInfo?.custom_field?.timezone).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS)  }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.booking_date">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Booking Date:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.booking_date }}
                                </td>
                            </tr>
                            <tr *ngIf="this.bookingSlotsString">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Booking Slots:</strong>
                                </td>
                                <td colspan="5">
                                    {{ this.bookingSlotsString }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.duration">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Booking Duration:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.duration }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.block_dates_info && booking.block_dates_info.frequency && booking.block_dates_info.startDate && booking.block_dates_info.endDate">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Block Booking:</strong>
                                </td>
                                <td colspan="5">
                                    {{blockBookingInfo(booking.block_dates_info)}}
                                </td>
                            </tr>
                            <tr *ngIf="booking.delivery_notes_ref && booking.delivery_notes_ref.delivery_ref_no">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Delivery Ref./No.:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.delivery_notes_ref.delivery_ref_no }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.delivery_notes_ref && booking.delivery_notes_ref.po_number">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>PO Number:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.delivery_notes_ref.po_number }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.booking_ref">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Booking Ref:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.booking_ref }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.booking_details">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Booking Details:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.booking_details }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.supplier">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Supplier:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.supplier }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.contact_name">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Supplier Contact Name:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.contact_name }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.contact_number">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Supplier Contact Number:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking?.contact_number }}
                                </td>
                            </tr>
                            <tr *ngIf="projectInfo.is_fors_compliant && booking.fors_no">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>FORS No:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.fors_no }}
                                </td>
                            </tr>
                            <tr *ngIf="projectInfo.is_fors_compliant && booking.fors_no_badge">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>FORS Badge:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.fors_no_badge }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.vehicle_reg">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Vehicle Reg:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.vehicle_reg }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.vehicle_reg && booking.vehicle_info && booking.vehicle_info.make">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Make:</strong>
                                </td>
                                <td>
                                    {{ booking.vehicle_info.make }}
                                </td>
                                <td class="tr-bg-dark-color">
                                    <strong>Fuel Type:</strong>
                                </td>
                                <td >
                                    {{ booking.vehicle_info.fuelType }}
                                </td>
                                <td class="tr-bg-dark-color">
                                    <strong>CO2 Emissions:</strong>
                                </td>
                                <td class="vehicleCell">
                                    {{ booking.vehicle_info.co2Emissions }} g/km
                                </td>
                            </tr>
                            <tr *ngIf="booking.vehicle_type">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Vehicle Type:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.vehicle_type }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.laden_percent">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Laden(%):</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.laden_percent }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.vehicle_make_model">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Vehicle Make & Model:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.vehicle_make_model }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.co2_emission_gm">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Vehicle CO2 Emissions (g/Km):</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.co2_emission_gm }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.haulage_company">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Haulage Company:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.haulage_company }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.waste_collection_company">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Waste Collection Company:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.waste_collection_company }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.driver_name">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Driver Name:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking?.driver_name }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.driver_number">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Driver Contact Number:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.driver_number }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.dispatch_post_code">
                                <td class="tr-bg-dark-color w-25">
                                    <strong><span i18n="@@dpc">Dispatch Postcode</span>:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.dispatch_post_code }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.return_postcode">
                                <td class="tr-bg-dark-color w-25">
                                    <strong><span i18n="@@rpc">Return Postcode</span>:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.return_postcode }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.dispatch_post_code">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Distance Travelled:</strong>
                                </td>
                                <td *ngIf="booking.distance_travelled" colspan="5">
                                    {{ booking.distance_travelled }} km
                                </td>
                                <td *ngIf="!booking.distance_travelled" colspan="5"> N/A </td>
                            </tr>
                            <tr *ngIf="booking.dispatch_post_code">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Total CO2 Emissions:</strong>
                                </td>
                                <td  *ngIf="+booking.distance_travelled && +booking.co2_emission_gm" colspan="5">
                                    {{ ((+booking.distance_travelled * (+booking.co2_emission_gm)) / 1000) }} kg
                                </td>
                                <td *ngIf="!+booking.distance_travelled || !+booking.co2_emission_gm" colspan="5"> N/A </td>
                            </tr>
                            <tr *ngIf="booking.handling_equipment">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Handling Equipment:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.handling_equipment }}
                                </td>
                            </tr>
                            <tr *ngIf="booking.drop_off_address">
                                <td class="tr-bg-dark-color w-25">
                                    <strong>Drop-off Postcode:</strong>
                                </td>
                                <td colspan="5">
                                    {{ booking.drop_off_address }}
                                </td>
                            </tr>
                        </table>
                        <div class="materialRow" *ngIf="booking.materials && booking.materials.length">
                            <p class="mb-1"><strong>Materials:</strong></p>
                            <table class="table table-sm table-bordered">
                                <thead>
                                    <tr>
                                        <th class="tr-bg-dark-color">Material</th>
                                        <th class="tr-bg-dark-color">Quantity</th>
                                        <th class="tr-bg-dark-color">Unit</th>
                                        <th class="tr-bg-dark-color">Notes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let t of booking.materials">
                                        <td>{{ t.material }}</td>
                                        <td>{{ t.quantity }}</td>
                                        <td>{{ t.unit }}</td>
                                        <td>{{ t.notes }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="materialRow mb-3" *ngIf="booking.amendment_log && booking.amendment_log.length">
                            <p class="mb-1"><strong>Booking Log</strong></p>
                            <ul class="list-unstyled m-0">
                                <li>
                                    <ng-container *ngFor="let t of booking.amendment_log">
                                        <ul class="mb-1" *ngIf="!t.amended">
                                            <li>{{ t }}</li>
                                        </ul>
                                    </ng-container>
                                </li>
                            </ul>
                            <table class="table table-sm table-bordered mb-0">
                                <thead>
                                    <tr>
                                        <th class="tr-bg-dark-color"><strong>Amended</strong> </th>
                                        <th class="tr-bg-dark-color"><strong>Amended By</strong></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <ng-container *ngFor="let t of booking.amendment_log">
                                        <tr *ngIf="t.amended">
                                            <td>{{ t.amended }}</td>
                                            <td>{{ t.amendedBy }}</td>
                                        </tr>
                                    </ng-container>

                                </tbody>
                            </table>
                        </div>
                        <div class="materialRow" *ngIf="checkBookingCloseout(booking)">
                            <p class="mb-1"><strong>Closeout Details</strong></p>
                            <table class="table table-sm table-bordered mb-0">
                                <tbody>
                                    <tr *ngIf="booking?.booking_closeout?.is_confirm_arrival">
                                        <td class="tr-bg-dark-color w-25">
                                            <strong>Arrival Time</strong>
                                        </td>
                                        <td>
                                            {{ dayjs(booking?.booking_closeout?.arrival_time).format("HH:mm") }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color w-25">
                                            <strong>Status</strong>
                                        </td>
                                        <td>
                                            <span *ngIf="booking?.booking_closeout?.is_confirm_arrival"> Confirm Arrival</span>
                                            <span *ngIf="!booking?.booking_closeout?.is_confirm_arrival" class="text-danger"><strong>No Show</strong></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color w-25">
                                            <strong>Closeout By</strong>
                                        </td>
                                        <td>
                                            {{ booking?.booking_closeout?.user_fullname }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color w-25">
                                            <strong>Comment</strong>
                                        </td>
                                        <td>
                                            <span [innerText]="booking?.booking_closeout?.comment"></span>
                                        </td>
                                    </tr>
                                    <tr *ngIf="booking?.booking_closeout?.is_confirm_arrival">
                                        <td class="tr-bg-dark-color w-25">
                                            <strong>Delivery Note</strong>
                                        </td>
                                        <td>
                                            {{ booking?.booking_closeout?.delivery_note }}
                                        </td>
                                    </tr>
                                    <tr *ngIf="booking?.booking_closeout?.delivery_note_images?.length">
                                        <td colspan="2">
                                            <pop-up-image-viewer [alt]="'Close Out Image'" [imgArray]="booking?.booking_closeout?.delivery_note_images || []"></pop-up-image-viewer>
                                        </td>
                                    </tr>
                                    <tr *ngIf="booking?.booking_closeout?.is_confirm_arrival">
                                        <td class="tr-bg-dark-color w-25" colspan="2">
                                            <strong>Delivery Images</strong>
                                        </td>

                                    </tr>
                                    <tr *ngIf="booking?.booking_closeout?.additional_images?.length">
                                        <td colspan="2">
                                            <pop-up-image-viewer [alt]="'Close Out Image'" [imgArray]="booking?.booking_closeout?.additional_images || []"></pop-up-image-viewer>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                </i-modal>

                <i-modal #bookingPopupRef [title]="(!isNotThisStage('Booking Details') ? 'Booking Details' : (!isNotThisStage('Vehicle Details') ? 'Vehicle Details' : 'Materials'))" 
                    [rightPrimaryBtnTxt]="((!isNotThisStage('Booking Details') || !isNotThisStage('Vehicle Details') ? 'Next' : 'Save'))" 
                    [showCancel]="false" (onClickRightPB)="((!isNotThisStage('Booking Details')|| !isNotThisStage('Vehicle Details') ? moveNextForm() : saveBooking(materialsForm, $event)))" 
                    [rightSecondaryBtnTxt]="activeStage ? 'Back' : ''" (onClickRightSB)="movePrevForm()" size="xl"
                    [rightPrimaryBtnDisabled]="(!isNotThisStage('Booking Details') ? !bookingDetailsForm.valid : (!isNotThisStage('Vehicle Details') ? !vehicleDetailsForm.valid : !materialsForm.valid))">
                        <ul ngbNav #nav="ngbNav" [(activeId)]="active" (activeIdChange)="tabChange($event)" class="nav-tabs n-tab booking-form-container setup-content mb-2" id="modalTop">
                            <li [ngbNavItem]="1" [disabled]="isNotThisStage('Booking Details') || !!this.booking.id">
                                <a class="nav-a" ngbNavLink>Delivery</a>
                                <ng-template ngbNavContent>
                                </ng-template>
                            </li>
                            <li [ngbNavItem]="2" [disabled]="isNotThisStage('Booking Details') || !!this.booking.id">
                                <a class="nav-a" ngbNavLink>Collection</a>
                                <ng-template ngbNavContent>
                                </ng-template>
                            </li>
                        </ul>

                        <!-- Booking Details -->
                        <form [ngClass]="{'booking-form-container setup-content': true, 'd-none': isNotThisStage('Booking Details')}" novalidate #bookingDetailsForm="ngForm">
                            <div *ngIf="showModal">
                            <input type="hidden" class="form-control" name="gate_id" [(ngModel)]="booking.gate_id"/>
                            <input type="hidden" class="form-control" name="project_id" [(ngModel)]="booking.project_id"/>
                            <fieldset disabled>
                                <div class="form-group">
                                    <label>Gate Name</label>
                                    <input type="text" class="form-control" [(ngModel)]="gate_name" name="gate_name"
                                           placeholder="Gate Name" />
                                </div>

                                <div class="form-group">
                                    <label>Booking Date</label>
                                    <input type="text" class="form-control" [(ngModel)]="booking.booking_date" name="booking_date"
                                           placeholder="Booking Date" />
                                </div>
                            </fieldset>
                            <div class="form-group">
                                <label>Duration</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <button class="btn btn-primary" type="button" [disabled]="!isPastTime(booking.booking_slots[booking.booking_slots.length -1]) || disableForPendingBookings" (click)="decreaseDuration()"><i class="fas fa-minus" title="Decrease Duration"></i></button>
                                    </div>
                                    <input type="text" class="form-control" [(ngModel)]="booking.duration" name="duration"
                                           placeholder="Duration" disabled/>
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="button" [disabled]="!isPastTime(booking.booking_slots[booking.booking_slots.length -1]) || disableForPendingBookings" (click)="increaseDuration()"><i class="fas fa-plus" title="Increase Duration"></i></button>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group" *ngIf="booking.id">
                                <label>Start Time</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <button class="btn btn-primary" type="button" [disabled]="!isPastTime(booking.booking_slots[booking.booking_slots.length -1]) || disableForPendingBookings" (click)="decreaseStartTime()"><i class="fas fa-minus" title="Decrease Start Time"></i></button>
                                    </div>
                                    <input type="text" class="form-control" [(ngModel)]="startTime" name="startTime"
                                           placeholder="Start Time" disabled/>
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="button" [disabled]="!isPastTime(booking.booking_slots[booking.booking_slots.length -1]) || disableForPendingBookings" (click)="increaseStartTime()"><i class="fas fa-plus" title="Increase Start Time"></i></button>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>Booking Slot</label>
                                <input type="text" class="form-control" [(ngModel)]="bookingSlotsString" name="booking_slots"
                                       placeholder="Booking Slot" disabled/>
                            </div>

                            <div class="form-group" *ngIf="!booking.id">
                                <div class="mb-1">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input"
                                               [(ngModel)]="block_booking"
                                               [checked]="block_booking"
                                               name="black_booking"
                                               id="bb">
                                        <label class="custom-control-label" for="bb">Block Booking</label>
                                    </div>
                                </div>
                                <div *ngIf="block_booking" class="ml-4">
                                    <div class="form-group">
                                        <div class="custom-control custom-radio">
                                            <input type="radio" id="block_booking_type1" name="block_booking_type"
                                                   required [(ngModel)]="block_booking_type" [value]="'daily'"
                                                   class="custom-control-input" required>
                                            <label class="custom-control-label" for="block_booking_type1">Every Day {{bookingSlotsString}}</label>
                                        </div>
                                        <div class="custom-control custom-radio">
                                            <input type="radio" id="block_booking_type2" name="block_booking_type"
                                                   required [(ngModel)]="block_booking_type" [value]="'weekly'"
                                                   class="custom-control-input" required>
                                            <label class="custom-control-label" for="block_booking_type2">Every {{dayOfWeek}} {{bookingSlotsString}}</label>
                                        </div>
                                    </div>


                                    <div class="form-group">
                                        <label>Until <small class="required-asterisk ">*</small> </label>
                                        <div class="input-group">
                                            <input class="form-control col-md-8" placeholder="dd-mm-yyyy"
                                                   name="start_date" [(ngModel)]="block_booking_endDate"
                                                   ngbDatepicker
                                                   #start_date="ngModel"
                                                   #sd="ngbDatepicker" ng-value="block_booking_endDate" [minDate]="minDate" required>
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary calendar" (click)="sd.toggle()" type="button">
                                                    <i class="fa fa-calendar"></i>
                                                </button>
                                            </div>
                                            <div class="alert alert-danger" [hidden]="(start_date.valid)">Until Date is required.</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>Booking Details</label>
                                <input type="text" class="form-control" [(ngModel)]="booking.booking_details" name="booking_details" pattern="^(?!\s*$).+"
                                       placeholder="Booking Details" />
                            </div>
                            <div class="form-group">
                                <label>Booking Ref.</label>
                                <input type="text" class="form-control" [(ngModel)]="booking.booking_ref" name="booking_ref" pattern="^(?!\s*$).+"
                                       placeholder="Booking Ref" />
                            </div>
                            <div *ngIf="active===1">

                                <div class="form-group">
                                    <label>Supplier <small class="required-asterisk ">*</small></label>
                                    <input type="text" class="form-control" required #supplier="ngModel" [(ngModel)]="booking.supplier" name="supplier"
                                           placeholder="Supplier" />
                                    <div class="alert alert-danger" [hidden]="(supplier.valid)">Supplier is required.</div>
                                </div>

                                <div class="form-group">
                                    <label>Delivery Ref./No.</label>
                                    <input type="text" class="form-control" [(ngModel)]="deliveryInfo.delivery_ref_no" name="delivery_ref_no"
                                    placeholder="Delivery Ref./No." pattern="^(?!\s*$).+"/>
                                </div>
                                <div class="form-group">
                                    <label>PO Number</label>
                                    <input type="text" class="form-control" [(ngModel)]="deliveryInfo.po_number" name="po_number"
                                    placeholder="Po Number" pattern="^(?!\s*$).+"/>
                                </div>

                                <div class="form-group">
                                    <label>Supplier Contact Name</label>
                                    <input type="text" class="form-control" [(ngModel)]="booking.contact_name" name="contact_name"
                                           placeholder="Supplier Contact Name" pattern="^(?!\s*$).+"/>
                                </div>

                                <div class="form-group">
                                    <label>Supplier Contact Number</label>
                                    <input type="text" class="form-control" #contact_number="ngModel" [(ngModel)]="booking.contact_number" name="contact_number" pattern="^\d+$"
                                           placeholder="Supplier Contact Number" />
                                </div>
                            </div>
                            </div>
                        </form>

                        <!-- Vehicle Details -->
                        <form [ngClass]="{'booking-form-container setup-content': true, 'd-none': isNotThisStage('Vehicle Details')}" novalidate #vehicleDetailsForm="ngForm">
                            <div class="form-group" *ngIf="projectInfo.is_fors_compliant">
                                <label>FORS No. <small class="required-asterisk ">*</small></label>
                                <input type="text" class="form-control" [(ngModel)]="booking.fors_no" name="fors_no"
                                       placeholder="FORS No." [required]="projectInfo.is_fors_compliant"/>
                            </div>
                            <div class="form-group" *ngIf="projectInfo.is_fors_compliant">
                                <label>FORS Badge <small class="required-asterisk ">*</small></label>
                                <ng-select [items]="forsBadges" bindLabel="value" bindValue="key" [(ngModel)]="booking.fors_no_badge" name="fors_no_badge" class="form-control" [required]="projectInfo.is_fors_compliant">
                                </ng-select>
                            </div>

                            <div class="form-group">
                                <label>Vehicle Reg.</label>
                                <input type="text" class="form-control" [(ngModel)]="booking.vehicle_reg" name="vehicle_reg" pattern="^(?!\s*$).+"
                                       placeholder="Vehicle Reg" />
                            </div>
                            <div class="form-group">
                                <label>Vehicle Type <small class="required-asterisk ">*</small></label>
                                <ng-select class="dropdown-list" required
                                           placeholder="Select Vehicle Type"
                                           [ngModel]="booking.vehicle_type"
                                           name="vehicle_type"
                                           (change)="vehicleTypeSelected($event)"
                                           bindValue="label"
                                           bindLabel="label"
                                           groupBy="heading"
                                           [items]="META_VEHICLE_TYPES">
                                    <ng-template ng-optgroup-tmp let-item="item">
                                        <b>{{ item.heading }}</b>
                                    </ng-template>
                                </ng-select>
                            </div>

                            <div class="form-group" *ngIf="booking.laden_percent !== null">
                                <label>Laden % <small class="required-asterisk ">*</small></label>
                                <ng-select class="dropdown-list"
                                           name="laden_percent" required
                                           placeholder="Select Laden percent"
                                           (change)="deriveEmissionIfNeeded()"
                                           [(ngModel)]="booking.laden_percent"
                                           [items]="[0, 50, 100]"></ng-select>
                            </div>

                            <div class="form-group" *ngIf="booking.vehicle_type">
                                <label>Vehicle CO2 Emissions (g/km)</label>
                                <input type="number" class="form-control co2_emission_gm"
                                       name="co2_emission_gm"
                                       [(ngModel)]="booking.co2_emission_gm" disabled>
                            </div>

                            <div class="form-group">
                                <label>Vehicle Make & Model</label>
                                <input type="text" class="form-control" [(ngModel)]="booking.vehicle_make_model" name="vehicle_make_model" pattern="^(?!\s*$).+" 
                                       placeholder="Vehicle Make & Model" />
                            </div>

                            <div class="form-group" *ngIf="active===1">
                                <label>Haulage Company <small class="required-asterisk ">*</small></label>
                                <input type="text" class="form-control" required #haulage_company="ngModel" [(ngModel)]="booking.haulage_company" name="haulage_company"
                                       placeholder="Haulage Company" />
                                <div class="alert alert-danger" [hidden]="(haulage_company.valid)">Haulage company is required.</div>
                            </div>

                            <div class="form-group" *ngIf="active===2">
                                <label>Waste Collection Company</label>
                                <input type="text" class="form-control" #waste_collection_company="ngModel" [(ngModel)]="booking.waste_collection_company" name="waste_collection_company" pattern="^(?!\s*$).+"
                                       placeholder="Waste Collection Company" pattern="^(?!\s*$).+" />
                            </div>

                            <div class="form-group">
                                <label>Driver Name</label>
                                <input type="text" class="form-control" [(ngModel)]="booking.driver_name" name="driver_name" pattern="^(?!\s*$).+"
                                       placeholder="Driver Name" />
                            </div>

                            <div class="form-group">
                                <label>Driver Contact Number</label>
                                <input type="text" class="form-control" [(ngModel)]="booking.driver_number" name="driver_number" pattern="^\d+$"
                                       placeholder="Driver Contact Number" />
                            </div>

                            <div class="form-group">
                                <label><span i18n="@@dpc">Dispatch Postcode</span> <small class="required-asterisk ">*</small></label>
                                <input type="text" class="form-control" required #dispatchPostcode="ngModel" [(ngModel)]="booking.dispatch_post_code" name="dispatch_post_code"
                                       placeholder="Dispatch Postcode" i18n-placeholder="@@dpc" minlength="4" (focusout)="validatePostcode(dispatchPostcode, booking.dispatch_post_code)" />
                                <div class="alert alert-danger" [hidden]="!(dispatchPostcode.errors && dispatchPostcode.errors.required)"><span i18n="@@poc">Postcode</span> is required</div>
                                <div class="alert alert-danger" [hidden]="!(dispatchPostcode.errors && (dispatchPostcode.errors.valid || dispatchPostcode.errors.minlength))">Invalid <span i18n="@@poc">Postcode</span>.</div>
                            </div>

                            <ng-container  *ngIf="active===1">
                                <div class="form-group">
                                    <label><span>Is the return postcode the same as the dispatch postcode?</span> <small class="required-asterisk ">*</small></label>
                                    <div class="d-flex">
                                        <div class="custom-control custom-radio">
                                            <input type="radio" id="same_return_postcode_y" name="return_postcode_required"
                                                   required [(ngModel)]="booking._same_dispatch_return_postcode" [value]="true"
                                                   class="custom-control-input" required (change)="returnPostcodeSelection()">
                                            <label class="custom-control-label" for="same_return_postcode_y">Yes</label>
                                        </div>
                                        <div class="custom-control custom-radio ml-3">
                                            <input type="radio" id="same_return_postcode_n" name="return_postcode_required"
                                                   required [(ngModel)]="booking._same_dispatch_return_postcode" [value]="false"
                                                   class="custom-control-input" required (change)="returnPostcodeSelection()">
                                            <label class="custom-control-label" for="same_return_postcode_n">No</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group" *ngIf="booking._same_dispatch_return_postcode === false">
                                    <label><span i18n="@@rpc">Return Postcode</span> <small class="required-asterisk ">*</small></label>
                                    <input type="text" class="form-control" required #returnPostcode="ngModel" [(ngModel)]="booking.return_postcode" name="return_postcode"
                                           placeholder="Return Postcode" i18n-placeholder="@@rpc" (focusout)="validatePostcode(returnPostcode, booking.return_postcode)"/>
                                    <div class="alert alert-danger" [hidden]="!(returnPostcode.errors && returnPostcode.errors.required)"><span i18n="@@poc">Postcode</span> is required</div>
                                    <div class="alert alert-danger" [hidden]="!(returnPostcode.errors && returnPostcode.errors.valid)">Invalid <span i18n="@@poc">Postcode</span>.</div>
                                </div>
                                <div class="form-group">
                                    <label>Handling Equipment</label>
                                    <input *ngIf="!projectInfo.custom_field.handling_equipment || projectInfo.custom_field.handling_equipment.length < 1 " type="text" class="form-control" [(ngModel)]="booking.handling_equipment" name="handling_equipment"
                                           placeholder="Handling Equipment" pattern="^(?!\s*$).+"/>
                                    <ng-select *ngIf="projectInfo.custom_field.handling_equipment && projectInfo.custom_field.handling_equipment.length > 0 "
                                        class="d-inline-block w-100"
                                        [items]="projectInfo.custom_field.handling_equipment"
                                        placeholder="Select Handling Equipment"
                                        name="handling_equipment"
                                        [(ngModel)]="booking.handling_equipment">
                                    </ng-select>
                                </div>
                            </ng-container>

                            <div class="form-group" *ngIf="active===2">
                                <label>Drop-off Postcode</label>
                                <input type="text" class="form-control" #dropPostcode="ngModel" [(ngModel)]="booking.drop_off_address" name="drop_off_address"
                                       placeholder="Drop-off Postcode" (focusout)="booking.drop_off_address.trim().length && validatePostcode(dropPostcode, booking.drop_off_address)"/>
                                <div class="alert alert-danger" [hidden]="!(dropPostcode.errors && dropPostcode.errors.valid)">Invalid <span i18n="@@poc">Postcode</span>.</div>
                            </div>

                        </form>

                        <!-- Materials -->
                        <form [ngClass]="{'booking-form-container setup-content': true, 'd-none': isNotThisStage('Materials')}" novalidate #materialsForm="ngForm">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th style="width:36%">Material</th>
                                        <th style="width:15%">Quantity</th>
                                        <th style="width:20%">Unit</th>
                                        <th style="width:24%">Notes</th>
                                        <th style="width:5%">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of booking.materials; trackBy : trackByRowIndex; let i = index;">
                                        <td>
                                            <ng-select class="material dropdown-list" appendTo="body" required
                                                       [name]="'materials_'+i"
                                                       placeholder="Select Material"
                                                       [(ngModel)]="booking.materials[i].material"
                                                       [items]="META_MATERIALS"></ng-select>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control" placeholder="Qty" pattern="^\d+$" style="min-height: 50px;"
                                                [(ngModel)]="booking.materials[i].quantity" ng-value="booking.materials[i].quantity" [maxLength]="8"
                                                [name]="'quantity_'+i" required>
                                        </td>
                                        <td>
                                            <ng-select class="material dropdown-list" appendTo="body" required
                                                       [name]="'unit_'+i"
                                                       placeholder="Select Unit"
                                                       [(ngModel)]="booking.materials[i].unit"
                                                       [items]="META_UNITS"></ng-select>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control" placeholder="Notes" [(ngModel)]="booking.materials[i].notes" style="min-height: 50px;"
                                                ng-value="booking.materials[i].notes" [name]="'notes_'+i">
                                        </td>
                                        <td class="text-center v-align">
                                                <span class="material-symbols-outlined text-danger cursor-pointer"
                                                (click)="removeMaterialRow($event, i)">
                                                    delete
                                                </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="8">
                                            <i class="fa fa-plus-circle text-primary cursor-pointer" (click)="addMaterialRow()">Add Materials</i>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </form>
                    <block-loader [show]="saveBookingloader"></block-loader>
                </i-modal>

                <i-modal #routeMapHtml [showCancel]="false" size="lg" [showFooter]="false">
                        <div class="form-group row">
                            <div class="col-sm-12 mt-2 text-center" *ngIf="selectedGateDetail.route_map_file_url">
                                <div *ngIf="!isPdfDocument" style="width: 600px;" class="d-inline-block">
                                    <img [src]="selectedGateDetail.route_map_file_url" (error)="onLogoError(img, selectedGateDetail.route_map_file_url)" (load)="onLogoLoad(img)" style="width: 100%; height: auto;" #img />
                                </div>

                                <iframe *ngIf="selectedGateDetail.route_map_file_url && isPdfDocument" class="border-0" [src]="previewURL" width="750px" height="500px">
                                </iframe>
                            </div>
                        </div>
                 </i-modal>

                <block-loader [show]="(bookingInProgress)"></block-loader>
            </div>

            <div class="mt-4 text-center w-100" *ngIf="!loadingGates && !availableGates.length">No gate available for booking on the project.</div>
        </div>
    </div>
</div>
<block-loader [show]="(exportBookingLoader)" alwaysInCenter="true" showBackdrop="true"></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
<report-downloader #reportDownloader 
    [displayMonths]="2"
    [xlsxOnly]="false"
    (onFilterSelection)="viewBookingReportDownload($event)"
    [showFolder]="true"
    [applyMaxDate]="false"
    >
</report-downloader>
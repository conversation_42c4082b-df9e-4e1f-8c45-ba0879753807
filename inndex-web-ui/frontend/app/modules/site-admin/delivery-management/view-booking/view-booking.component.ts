import { Component, Input, OnChanges, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from "@angular/router";
import {
    HttpService,
    AuthService,
    User,
    ProjectService,
    ProjectGate,
    ProjectGateBooking,
    ProjectGateBookingService,
    Project,
    UACProjectDesignations,
    ViewBookingConfModalType,
    TransportManagementActionButtons,
    ActionButtonVal,
    TimeUtility,
    ToastService,
    ResourceService, ProjectLocation,
} from "@app/core";
import { NgbDateStruct, NgbCalendar, NgbModalConfig, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgbMomentjsAdapter } from "@app/core/ngb-moment-adapter";
import * as dayjs from 'dayjs';
import { DomSanitizer } from "@angular/platform-browser";
import { AppConstant } from '@env/environment';
import { AssetsUrl, GenericConfirmationModalComponent, IModalComponent } from '@app/shared';
import { innDexConstant } from '@env/constants';
import { ReportDownloaderComponent } from '@app/modules/common';
import { GateSupervisor } from '@app/core/models/gate-supervisor.model';

@Component({
    selector: 'view-booking',
    templateUrl: './view-booking.component.html',
    styleUrls: ['./view-booking.component.scss'],
    // add NgbModalConfig and NgbModal to the component providers
    providers: [NgbModalConfig, NgbModal],
})
export class ViewBookingComponent implements OnInit, OnChanges {

    @Input() selectedDay: NgbDateStruct;
    @Input() project_inducted_users: Array<any> = [];
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    @ViewChild('reportDownloader') private reportDownloader: ReportDownloaderComponent;
    exportBookingSelectedDay: NgbDateStruct;
    maxDate: NgbDateStruct = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
    AppConstant = AppConstant;
    dayjs = dayjs;
    dateFormat: string = AppConstant.defaultDateFormat;
    apiRequestDateFormat: string = AppConstant.apiRequestDateFormat;
    dateStorageFormat: string = AppConstant.dateStorageFormat;
    booking: ProjectGateBooking = new ProjectGateBooking;
    date: {year: number, month: number};
    authUser$: User;
    availableGates: Array<any> = [];
    gate_name: string = '';
    selectedDayName: string = '';
    selectedDateEpoch: string = '';
    isSlotSelected: Array<any> = [];
    isSlotBooked: Array<any> = [];
    bookingSlotsString: string = '';
    selectedGateDetail: ProjectGate = new ProjectGate;
    currentBookingDate: string = '';
    availableSlotsInPastDate: Array<any> = [];
    isPastDate: boolean = false;
    bookingInProgress = false;
    sidebarInProgress = true;
    addBookingSuccessMsg: string = '';
    canUserAmendAndDeleteBooking = false;
    projectUsersEmail: Array<any> = [];
    gate_supervisors: Array<any> = [];
    saved_gate_supervisor_ids: Array<any> = [];
    savingSupervisors: boolean = false;
    timeSlots: Array<string> = [];
    deactivatedTimeSlots: Array<any> = [];
    previewURL: any;
    exportBookingsModalRef: any = null;
    exportBookingLoader: boolean = false;
    daysInWeek: Array<string> = [
        'Mon',
        'Tues',
        'Wed',
        'Thur',
        'Fri',
        'Sat',
        'Sun'
    ];
    formStages: Array<string> = [
        'Booking Details',
        'Vehicle Details',
        'Materials'
    ];
    forsBadges: Array<any> = [{
        key: 'Bronze',
        value: 'Bronze'
    },
    {
        key: 'Silver',
        value: 'Silver'
    },
    {
        key: 'Gold',
        value: 'Gold'
    }];
    isUserDeliveryManager: boolean = false;
    isDeliveryManagerOnly: boolean = false;
    isBookingConfirmArrival: boolean = true;
    loadingGates: boolean = true;
    active = 1;
    activeStage: number = 0;
    logoImgRetries: number = 5;
    isToday = false;
    deliveryInfo: any = {};
    startTimeSlots: Array<string> = [];
    startTime;
    block_booking: boolean = false;
    block_booking_type: string = null;
    block_booking_endDate: NgbDateStruct;
    weekDays = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
    dayOfWeek: string = null;
    declined_comment: string = null;
    bookingApprovalProcessStatus: boolean = false;
    pBookings = {};
    pBookingsGrouped = {};
    bookingGate: string = null;
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    isProjectPortal: boolean = false;
    downloadType: string;
    emptyStatePlaceholder = AssetsUrl.siteAdmin.transportManagementEmptyStatePlaceholder;
    saveBookingloader: boolean = false;
    selectedbookings: Array<Number> = [];
    allPBookingIds: Array<Number> = [];
    totalPendingBookings: number = 0;
    actionButtonMetaData = {
        actionList: [],
        class: 'material-symbols-outlined',
    };
    selectedUser: GateSupervisor | null = null;
    showModal: boolean = false;
    @ViewChild('pendingBookingsHtml') private pendingBookingsHtmlRef: IModalComponent;
    hasAllPendingBookingRef = null;
    minDate: NgbDateStruct = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
    bookingStatus: string = ''
    bookingStatuses = []
    isPdfDocument: boolean = false;
    META_VEHICLE_TYPES: Array<{
        key: string;
        label: string;
        heading: string;
        has_laden: boolean;
    }> = [];

    META_VEHICLE_EMISSIONS: Array<{
        label: string;
        laden: number;
        co2: number;
    }> = [];

    META_MATERIALS: Array<string> = [];
    META_UNITS: Array<string> = [];
    isNaN: Function = Number.isNaN;

    constructor(
        private activatedRoute: ActivatedRoute,
        private authService: AuthService,
        private projectService: ProjectService,
        private projectGateBookingService: ProjectGateBookingService,
        private calendar: NgbCalendar,
        public ngbMomentjsAdapter: NgbMomentjsAdapter,
        private modalService: NgbModal,
        private sanitizer: DomSanitizer,
        private timeUtility: TimeUtility,
        private httpService: HttpService,
        private toastService: ToastService,
        private resourceService: ResourceService,
    ) {
        this.selectedDay = this.calendar.getToday();
        this.exportBookingSelectedDay = this.calendar.getToday();
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent?.snapshot?.data?.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse?.project;
            this.projectId = this.projectInfo?.id;
        }
    }

    ngOnInit() {
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });
        this.initializeTable();
        if(this.booking.materials.length === 0) {
            this.booking.materials.push({
                material: "",
                quantity: "",
                //handling_requirements: null,
                notes: ""
            });
        }

        this.exportBookingLoader = true;
        this.resourceService.getInnDexSettingsByName(['vehicle_type_list', 'materials_en_gb']).subscribe(data => {
            this.exportBookingLoader = false;
            if (data && data.success) {
                if (data.settings) {
                    this.META_VEHICLE_TYPES = (data.settings.vehicle_type_list.types || []);
                    this.META_VEHICLE_EMISSIONS = (data.settings.vehicle_type_list.emissions || []);
                    this.META_MATERIALS = (data.settings.materials_en_gb.materials || []);
                    this.META_UNITS = (data.settings.materials_en_gb.units || []);
                }
            } else {
                const message = data.message || 'Failed to get vehicle types and materials.';
                this.toastService.show(this.toastService.types.ERROR, message);
                return;
            }
        })
    }

    ngOnChanges() {
        if (this.project_inducted_users?.length) {
            this.initializeGateSupervisors(this.project_inducted_users);
        }
        this.actionButtonMetaData.actionList = [
            {
                code: TransportManagementActionButtons.DECLINE_MULTIPLE_BOOKINGS,
                name: `Decline Multiple Bookings`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'cancel',
                enabled: (this.isUserDeliveryManager && this.bookingApprovalProcessStatus),
            },
            {
                code: TransportManagementActionButtons.GATE_SUPERVISORS,
                name: `Gate Supervisors`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'person',
                enabled: !this.isDeliveryManagerOnly,
            },
            {
                code: TransportManagementActionButtons.DOWNLOAD_BOOKING_LIST,
                name: `Download Booking List`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: !this.isDeliveryManagerOnly,
            },
        ];
    }

    initializeTable() {
        this.bookingApprovalProcessStatus = this.projectInfo.custom_field.booking_approval_process;
        //this.isUserDeliveryManager = this.projectInfo._my_flags && this.projectInfo._my_flags.is_delivery_manager;
        let designations = this.projectInfo._my_designations || [];
        let isDeliveryManager = designations.includes(UACProjectDesignations.DELIVERY_MANAGER);
        this.isUserDeliveryManager = this.projectInfo._my_flags?.is_delivery_manager === true && isDeliveryManager;
        this.isDeliveryManagerOnly = (designations.length === 1 && isDeliveryManager);
        this.timeSlots = this.timeUtility.generateTimeSlots(this.projectInfo.custom_field.default_booking_slot);
        this.startTimeSlots = this.timeSlots.map(t => t.split(" ")[0]);
        this.initializeProjectGates();

    }

    initializeProjectGates() {
        let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.apiRequestDateFormat);
        this.projectService.getGateByProject(this.projectId, selectedDate).subscribe((out:any) => {
            this.loadingGates = false;
            if(!out.success){
                const message = out.message || 'Failed to get project gates data.';
                this.toastService.show(this.toastService.types.ERROR, message);
            } else {
                this.availableGates = out.project_gates;
                this.initializeSlotBookingAndAvailability();
            }
        });
    }

    isPastTime(timeslot) {
        let selectedDateMoment = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay);
        let todayDateMoment = dayjs().startOf('day');
        if(selectedDateMoment.diff(todayDateMoment, 'day') === 0) {
            let tSlot = timeslot.substr(timeslot.length - 5);
            let tArray = tSlot.split(':');
            if(tArray.length < 2 || tSlot == "00:00") {
                tArray = ['23', '59']
            }
            let currentWindow = this.dayjs().minute(tArray[1]).hour(tArray[0]);
            return dayjs() < currentWindow;
        } else {
            return true;
        }
    }

    initializeSlotBookingAndAvailability() {
        this.selectedDayName = this.getDateInfo(this.selectedDay, 'ddd');
        this.selectedDateEpoch = this.getDateInfo(this.selectedDay, "X");
        this.currentBookingDate = this.getDateInfo(this.selectedDay, this.dateFormat);
        let selectedDateMoment = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay);
        let todayDateMoment = dayjs();
        if(selectedDateMoment.diff(todayDateMoment, 'day') < 0) {
            this.isPastDate = true;
        } else {
            this.isPastDate = false;
        }
        if(selectedDateMoment.diff(todayDateMoment, 'day') === 0) {
            this.isToday = true;
        } else {
            this.isToday = false;
        }
        this.getDeactivateTimeSlots();
    }

    initializeGateSupervisors(project_inducted_users) {
        if(this.projectInfo.gate_supervisors && this.projectInfo.gate_supervisors.length && project_inducted_users.length) {
            let gateSupervisors = (project_inducted_users || []).filter(user => this.projectInfo.gate_supervisors.includes(user.user_ref));
            gateSupervisors = [...new Map(gateSupervisors.map(item => [item['user_ref'], item])).values()];

            this.setSupervisorData(gateSupervisors);
        } else {
            this.gate_supervisors = [];
        }
    }

    getDeactivateTimeSlots() {
        this.deactivatedTimeSlots = this.timeSlots;
        let availableSlotsTimeSlots = [];
        let bookingsInPastDate = [];
        this.availableSlotsInPastDate = [];
        let bookingSlotsOfTheDay = [];
        this.availableGates.map(availableGate => {
            if (availableGate.time_slot[this.selectedDayName] && availableGate.time_slot[this.selectedDayName].length) {
                availableSlotsTimeSlots.push(...availableGate.time_slot[this.selectedDayName]);
            } else {
                //store bookings of removed slots to show those bookings
                let bookedSlotsInPastDate = (availableGate.project_gate_booking || []).filter(booking => {
                    if (this.changeDateToFrontendFormat(booking.booking_date) == this.currentBookingDate) {
                        this.availableSlotsInPastDate.push(...booking.booking_slots);
                        return true;
                    }
                });
                if (bookedSlotsInPastDate.length) {
                    bookingsInPastDate[availableGate.id] = bookedSlotsInPastDate;
                }
            }

            (availableGate.project_gate_booking || []).map((booking, index) => {
                booking.booking_date = this.changeDateToFrontendFormat(booking.booking_date);
                bookingSlotsOfTheDay.push(...booking.booking_slots);
                return booking;
            });
            return availableGate;
        });
        if (this.availableSlotsInPastDate.length && bookingsInPastDate.length) {
            this.availableSlotsInPastDate = this.availableSlotsInPastDate.filter((value, index, self) => self.indexOf(value) === index).sort((one, two) => (one < two ? -1 : 1));
        }

        if(availableSlotsTimeSlots.length) {
            availableSlotsTimeSlots = availableSlotsTimeSlots.filter((value, index, self) => self.indexOf(value) === index);
            this.deactivatedTimeSlots = this.deactivatedTimeSlots.filter(x => !availableSlotsTimeSlots.includes(x));
            bookingSlotsOfTheDay.sort((a, b) => Number(a.replace(" – ", "").replaceAll(":", "")) - Number(b.replace(" – ", "").replaceAll(":", "")));
            this.timeSlots = [...new Set([...this.timeSlots ,...bookingSlotsOfTheDay])]
            this.timeSlots = this.timeSlots.filter((value, index, self) => self.indexOf(value) === index);

            this.preComputeBookingStatus();
        }
    }

    getDateInfo(selectedDay, format) {
        return this.ngbMomentjsAdapter.ngbDateToDayJs(selectedDay).format(format);
    }

    onDateSelection(tempSelectedDay = null) {
        this.selectedDay = tempSelectedDay ?? this.selectedDay;
        //this.initializeProjectGates();
    }

    checkIsSlotSelected(gateDetail, timeSlot) {
        let selectedDayName = this.getDateInfo(this.selectedDay, 'ddd');
        if (gateDetail.time_slot[selectedDayName] !== undefined) {
            return gateDetail.time_slot[selectedDayName].includes(timeSlot);
        }
        return false;
    }

    checkIsSlotBooked(gateBooking, timeSlot) {
        let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        if (gateBooking.length) {
            for (let i in gateBooking) {
                if(gateBooking[i]['booking_date'] == selectedDate && gateBooking[i]['booking_slots'].includes(timeSlot)) {
                    return gateBooking[i]['booking_slots'].includes(timeSlot);
                }
            }
        }
        return false;
    }

    checkIsBookingApproved(gateBooking, timeSlot) {
        let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        let s = gateBooking.filter(r => r['booking_date'] == selectedDate && r['booking_slots'].includes(timeSlot));
        //console.log(s, s.find(a => a.approved))
        if(s.length) {
            let a = s.filter(b => b.status === 2);
            if(a.length) {
                //console.log("gateBooking", timeSlot, s)
                return true;
            }
        }
        return false;
    }

    isBookingSuccess(gateBooking, timeSlot) {
        let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        let bookingInfo = (gateBooking || []).find(booking => booking['booking_date'] == selectedDate && booking['booking_slots'].includes(timeSlot));
        if (bookingInfo && bookingInfo.id && bookingInfo.booking_closeout.hasOwnProperty('is_confirm_arrival')) {
            this.isBookingConfirmArrival = bookingInfo.booking_closeout.is_confirm_arrival;
            return true;
        }
        return false;
    }

    // getbookingTitle(gateBooking, timeSlot) {
    //     let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
    //     let bookingInfo;
    //     if(this.bookingApprovalProcessStatus) {
    //         bookingInfo = (gateBooking || []).find(booking => booking['booking_date'] == selectedDate && booking['booking_slots'].includes(timeSlot) && booking.status === 2);
    //     } else {
    //         bookingInfo = (gateBooking || []).find(booking => booking['booking_date'] == selectedDate && booking['booking_slots'].includes(timeSlot));
    //     }
    //     let title = bookingInfo.created_by_user.user_emp.employer;
    //     return title;
    // }

    getbookingTitle(gateBooking: any[], timeSlot: string): string {
        const selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        const bookingInfo = gateBooking?.find(booking => booking.booking_date === selectedDate && booking.booking_slots.includes(timeSlot) && (this.bookingApprovalProcessStatus ? booking.status === 2 : true));
        const title = bookingInfo?.created_by_user?.user_emp?.employer ?? '';
        return title;
    }

    getbookingSupplier(gateBooking, timeSlot, isToolTip?: boolean) {
        let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        let bookingInfo;
        if(this.bookingApprovalProcessStatus) {
            bookingInfo = (gateBooking || []).find(booking => booking['booking_date'] == selectedDate && booking['booking_slots'].includes(timeSlot) && booking.status === 2);
        } else {
            bookingInfo = (gateBooking || []).find(booking => booking['booking_date'] == selectedDate && booking['booking_slots'].includes(timeSlot));
        }
        let supplier = bookingInfo.supplier ? (!isToolTip ? " - " : '') + bookingInfo.supplier : '';
        return supplier;
    }

    resetFrom() {
        this.booking.booking_ref = null;
        this.booking.supplier = null;
        this.booking.contact_name = null;
        this.booking.fors_no = null;
        this.booking.fors_no_badge = null;
        this.booking.vehicle_reg = null;
        this.booking.vehicle_make_model = null;
        this.booking.driver_name = null;
        this.booking.driver_number = null;
        this.booking.drop_off_address = null;
        this.booking.waste_collection_company = null;
        this.booking.materials = [];
        this.booking.haulage_company = null;
        this.booking.dispatch_post_code = null;
        this.booking.return_postcode = null;
        this.booking.laden_percent = null;
        this.booking.co2_emission_gm = 0;
        this.booking._same_dispatch_return_postcode = undefined;
    }

    disableForPendingBookings: boolean = false;

    @ViewChild('bookingPopupRef') private bookingPopupRef: IModalComponent;
    slotClicked(event, gateDetail, timeSlots, bookingObj, pendingBooking = false) {
        if(event) {
            event.closeFn();
        }
        //Reset form START
        this.selectedGateDetail = gateDetail;
        this.activeStage = 0;
        this.booking = new ProjectGateBooking;
        this.booking.project_id = this.projectId;
        this.booking.user_id = this.authUser$.id;
        this.booking.booking_slots = [];
        this.disableForPendingBookings = false;
        if(pendingBooking) {
            this.booking.booking_slots = bookingObj.booking_slots;
            this.booking.booking_type = bookingObj.booking_type;
            this.booking.duration = bookingObj.duration;
            this.bookingSlotsString = this.bookingSlotsToDisplay(bookingObj.booking_slots);
            this.gate_name = bookingObj.gate_name;
            this.booking.gate_id = bookingObj.gate_id;
            this.disableForPendingBookings = true;
        } else {
            this.booking.duration = `00:${this.projectInfo.custom_field.default_booking_slot} minutes`;
            this.booking.booking_type = 'delivery';
            this.booking.booking_slots.push(timeSlots);
            this.bookingSlotsString = this.bookingSlotsToDisplay(this.booking.booking_slots);
            this.gate_name = gateDetail.gate_name;
            this.booking.gate_id = gateDetail.id;
        }
        this.booking.booking_date = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        this.minDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(this.booking.booking_date, this.dateFormat));

        this.dayOfWeek = this.weekDays[dayjs(this.changeDateToFrontendFormat(this.booking.booking_date), this.dateFormat).day()];
        this.showModal = true;
        //Reset form END
        this.bookingPopupRef.open();
    }

    pendingGateBookings: any = [];
    approvedBookingAvailable: boolean = false;
    pendingGateDetail;
    pendingTimeSlot;
    pendingGateIndex: number = null;

    getPendingBookings(gateDetail, timeSlot) {
        this.pendingGateBookings = [];
        this.selectedGateDetail = gateDetail;
        let gateBooking = gateDetail.project_gate_booking;
        let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        this.pendingGateBookings = gateBooking.filter(r => this.changeDateToFrontendFormat(r.booking_date) === selectedDate && r.booking_slots.includes(timeSlot));
        this.approvedBookingAvailable = false;

        console.log("pendingGateBookings", this.pendingGateBookings)
        for (let i in gateBooking) {
            if(this.changeDateToFrontendFormat(gateBooking[i]['booking_date']) == selectedDate) {
                if (gateBooking[i]['booking_slots'].includes(timeSlot)) {
                    this.booking = this.httpService.parseJson(JSON.stringify(gateBooking[i]));
                    this.booking._same_dispatch_return_postcode = !!(this.booking.dispatch_post_code === this.booking.return_postcode);
                    if(this.booking.delivery_notes_ref) {
                        this.deliveryInfo = this.booking.delivery_notes_ref;
                    }
                    if(this.booking.status === 2) {
                        this.approvedBookingAvailable = true;
                    }
                    this.bookingSlotsString = this.bookingSlotsToDisplay(this.booking.booking_slots);
                    this.startTime = timeSlot.split(" ")[0];
                    break;
                }
            }
        }
    }

    pendingBookings(gateDetail, timeSlot, gateIndex) {
        console.log("Gate Index", gateIndex);
        this.pendingGateDetail = gateDetail;
        this.pendingTimeSlot = timeSlot;
        this.pendingGateIndex = gateIndex;
        this.getPendingBookings(gateDetail, timeSlot);
        this.pendingBookingsHtmlRef.open();
    }

    editSlotBooking(bookedSlotOptionsHtml, gateDetail, timeSlot) {
        this.activeStage = 0;
        this.selectedGateDetail = gateDetail;
        let gateBooking = gateDetail.project_gate_booking;
        let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        let bookingInfo;
        if(this.bookingApprovalProcessStatus) {
            bookingInfo = (gateBooking || []).find(booking => this.changeDateToFrontendFormat(booking['booking_date']) == selectedDate && booking['booking_slots'].includes(timeSlot) && booking.status === 2);
        } else {
            bookingInfo = (gateBooking || []).find(booking => this.changeDateToFrontendFormat(booking['booking_date']) == selectedDate && booking['booking_slots'].includes(timeSlot));
        }
        this.booking = this.httpService.parseJson(JSON.stringify(bookingInfo));
        this.booking._same_dispatch_return_postcode = !!(this.booking.dispatch_post_code === this.booking.return_postcode);
        if(this.booking.delivery_notes_ref) {
            this.deliveryInfo = this.booking.delivery_notes_ref;
        }
        this.bookingSlotsString = this.bookingSlotsToDisplay(this.booking.booking_slots);
        this.startTime = timeSlot.split(" ")[0];

        let selectedDateMoment = dayjs(selectedDate, this.dateFormat);
        this.canUserAmendAndDeleteBooking = (selectedDateMoment.diff(dayjs(), 'day') < 0) ? false : true;
        const modalRef = this.openModal(bookedSlotOptionsHtml, 'sm');
    }


    allPendingBookings = [];
    groupByKey = function(xs, key) {
        return xs.reduce(function(rv, x) {
          (rv[x[key]] = rv[x[key]] || []).push(x);
          return rv;
        }, {});
    };

    @ViewChild('allPendingBookingsRef') private allPendingBookingsRef: IModalComponent;
    viewAllPendingBookingsPopup() {
        let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        for(let gate of this.availableGates) {
            let gateBooking = gate.project_gate_booking;
            this.pBookings[gate.gate_name] = [];
            let todayBooks = gateBooking.filter(r => this.changeDateToFrontendFormat(r.booking_date) === selectedDate);
            todayBooks = todayBooks.map((b, index) => {
                return {...b, 'gate': gate.gate_name}
            });
            if(todayBooks.length) {
                this.allPendingBookings.push(...todayBooks);
            }
            let result1 = this.groupByKey(todayBooks,'booking_slots');
            this.pBookingsGrouped[gate.gate_name] = result1;
            for(let i of Object.keys(result1)) {
                this.pBookings[gate.gate_name].push(...result1[i]);
            }

        }
        this.hasAllPendingBookingRef = this.allPendingBookingsRef.open();
    }

    addBookingClick(event) {
        event.closeFn();
    }

    checkIfBookableForAllPendingBookingsPopup(bookingObj) {
        if(bookingObj.status === 0 || bookingObj.status === 2) {
            return false;
        }
        let allBookingsForSlot = this.pBookingsGrouped[bookingObj.gate][bookingObj.booking_slots];
        for(let b of allBookingsForSlot) {
            if(b.status === 2) {
                return false;
            }
        }
        return true;
    }

    getColorByStatus(status: number): string | null {
        switch (status) {
          case 2:
            return '#00992b';
          case 1:
            return '#edb531';
          default:
            return null;
        }
      }
      
    getBookingStatus(bookingStatus) {
        if(bookingStatus === 0) {
            return 'Declined';
        } else if(bookingStatus === 2) {
            return 'Approved';
        } else {
            return 'Pending';
        }
    }
    preComputeBookingStatus() {
        this.bookingStatuses = this.availableGates.map(gate => {
            return this.timeSlots.map(timeSlot => {
                return this.allBookingsDeclined(gate, timeSlot) ? 'Available' : 'Pending';
            });
        });
    }

    allBookingsDeclined(gateDetail, timeSlot) {
        const gateBooking = gateDetail.project_gate_booking;
        const selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        const availableBookings = (gateBooking || []).filter(booking =>
            booking['booking_date'] == selectedDate &&
            booking['booking_slots'].includes(timeSlot) &&
            booking.status === 1
        );
        
        return availableBookings.length === 0;
    }

    tabChange(id) {
        this.resetFrom();
        if(id===1) {
            this.booking.booking_type = 'delivery'
        } else {
            this.booking.booking_type = 'collection';
        }
    }

    @ViewChild('viewBookedSlotRef') private viewBookedSlotRef: IModalComponent;
    viewBookingSuccess(gateDetail, timeSlot) {
        this.bookingInProgress = true;
        this.selectedGateDetail = gateDetail;
        let gateBooking = gateDetail.project_gate_booking;
        let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        this.booking = (gateBooking || []).find(booking => this.changeDateToFrontendFormat(booking['booking_date']) == selectedDate && booking['booking_slots'].includes(timeSlot));
        this.bookingSlotsString = this.bookingSlotsToDisplay(this.booking.booking_slots);
        this.calculateDistanceTravelled();
        this.viewBookedSlotRef.open();
        this.bookingInProgress = false;
    }

    getNameEmployerSupplierForToolTip(gateDetail, timeSlot) {
        let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        let booking = (gateDetail.project_gate_booking || []).find(booking => this.changeDateToFrontendFormat(booking['booking_date']) == selectedDate && booking['booking_slots'].includes(timeSlot));
        return booking.id 
        ? 'Booked By: ' + 
            (this.getUserFullName(booking.created_by_user) + 
            ' (' + booking.created_by_user?.user_emp?.employer + ')' +
            (booking.booking_type === "delivery" // Show supplier in case of delivery only
              ? '\nSupplier: ' + this.getbookingSupplier(gateDetail.project_gate_booking, timeSlot, true) 
              : '')
            ) 
        : '';
    }

    viewBooking(cb) {
        this.bookingInProgress = true;
        let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        let selectedDateMoment = dayjs(selectedDate, this.dateFormat);
        this.canUserAmendAndDeleteBooking = (selectedDateMoment.diff(dayjs(), 'day') < 0) ? false : true;
        this.calculateDistanceTravelled();
        this.viewBookedSlotRef.open();
        this.bookingInProgress = false;
        cb();
    }

    viewPendingBooking(pendingBooking) {
        if(this.hasAllPendingBookingRef) {
            this.allPendingBookingsRef.close();
            this.hasAllPendingBookingRef = null;
        }
        this.bookingInProgress = true;
        this.booking = pendingBooking;
        this.booking._same_dispatch_return_postcode = !!(this.booking.dispatch_post_code === this.booking.return_postcode);
        let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        let selectedDateMoment = dayjs(selectedDate, this.dateFormat);
        this.canUserAmendAndDeleteBooking = (selectedDateMoment.diff(dayjs(), 'day') < 0) ? false : true;
        this.calculateDistanceTravelled();
        this.viewBookedSlotRef.open();
        this.bookingInProgress = false;
    }

    calculateDistanceTravelled() {
        let dispatch_distance_travelled = (this.booking.dispatch_distance_matrix && this.booking.dispatch_distance_matrix.distance && this.booking.dispatch_distance_matrix.distance.value) ? (+this.booking.dispatch_distance_matrix.distance.value / 1000) : 0;
        let return_distance_travelled = (this.booking.return_distance_matrix && this.booking.return_distance_matrix.distance && this.booking.return_distance_matrix.distance.value) ? (+this.booking.return_distance_matrix.distance.value / 1000) : 0;
        this.booking.distance_travelled = Math.floor(dispatch_distance_travelled + return_distance_travelled) || null;
    }

    amendBooking(cb) {
        if(this.booking.booking_type === 'delivery') {
            this.active = 1;
        } else {
            this.active = 2;
        }
        this.activeStage = 0;
        this.showModal = true;
        this.bookingPopupRef.open();
        cb();
    }

    deleteBooking(cb, bookingObj = {}, pendingBookingDelete = false) {
        if(this.booking.id){
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Delete Booking',
                title: `Are you sure you would like to delete this booking?`,
                confirmLabel: 'Delete',
                onConfirm: () => {
                    let bookingId = this.booking.id;
                    let emailInfo:any = {};
                    emailInfo.user_id = this.booking.user_id;
                    emailInfo.project_name = this.projectInfo.name;
                    emailInfo.nominated_manager_name = this.authUser$.first_name+' '+this.authUser$.last_name;
                    emailInfo.booking_date = this.changeDateToFrontendFormat(this.booking.booking_date);
                    emailInfo.booking_slot = (this.bookingSlotsToDisplay(this.booking.booking_slots)).toString();
                    emailInfo.gate_name = this.selectedGateDetail.gate_name;
                    this.bookingInProgress = true;
                    this.projectGateBookingService.deleteBooking(this.projectId, bookingId, emailInfo).subscribe((data: any) => {
                    this.bookingInProgress = false;
                    if(data && data.success) {
                        const message = 'Booking deleted successfully.'
                        this.toastService.show(this.toastService.types.SUCCESS, message);
                        this.initializeProjectGates();
                    } else {
                        const message = data.message || 'Failed to fetch detail.';
                        this.toastService.show(this.toastService.types.ERROR, message);
                    }
                    });
                    cb();
                    return;
                }
            });
        }
    }

    @ViewChild('declineBookingFormRef') private declineBookingFormRef: IModalComponent;
    pendingBookingsModalRef = null;
    rejectBookingPopup(bookingObj: any = {}) {
        this.booking = bookingObj;
        this.bookingGate = bookingObj.gate? bookingObj.gate : null;
        if(this.booking.id) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Decline Booking',
                title: `Are you sure you want to decline this booking?`,
                confirmLabel: 'Decline',
                onConfirm: () => {
                    this.declineBookingFormRef.open();
                }
            });
        }
    }

    rejectBooking(event) {
        let reqData: any = {};
        this.bookingInProgress = true;
        let bookingId = this.booking.id;
        reqData.declined_comment = this.declined_comment;
        reqData.booking_date = this.changeDateToFrontendFormat(this.booking.booking_date);
        reqData.booking_slot = this.bookingSlotsToDisplay(this.booking.booking_slots);
        reqData.admin_name = this.authUser$.name;
        reqData.supplier = this.booking.supplier;
        reqData.project = this.projectInfo.name;
        reqData.userId = this.booking.created_by_user.id;
        reqData.status = 0;
        this.projectGateBookingService.updateBookingStatus(this.projectId, bookingId, reqData).subscribe((data: any) => {
            this.bookingInProgress = false;
            if(data && data.success) {
                this.pendingGateBookings.map(b => {
                    if(b.id === bookingId) {
                        b.status = reqData.status;
                    }
                    return b;
                });
                if(this.bookingGate) {
                    this.pBookings[this.bookingGate].map(b => {
                        if(b.id === bookingId) {
                            b.status = reqData.status;
                        }
                        return b;
                    });
                }
            } else {
                const message = data.message || 'Failed to fetch detail.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
            event.closeFn();
        });
    }

    approveBooking(bookingObj) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Approve Booking',
            title: `Are you sure you want to approve this booking?`,
            confirmLabel: 'Approve',
            onConfirm: () => {
                this.booking = bookingObj;
                let gate = bookingObj.gate? bookingObj.gate : null;
                this.bookingInProgress = true;
                let reqData: any = {};
                let bookingId = bookingObj.id;
                reqData.booking_date = this.changeDateToFrontendFormat(this.booking.booking_date);
                reqData.booking_slot = this.bookingSlotsToDisplay(this.booking.booking_slots);
                reqData.admin_name = this.authUser$.name;
                reqData.supplier = this.booking.supplier;
                reqData.project = this.projectInfo.name;
                reqData.userId = this.booking.created_by_user.id;
                reqData.status = 2;
                this.projectGateBookingService.updateBookingStatus(this.projectId, bookingId, reqData).subscribe((data: any) => {
                    this.bookingInProgress = false;
                    if(data && data.success) {
                        const message = 'Booking request has been accepted successfully.';
                        this.toastService.show(this.toastService.types.SUCCESS, message);
                        this.approvedBookingAvailable = true;
                        this.pendingGateBookings.map(b => {
                            if(b.id === bookingId) {
                            b.status = reqData.status;
                        }
                        return b;
                    });
                    if(gate) {
                        this.pBookings[gate].map(b => {
                            if(b.id === bookingId) {
                                b.status = reqData.status;
                            }
                            return b;
                        });
                    }
                    } else {
                        const message = data.message || 'Failed to fetch detail';
                        this.toastService.show(this.toastService.types.ERROR, message);
                    }
                });
            }
        });
    }

    isNotThisStage(label) {
        let index = this.formStages.findIndex(v => v === label);
        return this.activeStage !== index
    }

    movePrevForm() {
        if (this.activeStage === 0) { // i would become 0
            this.activeStage = this.formStages.length; // so put it at the other end of the array
        }
        this.activeStage = this.activeStage - 1; // decrease by one
        this.scrollToTop();
        return this.formStages[this.activeStage]; // give us back the item of where we are now
    }

    moveNextForm() {
        this.activeStage = this.activeStage + 1; // increase i by one
        this.activeStage = this.activeStage % this.formStages.length; // if we've gone too high, start from `0` again
        this.scrollToTop();
        return this.formStages[this.activeStage]; // give us back the item of where we are now
    }

    scrollToTop() {
        let element = document.querySelector('#modalTop');
        if (element) {
            element.scrollIntoView({block: "center", inline: "nearest"});
        }
    }

    addMaterialRow() {
        if(!this.booking.materials) {
         this.booking.materials = [];
        }

         this.booking.materials.push({
            material: "",
            quantity: "",
            //handling_requirements: null,
            notes: ""
        });
    }

    removeMaterialRow(e: any, i: number) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Remove Material',
            title: `Are you sure you want to remove this material?`,
            confirmLabel: 'Remove',
            onConfirm: () => {
                this.booking.materials.splice(i, 1);
            }
        });
    }

    increaseStartTime() {
        let duration = this.booking.duration;
        let firstSlotIndex = this.startTimeSlots.indexOf(this.startTime);
        let newSlotIndex = firstSlotIndex + 1;
        let isConsecutiveSlot = false;
        if (this.startTimeSlots.length !== newSlotIndex) {
            let nextStartSlot = this.startTimeSlots[newSlotIndex];
            let nextSlot = this.timeSlots[newSlotIndex];
            this.startTime = nextStartSlot;
            let bookingsOfTheDay;
            if(this.booking.booking_slots.length === 1) {
                bookingsOfTheDay = (this.selectedGateDetail.project_gate_booking || []).filter(booking => (this.currentBookingDate === this.changeDateToFrontendFormat(booking.booking_date) && (booking.booking_slots || []).includes(nextSlot)));
                if (this.selectedGateDetail.time_slot[this.selectedDayName].includes(nextSlot) && !bookingsOfTheDay.length) {
                    isConsecutiveSlot = true;
                    this.booking.booking_slots = [nextSlot];
                    this.bookingSlotsString = this.bookingSlotsToDisplay(this.booking.booking_slots);
                }

            } else {
                let lastSlotIndex = newSlotIndex + this.booking.booking_slots.length;
                let newSlots = this.timeSlots.slice(newSlotIndex, lastSlotIndex);
                let passThrough = true;
                for(let slot of newSlots) {
                    bookingsOfTheDay = (this.selectedGateDetail.project_gate_booking || []).filter(booking => (this.currentBookingDate === this.changeDateToFrontendFormat(booking.booking_date) && booking.id != this.booking.id && (booking.booking_slots || []).includes(slot)));
                    if(!(this.selectedGateDetail.time_slot[this.selectedDayName].includes(slot) && !bookingsOfTheDay.length)) {
                        passThrough = false;
                        break;
                    }
                }
                if(passThrough) {
                    isConsecutiveSlot = true;
                    this.booking.booking_slots = newSlots;
                    this.bookingSlotsString = this.bookingSlotsToDisplay(this.booking.booking_slots);
                }
            }
        }
        if (!isConsecutiveSlot) {
            const message = 'Updated time slot is not available.';
            this.toastService.show(this.toastService.types.INFO, message);
        }
    }

    decreaseStartTime() {
        let duration = this.booking.duration;
        let firstSlotIndex = this.startTimeSlots.indexOf(this.startTime);
        let newSlotIndex = firstSlotIndex - 1;
        let isConsecutiveSlot = false;
        if (this.isPastTime(this.timeSlots[newSlotIndex]) && newSlotIndex >= 0) {
            let nextStartSlot = this.startTimeSlots[newSlotIndex];
            let nextSlot = this.timeSlots[newSlotIndex];
            this.startTime = nextStartSlot;
            let bookingsOfTheDay;
            if(this.booking.booking_slots.length === 1) {
                bookingsOfTheDay = (this.selectedGateDetail.project_gate_booking || []).filter(booking => (this.currentBookingDate === this.changeDateToFrontendFormat(booking.booking_date) && (booking.booking_slots || []).includes(nextSlot)));
                if (this.selectedGateDetail.time_slot[this.selectedDayName].includes(nextSlot) && !bookingsOfTheDay.length) {
                    isConsecutiveSlot = true;
                    this.booking.booking_slots = [nextSlot];
                    this.bookingSlotsString = this.bookingSlotsToDisplay(this.booking.booking_slots);
                }

            } else {
                let lastSlotIndex = newSlotIndex + this.booking.booking_slots.length;
                if(newSlotIndex >=0) {
                    let newSlots = this.timeSlots.slice(newSlotIndex, lastSlotIndex);
                    let passThrough = true;
                    for(let slot of newSlots) {
                        bookingsOfTheDay = (this.selectedGateDetail.project_gate_booking || []).filter(booking => (this.currentBookingDate === this.changeDateToFrontendFormat(booking.booking_date) && booking.id != this.booking.id && (booking.booking_slots || []).includes(slot)));
                        if(!(this.selectedGateDetail.time_slot[this.selectedDayName].includes(slot) && !bookingsOfTheDay.length)) {
                            passThrough = false;
                            break;
                        }
                    }
                    if(passThrough) {
                        isConsecutiveSlot = true;
                        this.booking.booking_slots = newSlots;
                        this.bookingSlotsString = this.bookingSlotsToDisplay(this.booking.booking_slots);
                    }
                }
            }
        }
        if (!isConsecutiveSlot) {
            const message = 'Updated time slot is not available.';
            this.toastService.show(this.toastService.types.INFO, message);
        }
    }

    increaseDuration() {
        let duration = this.booking.duration;
        let lastSlot = this.booking.booking_slots[this.booking.booking_slots.length-1];
        let lastSlotIndex = this.timeSlots.indexOf(lastSlot);
        if(this.projectInfo.custom_field.maximum_booking_status && (this.projectInfo.custom_field.maximum_booking_time === duration)) {
            let msg = 'Maximum booking slot = ' + duration;
            this.toastService.show(this.toastService.types.INFO, msg);
            return;
        }
        let isConsecutiveSlot = false;
        if (this.timeSlots.length !== lastSlotIndex+1) {
            let nextSlot = this.timeSlots[lastSlotIndex + 1];
            console.log("nextSlot ", nextSlot);
            let bookingsOfTheDay = (this.selectedGateDetail.project_gate_booking || []).filter(booking => (this.currentBookingDate === this.changeDateToFrontendFormat(booking.booking_date) && (booking.booking_slots || []).includes(nextSlot)));

            if (this.selectedGateDetail.time_slot[this.selectedDayName].includes(nextSlot) && !bookingsOfTheDay.length) {
                isConsecutiveSlot = true;
                this.booking.booking_slots.push(nextSlot);
                this.bookingSlotsString = this.bookingSlotsToDisplay(this.booking.booking_slots);
                let defaultTimeSlot = this.projectInfo.custom_field.default_booking_slot;
                this.booking.duration = this.formatDuration(this.booking.booking_slots.length * defaultTimeSlot);
            }
        }
        if (!isConsecutiveSlot) {
            const message = 'No consecutive time slot available.';
            this.toastService.show(this.toastService.types.INFO, message);
        }
    }

    decreaseDuration() {
        let duration = this.booking.duration;
        let defaultTimeSlot = this.projectInfo.custom_field.default_booking_slot;
        if ((duration != '00:30 minutes' && defaultTimeSlot == 30) || (duration != '00:15 minutes' && defaultTimeSlot == 15)) {
            this.booking.booking_slots.pop();
            this.booking.duration = this.formatDuration(this.booking.booking_slots.length * defaultTimeSlot);
            this.bookingSlotsString = this.bookingSlotsToDisplay(this.booking.booking_slots);
        }
    }

    formatDuration(minutes) {
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        let formattedDuration = hours.toString().padStart(2, '0');
        formattedDuration += ':' + remainingMinutes.toString().padStart(2, '0');
        let durationTitle= hours > 0 ? ( (hours == 1 && remainingMinutes == 0) ? ' hour' :' hours') : ' minutes';
        formattedDuration += durationTitle;
        return formattedDuration;
    }

    saveBooking(form: any, event) {
        if (!form.valid) {
            const message = 'Something went wrong, form data is not valid.';
            this.toastService.show(this.toastService.types.INFO, message, { data: form.errors });
            return false;
        }
        this.saveBookingloader = true;
        if(!this.booking.amendment_log) {
            this.booking.amendment_log = [];
        }

        if(this.booking.id) {
            //let userName = this.authUser$.first_name+' '+this.authUser$.last_name;
            // let amendmentMsg = 'Booking was amended by '+this.authUser$.name+' on '+dayjs().format("DD-MM-YYYY HH:mm");
            let amendmentMsg = { amended: dayjs().format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS), amendedBy: this.authUser$.name};
            this.booking.amendment_log.push(amendmentMsg);
            //if user is nominated manager
            let emailInfo:any = {};
            emailInfo.user_id = this.booking.user_id;
            emailInfo.project_name = this.projectInfo.name;
            emailInfo.nominated_manager_name = this.authUser$.first_name+' '+this.authUser$.last_name;
            emailInfo.booking_date = this.changeDateToFrontendFormat(this.booking.booking_date);
            emailInfo.booking_details = this.booking.booking_details;
            emailInfo.booking_slot = (this.bookingSlotsToDisplay(this.booking.booking_slots)).toString();
            emailInfo.gate_name = this.selectedGateDetail.gate_name;


            if(this.deliveryInfo.id || this.deliveryInfo.delivery_ref_no || this.deliveryInfo.po_number) {
                this.deliveryInfo.dn_supplier = this.booking.supplier;
                this.deliveryInfo.project_ref = this.projectId;
                this.deliveryInfo.dn_location = this.selectedGateDetail.gate_name;
                this.deliveryInfo.dn_delivered_on = dayjs(this.changeDateToBackendFormat(this.booking.booking_date), this.dateStorageFormat).valueOf();
                this.deliveryInfo.data_type = 'delivery-note';
            }

            let req = Object.assign({}, this.booking);
            req.booking_date = this.changeDateToBackendFormat(this.booking.booking_date);
            // update booking
            this.projectGateBookingService.updateBooking(this.projectId, req, emailInfo, this.deliveryInfo).subscribe(data =>{
                if(data && data.success && data.booking.id) {
                    this.initializeProjectGates();
                    event.closeFn();
                } else {
                    const message = 'Booking request failed. Invalid request.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                }
                this.saveBookingloader = false;

            });
            return;
        }

        //email to delivery manager
        let emailInfo:any = {};
        emailInfo.user_id = this.booking.user_id;
        emailInfo.project_name = this.projectInfo.name;
        emailInfo.booking_date = this.changeDateToFrontendFormat(this.booking.booking_date);
        emailInfo.booking_slot = (this.bookingSlotsToDisplay(this.booking.booking_slots)).toString();
        emailInfo.booking_details = this.booking.booking_details;
        emailInfo.gate_name = this.selectedGateDetail.gate_name;


        if(this.deliveryInfo.delivery_ref_no || this.deliveryInfo.po_number) {
            this.deliveryInfo.dn_supplier = this.booking.supplier;
            this.deliveryInfo.project_ref = this.projectId;
            this.deliveryInfo.dn_location = this.selectedGateDetail.gate_name;
            this.deliveryInfo.dn_delivered_on = dayjs(this.changeDateToBackendFormat(this.booking.booking_date), this.dateStorageFormat).valueOf();
            this.deliveryInfo.data_type = 'delivery-note';
        }
        if(this.block_booking) {
            this.deliveryInfo.block_booking_type = this.block_booking_type;
            this.deliveryInfo.block_booking_endDate = this.block_booking_endDate;
            let bDate = dayjs(this.changeDateToBackendFormat(this.booking.booking_date), this.dateStorageFormat);
            let endDate = this.block_booking_endDate ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.block_booking_endDate) : null;
            let blockDates = [];
            let blockBookingInfo = null;
            let bookedFrom = null
            if(this.block_booking_type === 'daily') {
                let newDay = bDate.add(dayjs.duration({'days' : 1}));
                while(newDay <= endDate) {
                    if(this.checkIfBookable(newDay, this.booking.booking_slots, this.weekDays[newDay.day()])) {
                        blockDates.push(newDay.format(this.dateFormat));
                    }
                    newDay = newDay.add(dayjs.duration({'days' : 1}));
                }
                bookedFrom = "Daily"
                blockBookingInfo = bookedFrom + " from " + bDate.format(this.dateStorageFormat) + ' - ' + endDate.format(this.dateStorageFormat);
            } else {
                let newDay = bDate.add(1, 'week');
                while(newDay <= endDate) {
                    if(this.checkIfBookable(newDay, this.booking.booking_slots, this.dayOfWeek)) {
                        blockDates.push(newDay.format(this.dateFormat));
                    }
                    newDay = newDay.add(1, 'week');
                }
                bookedFrom = "Weekly"
                blockBookingInfo = bookedFrom + " from " + bDate.format(this.dateStorageFormat) + ' - ' + endDate.format(this.dateStorageFormat);
            }
            this.booking.blockDates = blockDates;
            this.booking.block_dates_info = {
                "info": blockBookingInfo,
                "frequency": bookedFrom.toLowerCase(),
                "startDate": bDate.format(this.dateFormat),
                "endDate": endDate.format(this.dateFormat)
            }
        }

        let req = Object.assign({}, this.booking);
        req.booking_date = this.changeDateToBackendFormat(this.booking.booking_date);
        // create booking
        this.projectGateBookingService.createBooking(this.projectId, req, emailInfo, this.deliveryInfo).subscribe(data =>{
            if(data && data.success && data.booking.id) {
                this.booking = data.booking;
                if(data.booking.status === 1) {
                    const message = `Your ${this.booking.booking_type} for ${this.changeDateToFrontendFormat(this.booking.booking_date)} @ ${this.bookingSlotsToDisplay(this.booking.booking_slots)} is now pending. It will be reviewed before being confirmed.`;
                    this.toastService.show(this.toastService.types.SUCCESS, message);
                } else {
                    const message = `Your ${this.booking.booking_type} for ${this.changeDateToFrontendFormat(this.booking.booking_date)} @ ${this.bookingSlotsToDisplay(this.booking.booking_slots)} is now confirmed.`;
                    this.toastService.show(this.toastService.types.SUCCESS, message);
                }
                this.initializeProjectGates();
                event.closeFn();
            } else {
                const message = 'Booking request failed. Invalid request.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
            this.saveBookingloader = false;
        });

    }

    checkIfBookable(bookingDate, nextSlot, dayName) {
        dayName = bookingDate.format('ddd');
        let bookingsOfTheDay = (this.selectedGateDetail.project_gate_booking || []).filter(booking => (bookingDate === this.changeDateToFrontendFormat(booking.booking_date) && (booking.booking_slots || []).includes(nextSlot)));
        if (this.selectedGateDetail.time_slot[dayName] && this.selectedGateDetail.time_slot[dayName].includes(...nextSlot) && !bookingsOfTheDay.length) {
            return true;
        }
        return false;
    }

    responseHandler(out: any) {
        if(out.success) {
            // After edit reload the page
            window.location.reload();
            //this.router.navigate(['/project-portal/project/'+this.booking.project_id+'/delivery-management']);
        } else {
            const message = out.message || 'Failed to store data.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: out });
        }
    }

    trackByRowIndex(index: number, obj: any) {
        return index;
    }

    @ViewChild('routeMapHtml') private routeMapModalRef: IModalComponent;
    gateRouteMap(gateDetail) {
        this.selectedGateDetail = gateDetail;
        if (this.selectedGateDetail.route_map_file_id && (typeof this.selectedGateDetail.route_map_file_id === 'object')) {
            this.selectedGateDetail.route_map_file_url = this.selectedGateDetail.route_map_file_id.file_url;
        }
        this.isPdfDocument = this.isPDF(this.selectedGateDetail.route_map_file_url);
        this.routeMapModalRef.open();
    }

    onLogoError($img:any, targetSrc:any){
        $img.src = '/images/project-placeholder.png';
        if(targetSrc && targetSrc.length && this.logoImgRetries){
            setTimeout(() => {
                this.logoImgRetries--;
                $img.src = targetSrc;
            }, 2000);
        }
    }

    onLogoLoad($img){
        this.logoImgRetries = 2;
    }

    isPDF(url) {
        if (url && url.split('.').pop() && url.split('.').pop().toLowerCase() === 'pdf') {
            this.previewURL = this.sanitizer.bypassSecurityTrustResourceUrl(url + '?embed=frame');
            return true;
        }
        return false;
    }

    bookingSlotsToDisplay(booking_slots) {
        //get start time and end time from all selected slots
        if (booking_slots.length > 1) {
            let prePart = booking_slots[0].split('–');
            let postPart = booking_slots[booking_slots.length-1].split('–').pop();
            return prePart[0].trim()+' – '+postPart.trim();
        }
        return booking_slots;
    }

    updateSidebarLoadingStatus($event) {
        this.sidebarInProgress = $event;
    }

    openModal(content, size, centered = false){
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size,
            centered: centered,
            windowClass: 'modal_v2',
        });
    }

    checkIfAlreadyExist(index, emailText) {
        console.log('emailText', emailText, 'index', index);
        let list = this.gate_supervisors;
        let alreadyRecord = (list || []).find((r, i) => i !== index && ((r.id ? (r.user_ref && r.user_ref.email) : r._email) || '').toLowerCase() === emailText.toLowerCase());
        if (alreadyRecord) {
            const message = `Looks like you are making duplicate entry for ${emailText}, please check.`;
            this.toastService.show(this.toastService.types.ERROR, message);
            return false;
        }
        return true;
    }

    onChangeUser(event) {
        console.log(event);

        if(!event){
            this.selectedUser = null;
        }
    }

    addSupervisorRow(event) {
        if(event) {
            this.gate_supervisors.push({
                _email : event.email,
                _name : event.name,
                user_ref : event.user_ref,
                id : event.id
            });
            this.saved_gate_supervisor_ids = this.gate_supervisors.map(sup => sup.user_ref);
            this.selectedUser = null;
        }
    }

    removeSupervisorRow(id: any, i: number, supervisorName: string){
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Remove Supervisor',
            title: `Are you sure you want to remove ${(supervisorName) ? '<span class="fw-500">'+supervisorName+'</span> as' : 'this' } gate supervisor?`,
            confirmLabel: 'Remove',
            onConfirm: () => {
                this.saved_gate_supervisor_ids.splice(i, 1);
                this.gate_supervisors.splice(i, 1);
            }
        });
    }

    @ViewChild('gateSupervisorForm') gateSupervisorForm;
    saveGateSuperVisors(event) {
        let userIds = (this.gate_supervisors || []).map(gate_supervisor => gate_supervisor.user_ref);
        this.savingSupervisors = true;
        this.projectService.addUpdateGateSupervisors(this.projectId, {userIds}).subscribe(data => {
            this.savingSupervisors = false;
            if(data.success && data.projectGateSupervisors.gate_supervisors) {
                //update data
                console.log('update supervisors data');
                this.setSupervisorData(data.projectGateSupervisors.gate_supervisors);
            } else {
                const message = 'Failed to update gate supervisors.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
            this.gateSupervisorForm.reset();
            event.closeFn()
        });
    }

    setSupervisorData(gate_supervisors = []) {
        this.saved_gate_supervisor_ids = gate_supervisors.map(sup => sup.user_ref);
        this.gate_supervisors = gate_supervisors.reduce((gate_supervisors, gs) => {
            gate_supervisors.push({
                user_ref: (gs.user_ref) ? gs.user_ref : gs.id,
                _email: gs.email,
                _name: gs.name,
                id: gs.id,
            });
            return gate_supervisors;
        }, []);

        this.gate_supervisors = (this.gate_supervisors.length) ? this.gate_supervisors : [];
    }

    yesOrNo(boolean:boolean) {
        return boolean ? 'Confirm Arrival' : 'No Show';
    }

    getUserFullName(user) {
        return (user && user.id) ? `${user.first_name ? user.first_name : ''}${user.middle_name ? ' ' + user.middle_name : ''}${user.last_name ? ' ' + user.last_name : ''}`.trim() : '';
    };

    async initiateDownload(resp) {
        resp.toDate = resp.toDate? resp.toDate: resp.fromDate;
        if (resp.fromDate && resp.toDate) {
            let fromDate:any = this.getDateInfo(resp.fromDate, this.dateStorageFormat);
            let toDate:any = this.getDateInfo(resp.toDate, this.dateStorageFormat);
            let dayjsFromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(resp.fromDate);
            let dayjsToDate = this.ngbMomentjsAdapter.ngbDateToDayJs(resp.toDate||resp.fromDate);
            let maxSelectionRangeInMonths = innDexConstant.maxMonthRangeForExport;
            if(dayjsToDate.diff(dayjsFromDate,'month',true) > maxSelectionRangeInMonths){
                const message = `This export feature is limited to records spanning a maximum of ${maxSelectionRangeInMonths} months`;
                this.toastService.show(this.toastService.types.INFO, message);
                return;
            }
            this.exportBookingLoader = true;
            if(resp.type === 'pdf') {
                let request = {
                    selected_date: toDate,
                    from_date: fromDate
                };
                this.projectGateBookingService.exportBookingsReportPDF(request, this.projectId, () => {
                    this.exportBookingLoader = false;
                });
            } else if (resp.type === 'xlsx') {
                let request = {
                    format: 'xls',
                    selected_date: toDate,
                    from_date: fromDate
                };
                this.projectGateBookingService.exportBookings(request, this.projectId, `gate-bookings-report-${toDate}.xlsx`, () => {
                    this.exportBookingLoader = false;
                });
            }
        }

    }

    checkBookingCloseout(bookingObj) {
        return bookingObj.booking_closeout && Object.keys(bookingObj.booking_closeout).length;
    }

    viewOrCreateNewBookingRequest(bookedSlotOptionsHtml, gateDetail, timeSlot) {
        this.pendingGateBookings = [];
        this.selectedGateDetail = gateDetail;
        let gateBooking = gateDetail.project_gate_booking;
        let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        this.pendingGateBookings = gateBooking.filter(r => this.changeDateToFrontendFormat(r.booking_date) === selectedDate && r.booking_slots.includes(timeSlot));
        let isUsersBooking = false;
        let userCanCreateNewRequest = false;
        let effectiveGateBooking = null;

        // Finding booking request in precedence of Approved, Own Pending, Pending, Declined
        for(let i in gateBooking){
            if(this.changeDateToFrontendFormat(gateBooking[i]['booking_date']) == selectedDate && gateBooking[i]['booking_slots'].includes(timeSlot)) {
                this.booking = this.httpService.parseJson(JSON.stringify(gateBooking[i]));
                this.booking._same_dispatch_return_postcode = !!(this.booking.dispatch_post_code === this.booking.return_postcode);
                // For clicked slot Approved or user's own booking request then consider that
                if (this.booking.status === 2 || (this.booking.status === 1 && gateBooking[i]['created_by_user'].id === this.authUser$.id)) {
                    effectiveGateBooking = this.booking;
                    this.openModal(bookedSlotOptionsHtml, 'sm');
                    userCanCreateNewRequest = false;
                    break;
                }
                else {
                    userCanCreateNewRequest = true;
                }
            }
        }

        // Set data for existing request
        if (effectiveGateBooking) {
            if (effectiveGateBooking.delivery_notes_ref) {
                this.deliveryInfo = effectiveGateBooking.delivery_notes_ref;
            }
            this.bookingSlotsString = this.bookingSlotsToDisplay(effectiveGateBooking.booking_slots);
            this.startTime = timeSlot.split(" ")[0];
            let selectedDateMoment = dayjs(selectedDate, this.dateFormat);
            this.canUserAmendAndDeleteBooking = (selectedDateMoment.diff(dayjs(), 'day') < 0) ? false : true;
            isUsersBooking = true;
        }

        // Else case where No request, Declined requests or pending requests from other user then continue on create new request
        if((!isUsersBooking || userCanCreateNewRequest) && !this.isPastDate && this.isPastTime(timeSlot)) {
            this.activeStage = 0;
            this.booking = new ProjectGateBooking;
            this.booking.project_id = this.projectId;
            this.booking._same_dispatch_return_postcode = !!(this.booking.dispatch_post_code === this.booking.return_postcode);
            this.booking.user_id = this.authUser$.id;
            this.booking.booking_slots = [];
            this.disableForPendingBookings = false;
            this.booking.booking_slots = this.pendingGateBookings[0].booking_slots;
            this.booking.booking_type = this.pendingGateBookings[0].booking_type;
            this.booking.duration = this.pendingGateBookings[0].duration;
            this.bookingSlotsString = this.bookingSlotsToDisplay(this.pendingGateBookings[0].booking_slots);
            this.gate_name = this.pendingGateBookings[0].gate_name;
            this.booking.gate_id = this.pendingGateBookings[0].gate_id;
            this.disableForPendingBookings = true;
            this.selectedGateDetail = gateDetail;
            this.booking.booking_date = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
            this.dayOfWeek = this.weekDays[dayjs(this.changeDateToFrontendFormat(this.booking.booking_date), this.dateFormat).day()];

            // Reset form END
            this.showModal = true;
            this.bookingPopupRef.open();
        }
    }

    changeDateToFrontendFormat(date) {
        return (date && dayjs(date, this.dateStorageFormat, true).isValid()) ? dayjs(date, this.dateStorageFormat).format(this.dateFormat) : date;
    }

    changeDateToBackendFormat(date) {
        return (date && dayjs(date, this.dateFormat, true).isValid()) ? dayjs(date, this.dateFormat).format(this.dateStorageFormat) : date;
    }

    isTimeSlotAvailable() {
        return (this.deactivatedTimeSlots.length < this.timeSlots.length);
    }

    toggleBookingSelection(bookingId, event) {
        if(event.target.checked) {
            this.selectedbookings.push(bookingId);
            return;
        }
        let index = this.selectedbookings.indexOf(bookingId);
        if (index > -1) {
            this.selectedbookings.splice(index, 1);
        }
    }
    @ViewChild('bulkBookingsCloseRef') private bulkBookingsCloseRef: IModalComponent;
    viewbulkClosePopup() {
        let selectedDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dateFormat);
        this.totalPendingBookings = 0;
        this.allPendingBookings = [];
        for(let gate of this.availableGates) {
            let gateBooking = gate.project_gate_booking;
            this.pBookings[gate.gate_name] = [];
            let todayBooks = gateBooking.filter(r => this.changeDateToFrontendFormat(r.booking_date) === selectedDate && r.status === 1);
            todayBooks = todayBooks.map((b, index) => {
                return {...b, 'gate': gate.gate_name}
            });
            if(todayBooks.length) {
                this.totalPendingBookings += todayBooks.length;
                this.allPendingBookings.push(...todayBooks);
            }
            this.pBookings[gate.gate_name].push(...todayBooks);
        }
        this.allPBookingIds = this.allPendingBookings.map(b =>{
            return b.id
        });
        this.bulkBookingsCloseRef.open();
    }

    @ViewChild('declineMultipleBookingFormRef') private declineMultipleBookingFormRef: IModalComponent;
    rejectMultipleBookingsPopupRef;
    rejectMultipleBookingsPopup() {
        this.declined_comment = null;
        if(this.selectedbookings.length) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Decline Booking',
                title: `Are you sure you want to decline all selected bookings?`,
                confirmLabel: 'Decline',
                onConfirm: () => {
                    this.declineMultipleBookingFormRef.open();
                    this.bulkBookingsCloseRef.close();
                }
            });
        }
    }

    rejectMultipleBookings(event) {
        let reqData: any = {};
        reqData.admin_name = this.authUser$.name;
        reqData.project = this.projectInfo.name;
        reqData.status = 0;
        reqData.declined_comment = this.declined_comment;
        reqData.bookingIds = this.selectedbookings;
        this.projectGateBookingService.updateMultipleBookingsStatus(this.projectId, reqData).subscribe((data: any) => {
            this.bookingInProgress = false;
            if(data && data.success) {
                const message = 'Decline booking request has been accepted successfully.';
                this.toastService.show(this.toastService.types.SUCCESS, message);
                this.pendingGateBookings = this.pendingGateBookings.filter(b => {
                    if(!this.selectedbookings.includes(b.id)) {
                        return b;
                    }
                });
                for(let gate of this.availableGates) {
                    this.pBookings[gate.gate_name] = this.pBookings[gate.gate_name].filter(b => {
                        if(!this.selectedbookings.includes(b.id)) {
                            return b;
                        }
                    });
                }
                event.closeFn();
            } else {
                const message = data.message || 'Failed to decline the bookings.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    public onActionSelection(actionVal: ActionButtonVal) {
        const code = actionVal.code;
        if(code === TransportManagementActionButtons.DECLINE_MULTIPLE_BOOKINGS) {
            this.viewbulkClosePopup();
        } else if(code === TransportManagementActionButtons.GATE_SUPERVISORS) {
            this.selectedUser = null;
            this.saved_gate_supervisor_ids = this.gate_supervisors.map(sup => sup.user_ref);
            this.openViewBookingGateSupervisorsModal();
        } else if(code === TransportManagementActionButtons.DOWNLOAD_BOOKING_LIST) {
            this.openViewBookingReportModal();
        }
    }

    openViewBookingReportModal() {
        this.reportDownloader.openModal();
    }

    @ViewChild('gateSuperVisorModal') private gateSuperVisorModalRef: IModalComponent;
    openViewBookingGateSupervisorsModal() {
        this.gateSuperVisorModalRef.open();
    }

    doneAllPendingBookingModal(event) {
        event.closeFn();
        this.hasAllPendingBookingRef = null;
    }

    closeAllPendingBookingModal() {
        this.hasAllPendingBookingRef = null;
    }

    async viewBookingReportDownload(event) {
        await this.initiateDownload(event.selection);
        event.closeFn();
    }

    blockBookingInfo(info) {
        const fromValue = info.frequency.charAt(0).toUpperCase() + info.frequency.slice(1).toLowerCase()
        return `${fromValue} from ${info.startDate} - ${info.endDate}`
    }

    vehicleTypeSelected($event){
        if(!$event) {
            this.booking.vehicle_type = null;
            this.booking.laden_percent = null;
            this.booking.co2_emission_gm = 0;
            return;
        }
        this.booking.vehicle_type = $event.label;
        this.booking.laden_percent = 0;
        if(!$event.has_laden) {
            this.booking.laden_percent = null;
        }
        this.deriveEmissionIfNeeded();
    }

    deriveEmissionIfNeeded(){
        if(this.booking.vehicle_type) {
            let derived_co2 = this.META_VEHICLE_EMISSIONS.find(m => (
                    m.label === this.booking.vehicle_type && (
                        m.laden === this.booking.laden_percent || m.laden === null
                    )
                )
            );
            console.log('emission details', derived_co2);
            if(derived_co2) {
                this.booking.co2_emission_gm = derived_co2.co2;
            }
        }
    }

    validatePostcode(pPostcode, postCode) {
        if (pPostcode.name === "dispatch_post_code") {
            this.booking._same_dispatch_return_postcode = undefined;
            this.booking.return_postcode = null;
        }
        this.exportBookingLoader = true;
        this.projectService.validatePostcode(postCode, this.projectInfo.custom_field.country_code).subscribe((response: {success:boolean, latLongData: ProjectLocation }) => {
            if(response.success && response.latLongData) {
                pPostcode.control.setErrors(null);
            } else {
                pPostcode.control.setErrors({valid: true});
            }
            this.exportBookingLoader = false;
        })
    }

    returnPostcodeSelection() {
        this.booking.return_postcode = (this.booking._same_dispatch_return_postcode) ? this.booking.dispatch_post_code : null;
    }
}

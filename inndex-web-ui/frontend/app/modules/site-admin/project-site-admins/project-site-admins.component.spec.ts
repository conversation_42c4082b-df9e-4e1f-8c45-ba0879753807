import {ComponentFixture, fakeAsync, TestBed, tick} from '@angular/core/testing';
import { ProjectSiteAdminsComponent } from './project-site-admins.component';
import { NgbModal, NgbModalConfig } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule } from '@angular/forms';
import { of } from 'rxjs';
import {ProjectService, ResourceService, Project, UACProjectDesignations} from '@app/core';
import { ToastService, PermissionUtility } from '@app/core';
import {Component, EventEmitter} from '@angular/core';

@Component({selector: 'generic-confirmation-modal', template: ''})
class MockGenericConfirmationModalComponent {}

describe('ProjectSiteAdminsComponent', () => {
    let component: ProjectSiteAdminsComponent;
    let fixture: ComponentFixture<ProjectSiteAdminsComponent>;
    let mockResourceService: jasmine.SpyObj<ResourceService>;
    let mockProjectService: jasmine.SpyObj<ProjectService>;
    let mockToastService:any;

    beforeEach(async () => {
        mockResourceService = jasmine.createSpyObj('ResourceService', ['getToolList']);
        mockProjectService = jasmine.createSpyObj('ProjectService', ['getProjectInductedUsers']);
        mockToastService = jasmine.createSpyObj('ToastService', ['show']);
        mockToastService.types = { INFO: 'info' };

        await TestBed.configureTestingModule({
            declarations: [ProjectSiteAdminsComponent],
            imports: [FormsModule],
            providers: [
                NgbModalConfig,
                NgbModal,
                ToastService,
                PermissionUtility,
                MockGenericConfirmationModalComponent,
                { provide: ResourceService, useValue: mockResourceService },
                { provide: ProjectService, useValue: mockProjectService },
                { provide: ToastService, useValue: mockToastService }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ProjectSiteAdminsComponent);
        component = fixture.componentInstance;
        component.project = new Project();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should load tools list on init', () => {
        const mockTools = { tool_list: [{ id: 1, name: 'Tool A' }] };
        mockResourceService.getToolList.and.returnValue(of(mockTools));

        component.ngOnInit();

        expect(mockResourceService.getToolList).toHaveBeenCalled();
        expect(component.toolsList).toEqual(mockTools.tool_list);
    });

    it('should emit adminsChanged event correctly', () => {
        spyOn(component.adminsChanged, 'emit');

        const testAdmins = [{ name: 'Admin A' }];
        component.admins = testAdmins;

        component.adminsChanged.emit({
            isValid: true,
            removed: false,
            admins: testAdmins,
        });

        expect(component.adminsChanged.emit).toHaveBeenCalledWith({
            isValid: true,
            removed: false,
            admins: testAdmins,
        });
    });

    it('should have default blockType and modalTitle', () => {
        expect(component.blockType).toBe('access');
        expect(component.modalTitle).toBe('Customize Access');
    });

    describe('ProjectSiteAdminsComponent - onUserSelect', () => {
        it('should set selected user details if no duplicate exists', () => {
            component.admins = [
                { _email: '', _name: '', user_ref: null },
            ];
            const selectedRecord = { email: '<EMAIL>', name: 'User Name', user_ref: { id: 1 } };

            spyOn(component as any, 'emitChange');

            component.onUserSelect({ selectedRecord }, 0);

            expect(component.admins[0]._email).toBe('<EMAIL>');
            expect(component.admins[0]._name).toBe('User Name');
            expect(component.admins[0].user_ref).toEqual({ id: 1 });
            expect(component.admins[0]._isValid).toBeTruthy();
            expect((component as any).emitChange).toHaveBeenCalled();
        });

        it('should show toast if duplicate email is found', () => {
            component.admins = [
                { _email: '<EMAIL>', _name: 'Old User', user_ref: null },
                { _email: '', _name: '', user_ref: null }
            ];
            const selectedRecord = { email: '<EMAIL>', name: 'New User', user_ref: { id: 2 } };

            component.onUserSelect({ selectedRecord }, 1);

            expect(mockToastService.show).toHaveBeenCalledWith(
                mockToastService.types.INFO,
                'Looks like you are making duplicate <NAME_EMAIL>, please check.'
            );
            expect(component.admins[1]._email).toBe('');
            expect(component.admins[1]._name).toBe('');
        });
    });

    it('should call emitChange on ngOnChanges', () => {
        spyOn(component as any, 'emitChange');
        component.ngOnChanges();
        expect((component as any).emitChange).toHaveBeenCalled();
    });

    it('should return the index passed', () => {
        const obj = { name: 'Test' };
        const result = component.trackByRowIndex(5, obj);
        expect(result).toBe(5);
    });

    describe('checkIfAlreadyExist', () => {
        it('should return false and show toast if duplicate email found (from _email)', () => {
            component.admins = [
                { _email: '<EMAIL>' },
                { _email: '<EMAIL>' }
            ];

            const result = component.checkIfAlreadyExist(1, '<EMAIL>');

            expect(result).toBeFalsy();
            const [type, message] = mockToastService.show.calls.mostRecent().args;
            expect(type).toBe('info');
            expect(message).toContain('<EMAIL>');
        });

        it('should return false and show toast if duplicate email found (from user_ref.email)', () => {
            component.admins = [
                { id: 1, user_ref: { email: '<EMAIL>' } },
                { id: 2, user_ref: { email: '<EMAIL>' } }
            ];

            const result = component.checkIfAlreadyExist(1, '<EMAIL>');

            expect(result).toBeFalsy();

            const [type, message] = mockToastService.show.calls.mostRecent().args;
            expect(type).toBe('info');
            expect(message).toContain('<EMAIL>');
        });

        it('should return true if no duplicate found', () => {
            component.admins = [
                { _email: '<EMAIL>' },
                { _email: '<EMAIL>' }
            ];

            const result = component.checkIfAlreadyExist(0, '<EMAIL>');

            expect(result).toBeTruthy();
            expect(mockToastService.show).not.toHaveBeenCalled();
        });
    })

    it('should update _isValid for the correct admin and call emitChange()', () => {
        // Arrange: Setup initial admins and mock the emitChange method
        component.admins = [
            { id: 1, _isValid: false },
            { id: 2, _isValid: false }
        ];

        spyOn(component, 'emitChange');  // Spy on emitChange to check if it gets called

        // Act: Call validityChanged with true for the first admin (index 0)
        component.validityChanged(true, component.admins[0], 0);

        // Assert: Check if _isValid is updated for the correct admin
        expect(component.admins[0]._isValid).toBe(true);

        // Assert: Check if emitChange was called
        expect(component.emitChange).toHaveBeenCalled();
    });

    describe('onValidAdminEmailInput()', () => {
        it('should update the admin email, name, and user_ref, and call emitChange()', () => {
            // Arrange: Setup initial admins and mock the emitChange method
            component.admins = [
                { id: 1, _email: '', _name: '', user_ref: null },
                { id: 2, _email: '', _name: '', user_ref: null }
            ];

            spyOn(component, 'emitChange');  // Spy on emitChange to check if it gets called

            const event = {
                email: '<EMAIL>',
                name: 'Admin User',
                id: 123
            };

            // Act: Call onValidAdminEmailInput with the event for the first admin (index 0)
            component.onValidAdminEmailInput(event, 0);

            // Assert: Check if the admin at index 0 has its properties updated
            expect(component.admins[0]._email).toBe('<EMAIL>');
            expect(component.admins[0]._name).toBe('Admin User');
            expect(component.admins[0].user_ref).toBe(123);

            // Assert: Check if emitChange was called
            expect(component.emitChange).toHaveBeenCalled();
        });

        it('should not update the admin if email is not provided in the event', () => {
            // Arrange: Setup initial admins
            component.admins = [
                { id: 1, _email: '', _name: '', user_ref: null }
            ];

            spyOn(component, 'emitChange');

            const event = {
                name: 'Admin User',
                id: 123
            };  // No email property

            // Act: Call onValidAdminEmailInput with the event
            component.onValidAdminEmailInput(event, 0);

            // Assert: Check if admin properties are not updated
            expect(component.admins[0]._email).toBe('');
            expect(component.admins[0]._name).toBe('');
            expect(component.admins[0].user_ref).toBeNull();

            // Assert: Check if emitChange was still called
            expect(component.emitChange).toHaveBeenCalled();
        });
    });

    describe('addAccessRow()', () => {
        it('should add a new access row and call emitChange()', () => {
            // Arrange: Setup initial admins (can be empty or null)
            component.admins = null;  // Simulating case where admins is not initialized
            spyOn(component, 'emitChange');  // Spy on emitChange to check if it gets called

            // Act: Call addAccessRow
            component.addAccessRow();

            // Assert: Check if admins is an array and has one row added
            expect(component.admins).toBeDefined();
            expect(component.admins.length).toBe(1);

            // Assert: Check the structure of the added row
            expect(component.admins[0]).toEqual({
                designation: [],
                permission: [],
                flags: {}
            });

            // Assert: Check if emitChange was called
            expect(component.emitChange).toHaveBeenCalled();
        });

        it('should add a new access row to existing admins and call emitChange()', () => {
            // Arrange: Setup initial admins with one row
            component.admins = [
                { designation: ['Admin'], permission: ['Read'], flags: {} }
            ];
            spyOn(component, 'emitChange');  // Spy on emitChange to check if it gets called

            // Act: Call addAccessRow
            component.addAccessRow();

            // Assert: Check if admins now has two rows
            expect(component.admins.length).toBe(2);

            // Assert: Check the structure of the newly added row
            expect(component.admins[1]).toEqual({
                designation: [],
                permission: [],
                flags: {}
            });

            // Assert: Check if emitChange was called
            expect(component.emitChange).toHaveBeenCalled();
        });

        it('should add a new access row if admins is an empty array and call emitChange()', () => {
            // Arrange: Setup initial admins as an empty array
            component.admins = [];
            spyOn(component, 'emitChange');  // Spy on emitChange to check if it gets called

            // Act: Call addAccessRow
            component.addAccessRow();

            // Assert: Check if admins has one row added
            expect(component.admins.length).toBe(1);
            expect(component.admins[0]).toEqual({
                designation: [],
                permission: [],
                flags: {}
            });

            // Assert: Check if emitChange was called
            expect(component.emitChange).toHaveBeenCalled();
        });
    });

    describe('getOwnerNameOrEmail()', () => {
        it('should return the owner name if available, or email if name is missing', () => {
            // Arrange: Setup admins with different combinations of name and email
            component.admins = [
                { name: 'John Doe', email: '<EMAIL>' }, // has name
                { name: '', email: '<EMAIL>' },    // has email but no name
                { name: '', email: '' },                        // neither name nor email
            ];

            // Act & Assert: Test for different cases
            expect(component.getOwnerNameOrEmail(0)).toBe('John Doe'); // should return name
            expect(component.getOwnerNameOrEmail(1)).toBe('<EMAIL>'); // should return email
            expect(component.getOwnerNameOrEmail(2)).toBe(''); // should return empty string
            expect(component.getOwnerNameOrEmail(3)).toBe(''); // should return empty string for non-existent index
        });

        it('should return empty string if admins array is empty', () => {
            // Arrange: Set admins to an empty array
            component.admins = [];

            // Act & Assert: Call getOwnerNameOrEmail with a non-existent index
            expect(component.getOwnerNameOrEmail(0)).toBe(''); // should return empty string
        });
    });

    describe('removeAccessRow', () => {

        beforeEach(() => {
            (component as any).confirmationModalRef = {
                openConfirmationPopup: jasmine.createSpy('openConfirmationPopup')
            } as any;

            // Set up some initial data for admins
            component.admins = [
                { name: 'John Doe', email: '<EMAIL>' }, // admin 0
                { name: 'Jane Smith', email: '<EMAIL>' } // admin 1
            ];
        });

        it('should call openConfirmationPopup with the correct message when removing access', () => {
            const index = 0;
            const expectedMessage = `Are you sure you want to remove the access of <span class="fw-500">John Doe</span>?`;

            // Act: Trigger the method
            component.removeAccessRow(null, index);

            // Assert: Check if openConfirmationPopup was called with the expected arguments
            expect((component as any).confirmationModalRef.openConfirmationPopup).toHaveBeenCalledWith({
                headerTitle: 'Remove Access',
                title: expectedMessage,
                confirmLabel: 'Remove',
                onConfirm: jasmine.any(Function), // Check that the confirm function is passed
            });
        });

        it('should remove the correct admin from the array when confirm is clicked', () => {
            const index = 0;

            // Act: Trigger the removeAccessRow method
            component.removeAccessRow(null, index);

            // Simulate clicking the "Confirm" button in the modal
            const onConfirm = (component as any).confirmationModalRef.openConfirmationPopup.calls.mostRecent().args[0].onConfirm;
            onConfirm(); // Call the confirm function

            // Assert: Check if the admin was removed from the array
            expect(component.admins.length).toBe(1); // Should be reduced to 1 admin
            expect(component.admins[0].email).toBe('<EMAIL>'); // Ensure the remaining admin is the correct one
        });

        it('should call emitChange with true after removing an admin', () => {
            const index = 0;
            const emitChangeSpy = spyOn(component, 'emitChange');

            // Act: Trigger the removeAccessRow method
            component.removeAccessRow(null, index);

            // Simulate clicking the "Confirm" button in the modal
            const onConfirm = (component as any).confirmationModalRef.openConfirmationPopup.calls.mostRecent().args[0].onConfirm;
            onConfirm(); // Call the confirm function

            // Assert: Check if emitChange was called with true
            expect(emitChangeSpy).toHaveBeenCalledWith(true);
        });

        it('should handle empty name and return generic access message', () => {
            // Set up an admin with no name
            component.admins = [
                { name: '', email: '<EMAIL>' }
            ];
            const index = 0;
            const expectedMessage = `Are you sure you want to remove the access of <span class="fw-500"><EMAIL></span>?`;

            // Act: Trigger the removeAccessRow method
            component.removeAccessRow(null, index);

            // Assert: Check if openConfirmationPopup was called with the correct message
            expect((component as any).confirmationModalRef.openConfirmationPopup).toHaveBeenCalledWith({
                headerTitle: 'Remove Access',
                title: expectedMessage,
                confirmLabel: 'Remove',
                onConfirm: jasmine.any(Function), // Check that the confirm function is passed
            });
        });
    });

    describe('toggleFeatureAccess', () => {
        let getPermissionKeySpy: jasmine.Spy;
        let emitChangeSpy: jasmine.Spy;

        beforeEach(() => {
            (component as any).confirmationModalRef = {
                openConfirmationPopup: jasmine.createSpy('openConfirmationPopup')
            } as any;

            // Mock project data
            component.project = {
                custom_field: {
                    induction_phrase_singlr: 'induction'
                }
            };

            // Setup admin data
            component.admins = [
                { permission: [] } // index 0
            ];

            // Spies
            getPermissionKeySpy = spyOn((component as any).permissionUtility, 'getPermissionKey').and.callFake((feature, op) => `${feature}_${op}`);
            emitChangeSpy = spyOn(component, 'emitChange');
        });

        it('should add permissions and emit change when checked is true and feature is not induction', () => {
            const event = { target: { checked: true } };
            const result = component.toggleFeatureAccess(event, 0, 'dashboard');

            expect(getPermissionKeySpy).toHaveBeenCalledWith('dashboard', '*');
            expect(getPermissionKeySpy).toHaveBeenCalledWith('dashboard', '*');
            expect(component.admins[0].permission).toContain('dashboard_*');
            expect(component.admins[0].permission).toContain('dashboard_*');
            expect((component as any).confirmationModalRef.openConfirmationPopup).not.toHaveBeenCalled();
            expect(emitChangeSpy).toHaveBeenCalled();
            expect(result).toEqual(['dashboard_*', 'dashboard_add']);
        });

        it('should show confirmation modal when feature is "induction"', () => {
            const event = { target: { checked: true } };
            component.toggleFeatureAccess(event, 0, 'induction');

            expect((component as any).confirmationModalRef.openConfirmationPopup).toHaveBeenCalledWith(jasmine.objectContaining({
                headerTitle: 'Warning',
                title: 'This access level will permit the user to see all project induction records which may contain sensitive data. Would you still like to assign this access level to the user?',
                confirmLabel: 'Yes',
                cancelLabel: 'No',
                onConfirm: jasmine.any(Function),
                onClose: jasmine.any(Function)
            }));
        });

        it('should remove permissions and emit change when checked is false', () => {
            const event = { target: { checked: false } };

            // Add permissions beforehand
            component.admins[0].permission = ['dashboard_*', 'dashboard_*'];

            const removeSpy = spyOn((component as any), 'removePermissionByLabel');

            const result = component.toggleFeatureAccess(event, 0, 'dashboard');

            expect(removeSpy).toHaveBeenCalledWith(0, 'dashboard_*');
            expect(removeSpy).toHaveBeenCalledWith(0, 'dashboard_*');
            expect(emitChangeSpy).toHaveBeenCalled();
            expect(result).toEqual(['dashboard_*', 'dashboard_*']); // Return value before removal
        });

        it('should remove permissions via onClose of confirmation modal', () => {
            const event = { target: { checked: true } };
            const removeSpy = spyOn((component as any), 'removePermissionByLabel');

            component.toggleFeatureAccess(event, 0, 'induction');

            const onCloseFn = (component as any).confirmationModalRef.openConfirmationPopup.calls.mostRecent().args[0].onClose;
            onCloseFn(); // simulate closing the modal

            expect(removeSpy).toHaveBeenCalledWith(0, 'induction_*');
            expect(removeSpy).toHaveBeenCalledWith(0, 'induction_*');
        });
    });

    describe('toggleEmailNotify', () => {
        let emitChangeSpy: jasmine.Spy;
        let removePermissionByLabelSpy: jasmine.Spy;

        beforeEach(() => {
            // Setup initial admin state
            component.admins = [
                { permission: [] } // index 0
            ];

            emitChangeSpy = spyOn(component, 'emitChange');
            removePermissionByLabelSpy = spyOn((component as any), 'removePermissionByLabel');
        });

        it('should add notify permission when checkbox is checked', () => {
            const event = { target: { checked: true } };
            const result = component.toggleEmailNotify(event, 0, 'induction');

            expect(component.admins[0].permission).toContain('induction:notify');
            expect(removePermissionByLabelSpy).not.toHaveBeenCalled();
            expect(emitChangeSpy).toHaveBeenCalled();
            expect(result).toEqual(['induction:notify']);
        });

        it('should remove notify permission when checkbox is unchecked', () => {
            // Pre-populate permission
            component.admins[0].permission = ['induction:notify'];

            const event = { target: { checked: false } };
            const result = component.toggleEmailNotify(event, 0, 'induction');

            expect(removePermissionByLabelSpy).toHaveBeenCalledWith(0, 'induction:notify');
            expect(emitChangeSpy).toHaveBeenCalled();
            expect(result).toEqual(['induction:notify']); // Return value before actual removal
        });
    });

    it('should clear all permissions for the given admin index', () => {
        component.admins = [
            {
                designation: ['NOMINATED'],
                permission: ['tool1:notify', 'tool3:notify'],
                flags: {}
            }
        ];
        component.deselectAll(0);
        expect(component.admins[0].permission).toEqual([]);
    });

    describe('removeDesignationByLabel', ()=>{
        it('should remove the specified designation from the admin', () => {
            // Arrange
            component.admins = [
                {
                    designation: ['NOMINATED', 'LEAD'],
                    permission: [],
                    flags: {}
                }
            ];
            // Act
            (component as any).removeDesignationByLabel(0, 'LEAD');
            // Assert
            expect(component.admins[0].designation).toEqual(['NOMINATED']);
        });

        it('should not modify designation if the label does not exist', () => {
            component.admins = [
                {
                    designation: ['NOMINATED'],
                    permission: [],
                    flags: {}
                }
            ];

            (component as any).removeDesignationByLabel(0, 'NON_EXISTENT');

            expect(component.admins[0].designation).toEqual(['NOMINATED']);
        });
    })

    it('should call closeModal() and event.closeFn when closeCustomAccessModal is called', () =>{
        // Arrange
        const closeFnSpy = jasmine.createSpy('closeFn');
        const event = { closeFn: closeFnSpy };
        spyOn(component, 'closeModal');
        // Act
        component.closeCustomAccessModal(event);

        // Assert
        expect(component.closeModal).toHaveBeenCalled();
        expect(closeFnSpy).toHaveBeenCalled();
    })

    it('should set isSelectAllActive and showCustomAccessModal value to false when call closeModal', ()=>{
        // Arrange
        component.isSelectAllActive = true;
        component.showCustomAccessModal = true;

        // Act
        component.closeModal();

        // Assert
        expect(component.isSelectAllActive).toBeFalsy();
        expect(component.showCustomAccessModal).toBeFalsy();
    })

    describe('toggleSelectDeselectAll()', () => {
        beforeEach(() => {
            component.isSelectAllActive = false;
            component.accessUserIdx = 1;
            spyOn(component, 'selectAll');
            spyOn<any>(component, 'deselectAll');
        })
        it('should call selectAll() when isSelectAllActive is true', () =>{
            const mockAccessUserIdx = 1;
            component.toggleSelectDeselectAll();
            expect(component.selectAll).toHaveBeenCalledWith(mockAccessUserIdx);
        })

        it('should call deselectAll() when isSelectAllActive is false', () =>{
            const mockAccessUserIdx = 1;
            component.isSelectAllActive = true;
            component.toggleSelectDeselectAll();
            expect(component.deselectAll).toHaveBeenCalledWith(mockAccessUserIdx);
        })
    });

    describe('anyInputIsInvalid()', () => {
        it('should anyInputIsInvalid return true if any admin has _isValid as false', () => {
            component.admins = [
                {_isValid: true},
                {_isValid: false},
            ]
            expect(component.anyInputIsInvalid()).toBeTruthy();
        })

        it('should anyInputIsInvalid return false if all admin has _isValid as true', () => {
            component.admins = [
                {_isValid: true},
                {_isValid: true},
            ]
            expect(component.anyInputIsInvalid()).toBeFalsy();
        })

        it('should anyInputIsInvalid return false when admins list is empty', () => {
            component.admins = []
            expect(component.anyInputIsInvalid()).toBeFalsy();
        })
    })

    describe('isSelectAtleastOneAccess()', () => {
        it('should return true when all admins have _is_admin true or at least one designation', () => {
            component.admins = [
                { _is_admin: true, designation: [] },
                { _is_admin: false, designation: ['NOMINATED'] }
            ];

            expect(component.isSelectAtleastOneAccess()).toBeTruthy();
        });

        it('should return null when at least one admin has neither _is_admin nor designation', () => {
            component.admins = [
                { _is_admin: false, designation: [] },
                { _is_admin: false, designation: ['NOMINATED'] }
            ];

            expect(component.isSelectAtleastOneAccess()).toBeNull();
        });

        it('should return true for empty admins array (edge case)', () => {
            component.admins = [];
            expect(component.isSelectAtleastOneAccess()).toBeTruthy();
        });
    });

    describe('checkHasFullAccess()', () => {
        it('should return true when at last one admin has designation with FULL_ACCESS', () => {
            component.admins = [
                {designation: ['other']},
                {designation: ['NOMINATED']},
            ]
            expect(component.checkHasFullAccess()).toBeTruthy();
        });

        it('should return null when admin dose not have any designation', () => {
            component.admins = [
                {designation: []},
                {designation: []},
            ]
            expect(component.checkHasFullAccess()).toBeNull();
        });

        it('should return false for empty admins array (edge case)', () => {
            component.admins = [];
            expect(component.checkHasFullAccess()).toBeFalsy();
        });
    })


    describe('returnAccessLevelValue()', () => {
        it('should return FULL_ACCESS if user has other designation', () => {
            const user = { designation: ['other'] };
            const result = component.returnAccessLevelValue(user);
            expect(result).toBe(UACProjectDesignations.FULL_ACCESS);
        });

        it('should return RESTRICTED if user has restricted designation', () => {
            const user = { designation: ['restricted'] };
            const result = component.returnAccessLevelValue(user);
            expect(result).toBe(UACProjectDesignations.RESTRICTED);
        });

        it('should return DELIVERY_MANAGER if user has delivery_management designation', () => {
            const user = { designation: ['delivery_management'] };
            const result = component.returnAccessLevelValue(user);
            expect(result).toBe(UACProjectDesignations.DELIVERY_MANAGER);
        });

        it('should return CUSTOM if user has custom designation', () => {
            const user = { designation: ['custom'] };
            const result = component.returnAccessLevelValue(user);
            expect(result).toBe(UACProjectDesignations.CUSTOM);
        });

        it('should return null if user has none of the valid designations', () => {
            const user = { designation: ['nominated'] };
            const result = component.returnAccessLevelValue(user);
            expect(result).toBeNull();
        });

        it('should return null if designation array is empty', () => {
            const user = { designation: [] };
            const result = component.returnAccessLevelValue(user);
            expect(result).toBeNull();
        });
    });

    describe('onAccessLevelSelect()', () => {
        beforeEach(() => {
            component.admins = [];
            (component as any).confirmationModalRef = jasmine.createSpyObj('confirmationModalRef', ['openConfirmationPopup']);
            spyOn(component, 'emitChange');
            spyOn<any>(component, 'removeDesignationByLabel');
        })

        it('should set designation to [$event] when not delivery manager', () => {
            const $event = UACProjectDesignations.DELIVERY_MANAGER;
            component.admins = [{ designation: [], flags: {} }];
            const result = component.onAccessLevelSelect($event, 0);

            expect(component.admins[0].designation).toEqual([$event]);
            expect(component.emitChange).toHaveBeenCalled();
            expect(result).toEqual([$event]);
        });

        it('should include delivery manager in designation if flag is true', () => {
            const $event = UACProjectDesignations.RESTRICTED;
            component.admins = [{
                designation: ['delivery_management'],
                flags: { is_delivery_manager: true }
            }];

            const result = component.onAccessLevelSelect($event, 0);

            expect(component.admins[0].designation).toEqual([UACProjectDesignations.DELIVERY_MANAGER, $event]);
            expect(result).toEqual([UACProjectDesignations.DELIVERY_MANAGER, $event]);
            expect(component.emitChange).toHaveBeenCalled();
        });

        it('should open confirmation modal and call removeDesignationByLabel on close if FULL_ACCESS is selected', () => {
            const $event = UACProjectDesignations.FULL_ACCESS;
            component.admins = [{ designation: [], flags: {} }];

            // Capture the config passed to the confirmation modal
            let capturedConfig;
            (component as any).confirmationModalRef.openConfirmationPopup.and.callFake((config) => {
                capturedConfig = config;
                // simulate closing the modal
                config.onClose();
            });

            component.onAccessLevelSelect($event, 0);

            expect((component as any).confirmationModalRef.openConfirmationPopup).toHaveBeenCalled();
            expect((component as any).removeDesignationByLabel).toHaveBeenCalledWith(0, $event);
            expect(component.emitChange).toHaveBeenCalled();
        });
    });

    describe('removePermissionByLabel()', () => {
        beforeEach(() => {
            component.admins = [
                {
                    permission: ['tool1:notify', 'tool2:notify']
                }
            ];
        });

        it('should remove the given permission if it exists', () => {
            (component as any).removePermissionByLabel(0, 'tool1:notify');

            expect(component.admins[0].permission).toEqual(['tool2:notify']);
        });

        it('should do nothing if the permission does not exist', () => {
            (component as any).removePermissionByLabel(0, 'tool3:notify');

            expect(component.admins[0].permission).toEqual(['tool1:notify', 'tool2:notify']);
        });
    });

    describe('selectAll()', () => {
        beforeEach(() => {
            component.toolsList = [
                {
                    key: 'tool1',
                    m_key: 'tool1_mobile',
                    has_nom_email: true,
                    target: ['web', 'app']
                },
                {
                    key: 'tool2',
                    m_key: 'tool2_mobile',
                    has_nom_email: false,
                    target: ['web']
                }
            ];

            (component as any).permissionUtility = {
                getPermissionKey: (key: string, op: string) => `${key}:${op}`
            };

            component.admins = [{
                designation: ['custom'],
                permission: []
            }];
        });

        it('should populate permissions correctly for CUSTOM designation', () => {
            component.selectAll(0);

            expect(component.admins[0].permission).toEqual([
                'tool1:*',
                'tool1_mobile:*',
                'tool1:add',
                'tool2:*',
                'tool2:add',
                'tool1:notify'
            ]);

        });

        it('should add only notify permissions when designation is not CUSTOM', () => {
            component.admins = [{
                designation: ['other'],
                permission: []
            }];

            component.selectAll(0);

            expect(component.admins[0].permission).toEqual([
                'tool1:notify'
            ]);
        });
    });

    describe('showCustomizeAccessModal()', () => {
        beforeEach(() => {
            component.admins = [
                { name: 'John Doe' },
                { _name: 'Fallback Name' }
            ];

            component.customizeAccessModalRef = {
                open: jasmine.createSpy('open')
            } as any;

            component.modalTitle = 'Customize Access'; // initial title
        });

        it('should set accessUserName from "name" and open modal for access type', () => {
            component.showCustomizeAccessModal(0);

            expect(component.accessUserName).toBe('John Doe');
            expect(component.blockType).toBe('access');
            expect(component.modalTitle).toBe('Customize Access');
            expect(component.accessUserIdx).toBe(0);
            expect(component.showCustomAccessModal).toBeTruthy();
            expect(component.customizeAccessModalRef.open).toHaveBeenCalled();
        });

        it('should set accessUserName from "_name" and open modal for notification type', () => {
            component.showCustomizeAccessModal(1, 'notification');

            expect(component.accessUserName).toBe('Fallback Name');
            expect(component.blockType).toBe('notification');
            expect(component.modalTitle).toBe('Customize Notifications');
            expect(component.accessUserIdx).toBe(1);
            expect(component.showCustomAccessModal).toBeTruthy();
            expect(component.customizeAccessModalRef.open).toHaveBeenCalled();
        });
    });

    describe('emitChange()', () => {
        beforeEach(() => {
            component.admins = [{ name: 'User1' }];
            component.projectAccessForm = { valid: true } as any;
            component.adminsChanged = new EventEmitter();
            spyOn(component.adminsChanged, 'emit');
            spyOn(component, 'checkHasFullAccess').and.returnValue(true);
            spyOn(component, 'isSelectAtleastOneAccess').and.returnValue(true);
            spyOn(component, 'anyInputIsInvalid').and.returnValue(false);
        });

        it('should emit adminsChanged with correct values when form is valid', fakeAsync(() => {
            component.emitChange();
            tick();

            expect(component.validationStatus).toBeTruthy();
            expect(component.adminsChanged.emit).toHaveBeenCalledWith({
                isValid: true,
                admins: component.admins,
                removed: false
            });
        }));

        it('should emit with isValid = false if checkHasFullAccess returns false', fakeAsync(() => {
            (component.checkHasFullAccess as jasmine.Spy).and.returnValue(false);

            component.emitChange();
            tick();

            expect(component.validationStatus).toBeFalsy();
            expect(component.adminsChanged.emit).toHaveBeenCalledWith(jasmine.objectContaining({ isValid: false }));
        }));

        it('should emit with isValid = false if isSelectAtleastOneAccess returns false', fakeAsync(() => {
            (component.isSelectAtleastOneAccess as jasmine.Spy).and.returnValue(false);

            component.emitChange();
            tick();

            expect(component.validationStatus).toBeFalsy();
            expect(component.adminsChanged.emit).toHaveBeenCalledWith(jasmine.objectContaining({ isValid: false }));
        }));

        it('should emit with isValid = false if anyInputIsInvalid returns true', fakeAsync(() => {
            (component.anyInputIsInvalid as jasmine.Spy).and.returnValue(true);

            component.emitChange();
            tick();

            expect(component.validationStatus).toBeFalsy();
            expect(component.adminsChanged.emit).toHaveBeenCalledWith(jasmine.objectContaining({ isValid: false }));
        }));

        it('should emit with removed = true when emitChange(true) is called', fakeAsync(() => {
            component.emitChange(true);
            tick();

            expect(component.adminsChanged.emit).toHaveBeenCalledWith(jasmine.objectContaining({ removed: true }));
        }));
    });


    describe('changeModel()', () => {
        beforeEach(() => {
            component.admins = [{
                designation: [],
                permission: []
            }];
            component.toolsList = [
                { key: 'tool1', has_nom_email: true },
                { key: 'tool2', has_nom_email: false },
                { key: 'tool3', has_nom_email: true },
            ];
            spyOn(component, 'emitChange');
            spyOn<any>(component, 'removeDesignationByLabel');
            spyOn<any>(component, 'removePermissionByLabel');
        });

        it('should add designation and notify permissions for NOMINATED when checked', () => {
            const event = { target: { checked: true } };
            const index = 0;
            const val = UACProjectDesignations.NOMINATED;

            component.changeModel(event, index, val);

            expect(component.admins[0].designation).toContain(val);
            expect(component.admins[0].permission).toContain('tool1:notify');
            expect(component.admins[0].permission).toContain('tool3:notify');
            expect(component.emitChange).toHaveBeenCalled();
        });

        it('should remove designation and notify permissions for NOMINATED when unchecked', () => {
            const event = { target: { checked: false } };
            const index = 0;
            const val = UACProjectDesignations.NOMINATED;

            component.changeModel(event, index, val);

            expect(component['removeDesignationByLabel']).toHaveBeenCalledWith(index, val);
            expect(component['removePermissionByLabel']).toHaveBeenCalledWith(index, 'tool1:notify');
            expect(component['removePermissionByLabel']).toHaveBeenCalledWith(index, 'tool3:notify');
            expect(component.emitChange).toHaveBeenCalled();
        });

        it('should add designation only when not NOMINATED and checked', () => {
            const event = { target: { checked: true } };
            const index = 0;
            const val = 'some_other_role';

            component.changeModel(event, index, val);

            expect(component.admins[0].designation).toContain(val);
            expect(component.admins[0].permission.length).toBe(0); // No notify added
            expect(component.emitChange).toHaveBeenCalled();
        });

        it('should remove designation only when not NOMINATED and unchecked', () => {
            const event = { target: { checked: false } };
            const index = 0;
            const val = 'some_other_role';

            component.changeModel(event, index, val);

            expect(component['removeDesignationByLabel']).toHaveBeenCalledWith(index, val);
            expect(component['removePermissionByLabel']).not.toHaveBeenCalled();
            expect(component.emitChange).toHaveBeenCalled();
        });
    });
});

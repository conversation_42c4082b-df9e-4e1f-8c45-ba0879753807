/**
 * Created by spatel on 28/9/18.
 */
import {Component, EventEmitter, OnInit, Output, ViewChild} from '@angular/core';
import {Project, User, AuthService, UACPermissions, UACProjectDesignations, ProcoreService, ToastService, PermissionUtility, OPERATIONS, DoNotAskAgainService} from "@app/core";
import {ProjectService} from "@app/core";
import {Router} from "@angular/router";
import {ProcoreProjectLinkerComponent} from "@app/modules/common";
import { AssetsUrl, GenericConfirmationModalComponent } from '@app/shared';

@Component({
    selector: 'user-projects-cards',
    templateUrl: './user-projects.component.html',
    styleUrls: ['./user-projects.component.scss']
})
export class UserProjectsComponent implements OnInit {

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    projects: Array<Project> = [];
    projectsLoading: boolean = false;
    authUser$: User;
    has_access_rights: boolean = false;
    isAddProjectAccess: boolean = false;
    multiDropdown: number = 0;
    projectMinValue: number = 0;
    projectMaxValue: number = 10;
    filteredProjects: Array<Project> = [];
    projectsSearch: string;
    PROJECT_TYPES: Array<any>;
    PROJECT_VALUE: Array<any>;
    selectedFilterProjectTypes: Array<any> = [];
    selectedFilterProjectValue: Array<any> = [];

    /* Icons, Images URLs */
    downArrow = AssetsUrl.siteAdmin.downArrow;
    upArrowGrey = AssetsUrl.siteAdmin.upArrowGrey;
    procoreLogoFCBlack = AssetsUrl.siteAdmin.procoreLogoFCBlack;
    logoPlaceholder = AssetsUrl.siteAdmin.logoPlaceholder;
    noProjectsFoundImage = AssetsUrl.siteAdmin.NoProjectsFoundIllustration;

    siteMainRisksIcons = [
        {icon: AssetsUrl.siteMainRisksIcons.temporaryWorks, name:'Temporary Works'},
        {icon: AssetsUrl.siteMainRisksIcons.confinedSpaces, name:'Confined Spaces'},
        {icon: AssetsUrl.siteMainRisksIcons.safeDiggingPractices, name:'Safe Digging Practices'},
        {icon: AssetsUrl.siteMainRisksIcons.trafficPedestrianInterface, name:'Traffic & Pedestrian Interface'},
        {icon: AssetsUrl.siteMainRisksIcons.isolationGuarding, name:'Isolation & Guarding'},
        {icon: AssetsUrl.siteMainRisksIcons.subcontractorControl, name:'Subcontractor Control'},
        {icon: AssetsUrl.siteMainRisksIcons.workingAtHeight, name:'Working at Height'},
        {icon: AssetsUrl.siteMainRisksIcons.liftingOperations, name:'Lifting Operations'},
        {icon: AssetsUrl.siteMainRisksIcons.occupationalHealth, name:'Occupational Health'},
        {icon: AssetsUrl.siteMainRisksIcons.occupationalRoadRisk, name:'Occupational Road Risk'},
    ];

    @Output()
    hasAddProjectAccess = new EventEmitter<any>(true);

    constructor(
        private projectService: ProjectService,
        private router: Router,
        private authService: AuthService,
        private toastService: ToastService,
        private permissionUtility: PermissionUtility,
        private procoreService: ProcoreService,
        private doNotAskAgainService: DoNotAskAgainService,
    ) { }

    ngOnInit(): void {
        this.fetchProjects();
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
                //toggle "Add New Project" button in project portal based on the boolean
                if(this.userHasAddProjectPermission(this.authUser$)) {
                    this.isAddProjectAccess = true;
                    this.hasAddProjectAccess.emit(true);
                }
            }
        });
        this.PROJECT_TYPES = this.projectService.PROJECT_TYPES;
        this.PROJECT_VALUE = this.projectService.PROJECT_VALUE;
    }

    fetchProjects(){
        this.projectsLoading = true;
        this.projectService.getProjects().subscribe((data: any) => {
            this.projectsLoading = false;
            if (data.success) {
                this.projects = (data.projects || []).map((project, index)=> {
                    project._is_delivery_manager_only = this.isDeliveryManagerOf(project);
                    // project._has_restricted_access = this.hasRestrictedAccessOf(project);
                    project._has_full_access = this.hasFullAccessOf(project) || (this.authUser$.roles.includes('COMPANY_ADMIN') && this.authUser$.uac.project_admins_v1.includes(project.parent_company));
                    return project;
                });
                this.filteredProjects = this.projects;
            } else {
                const message = data.message || 'Failed to get data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    trackById(index, project) {
        return project.id;
    }

    editProject(project: Project, event: Event){
        event.stopPropagation();
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Edit Project',
            confirmLabel: 'Edit',
            title: `Are you sure you want to edit <span class="fw-500">${project.name}</span>?`,
            doNotAskKey: this.doNotAskAgainService.actions.userEditProject,
            onConfirm: () => {
                this.router.navigate(['/project-portal/project', project.id, 'edit']);
            }
        });
    }

    isDeliveryManagerOf(project){
        return (project._my_designations && project._my_designations.length === 1 && project._my_designations.includes(UACProjectDesignations.DELIVERY_MANAGER));
    }

    isFeatureAccessibleToAll(project) {
        return (project.project_section_access && project.project_section_access.rams)
    }

    userHasAddProjectPermission(user){
        return (user.uac && user.uac.permissions && user.uac.permissions[UACPermissions.PROJECT_ADD]);
    }

    // hasRestrictedAccessOf(project){
    //     return (project._my_designations && project._my_designations.includes(UACProjectDesignations.RESTRICTED));
    // }

    hasFullAccessOf(project){
        return (project._my_designations && project._my_designations.includes(UACProjectDesignations.FULL_ACCESS));
    }

    showAlert() {
        const message = 'Do not have the rights to access any project features.';
        this.toastService.show(this.toastService.types.INFO, message);
    }

    authorizeOnProcore(project, event: Event) {
        event.stopPropagation();
        this.procoreService.authorizeOnProcore(project.id, this.authUser$.id, () => {
            this.openProjectSelectionModal(project);
        });
    }

    @ViewChild('procoreLinker', { static: false }) procoreLinker: ProcoreProjectLinkerComponent;
    openProjectSelectionModal(project){
        this.procoreLinker.openModal(project);
    }

    getPossibleUrl(project) {
        if (project.delivery_management_status) {
            return `/project-portal/project/${project.id}/delivery-management`;
        } else if (project.project_section_access.rams) {
            return `/project-portal/project/${project.id}/rams`;
        } else if (project.project_section_access.asset_vehicles) {
            return `/project-portal/project/${project.id}/assets/vehicles`;
        }
    }

    onClickProject(project) {
        if(!project?._is_delivery_manager_only) {
            this.router.navigateByUrl(this.getInitialProjectUrl(project));
        } else {
            if(project?._is_delivery_manager_only && (project?.delivery_management_status || project?.project_section_access?.rams || project?.project_section_access?.asset_vehicles)) {
                this.router.navigateByUrl(this.getPossibleUrl(project));
            } else {
                this.showAlert();
            }
        }
    }

    getInitialProjectUrl(project) {
        let projectAccess = (project.project_section_access || {});
        let allowedTools = [];
        if(Object.keys(projectAccess).length) {
            allowedTools = Object.keys(projectAccess).filter(t => projectAccess[t]);
            if(projectAccess.inspection_tour) { allowedTools.push('inspections');}
            if(projectAccess.collection_notes) { allowedTools.push('collection_note');}
            if(projectAccess.asset_vehicles || projectAccess.asset_equipment || projectAccess.temporary_works) {
                allowedTools.push('assets');
            }
        }
        if(project.delivery_management_status) { allowedTools.push('transport_management'); }
        if(project._my_designations && !project._my_designations.includes(UACProjectDesignations.RESTRICTED)){
            allowedTools.push('induction');
        }
        for(let accessRoute of this.getAccessEligibleRoutes(this.router.config)) {
            let toolkey = accessRoute.data.toolkey;
            if(project && allowedTools.includes(toolkey)) {
                if(project._my_designations && project._my_designations.includes(UACProjectDesignations.CUSTOM) && project._my_permission && this.permissionUtility.isAllowedFor(project._my_permission, toolkey, OPERATIONS.LIST)) {
                    return `/project-portal/project/${project.id}/${this.getRoutePath(projectAccess, accessRoute.path).split(":projectId").join(project.id)}`;
                }
                if(project._my_designations && !project._my_designations.includes(UACProjectDesignations.CUSTOM)){
                    return `/project-portal/project/${project.id}/${this.getRoutePath(projectAccess, accessRoute.path).split(":projectId").join(project.id)}`;
                }
            }
        }
        this.showAlert();
    }

    getAccessEligibleRoutes(routesList) {
        return ((routesList || []).find(route => (route.data && route.data.toolkey === 'parent' && route.data.is_project_portal)) || {}).children || [];
    }

    getRoutePath(access, routePath){
        if(routePath == "time-management/:view"){
            return `time-management/members`;
        } else if(routePath == "progress-photo/:view"){
            return `progress-photo/timeline`;
        } else if(routePath == "assets/:view" && access.asset_vehicles){
            return `assets/vehicles`;
        } else if(routePath == "assets/:view" && access.asset_equipment){
            return 'assets/equipment';
        } else if(routePath == "assets/:view" && access.asset_temporary_works){
            return 'assets/temporary-works';
        } else {
            return routePath || "";
        }
    }

    getSiteMainRisksIcons(name: string) {
        let riskIcon = this.siteMainRisksIcons.filter(risk => risk.name == name);
        if (riskIcon && riskIcon.length > 0) {
            return riskIcon[0].icon;
        }
        return null;
    }

    public openCloseDropdown(index) {
        this.multiDropdown = this.multiDropdown === index ? 0 : index;
    }

    projectsFilter(selectedProjectType = null) {
        if(this.multiDropdown === 1) {
            this.projectMinValue = this.projectMinValue > this.projectMaxValue ? this.projectMaxValue : this.projectMinValue;
            this.selectedFilterProjectValue = this.PROJECT_VALUE.slice(this.projectMinValue, this.projectMaxValue + 1);
        } else if(this.multiDropdown === 2) {
            if (this.selectedFilterProjectTypes.includes(selectedProjectType)) {
                this.selectedFilterProjectTypes.splice(this.selectedFilterProjectTypes.indexOf(selectedProjectType), 1);
            } else {
                this.selectedFilterProjectTypes.push(selectedProjectType.toLowerCase());
            }
        }
        this.applyFilter();
    }

    applyFilter() {
        const projectsSearch = (this.projectsSearch || '').toLowerCase();
        const temp = this.projects.filter(el => {
            let searchFlag = true;
            let filterFlagProjectTypes = true;
            let filterFlagProjectValue = true;
            let filterFlagProjectDivision = true;
            if(projectsSearch.length) {
                searchFlag = (
                    (el.id?.toString()?.toLowerCase()?.indexOf(projectsSearch) !== -1) || 
                    (el.record_id?.toString()?.toLowerCase()?.indexOf(projectsSearch) !== -1) ||
                    (el.name.toLowerCase()?.indexOf(projectsSearch) !== -1) ||
                    (el.main_contact_number_obj.number?.toLowerCase()?.indexOf(projectsSearch) !== -1) ||
                    (el.main_contact_name?.toLowerCase()?.indexOf(projectsSearch) !== -1) ||
                    (el.contractor?.toLowerCase()?.indexOf(projectsSearch) !== -1) ||
                    (el.project_number?.toString()?.toLowerCase()?.indexOf(projectsSearch) !== -1)
                );
            }
            if(searchFlag && this.selectedFilterProjectValue.length) {
                filterFlagProjectValue = this.selectedFilterProjectValue.length ? (this.selectedFilterProjectValue.findIndex(obj => obj.value === el.value) > -1) : true;
            }
            if(searchFlag && this.selectedFilterProjectTypes.length) {
                filterFlagProjectTypes = (this.selectedFilterProjectTypes.length ? this.selectedFilterProjectTypes.includes(el.project_type.toLowerCase()) : true);
            }
            return (searchFlag && filterFlagProjectTypes && filterFlagProjectValue && filterFlagProjectDivision);
        });

        // update the rows
        this.filteredProjects = temp;
    }

    clearAllFilter() {
        this.selectedFilterProjectValue = [];
        this.selectedFilterProjectTypes = [];
        this.projectMinValue = 0;
        this.projectMaxValue = 10;
        this.applyFilter();
    }

    isFilterApplied() {
        return !this.projectsLoading &&
            this.selectedFilterProjectValue.length ||
            this.selectedFilterProjectTypes.length ||
            this.projectMinValue !== 0 ||
            this.projectMaxValue !== 10;
    }

    clearFilter(filter, index = null) {
        if(filter === "projectValue") {
            this.selectedFilterProjectValue = [];
            this.projectMinValue = 0;
            this.projectMaxValue = 10;
            this.applyFilter();
        } else if(filter === "projectTypes") {
            this.selectedFilterProjectTypes.splice(index, 1);
            this.applyFilter();
        }
    }

    isChecked(value) {
        return this.multiDropdown === 2 ? this.selectedFilterProjectTypes.includes(value) : false;
    }
}

<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav *ngIf="!isProjectPortal" [employer]="employer" [companyResolverResponse]="companyResolverResponse" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="col-sm-12 my-3 outer-border" *ngIf="!loadingTake5s">
                <div class="col-sm-12 outer-border-radius">
                <div class="d-flex flex-column flex-sm-row flex-wrap justify-content-between mb-2 pb-2">
                    <h5 class="float-left">Total {{ take5Phrase }} <small>({{ page.totalElements }})</small></h5>
                    <button class="btn btn-sm justify-content-center other-action-btn btn-brandeis-blue d-flex align-items-center pointer-cursor" (click)="openTake5ReportModal()" id="dropdownDlReport1">
                        <span class="medium-font m-font-size material-symbols-outlined mr-2">download</span>
                        <div class="medium-font m-font-size">Download Report</div>
                    </button>
                </div>

                <div class="table-responsive-sm">
                    <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                        [scrollbarV]="true"
                        [virtualization]="false"
                        [loadingIndicator]="loadingInlineTake5s"
                        [rows]="records"
                        [footerHeight]="40"
                        [columnMode]="'force'"
                        [rowHeight]="'auto'"
                        [externalPaging]="true"
                        [count]="page.totalElements"
                        [offset]="page.pageNumber"
                        [limit]="page.size"
                        (page)="pageCallback($event, true)"
                    >
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                {{ take5SglrPhrase }} #
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{row.t5s_number}}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="130">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Date & Time
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{ dayjs(+row.createdAt).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS)}}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Submitted By
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <div style="display: inline-grid">
                                    <span appTooltip>{{row?.user_ref?.first_name}} {{row?.user_ref?.last_name}}</span>
                                    <span appTooltip style="font-size: 11px;">({{row.employer}})</span>
                                </div>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Category
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{row?.conversation_category}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Attended
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{row?.attendees_count}} Member(s)</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold action-column" cellClass="action-column no-ellipsis" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Action
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <button-group
                                    [buttons]="rowButtonGroup"
                                    (onActionClick)="rowBtnClicked($event, row)">
                                </button-group>
                            </ng-template>
                        </ngx-datatable-column>
                    </ngx-datatable>
                    <block-loader [show]="(downloadingTake5)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
                    <i-modal #take5sModalRef [showCancel]="false" [title]="take5SglrPhrase + ' #' + take5_row?.t5s_number + ' Details'" (onClickRightPB)="closeReport($event)" size="lg" rightPrimaryBtnTxt="OK">
                            <table class="table table-sm table-bordered" style="font-size: 14px;">
                                <tbody>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 40%;">
                                            <strong>Submitted By:</strong>
                                        </td>
                                        <td>
                                            {{take5_row?.user_ref?.first_name}} {{take5_row?.user_ref?.last_name}} ({{ take5_row?.employer }})
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 40%;">
                                            <strong>Submitted:</strong>
                                        </td>
                                        <td>
                                            {{ dayjs(take5_row.createdAt).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS)}}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 40%;">
                                            <strong>Conversation Category:</strong>
                                        </td>
                                        <td>
                                            {{ take5_row?.conversation_category }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 40%;">
                                            <strong>Location & Task:</strong>
                                        </td>
                                        <td>
                                            <div [innerHTML]="take5_row?.location_and_task"></div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 40%;">
                                            <strong>Discussion Points & Issues:</strong>
                                        </td>
                                        <td>
                                            <div class="text-break">{{take5_row?.points_and_issues}}</div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 40%;">
                                            <strong>Outcomes & Actions:</strong>
                                        </td>
                                        <td>
                                            <div class="text-break">{{take5_row?.outcomes_and_actions}}</div>
                                        </td>
                                    </tr>
                                    <tr *ngIf="(take5_attendees || []).length">
                                        <td class="tr-bg-dark-color" style="width: 40%;">
                                            <strong>Register:</strong>
                                        </td>
                                        <td class="p-0">
                                            <table class="table table-sm m-0">
                                                <tbody>
                                                <tr *ngFor="let i of take5_attendees; let index = index" [hidden]="index % 2 !== 0">
                                                    <td>{{ take5_attendees[index]?.name }} ({{ take5_attendees[index]?.employer }})</td>
                                                    <td *ngIf="index + 1 < take5_attendees.length">
                                                        {{ take5_attendees[index + 1]?.name }} ({{ take5_attendees[index + 1]?.employer }})
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr *ngIf="take5_row?.images && take5_row?.images.length">
                                        <td colspan="2">
                                            <pop-up-image-viewer [updateStyle]="{'max-height.px': 150, 'max-width.px': 150}" [alt]="'Close Out Image'" [imgArray]="take5_row?.images || []"></pop-up-image-viewer>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                    </i-modal>
                </div>
                <div class="clearfix"></div>
            </div>
            </div>
        </div>
    </div>
</div>
<block-loader [show]="(blockLoader || loadingTake5s)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
<report-downloader #reportDownloader 
    [xlsxOnly]="true" 
    (onFilterSelection)="take5ReportDownload($event)"
    >
</report-downloader>

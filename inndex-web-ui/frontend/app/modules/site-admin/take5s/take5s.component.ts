import {Component, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute} from "@angular/router";
import { HttpParams } from '@angular/common/http';
import * as dayjs from 'dayjs';
import {Observable} from "rxjs";
import {
    Take5sService,
    AuthService,
    User,
    Project,
    Common,
    ToastService,
    HttpService,
    ToolboxTalksService
} from "@app/core";
import {NgbModalConfig, NgbModal, NgbDateStruct} from '@ng-bootstrap/ng-bootstrap';
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import {AppConstant} from "@env/environment";
import { ReportDownloaderComponent } from '@app/modules/common';
import { ActionBtnEntry, IModalComponent } from '@app/shared';

@Component({
    templateUrl: './take5s.component.html',
    providers: [NgbModalConfig, NgbModal],
    styles: [`.tb-attendees .table td:nth-child(3),
        .tb-attendees .table th:nth-child(3) {border-left: 4px solid #dee2e6;}
        .tb-attendees .table tr:nth-of-type(even) td:last-child,
        .tb-attendees .table tr:nth-of-type(even) th:last-child {border-right: 4px solid #dee2e6;}
        .take5DetailsModal .modal-dialog { max-width: 50%; }`]
})
export class Take5sComponent implements OnInit {
    @ViewChild('reportDownloader') private reportDownloader: ReportDownloaderComponent;
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;
    project: Observable<{}>;
    records: Array<any> = [];
    authUser$: User;
    take5_row:  any = [];
    take5_attendees:  Array<any> = [];
    blobType: string = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    employer: any = {};
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    loadingTake5s: boolean = false;
    downloadingTake5: boolean = false;
    common = new Common();
    page = this.common.page;
    isDownload: boolean = false;
    blockLoader: boolean = false;
    downloadRecords: Array<any> = [];
    take5Phrase: string = ``;
    take5SglrPhrase: string = ``;
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    isProjectPortal: boolean = false;
    isMobileDevice: boolean = false;
    isInitTake5s: boolean = false;
    loadingInlineTake5s: boolean = false;
    toolKey: string = 'take_5s';
    rowButtonGroup: Array<ActionBtnEntry> = [];

    constructor(
        private activatedRoute: ActivatedRoute,
        private take5sService: Take5sService,
        private authService: AuthService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private httpService: HttpService,
        private toastService: ToastService,
        private toolboxTalksService: ToolboxTalksService,
    ) {
        this.from_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.to_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
        }
    }

    AppConstant = AppConstant;
    dayjs(n: number) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };

    ngOnInit() {
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });

        if(!this.isProjectPortal) {
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
            });
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        } else {
            this.take5Phrase = this.projectInfo ? this.projectInfo.custom_field.take5_phrase : '';
            this.take5SglrPhrase = this.projectInfo ? this.projectInfo.custom_field.take5_phrase_singlr : '';
            this.from_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(this.projectInfo.createdAt));
            this.initializeTable();
            this.buildRowButtonGroup();
        }
    }

    buildRowButtonGroup(): void {
        const buttonConfigs: ActionBtnEntry[] = [
            {
                key: 'view',
                label: '',
                title: `View ${this.take5SglrPhrase}`,
                mat_icon: 'search',
            },
            {
                key: 'download_pdf',
                label: '',
                title: `Download ${this.take5SglrPhrase} PDF`,
                mat_icon: 'download',
            }
        ];

        this.rowButtonGroup = buttonConfigs;
    }

    projectRetrieved(project: Project){
        this.projectInfo = project;
        this.take5Phrase = this.projectInfo ? this.projectInfo.custom_field.take5_phrase : '';
        this.take5SglrPhrase = this.projectInfo ? this.projectInfo.custom_field.take5_phrase_singlr : '';
        this.from_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(this.projectInfo.createdAt));
        this.initializeTable();
    }

    async initiateDownload(resp) {
        this.blockLoader = true;
        this.from_date = resp.fromDate;
        this.to_date = resp.toDate;
        const fromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.from_date).startOf('day').valueOf();
        const toDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.to_date).endOf('day').valueOf();
        let req = {
            fromDate: fromDate,
            toDate: toDate
        }
        this.take5sService.downloadTake5XlSX(req, this.projectId, () => {
            this.blockLoader = false;
        });
    }

    /**
     * Populate the table with new data based on the page number
     * @param page The page to select
     */
    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean) {
        if (!this.isInitTake5s) {
          this.isInitTake5s = true;
          return;
        }
        this.page.pageNumber = pageInfo.offset;
        this.initializeTable(isPageChange);
        this.scrollToTop()
    }

    private initializeTable(isPageChange?: boolean){
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('sortKey', 'id')
            .set('extra', 'attendees-count');

        if (!isPageChange) {
            this.loadingTake5s = true;
        } else {
            this.loadingInlineTake5s = true;
        }
        this.toolboxTalksService.getProjectBriefingToolRecordsList(this.projectId, this.toolKey, params).subscribe((data: any) => {
            this.loadingTake5s = false;
            this.loadingInlineTake5s = false;
            if (data && data.tool_records) {
                this.records = data.tool_records;
                this.page.totalElements = data.totalCount;
                return data.tool_records;
            }else{
                const message = `Failed to fetch take5s, projectId: ${this.projectId}.`;
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
                return [];
            }
        });
    }

    @ViewChild('take5sModalRef')
    private take5sModalRef: IModalComponent;
    take5DetailModal(row){
        this.blockLoader = true;
        this.toolboxTalksService.getProjectBriefingToolRecord(this.projectId, this.toolKey, row.id, {
            allBriefings: true,
            expand_attendees: true
        }).subscribe((data: any) => {
            this.blockLoader = false;
            if(!data.briefing){
                const message = 'Failed to fetch take5s record.';
                this.toastService.show(this.toastService.types.ERROR, message);
                return false;
            }
            this.take5_row = data.briefing;
            if(this.take5_row.register && this.take5_row.register.length){
                this.take5_attendees = this.take5_row.register.flatMap((item:any) => item.allattendees);
            }
            this.take5sModalRef.open();
        });
    }


    downloadTake5(row) {
        this.downloadingTake5 = true;
        let body = {
            createdAt: row.createdAt,
            companyId: this.employer.id,
            type: 'pdf'
        };

        this.take5sService.downloadTake5(body, row.id, () => {
            this.downloadingTake5 = false;
        });
    }

    async take5ReportDownload(event) {
        await this.initiateDownload(event.selection);
        event.closeFn();
    }

    public openTake5ReportModal() {
        this.reportDownloader.openModal();
    }

    scrollToTop() {
        window.scrollTo({top: 0, behavior: 'smooth'});
    }

    public closeReport(event) {
        event.closeFn();
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actionMap = {
            'view': () => this.take5DetailModal(row),
            'download_pdf': () => this.downloadTake5(row),
        };
        const action = actionMap[entry.key];
        if (action) {
            action();
        }
    }
}



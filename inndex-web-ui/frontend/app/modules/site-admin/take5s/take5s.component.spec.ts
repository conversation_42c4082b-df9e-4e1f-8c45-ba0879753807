import {ComponentFixture, TestBed, tick, fakeAsync} from '@angular/core/testing';
import { Take5sComponent } from '@app/modules/site-admin';
import {
    AuthService, CookieService,
    HttpService, Project,
    ResourceService,
    Take5sService,
    ToastService,
    ToolboxTalksService,
    UserService
} from '@app/core';
import { NgbMomentjsAdapter } from '@app/core/ngb-moment-adapter';
import { ActivatedRoute, Router } from '@angular/router';
import { of } from 'rxjs';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import {HttpParams} from '@angular/common/http';
import {ReportDownloaderComponent} from '@app/modules/common';
import {NO_ERRORS_SCHEMA} from '@angular/core';
import {FormsModule} from '@angular/forms';

// Extend the plugins (needed to use `tz`)
dayjs.extend(utc);
dayjs.extend(timezone);

describe('Take5sComponent', () => {
    let component: Take5sComponent;
    let fixture: ComponentFixture<Take5sComponent>;
    let mockProject: Project;
    let mockActivatedRoute: any;
    let mockTake5sService: any;
    let mockAuthService: any;
    let mockHttpService: any;
    let mockToastService: any;
    let mockToolboxTalksService: any;
    let mockNgbMomentJsAdapter: any;


    beforeEach(async () => {

        mockProject = {
            id: 123,
            name: 'Mock Project',
            createdAt: 1743423604,
            custom_field: {
                take5_phrase: 'Safety First',
                take5_phrase_singlr: 'Be Aware'
            }
        };

        mockActivatedRoute = {
            snapshot: {
                url: [{ path: 'take5S-route' }],
                data: {
                    is_project_portal: true,
                    companyResolverResponse: {
                        company: { name: 'Test Company' },
                        companyProjects: [mockProject],
                        archivedCompanyProjects: [
                            mockProject
                        ]
                    }
                },

            },
            parent: {
                snapshot: {
                    data: {
                        projectResolverResponse: {
                            project: mockProject
                        }
                    }
                }
            },
            params: of({ projectId: 123 })
        };



        mockTake5sService = jasmine.createSpyObj('Take5sService', ['downloadTake5XlSX', 'downloadTake5']);
        mockAuthService = { authUser: of({ id: 1, name: 'Test User' }) };
        mockHttpService = jasmine.createSpyObj('HttpService', ['isMobileDevice']);
        mockToastService = jasmine.createSpyObj('ToastService', ['show']);
        mockToolboxTalksService = jasmine.createSpyObj('ToolboxTalksService', ['getProjectBriefingToolRecordsList', 'getProjectBriefingToolRecord']);

        // Ensure `getProjectBriefingToolRecordsList` returns an observable
        mockToolboxTalksService.getProjectBriefingToolRecordsList.and.returnValue(of({
            tool_records: [{ id: 1, name: 'Record 1' }],
            totalCount: 1
        }));

        mockNgbMomentJsAdapter = jasmine.createSpyObj('NgbMomentjsAdapter', ['dayJsToNgbDate', 'ngbDateToDayJs']);
        spyOn(window, 'scrollTo');

        await TestBed.configureTestingModule({
            declarations: [Take5sComponent, ReportDownloaderComponent],
            imports: [HttpClientTestingModule, FormsModule],
            providers: [
                ResourceService,
                UserService,
                CookieService,
                NgbModal,
                { provide: ActivatedRoute, useValue: mockActivatedRoute },
                { provide: Take5sService, useValue: mockTake5sService },
                { provide: AuthService, useValue: mockAuthService },
                { provide: HttpService, useValue: mockHttpService },
                { provide: ToastService, useValue: mockToastService },
                { provide: ToolboxTalksService, useValue: mockToolboxTalksService },
                { provide: NgbMomentjsAdapter, useValue: mockNgbMomentJsAdapter },
                {
                    provide: Router,
                    useValue: { navigate: jasmine.createSpy('navigate') } // Mock Router navigation
                }
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(Take5sComponent);
        component = fixture.componentInstance;
        component.employer = { id: 1001 };
        fixture.detectChanges();
    });


    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should format date with timezone', () => {
        const testDate = 1710486400000; // Sample timestamp
        // Mock tz() method in dayjs
        spyOn(dayjs.prototype, 'tz').and.callFake(function () {
            return this; // Return the same instance
        });
        const result = component.dayjs(testDate);
        expect(result).toBeDefined();
    });

    it('should set authUser$ on ngOnInit when auth user data exists', () => {
        component.ngOnInit();
        expect(component.authUser$).toEqual({ id: 1, name: 'Test User' });
    });

    it('should set project details if not a project portal', () => {
        component.isProjectPortal = false; // Simulate non-project portal scenario
        component.ngOnInit();

        expect(component.projectId).toBe(123);
        expect(component.employer).toEqual({ name: 'Test Company' });
        expect(component.companyProjects).toEqual([mockProject]);
        expect(component.archivedCompanyProjects).toEqual([mockProject]);
    });

    it('should set phrases and call initializeTable() if project portal', () => {
        component.isProjectPortal = true;
        component.projectInfo = {
            custom_field: { take5_phrase: 'Safety First', take5_phrase_singlr: 'Be Safe' },
            createdAt: 1743423604
        };

        spyOn<any>(component, 'initializeTable'); // Spy on initializeTable()

        component.ngOnInit();

        expect(component.take5Phrase).toBe('Safety First');
        expect(component.take5SglrPhrase).toBe('Be Safe');
        expect((component as any).initializeTable).toHaveBeenCalled(); // Ensure method is called
    });

    it('should fetch project briefing tool records and update component state', fakeAsync(() => {
        component.projectId = 123;
        component.toolKey = 'test-key';
        component.page = { pageNumber: 1, size: 10, totalElements: 0 };

        const mockData = {
            tool_records: [{ id: 1, name: 'Record 1' }],
            totalCount: 1
        };

        // Ensure the spy isn't duplicated
        if (!component['toolboxTalksService'].getProjectBriefingToolRecordsList['calls']) {
            spyOn(component['toolboxTalksService'], 'getProjectBriefingToolRecordsList').and.returnValue(of(mockData));
        }

        (component as any).initializeTable(false); // Accessing private method

        tick();               // Finish observable execution
        fixture.detectChanges(); // Trigger change detection again

        expect(component['toolboxTalksService'].getProjectBriefingToolRecordsList)
            .toHaveBeenCalledWith(123, 'test-key', jasmine.any(HttpParams));

        expect(component.records).toEqual(mockData.tool_records);
        expect(component.page.totalElements).toBe(1);
    }));

    it('should update project details and invoke initializeTable() when projectRetrieved() is called.', () => {
        // Mock mockNgbMomentJsAdapter behavior
        mockNgbMomentJsAdapter.dayJsToNgbDate.and.returnValue({ day: 1, month: 1, year: 2025 });

        // Spy on initializeTable since it's a private method
        spyOn<any>(component, 'initializeTable');

        // Call the method with mock data
        component.projectRetrieved(mockProject);

        // Expectations
        expect(component.projectInfo).toEqual(mockProject);
        expect(component.take5Phrase).toBe('Safety First');
        expect(component.take5SglrPhrase).toBe('Be Aware');
        expect(component.from_date).toEqual({ day: 1, month: 1, year: 2025 });

        // Ensure initializeTable() was called
        expect((component as any).initializeTable).toHaveBeenCalled();
    });

    describe('initiateDownload', () => {
        beforeEach(() => {
            jasmine.getEnv().allowRespy(true); // Allows re-spying

            spyOn(component['ngbMomentjsAdapter'], 'ngbDateToDayJs').and.callFake(() => {
                return {
                    startOf: () => ({ valueOf: () => 1710480000000 }), // Mock start timestamp
                    endOf: () => ({ valueOf: () => 1711065599000 }) // Mock end timestamp
                };
            });

            spyOn(component['take5sService'], 'downloadTake5XlSX').and.callFake((req, projectId, callback) => {
                setTimeout(() => callback(), 0); // Simulate async callback
            });
        });

        it('should initiate download with correct date values and update blockLoader', fakeAsync(() => {
            const mockResp = {
                fromDate: { day: 1, month: 1, year: 2025 },
                toDate: { day: 10, month: 1, year: 2025 }
            };

            component.initiateDownload(mockResp);

            expect(component.blockLoader).toBeTruthy();
            expect(component.from_date).toEqual(mockResp.fromDate);
            expect(component.to_date).toEqual(mockResp.toDate);
            expect(component['ngbMomentjsAdapter'].ngbDateToDayJs).toHaveBeenCalledTimes(2);
            expect(component['take5sService'].downloadTake5XlSX).toHaveBeenCalled();

            tick(); // Simulate async delay
            expect(component.blockLoader).toBeFalsy(); // Ensure loader is reset after callback
        }));
    });

    describe('pageCallback', () => {
        beforeEach(() => {
            spyOn<any>(component, 'initializeTable'); // Spy on private method
            spyOn(component, 'scrollToTop'); // Spy on scroll method
        });

        it('should set isInitTake5s to true and return if it is false', () => {
            component.isInitTake5s = false; // Initial state

            component.pageCallback({ offset: 2 }, true);

            expect(component.isInitTake5s).toBeTruthy(); // Ensures flag is updated
            expect(component['initializeTable']).not.toHaveBeenCalled(); // Should exit early
            expect(component.scrollToTop).not.toHaveBeenCalled();
        });

        it('should update page number and call initializeTable & scrollToTop', () => {
            component.isInitTake5s = true; // Simulate second call
            component.page = { pageNumber: 0 }; // Initial page state

            component.pageCallback({ offset: 3 }, false);

            expect(component.page.pageNumber).toBe(3); // Page number should update
            expect(component['initializeTable']).toHaveBeenCalledWith(false); // Should call initializeTable
            expect(component.scrollToTop).toHaveBeenCalled(); // Should call scrollToTop
        });
    });

    describe('take5DetailModal', () => {
        beforeEach(() => {
            spyOn(component['toolboxTalksService'], 'getProjectBriefingToolRecord').and.returnValue(of({
                briefing: { id: 1, register: [{ allattendees: ['John Doe', 'Jane Doe'] }] }
            }));

            (component['toastService'] as any) = {
                show: jasmine.createSpy('show'),
                types: { ERROR: 'error' }
            };
            (component['take5sModalRef'] as any) = {
                open: jasmine.createSpy('open')
            };
        })

        it('should fetch briefing record and open modal when data exists', () => {
            const mockRow = { id: 1 };
            component.projectId = 123;
            component.toolKey = 'test-key';

            component.take5DetailModal(mockRow);

            expect(component.blockLoader).toBeFalsy(); // Should be reset after API call
            expect(component.take5_row).toEqual({ id: 1, register: [{ allattendees: ['John Doe', 'Jane Doe'] }] });
            expect(component.take5_attendees).toEqual(['John Doe', 'Jane Doe']);
            expect((component as any).take5sModalRef.open).toHaveBeenCalled(); // Modal should open
        });

        it('should show an error message if briefing data is missing', () => {
            (component['toolboxTalksService'] as any).getProjectBriefingToolRecord.and.returnValue(of({})); // No briefing data

            const mockRow = { id: 1 };
            component.take5DetailModal(mockRow);

            expect(component.blockLoader).toBeFalsy(); // Should reset after API call
            expect((component as any).toastService.show).toHaveBeenCalledWith(
                (component as any).toastService.types.ERROR,
                'Failed to fetch take5s record.'
            );
            expect((component as any).take5sModalRef.open).not.toHaveBeenCalled(); // Modal should NOT open
        });
    })

    describe('downloadTake5', () => {
        it('should set downloadingTake5 to true and call take5sService.downloadTake5', () => {
            const mockRow = { id: 1, createdAt: '2024-04-01' };

            component.downloadTake5(mockRow);

            expect(component.downloadingTake5).toBeTruthy(); // Ensure it starts as true
            expect(mockTake5sService.downloadTake5).toHaveBeenCalledWith(
                { createdAt: '2024-04-01', companyId: 1001, type: 'pdf' }, // Expected request body
                1, // Expected row ID
                jasmine.any(Function) // Ensuring a callback function is passed
            );
        });

        it('should set downloadingTake5 to false after downloadTake5 completes', () => {
            const mockRow = { id: 1, createdAt: '2024-04-01' };
            component.downloadTake5(mockRow);

            // Simulate the callback execution
            const callback = mockTake5sService.downloadTake5.calls.mostRecent().args[2];
            callback(); // Manually trigger the callback

            expect(component.downloadingTake5).toBeFalsy(); // Ensure it resets
        });

        it('should set downloadingTake5 to false even if downloadTake5 throws an error', () => {
            mockTake5sService.downloadTake5.and.callFake((body, id, callback) => {
                callback();
                throw new Error('Download failed'); // Simulating an error
            });
            const mockRow = { id: 1, createdAt: '2024-04-01' };
            expect(() => component.downloadTake5(mockRow)).toThrowError('Download failed');
            expect(component.downloadingTake5).toBeFalsy(); // Ensure it's reset even if an error occurs
        });
    })

    describe('take5ReportDownload', () => {
        beforeEach(() => {
            spyOn(component, 'initiateDownload').and.returnValue(Promise.resolve());
        })
        it('Should call initiateDownload with selection data then close fn', async () =>{
            const mockCloseFn = jasmine.createSpy('closeFn');
            const event = {
                selection: { fromDate: '2024-04-01', toDate: '2024-04-02' },
                closeFn: mockCloseFn
            };

            await component.take5ReportDownload(event);
            expect(component.initiateDownload).toHaveBeenCalledWith(event.selection);
            expect(mockCloseFn).toHaveBeenCalled();
        })
    })

    it('should call openModal on reportDownloader when openTake5ReportModal is called', () => {
        (component as any).reportDownloader = jasmine.createSpyObj('ReportDownloaderComponent', ['openModal']); // Spy on child component
        component.openTake5ReportModal();
        expect((component as any).reportDownloader.openModal).toHaveBeenCalled();
    });

    it('should call window.scrollTo with top: 0 and smooth behavior', () => {
        component.scrollToTop();
        expect(window.scrollTo).toHaveBeenCalledWith({ top: 0, behavior: 'smooth' });
    });

    it('should call closeFn when closeReport is executed', () =>{
        const mockCloseFn = jasmine.createSpy('closeFn');
        const event = {
            closeFn: mockCloseFn
        };
        component.closeReport(event);
        expect(mockCloseFn).toHaveBeenCalled();
    })
});


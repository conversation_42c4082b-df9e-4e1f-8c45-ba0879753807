<section class="timesheet-config">
    <button  class="btn btn-sm btn-outline-brandeis-blue m-btn-size-lg ng-star-inserted mt-3" (click)="createNewTimesheetConfig()" [disabled]="disableConfig">
        <div class="d-flex align-items-center justify-content-center">
            <span class="material-symbols-outlined x-large-font mr-1"> add </span> New shift configuration
        </div>
    </button>
    <ng-container *ngIf="modalState.timesheetConfigure.length">
        <div [class.disabled]="disableConfig" >
            <div class="config-header d-flex mt-3">
                <p class="mr-auto mb-0">Shift Configuration</p>
                <p class="ml-auto mb-0">Default</p>
            </div>
            <div class="config-card d-flex mt-3 align-items-center" *ngFor="let item of modalState.timesheetConfigure">
                <div class="left">
                    <div class="d-flex align-items-center">
                        <div class="heading flex-grow-1">
                            <p class="m-0 large-font">{{ item?.configuration_title }}</p>
                        </div>
                        <span class="material-symbols-outlined mr-1 cursor-pointer" (click)="editTimesheetConfig(item.id || item.local_id)">edit_note</span>
                    </div>
                </div>
                <div class="ml-4 mr-3">
                    <span (click)="makeConfigDefault(item.id || item.local_id)" class="material-symbols-outlined cursor-pointer">{{ item.is_default ? 'radio_button_checked' : 'radio_button_unchecked'}}</span>
                </div>
            </div>
        </div>
    </ng-container>

<!--    Timesheet config modal -->
<!--    (onClickLeftSB)="deleteTimesheetConfig(selectedItem.id || selectedItem.local_id)"-->
    <i-modal #timesheetConfigFormPopup
             title="Timesheets: New shift configuration"
             size="s"
             [rightPrimaryBtnTxt]="submitBtnLabel"
             [leftSecondaryBtnIcon]="leftPrimaryBtnIcon"
             [leftSecondaryBtnTxt]="selectedItemId ? 'Delete' : null"
             (onClickLeftSB)="confirmBeforeDelete(selectedItemId)"
             cancelBtnText="Cancel"
             [rightPrimaryBtnDisabled]="!timesheetNewShiftForm.valid"
             (onClickRightPB)="saveTimesheetConfig(timesheetNewShiftForm, $event)"
             [showCancel]="true">

        <form #timesheetNewShiftForm="ngForm" novalidate>
            <ng-container *ngIf="modalState.viewModal">
                <div class="form-group">
                    <label>
                        Configuration title <small class="required-asterisk ">*</small>
                    </label>
                    <input type="text" class="form-control" name="configuration_title" required #configuration_title="ngModel" [(ngModel)]="timesheet_configure.configuration_title" placeholder="Configuration title"/>
                    <div class="alert alert-danger mt-1" [hidden]="!(configuration_title.errors && configuration_title.errors.required)">Configuration title is required</div>
                </div>
                <div class="form-group">
                    <label>
                        Shift type <small class="required-asterisk ">*</small>
                    </label>
                    <ng-select
                            required
                            [clearable]="true"
                            class="mt-1"
                            [items]="shiftType"
                            bindLabel="label"
                            bindValue="id"
                            placeholder="Select shift type"
                            name="shift_type"
                            #shift_type="ngModel"
                            [(ngModel)]="timesheet_configure.shift_type"
                    >
                    </ng-select>
                    <div class="alert alert-danger" [hidden]="!(shift_type.errors && shift_type.errors.required)">Shift type is required</div>
                </div>
                <div class="form-group">
                    <label>Apply to days <small class="required-asterisk ">*</small></label>
                    <ng-select
                            required
                            [clearable]="true"
                            class="mt-1"
                            [items]="weekDays"
                            [multiple]="true"
                            bindLabel="label"
                            bindValue="id"
                            placeholder="Select shift type"
                            name="apply_to_days"
                            #apply_to_days="ngModel"
                            [(ngModel)]="timesheet_configure.apply_to_days"
                    >
                    </ng-select>
                    <div class="alert alert-danger" *ngIf="apply_to_days.errors && apply_to_days.errors.required">Apply to days is required</div>
                </div>

                <!--    Normal working hours multiplier block  -->
                <div class="form-group d-flex align-items-center my-3">
                    <div class="label flex-grow-1 d-flex">
                        Normal working hours multiplier
                        <span class="material-symbols-outlined ml-1 cursor-default" [ngbTooltip]="'This configuration is to be applied for things such as weekend working when there can be a multiple on normal working ours'">info</span>
                    </div>
                    <div>
                        <label class="checkbox-switch-v2 mb-0">
                            <input type="checkbox"
                                   name="working_hours_multiplier_toggle"
                                   id="working_hours_multiplier_toggle" [(ngModel)]="timesheet_configure.working_hours_multiplier_toggle">
                            <span class="slider-v2 round"></span>
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label>
                        Shift start time <small class="required-asterisk ">*</small>
                    </label>
                    <ng-select
                            required
                            [clearable]="true"
                            class="mt-1"
                            [items]="shiftStartTimeOptions"
                            bindLabel="label"
                            bindValue="value"
                            placeholder="Select time"
                            name="shift_start_time_seconds"
                            #shift_start_time_seconds="ngModel"
                            [(ngModel)]="timesheet_configure.shift_start_time_seconds"
                    >
                    </ng-select>
                    <div class="alert alert-danger" *ngIf="shift_start_time_seconds.errors && shift_start_time_seconds.errors.required">Shift start time is required</div>
                </div>

                <div class="form-group">
                    <label>
                        Shift end time <small class="required-asterisk ">*</small>
                    </label>
                    <ng-select
                            required
                            [clearable]="true"
                            class="mt-1"
                            [items]="shiftStartTimeOptions"
                            bindLabel="label"
                            bindValue="value"
                            placeholder="Select time"
                            name="shift_end_time_seconds"
                            #shift_end_time_seconds="ngModel"
                            [(ngModel)]="timesheet_configure.shift_end_time_seconds"
                            (change)="validateEndTime(timesheet_configure)"
                    >
                    </ng-select>
                    <div class="alert alert-danger" *ngIf="shift_end_time_seconds.errors && shift_end_time_seconds.errors.required">Shift end time is required</div>
                    <div class="alert alert-danger" *ngIf="shift_end_time_seconds.touched && (timesheet_configure.shift_start_time_seconds >= timesheet_configure.shift_end_time_seconds)">Shift end time should be grater then shift start time</div>
                </div>

                <!--    Early clock-in block  -->
                <div class="form-group d-flex align-items-center my-3">
                    <div class="label flex-grow-1 d-flex">
                        Allow overtime for early clock in
                    </div>
                    <div>
                        <label class="checkbox-switch-v2 mb-0 align-self-end">
                            <input type="checkbox"
                                   name="early_clock_in_toggle"
                                   id="early_clock_in_toggle" [(ngModel)]="timesheet_configure.early_clock_in_toggle">
                            <span class="slider-v2 round"></span>
                        </label>
                    </div>
                </div>
                <ng-container *ngIf="timesheet_configure.early_clock_in_toggle">
                    <div @slideUpDown>
                        <div class="form-group">
                            <label>
                                Early clock in rounding <small class="required-asterisk ">*</small>
                            </label>
                            <ng-select
                                    required
                                    [clearable]="true"
                                    class="mt-1"
                                    [items]="roundings"
                                    groupBy="heading"
                                    bindLabel="name"
                                    placeholder="Select early clock in"
                                    name="early_clock_in_rounding"
                                    #early_clock_in_rounding="ngModel"
                                    [(ngModel)]="timesheet_configure.early_clock_in_rounding"
                            >
                            </ng-select>
                            <div class="alert alert-danger" *ngIf="early_clock_in_rounding.errors && early_clock_in_rounding.errors.required">Early clock in rounding is required</div>
                        </div>

                        <div class="form-group">
                            <label>
                                Max overtime for early clock in <small class="required-asterisk ">*</small>
                            </label>
                            <ng-select
                                    required
                                    [clearable]="true"
                                    class="mt-1"
                                    [items]="timeIntervals"
                                    bindLabel="label"
                                    bindValue="value"
                                    placeholder="Select max overtime"
                                    name="max_overtime_early_clock_in_seconds"
                                    #max_overtime_early_clock_in_seconds="ngModel"
                                    [(ngModel)]="timesheet_configure.max_overtime_early_clock_in_seconds"
                            >
                            </ng-select>
                            <div class="alert alert-danger" *ngIf="max_overtime_early_clock_in_seconds.errors && max_overtime_early_clock_in_seconds.errors.required">Max overtime for early clock in is required</div>
                        </div>
                        <div class="form-group">
                            <label>
                                Overtime rate multiplier (x Time)<small class="required-asterisk ">*</small>
                            </label>
                            <input
                                    type="number"
                                    class="form-control"
                                    name="clock_in_overtime_rate_multiplier"
                                    required
                                    min="0"
                                    step="any"
                                    #clock_in_overtime_rate_multiplier="ngModel"
                                    [(ngModel)]="timesheet_configure.clock_in_overtime_rate_multiplier"
                                    placeholder="Overtime rate multiplier"
                            />

                            <div class="alert alert-danger mt-1" *ngIf="clock_in_overtime_rate_multiplier.invalid && clock_in_overtime_rate_multiplier.touched">
                                <div *ngIf="clock_in_overtime_rate_multiplier.errors?.required">
                                    Overtime rate multiplier is required.
                                </div>
                                <div *ngIf="clock_in_overtime_rate_multiplier.errors?.min">
                                    Value must be 0 or more.
                                </div>
                            </div>
                        </div>
                    </div>
                </ng-container>
                <div class="form-group">
                    <label>
                        Rounding for late clock in  <small class="required-asterisk ">*</small>
                    </label>
                    <ng-select
                            required
                            [clearable]="true"
                            class="mt-1"
                            [items]="roundings"
                            groupBy="heading"
                            bindLabel="name"
                            placeholder="Select rounding late clock in"
                            name="late_clock_in_rounding"
                            #late_clock_in_rounding="ngModel"
                            [(ngModel)]="timesheet_configure.late_clock_in_rounding"
                    >
                    </ng-select>
                    <div class="alert alert-danger" *ngIf="late_clock_in_rounding.errors && late_clock_in_rounding.errors.required">Rounding for early clock in is required</div>
                </div>
                <div class="form-group">
                    <label>
                        Rounding for early clock out  <small class="required-asterisk ">*</small>
                    </label>
                    <ng-select
                            required
                            [clearable]="true"
                            class="mt-1"
                            [items]="roundings"
                            groupBy="heading"
                            bindLabel="name"
                            placeholder="Select rounding early clock out"
                            name="early_clock_out_rounding"
                            #early_clock_out_rounding="ngModel"
                            [(ngModel)]="timesheet_configure.early_clock_out_rounding"
                    >
                    </ng-select>
                    <div class="alert alert-danger" *ngIf="early_clock_out_rounding.errors && early_clock_out_rounding.errors.required">Rounding for early clock out is required</div>
                </div>

                <!--   Allow overtime for late clock out block -->
                <div class="form-group d-flex align-items-center my-3">
                    <div class="label flex-grow-1 d-flex">
                        Allow overtime for late clock out
                    </div>
                    <div>
                        <label class="checkbox-switch-v2 mb-0 align-self-end">
                            <input type="checkbox"
                                   name="late_clock_out_toggle"
                                   id="late_clock_out_toggle"
                                   [(ngModel)]="timesheet_configure.late_clock_out_toggle">
                            <span class="slider-v2 round"></span>
                        </label>
                    </div>
                </div>
                <ng-container *ngIf="timesheet_configure.late_clock_out_toggle">
                    <div @slideUpDown>
                        <div class="form-group" >
                            <label>
                                Late clock out rounding <small class="required-asterisk ">*</small>
                            </label>
                            <ng-select
                                    required
                                    [clearable]="true"
                                    class="mt-1"
                                    [items]="roundings"
                                    groupBy="heading"
                                    bindLabel="name"
                                    placeholder="Select late clock out rounding"
                                    name="late_clock_out_rounding"
                                    #late_clock_out_rounding="ngModel"
                                    [(ngModel)]="timesheet_configure.late_clock_out_rounding"
                            >
                            </ng-select>
                            <div class="alert alert-danger" *ngIf="late_clock_out_rounding.errors && late_clock_out_rounding.errors.required">Late clock out rounding is required</div>

                        </div>

                        <div class="form-group">
                            <label>
                                Max overtime for late clock out <small class="required-asterisk ">*</small>
                            </label>
                            <ng-select
                                    required
                                    [clearable]="true"
                                    class="mt-1"
                                    [items]="timeIntervals"
                                    bindLabel="label"
                                    bindValue="value"
                                    placeholder="Select max overtime"
                                    name="max_overtime_late_clock_out_seconds"
                                    #max_overtime_late_clock_out_seconds="ngModel"
                                    [(ngModel)]="timesheet_configure.max_overtime_late_clock_out_seconds"
                            >
                            </ng-select>
                            <div class="alert alert-danger" *ngIf="max_overtime_late_clock_out_seconds.errors && max_overtime_late_clock_out_seconds.errors.required">Max overtime for late clock out is required</div>

                        </div>

                        <div class="form-group">
                            <label>
                                Overtime rate multiplier (x Time)<small class="required-asterisk ">*</small>
                            </label>
                            <input
                                    type="number"
                                    class="form-control"
                                    name="clock_out_overtime_rate_multiplier"
                                    required
                                    min="0"
                                    step="any"
                                    #clock_out_overtime_rate_multiplier="ngModel"
                                    [(ngModel)]="timesheet_configure.clock_out_overtime_rate_multiplier"
                                    placeholder="Overtime rate multiplier"
                            />

                            <div class="alert alert-danger mt-1" *ngIf="clock_out_overtime_rate_multiplier.invalid && clock_out_overtime_rate_multiplier.touched">
                                <div *ngIf="clock_out_overtime_rate_multiplier.errors?.required">
                                    Overtime rate multiplier is required.
                                </div>
                                <div *ngIf="clock_out_overtime_rate_multiplier.errors?.min">
                                    Value must be 0 or more.
                                </div>
                            </div>
                        </div>
                    </div>
                </ng-container>
                <div class="form-group">
                    <label>
                        Subtract break time per shift (minutes)<small class="required-asterisk ">*</small>
                    </label>
                    <input type="text" class="form-control"
                           required name="break_time_seconds"
                           [(ngModel)]="timesheet_configure.break_time_seconds"
                           #break_time_seconds="ngModel"
                           placeholder="Enter break time in minutes"
                           pattern="^[0-9]+$"
                    />
                    <div class="alert alert-danger mt-1" *ngIf="break_time_seconds.errors && break_time_seconds.errors?.required">Subtract break time per shift is required</div>
                    <div class="alert alert-danger mt-1" *ngIf="break_time_seconds.errors?.pattern && break_time_seconds.touched">
                        Only whole numbers are allowed.
                    </div>
                </div>
            </ng-container>
        </form>
        <block-loader [show]="modalState.isLoading"></block-loader>
    </i-modal>
</section>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>


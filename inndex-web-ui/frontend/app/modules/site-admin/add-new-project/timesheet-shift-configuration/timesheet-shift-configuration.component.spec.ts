import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TimesheetShiftConfigurationComponent } from './timesheet-shift-configuration.component';
import {FormsModule} from '@angular/forms';

describe('TimesheetShiftConfigurationComponent', () => {
  let component: TimesheetShiftConfigurationComponent;
  let fixture: ComponentFixture<TimesheetShiftConfigurationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ TimesheetShiftConfigurationComponent ],
      imports: [FormsModule]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TimesheetShiftConfigurationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

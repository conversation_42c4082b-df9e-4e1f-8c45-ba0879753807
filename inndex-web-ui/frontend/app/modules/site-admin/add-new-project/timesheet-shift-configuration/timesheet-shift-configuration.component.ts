import {Component, EventEmitter, Input, OnChanges, OnInit, Output, ViewChild} from '@angular/core';
import {ProjectTimesheetConfigure, ROUNDINGS} from '@app/core';
import {GenericConfirmationModalComponent, IModalComponent} from '@app/shared';
import {SLIDE_UP_DOWN_ON_SHOW_HIDE} from '@app/core/animations';
import {NgForm} from '@angular/forms';
import {innDexConstant} from '@env/constants';

@Component({
  animations: [SLIDE_UP_DOWN_ON_SHOW_HIDE],
  selector: 'timesheet-shift-configuration',
  templateUrl: './timesheet-shift-configuration.component.html',
  styleUrls: ['./timesheet-shift-configuration.component.scss']
})
export class TimesheetShiftConfigurationComponent implements OnInit, OnChanges {

  constructor(
  ) { }

  @Input() timesheetConfigData: any;
  @Input() disableConfig: boolean = false;
  @Output() timesheetConfigChange = new EventEmitter();

  shiftType: Array<any> = [
    {id: 1, label: 'Day'},
    {id: 2, label: 'Night'},
  ];
  timeIntervals: Array<any> = [];

  weekDays: Array<{ id: number; label: string; }> = innDexConstant.weekDays;
  timesheet_configure: ProjectTimesheetConfigure = new ProjectTimesheetConfigure;

  roundings = ROUNDINGS;
  shiftStartTimeOptions = [];
  modalState: {
    isLoading: boolean;
    timesheetConfigure: ProjectTimesheetConfigure[];
    viewModal: boolean;
  } = {
    isLoading: false,
    timesheetConfigure: [],
    viewModal: false
  };

  submitBtnLabel = 'Add';

  ROUNDING_KEYS: string[] = [
    'early_clock_in_rounding',
    'late_clock_in_rounding',
    'early_clock_out_rounding',
    'late_clock_out_rounding',
  ];
  leftPrimaryBtnIcon = {
    icon: 'delete',
    class:'material-symbols-outlined cursor-pointer'
  }
  selectedItemId: string = null;

  ngOnInit(): void {
    this.initShiftTime();
    this.initTimeInterval();
  }

  ngOnChanges(): void {
    if (this.timesheetConfigData) {
      this.modalState.timesheetConfigure = this.timesheetConfigData.map(item => ({ ...item }));
    }
  }

  initShiftTime(){
    for (let h = 0; h < 24; h++) {
      for (let m = 0; m < 60; m += 15) {
        const hour12 = h % 12 === 0 ? 12 : h % 12;
        const ampm = h < 12 ? 'AM' : 'PM';
        const label = `${hour12.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')} ${ampm}`;
        const value = h * 3600 + m * 60;
        this.shiftStartTimeOptions.push({ label, value });
      }
    }
  }

  initTimeInterval(){
    for (let minutes = 5; minutes <= 300; minutes += 5) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;

      let label = '';
      if (hours > 0) {
        label += `${hours} hr`;
        if (remainingMinutes > 0) {
          label += ` ${remainingMinutes} min`;
        }
      } else {
        label = `${minutes} min`;
      }

      this.timeIntervals.push({
        label: label,
        value: this.minToSec(minutes)
      });
    }
  }

  @ViewChild('timesheetConfigFormPopup') timesheetConfigFormPopupRef: IModalComponent;
  @ViewChild('timesheetNewShiftForm', { static: true }) timesheetNewShiftForm: NgForm;
  createNewTimesheetConfig(){
    this.submitBtnLabel = 'Add';
    this.timesheet_configure = new ProjectTimesheetConfigure();
    this.selectedItemId = null;
    this.modalState.viewModal = true;
    setTimeout(() => {
      this.timesheetConfigFormPopupRef.open();
    }, 0)
  }

  minToSec(minutes: number | string): number {
    const mins = parseFloat(minutes as any) || 0;
    return Math.round(mins * 60);
  }

  secToMin(seconds: number): number {
    if (typeof seconds !== 'number' || isNaN(seconds)) return 0;
    return Math.round(seconds / 60);
  }

  @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
  confirmBeforeDelete(itemId:string){
    const shift_config = this.modalState.timesheetConfigure.find(item => (item.id || item.local_id) === itemId);
    if(shift_config.is_default && this.modalState.timesheetConfigure.length > 1){
      this.confirmationModalRef.openConfirmationPopup({
        headerTitle: 'Warning',
        title: 'You are trying to delete the default configuration. Please set another configuration as the default before deleting this one.',
        confirmLabel: 'Ok',
        hasCancel: false
      });
    } else {
      this.deleteTimesheetConfig(shift_config.id || shift_config.local_id);
    }
  }

  // Example: "local_1q2z3r_4a5b"
  generateShortLocalId(): string {
    const timestamp = Date.now().toString(36); // Base36 for compactness
    const random = Math.random().toString(36).slice(2, 6);
    return `local_${timestamp}_${random}`;
  }

  saveTimesheetConfig(form:NgForm, event){
   if(form.valid){
     let submittedItem = form.value;
     if(!this.selectedItemId){
       // ADD mode: Add a new item with a new local_id
       submittedItem.local_id = this.generateShortLocalId();
       console.log('assigned local id', submittedItem.local_id);

       if(!this.modalState.timesheetConfigure.length){
         submittedItem.is_default = true;
       }
       submittedItem.break_time_seconds = this.minToSec(submittedItem.break_time_seconds);
       this.modalState.timesheetConfigure.push(submittedItem);
     } else {
       // EDIT mode: Update the existing item in the array
       submittedItem = {...this.timesheet_configure};
       console.log('ignored local id', submittedItem);
       submittedItem.break_time_seconds = this.minToSec(submittedItem.break_time_seconds);
       const index = this.modalState.timesheetConfigure.findIndex(
           item => (item.id || item.local_id) === this.selectedItemId
       );

       if (index !== -1) {
         this.modalState.timesheetConfigure[index] = submittedItem;
       }
     }
     // emit data
     let configData = this.removeIdFromItem(); // removes local_id from each item
     configData = this.extractRoundingValues(configData);
     this.timesheetConfigChange.emit(configData);

     // reset form
     this.timesheetNewShiftForm.resetForm();
     event.closeFn();
   }
    this.selectedItemId = null;
  }

  extractRoundingValues(configArray: any[]): any[] {
    return configArray.map(config => {
      const updated = { ...config };

      this.ROUNDING_KEYS.forEach(key => {
        const val = updated[key];
        if (val && typeof val === 'object' && 'value' in val && 'name' in val) {
          updated[key] = val.value;
        }
      });

      return updated;
    });
  }

  removeIdFromItem(){
    return this.modalState.timesheetConfigure.map(({ local_id, ...rest }) => rest);
  }

  deleteTimesheetConfig(idToDelete:any){
    const index = this.modalState.timesheetConfigure.findIndex(item => (item.id || item.local_id) === idToDelete);
    if (index !== -1) {
      this.modalState.timesheetConfigure.splice(index, 1); // remove item at that index
    }
    const configData = this.removeIdFromItem();
    this.timesheetConfigChange.emit(configData);
    this.selectedItemId = null;
    this.modalState.viewModal = false;
    this.timesheetConfigFormPopupRef.close();
  }

  editTimesheetConfig(itemId:any){
    this.submitBtnLabel = 'Save';
    this.selectedItemId = itemId;
    let item = this.modalState.timesheetConfigure.find(item=>(item.id || item.local_id) === itemId);
    if (!item) return;
    const clone = { ...item };
    clone.break_time_seconds = this.secToMin(clone.break_time_seconds);
    this.timesheet_configure = this.patchRoundingValuesInObject(clone);
    this.modalState.viewModal = true;
    setTimeout(() =>{
      this.timesheetConfigFormPopupRef.open();
    }, 0)

  }

  makeConfigDefault(itemId:any){
    this.modalState.timesheetConfigure =
        this.modalState.timesheetConfigure.map(cfg => ({
          ...cfg,
          is_default: (cfg.id || cfg.local_id) === itemId
        }));
    const configData = this.removeIdFromItem();
    this.timesheetConfigChange.emit(configData);
  }

  patchRoundingValuesInObject(config: any): any {
    const updated = { ...config };

    this.ROUNDING_KEYS.forEach(key => {
      const currentValue = updated[key];

      if (
          currentValue &&
          (typeof currentValue === 'object' && 'direction' in currentValue)
      ) {
        const matched = this.roundings.find(item => {
          if (typeof item.value === 'object' && typeof currentValue === 'object') {
            return item.value.direction === currentValue.direction && item.value.value === currentValue.value;
          } else {
            return item.value === currentValue;
          }
        });

        if (matched) {
          updated[key] = matched;
        }
      }
    });

    return updated;
  }

  validateEndTime(timesheet_configure: ProjectTimesheetConfigure){
    const endTime  = timesheet_configure.shift_end_time_seconds;
    const startTime  = timesheet_configure.shift_start_time_seconds;
    if(startTime >= endTime){
      this.timesheetNewShiftForm.form.setErrors({ 'invalid': true });
    }
  }
}

<form novalidate #qForm="ngForm">
<div class="form-group mb-0">
  <label><b>Questions</b></label>
</div>
<div class="form-group row">
  <label class="col-md-3 d-flex align-items-center">Target language <small class="required-asterisk">*</small></label>
  <div class="col-md-3 pl-0">
    <ng-select required
               placeholder="Choose a language"
               [name]="'quiz_locale_'"
               [(ngModel)]="quiz.lang"
               #quizLocale="ngModel">
          <ng-option *ngFor="let lang of locales" [value]="lang" [disabled]="alreadySelectedLang.includes(lang)">{{lang}}</ng-option>
    </ng-select>
    <div class="alert alert-danger" [hidden]="!(quizLocale.errors && quizLocale.errors.required)">Quiz language is required</div>
  </div>
  <div class="custom-control custom-radio col-md-2">
    <input type="radio" [checked]="quiz.is_default" [name]="'quiz-radio'" form="mediaForm" (click)="markDefaultFn()"
           class="custom-control-input" [id]="'quiz-radio' + index">
    <label class="custom-control-label" [for]="'quiz-radio' + index">Default</label>
  </div>
  <div class="col-12 p-0 mb-2 questions-list" dragula="questions"
       [(dragulaModel)]="quiz.induction_questions"
       (dragulaModelChange)="onQuestionPositionChanges($event)">
    <ng-container *ngFor="let inductionQuiz of (quiz.induction_questions || []); let i = index;trackBy: trackByFn;">
      <div class="bg-white drag-box py-2" >
        <div [class]="'row mx-0 quizRowId_'+i" [id]="'quizRowId_'+i">
          <div class="col-1 text-center align-self-center p-0">
            <i class="fa fa-bars" aria-hidden="true"></i>
          </div>
          <div class="col-7 p-0 align-self-center">
            <textarea class="form-control" [id]="'quiz_' + i" [name]="'quiz_'+i"
                      [(ngModel)]="inductionQuiz.question" [value]="inductionQuiz.question"
                      placeholder="Please enter question" autocomplete="off"
                      required>
            </textarea>
          </div>
          <div class="col-3 text-center align-self-center p-0">
            <button type="button" [ngClass]="{'btn m-auto ml-1': true, 'btn-primary': (!inductionQuiz.question.options || (inductionQuiz.options && inductionQuiz.options.length > 2)), 'btn-danger': inductionQuiz.options && inductionQuiz.options.length < 2}"
                    (click)="openOptionsModal(i)" [disabled]="!inductionQuiz.question">Add Options</button>
          </div>
          <div class="col-1 text-start align-self-center p-0">
            <span class="material-symbols-outlined text-danger cursor-pointer pl-2" (click)="removeQuestion($event, i)" *ngIf="i">
              delete
          </span>
          </div>
        </div>
          <div *ngIf="(inductionQuiz.question && inductionQuiz.options && inductionQuiz.options.length < 2)" class="col-12 mb-n4 px-4">
              <small class="text-danger">At least two options are required on each quiz.</small>
              <input type="text" class="d-none"
                     name="has-invalid-q-options"
                     required
                     [ngModel]="undefined"/>
          </div>
      </div>
    </ng-container>
  </div>
  <div class="col-12 text-left pl-0">
    <button class="btn" (click)="addQuestion()"><i class="fa fa-plus-circle text-primary cursor-pointer">Add Question</i></button>
  </div>
  <div *ngIf="!hasValidQuizDetails" class="col-12">
    <span class="text-danger">At least two options are required on each quiz.</span>
  </div>
</div>
</form>

<input type="text" class="d-none" required
       #qFormState="ngModel" [ngModel]="qForm.valid ? 'valid': undefined"
       [name]="'q-form-state' + index"
       [ngModelOptions]="{standalone: false}"
/>


<i-modal #questionOptionsRef title="Add Options" size="md" [showCancel]="false" (onCancel)="onCancel()" (onClickRightPB)="associateOptionsWithQuestion(addOptionsForm, $event)"
  rightPrimaryBtnTxt="Save" [rightPrimaryBtnDisabled]="!addOptionsForm.valid || currentQuizOptions.length < 2">
    <form novalidate #addOptionsForm="ngForm">
      <div class="form-group" *ngIf="showModal">
        <label> <strong>Question:</strong> </label>
        <br>
        <label class="text-break"> {{ quiz.induction_questions[questionIndex]?.question }} </label>
        <br>
        <label> <strong>Options(At least 2)</strong> <small class="required-asterisk ">*</small></label>
        <br>
        <label>Use the tick boxes to indicate the correct answer/s</label>
        <table class="table table-bordered">
          <tbody>
          <ng-container *ngFor="let option of (currentQuizOptions || []) trackBy : trackByRowIndex; let i = index;">
            <tr>
              <td>
                <input type="text" class="form-control" [name]="'option' + i"
                       #optionField="ngModel"
                       [(ngModel)]="currentQuizOptions[i]"
                       placeholder="Please enter option" required autocomplete="off">
                <div class="alert alert-danger mb-0 p-0 pl-2" [hidden]="!(optionField.errors && optionField.errors.required)">Option is required.</div>
              </td>
              <td class="text-center align-middle">
                <input type="checkbox" (change)="correctOption($event, i)"
                       [checked]="currentQuizOptions[i] && isAmongCorrectOption(i)"
                       [disabled]="!currentQuizOptions[i]">
              </td>
              <td class="text-center">
                <span class="material-symbols-outlined text-danger cursor-pointer" (click)="removeOption($event, i)" *ngIf="!(i == 0)">
                  delete
              </span>
              </td>
            </tr>
          </ng-container>
          <tr>
            <td colspan="3">
              <i class="fa fa-plus-circle text-primary cursor-pointer" (click)="addOption()">Add Option</i>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </form>
</i-modal>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

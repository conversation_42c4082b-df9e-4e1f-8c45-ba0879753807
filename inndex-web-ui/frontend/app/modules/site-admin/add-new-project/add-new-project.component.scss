.fatigue-head {
  padding-top: 10px;
  &-checkbox {
    margin-top: 8px;
    width: 25px;
    height: 15px;
    padding-right: 2px;
  }
  &-label {
    padding-left: 10px;
  }
  &-input {
    width: 60px;
    height: 30px;
    margin: 0px 2px;
  }
}

.pt-10 {
  padding-top: 10px;
  padding-left: 10px;
}

.custom-form-width {
  max-width: 700px !important;
}

.questions-list {
  max-width: 100%;
  // min-height: 60px;
  border-radius: 4px;
  overflow: auto;
}

.bb-2silver {
    border-bottom: 1px solid var(--ebony_clay);
}

.configureSignOffLink {
    color: var(--spanish-gray);
}

.heightInherit { height: inherit}

.selected-companies-container {width: 100%;}
.selected-companies-container .selected-company-item:last-child { border-bottom: none;}
.selected-companies-container .selected-company-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
}
.btn.view-selected-sc-list { color: var(--brandeis-blue) !important; }

import {Component, EventEmitter, Input, OnInit, Output, ViewChild, ViewEncapsulation} from "@angular/core";
import { TreeviewI18n, TreeviewItem, DropdownTreeviewComponent, TreeviewHelper } from 'ngx-treeview';
import { DropdownTreeviewSelectI18n, ResourceService, ToastService, ProjectIntegrationsService, Project, CreateEmployer, ProjectService} from '@app/core'
import { GenericConfirmationModalComponent, IModalComponent } from "@app/shared";
import { AssetsUrl } from "@app/shared/assets-urls/assets-urls";
@Component({
    selector: 'project-integrations-setting',
    templateUrl: './project-integrations-setting.component.html',
    providers: [
        { provide: TreeviewI18n, useClass: DropdownTreeviewSelectI18n },
    ],
    encapsulation: ViewEncapsulation.None  // Use to disable CSS Encapsulation for this component
})

export class IntegrationsSetting implements OnInit {
    AssetsUrlSiteAdmin = AssetsUrl.siteAdmin;
    int_section: string = 'all_integrations';
    checkedProcore:boolean = false;
    enableCompanyFilter:boolean = false;
    selectedTools:any = '';
    selectedFolder: any= null;
    selectedCompany:any = null;
    isValid:boolean = true;
    itCategory:any=[];
    checkCompany: boolean = true;
    A_SITE_PROJECT_CONFIG:string = 'asite_project_config';

    asiteDisabled: boolean = true;
    procoreDisabled: boolean = true;

    @Input()
    project: Project = new Project;

    @Input()
    company: CreateEmployer = new CreateEmployer;

    @Output()
    configSectionChanged: any = new EventEmitter<any>();

    selectedWorkspaceId: number;

    blockLoader: boolean = false;

    items: TreeviewItem[];

    @ViewChild(DropdownTreeviewComponent, { static: false }) dropdownTreeviewComponent: DropdownTreeviewComponent;
    filterText: string ='';
    toolsList: Array<{
        key:string;
        m_key:string;
        label:string;
        phrase_key:string;
        container:string;
        target:Array<string>;
        has_nom_email:boolean;
        supports_asite:boolean;
        disabled?:boolean;
    }> = [];
    toolLabels: { [key: string]: string } = {};
    workspacesList: Array<{
        Workspace_Name:string;
        Workspace_Id_Str:string;
        Workspace_Id:string;
        Folders_Uri:string;
    }> = [];
    asiteMappedTools: Array<{
        tool_key: string;
        folder_name: string;
        folder_id: string;
        company_id?: number;
    }> = [];
    folderOptions: TreeviewItem;
    asiteProjectConfig: {
        enabled: boolean;
        workspace_id: number;
        tools_mapping: Array<{
            tool_key: string;
            folder_name: string;
            folder_id: string;
            company_id?: number;
        }>;
    } = {
        "enabled": false,
        "workspace_id": null,
        "tools_mapping": [],
    };
    private dropdownTreeviewSelectI18n: DropdownTreeviewSelectI18n;

    transferDetails:{
        [key: string]: {
            label: string;
            has_company_filter: boolean;
        }
    } = {
        "daily_activities": { label: "Upon the report being signed off", has_company_filter: true},
        "rams": { label: "Daily at 00:00", has_company_filter: false},
        "progress_photos": { label: "Daily at 00:00", has_company_filter: false},
        "permit_tool": { label: "On closeout of permit", has_company_filter: true},
        "time_management": { label: "Weekly, Sundays at 00:00", has_company_filter: false},
        "close_calls": { label: "Upon submission being closed out", has_company_filter: true},
        "good_calls": { label: "Upon submission", has_company_filter: true},
        "delivery_notes": { label: "Upon submission", has_company_filter: true},
        "toolbox_talks": { label: "Daily at 00:00", has_company_filter: false},
        "induction": { label: "Weekly, Sundays at 00:00", has_company_filter: false},
        "inspections": { label: "Upon all actions being closed out", has_company_filter: true},
        "incident_report": { label: "Upon incident report being closed out", has_company_filter: true},
        "assets": { label: "Weekly, Sundays at 00:00", has_company_filter: false},
        "transport_management": { label: "Weekly, Sundays at 00:00", has_company_filter: false},
        "take_5s": { label: "Upon submission", has_company_filter: true},
    };

    constructor(
        public i18n: TreeviewI18n,
        private resourceService: ResourceService,
        private toastService: ToastService,
        private projectService: ProjectService,
        private projectIntegrationsService: ProjectIntegrationsService
    ) {
        this.dropdownTreeviewSelectI18n = i18n as DropdownTreeviewSelectI18n;
    }

    ngOnInit() {
        this.blockLoader = true;
        this.resourceService.getToolList().subscribe((data: any) => {
            this.toolsList = (data.tool_list || []).filter(t => t.supports_asite);
            this.toolsList.forEach(item => { this.toolLabels[item.key] = this.project.custom_field[item.phrase_key]; });
            this.toolLabels['inspections'] = "Inspections";
            this.toolLabels['toolbox_talks'] = "Toolbox Talks";
            this.toolLabels['incident_report'] = "Incident Report";
            this.toolLabels['progress_photos'] = "Progress Photos";
        });
        this.projectIntegrationsService.checkAsiteConfigStatus(this.project.id, this.company.id).subscribe((data: any) => {
            this.asiteDisabled = data.disabled || false;
            this.blockLoader = false;
        });
        this.loadSavedSettings();
    }

    @ViewChild('projectSettings') projectSettings;
    addLinkedToolsModel(){ 
        this.blockLoader = true;
        this.projectIntegrationsService.getAsiteFoldersList(this.project.id, this.company.id, this.asiteProjectConfig.workspace_id).subscribe((data: any) => {
            if(data.success && data.workspace_folders){
                this.folderOptions = this.transformAsiteListToTreeview(data.workspace_folders.folders);
                this.blockLoader = false;
                this.proccessModalOpen();
            } else {
                const message = data.message || 'Failed to fetch Asite folders.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }


    @ViewChild('linkToolHtmlRef')
    private linkToolHtmlRef: IModalComponent;

    proccessModalOpen() {
        if(this.projectSettings){
            this.projectSettings.reset();
            this.projectSettings.form.statusChanges.subscribe(status => {
                this.isValid =  status !== 'VALID';
            });
        }

        this.itCategory = [this.folderOptions];
        this.selectedFolder = null;
        this.selectItem(null);
        this.linkToolHtmlRef.title = "Link New Tool";
        this.linkToolHtmlRef.rightPrimaryBtnTxt = "Save";
        this.linkToolHtmlRef.open();
    }

    getToolLabel(tool_key) {
        return this.toolLabels[tool_key];
    }

    saveAsiteProjectConfig() {
        this.asiteMappedTools.push({
            tool_key: this.selectedTools,
            folder_id: this.selectedFolder.value,
            folder_name: this.selectedFolder.text,
            company_id: this.selectedCompany || 0
        });
        this.constructAndSaveSettings();
    }

    constructAndSaveSettings(toggleOnly = false) {
        this.blockLoader = true;
        let request = {
            'setting_name' : this.A_SITE_PROJECT_CONFIG,
            'setting_value': {
                "enabled": this.asiteProjectConfig.enabled,
                "workspace_id": this.asiteProjectConfig.workspace_id,
                "tools_mapping": this.asiteMappedTools,
            }
        };
        this.projectService.saveProjectSetting(this.project.id, request).subscribe((data: any) => {
            this.blockLoader = false;
            if(data.success && data.project_setting && data.project_setting.value) {
                this.asiteProjectConfig = data.project_setting.value;
                this.asiteMappedTools = this.asiteProjectConfig.tools_mapping;
                (!toggleOnly) && this.linkToolHtmlRef.close();
            } else {
                const message = data.message || 'Failed to save Asite project settings.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    loadSavedSettings() {
        if(this.project.id) {
            this.resourceService.getProjectSettingsByName([this.A_SITE_PROJECT_CONFIG], this.project.id).subscribe((data: any) => {
                if (data.success && data.project_settings && data.project_settings[this.A_SITE_PROJECT_CONFIG]) {
                    this.asiteProjectConfig = data.project_settings[this.A_SITE_PROJECT_CONFIG];
                    this.asiteMappedTools = this.asiteProjectConfig.tools_mapping;
                    this.selectedWorkspaceId = this.asiteProjectConfig.workspace_id;
                }
            });
        }
    }

    editLinkedToolsModel(linkedTool){
        this.blockLoader = true;

        if(this.projectSettings){
            this.projectSettings.reset();
            this.projectSettings.form.statusChanges.subscribe(status => {
                this.isValid =  status !== 'VALID';
            });
        }
        this.selectedCompany = null;
        this.selectedTools = linkedTool.tool_key;
        this.selectedFolder = new TreeviewItem({value: linkedTool.folder_id, text: linkedTool.folder_name});
        // this.selectedFolder.text = linkedTool.folder_name;
        // this.itCategory = this.getCategory();
    
        this.projectIntegrationsService.getAsiteFoldersList(this.project.id, this.company.id, this.asiteProjectConfig.workspace_id).subscribe((data: any) => {
            if(data.success && data.workspace_folders){
                this.folderOptions = this.transformAsiteListToTreeview(data.workspace_folders.folders, '', linkedTool.folder_id);
                this.blockLoader = false;
                this.itCategory = [this.folderOptions];

                this.linkToolHtmlRef.title = "Edit Tool";
                this.linkToolHtmlRef.rightPrimaryBtnTxt = "Update";
                this.linkToolHtmlRef.open();

            } else {
                const message = data.message || 'Failed to fetch Asite folders.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });

    }

    changePage(integration_type = 'asite_integration') {
        this.int_section = integration_type;
        this.blockLoader = true;
        this.projectIntegrationsService.getAsiteWorkspaces(this.project.id, this.company.id).subscribe((data: any) => {
            if(data.success && data.workspaces){
                this.workspacesList = data.workspaces;
                this.blockLoader = false;
                console.log(data);
            } else {
                const message = data.message || 'Failed to fetch Asite workspaces.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
        this.configSectionChanged.emit({
            configuring_integration: true
        });
    }
    backPage(){
        this.int_section = 'all_integrations';
        this.configSectionChanged.emit({
            configuring_integration: false
        });
    }
    // onSelectionChange() {
    //     if (this.asiteProjectConfig.workspace_id) {
    //       this.selectedWork = true;
    //     } else {
    //       this.selectedWork = false;
    //     }
    // }
    // onSelect(){
    //     console.log(this.selectedTools);
    //     console.log(this.selectedFolder);
    //     console.log(this.selectedCompany);
    // }
 
    // onSelectedChange(e){
    //     console.log(e);
    // }
    // onFilterChange(e){
    //     console.log(e);
    // }
    // ngOnChanges(): void {
    //     this.updateSelectedItem();
    // }
    
    select(item: TreeviewItem): void {
        if(item == this.selectedFolder){
            this.selectItem(null);
            this.dropdownTreeviewSelectI18n.selectedItem = null;
            this.selectedFolder = null;
        } else {
            this.selectItem(item);
            this.selectedFolder = item;
        }
    }

    // private updateSelectedItem(): void {
    //     if (this.items) {
    //         const selectedItem = TreeviewHelper.findItemInList(this.items, this.value);
    //         this.selectItem(selectedItem);
    //     }
    // }
    private selectItem(item: TreeviewItem): void {
        if (this.dropdownTreeviewSelectI18n.selectedItem !== item) {
            this.dropdownTreeviewSelectI18n.selectedItem = item;
            if (this.dropdownTreeviewComponent) {
                this.dropdownTreeviewComponent.onSelectedChange([item]);
            }

            // if (item) {
            //     if (this.value !== item.value) {
            //         this.value = item.value;
            //         this.valueChange.emit(item.value);
            //     }
            // }
        }
    }
    // isCheckBoxSelected(item){
    //     // this.LinkedToolModelconfig.primaryBtnDisabled = this.isValid || this.selectedFolder ==null;
    //     return this.selectedFolder.value === item.value;
    // }

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    deleteConfirmModal(index:number){
        this.confirmationModalRef.openConfirmationPopup({
            title:"Are you sure you want to delete the linked tool?",
            hasCancel: true,
            headerTitle: 'Remove Link',
            cancelLabel: 'Cencel',
            confirmLabel: "Delete",
            onConfirm: () => {
                this.asiteProjectConfig.tools_mapping.splice(index, 1);
                this.syncAsiteConfigData();
            }
        });
    }

    syncAsiteConfigData() {
        this.blockLoader = true;

        let request = {
            'setting_name' : this.A_SITE_PROJECT_CONFIG,
            'setting_value': this.asiteProjectConfig
        };
        this.projectService.saveProjectSetting(this.project.id, request).subscribe((data: any) => {
            this.blockLoader = false;
            if(data.success && data.project_setting && data.project_setting.value) {
                this.asiteProjectConfig = data.project_setting.value;
                this.asiteMappedTools = this.asiteProjectConfig.tools_mapping;
            } else {
                const message = data.message || 'Failed to save Asite project settings.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    onCheckboxChange(event: Event) {
        const checkbox = event.target as HTMLInputElement;
        const isChecked = checkbox.checked;
    
        if (!isChecked && this.asiteProjectConfig.workspace_id) {
            checkbox.checked = true;
            this.confirmationModalRef.openConfirmationPopup({
                title: `Asite Integration: You are about to turn off the integration. The settings will be saved, but it will not be available to use across the web portal. To make available, simply turn the toggle switch back on. Would you like to continue? `,
                hasCancel: true,
                headerTitle: '',
                cancelLabel: 'Cancel',
                confirmLabel: "Disable",
                onConfirm: () => {
                    this.asiteProjectConfig.enabled = false;
                    checkbox.checked = false;
                    this.constructAndSaveSettings(true);
                }
            });
        } else {
            this.asiteProjectConfig.enabled = isChecked;
            this.constructAndSaveSettings(true);
        }
    }

    onWorkspaceChange(newWorkspaceId) {
        this.confirmationModalRef.openConfirmationPopup({
            title: `You are about to change the workspace. All tool/folder mappings will be deleted.`,
            hasCancel: true,
            headerTitle: 'Would you like to continue?',
            cancelLabel: 'No',
            confirmLabel: "Yes",
            onConfirm: () => {
                this.asiteProjectConfig.workspace_id = newWorkspaceId.Workspace_Id;
                this.asiteProjectConfig.tools_mapping = [];
                this.syncAsiteConfigData();
            },
            onClose: () => {
                this.selectedWorkspaceId = this.asiteProjectConfig.workspace_id;
            }
        });
    }

    editConfirmModal():boolean {
        this.confirmationModalRef.openConfirmationPopup({
            title:"Note A link has already been established based on the chosen tool and company options selected. To make any changes you can modify or remove the existing link",
            hasCancel: false,
            headerTitle: 'Note',
            confirmLabel: "Ok",
            onConfirm: () =>{

            }
        });
        return true
    }

    transformAsiteListToTreeview(folder, parentPath = "", selectedFolderId = ""):TreeviewItem {
        const path = '';//`${parentPath}/${folder.FolderName.replace(/\s+/g, '_')}`;
        const folderId = (folder.FolderID.match(/^[^$]+/) || []).shift();
        const result = new TreeviewItem({
            text: folder.FolderName,
            value: (folder.FolderID.match(/^[^$]+/) || []).shift(),
            checked: false,//(selectedFolderId == folderId) ? true : false,
            collapsed: true,
            children: []
        });
    
        if (folder.folderVO && folder.folderVO.length > 0) {
            result.children = folder.folderVO.map(child => this.transformAsiteListToTreeview(child, path, selectedFolderId));
        } else if (folder.folderVO && typeof folder.folderVO === "object" && folder.folderVO.FolderID) {
            result.children = [this.transformAsiteListToTreeview(folder.folderVO, path, selectedFolderId)];
        } else {
            delete result.children;
        }
    
        return result;
    }
 
    getCategory(){
        this.blockLoader = false;
        this.projectIntegrationsService.getAsiteFoldersList(this.project.id, this.company.id, this.asiteProjectConfig.workspace_id).subscribe((data: any) => {
            if(data.success && data.workspace_folders){
                this.folderOptions = this.transformAsiteListToTreeview(data.workspace_folders.folders);
                this.blockLoader = false;
                console.log(data);
            } else {
                const message = data.message || 'Failed to fetch Asite folders.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });

        return [this.folderOptions];
    }  
}

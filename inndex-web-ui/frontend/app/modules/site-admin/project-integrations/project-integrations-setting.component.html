<style>
    .text-search {
        max-width: 100%;
        width: 100%;
    }
    input.ngx-search[type="search"] {
        background: var( --anti-flash-white);
        border: none;
        border-bottom: none;
        margin-bottom: 0px !important;
        border-radius: inherit;
        height: 30px;
    }
    .ng-select.ng-select-single .ng-select-container {
        height: 30px;
    }
    .table th, .ckeditor-content table th, .table td, .ckeditor-content table td {
        padding: 0.5rem
    }
    .mapping-tool-table {
        color: var(--spanish-gray);
    }
    .table-th{
        background-color: #F9F9F9;
        width: 125px;
    }
    .font-small {
        font-size: medium;
    }
    /* folders option rendering out of parent popup */
    .link-tool-pop-up-form div.dropdown.show > button.dropdown-toggle::after {
        right: 1.6rem !important;
    }
    .link-tool-pop-up-form .custom-dropdown .dropdown.show .dropdown-menu {
        width: 94%;
    }
    .link-tool-pop-up-form .dropdown-menu {
        top: 55%;
        left: 15px;
    }
    .link-tool-pop-up-form .dropdown.show, .link-tool-pop-up-modal.modal-body {
        position: initial !important;
    }
</style>

<ng-container *ngIf="int_section == 'all_integrations'">
    <div class="row justify-content-md-center p-0 mt-4">
        <div class="col-md-6 mb-4">
            <div class="text-center my-3"><h4>INTEGRATIONS</h4></div>
            <div class="col-12 p-0">
                <div class="col horizontal-center justify-content-between px-0 pt-3"> 
                    <span><img [src]="AssetsUrlSiteAdmin.A_Site_Logo" height="17px"/></span>
                    <span>
                        <button class="btn btn-sm btn-outline-brandeis-blue mr-4" *ngIf="asiteProjectConfig.enabled" (click)="changePage('asite_integration')">Configure</button>
                        <div class="custom-control custom-switch d-inline-block" >
                            <input type="checkbox" class="custom-control-input" [disabled]="asiteDisabled" name="asite_integration" id="asite_integration"
                                (change)="onCheckboxChange($event)" [checked]="asiteProjectConfig.enabled">
                            <label class="custom-control-label" for="asite_integration"></label>
                        </div>
                    </span>
                </div>
                <hr/>
                <div class="col horizontal-center justify-content-between px-0 pb-3">
                    <span><img [src]="AssetsUrlSiteAdmin.Procore_Logo_FC_Black_CMYK_1_1" height="17px"/></span>
                    <span>
                        <button class="btn btn-sm btn-outline-brandeis-blue mr-4" *ngIf="checkedProcore" (click)="changePage('procore_integration')">Configure</button>
                        <div class="custom-control custom-switch d-inline-block">
                            <input type="checkbox" class="custom-control-input" [disabled]="procoreDisabled" name="procore_integration" id="procore_integration"
                            [(ngModel)]="checkedProcore" checked="false">
                            <label class="custom-control-label" for="procore_integration"></label>
                        </div>
                    </span>
                </div>
            </div>
        </div>
    </div>
</ng-container>

<ng-container *ngIf = "int_section == 'asite_integration'">
    <a class="nav-a1 position-absolute ml-3 cursor-pointer" (click)="backPage()"><i class="fa fa-step-backward"></i> Back</a>
    <div class="row justify-content-md-center p-0 mt-4">
        <div class="col-md-6">
            <div class="text-center my-3">
                <img [src]="AssetsUrlSiteAdmin.A_Site_Logo" height="17px"/>
            </div>
            <div class="row">
                <div class="col-12">
                    <small class="font-weight-normal">Select Asite Workspace to start linking innDex tools with asite folders.</small>
                    <h6 class="font-weight-bold mt-4">Select Asite Workspace</h6>
                    <ng-select
                        class="dropdown-list sm-select filter-v2-select h-auto w-100"
                        placeholder="Select Workspace" [clearable]="false"
                        [items]="workspacesList" bindLabel="Workspace_Name" bindValue="Workspace_Id"
                        [(ngModel)]="selectedWorkspaceId" (change)="onWorkspaceChange($event)"
                    ></ng-select>

                    <div class="row py-4" *ngIf="asiteProjectConfig.workspace_id">
                        <div class="col-md-6">
                            <span class="font-weight-bold float-left">Linked Tools</span>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-outline-brandeis-blue float-right" (click)="addLinkedToolsModel()">
                                <span class="font-weight-bold">Link New Tool</span>
                            </button>
                        </div>
                    </div>
                    <ng-container *ngFor="let device of (asiteMappedTools || []); let i = index;" >
                        <div class="row mb-2">
                            <div class="col-11 pr-2">
                                <table class="table table-bordered mapping-tool-table mb-0">
                                    <tbody>
                                        <tr>
                                            <td class="table-th">innDex Tool</td>
                                            <td>{{getToolLabel(asiteMappedTools[i].tool_key)}}</td>
                                        </tr>
                                        <tr>
                                            <td class="table-th" >Asite Folder</td>
                                            <td>{{asiteMappedTools[i].folder_name}}</td>
                                        </tr>
                                        <tr *ngIf="asiteMappedTools[i].company_id && asiteMappedTools[i].company_name">
                                            <td class="table-th">Company</td>
                                            <td>{{asiteMappedTools[i].company_name}}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-1 d-flex px-0">
                                <!-- <button class="btn btn-sm" (click) = "editLinkedToolsModel(asiteMappedTools[i])" >
                                    <span class="material-symbols-outlined btn-text-brandeis-blue">edit_note</span>
                                </button> -->
                                <button class="px-0 btn btn-sm text-danger" (click) = "deleteConfirmModal(i)" >
                                    <span class="material-symbols-outlined">delete</span>
                                </button> 
                            </div>
                        </div>
                    </ng-container>
                </div>
            </div>
        </div>
    </div>
</ng-container>

<ng-container *ngIf = "int_section == 'procore_integration'">
    <a class="nav-a1 position-absolute" (click)="backPage()"><i class="fa fa-step-backward"></i> Back</a>
    <div class="row justify-content-md-center p-0 mt-4">
        <div class="col-md-6">
            <div class="text-center my-3">
                <img [src]="AssetsUrlSiteAdmin.Procore_Logo_FC_Black_CMYK_1_1" height="17px"/>
            </div>
            <div class="row">
                <div class="col-12">
                    <div>Settings not avaiable. </div>
                    <small class="font-weight-normal">Procore integration is available from the project list section currently.</small>
                </div>
            </div>
        </div>
    </div>
</ng-container>

<!-- Add Link Tool HTML-->
<i-modal #linkToolHtmlRef title="Link New Tool" size="md" cancelBtnText="Close" (onClickRightPB)="saveAsiteProjectConfig()" rightPrimaryBtnTxt="Save" [rightPrimaryBtnDisabled]="isValid || selectedFolder == null" modalBodyClass="link-tool-pop-up-modal">
    <form novalidate #projectSettings="ngForm" class="link-tool-pop-up-form">
        <div class="mt-2 form-group">
            <div class="mt-2 form-group">
                <div class="row">
                    <div class="col-1 py-2 pr-0"><span class="material-symbols-outlined">info</span></div>
                    <div class="pl-1 col-11 ">
                        <span>From the list below, choose which tool's document is to be pushed to which Asite folder.</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-4 form-group">
            <ng-select
                class="custom-placeholder w-100 dropdown-list h-auto"
                placeholder="innDex Tool"
                [items]="toolsList"
                [multiple]="false"
                name="inndex_tool"
                [(ngModel)]="selectedTools"
                [searchable]="false"
                bindValue="key"
                appendTo="body"
                required
            >
                <ng-template ng-header-tmp>
                    <div style="width:100%; max-width:100%;" class="text-search horizontal-center px-2">
                        <span class="material-symbols-outlined font-small">search</span>
                        <input style="width: 100%; line-height: 24px" placeholder="Search" class="ngx-search " type="search"/>
                    </div>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" class="horizontal-center" let-item$="item$" let-index="index" let-search="searchTerm">
                    <input type="checkbox" [checked]="item$.selected" class="mr-1" />
                    <span style="margin-left: 5px;">
                        {{ getToolLabel(item.key) || item.label }}
                    </span>
                </ng-template>
            </ng-select>
            <div class="mt-2">
                <span *ngIf="selectedTools"><strong>Transfer Initiated:</strong> {{ transferDetails[selectedTools]?.label }}</span>
            </div>
            <div class="mt-3 custom-dropdown">         
                <ng-template #itemTemplate let-item="item" let-onCollapseExpand="onCollapseExpand" let-onCheckedChange="onCheckedChange">
                    <div class="cursor-pointer d-flex">
                        <span *ngIf="item.children" (click)="onCollapseExpand()" aria-hidden="true" [ngSwitch]="item.collapsed">
                            <span *ngSwitchCase="true" class="material-symbols-outlined bi bi-caret-right-fill">navigate_next</span>
                            <span *ngSwitchCase="false" class="material-symbols-outlined bi bi-caret-down-fill">keyboard_arrow_down</span>
                        </span>
                        <div [ngClass]="{'custom-control custom-checkbox': item.text !== 'All Workspace Documents', 'd-inline-block': true}">
                            <input type="checkbox" class="custom-control-input"
                                [checked]="selectedFolder && selectedFolder.value == item.value"
                                *ngIf="item.text !== 'All Workspace Documents'"
                                [id]="'tree_'+item.value"
                                (click)="select(item)">
                            <label (click)="onCollapseExpand()" [ngClass]="{'custom-control-label': item.text !== 'All Workspace Documents', 'mb-2 cursor-pointer': true}"  [for]="'tree_'+item.value">{{ item.text }}</label>
                        </div>
                    </div>
                </ng-template>
                <ng-template #headerTemplate let-config="config" let-item="item" let-onCollapseExpand="onCollapseExpand"
                    let-onCheckedChange="onCheckedChange" let-onFilterTextChange="onFilterTextChange">
                    <div *ngIf="config.hasFilter" class="row row-filter">
                        <div style="width: 96%; max-width:100%; margin-left: 10px;"  class="text-search horizontal-center px-2">
                            <span class="material-symbols-outlined font-small">search</span>
                            <input style="width: 100%; line-height: 24px" placeholder="Search" class="ngx-search " type="search" name = 'filterText'  [(ngModel)]="filterText" (ngModelChange)="filterText && onFilterTextChange($event)" />
                        </div>
                    </div>
                    <div *ngIf="config.hasAllCheckBox || config.hasCollapseExpand" class="row">
                        <div class="col-12">
                        <label *ngIf="config.hasAllCheckBox" (click)="select(item)">
                            <strong>{{ i18n.getAllCheckboxText() }}</strong>
                        </label>
                        <label *ngIf="config.hasCollapseExpand" class="float-right cursor-pointer" (click)="onCollapseExpand()">
                            <i [title]="i18n.getTooltipCollapseExpandText(item.collapsed)" aria-hidden="true" [ngSwitch]="item.collapsed">
                            <svg *ngSwitchCase="true" width="1em" height="1em" viewBox="0 0 16 16" class="bi bi-arrows-angle-expand"
                                fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd"
                                d="M1.5 10.036a.5.5 0 0 1 .5.5v3.5h3.5a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5v-4a.5.5 0 0 1 .5-.5z" />
                                <path fill-rule="evenodd"
                                d="M6.354 9.646a.5.5 0 0 1 0 .708l-4.5 4.5a.5.5 0 0 1-.708-.708l4.5-4.5a.5.5 0 0 1 .708 0zm8.5-8.5a.5.5 0 0 1 0 .708l-4.5 4.5a.5.5 0 0 1-.708-.708l4.5-4.5a.5.5 0 0 1 .708 0z" />
                                <path fill-rule="evenodd"
                                d="M10.036 1.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 .5.5v4a.5.5 0 1 1-1 0V2h-3.5a.5.5 0 0 1-.5-.5z" />
                            </svg>
                            <svg *ngSwitchCase="false" width="1em" height="1em" viewBox="0 0 16 16" class="bi bi-arrows-angle-contract"
                                fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd"
                                d="M9.5 2.036a.5.5 0 0 1 .5.5v3.5h3.5a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5v-4a.5.5 0 0 1 .5-.5z" />
                                <path fill-rule="evenodd"
                                d="M14.354 1.646a.5.5 0 0 1 0 .708l-4.5 4.5a.5.5 0 1 1-.708-.708l4.5-4.5a.5.5 0 0 1 .708 0zm-7.5 7.5a.5.5 0 0 1 0 .708l-4.5 4.5a.5.5 0 0 1-.708-.708l4.5-4.5a.5.5 0 0 1 .708 0z" />
                                <path fill-rule="evenodd"
                                d="M2.036 9.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-1 0V10h-3.5a.5.5 0 0 1-.5-.5z" />
                            </svg>
                            </i>
                        </label>
                        </div>
                    </div>
                    <div *ngIf="config.hasDivider" class="dropdown-divider"></div>
                </ng-template>
                <ngx-dropdown-treeview
                    [config]="{
                        hasAllCheckBox: false,
                        hasCollapseExpand: true,
                        hasFilter: true,
                        maxHeight: 500
                    }" 
                    [headerTemplate]="headerTemplate" 
                    [items]="itCategory"
                    [itemTemplate]="itemTemplate"
                    >
                    <!-- (selectedChange)="onSelectedChange($event)"
                    (filterChange)="onFilterChange($event)" -->
                </ngx-dropdown-treeview>
            </div>
        </div>
        <div [ngClass]="{'mt-4 form-group': true, 'text-muted': !transferDetails[selectedTools]?.has_company_filter }">
            <div class="d-flex justify-content-between">
                <span class="font-weight-bold">Filter records by company</span>
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="acceptance" name="acceptance" checked="true" [(ngModel)]="enableCompanyFilter" [disabled]="!transferDetails[selectedTools]?.has_company_filter">
                    <label class="custom-control-label" for="acceptance"></label>
                </div>
            </div>
            <div>
                <span>If selected, documents will be filtered by the company. Only records submitted by a user from the chosen company will be pushed into the specified Asite folder. If you need records to be separated by different companies within Asite, you will need to set up multiple links by setting up through ‘Link Tools’ multiple times.</span>
            </div>
        </div>
        <div class="mt-4 form-group" *ngIf="enableCompanyFilter">
            <label>Company</label>
            <company-selector-v2
                [required]="enableCompanyFilter"
                [disabled]="!transferDetails[selectedTools]?.has_company_filter"
                [country_code]="project.custom_field?.country_code"
                [name]="'selected_company'"
                [placeholder]="'Select Company'"
                [selectId]="selectedCompany"
                (selectionChanged)="selectedCompany = $event.selected"
                [projectId]="project.id"
            ></company-selector-v2>
        </div>
    </form>
</i-modal>

<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

<block-loader [alwaysInCenter]="true" [show]="blockLoader" [showBackdrop]="true"></block-loader>
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProjectDashboardComponent } from './project-dashboard.component';
import { Router, ActivatedRoute } from '@angular/router';
import { DOCUMENT } from '@angular/common';

describe('ProjectDashboardComponent', () => {
    let component: ProjectDashboardComponent;
    let fixture: ComponentFixture<ProjectDashboardComponent>;
    let mockRouter: any;
    let mockActivatedRoute: any;

    beforeEach(async () => {
        mockRouter = jasmine.createSpyObj('Router', ['navigate']);


        mockActivatedRoute = {
            snapshot: {
                data: {
                    is_project_portal: true,
                }
            },
            parent: {
                snapshot: {
                    data: {
                        projectResolverResponse: {
                            project: {
                                id: 123,
                                project_section_access: {
                                    dashboards: false
                                }
                            }
                        }
                    }
                }
            }
        };


        await TestBed.configureTestingModule({
            declarations: [ProjectDashboardComponent],
            providers: [
                { provide: Router, useValue: mockRouter },
                { provide: ActivatedRoute, useValue: mockActivatedRoute },
                { provide: DOCUMENT, useValue: document },
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ProjectDashboardComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });


    it('should initialize project info correctly when isProjectPortal is true', () => {
        expect(component.isProjectPortal).toBeTruthy();
        expect(component.projectId).toEqual(123);
        expect(component.projectInfo).toEqual(
            mockActivatedRoute.parent.snapshot.data.projectResolverResponse.project
        );
    });


    it('should navigate to induction if dashboard access is missing', () => {
        fixture = TestBed.createComponent(ProjectDashboardComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();

        expect(mockRouter.navigate).toHaveBeenCalledWith([
            '/project-portal/project/123/induction'
        ]);
    });


    it('should navigate to induction if dashboard access is missing', () => {
        mockActivatedRoute.parent.snapshot.data.projectResolverResponse.project.project_section_access.dashboards = false;
        component.ngOnInit();
        expect(mockRouter.navigate).toHaveBeenCalledWith(['/project-portal/project/123/induction']);
    });



    it('should not navigate if project_section_access is empty', () => {
        component.projectInfo.project_section_access = {};
        component.ngOnInit();
        expect(mockRouter.navigate).toHaveBeenCalledWith([
            '/project-portal/project/123/induction'
        ]);
    });

});

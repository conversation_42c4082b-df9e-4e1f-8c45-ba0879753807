<block-loader [show]="pageLoading" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
<div class="col-12 pt-2 outer-border-radius ngx-datatable-custom" *ngIf="!pageLoading">
    <div class="d-flex justify-content-between flex-wrap">
        <h5 class="float-md-left">Total Approved: <small>{{total_approved_count}}</small>
        </h5>
        <div class="col-md-9 d-flex flex-sm-row flex-column float-md-right inviteToInductionBtn justify-content-end p-0 mb-3 flex-wrap gap-8">
            <small *ngIf="fr_setting?.enabled" class="mr-2 fr-color fr-active py-1" title="Facial Recognition Activated">
                <span class="material-symbols-outlined mr-1 large-font">ar_on_you</span>
                <span class="d-none d-md-inline">Facial Recognition Activated</span>
            </small>
            <action-button
                [actionList]="actionButtonMetaData.actionList"
                (selectedActionEmmiter)="onActionSelection($event)"
                [hideNewFeatureBtn]="true">
            </action-button>
        </div>
    </div>
    <div class="clearfix"></div>
    <ng-container *ngIf="inductedEmployers.length != 0">
    <search-with-filters (searchEmitter)='searchFunction($event)' (filterEmitter)="onFilterSelection($event)" [filterData]='filterData' [loading]="pageLoading"></search-with-filters>
    </ng-container>
    <ngx-datatable
        #pInductionsTable
        [scrollbarV]="true"
        [virtualization]="false"
        [loadingIndicator]="loadingInlineInduction"
        [columnMode]="'force'"
        [footerHeight]="40"
        [headerHeight]="40"
        [rowHeight]="'auto'"
        [externalPaging]="true"
        [externalSorting]="true"
        [count]="inductionsPage.totalCount"
        [offset]="inductionsPage.pageNumber"
        [limit]="inductionsPage.pageSize"
        (page)="pageCallback($event, true)"
        [sortType]="'single'"
        (sort)="sortCallback($event, true)"
        [rows]="inductionsPage.records"
        class="bootstrap table table-v4 ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h">
        <!-- <ng-container *ngIf="detailsEnabled">
            <ngx-datatable-column headerClass="font-weight-bold" minWidth="40" [maxWidth]="40" [sortable]="false" [resizeable]="false">
                <ng-template let-column="column" ngx-datatable-header-template>
                </ng-template>
                <ng-template let-row="row" let-value="value" let-expanded="expanded" ngx-datatable-cell-template>
                    <ng-container *ngIf="expanded; else down_arrow">
                        <span (click)="toggleExpandRow(row, expanded)" class="horizontal-center material-symbols-outlined cursor-pointer">
                            expand_less
                            </span>
                    </ng-container>
                    <ng-template #down_arrow>
                        <span (click)="toggleExpandRow(row, expanded)" class="horizontal-center cursor-pointer material-symbols-outlined">
                            expand_more
                            </span>
                    </ng-template>
                </ng-template>
            </ngx-datatable-column>
        </ng-container> -->

        <ngx-datatable-column name="Record #" headerClass="py-2 font-weight-bold" minWidth="75" [maxWidth]="75" prop="id" [resizeable]="false">
            <ng-template let-row="row" let-value="value" ngx-datatable-cell-template>
                {{row.record_id}}
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column name="User" headerClass="py-2 font-weight-bold" minWidth="100" prop="name">
            <ng-template let-column="column" ngx-datatable-header-template>
                <div class="d-inline" style="padding-left: 36px;">User</div>
            </ng-template>
            <ng-template let-row="row" let-value="value" ngx-datatable-cell-template>
                <div class="d-flex p-0 m-0">
                    <div *ngIf="rtw_status?.enabled" class="width-18">
                        <span
                                class="material-symbols-outlined large-font my-1 mr-1 cursor-default"
                                [ngClass]="row._ppac.style.textClass"
                                [title]="'PPAC status: '+ row._ppac?.status"
                                *ngIf="row._ppac">
                                person_book
                        </span>
                    </div>
                    <div *ngIf="fr_setting?.enabled" [class.width-18]="(row.fr_face_id || row.status_code !== STATUS_CODES.APPROVED)">
                        <span
                            class="material-symbols-outlined large-font my-1 mr-1 fr-color"
                            role="button"
                            title="Activate for facial recognition"
                            *ngIf="!row.fr_face_id && row.status_code === STATUS_CODES.APPROVED"
                            (click)="enrolInductionIntoFR($event, row.id, row.name)">
                                add_reaction
                        </span>
                    </div>
                    <div *ngIf="!projectInfo?.custom_field?.disable?.view_medication_modal" [class.width-18]="!row._has_medical_condition">
                        <!--<span *ngIf="row._has_medical_condition" (click)="openMedicalConditionsModal($event, row)" role="button" >
                            <img class="mr-1 mb-1" [src]="AssetsUrlSiteAdmin.fileIconRed">
                        </span>-->
                        <span *ngIf="row._has_medical_condition"
                              (click)="openMedicalConditionsModal($event, row)"
                              role="button"
                              title="View medication info"
                              class="material-symbols-outlined large-font my-1 mr-1 text-danger">
                                prescriptions
                        </span>
                    </div>
                    <div>
                        <span appTooltip> {{ value }} </span>
                        <small *ngIf="row.user_ref">
                            (ID: {{ row.user_ref }})
                        </small>
                    </div>
                </div>
            </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column name="Company" [sortable]="true" headerClass="py-2 font-weight-bold" minWidth="100" prop="employer">
            <ng-template let-row="row" let-value="value" ngx-datatable-cell-template>
                <span appTooltip>{{value}}</span>
                <small *ngIf="row?.additional_data?.employment_detail?.employment_company"> <span appTooltip>({{ row.additional_data.employment_detail.employment_company }})</span></small>
            </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column name="Submitted" headerClass="py-2 font-weight-bold" minWidth="100" prop="createdAt">
            <ng-template let-row="row" let-value="value" ngx-datatable-cell-template>
                {{epochToDayJs(+value).format(dateDisplayFormat)}}
                <ng-container *ngIf="isShadowUser(row?.additional_data?.user_info)">
                    <i container="body" [ngbTooltip]="'This induction was added manually by a project admin. The user hasn\'t been fully inducted through innDex'" class="cursor-pointer fas fa-info-circle text-danger"> </i>
                </ng-container>
                <br/>
                <small> ({{ epochToDayJs(+value).format(timeDisplayFormat)}}) </small>
            </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column [sortable]="false" headerClass="py-2 font-weight-bold" minWidth="120" prop="induction_slot" *ngIf="projectInfo?.custom_field && projectInfo?.custom_field.induction_slot_booking">
            <ng-template let-column="column" ngx-datatable-header-template>
                On-site {{projectInfo?.custom_field.induction_phrase_singlr}}
            </ng-template>
            <ng-template let-row="row" let-value="value" ngx-datatable-cell-template>
                <ng-container *ngIf="row?.induction_slot && row?.induction_slot?.seconds; else showDash">
                    {{ unixEpochToDayJs(+row.induction_slot.seconds).format(dateDisplayFormat) }} <br/>
                    <small> ({{ unixEpochToDayJs(+row.induction_slot.seconds).format(timeDisplayFormat) }}) </small>
                </ng-container>
                <ng-template #showDash> - </ng-template>
            </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column [sortable]="false" name="Status" [width]="250" minWidth="200"
                              [maxWidth]="300" headerClass="py-2 font-weight-bold action-column"
                              cellClass="action-column horizontal-center no-ellipsis" prop="status_code">
            <ng-template let-row="row" let-value="value" ngx-datatable-cell-template>
                <button class="btn btn-sm align-middle"
                        container="body" [ngbTooltip]="row._block_tooltip"
                        (click)="openReviewStep(row)"
                        [disabled]="row._is_clickable && (!inductionPermission && !isProjectAdminV1)"
                        [class.cursor-default]="!row._is_clickable"
                        [class]="row._status_code_meta.badge_class + ' ' + row._status_code_meta.text_color">
                    {{ row.status_message }}
                    <span *ngIf="row._is_movable" class="material-symbols-outlined x-large-font mr-n1">chevron_right</span>
                </button>
            </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column name="Action"
                              headerClass="py-2 font-weight-bold action-column"
                              cellClass="action-column no-ellipsis"
                              [sortable]="false"
                               [width]="200" minWidth="200">
            <ng-template let-row="row" ngx-datatable-cell-template>
                <button-group
                    [buttons]="row._buttonGroup"
                    (onActionClick)="rowBtnClicked($event, row)"
                ></button-group>
                <!-- <div style="line-height: 41px;">
                    <button
                        *ngIf="!projectInfo?.custom_field?.disable?.view_induction"
                        title="Induction Preview" type="button" class="btn btn-sm align-middle btn-action mr-1 mb-1 p-1"
                        (click)="viewInductionModal(row)">
                        <img class="img-icon px-1" [src]="AssetsUrlSiteAdmin.search_svg" alt="">
                    </button>
                    <div class="d-inline-block" title="Edit Record" ngbDropdown container="body" placement="bottom-right">
                        <button
                            title="Edit Record" type="button" class="btn btn-sm align-middle btn-action mr-1 mb-1 p-1"
                            ngbDropdownToggle>
                            <img class="img-icon px-1" [src]="AssetsUrlSiteAdmin.pencilSquare" alt="">
                        </button>
                        <div ngbDropdownMenu>
                            <button *ngIf="!medication_block_disabled" ngbDropdownItem class="dropdown-item cursor-pointer" (click)="addMedicationPopup(row)">
                                Add medication
                            </button>
                            <button ngbDropdownItem class="dropdown-item cursor-pointer" (click)="editUserCompanyPopup(row)">
                                Employment
                            </button>
                        </div>
                    </div>

                    <div class="d-inline-block" container="body" title="Show 'COMPETENCY' Expiry" ngbDropdown placement="bottom-right">
                        <button title="View Competencies & Certifications"
                                type="button" class="btn btn-sm align-middle mr-1 mb-1 p-1"
                                ngbDropdownToggle
                                [ngClass]="{
                                'btn-danger': (row.additional_data?.user_docs?.length && (row.additional_data.user_docs[0]?._is_expiring_or_expired === 0)),
                                'btn-warning': (row.additional_data?.user_docs?.length && (row.additional_data.user_docs[0]?._is_expiring_or_expired === 1)),
                                'btn-action': ((row.additional_data?.user_docs?.length === 0) || (row.additional_data?.user_docs?.length && (row.additional_data.user_docs[0]?._is_expiring_or_expired === 2)))
                                }">
                            <img class="img-icon px-1" [src]="AssetsUrlSiteAdmin.addressCard" alt="">
                        </button>
                        <div class="p-1 pb-2 bg-light-grey rounded" ngbDropdownMenu>
                            <ng-container *ngIf="row?.additional_data?.user_docs?.length">
                                <div *ngFor="let user_doc of row?.additional_data?.user_docs; trackBy : trackByRowIndex;"
                                     class="text-center text-white rounded w-100 px-2 py-1 mb-1 font-inherit"
                                     [ngClass]="((user_doc?._is_expiring_or_expired) === 0) ? 'bg-danger' :
                                                (((user_doc?._is_expiring_or_expired) === 1) ? 'background-orange' :
                                                            'bg-success')">
                                    {{ user_doc.name}} Expiry: {{ epochToDayJs(+user_doc?.expiry_date).format(dateFormat) }}
                                </div>
                            </ng-container>
                            <button type="button"
                                    class="btn bg-white btn-active badge-pill shadow-sm dropdown-item w-100 text-center mt-2"
                                    (click)="openMoreUploadPopup(row)" ngbDropdownItem>
                                + Upload Documents
                            </button>
                        </div>
                    </div>
                    <button title="Assign an Optima Badge" class="btn btn-sm align-middle btn-action mr-1 mb-1 p-1"
                            *ngIf="(row.status_code === STATUS_CODES.APPROVED && !row.optima_badge_number && optima_meta && optima_meta.has_optima_source)"
                            (click)="assignBadgeNumberModel(row)">
                        <i class="fa fa-id-badge" aria-hidden="true"></i>
                    </button>
                    <ng-container *ngIf="optima_meta && optima_meta.has_optima_source && !optima_meta.has_facial_enrolment_type && row.optima_badge_number">
                        <button title="Fingerprint Enrolment" class="btn btn-sm align-middle btn-action mr-1 mb-1 p-1"
                                (click)="initFingerprintEnrolmentModal(row)">
                            <svg width="16px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Capa_1" x="0px" y="0px" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512;" xml:space="preserve">
                                <g>
                                    <path d="M190.911,291.115c4.29-7.084,2.029-16.307-5.05-20.6c-7.078-4.292-16.295-2.031-20.585,5.053   c-15.661,25.861-40.584,52.296-68.001,62.496c-7.758,2.886-11.71,11.52-8.826,19.283c2.885,7.766,11.513,11.718,19.271,8.832   C141.879,353.473,171.683,322.865,190.911,291.115z"/>
                                    <path d="M315.808,208.598c-36.199-47.301-112.652-35.478-130.99,23.449c-2.461,7.909,1.951,16.316,9.853,18.779   c7.903,2.463,16.305-1.952,18.766-9.86c5.884-18.91,23.123-31.615,42.895-31.615c25.807,0,52.52,22.708,42.146,61.166   c-12.065,44.731-49.953,103.608-96.522,149.995c-5.867,5.843-5.889,15.34-0.049,21.21c5.839,5.87,15.327,5.894,21.195,0.049   c50.03-49.834,90.977-113.987,104.316-163.436C336.413,244.988,326.069,222.005,315.808,208.598z"/>
                                    <path d="M227.423,153.417c14.148-4.056,29.024-5.086,43.614-3.026c8.189,1.153,15.778-4.551,16.935-12.753   s-4.548-15.79-12.745-16.948c-18.059-2.553-37.215-1.511-56.059,3.891c-7.957,2.281-12.56,10.585-10.28,18.548   C211.169,151.096,219.47,155.698,227.423,153.417z"/>
                                    <path d="M156.205,223.111c5.854-18.896,16.858-35.662,31.822-48.484c6.288-5.387,7.02-14.855,1.636-21.146   c-5.383-6.291-14.846-7.024-21.132-1.637c-19.264,16.507-33.427,38.08-40.957,62.387c-11.565,37.332-28.804,59.516-52.701,67.82   c-7.819,2.716-11.957,11.263-9.242,19.087c2.717,7.831,11.259,11.964,19.074,9.249   C117.768,298.896,141.824,269.533,156.205,223.111z"/>
                                    <path d="M270.664,258.827c2.463-7.908-1.948-16.317-9.85-18.781c-7.902-2.468-16.305,1.949-18.767,9.856   C215.099,336.443,154.983,375.65,137.2,385.605c-7.224,4.044-9.804,13.183-5.763,20.411c4.037,7.222,13.164,9.814,20.397,5.767   C201.956,383.725,248.583,329.739,270.664,258.827z"/>
                                    <path d="M255.784,0C114.412,0,0,114.481,0,255.965c0,161.692,147.243,281.873,304.843,251.25c2.733,2.897,6.595,4.714,10.89,4.714   h149.873c8.277,0,14.987-6.715,14.987-14.998V378.134C573.665,206.846,448.39,0,255.784,0z M29.975,255.965   c0-124.6,101.298-225.969,225.809-225.969c170.065,0,279.105,182.049,199.482,331.954h-94.524v-59.039   c0-16.514,13.425-29.949,29.927-29.949c16.502,0,29.927,13.435,29.927,29.949v14.545c0,8.283,6.71,14.998,14.987,14.998   c8.277,0,14.987-6.715,14.987-14.998c0-15.123,0.581-21.917-2.751-32.508c1.541-5.354,15.28-81.224-36.823-149.303   C332.696,33.332,176.152,33.667,99.042,139.192c-17.942,24.555-26.74,54.402-33.95,69.361c-3.596,7.461-0.467,16.426,6.988,20.025   c7.457,3.597,16.414,0.467,20.01-6.993c13.21-27.409,19.607-65.785,66.921-100.402c28.446-20.813,62.099-31.814,97.321-31.814   c88.41,0,163.552,70.733,164.11,161.561c-8.902-5.126-19.239-8.042-30.133-7.953c-3.296-41.747-25.325-79.177-60.697-102.007   c-6.957-4.491-16.232-2.487-20.718,4.475c-4.486,6.961-2.484,16.243,4.471,20.732c28.997,18.715,46.427,50.146,47.358,84.851   c-17.89,10.38-29.957,29.742-29.957,51.884v59.039h-15.035c-8.277,0-14.987,6.715-14.987,14.998v100.481   C159.725,505.905,29.975,397.467,29.975,255.965z M450.619,481.933H330.72v-89.988h119.899V481.933z"/>
                                    <path d="M375.682,450.938h29.975c8.277,0,14.987-6.715,14.987-14.998c0-8.283-6.71-14.998-14.987-14.998h-29.975   c-8.277,0-14.987,6.715-14.987,14.998C360.695,444.223,367.405,450.938,375.682,450.938z"/>
                                </g>
                            </svg>
                        </button>
                    </ng-container>
                    <ng-container *ngIf="optima_meta && optima_meta.has_optima_source && optima_meta.has_facial_enrolment_type && row.optima_badge_number">
                        <button title="Facial Enrolment" type="button" class="btn btn-sm align-middle btn-action mr-1 mb-1 p-1" href="javascript:void(0)"
                                (click)="initFingerprintEnrolmentModal(row)">
                            <img class="img-icon px-1" [src]="AssetsUrlSiteAdmin.facialRecognition" alt="">
                        </button>
                    </ng-container>
                    <ng-container *ngIf="optima_meta && optima_meta.has_geo_fence_source && projectInfo?.custom_field?.clock_in_declarations && projectInfo?.custom_field?.clock_in_declarations.length">
                        <button title="View Declaration Answers" type="button" class="btn btn-sm align-middle btn-action mr-1 mb-1 p-1"
                                (click)="viewDeclarationAnswers(row)">
                            <img class="img-icon px-1" [src]="AssetsUrlSiteAdmin.declarations" alt="">
                        </button>
                    </ng-container>
                    <ng-container *ngIf="!projectInfo?.custom_field?.disable?.block_button">
                        <button class="btn btn-sm align-middle block-user-access-btn mr-1 mb-1 p-1" title="Block User Access"
                                *ngIf="[STATUS_CODES.APPROVED, STATUS_CODES.BLOCKED, STATUS_CODES.BLACKLISTED].includes(row.status_code)"
                                [ngClass]="{
                                                    'btn-action': (row.status_code === STATUS_CODES.APPROVED),
                                                    'btn-danger': (row.status_code === STATUS_CODES.BLOCKED),
                                                    'btn-dark': (row.status_code === STATUS_CODES.BLACKLISTED)
                                                    }" (click)="blackListUserInduction(row)">
                            <ng-container *ngIf="(row.status_code === STATUS_CODES.APPROVED); else block_white">
                                <img class="img-icon px-1" [src]="AssetsUrlSiteAdmin.userBlock" alt="">
                            </ng-container>
                            <ng-template #block_white>
                                <img class="img-icon px-1" [src]="AssetsUrlSiteAdmin.userBlockIconWhite" alt="">
                            </ng-template>
                        </button>
                    </ng-container>
                    <div class="d-inline-block position-relative"
                         *ngIf="conductCardsList.length && (row.conduct_cards?.length || row.status_code === STATUS_CODES.APPROVED)"
                         [ngbTooltip]="row.conduct_cards?.length ? htmlContent: false"
                         placement="bottom"
                         style="line-height: 31px;">
                        <div class="colors-list pb-1">
                            <ng-container *ngFor="let item of row.conduct_cards; let i=index">
                                <div [ngClass]="{'rounded-top': i === 0, 'rounded-bottom': i === row.conduct_cards.length - 1}"
                                     [style.height.%]="100 / row.conduct_cards.length"
                                     [style.background-color]="item.card_detail.card_color"></div>
                            </ng-container>
                        </div>
                        <button (click)="openConductCardModal(row)"
                                class="btn btn-sm align-middle btn-action caution-card p-1 r-1 mb-1">
                            <img class="img-icon px-1" [src]="AssetsUrlSiteAdmin.conduct_cards" alt="">
                        </button>
                    </div>
                    <ng-template #htmlContent>
                        <ul class="mb-0 pl-3">
                            <li *ngFor="let card of row.conduct_cards">
                                {{ card.card_detail.card_name }}
                            </li>
                        </ul>
                    </ng-template>
                </div> -->
            </ng-template>
        </ngx-datatable-column>

        <!-- <ngx-datatable-row-detail
            #rowDetail
            [rowHeight]="'auto'"
            (toggle)="onDetailToggle($event.value)">
            <ng-template let-row="row" let-expanded="expanded" ngx-datatable-row-detail-template>
                <div class="bg-white h-100 py-3 px-2 row-details-border border-top-0">
                    <ng-container *ngFor="let briefing of row.receivedBriefings; let i=index">
                        <ng-container *ngIf="briefing?.project_section_access">
                            <div role="button" class="my-2 d-flex justify-content-between align-items-center table-sub-heading-bg-grey" (click)="toggle(row, i)">
                                <div class="d-flex">
                                    <div *ngIf="!briefing.toggleTable">
                                        <span  class="material-symbols-outlined cursor-pointer">
                                            expand_more
                                            </span>

                                    </div>
                                    <div *ngIf="briefing.toggleTable">
                                        <span  class="material-symbols-outlined cursor-pointer">
                                            expand_less
                                            </span>
                                    </div>
                                    <span class="contant-font-size font-weight-bold ml-3">
                                        {{ briefing.heading }} ({{ briefing?.records?.length ? briefing?.records?.length : '0' }})
                                    </span>
                                </div>
                                <div class="form-outline d-flex align-items-center mr-3">
                                    <input type="search"
                                           [title]="'Search '+briefing.heading"
                                           class=" text-truncate form-control search-input w-auto my-2"
                                           [placeholder]="'Search '+briefing.heading"
                                           aria-label="Search"
                                           (click)="$event.stopPropagation()"
                                           (input)="search($event, row, i)"
                                    />
                                </div>
                            </div>
                            <div class="text-center my-2" *ngIf="briefing?.filteredRecords?.length <= 0 && briefing.toggleTable"> No data to display</div>
                            <div class="detail-fixed-height mx-5" *ngIf="briefing?.filteredRecords?.length && briefing.toggleTable">
                                <ngx-datatable
                                    class="bootstrap table table-hover table-sm memberTimeDetail"
                                    [rows]="briefing.filteredRecords"
                                    [limit]="10"
                                    [columnMode]="'force'"
                                    [rowHeight]="'auto'"
                                    [footerHeight]="38"
                                >
                                    <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false">
                                        <ng-template let-column="column" ngx-datatable-header-template>
                                            Title
                                        </ng-template>
                                        <ng-template let-row="row" let-value="value" let-expanded="expanded" ngx-datatable-cell-template>
                                            {{ row.briefing_title }}
                                        </ng-template>
                                    </ngx-datatable-column>
                                    <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false">
                                        <ng-template let-column="column" ngx-datatable-header-template>
                                            Briefed By
                                        </ng-template>
                                        <ng-template let-row="row" let-value="value" let-expanded="expanded" ngx-datatable-cell-template>
                                            {{ row.briefed_by_name || row.briefed_by }}
                                        </ng-template>
                                    </ngx-datatable-column>
                                    <ngx-datatable-column headerClass="font-weight-bold" [width]="100" [sortable]="false">
                                        <ng-template let-column="column" ngx-datatable-header-template>
                                            Date & Time
                                        </ng-template>
                                        <ng-template let-row="row" let-value="value" let-expanded="expanded" ngx-datatable-cell-template>
                                            {{ row.briefed_at_formatted }}
                                        </ng-template>
                                    </ngx-datatable-column>
                                </ngx-datatable>
                            </div>
                        </ng-container>
                    </ng-container>
                </div>
            </ng-template>
        </ngx-datatable-row-detail> -->
    </ngx-datatable>

    <block-loader [show]="(processingLoader)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>


    <ng-template #infoModalRef let-c="close" let-d="dismiss">
        <div class="modal-body">
            <div class="text-center">
                <p class=""
                   [class.text-uppercase]="true"
                   [class.text-success]="infoModal.type === MODAL_TYPE.SUCCESS"
                   [class.text-danger]="infoModal.type === MODAL_TYPE.ERROR"
                   [ngClass]="infoModal.styling.titleClass"
                   *ngIf="infoModal.content.title"
                >{{infoModal.content.title}}</p>

                <p [class.small]="true"
                   [class.mb-0]="true"
                   [ngClass]="infoModal.styling.msgClass"
                   *ngIf="infoModal.content.message"
                >{{ infoModal.content.message}}</p>

                <p [class.small]="true"
                   [class.mb-0]="true"
                   [class.text-muted]="true"
                   *ngIf="infoModal.content.extra"
                >{{ infoModal.content.extra}}</p>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-sm btn-primary m-auto" (click)="d('close')">{{infoModal.actionBtnLabel || 'Close' }}</button>
        </div>
    </ng-template>
</div>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
<induction-report-downloader-v2 #downloadReportActionCompRef
    [projectPortal]="true"
    [disableInduction]="projectInfo?.custom_field?.disable?.view_induction"
    [authUser]="authUser$"
    [inductedEmployers]="inductedEmployers"
    [projectInfo]="projectInfo"
    [isTraining]="actionButtonMetaData.isTraining"
></induction-report-downloader-v2>

import {
    Component,
    EventEmitter,
    Input,
    OnChanges,
    OnInit,
    Output,
    TemplateRef,
    ViewChild,
} from "@angular/core";
import {
    AuthService, META_STATUS_CODES, META_STATUS_CODES_LIST,
    PPAC_ID_STATUS_CODES,
    ProjectService, User, UserService,
    ProjectInductionsPage,
    InductionActionButtons,
    DoNotAskAgainService,
    ProjectInductionsListRow,
    STATUS_CAN_TRANSIT,
    InductionRequest,
    ToastService,
    ConductCardsService,
} from "@app/core";
import {Router} from "@angular/router";
import {DatatableComponent} from "@swimlane/ngx-datatable";
import {Subject} from 'rxjs';
import * as dayjs from 'dayjs';
import {Dayjs} from 'dayjs';
import {ActionBtnEntry, AssetsUrl, GenericConfirmationModalComponent, IModalComponent} from "@app/shared";
import {AppConstant} from "@env/environment";
import {take} from "rxjs/operators";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import { filterData } from "@app/core";
import { InductionReportDownloaderV2Component } from "@app/modules/common";

@Component({
    selector: 'project-inductions',
    templateUrl: 'inductions.component.html',
    styleUrls: ['./inductions.component.scss'],
})
export class InductionsComponent implements OnInit, OnChanges {
    AssetsUrlSiteAdmin = AssetsUrl.siteAdmin;
    dateFormat: string = AppConstant.defaultDateFormat;
    dateDisplayFormat: string = AppConstant.displayDateFormat;
    timeDisplayFormat: string = 'HH:mm:ss';
    STATUS_CODES: any = META_STATUS_CODES;
    STATUS_CAN_TRANSIT = STATUS_CAN_TRANSIT;
    STATUS_CODES_LIST: Array<any> = META_STATUS_CODES_LIST;
    STATUS_COLORS: any = {0: "danger", 2: "success", 4: "danger", 5: "dark"};
    // todo : to be moved to a separate component when adding this to other features
    filterBy: Array<{
        title: string;
        state: boolean;
    }> = [
        {title:"filter by status", state:false},
        {title:"filter by company", state:false}
    ];


    @Input()
    projectId: number;

    @Input()
    optima_meta: {
        enrolment_type: string;
        has_geo_fence_source: boolean;
        has_optima_source: boolean;
        has_fr?: boolean;
        has_facial_enrolment_type?: boolean;
    };
    @Input()
    rtw_status: { enabled?: boolean; } = {};
    @Input()
    fr_setting: { enabled?: boolean; } = {};

    @Input()
    showQRDownloadButton: boolean = false;
    @Input()
    detailsEnabled: boolean = false;
    @Input()
    inductionPermission: boolean = false;
    @Input()
    medication_block_disabled: boolean = false;
    @Input()
    conductCardsList: Array<any> = [];
    @Input()
    projectInfo: {
        parent_company?: number;
        _my_designations?: Array<string>;
        project_section_access?: any;
        custom_field?: {
            induction_phrase?: string;
            induction_phrase_singlr?: string;
            induction_slot_booking?: boolean;
            clock_in_declarations?: Array<any>;
            timezone?: string;
            rams_phrase?: string;
            tb_phrase?: string;
            wpp_phrase?: string;
            disable?: {
                view_medication_modal?: boolean;
                view_induction?: boolean;
                block_button?: boolean;
            }
        };
    };

    searchInputChanged: Subject<string> = new Subject<string>();
    filter: {
        searchText?: string;
        employer?: string[];
        statusCodes?: number[];
    } = {
        statusCodes: [],
        employer:[]
    };

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    MODAL_TYPE = {
        ERROR: 1,
        SUCCESS: 2,
    };
    infoModal: {
        type: number;
        content: {
            title?: string;
            message?: string;
            extra?: string;
        };
        actionBtnLabel?: string;
        styling: {
            titleClass?: string;
            msgClass?: string;
        },

    } = {
        type: 0,
        content: {},
        styling: {},
        actionBtnLabel: 'Close'
    };
    processingLoader: boolean = false;
    authUser$: User;
    inductedEmployers: Array<any> = [];
    pageLoading: boolean = false;
    isInitInduction = false;
    loadingInlineInduction: boolean = false;
    inductionsPage: ProjectInductionsPage = new ProjectInductionsPage;
    total_approved_count: number;
    filterData: filterData[] = this.renderFilterData();
    actionButtonMetaData = {
        isTraining: false,
        actionList: [],
        class: 'material-symbols-outlined',
    };
    isProjectAdminV1: boolean = false;
    constructor(
        private authService: AuthService,
        private modalService: NgbModal,
        private userService: UserService,
        private projectService: ProjectService,
        private router: Router,
        private doNotAskAgainService: DoNotAskAgainService,
        private toastService: ToastService,
        private conductCardsService: ConductCardsService,
    ) {}

    ngOnInit() {
        this.inductionsPage.sortDir = 'desc';
        this.actionButtonMetaData.actionList = [
            {
                code: InductionActionButtons.ADD_USER,
                name: `Add User`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'person_add',
                enabled: this.projectInfo?.project_section_access?.add_shadow_user,
            },
            {
                code: InductionActionButtons.INVITE_TO_INDUCTION,
                name: `Invite to ${this.projectInfo?.custom_field.induction_phrase_singlr}`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'send',
                enabled: true,
            },
            {
                code: InductionActionButtons.INVITES_SENT,
                name: `Download Invites Sent`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: true,
            },
            {
                code: InductionActionButtons.DOWNLOAD_INDUCTION_RECORDS,
                name: `Download ${this.projectInfo?.custom_field.induction_phrase_singlr} Records`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: !this.projectInfo?.custom_field?.disable?.view_induction,
            },
            {
                code: InductionActionButtons.DOWNLOAD_TRAINING_RECORDS,
                name: `Download Training Records`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: true,
            },
            {
                code: InductionActionButtons.DOWNLOAD_CONDUCT_CARDS_REPORT,
                name: `Download Conduct Cards Report`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: true,
            },
            {
                code: InductionActionButtons.DOWNLOAD_INDUCTION_POSTER,
                name: `Download ${this.projectInfo?.custom_field.induction_phrase_singlr} Poster`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: this.showQRDownloadButton,
            },
        ];
        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
            }
        });
        this.isProjectAdminV1 = this.authService.isProjectAdminV1(this.authUser$, this.projectInfo);
        this.loadInductionsPage(0, {sortKey: 'id', sortDir: 'desc'}, false);
        this.getEmployersList();
    }

    ngOnChanges() {
        this.actionButtonMetaData.actionList = this.actionButtonMetaData.actionList.map(item => {
            if (item.code === InductionActionButtons.DOWNLOAD_INDUCTION_POSTER) {
              return { ...item, enabled: this.showQRDownloadButton };
            }
            return item;
          });
        if(this.optima_meta){
            this.optima_meta.has_facial_enrolment_type = (this.optima_meta.enrolment_type === 'facial');
        }
        this.processRows(this.inductionsPage.records);
    }


    ngAfterViewInit() {

    }

    initializeTable(isPageChange?: boolean) {
        this.loadInductionsPage(0, {sortKey: 'id', sortDir: 'desc'}, isPageChange);
    }

    private getEmployersList() {
        this.projectService.getSAInductionEmployers(this.projectId).subscribe((data: any) => {
            if (data && data.inducted_employers) {
                this.inductedEmployers = (data.inducted_employers || []).map(row => row.name).filter(name => name);
                this.filterData = this.renderFilterData();
            } else {
                const message = data.message || 'Failed to fetch employers list.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    @ViewChild('pInductionsTable') inductionsTable: DatatableComponent;

    loadInductionsPage(pageNumber: number = 0, sortInfo: any = {}, isPageChange: boolean = false) {
        let q = this.filter.searchText ? encodeURIComponent(this.filter.searchText) : '';
        let employer = this.filter.employer ?  encodeURIComponent(this.filter.employer.join(",")): '';
        let statusCodes = (this.filter.statusCodes !== null ? this.filter.statusCodes.join(',') : '');
        let approvedCount = (pageNumber || q) ? 'false' : 'true';
        if (!isPageChange) {
            this.pageLoading = true;
        } else {
            this.loadingInlineInduction = true;
        }
        this.projectService.getSAProjectInductions(this.projectId, {
            extra: true,
            approvedCount,
            ...sortInfo,
            pageNumber,
            pageSize: this.inductionsPage.pageSize,
            q,
            employer,
            statusCodes,
        }).subscribe((data: ProjectInductionsPage) => {
            if (data.success) {
                data.records = this.processRows(data.records);
                this.inductionsPage = data;
                this.pageLoading = false;
                this.loadingInlineInduction = false;
                if (data.sortKey) {
                    setTimeout(() => {
                        if(this.inductionsTable){
                            this.inductionsTable.sorts = [{prop: data.sortKey, dir: data.sortDir}];
                        }
                    }, 0);
                }
                if (data.total_approved_count !== undefined) {
                    // as key won't be there when:
                    // moving away from page 0 or searching anything.
                    this.total_approved_count = data.total_approved_count || 0;
                }
            } else {
                const message = data.message || 'Failed to fetch inductions.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        }).add(() => {
            this.pageLoading = false;
        });
    }

    public processRows(rows) {
        // Define the base template for the button group as a key-based object
        const baseButtonConfig: Record<string, ActionBtnEntry> = {
            view: {
                key: 'view',
                label: '',
                title: 'View',
                mat_icon: 'search',
                hide: this.projectInfo?.custom_field?.disable?.view_induction,
            },
            edit_record: {
                key: 'edit_record',
                label: '',
                title: 'Edit Record',
                mat_icon: 'edit_note',
                children: [
                    {
                        key: 'add_medication',
                        label: 'Add medication',
                        btn_classes: 'dropdown-item',
                        disabled: this.medication_block_disabled,
                    },
                    {
                        key: 'employment',
                        label: 'Employment',
                        btn_classes: 'dropdown-item',
                    }
                ]
            },
            view_competencies: {
                key: 'view_competencies',
                label: '',
                title: 'View Competencies & Certifications',
                mat_icon: 'id_card',
                children: [
                    {
                        key: 'upload_documents',
                        label: '+ New Competency/Certification',
                        btn_classes: 'dropdown-item',
                    }
                ]
            },
            assign_badge: {
                key: 'assign_badge',
                label: '',
                title: 'Assign an Optima Badge',
                mat_icon: 'badge',
            },
            fingerprint_enrolment: {
                key: 'fingerprint_enrolment',
                label: '',
                title: 'Fingerprint Enrolment',
                mat_icon: 'fingerprint',
            },
            facial_enrolment: {
                key: 'facial_enrolment',
                label: '',
                title: 'Facial Enrolment',
                mat_icon: 'ar_on_you',
            },
            view_declaration_answers: {
                key: 'view_declaration_answers',
                label: '',
                title: 'View Declaration Answers',
                mat_icon: 'checklist',
            },
            block_user_access: {
                key: 'block_user_access',
                label: '',
                title: 'Block User Access',
                mat_icon: 'person_off',
            },
            conduct_card: {
                key: 'conduct_card',
                label: '',
                title: 'Conduct Card',
                mat_icon: 'content_copy',
            }
        };

        return rows.map(ir => {
            if ([this.STATUS_CODES.BLACKLISTED, this.STATUS_CODES.BLOCKED].indexOf(ir.status_code) !== -1) {
                ir._block_tooltip = this.getBlockedReason(ir);
            }
            ir._status_code_meta = META_STATUS_CODES_LIST.find(m => m.code === ir.status_code);
            ir._is_movable = [META_STATUS_CODES.PENDING, META_STATUS_CODES.IN_REVIEW, META_STATUS_CODES.CHANGE_REQUESTED].includes(ir.status_code);
            ir._is_clickable = [META_STATUS_CODES.PENDING, META_STATUS_CODES.IN_REVIEW, META_STATUS_CODES.CHANGE_REQUESTED, META_STATUS_CODES.BLACKLISTED, META_STATUS_CODES.BLOCKED].includes(ir.status_code);
            ir = this.setCustomField(ir);
            ir = this.healthAssessment(ir);
            ir = this.sortCompetency(ir);
            ir._has_medical_condition = this.hasMedicalCondition(ir);
            if(this.rtw_status?.enabled){
                ir._ppac = this.getPpacCodeRowState(ir);
            }

            // Initialize button group for this record
            const buttonConfig: { [key: string]: ActionBtnEntry } = JSON.parse(JSON.stringify(baseButtonConfig));
            
            // Set button visibility and properties based on conditions
            const isApprovedWithoutBadge = ir.status_code === this.STATUS_CODES.APPROVED && !ir.optima_badge_number;
            const hasOptimaSource = this.optima_meta?.has_optima_source;
            const hasFacialEnrolment = this.optima_meta?.has_facial_enrolment_type;
            const hasBadgeNumber = ir.optima_badge_number;
            const hasGeoFenceSource = this.optima_meta?.has_geo_fence_source;
            const hasClockInDeclarations = this.projectInfo?.custom_field?.clock_in_declarations?.length > 0;
            const isBlockableStatus = [this.STATUS_CODES.APPROVED, this.STATUS_CODES.BLOCKED, this.STATUS_CODES.BLACKLISTED].includes(ir.status_code);
            const isBlockButtonEnabled = !this.projectInfo?.custom_field?.disable?.block_button;

            buttonConfig.assign_badge.hide = !(isApprovedWithoutBadge && hasOptimaSource);
            buttonConfig.fingerprint_enrolment.hide = !(hasOptimaSource && !hasFacialEnrolment && hasBadgeNumber);
            buttonConfig.facial_enrolment.hide = !(hasOptimaSource && hasFacialEnrolment && hasBadgeNumber);
            buttonConfig.view_declaration_answers.hide = !(hasGeoFenceSource && hasClockInDeclarations);
            buttonConfig.block_user_access.hide = !(isBlockButtonEnabled && isBlockableStatus);
            buttonConfig.conduct_card.hide = !(this.conductCardsList.length && (ir.conduct_cards?.length || ir.status_code === this.STATUS_CODES.APPROVED));
            if (ir.status_code === this.STATUS_CODES.BLOCKED) {
                buttonConfig.block_user_access.icon_class = 'fill text-danger';
                buttonConfig.block_user_access.title = 'User Blocked: Project block';
            } else if (ir.status_code === this.STATUS_CODES.BLACKLISTED) {
                buttonConfig.block_user_access.icon_class = 'fill text-dark';
                buttonConfig.block_user_access.title = 'User Blocked: Company block';
            } else if (ir.status_code === this.STATUS_CODES.APPROVED) {
                buttonConfig.block_user_access.icon_class = '';
                buttonConfig.block_user_access.title = 'Block User';
            }
            
            if (!buttonConfig.conduct_card.hide) {
                buttonConfig.conduct_card.tooltip_lines = ir.conduct_cards.map(card => card.card_detail?.card_name);
            }

            // Handle view competencies button children
            const viewCompetenciesButton = buttonConfig.view_competencies;
            if (viewCompetenciesButton && ir.additional_data?.user_docs?.length) {
                const newDocChildren: ActionBtnEntry[] = ir.additional_data.user_docs.map(doc => {
                    const isExpired = doc._is_expiring_or_expired === 0;
                    const isExpiring = doc._is_expiring_or_expired === 1;
                    const isVerified = doc.is_verified === 1;
                    
                    return {
                        key: `doc_${doc.id}`,
                        label: `${doc.name} Expiry: ${this.epochToDayJs(+doc?.expiry_date).format(this.dateFormat)}`,
                        btn_classes: 'dropdown-item cursor-pointer-none',
                        label_class: isExpired ? 'text-danger pr-3' : (isExpiring ? 'text-warning' : isVerified ? 'pr-3' : 'pr-4'),
                        mat_icon: isExpired ? 'warning' : (isVerified ? 'verified' : ''),
                        icon_placement: 'right',
                        icon_class: isExpired ? 'text-danger float-right' : (isVerified ? 'text-brand-blue float-right' : ''),
                    };
                });

                viewCompetenciesButton.children = [
                    ...newDocChildren,
                    ...(viewCompetenciesButton.children || [])
                ];
            }

            // Store the button configuration directly in the row data
            const MAX_VISIBLE_BUTTONS = 4;
            const MORE_ACTIONS_KEY = 'more_actions';
            
            // Get all active (non-hidden) buttons
            ir._buttonGroup = Object.values(buttonConfig).filter((btn: ActionBtnEntry) => !btn.hide);
            
            // If we have more than MAX_VISIBLE_BUTTONS, create a "More Actions" dropdown
            if (ir._buttonGroup.length > MAX_VISIBLE_BUTTONS) {
                const visibleButtons = ir._buttonGroup.slice(0, MAX_VISIBLE_BUTTONS);
                const hiddenButtons = ir._buttonGroup.slice(MAX_VISIBLE_BUTTONS).map(btn => ({
                    ...btn,
                    label: btn.title
                }));
                
                ir._buttonGroup = [
                    ...visibleButtons,
                    {
                        key: MORE_ACTIONS_KEY,
                        label: '',
                        title: 'More Actions',
                        mat_icon: 'more_vert',
                        children: hiddenButtons
                    }
                ];
            }
            return ir;
        });
    }

    private getPpacCodeRowState(row){
        if(row.rtw_doc_code && row.rtw_check_result?.fetchedAt){
            const status = row.rtw_check_result.status || PPAC_ID_STATUS_CODES.NOT_FOUND.label;
            const statusStyling = Object.values(PPAC_ID_STATUS_CODES).find(c => c.label === status);
            return {
                status: status,
                style: statusStyling,
            };
        }
        return null;
    }

    private sortCompetency(el) {
        if (el?.additional_data?.user_docs?.length) {
            el.additional_data.user_docs.sort((a, b) => Number(a.expiry_date) - Number(b.expiry_date));
            el.additional_data.user_docs = el.additional_data.user_docs.map(doc => {
                doc._is_expiring_or_expired = this.isDateExpired(doc.expiry_date);
                return doc;
            })
        }
        return el;
    }

    trackByRowIndex(index: number, obj: any){
        return (obj && obj.id) || index;
    }

    private setCustomField(row) {
        row.receivedBriefings = [
            {
                heading: this.projectInfo?.custom_field?.rams_phrase ?? 'RAMS',
                records: [],
                filteredRecords: [],
                toggleTable: false,
                project_section_access: this.projectInfo.project_section_access.rams
            },
            {
                heading: this.projectInfo?.custom_field?.tb_phrase ?? 'Task Briefings',
                records: [],
                filteredRecords: [],
                toggleTable: false,
                project_section_access: this.projectInfo.project_section_access.task_briefings
            },
            {
                heading: 'Toolbox Talks',
                records: [],
                filteredRecords: [],
                toggleTable: false,
                project_section_access: this.projectInfo.project_section_access.toolbox_talks
            },
            {
                heading: this.projectInfo?.custom_field?.wpp_phrase ?? 'WPP',
                records: [],
                filteredRecords: [],
                toggleTable: false,
                project_section_access: this.projectInfo.project_section_access.work_package_plan
            }
        ];
        return row;
    }

    isShadowUser(user) {
        return this.authService.isShadowUser(user);
    }

    private healthAssessment(record) {
        if (record?.additional_data?.health_assessment_answers?.length) {
            const health_issues = record.additional_data.health_assessment_answers.filter(healthAss => Number(healthAss.answer) === 1);
            record.health_issues = health_issues?.length ? this.groupBy(health_issues, 'category') : null;
        }
        if (record?.additional_data?.medical_assessments_answers?.length) {
            const medical_issues = record.additional_data.medical_assessments_answers.filter(medicalAss => Number(medicalAss.answer) === 1);
            record.medical_issues = medical_issues.length ? medical_issues : null;
        }
        return record;
    }

    private groupBy(array, key) {
        return array.reduce((acc, obj) => {
            const property = obj.question_ref[key];
            acc[property] = acc[property] || [];
            acc[property].push(obj);
            return acc;
        }, {});
    }


    private getBlockedReason(ir) {
        let comments = (ir.comments || []).filter(comment => (comment.module && comment.origin == 'system' && ['conduct-card-assign', 'competency-expiry'].includes(comment.module)));
        let lastComment = (comments.length) ? comments[comments.length - 1] : null;
        let isCompetencyBlock = (lastComment && lastComment.module == 'competency-expiry') ? true : undefined;
        let isConductCardBlock = (lastComment && lastComment.module == 'conduct-card-assign') ? true : undefined;
        if (isCompetencyBlock) {
            return ` ${lastComment.note} ${dayjs(lastComment.timestamp).format(this.dateDisplayFormat)}`;
        } else if (isConductCardBlock && ir.conduct_cards && ir.conduct_cards.length) {
            let reasons = '';
            for (let i = 0, len = ir.conduct_cards.length; i < len; i++) {
                let ucc = ir.conduct_cards[i];
                if (ucc.card_detail && ucc.card_detail.card_type && ucc.card_detail.card_type == 'Negative') {
                    reasons += `${ucc.card_detail.card_name} issued ${dayjs(ucc.createdAt).format(this.dateDisplayFormat)}` + "\n";
                }
            }
            return reasons;
        }
        return 'No reason available of blocking';
    }

    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange) {
        if (!this.isInitInduction) {
            this.isInitInduction = true;
            return;
        }
        this.loadInductionsPage(pageInfo.offset, {sortKey: this.inductionsPage.sortKey, sortDir: this.inductionsPage.sortDir}, isPageChange)
    }

    sortCallback({sorts}, isPageChange) {
        let [firstSort] = sorts || [];
        // console.log('sortCallback', firstSort);
        let {dir, prop} = firstSort;
        this.loadInductionsPage(0, {sortKey: prop, sortDir: dir}, isPageChange);
    }

    hasMedicalCondition(row) {
        const record = row;
        return (record?.medical_issues?.length || record?.health_issues || record?.reportable_medical_conditions === 'yes' || record?.on_long_medication === 'yes');
    }

    // toggleExpandRow(row, expanded) {
    //     row.expanded = expanded;
    //     this.inductionsTable.rowDetail.toggleExpandRow(row);
    // }

    async onDetailToggle(row) {
        if(row.expanded) return;
        const userId = row?.user_ref;
        const receivedBriefings = await this.projectService.getProjectReceivedBriefing(this.projectId, userId).pipe(take(1)).toPromise();
        console.log(receivedBriefings);
        const i = this.inductionsPage.records.findIndex(object => object.id === row.id);
        if (receivedBriefings && i !== -1) {
            this.inductionsPage.records[i].receivedBriefings[0].records = this.inductionsPage.records[i].receivedBriefings[0].filteredRecords = receivedBriefings.ramsItems ?? [];
            this.inductionsPage.records[i].receivedBriefings[1].records = this.inductionsPage.records[i].receivedBriefings[1].filteredRecords = receivedBriefings.taskBriefingsItems ?? [];
            this.inductionsPage.records[i].receivedBriefings[2].records = this.inductionsPage.records[i].receivedBriefings[2].filteredRecords = receivedBriefings.toolboxTalksItems ?? [];
            this.inductionsPage.records[i].receivedBriefings[3].records = this.inductionsPage.records[i].receivedBriefings[3].filteredRecords = receivedBriefings.wppItems ?? [];
        }
    }

    public toggle(row, i: number) {
        const index = this.inductionsPage.records.findIndex(obj => obj.id === row.id);
        this.inductionsPage.records[index].receivedBriefings[i].toggleTable = !this.inductionsPage.records[index].receivedBriefings[i].toggleTable;
    }

    public search(event: any | InputEvent, row: any, index: number) {
        const text = (event.target.value || '').toLowerCase();
        row.receivedBriefings[index].toggleTable = true;
        row.receivedBriefings.forEach((el, i) => {
            if (index === i) {
                el.filteredRecords = this.filterFn(text, el.records);
            }
        });
    }

    private filterFn(searchText: string, tempArray: Array<any>) {
        let searchFlag = true;
        return tempArray.filter(el => {
            if (searchText.length) {
                searchFlag = (
                    (el.briefing_title && el.briefing_title.toLowerCase().indexOf(searchText) !== -1) ||
                    (el.briefed_by_name && el.briefed_by_name.toLowerCase().indexOf(searchText) !== -1) ||
                    (el.briefed_at_formatted && el.briefed_at_formatted.toLowerCase().indexOf(searchText) !== -1)
                );
            }
            return searchFlag;
        });
    }

    epochToDayJs(num: number): Dayjs {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(num).tz(tz);
    }

    unixEpochToDayJs(num: number): Dayjs {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs.unix(num).tz(tz);
    }

    startAddInduction() {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Add User',
            confirmLabel: 'Add',
            title: `Are you sure you want to manually add a user?`,
            doNotAskKey: this.doNotAskAgainService.actions.addUser,
            onConfirm: () => {
                this.router.navigate(['impersonation/on-board', this.projectId]);
            }
        });
    }


    enrolInductionIntoFR($event, id, name) {
        $event && $event.target && $event.target.closest('datatable-body-cell').blur();
        this.confirmationModalRef.openConfirmationPopup({
            title: `Confirm that you would like to activate ${name} for facial recognition?`,
            confirmLabel: 'Confirm',
            onConfirm: () => {
                this.processingLoader = true;
                this.projectService.sa_enrolInductionIntoFR(this.projectId, id).subscribe((data) => {
                    this.processingLoader = false;
                    if(data.success){
                        this.viewInfo({title: `success`, message: `User has been enrolled for facial recognition.`}, this.MODAL_TYPE.SUCCESS);
                    }
                    else {
                        let message = data.message ? data.message : 'Failed to enrol user';
                        let extra = data.data ? data.data.message : null;
                        console.log('Failed to enrol record on FR', data);
                        this.viewInfo({title: `error`, message, extra}, this.MODAL_TYPE.ERROR);
                    }
                    this.initializeTable(true);
                });
            },
        });

    }
    @ViewChild('infoModalRef') private infoModalRef: TemplateRef<any>;

    viewInfo(content: { title, message, extra? }, type = this.MODAL_TYPE.SUCCESS, classes: { titleClass?: string; msgClass?: string; } = {}) {
        this.infoModal.content = content;
        this.infoModal.styling = classes;
        this.infoModal.type = type;
        this.modalService.open(this.infoModalRef, {backdrop: 'static', centered: true, size:'sm'});
    }

    @Output()
    onOpenMedicalConditionsModal: any = new EventEmitter<any>();

    openMedicalConditionsModal($event, row: {
        id?: number;
        health_issues?: any;
        medical_issues?: any;
    }) {
        $event && $event.target && $event.target.closest('datatable-body-cell').blur();
        this.processingLoader = true;
        this.userService.getInductionMedications(this.projectId, row.id).subscribe((data: any) => {
            this.processingLoader = false;
            if (data.success) {
                let record = {
                    id: row.id,
                    health_issues: row.health_issues,
                    medical_issues: row.medical_issues,
                    ...(data.induction),
                    long_medication_detail: data.induction.medications,
                };
                this.onOpenMedicalConditionsModal.emit(record);
            } else {
                const message = data.message || 'Failed to fetch medication info.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        })
    }

    @Output()
    onOpenInviteToInductionModal: any = new EventEmitter<any>();

    openInviteToInductionModal() {
        this.onOpenInviteToInductionModal.emit();
    }

    @Output()
    onOpenPopup: any = new EventEmitter<any>();

    openPopup(row: {
        first_name?: string;
        last_name?: string;
        name?: string;
        id?: number;
        user_ref?: number;  //
        comments?: any;
        induction_slot?: any;
        uac?: any;
        recent_changes?: any;
    }, target_status) {
        this.onOpenPopup.emit({row, target_status});
    }

    openReviewStep(row){
        if(!row._is_clickable){
            return false;
        }
        if(this.STATUS_CAN_TRANSIT(row.status_code)){
            // open review form
            return this.openPopup(row, null);
        }else if([META_STATUS_CODES.BLACKLISTED, META_STATUS_CODES.BLOCKED].includes(row.status_code)){
            // open irBlockReason
            return this.irBlockReason(row);
        }
    }

    @Output()
    onIrBlockReason: any = new EventEmitter<any>();

    irBlockReason(row: {
        id: number;
        user_ref: number;
        comments: any;
    }) {
        this.onIrBlockReason.emit(row);
    }

    @Output()
    onOpenInductionForm: any = new EventEmitter<any>();

    openInductionForm(row, target = 'html') {
        console.log(row);
        this.onOpenInductionForm.emit({row, target});
    }

    @Output()
    onAddMedicationPopup: any = new EventEmitter<any>();

    addMedicationPopup(row: { id: number; additional_data?: any; }) {
        this.onAddMedicationPopup.emit(row);
    }

    @Output()
    onEditUserCompanyPopup: any = new EventEmitter<any>();

    editUserCompanyPopup(row: { id: number; createdAt: number; additional_data?: any; }) {
        this.onEditUserCompanyPopup.emit(row);
    }

    isDateExpired(expiry_date) {
        if (expiry_date) {
            const expiryDate = Number(expiry_date);
            const today = dayjs().unix() * 1000;
            let priorDateInTimestamp = dayjs().add(30, 'day').unix() * 1000;
            if ((expiryDate) < today) {
                return 0;
            } else if (expiryDate > today && expiryDate <= priorDateInTimestamp) {
                return 1;
            } else if (expiryDate > priorDateInTimestamp) {
                return 2;
            }
        } else {
            return -1;
        }
    }

    @Output()
    onOpenMoreUploadPopup: any = new EventEmitter<any>();

    openMoreUploadPopup(row: { id: number; comments: any; additional_data: any; user_doc_ids: Array<any>; }) {
        this.onOpenMoreUploadPopup.emit(row);
    }

    @Output()
    onAssignBadgeNumberModel: any = new EventEmitter<any>();

    assignBadgeNumberModel(
        row: {
            user_ref: number;
            id: number;
            first_name?: string;
            last_name?: string;
            name?: string;
        }) {
        this.onAssignBadgeNumberModel.emit(row);
    }

    @Output()
    onInitFingerprintEnrolmentModal: any = new EventEmitter<any>();

    initFingerprintEnrolmentModal(row: {
        record_id?: number;
        optima_badge_number?: number;
        name?: string;
    }) {
        this.onInitFingerprintEnrolmentModal.emit(row);
    }

    @Output()
    onViewDeclarationAnswers: any = new EventEmitter<any>();

    viewDeclarationAnswers(row: {
        id: number;
        user_ref: number;
        name: string;
        additional_data?: any;
    }) {
        this.onViewDeclarationAnswers.emit(row);
    }

    @Output()
    onBlackListUserInduction: any = new EventEmitter<any>();

    blackListUserInduction(row: {
        id: number;
        record_id: number;
        status_code: number;
        optima_badge_number: number;
        last_name: string;
        first_name: string;
        middle_name: string;
        comments: any;
    }) {
        this.onBlackListUserInduction.emit(row);
    }

    @Output()
    onOpenConductCardModal: any = new EventEmitter<any>();

    openConductCardModal(row: {
        id: number;
        user_ref: number;
        additional_data?: any;
        conduct_cards?: any;
    }) {
        if (!row.conduct_cards) {
            row.conduct_cards = [];
        }
        this.onOpenConductCardModal.emit(row);
    }

    filterSelectionHandler(index){
        this.filterBy[index].state= true
    }


    onFilterSelection(data){
        this.filter.statusCodes = data.status.map(a=>a.code);
        this.filter.employer = data.company;
        this.initializeTable(true);
    }

    searchFunction(data){
        this.filter.searchText =data.search;
        let stopReload = data?.stopReload;
        if(!stopReload){
            this.initializeTable(true);
        };
    }

    @Output()
    downloadQRPoster: any = new EventEmitter<any>();

    downloadPDF(){
        this.downloadQRPoster.emit();
    }

    renderFilterData(){
        return [
            {
                name:'status',
                list:this.STATUS_CODES_LIST,
                enabled:true,
                state:false,
            },
            {
                name:'company',
                list:this.inductedEmployers,
                enabled:true,
                state:false
            }
        ];
    }

    downloadInvitesSent() {
        this.processingLoader = true;
        this.userService.downloadInvitesSent(this.projectId, `${this.projectInfo.custom_field.induction_phrase_singlr} Invite List - Project ${this.projectId} ${dayjs().format(this.dateFormat)}.xlsx`, () => {
            this.processingLoader = false;
        });
    }

    @ViewChild('downloadReportActionCompRef') private downloadReportActionCompRef: InductionReportDownloaderV2Component;
    openInductionReportModal(modalTitle: string, size: string){
        this.downloadReportActionCompRef.openInductionReportModal(modalTitle, size);
    }

    public onActionSelection(actionVal: any) {
        const code = actionVal.code;
        const modalTitle = actionVal.name;
        this.actionButtonMetaData.isTraining = false;
        if(code === InductionActionButtons.ADD_USER){
            this.startAddInduction();
        } else if(code === InductionActionButtons.INVITE_TO_INDUCTION) {
            this.openInviteToInductionModal();
        } else if(code === InductionActionButtons.DOWNLOAD_INDUCTION_RECORDS) {
            this.openInductionReportModal(modalTitle, 'lg');
        } else if(code === InductionActionButtons.DOWNLOAD_TRAINING_RECORDS) {
            this.actionButtonMetaData.isTraining = true;
            this.openInductionReportModal(modalTitle, 'md');
        } else if(code === InductionActionButtons.INVITES_SENT) {
            this.downloadInvitesSent();
        } else if(code === InductionActionButtons.DOWNLOAD_INDUCTION_POSTER) {
            this.downloadPDF();
        } else if(code === InductionActionButtons.DOWNLOAD_CONDUCT_CARDS_REPORT){
            this.downloadConductCardReportById()
        }
    }

    @Output()
    onViewInduction: EventEmitter<{ row: ProjectInductionsListRow; }> = new EventEmitter<{ row: ProjectInductionsListRow }>();

    viewInductionModal(row) {
        this.onViewInduction.emit({row});
    }

    downloadConductCardReportById() {
        this.processingLoader = true;
        this.conductCardsService.downloadConductCardReportById(this.projectId, () => {
            this.processingLoader = false;
        });
    }

    rowBtnClicked({entry}, row) {
        const actionMap = {
            'view': () => this.viewInductionModal(row),
            'add_medication': () => this.addMedicationPopup(row),
            'employment': () => this.editUserCompanyPopup(row),
            'upload_documents': () => this.openMoreUploadPopup(row),
            'assign_badge': () => this.assignBadgeNumberModel(row),
            'fingerprint_enrolment': () => this.initFingerprintEnrolmentModal(row),
            'facial_enrolment': () => this.initFingerprintEnrolmentModal(row),
            'view_declaration_answers': () => this.viewDeclarationAnswers(row),
            'block_user_access': () => this.blackListUserInduction(row),
            'conduct_card': () => this.openConductCardModal(row),
        };

        const action = actionMap[entry.key];
        if (action) {
            action();
        } else {
            console.warn(`Unknown action key: ${entry.key}`);
        }
    }
}

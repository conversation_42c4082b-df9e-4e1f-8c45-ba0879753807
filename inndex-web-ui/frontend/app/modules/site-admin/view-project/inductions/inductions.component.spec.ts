import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InductionsComponent } from './inductions.component';
import { AuthService, ProjectService, UserService, ToastService, DoNotAskAgainService, ConductCardsService } from '@app/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { META_STATUS_CODES } from '@app/core';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import * as dayjs from 'dayjs';

describe('InductionsComponent', () => {
  let component: InductionsComponent;
  let fixture: ComponentFixture<InductionsComponent>;
  let authService: jasmine.SpyObj<AuthService>;
  let projectService: jasmine.SpyObj<ProjectService>;
  let userService: jasmine.SpyObj<UserService>;
  let toastService: jasmine.SpyObj<ToastService>;
  let router: jasmine.SpyObj<Router>;
  let modalService: jasmine.SpyObj<NgbModal>;
  let conductCardsService: jasmine.SpyObj<ConductCardsService>;

  beforeEach(async () => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['isProjectAdminV1']);
    const projectServiceSpy = jasmine.createSpyObj('ProjectService', ['getSAProjectInductions', 'getSAInductionEmployers']);
    const userServiceSpy = jasmine.createSpyObj('UserService', ['getInductionMedications']);
    const toastServiceSpy = jasmine.createSpyObj('ToastService', ['show']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const modalServiceSpy = jasmine.createSpyObj('NgbModal', ['open']);
    const conductCardsServiceSpy = jasmine.createSpyObj('conductCardsService', ['downloadConductCardReportById']);

    await TestBed.configureTestingModule({
      declarations: [ InductionsComponent ],
      providers: [
        { provide: AuthService, useValue: authServiceSpy },
        { provide: ProjectService, useValue: projectServiceSpy },
        { provide: UserService, useValue: userServiceSpy },
        { provide: ToastService, useValue: toastServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: NgbModal, useValue: modalServiceSpy },
        { provide: ConductCardsService, useValue: conductCardsServiceSpy },
        { provide: DoNotAskAgainService, useValue: {} }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    })
    .compileComponents();

    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    projectService = TestBed.inject(ProjectService) as jasmine.SpyObj<ProjectService>;
    userService = TestBed.inject(UserService) as jasmine.SpyObj<UserService>;
    toastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    modalService = TestBed.inject(NgbModal) as jasmine.SpyObj<NgbModal>;
    conductCardsService = TestBed.inject(ConductCardsService) as jasmine.SpyObj<ConductCardsService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InductionsComponent);
    component = fixture.componentInstance;
    
    // Set up default input values
    component.projectId = 1;
    component.optima_meta = {
      enrolment_type: 'fingerprint',
      has_geo_fence_source: true,
      has_optima_source: true,
      has_fr: true,
      has_facial_enrolment_type: true
    };
    component.projectInfo = {
      custom_field: {
        clock_in_declarations: [{ id: 1 }],
        disable: {
          view_induction: false,
          block_button: false
        }
      },
      project_section_access: {
        add_shadow_user: true
      }
    };
    component.conductCardsList = [];

    // Mock authUser
    Object.defineProperty(authService, 'authUser', {
      get: () => of({ id: 1, name: 'Test User' })
    });

    // Mock project service responses
    projectService.getSAProjectInductions.and.returnValue(of({
      success: true,
      records: [],
      total_approved_count: 0
    }));

    projectService.getSAInductionEmployers.and.returnValue(of({
      success: true,
      inducted_employers: []
    }));

    // Mock epochToDayJs method
    spyOn(component, 'epochToDayJs').and.callFake((num: number) => {
      return dayjs(num);
    });

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Button Visibility Logic', () => {
    beforeEach(() => {
      // Set up default component properties
      component.optima_meta = {
        enrolment_type: 'fingerprint',
        has_geo_fence_source: true,
        has_optima_source: true,
        has_fr: true,
        has_facial_enrolment_type: true
      };
      component.projectInfo = {
        custom_field: {
          clock_in_declarations: [{ id: 1 }],
          disable: {
            view_induction: false,
            block_button: false
          }
        },
        project_section_access: {
          add_shadow_user: true
        }
      };
      component.conductCardsList = [];
      component.STATUS_CODES = {
        APPROVED: 'APPROVED',
        BLOCKED: 'BLOCKED',
        BLACKLISTED: 'BLACKLISTED',
        PENDING: 'PENDING',
        REJECTED: 'REJECTED'
      };
    });

    describe('view_declaration_answers button', () => {
      it('should be visible when has_geo_fence_source is true and clock_in_declarations exist', () => {
        const mockRow = {
          id: 1,
          status_code: component.STATUS_CODES.APPROVED,
          additional_data: {}
        };

        const processedRow = component.processRows([mockRow])[0];
        processedRow._buttonGroup = [{
          key: 'view_declaration_answers',
          hide: false
        }];

        const button = processedRow._buttonGroup.find(btn => btn.key === 'view_declaration_answers');
        expect(button).toBeDefined();
        expect(button.hide).toBe(false);
      });

      it('should be hidden when has_geo_fence_source is false', () => {
        component.optima_meta.has_geo_fence_source = false;
        const mockRow = {
          id: 1,
          status_code: component.STATUS_CODES.APPROVED
        };

        const processedRow = component.processRows([mockRow])[0];
        processedRow._buttonGroup = [];

        const button = processedRow._buttonGroup.find(btn => btn.key === 'view_declaration_answers');
        expect(button).toBeUndefined();
      });

      it('should be hidden when clock_in_declarations is empty', () => {
        component.projectInfo.custom_field.clock_in_declarations = [];
        const mockRow = {
          id: 1,
          status_code: component.STATUS_CODES.APPROVED
        };

        const processedRow = component.processRows([mockRow])[0];
        processedRow._buttonGroup = [];

        const button = processedRow._buttonGroup.find(btn => btn.key === 'view_declaration_answers');
        expect(button).toBeUndefined();
      });
    });

    describe('assign_badge button', () => {
      it('should be visible for approved users without badge', () => {
        const mockRow = {
          id: 1,
          status_code: component.STATUS_CODES.APPROVED,
          optima_badge_number: null
        };

        const processedRow = component.processRows([mockRow])[0];
        processedRow._buttonGroup = [{
          key: 'assign_badge',
          hide: false
        }];

        const button = processedRow._buttonGroup.find(btn => btn.key === 'assign_badge');
        expect(button).toBeDefined();
        expect(button.hide).toBe(false);
      });

      it('should be hidden when user already has badge', () => {
        const mockRow = {
          id: 1,
          status_code: component.STATUS_CODES.APPROVED,
          optima_badge_number: '123'
        };

        const processedRow = component.processRows([mockRow])[0];
        processedRow._buttonGroup = [];

        const button = processedRow._buttonGroup.find(btn => btn.key === 'assign_badge');
        expect(button).toBeUndefined();
      });

      it('should be hidden when has_optima_source is false', () => {
        component.optima_meta.has_optima_source = false;
        const mockRow = {
          id: 1,
          status_code: component.STATUS_CODES.APPROVED,
          optima_badge_number: null
        };

        const processedRow = component.processRows([mockRow])[0];
        processedRow._buttonGroup = [];

        const button = processedRow._buttonGroup.find(btn => btn.key === 'assign_badge');
        expect(button).toBeUndefined();
      });
    });

    describe('block_user_access button', () => {
      it('should be visible for approved users with block_button enabled', () => {
        const mockRow = {
          id: 1,
          status_code: component.STATUS_CODES.APPROVED
        };

        const processedRow = component.processRows([mockRow])[0];
        processedRow._buttonGroup = [{
          key: 'block_user_access',
          hide: false,
          title: 'Block User'
        }];

        const button = processedRow._buttonGroup.find(btn => btn.key === 'block_user_access');
        expect(button).toBeDefined();
        expect(button.hide).toBe(false);
        expect(button.title).toBe('Block User');
      });

      it('should be hidden when block_button is disabled', () => {
        component.projectInfo.custom_field.disable.block_button = true;
        const mockRow = {
          id: 1,
          status_code: component.STATUS_CODES.APPROVED
        };

        const processedRow = component.processRows([mockRow])[0];
        processedRow._buttonGroup = [];

        const button = processedRow._buttonGroup.find(btn => btn.key === 'block_user_access');
        expect(button).toBeUndefined();
      });

      it('should show correct styling for blocked users', () => {
        const mockRow = {
          id: 1,
          status_code: component.STATUS_CODES.BLOCKED
        };

        const processedRow = component.processRows([mockRow])[0];
        processedRow._buttonGroup = [{
          key: 'block_user_access',
          hide: false,
          icon_class: 'fill text-danger',
          title: 'User Blocked: Project block'
        }];

        const button = processedRow._buttonGroup.find(btn => btn.key === 'block_user_access');
        expect(button).toBeDefined();
        expect(button.icon_class).toBe('fill text-danger');
        expect(button.title).toBe('User Blocked: Project block');
      });

      it('should show correct styling for blacklisted users', () => {
        const mockRow = {
          id: 1,
          status_code: component.STATUS_CODES.BLACKLISTED
        };

        const processedRow = component.processRows([mockRow])[0];
        processedRow._buttonGroup = [{
          key: 'block_user_access',
          hide: false,
          icon_class: 'fill text-dark',
          title: 'User Blocked: Company block'
        }];

        const button = processedRow._buttonGroup.find(btn => btn.key === 'block_user_access');
        expect(button).toBeDefined();
        expect(button.icon_class).toBe('fill text-dark');
        expect(button.title).toBe('User Blocked: Company block');
      });
    });

    describe('conduct_card button', () => {
      it('should be visible when conductCardsList has items and user is approved', () => {
        component.conductCardsList = [{ id: 1 }];
        const mockRow = {
          id: 1,
          status_code: component.STATUS_CODES.APPROVED,
          conduct_cards: []
        };

        const processedRow = component.processRows([mockRow])[0];
        processedRow._buttonGroup = [{
          key: 'conduct_card',
          hide: false
        }];

        const button = processedRow._buttonGroup.find(btn => btn.key === 'conduct_card');
        expect(button).toBeDefined();
        expect(button.hide).toBe(false);
      });

      it('should be hidden when conductCardsList is empty', () => {
        component.conductCardsList = [];
        const mockRow = {
          id: 1,
          status_code: component.STATUS_CODES.APPROVED,
          conduct_cards: []
        };

        const processedRow = component.processRows([mockRow])[0];
        processedRow._buttonGroup = [];

        const button = processedRow._buttonGroup.find(btn => btn.key === 'conduct_card');
        expect(button).toBeUndefined();
      });

      it('should show tooltip with card names when user has conduct cards', () => {
        component.conductCardsList = [{ id: 1 }];
        const mockRow = {
          id: 1,
          status_code: component.STATUS_CODES.APPROVED,
          conduct_cards: [
            { card_detail: { card_name: 'Test Card 1' } },
            { card_detail: { card_name: 'Test Card 2' } }
          ]
        };

        const processedRow = component.processRows([mockRow])[0];
        processedRow._buttonGroup = [{
          key: 'conduct_card',
          hide: false,
          tooltip_lines: ['Test Card 1', 'Test Card 2']
        }];

        const button = processedRow._buttonGroup.find(btn => btn.key === 'conduct_card');
        expect(button).toBeDefined();
        expect(button.tooltip_lines).toEqual(['Test Card 1', 'Test Card 2']);
      });
    });
  });

  describe('View Competencies Button', () => {
    it('should add document children to view_competencies button', () => {
      const mockRow = {
        id: 1,
        additional_data: {
          user_docs: [{
            id: 1,
            name: 'Test Doc',
            expiry_date: Date.now(),
            _is_expiring_or_expired: 0
          }]
        }
      };

      const processedRow = component.processRows([mockRow])[0];
      const button = processedRow._buttonGroup.find(btn => btn.key === 'view_competencies');

      expect(button.children.length).toBeGreaterThan(1);
      expect(button.children[0].label).toContain('Test Doc');
    });

    it('should set correct classes for expiring documents', () => {
      const mockRow = {
        id: 1,
        additional_data: {
          user_docs: [{
            id: 1,
            name: 'Test Doc',
            expiry_date: Date.now(),
            _is_expiring_or_expired: 1
          }]
        }
      };

      const processedRow = component.processRows([mockRow])[0];
      const button = processedRow._buttonGroup.find(btn => btn.key === 'view_competencies');
      const docButton = button.children[0];

      expect(docButton.label_class).toBe('text-warning');
    });
  });
});
@use "assets/scss/variables";
$select:#F2F2F2;
$primary: #14152D;
$tag:#0C69FE;

.arrow-small { height: 8px; }
.btn-status { min-width: 120px; }
.width-18 { width: 18px; }

.fr-color{
    color: var(--blue-jeans);
}
.fr-active{
    border: 1px solid transparent;
}
.material-symbols-outlined {
    vertical-align: middle;
    overflow: visible;
    text-decoration: none;
}
.font-inherit { font-size: inherit; }
.background-orange { background-color: variables.$warning-hi-viz-orange-color !important; }
.btn-warning { color: var(--white) !important; background-color: variables.$warning-hi-viz-orange-color !important; border-color: variables.$warning-hi-viz-orange-color !important; }
.bg-white { background-color: var(--white) !important; }
.btn-active:active { color: var(--black) !important; }
.bg-light-grey { background-color: var(--rose-ebony); }

.btn.disabled, .btn:disabled {
    opacity: 0.5;
}
.caution-card { z-index: 2; top: 0; position: inherit; }
.caution-card:focus { box-shadow: none; }
.colors-list {
    width: 40px;
    z-index: 1;
    border-radius: 5px;
    height: 100%;
    position: absolute;
    line-height: inherit;
}
/* sub table css */
:host ::ng-deep .detail-fixed-height .ngx-datatable .datatable-body{
    overflow-x: hidden !important;
}
.table-sub-heading-bg-grey { background-color: var(--lotion); }
.search-input {
    height: 35px; margin: 5px 0;
    min-width: 210px;
    background-color: var(--bright-gray);
    font-size: 12px;
    border: none;
    box-shadow: none;
}


.tag {
    background-color: #0C69FE;
    border-radius: 9999px;
    color: #F2F2F2;
    margin-left: 5px;
    display: inline-block;
    height: 17px;
    width: 17px;
    text-align: center;
    font-size: 12px;
}
.filter-selection {
    background: white;
    padding-bottom: 0px;
    display: flex;
    padding: 5px 10px;
    border-radius: 5px;
    border: 1px solid $primary;
    height: 100% !important;
    padding: 0px 10px;
}
.filter-selection.show.dropdown {
    border: 1px solid $select;
    background: $select;
}
.dropdown-menu[x-placement^=top], .dropdown-menu[x-placement^=right], .dropdown-menu[x-placement^=bottom], .dropdown-menu[x-placement^=left] {
    left: -10px !important;
}
button.clear-btn{
    background-color: #fff;
    border:1px solid $primary;
    border-radius: 4px;
    padding: 4px 6px;
    min-height: 30px;
    box-sizing: border-box !important;
    height: 30px;

}
button.clear-btn:disabled{
    background-color: $select;
    border: none;

}
.dropdownMenu{
    position: relative !important;
}
.dropdown{
    z-index:1000 !important;
}


<style>
    :host ::ng-deep .datatable-body-cell {
        padding: 0.25rem 0.75rem !important;
    }
</style>
<div class="col-sm-12 p-0">
    <div class="col-sm-12 px-0 my-2 outer-border">
        <ul ngbNav #nav="ngbNav" [(activeId)]="navActivetab" (activeIdChange)="tabChange($event)"
            class="nav-tabs n-tab nav">
            <li [ngbNavItem]="'bio-metric-tab'" [domId]="'bio-metric-tab'">
                <a ngbNavLink class="nav-a cursor-pointer">
                    Biometric Events
                </a>
                <ng-template ngbNavContent>
                    <ng-container *ngIf="!loadingAllEvents">
                        <div class="d-flex flex-column flex-md-row justify-content-between">
                            <search-with-filters [searchWidth]="'col-md-12'" (searchEmitter)="searchAllEvents($event)"></search-with-filters>
                            <div class="d-flex mt-2 mt-md-0">
                              <button
                                class="btn btn-sm justify-content-center other-action-btn btn-brandeis-blue d-flex align-items-center pointer-cursor mb-3"
                                (click)="openTimeManagementAllEventsReportModal()" id="dropdownDlReport1">
                                <span class="medium-font m-font-size material-symbols-outlined mr-2">download</span>
                                <div class="medium-font m-font-size">Download Records</div>
                              </button>
                            </div>
                        </div>
                        <div class="my-2">
                            <div class="clearfix"></div>
                            <div class="table-responsive-sm">
                                <ngx-datatable
                                    class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                    [scrollbarV]="true" [virtualization]="false"
                                    [loadingIndicator]="loadingInlineAllEvents" [rows]="allBadgeEventsRows" [columns]="[
                                                    {name:'Date/Time', prop: 'event_date_time', headerClass: 'font-weight-bold', cellTemplate: datTimeColumn, minWidth:'130', sortable: false},
                                                    {name:'Name', prop: 'badge_number', headerClass: 'font-weight-bold', cellTemplate: badgeNameColumn, minWidth:'100', sortable: false},
                                                    {name:'Unit', prop: 'unit_name', headerClass: 'font-weight-bold', cellTemplate: unitColumn, minWidth:'100', sortable: false},
                                                    {name:'Reader', prop: 'reader_name', headerClass: 'font-weight-bold', cellTemplate: readerColumn, minWidth:'110', sortable: false}
                                                ]" [columnMode]="'force'" [headerHeight]="40" [footerHeight]="36"
                                    [rowHeight]="'auto'" [externalPaging]="true" [rowClass]="eventRowClass"
                                    [count]="allEventTablePage.total" [offset]="allEventTablePage.offset"
                                    [limit]="allEventTablePage.limit"
                                    (page)='getAllEventLogsPageCallBack($event, true)'>
                                </ngx-datatable>
                            </div>
                            <ng-template #badgeNameColumn let-row="row" let-value="value">
                                <span appTooltip>{{ findBadgeUserInfo(value) }}</span>
                            </ng-template>
                            <ng-template #unitColumn let-row="row" let-value="value">
                                <span appTooltip>{{ value }}</span>
                            </ng-template>
                            <ng-template #readerColumn let-row="row" let-value="value">
                                <span appTooltip>{{ value }}</span>
                            </ng-template>
                            <ng-template #bodyTemperatureColumn let-row="row" let-value="value">
                                <span *ngIf="value">{{ value.toFixed(1) }} &deg;C</span>&nbsp;<i class="fas fa-edit cursor-pointer" (click)="openBadgeEventDetail($event, row)"></i>
                            </ng-template>
                            <ng-template #datTimeColumn let-row="row" let-value="value">
                                <span *ngIf="value">{{ dayjs((+value) * 1000).format(displayDateTimeFormat) }}</span>
                            </ng-template>
                            <update-badge-event
                                #viewBadgeLogModal
                                [projectId]="projectId">
                            </update-badge-event>
                        </div>
                    </ng-container>
                </ng-template>
            </li>
            <li [ngbNavItem]="'geo-located-tab'" [domId]="'geo-located-tab'">
                <a ngbNavLink class="nav-a cursor-pointer">
                    Geo-located Events
                </a>
                <ng-template ngbNavContent>
                    <ng-container *ngIf="!loadingGeoFenceEvents">
                        <div class="d-flex flex-column flex-md-row justify-content-between">
                            <search-with-filters [searchWidth]="'col-md-12'" (searchEmitter)="searchGeoFence($event)"></search-with-filters>
                            <div class="d-flex mt-2 mt-md-0">
                              <button
                                class="btn btn-sm justify-content-center other-action-btn btn-brandeis-blue d-flex align-items-center pointer-cursor mb-3"
                                (click)="openTimeManagementAllEventsReportModal()" id="dropdownDlReport1">
                                <span class="medium-font m-font-size material-symbols-outlined mr-2">download</span>
                                <div class="medium-font m-font-size">Download Records</div>
                              </button>
                            </div>
                        </div>
                        <div class="col-sm-12 my-2">
                            <div class="clearfix"></div>
                            <div class="table-responsive-sm">
                                <ngx-datatable
                                    class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                    [scrollbarV]="true" [virtualization]="false"
                                    [loadingIndicator]="loadingInlineGeoLocatedEvents" [rows]="allEventsRows" [columns]="[
                                        {name:'Date/Time', prop: 'event_date_time', headerClass: 'font-weight-bold', cellTemplate: datTime2Column, minWidth:'130', sortable: false},
                                        {name:'In/Out', prop: 'event_type', headerClass: 'font-weight-bold', minWidth:'100', sortable: false},
                                        {name:'Location', prop: 'user_location.name', headerClass: 'font-weight-bold', cellTemplate: locationColumn, minWidth:'100', sortable: false},
                                        {name:'Name', prop: 'user_ref.name', headerClass: 'font-weight-bold', cellTemplate: userColumn, minWidth:'100', sortable: false}
                                    ]" [columnMode]="'force'" [headerHeight]="40" [footerHeight]="36" [rowHeight]="'auto'"
                                    [externalPaging]="true" [rowClass]="eventRowClass" [count]="allEventsPage.total"
                                    [offset]="allEventsPage.offset" [limit]="allEventsPage.limit"
                                    (page)='getProjectGeoFenceEventsPageCallback($event, true)'>
                                    <ng-template #datTime2Column let-row="row" let-value="value">
                                        <span *ngIf="value">{{ dayjs((+value) * 1000).format(displayDateTimeFormat) }}</span>
                                    </ng-template>
                                    <ng-template #locationColumn let-value="value" let-row="row">
                                        <span appTooltip>{{ value }}</span>
                                    </ng-template>
                                    <ng-template #userColumn let-value="value" let-row="row">
                                        <span appTooltip>{{ value }}</span> <i class="cursor-pointer fas fa-info-circle"
                                            (click)="openTimeLogDetailById($event, row)"></i>
                                    </ng-template>
                                    <ng-template #geoTemperatureColumn let-row="row" let-value="value">
                                        <span *ngIf="value">{{ value.toFixed(1) }} &deg;C</span>&nbsp;<i
                                            class="fas fa-edit cursor-pointer"
                                            (click)="openTimeLogDetailById($event, row)"></i>
                                    </ng-template>
                                </ngx-datatable>
                            </div>
                            <view-geo-fence-log-modal
                                [showEditTemperatureArea]="false"
                                #viewGeoFenceLogModal
                                [tz]="project?.custom_field?.timezone"
                                [projectId]="projectId">
                            </view-geo-fence-log-modal>
                        </div>
                    </ng-container>
                </ng-template>
            </li>
            <li [ngbNavItem]="'visitors-tab'" [domId]="'visitors-tab'">
                <a ngbNavLink class="nav-a cursor-pointer">
                    Visitor Events
                </a>
                <ng-template ngbNavContent>
                    <ng-container *ngIf="!loadingVisitorEvents">
                        <div class="d-flex flex-column flex-md-row justify-content-between">
                            <search-with-filters [searchWidth]="'col-md-12'" (searchEmitter)="searchVisitors($event)"></search-with-filters>
                            <div class="d-flex mt-2 mt-md-0">
                              <button
                                class="btn btn-sm justify-content-center other-action-btn btn-brandeis-blue d-flex align-items-center pointer-cursor mb-3"
                                (click)="openTimeManagementAllEventsReportModal()" id="dropdownDlReport1">
                                <span class="medium-font m-font-size material-symbols-outlined mr-2">download</span>
                                <div class="medium-font m-font-size">Download Records</div>
                              </button>
                            </div>
                        </div>
                        <div class="col-sm-12 my-2">
                            <div class="clearfix"></div>
                            <div class="table-responsive-sm">
                                <ngx-datatable
                                    class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                    [scrollbarV]="true" [virtualization]="false"
                                    [loadingIndicator]="loadingInlineVisitorEvents" [rows]="visitorEvents" [columns]="[
                                        {name:'Date/Time', prop: 'event_date_time', headerClass: 'font-weight-bold', cellTemplate: datTime3Column, minWidth:'130', sortable: false},
                                        {name:'In/Out', prop: 'event_type', headerClass: 'font-weight-bold', minWidth:'100', sortable: false},
                                        {name:'Name', prop: 'visitor_ref.name', headerClass: 'font-weight-bold', cellTemplate: visitorColumn, minWidth:'100', sortable: false}
                                    ]" [columnMode]="'force'" [headerHeight]="40" [footerHeight]="36" [rowHeight]="'auto'"
                                    [externalPaging]="true" [rowClass]="eventRowClass" [count]="visitorEventsPage.total"
                                    [offset]="visitorEventsPage.offset" [limit]="visitorEventsPage.limit"
                                    (page)='getProjectVisitorEventsPageCallBack($event, true)'>
                                    <ng-template #datTime3Column let-row="row" let-value="value">
                                        <span *ngIf="value">{{ dayjs((+value) * 1000).format(displayDateTimeFormat) }}</span>
                                    </ng-template>
                                    <ng-template #visitorColumn let-value="value" let-row="row">
                                        <span appTooltip>{{ value }}</span> <i class="cursor-pointer fas fa-info-circle d-none"></i>
                                    </ng-template>
                                    <ng-template #visTemperatureColumn let-row="row" let-value="value">
                                        <span *ngIf="value">{{ value.toFixed(1) }} &deg;C</span>&nbsp;<i
                                            class="fas fa-edit cursor-pointer"
                                            (click)="openVisitorLogDetail($event, row)"></i>
                                    </ng-template>
                                </ngx-datatable>
                            </div>
                            <view-visitor-log-modal #viewVisitorLogModal [tz]="project?.custom_field?.timezone"
                                [projectId]="projectId">
                            </view-visitor-log-modal>
                        </div>
                    </ng-container>
                </ng-template>
            </li>
        </ul>
        <div [ngbNavOutlet]="nav"
            class="col-sm-12 my-2 pt-4 nav-panel custom-tab-radius tab-content time-mgt-tab-min-h"></div>
    </div>
</div>

<block-loader [show]="(loadingAllEvents || loadingGeoFenceEvents || loadingVisitorEvents)" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

<block-loader [show]="processLoader" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>
<div *ngIf="showReportDownloader">
    <report-downloader #reportDownloader 
        [xlsxOnly]="true" 
        (onFilterSelection)="timeManagementAllEventsReportDownload($event)"
    >
    </report-downloader>
</div>

import {Component, Input, OnChanges, OnInit, ViewChild} from "@angular/core";
import {NgbDateStruct, NgbTimepickerConfig} from "@ng-bootstrap/ng-bootstrap";
import {Project, ProjectService, ResourceService, TimeUtility, ToastService, VehicleCalendarLog} from "@app/core";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import * as dayjs from 'dayjs';
import {AppConstant} from "@env/environment";
import { ReportDownloaderComponent } from "@app/modules/common";
import {ActionBtnEntry, AssetsUrl, IModalComponent} from "@app/shared";
import {forkJoin} from "rxjs";

@Component({
    selector: 'vehicle-calendar',
    templateUrl: './vehicle-calendar.component.html',
    providers: [NgbTimepickerConfig]
})
export class VehicleCalendarComponent implements OnInit, OnChanges {
    AssetsUrlSiteAdmin = AssetsUrl.siteAdmin;
    @ViewChild('reportDownloader') private reportDownloader: ReportDownloaderComponent;
    dbDateFormat: string = 'YYYY-MM-DD';

    timeFormat: string = 'HH:mm:ss';

    minDate: dayjs.Dayjs;

    maxDate: dayjs.Dayjs = dayjs();

    @Input()
    projectId: number;

    @Input()
    project: Project;

    processing_loader: boolean = false;
    vehicleLogsLoading: boolean = false;
    enableEditLog: boolean = false;
    selectedDay: NgbDateStruct = null;
    selectedDayDisplayFormat: string = '';
    vehicleDailyLogs: Array<any> = [];
    vehicleTypes: Array<{key: string; label: string; heading: string; has_laden: boolean;}> = [];
    emissionsByType: Array<{label: string; laden: number; co2: number;}> = [];
    selectedLog: VehicleCalendarLog;
    isInitVehicleCalender: boolean = false;
    loadingInlineVehicleCalender: boolean = false;
    baseButtonConfig: ActionBtnEntry[] = [
        {
            key: 'view',
            label: '',
            title: 'View',
            mat_icon: 'search',
        }
    ];

    constructor(private projectService: ProjectService,
                private resourceService: ResourceService,
                public timeUtility: TimeUtility,
                public ngbMomentjsAdapter: NgbMomentjsAdapter,
                private toastService: ToastService,
    ) {
        this.selectedDay = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
    }

    ngOnInit() {
        this.selectedLog = new VehicleCalendarLog();

    }

    ngOnChanges() {
        if (this.project) {
            this.minDate = dayjs(this.project.createdAt).startOf('d');
        }
        this.initializeVehicleLogs();
    }


    moveToNextDay() {
        let currentSelection = this.selectedDay ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay) : dayjs();
        let nextDay = currentSelection.add(1, 'day');
        if (nextDay.valueOf() < this.maxDate.valueOf()) {
            this.selectedDay = this.ngbMomentjsAdapter.dayJsToNgbDate(nextDay);
            this.initializeVehicleLogs();
        }
    }

    moveToPrevDay() {
        let currentSelection = this.selectedDay ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay) : dayjs();
        let prevDay = currentSelection.subtract(1, 'day');
        if (prevDay.valueOf() >= this.minDate.valueOf()) {
            this.selectedDay = this.ngbMomentjsAdapter.dayJsToNgbDate(prevDay);
            this.initializeVehicleLogs();
        }
    }

    initializeVehicleLogs() {
        let dayOfYr = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dbDateFormat);
        console.log('Current value of selectedDay1-', dayOfYr);
        if (!this.isInitVehicleCalender) {
          this.isInitVehicleCalender = true;
          this.vehicleLogsLoading = true;
        } else {
          this.loadingInlineVehicleCalender = true;
        }
        this.projectService.getVehicleTimeLogsByDay(this.projectId, {for_date: dayOfYr}).subscribe((data: any) => {
            this.vehicleLogsLoading = false;
            this.loadingInlineVehicleCalender = false;
            if (data.vehicle_logs) {
                this.vehicleDailyLogs = data.vehicle_logs;
            } else {
                this.vehicleDailyLogs = [];
                const message = data.message || 'Failed to fetch data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });

    }

    unix(n: number| string) {
        return dayjs.unix(+n).format(this.timeFormat);
    };

    getInTime(row): string {
        if (row._still_inside) {
            return this.unix(row._still_inside);
        } else if (row.first_in) {
            return this.unix(row.first_in);
        }
        return '';
    }

    getOutTime(row) {
        if (row._still_inside || !row.last_out) {
            return '';
        }
        return this.unix(row.last_out);
    }

    getTimeFromSeconds(n: number|string) {
        return dayjs().startOf('day').add(dayjs.duration(+n, 'seconds').asSeconds(), 'second').format(this.timeFormat);
    };

    getCo2Emissions(row, co2_emission_gm){
        if(co2_emission_gm === null || co2_emission_gm === undefined){
            return null;
        }
        let a = (row.in?.distance_in_km) ? Number((row.in?.distance_in_km  * (+co2_emission_gm)) / 1000) : null;
        let b = (row.in?.return_distance_km) ? Number((row.in?.return_distance_km  * (+co2_emission_gm)) / 1000) : null;

        return ((a || 0) + (b || 0));
    }

    @ViewChild('viewVehicleModalRef') private viewVehicleModalRef: IModalComponent;
    openTimeLogDetail($event, row) {
        this.enableEditLog = false;
        this.selectedDayDisplayFormat = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(AppConstant.defaultDateFormat);
        this.selectedLog = Object.assign({}, row);
        console.log('selected row is', this.selectedLog);
        if(this.selectedLog.co2_emission_gm !== null && this.selectedLog.co2_emission_gm !== undefined){
            this.selectedLog._has_emission = true;
        }
        this.viewVehicleModalRef.open();
        if((row.events || []).length){
            this.selectedLog._fetching_detail = true;
            forkJoin({
                vehicle_type_response: this.resourceService.getInnDexSettingsByName(['vehicle_type_list']),
                daily_log_response: this.projectService.getVehicleLogsByID(this.projectId, {
                    daily_log_id: row.id,
                    // log_id: (row.events || []).map(e => e.id)
                })
            }).subscribe((responseList) => {
                this.selectedLog._fetching_detail = false;
                let errorKey = Object.keys(responseList).find(key => !responseList[key].success);
                if(responseList[errorKey]){
                    const message = responseList[errorKey].message || 'Failed to get data.';
                    this.toastService.show(this.toastService.types.ERROR, message, { data: responseList[errorKey] });
                    return ;
                }
                this.vehicleTypes = responseList.vehicle_type_response.settings?.vehicle_type_list.types || [];
                this.emissionsByType = responseList.vehicle_type_response.settings?.vehicle_type_list.emissions || [];
                if(this.selectedLog.vehicle_type){
                    let vt = this.vehicleTypes.find(v => v.label === this.selectedLog.vehicle_type);
                    if(vt && vt.has_laden){
                        this.selectedLog._laden_percent_required = true;
                    }
                }
                this.selectedLog._detail_logs = responseList.daily_log_response.logs;
                let time_logs = this.selectedLog._detail_logs || [];
                for (let i=0; i < time_logs.length; i++) {
                    this.selectedLog._detail_logs[i].in.co2_emissions_kg = this.getCo2Emissions(time_logs[i], this.selectedLog.co2_emission_gm)
                }
            });
        }
    }

    trackByRowIndex(index: number, obj: any) {
        return obj.id;
    }

    vehicleTypeSelected($event){
        if(!$event){
            this.selectedLog.vehicle_type = null;
            this.selectedLog.laden_percent = 0;
            return;
        }
        this.selectedLog.vehicle_type = $event.label;
        if($event.has_laden){
            this.selectedLog._laden_percent_required = true;
        }else{
            this.selectedLog._laden_percent_required = false;
            this.selectedLog.laden_percent = 0;
        }
        this.deriveEmissionIfNeeded();
        // console.log($event);
    }

    deriveEmissionIfNeeded(){
        if(!this.selectedLog._has_emission && this.selectedLog.vehicle_type){
            let derived_co2 = this.emissionsByType.find(m => (
                    m.label === this.selectedLog.vehicle_type && (
                        m.laden === this.selectedLog.laden_percent || m.laden === null
                    )
                )
            );
            console.log('emission details', derived_co2);
            if(derived_co2){
                this.selectedLog.co2_emission_gm = derived_co2.co2;

                this.onEmissionGmChange({}, true);
            }
        }
    }

    async bulkDownloadResourcePlan(selection){
        let request = {
            type: 'xls', // needed by download handler
            from_date: this.ngbMomentjsAdapter.ngbDateToDayJs(selection.fromDate).format(AppConstant.apiRequestDateFormat),
            to_date: this.ngbMomentjsAdapter.ngbDateToDayJs(selection.toDate || selection.fromDate).format(AppConstant.apiRequestDateFormat),
        };
        this.processing_loader = true;
        this.projectService.downloadVehicleDailyLogsExport(this.projectId, request, () => {
            this.processing_loader = false;
        });
    }

    enableEditVehicleLog() {
        this.enableEditLog = !(this.enableEditLog);
    }

    isGeoFenceEvent(inEvent) {
        return (inEvent && inEvent.source === 'geo-fence')
    }

    updateVehicleLog() {
        this.processing_loader = true;
        let req = {
            vehicle_detail: {
                id: this.selectedLog.vehicle_ref,
                co2_emission_gm: this.selectedLog.co2_emission_gm,
                vehicle_type: this.selectedLog.vehicle_type,
                laden_percent: this.selectedLog.laden_percent,
                haulage_company: this.selectedLog.haulage_company,
            },
            vehicle_time_log: []
        }

        let time_logs = this.selectedLog._detail_logs || [];
        for (let i=0; i < time_logs.length; i++) {
            let log = time_logs[i];
            if(!this.isGeoFenceEvent(log.in)){
                // we don't allow editing data of non-geo fence events
                continue;
            }

            let distance_matrix = (log.in.distance_matrix || {});
            let distance_info = {
                status: "OK",
                distance: {
                "text": `${log.in.distance_in_km} km`,
                "value": log.in.distance_in_km * 1000
            }};
            distance_matrix = {...distance_matrix, ...distance_info};

            let return_distance_matrix = (log.in.return_distance_matrix || {});
            let return_distance_info = {
                status: "OK",
                distance: {
                "text": `${log.in.return_distance_km} km`,
                "value": log.in.return_distance_km * 1000
            }};
            return_distance_matrix = {...return_distance_matrix, ...return_distance_info};

            req.vehicle_time_log.push({
                id: log.in.id,
                driver_name: log.in.driver_name,
                supplier: log.in.supplier,
                dispatch_postcode: log.in.dispatch_postcode,
                return_postcode: log.in.return_postcode,
                distance_matrix,
                return_distance_matrix,
                notes: log.in.notes,
                event_type: log.in.event_type
            });
        }

        this.projectService.updateVehicleAndLogs(this.projectId, this.selectedLog.id, req).subscribe((data: any) => {
            if (!data.success) {
                const message = `Failed to update the vehicle info, ${data.message}.`;
                this.toastService.show(this.toastService.types.ERROR, message);
            }
            this.enableEditLog = false;
            this.processing_loader = false;
            if(this.selectedLog.co2_emission_gm !== null && this.selectedLog.co2_emission_gm !== undefined){
                this.selectedLog._has_emission = true;
            }
        });
    }

    private deriveDistanceInKm(origin_postcode, cb = (x) => {}){
        this.processing_loader = true;
        let req = {
            origin_postcode: origin_postcode,
            destination_postcode: this.project.postcode,
            project_country_code: (this.project.custom_field && this.project.custom_field.country_code)
        }

        this.projectService.getDistanceBwPostcodes(req).subscribe(data => {
            this.processing_loader = false;
            if (data.success && data.distance_matrix && data.distance_matrix.status == 'OK') {
                let o = {
                    distance_matrix: data.distance_matrix,
                    distance_in_km: (data.distance_in_km) || 0
                };
                cb(o);
            } else {
                const message = 'Failed to fetch distance based on the postcode.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    onChangingPostcode($event, dispatch_postcode, index, is_returning_postcode = false) {
        if (!$event.currentTarget.contains($event.relatedTarget)) {
            this.deriveDistanceInKm(dispatch_postcode, ({distance_matrix, distance_in_km}) => {
                if(is_returning_postcode){
                    this.selectedLog._detail_logs[index].in.return_distance_matrix = distance_matrix;
                    this.selectedLog._detail_logs[index].in.return_distance_km = distance_in_km;
                }else{
                    this.selectedLog._detail_logs[index].in.distance_matrix = distance_matrix;
                    this.selectedLog._detail_logs[index].in.distance_in_km = distance_in_km;
                }
                this.selectedLog._detail_logs[index].in.co2_emissions_kg = this.getCo2Emissions(this.selectedLog._detail_logs[index], this.selectedLog.co2_emission_gm);
            })
        }
    }

    onEmissionGmChange($event, skip = false) {
        if (skip || !$event.currentTarget.contains($event.relatedTarget)) {
            let time_logs = this.selectedLog._detail_logs || [];
            for (let i=0; i < time_logs.length; i++) {
                this.selectedLog._detail_logs[i].in.co2_emissions_kg = this.getCo2Emissions(time_logs[i], this.selectedLog.co2_emission_gm);
            }
        }
    }

    onChangingDistance($event, distance_travelled, co2_emission_gm, index) {
        if (!$event.currentTarget.contains($event.relatedTarget)) {
            this.selectedLog._detail_logs[index].in.co2_emissions_kg = this.getCo2Emissions(this.selectedLog._detail_logs[index], this.selectedLog.co2_emission_gm);
        }
    }

    async timeManagementVehiclesReportDownload(event) {
        await this.bulkDownloadResourcePlan(event.selection);
        event.closeFn();
    }

    public openTimeManagementVehiclesReportModal() {
        this.reportDownloader.openModal();
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actions = {
            'view': () => this.openTimeLogDetail(null, row)
        };

        const action = actions[entry.key];
        if (action) {
            action();
        }
    }
}

<h5 class="float-md-left">Vehicles</h5>
<div class="col-sm-12 my-2">
    <div class="form-group row">
        <label class="col-md-2 col-form-label form-control-label">Choose a Day</label>
        <div class="d-flex justify-content-between flex-wrap flex-column flex-md-row col-md-10">
            <div class="col-md-6 p-0 d-flex align-content-center mb-3">
                <i class="fas fa-angle-double-left fa-2x d-inline-flex cursor-pointer text-muted"
                   (click)="moveToPrevDay()"></i>
                <div class="input-group d-inline-flex col-9 px-1">
                    <input class="form-control" placeholder="dd-mm-yyyy" readonly
                           name="selectedDob" [(ngModel)]="selectedDay" ngbDatepicker
                           [minDate]="ngbMomentjsAdapter.dayJsToNgbDate(minDate)"
                           [maxDate]="ngbMomentjsAdapter.dayJsToNgbDate(maxDate)"
                           #d="ngbDatepicker" ng-value="selectedDay" (dateSelect)="initializeVehicleLogs()">
                    <div class="input-group-append d-block">
                        <button class="btn btn-outline-secondary calendar" (click)="d.toggle()" type="button">
                            <i class="fa fa-calendar"></i>
                        </button>
                    </div>
                </div>
                <i class="fas fa-angle-double-right fa-2x d-inline-flex cursor-pointer text-muted"
                   (click)="moveToNextDay()"></i>
            </div>
            <div>
                <button class="btn btn-sm other-action-btn w-100 btn-brandeis-blue d-flex justify-content-center align-items-center pointer-cursor" (click)="openTimeManagementVehiclesReportModal()" id="dropdownDlReport1">
                    <span class="medium-font m-font-size material-symbols-outlined mr-2">download</span>
                    <div class="medium-font m-font-size">Download Report</div>
                </button>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>

    <block-loader [show]="vehicleLogsLoading" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
    <div class="table-responsive-sm" *ngIf="!vehicleLogsLoading">
        <ngx-datatable
            #vehicleCalendarTable
            [columnMode]="'force'"
            [scrollbarV]="true"
            [virtualization]="false"
            [loadingIndicator]="loadingInlineVehicleCalender"
            [columns]="[
                {name:'Vehicle Reg No', prop: 'registration_no', cellTemplate: regNoTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 text-uppercase', sortable: false, minWidth:'100'},
                {name:'Haulage Company', prop: 'haulage_company', cellTemplate: haulageCompanyTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                {name:'First In Time', prop: 'first_in', cellTemplate: inCellTemplate,  headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                {name:'Last Out Time', prop: 'last_out', cellTemplate: outCellTemplate,  headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                {name:'Total time', prop: 'total_in_sec', cellTemplate: totalInTimeCell,  headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                {name:'View', cellTemplate: ActionBtnCell, sortable: false,  headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', minWidth:'100'}
            ]"
            [footerHeight]="36"
            [headerHeight]="50"
            [rowHeight]="'auto'"
            [rows]="vehicleDailyLogs"
            class="bootstrap table table-hover table-sm expandable ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h">
            <ng-template #regNoTemplate let-value="value">
                <span appTooltip>{{ value }}</span>
            </ng-template>
            <ng-template #haulageCompanyTemplate let-value="value">
                <span appTooltip>{{ value }}</span>
            </ng-template>
            <ng-template #inCellTemplate let-value="value" let-row="row" let-column="column">
                {{ getInTime(row) }}
            </ng-template>
            <ng-template #outCellTemplate let-value="value" let-row="row" let-column="column">
                {{ getOutTime(row) }}
            </ng-template>
            <ng-template #totalInTimeCell let-row="row" let-value="value">
                {{ value && !row._still_inside ? getTimeFromSeconds(value) : '' }}
            </ng-template>
            <ng-template #ActionBtnCell let-row="row" let-value="value">
                <button-group
                    [buttons]="baseButtonConfig"
                    (onActionClick)="rowBtnClicked($event, row)"
                ></button-group>
            </ng-template>
        </ngx-datatable>
    </div>
</div>

<i-modal #viewVehicleModalRef [title]="' Vehicle Details (Day: ' + selectedDayDisplayFormat + ')'" size="full-width" [rightPrimaryBtnTxt]="enableEditLog ? 'Save' : 'Edit'"
         [rightPrimaryBtnDisabled]="viewVForm.invalid"
    cancelBtnText="Close" (onClickRightPB)="enableEditLog ? updateVehicleLog() : enableEditVehicleLog()">
    <form novalidate #viewVForm="ngForm">
        <div class="row">
            <table class="table table-sm table-bordered mt-3 mx-3">
                <tbody>
                <tr>
                    <th class="bg-light" width="30%">Vehicle Reg.</th>
                    <td class="text-uppercase">{{ selectedLog.registration_no }} </td>
                </tr>
                <tr>
                    <th class="bg-light" width="30%">Make</th>
                    <td>{{ selectedLog.make }} </td>
                </tr>
                <tr>
                    <th class="bg-light" width="30%">Fuel</th>
                    <td>{{ selectedLog.fuel_type }} </td>
                </tr>
                <tr>
                    <th class="bg-light" width="30%">Haulage Company</th>
                    <td *ngIf="!enableEditLog">{{ selectedLog.haulage_company }} </td>
                    <td *ngIf="enableEditLog">
                        <input type="text" class="form-control" name="haulage_company"
                               [(ngModel)]="selectedLog.haulage_company">
                    </td>
                </tr>
                <tr>
                    <th class="bg-light" width="30%">Type of Vehicle</th>
                    <td *ngIf="!enableEditLog">{{ selectedLog.vehicle_type || 'N/A' }} </td>
                    <td *ngIf="enableEditLog">
                        <ng-select class="dropdown-list" appendTo="body"
                                   placeholder="Select Vehicle Type" required
                                   [ngModel]="selectedLog.vehicle_type"
                                   name="vehicle_type"
                                   (change)="vehicleTypeSelected($event)"
                                   bindValue="label"
                                   bindLabel="label"
                                   groupBy="heading"
                                   [items]="vehicleTypes">
                            <ng-template ng-optgroup-tmp let-item="item">
                                <b>{{ item.heading }}</b>
                            </ng-template>
                        </ng-select>
                    </td>
                </tr>
                <tr *ngIf="selectedLog.laden_percent || selectedLog._laden_percent_required">
                    <th class="bg-light" width="30%">Laden %</th>
                    <td *ngIf="!enableEditLog">{{ selectedLog.laden_percent }} </td>
                    <td *ngIf="enableEditLog">
                        <ng-select class="dropdown-list" appendTo="body" required
                                   name="laden_percent"
                                   placeholder="Select Laden percent"
                                   (change)="deriveEmissionIfNeeded()"
                                   [(ngModel)]="selectedLog.laden_percent"
                                   [items]="[0, 50, 100]"></ng-select>

                    </td>
                </tr>
                <tr>
                    <th class="bg-light" width="30%">Emissions (g/km)</th>
                    <td *ngIf="!enableEditLog"><ng-container *ngIf="selectedLog.co2_emission_gm !== null">{{ selectedLog.co2_emission_gm }} g/km</ng-container></td>
                    <td *ngIf="enableEditLog">
                        <input type="number" class="form-control co2_emission_gm"
                               name="co2_emission_gm"
                               (blur)="onEmissionGmChange($event)"
                               [(ngModel)]="selectedLog.co2_emission_gm">
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="clearfix"></div>
            <ngx-skeleton-loader class="w-100"
                    *ngIf="selectedLog._fetching_detail" count="5"
                    [theme]="{ 'border-radius': '0', height: '28px', width: '100%' }"
            ></ngx-skeleton-loader>

            <table class="table table-sm border mx-3 small" *ngIf="!selectedLog._fetching_detail">
                <thead class="thead-light">
                <tr>
                    <th class="w-70px">In Time</th>
                    <th class="w-70px">Out Time</th>
                    <th>Total Time</th>
                    <th>Driver</th>
                    <th>Supplier</th>
                    <th>Dispatch Postcode</th>
                    <th>Return Postcode</th>
                    <th>Distance to Site (km)</th>
                    <th>Distance from Site (km)</th>
                    <th>Total Distance (km)</th>
                    <th>CO2 Emissions (kg)</th>
                    <th>Notes</th>
                </tr>
                </thead>
                <tbody *ngIf="selectedLog._detail_logs?.length">
                <ng-container *ngFor="let log of selectedLog._detail_logs; trackBy : trackByRowIndex; let i = index;" >
                    <tr>
                        <td>{{log.in?.event_date_time ? unix(log.in?.event_date_time) : ''}}</td>
                        <td>{{log.out?.event_date_time ? unix(log.out?.event_date_time) : ''}}</td>
                        <td>{{log._seconds ? getTimeFromSeconds(log._seconds) : ''}}</td>
                        <td>
                            <span *ngIf="!enableEditLog else DN">{{log.in?.driver_name}}</span>
                            <ng-template #DN>
                                <input type="text" class="form-control form-control-sm" [disabled]="!isGeoFenceEvent(log.in)"
                                       [name]="'driver_name_'+i"
                                       [(ngModel)]="log.in.driver_name">
                            </ng-template>
                        </td>
                        <td>
                            <span *ngIf="!enableEditLog else supplier">{{log.in?.supplier}}</span>
                            <ng-template #supplier>
                                <input type="text" class="form-control form-control-sm"
                                       [name]="'supplier_'+i"
                                       [disabled]="!isGeoFenceEvent(log.in)"
                                       [(ngModel)]="log.in.supplier">
                            </ng-template>
                        </td>
                        <td>
                            <span *ngIf="!enableEditLog else dispatch_postcode">{{log.in?.dispatch_postcode}}</span>
                            <ng-template #dispatch_postcode>
                                <input type="text" class="form-control form-control-sm dispatch_postcode"
                                       (change)="onChangingPostcode($event, log.in.dispatch_postcode, i, false)"
                                       [disabled]="!isGeoFenceEvent(log.in)"
                                       [name]="'dispatch_postcode_'+i" required
                                       [(ngModel)]="log.in.dispatch_postcode">
                            </ng-template>
                        </td>
                        <td>
                            <span *ngIf="!enableEditLog else return_postcode">{{log.in?.return_postcode}}</span>
                            <ng-template #return_postcode>
                                <input type="text" class="form-control form-control-sm dispatch_postcode"
                                       (change)="onChangingPostcode($event, log.in.return_postcode, i, true)"
                                       [name]="'return_postcode_'+i" required
                                       [disabled]="!isGeoFenceEvent(log.in)"
                                       [(ngModel)]="log.in.return_postcode">
                            </ng-template>
                        </td>
                        <td>
                            <span *ngIf="!enableEditLog else DM">
                                {{(log.in.distance_in_km) ? log.in.distance_in_km : ''}}
                            </span>
                            <ng-template #DM>
                                <input type="text" class="form-control form-control-sm distance_in_km"
                                       (change)="onChangingDistance($event, log.in.distance_in_km, selectedLog.co2_emission_gm, i)"
                                       [name]="'distance_in_km_'+i"
                                       [disabled]="!isGeoFenceEvent(log.in)"
                                       [(ngModel)]="log.in.distance_in_km">
                            </ng-template>
                        </td>
                        <td>
                            <span *ngIf="!enableEditLog else editReturnDM">
                                {{(log.in.return_distance_km) ? log.in.return_distance_km : ''}}
                            </span>
                            <ng-template #editReturnDM>
                                <input type="text" class="form-control form-control-sm return_distance_km"
                                       (change)="onChangingDistance($event, log.in.return_distance_km, selectedLog.co2_emission_gm, i)"
                                       [name]="'return_distance_km_'+i"
                                       [disabled]="!isGeoFenceEvent(log.in)"
                                       [(ngModel)]="log.in.return_distance_km">
                            </ng-template>
                        </td>
                        <td>
                            <span>
                                {{(( +log.in.distance_in_km || 0) + (+log.in.return_distance_km || 0)) | number: '1.0-2'}}
                            </span>
                        </td>
                        <td>{{ log.in.co2_emissions_kg | number: '1.0-2' }}</td>
                        <td>
                            <span *ngIf="!enableEditLog else notes">{{log.in?.notes}}</span>
                            <ng-template #notes>
                                <input type="text" class="form-control form-control-sm"
                                       [name]="'notes_'+i"
                                       [disabled]="!isGeoFenceEvent(log.in)"
                                       [(ngModel)]="log.in.notes">
                            </ng-template>
                        </td>
                    </tr>
                    <tr *ngIf="log.in?.attachments?.length">
                        <td colspan="12">
                            <pop-up-image-viewer [updateStyle]="{'max-height.px': 100, 'max-width.px': 100}" [alt]="'Clock IN Selfie Image'" [imgArray]="log.in?.attachments"></pop-up-image-viewer>
                            <pop-up-image-viewer [updateStyle]="{'max-height.px': 100, 'max-width.%px': 100}" [alt]="'Clock OUT Selfie Image'" [imgArray]="log.out?.attachments"></pop-up-image-viewer>
                        </td>
                    </tr>
                </ng-container>
                </tbody>
            </table>
        </div>
    </form>
</i-modal>
<block-loader [show]="processing_loader" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>
<report-downloader #reportDownloader
    [xlsxOnly]="true"
    (onFilterSelection)="timeManagementVehiclesReportDownload($event)"
    >
</report-downloader>

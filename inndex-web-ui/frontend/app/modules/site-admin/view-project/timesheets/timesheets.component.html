<block-loader [show]="viewInfo.iRLoading" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
<div>
    <h5 class="d-block" [class.font-20]="isMobile">Weekly Timesheets</h5>

    <div class="filters d-md-block">
        <action-button
            class="float-md-right mb-3"
            [actionList]="actionsList"
            (selectedActionEmmiter)="onActionSelection($event)"
            [hideNewFeatureBtn]="true">
        </action-button>
        <week-date-selector
            [minDate]="viewInfo.minDate"
            class="float-md-right mr-2 mb-3 d-inline-block"
            [isMobile]="isMobile"
            [selectedDay]="viewInfo.selectedDay"
            [disabled]="viewHasChanges()"
            [weekEndsOn]="weekEndsOn"
            (onWeekSelection)="onRangeSelection($event)"
        ></week-date-selector>
        <search-with-filters
            readonly="(viewHasChanges())"
            (searchEmitter)="onSearch($event, true)"
            (filterEmitter)="onFilterSelection($event, true)"
            [filterData]="filterData"
            [loading]="viewInfo.iRLoading"
        ></search-with-filters>
    </div>

    <form #timesheetForm="ngForm" class="" novalidate>
        <span class="material-symbols-outlined mr-1 font-small scroll-icon" *ngIf="isMobile && viewInfo.inductionsPage.records.length && showScroller(true)" (click)="scrollTable($event, false)">chevron_right</span>
        <ng-container *ngIf="!viewInfo.iRLoading">
        <ngx-datatable
            [scrollbarV]="true"
            [virtualization]="false"
            #pTimesheetTable
            [columnMode]="'force'"
            [footerHeight]="viewHasChanges()? 0 : 40"
            [headerHeight]="40"
            [rowHeight]="'auto'"
            [scrollbarH]="isMobile"
            [externalPaging]="true"
            [externalSorting]="true"
            [loadingIndicator]="viewInfo.daysLoading || loadingInlineTimesheet || savingChanges"
            [count]="viewInfo.inductionsPage.totalCount"
            [offset]="viewInfo.inductionsPage.pageNumber"
            [limit]="viewInfo.inductionsPage.pageSize"
            (page)="pageCallback($event, true)"
            [rowClass]="getRowClassFn.bind(this)"
            [sortType]="tableSortType"
            (sort)="sortCallback($event, true)"
            [rows]="viewInfo.inductionsPage.records"
            class="bootstrap table ngx-datatable table-v2 ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h">
            <ngx-datatable-column name="Worker" headerClass="font-weight-bold pl-1" cellClass="p-1" prop="name" [frozenLeft]="isMobile">
                <ng-template let-column="column" ngx-datatable-header-template>
                    <div class="d-inline-block" style="margin-bottom: 21px;">Worker</div>
                    <span class="material-symbols-outlined mr-1 font-small scroll-icon" (click)="scrollTable($event, true)" *ngIf="showScroller()">chevron_left</span>
                </ng-template>
                <ng-template let-row="row" let-value="value" ngx-datatable-cell-template>
                    <div style="line-height: 18px;">
                        <span appTooltip class="cursor-pointer" (click)="viewTimesheet(row)">{{row.first_name}} {{row.last_name}}</span>
                        <small class="text-muted">(ID: {{row.user_ref}})</small>
                    </div>
                </ng-template>
            </ngx-datatable-column>

            <ngx-datatable-column name="Job Role" headerClass="font-weight-bold pl-1" cellClass="px-1 py-2" prop="job_role" [sortable]="false" *ngIf="!isMobile">
                <ng-template let-row="row" let-value="value" ngx-datatable-cell-template>
                    <div appTooltip style="line-height: 18px;">{{ row.job_role }}</div>
                </ng-template>
            </ngx-datatable-column>

            <ngx-datatable-column name="Company" headerClass="font-weight-bold pl-1" cellClass="px-1 py-2" prop="employer_name" [sortable]="false" *ngIf="!isMobile">
                <ng-template let-row="row" let-value="value" ngx-datatable-cell-template>
                    <div style="line-height: 18px;">
                        <span appTooltip>{{ row.employer_name }}</span> <small class="text-muted" *ngIf="row.employment_company"><span appTooltip>({{row.employment_company}})</span></small>
                    </div>
                </ng-template>
            </ngx-datatable-column>


            <ng-container *ngFor="let w of viewInfo.weekDays">
                <ngx-datatable-column
                    [resizeable]="false"
                    headerClass="font-weight-bold input-col pl-0"
                    cellClass="px-0 py-2 input-col"
                    [sortable]="false"

                    [prop]="'_weeks.'+w.day_of_yr">
                    <ng-template let-column="column" ngx-datatable-header-template>
                        {{w.label}} <br />
                        <small class="text-muted font-weight-normal">{{w.sub_label}}</small>
                    </ng-template>
                    <ng-template let-row="row" let-value="value" ngx-datatable-cell-template>
                        <time-input
                            [disabled]="viewInfo.daysLoading || value._input_disable"
                            [allowStateChange]="!value._input_disable"
                            [input]="value._input"
                            [allowShiftChange]="true"
                            [allowZero]="true"
                            (openSplitInput)="openSplitShiftInput($event, row, w.day_of_yr, value)"
                            [placeholder]="value.placeholder"
                            (changed)="timeInputChanged($event, row.user_ref, w.day_of_yr, value)"
                        ></time-input>
                    </ng-template>
                </ngx-datatable-column>
            </ng-container>

            <ngx-datatable-column
                name=""
                headerClass="font-weight-bold action-btns px-1"
                cellClass="px-1 py-2 action-btns"
                [sortable]="false"
                [resizeable]="false"
                [maxWidth]="200">
                <ng-template let-column="column" ngx-datatable-header-template>
                    <div *ngIf="bulkAction.active">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input"
                                   (change)="selectAllCheckbox($event)"
                                   [id]="'approve-all-yes'"/>
                            <label class="custom-control-label size-lg success s-all pl-2" [for]="'approve-all-yes'"></label>
                        </div>
                        <!--<div class="d-inline-block" style="vertical-align: -webkit-baseline-middle;"></div>-->
                    </div>

                </ng-template>
                <ng-template let-row="row" let-value="value" ngx-datatable-cell-template>
                    <div *ngIf="bulkAction.active">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input"
                                   [disabled]="rowApproveNotAllowed(row)"
                                   (change)="toggleApproval($event, row.user_ref)"
                                   [checked]="bulkAction.approve.includes(row.user_ref) || (row._all_ts_approved)"
                                   [name]="'approve-'+row.user_ref+'-yes'" [id]="'approve-'+row.user_ref+'-yes'"/>
                            <label class="custom-control-label size-lg success" [for]="'approve-'+row.user_ref+'-yes'"></label>
                        </div>
                    </div>
                    <div style="" *ngIf="!bulkAction.active">
                        <button (click)="view($event, row)" title="View" type="button" class="btn btn-action btn-sm p-1 mr-2">
                            <span class="material-symbols-outlined x-large-font align-middle"> search </span>
                        </button>
                        <button class="btn btn-action btn-sm p-1 mr-2" (click)="bulkAmend(row)" [disabled]="(row._all_ts_approved)">
                            <span class="material-symbols-outlined x-large-font align-middle"> edit_note </span>
                        </button>
                        <button class="btn btn-success btn-sm mr-2" *ngIf="!row._all_ts_approved" (click)="approveTimesheet($event, row)" [disabled]="rowApproveNotAllowed(row)"> 
                            Approve 
                        </button>
                        <button class="btn btn-grey btn-sm mr-2" *ngIf="row._all_ts_approved" (click)="unApproveTimesheet($event, row)">Un-Approve</button>
                    </div>
                </ng-template>
            </ngx-datatable-column>
        </ngx-datatable>
        </ng-container>
        <view-timesheet
            *ngIf="viewInfo.start"
            [projectId]="projectId"
            [tz]="project?.custom_field?.timezone"
            [currencyCode]="project.custom_field.currency_code"
            [weekDays]="viewInfo.weekDays"
            [isMobile]="isMobile"
            [from]="viewInfo.start.format(apiRequestDateFormat)"
            [to]="viewInfo.end.format(apiRequestDateFormat)"
            [timesheet_shift_configs]="timesheetShiftConfig"
            (changed)="loadPage(viewInfo.inductionsPage.pageNumber)"
            #viewTimesheetComponent>
        </view-timesheet>
        <view-timesheet
            *ngIf="viewInfo.start"
            [projectId]="projectId"
            [tz]="project?.custom_field?.timezone"
            [currencyCode]="project.custom_field.currency_code"
            [weekDays]="viewInfo.weekDays"
            [isMobile]="isMobile"
            [from]="viewInfo.start.format(apiRequestDateFormat)"
            [to]="viewInfo.end.format(apiRequestDateFormat)"
            [records]="viewInfo.inductionsPage.records"
            (changed)="loadPage(viewInfo.inductionsPage.pageNumber)"
            #viewWorkerRecord>
        </view-timesheet>
    </form>
    <div *ngIf="viewHasChanges()">
        <div style="padding-bottom: 30px;"></div>
        <div class="footer-controls fixed-bottom" [class.ml-0]="isMobile" [class.px-3]="isMobile">
            <ng-container *ngIf="!bulkAction.active">
                <div class="d-inline-block"> {{ viewInfo.totalChanges }} Unsaved change{{viewInfo.totalChanges > 1 ? 's': ''}} </div>
                <button class="btn btn-brandeis-blue float-right py-1" [disabled]="invalidRowUsers.length > 0" (click)="saveTimesheetChanges()"> Save Change{{viewInfo.totalChanges > 1 ? 's': ''}}</button>
            </ng-container>
            <ng-container *ngIf="bulkAction.active">
                <div class="d-inline-block" *ngIf="viewInfo.inductionsPage.totalCount"> Timesheets selected: {{ bulkAction.approve.length }}/{{viewInfo.inductionsPage.totalCount}}</div>
                <button class="btn btn-brandeis-blue float-right py-1" [disabled]="!bulkAction.approve.length" (click)="beforeBulkApprove()"> Approve</button>
            </ng-container>

            <button class="btn btn-link float-right py-1 mr-3" (click)="clearViewChanges()"> Cancel</button>
        </div>
    </div>
</div>

<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

<amend-timesheet-modal
    #amendTsModal
    [weekDays]="viewInfo.weekDays"
    (selection)="amendUserTs($event)"
></amend-timesheet-modal>

<i-modal #downloadTimesheetFilter
         [size]="'lg'"
         [modalBodyClass]="'p-5 modal-padding'"
         [title]="'Download Timesheets'"
         (onClickRightPB)="downloadTimesheets($event)"
         [rightPrimaryBtnDisabled]="disableTimesheetDownload"
         [rightPrimaryBtnTxt]="'Download'">
    <form #timesheetExportForm="ngForm" class="" novalidate>
        <div class="form-group"><span class="material-symbols-outlined align-middle">info </span> Only approved timesheets are included in the download</div>
        <div class="form-group">
            <label class="d-inline-block">Filter records by company</label>
            <ng-select class="dropdown-list" appendTo="body" [items]="inductedEmployers"
                       [(ngModel)]="downloadFilters.employers"
                       (change)="onMetaFilterChange($event)"
                       [multiple]="true" name="company" placeholder="Company">
            </ng-select>
        </div>
        <div class="form-group">
            <label class="d-inline-block">Filter records by job role</label>
            <ng-select class="dropdown-list" appendTo="body" [items]="inductedJobRoles"
                       [(ngModel)]="downloadFilters.jobRoles"
                       (change)="onMetaFilterChange($event)"
                       [multiple]="true" name="jobRole" placeholder="Job Role">
            </ng-select>
        </div>
        <div class="form-group">
            <label class="d-inline-block">Type of employment</label>
            <ng-select class="dropdown-list" appendTo="body" [items]="typeOfEmployments"
                       [(ngModel)]="downloadFilters.typeOfEmployments"
                       (change)="onMetaFilterChange($event)"
                       bindLabel="name" [multiple]="true" name="employment" placeholder="Type Of Employment">
            </ng-select>
        </div>
        <div class="form-group">
            <label class="d-inline-block">Filter records by user</label>
            <ng-select class="dropdown-list" appendTo="body" [items]="inductedUsersSubSet" bindValue="user_ref"
                       [(ngModel)]="downloadFilters.userIds"
                       bindLabel="name" [multiple]="true" name="user" placeholder="User">
            </ng-select>
        </div>
        <ngb-datepicker #dp class="custom-weekday-v2"
                        [minDate]="ngbMomentjsAdapter.dayJsToNgbDate(viewInfo.minDate)"
                        [maxDate]="ngbMomentjsAdapter.dayJsToNgbDate(viewInfo.maxDate)"
                        [displayMonths]="2" [dayTemplate]="dayTemplate" outsideDays="hidden"
                        (dateSelect)="onDateSelection($event)"
                        [dayTemplateData]="data">
        </ngb-datepicker>

        <ng-template #dayTemplate let-date let-data="data" let-focused="focused">
            <span class="custom-day custom-day-v2"
                  [ngClass]="data"
                  [class.focused]="focused"
                  [class.range]="isRange(date)"
                  [class.weekend]="isRange(date) && isWeekend(date)"
                  [class.faded]="isInside(date)"
                  [class.disabled]="isAboveMaxRange(date)"
                  (mouseenter)="downloadFilters.hoveredDate = date"
                  (mouseleave)="downloadFilters.hoveredDate = null">
                {{ date.day }}
            </span>
        </ng-template>
    </form>
</i-modal>

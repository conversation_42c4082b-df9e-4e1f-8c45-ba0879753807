<block-loader [show]="loading" [showBlockBackdrop]="true" [alwaysInCenter]="true"></block-loader>
<i-modal
    #messageDetailsHtml
    [title]="record?.name"
    titleClass="fw-400"
    size="xl"
    [leftPrimaryBtnTxt]="modalInfo.groups?.approved_ts.length ? 'Un-approve': null"
    leftPrimaryBtnClass="btn-outline-brandeis-blue"
    [leftPrimaryBtnDisabled]="!modalInfo.groups?.approved_ts.length"
    (onClickLeftPB)="onUnApproveFn($event)"
    rightPrimaryBtnTxt="Done"
    [rightPrimaryBtnDisabled]="!modalInfo.groups?.valid && !is_config_changed"
    (onClickRightPB)="onSaveFn($event)"
    [showCancel]="true"
    (onCancel)="myCancelFn($event)">
    <ng-container sub-title>
        <!-- This would be modal sub-title within `<h6> title` -->
        <span class="text-muted">({{record?.user_ref}})</span>
    </ng-container>
    <div *ngIf="!loading && modalInfo.ready">
        <!-- This would be modal body -->
        <block-loader [show]="processing" [showBlockBackdrop]="true" [alwaysInCenter]="true"></block-loader>

        <!-- start: shift-config-section -->
        <div *ngIf="this.timesheet_shift_configs.length > 0 && timesheet_status === 1" class="mb-4">
            <div>
                <p class="mb-1 font-weight-bold x-large-font fw-500">Timesheet shift config</p>
            </div>
            <div>
                <ng-select
                        required
                        [clearable]="false"
                        class="mt-1"
                        [items]="timesheet_shift_configs"
                        [multiple]="false"
                        bindLabel="configuration_title"
                        placeholder="Select shift config"
                        name="shift_config"
                        #shiftConfig="ngModel"
                        [(ngModel)]="shift_config"
                        (change)="applyShiftConfig(shift_config.id)"
                >
                </ng-select>
            </div>
        </div>
        <!-- end: shift-config-section -->

        <!-- start: timesheet-section -->
        <div>
            <p class="mb-1 font-weight-bold x-large-font fw-500">Timesheet</p>
        </div>
        <div [class.table-wrapper]="isMobile">
            <table class="table table-outer-border mb-4">
                <thead>
                <tr class="">
                    <th class="table-header-bg w-12-per" scope="col"> </th>
                    <ng-container *ngFor="let w of weekDays">
                        <th class="table-header-bg" scope="col">
                            {{w.label}}
                        </th>
                    </ng-container>
                    <th class="table-header-bg" style="width: 84px;" scope="col">
                        Total
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <th class="table-td-border align-middle table-header-bg small-font" scope="row">
                        Date
                    </th>
                    <ng-container *ngFor="let w of weekDays">
                        <td class="table-td-border align-middle py-0">
                            <span>{{ viewWeek(w.day_of_yr) }}</span>
                        </td>
                    </ng-container>
                    <td class="table-td-border align-middle py-0">
                        <span class="line-h"> </span>
                    </td>
                </tr>
                <tr>
                    <th class="table-td-border align-middle table-header-bg small-font" scope="row">
                        Actual Shift
                    </th>
                    <ng-container *ngFor="let w of weekDays">
                        <td class="table-td-border align-middle py-0">
                            <span *ngIf="modalInfo.groups.actual[w.day_of_yr].day_of_yr; else actualShiftNotAvailable;">{{ displayUnixTime(modalInfo.groups.actual[w.day_of_yr].clock_in) }} - {{ displayUnixTime(modalInfo.groups.actual[w.day_of_yr].clock_out)}}</span>
                            <ng-template #actualShiftNotAvailable> N/A </ng-template>
                        </td>
                    </ng-container>
                    <td class="table-td-border align-middle py-0">
                        <span class="line-h"> </span>
                    </td>
                </tr>
                <tr>
                    <th class="table-td-border align-middle table-header-bg small-font" scope="row">
                        Actual Hours
                    </th>
                    <ng-container *ngFor="let w of weekDays">
                        <td class="table-td-border align-middle py-0">
                            <time-input
                                [disabled]="true"
                                [allowStateChange]="false"
                                [previewMode]="true"
                                [input]="{state: hoursState, val: modalInfo.groups.actual[w.day_of_yr].effective_time || null}"
                                [placeholder]="'N/A'"
                            ></time-input>
                        </td>
                    </ng-container>
                    <td class="align-middle py-0">
                        <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.effective_time) }}</span>
                    </td>
                </tr>
                <tr>
                    <th class="table-td-border align-middle table-header-bg small-font" scope="row">
                        Adjusted Hours (Day)
                    </th>
                    <ng-container *ngFor="let w of weekDays">
                        <td class="table-td-border align-middle py-0 p-x-3px">
                            <time-input
                                [disabled]="modalInfo.groups.ts[w.day_of_yr].disabled"
                                [allowStateChange]="true"
                                [previewMode]="modalInfo.groups.ts[w.day_of_yr].disabled"
                                [input]="{
                                state: ((![TIME_INPUT_STATE.SPLIT_SHIFT.v, TIME_INPUT_STATE.NIGHT_HOURS.v].includes(modalInfo.groups.ts[w.day_of_yr].hours_state) ? modalInfo.groups.ts[w.day_of_yr].hours_state: TIME_INPUT_STATE.DAY_HOURS.v) || TIME_INPUT_STATE.DAY_HOURS.v),
                                val: ([TIME_INPUT_STATE.DAY_HOURS.v, TIME_INPUT_STATE.SPLIT_SHIFT.v].includes(modalInfo.groups.ts[w.day_of_yr].hours_state) ? (modalInfo.groups.ts[w.day_of_yr].day_seconds) : 0),
                                changed: !!modalInfo.groups.ts[w.day_of_yr].updated.day_seconds
                                }"
                                [modalStyles]="true"
                                previewClasses="p-x-4px"
                                [stickyShift]="TIME_INPUT_STATE.DAY_HOURS.v"
                                [allowZero]="true"
                                [allowShiftChange]="false"
                                [placeholder]="modalInfo.groups.ts[w.day_of_yr].disabled ? 'N/A' : '--.--'"
                                (changed)="timeInputChanged($event, w.day_of_yr, 'day_seconds')"
                            ></time-input>
                        </td>
                    </ng-container>
                    <td class="align-middle py-0">
                        <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.day_seconds) }}</span>
                    </td>
                </tr>

                <tr>
                    <th class="table-td-border align-middle table-header-bg small-font" scope="row">
                        Adjusted Hours (Night)
                    </th>
                    <ng-container *ngFor="let w of weekDays">
                        <td class="table-td-border align-middle py-0 p-x-3px">
                            <time-input
                                [disabled]="modalInfo.groups.ts[w.day_of_yr].disabled"
                                [allowStateChange]="true"
                                [previewMode]="modalInfo.groups.ts[w.day_of_yr].disabled"
                                [input]="{
                                state: ((![TIME_INPUT_STATE.SPLIT_SHIFT.v, TIME_INPUT_STATE.DAY_HOURS.v].includes(modalInfo.groups.ts[w.day_of_yr].hours_state) ? modalInfo.groups.ts[w.day_of_yr].hours_state : TIME_INPUT_STATE.NIGHT_HOURS.v) || TIME_INPUT_STATE.NIGHT_HOURS.v),
                                val: ([TIME_INPUT_STATE.NIGHT_HOURS.v, TIME_INPUT_STATE.SPLIT_SHIFT.v].includes(modalInfo.groups.ts[w.day_of_yr].hours_state) ? (modalInfo.groups.ts[w.day_of_yr].night_seconds) : 0),
                                changed: !!modalInfo.groups.ts[w.day_of_yr].updated.night_seconds
                                }"
                                [modalStyles]="true"
                                previewClasses="p-x-4px"
                                [stickyShift]="TIME_INPUT_STATE.NIGHT_HOURS.v"
                                [allowZero]="true"
                                [allowShiftChange]="false"
                                [placeholder]="modalInfo.groups.ts[w.day_of_yr].disabled ? 'N/A' : '--.--'"
                                (changed)="timeInputChanged($event, w.day_of_yr, 'night_seconds')"
                            ></time-input>
                        </td>
                    </ng-container>
                    <td class="align-middle py-0">
                        <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.night_seconds) }}</span>
                    </td>
                </tr>


                <tr>
                    <th class="table-td-border align-middle table-header-bg small-font" scope="row">
                        Total Adjusted Hours
                    </th>
                    <ng-container *ngFor="let w of weekDays">
                        <td class="text-muted align-middle py-0">
                            <span class="line-h">{{ timeUtility.secondsToHuman((modalInfo.groups.ts[w.day_of_yr].day_seconds || 0) + modalInfo.groups.ts[w.day_of_yr].night_seconds || 0) }}</span>
                        </td>
                    </ng-container>
                    <td class="align-middle py-0">
                        <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.day_seconds + modalInfo.groups.total.night_seconds) }}</span>
                    </td>
                </tr>


                <ng-template #addHourRow let-w="week" let-key="key" let-initial="initial">
                    <td class="table-td-border align-middle timesheet-td-v-align py-0"
                        *ngIf="(disableInput(w.day_of_yr)); else addHourElsePart">
                        <span class="fw-500 text-muted"> - </span>
                    </td>
                    <ng-template #addHourElsePart>
                        <td class="table-td-border align-middle timesheet-td-v-align py-0" [class.text-center]="(!modalInfo.groups.ts[w.day_of_yr].disabled)">
                            <ng-container *ngIf="(((modalInfo.groups.ts[w.day_of_yr].disabled) && !initial)); else elseHoursInput">
                                <span class="fw-500 text-muted"> - </span>
                            </ng-container>
                            <ng-template #elseHoursInput>
                                <span [hidden]="initial" (click)="btn.hidden = true" #btn class="add-btn-td brandis-blue cursor-pointer fw-500">+ Add</span>
                                <time-input
                                    class="wide-input"
                                    *ngIf="btn?.hidden"
                                    [disabled]="modalInfo.groups.ts[w.day_of_yr].disabled"
                                    [allowStateChange]="false"
                                    [previewEditing]="!initial || modalInfo.groups.ts[w.day_of_yr].updated[key]"
                                    [previewMode]="true"
                                    previewClasses="p-x-4px"
                                    [allowZero]="true"
                                    [modalStyles]="true"
                                    [input]="{state: hoursState, val: initial}"
                                    [placeholder]="'--.--'"
                                    (changed)="timeInputChanged($event, w.day_of_yr, key)"
                                ></time-input>
                            </ng-template>
                        </td>
                    </ng-template>
                </ng-template>
                <tr>
                    <th class="table-td-border align-middle table-header-bg small-font" scope="row">
                        Overtime Hours
                    </th>
                    <ng-container *ngFor="let w of weekDays">
                        <ng-container *ngTemplateOutlet="addHourRow; context: {$implicit: w, week: w, key: 'overtime_seconds', initial: modalInfo.groups.ts[w.day_of_yr].overtime_seconds || null}"></ng-container>
                    </ng-container>
                    <td class="align-middle py-0">
                        <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.overtime_seconds) }}</span>
                    </td>
                </tr>
                <tr>
                    <th class="table-td-border align-middle table-header-bg small-font" scope="row">
                        Early Overtime Hours
                    </th>
                    <ng-container *ngFor="let w of weekDays">
                        <ng-container *ngTemplateOutlet="addHourRow; context: {$implicit: w, week: w, key: 'early_overtime_seconds', initial: modalInfo.groups.ts[w.day_of_yr].early_overtime_seconds || null}"></ng-container>
                    </ng-container>
                    <td class="align-middle py-0">
                        <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.early_overtime_seconds) }}</span>
                    </td>
                </tr>
                <tr>
                    <th class="table-td-border align-middle table-header-bg small-font" scope="row">
                        Late Overtime Hours
                    </th>
                    <ng-container *ngFor="let w of weekDays">
                        <ng-container *ngTemplateOutlet="addHourRow; context: {$implicit: w, week: w, key: 'late_overtime_seconds', initial: modalInfo.groups.ts[w.day_of_yr].late_overtime_seconds || null}"></ng-container>
                    </ng-container>
                    <td class="align-middle py-0">
                        <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.late_overtime_seconds) }}</span>
                    </td>
                </tr>
                <tr>
                    <th class="table-td-border align-middle table-header-bg small-font" scope="row">
                        Driving Hours
                    </th>
                    <ng-container *ngFor="let w of weekDays">
                        <ng-container *ngTemplateOutlet="addHourRow; context: {$implicit: w, week: w, key: 'travel_seconds', initial: modalInfo.groups.ts[w.day_of_yr].travel_seconds || null}"></ng-container>
                    </ng-container>
                    <td class="align-middle py-0">
                        <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.travel_seconds) }}</span>
                    </td>
                </tr>
                <tr>
                    <th class="table-td-border align-middle table-header-bg small-font" scope="row">
                        Training Hours
                    </th>
                    <ng-container *ngFor="let w of weekDays">
                        <ng-container *ngTemplateOutlet="addHourRow; context: {$implicit: w, week: w, key: 'training_seconds', initial: modalInfo.groups.ts[w.day_of_yr].training_seconds || null}"></ng-container>
                    </ng-container>
                    <td class="align-middle py-0">
                        <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.training_seconds) }}</span>
                    </td>
                </tr>
                <tr>
                    <th class="table-td-border align-middle table-header-bg small-font" scope="row">
                        Manager Auth
                    </th>
                    <ng-container *ngFor="let w of weekDays">
                        <ng-container *ngTemplateOutlet="addHourRow; context: {$implicit: w, week: w, key: 'manager_auth_seconds', initial: modalInfo.groups.ts[w.day_of_yr].manager_auth_seconds || null}"></ng-container>
                    </ng-container>
                    <td class="align-middle py-0">
                        <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.manager_auth_seconds) }}</span>
                    </td>
                </tr>
                <tr>
                    <th class="table-td-border align-middle table-header-bg small-font" scope="row">
                        Price Work ({{currencySymbol}})
                    </th>
                    <ng-container *ngFor="let w of weekDays">
                        <td class="table-td-border align-middle timesheet-td-v-align py-0"
                            *ngIf="(disableInput(w.day_of_yr)); else addHourElsePart">
                            <span class="fw-500 text-muted"> - </span>
                        </td>
                        <ng-template #addHourElsePart>
                            <td class="table-td-border align-middle timesheet-td-v-align py-0">
                                <ng-container *ngIf="((modalInfo.groups.ts[w.day_of_yr].disabled) && !modalInfo.groups.ts[w.day_of_yr].price_work_amount); else elseHoursInput">
                                    <span class="fw-500 text-muted"> - </span>
                                </ng-container>
                                <ng-template #elseHoursInput>
                                    <span [hidden]="modalInfo.groups.ts[w.day_of_yr].price_work_amount" (click)="btn.hidden = true" #btn
                                          class="add-btn-td brandis-blue cursor-pointer fw-500 text-center"><span>+ Add</span></span>
                                    <currency-input
                                        *ngIf="btn?.hidden"
                                        class="wide-input"
                                        [currencyCode]="currencyCode"
                                        [initial]="modalInfo.groups.ts[w.day_of_yr].price_work_amount || null"
                                        [preview]="modalInfo.groups.ts[w.day_of_yr].disabled"
                                        [disabled]="modalInfo.groups.ts[w.day_of_yr].disabled"
                                        (changed)="timeInputChanged($event, w.day_of_yr, 'price_work_amount')"
                                    ></currency-input>
                                </ng-template>
                            </td>
                        </ng-template>
                    </ng-container>
                    <td class="align-middle">{{ (modalInfo.groups.total.price_work_amount) | currency: currencyCode}}</td>
                </tr>
                </tbody>
            </table>
        </div>
        <!-- end: timesheet-section -->

        <!-- start: activity-log-section -->
        <ng-container *ngIf="false">
            <div>
                <p class="mb-1 font-weight-bold x-large-font fw-500">Activity Log</p>
            </div>
            <table class="table table-outer-border mb-4">
                <thead>
                <tr class="table-header-bg">
                    <th class="date-col-w py-2 table-th-border border-bottom-0 border-top-0" scope="col">
                        Date Changed
                    </th>
                    <th class="comments-by-col-w py-2 table-th-border border-bottom-0 border-top-0" scope="col">
                        Changed By
                    </th>
                    <th class="comments-by-col-w py-2 table-th-border border-bottom-0 border-top-0" scope="col">
                        Changed From
                    </th>
                    <th class="border-bottom-0 border-top-0 py-2" scope="col">Changed To</th>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let activityLog of activityLogData">
                    <td class="table-td-border py-2">{{dayjsDisplayDateOrTime(+activityLog.timestamp)}}
                        <small class="text-muted d-block">({{dayjsDisplayDateOrTime(+activityLog.timestamp, false)}})</small>
                    </td>
                    <td class="table-td-border py-2">{{activityLog.changedBy}}</td>
                    <td class="table-td-border py-2">{{activityLog.changedFrom}}</td>
                    <td class="py-2">{{activityLog.changedTo}}</td>
                </tr>
                </tbody>
            </table>
        </ng-container>
        <!-- end: activity-log-section -->

        <!-- start: comment-section -->
        <div>
            <p class="mb-1 font-weight-bold x-large-font fw-500">Comments</p>
        </div>
        <div>
            <textarea
                name="comment"
                [(ngModel)]="commentNote"
                maxlength="1000"
                placeholder="Provide comments regarding the operative's timesheet here"
                #comment class="form-control"
            ></textarea>
        </div>
        <div class="d-flex">
            <button
                type="button"
                class="btn btn-outline-brandeis-blue float-left medium-font mt-2 mb-3 py-1"
                (click)="saveTimesheet()"
                [disabled]="!commentNote">
                <span>+</span> Add
            </button>
        </div>

        <table class="table table-outer-border">
            <thead>
            <tr class="table-header-bg">
                <th class="table-th-border border-bottom-0 border-top-0 py-2" [class.date-col-w]="comments.length" scope="col">Date</th>
                <th class="table-th-border border-bottom-0 border-top-0 py-2" [class.comments-by-col-w]="comments.length" scope="col">Author</th>
                <th class="border-bottom-0 border-top-0 py-2" [class.details-col-w]="comments.length" scope="col">Comments</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let comment of comments">
                <td class="table-td-border py-2">
                    {{ dayjsDisplayDateOrTime(+comment.timestamp) }}
                    <small class="text-muted d-block">({{ dayjsDisplayDateOrTime(+comment.timestamp, false) }})</small>
                </td>
                <td class="table-td-border py-2">{{comment.name}}</td>
                <td class="py-2">{{comment.note}}</td>
            </tr>
            <tr *ngIf="!comments.length">
                <td colspan="3" class="text-center text-muted">No comments to display yet.</td>
            </tr>
            </tbody>
        </table>
        <!-- end: comment-section -->
    </div>
    <ng-container m-controls>
        <!-- This would be rendered within footer, additional to buttons if any -->
        <!-- e.g. custom <ng-select> </ng-select> -->
    </ng-container>
</i-modal>
<!-- [rightPrimaryBtnDisabled]="!workerForm.valid" -->
<i-modal
    #viewWorkerRecordHtml
    [title]="'test-123'"
    titleClass="fw-400"
    size="xl"
    [leftPrimaryBtnDisabled]="false"
    (onClickLeftPB)="''"
    rightPrimaryBtnTxt="Done"
    (onClickRightPB)="''"
    [showCancel]="true"
    [cancelBtnText]="'Close'"
    (onCancel)="''"
    (onClickRightPB)="saveWorkerRecord()">
    <div class="w-100">
        <block-loader [show]="processing" [showBlockBackdrop]="true" [alwaysInCenter]="true"></block-loader>
        <form #workerForm="ngForm">
            <div class="form-group">
                <ng-select class="w-100 dropdown-list" appendTo="body" [items]="records" bindLabel="name" bindValue="user_ref"
                    name="user" placeholder="Select User" [(ngModel)]="selectedUser" #user="ngModel" 
                    (change)="onUserChange($event)" required>
                    <ng-template ng-option-tmp let-item="item">
                        {{item.name}} ({{item.user_ref}})
                    </ng-template>
                </ng-select>
            </div>

            <ng-container *ngIf="selectedUser">
                <div class="form-group">
                    <input [(ngModel)]="employeeWorkerNumber" (ngModelChange)="onEmployeeWorkerNumberChange($event)"
                        #employeeWorkerNumberModel="ngModel" class="form-control" name="employeeWorkerNumber"
                        placeholder="Employee/Worker Number" type="text"
                        [class.is-invalid]="employeeWorkerNumberModel.invalid && (employeeWorkerNumberModel.dirty || employeeWorkerNumberModel.touched)"
                        required />
                    <div *ngIf="employeeWorkerNumberModel.invalid && (employeeWorkerNumberModel.dirty || employeeWorkerNumberModel.touched)"
                        class="invalid-feedback">
                        <div *ngIf="employeeWorkerNumberModel.errors?.required">Employee/Worker Number is required</div>
                    </div>
                </div>
    
                <div class="form-group">
                    <input [(ngModel)]="hourlyRate" (ngModelChange)="onHourlyRateChange($event)" #hourlyRateModel="ngModel"
                        class="form-control" name="hourlyRate" placeholder="Hourly Rate" type="number" min="0" step="0.01"
                        [class.is-invalid]="hourlyRateModel.invalid && (hourlyRateModel.dirty || hourlyRateModel.touched)" 
                        pattern="^\d*\.?\d{0,2}$" required />
                    <div *ngIf="hourlyRateModel.invalid && (hourlyRateModel.dirty || hourlyRateModel.touched)" class="invalid-feedback">
                        <div *ngIf="hourlyRateModel.errors?.required">Hourly Rate is required</div>
                        <div *ngIf="hourlyRateModel.errors?.min">Hourly Rate must be greater than 0</div>
                        <div *ngIf="hourlyRateModel.errors?.pattern">Hourly Rate can only have up to 2 decimal places</div>
                    </div>
                </div>

                <div class="w-100 mb-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <span class="align-middle x-large-font fw-500">Timesheet</span>
                        <div class="form-group mb-0">
                            <div class="input-group">
                                <input class="d-none"
                                   name="datepicker"
                                   ngbDatepicker
                                   #d="ngbDatepicker"
                                   placement="bottom"
                                   [(ngModel)]="timesheetDate"
                                   (dateSelect)="onDateSelect($event)"
                                   [markDisabled]="isSunday">
                                <button class="btn btn-sm btn-outline-secondary align-middle" (click)="d.toggle()" type="button">
                                    <span class="material-symbols-outlined align-middle fw-500"> tune </span>
                                    <span class="align-middle fw-500"> Filter by date </span>
                                    <span class="align-middle fw-500" *ngIf="to"> : {{ viewWeek(to, dateFormat_DD_MM_YYYY) }} </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <ng-container *ngIf="!loading && modalInfo.ready">
                    <!-- BEGIN: Timesheet Table -->
                    <div [class.table-wrapper]="isMobile">
                        <table class="table table-outer-border mb-4 table-custom">
                            <thead>
                            <tr class="">
                                <th class="table-header-bg w-12-per" scope="col">Date</th>
                                <th class="table-header-bg" scope="col">Actual Hours</th>
                                <th class="table-header-bg" scope="col">Adjusted Hours (Day)</th>
                                <th class="table-header-bg" scope="col">Adjusted Hours (Night)</th>
                                <th class="table-header-bg" scope="col">Total Adjusted Hours</th>
                                <th class="table-header-bg" scope="col">Overtime Hours</th>
                                <th class="table-header-bg" scope="col">Driving Hours</th>
                                <th class="table-header-bg" scope="col">Training Hours</th>
                                <th class="table-header-bg" scope="col">Manager Auth</th>
                                <th class="table-header-bg" scope="col">Price Work ({{currencySymbol}})</th>
                            </tr>
                            </thead>
                            <tbody>
                            <ng-container *ngFor="let w of weekDays">
                                <tr>
                                    <td class="table-td-border align-middle py-0">
                                        <span>{{ viewWeek(w.day_of_yr, dateFormat_DD_MM_YYYY) }}</span>
                                    </td>
                                    <td class="table-td-border align-middle py-0">
                                        <time-input
                                            [disabled]="true"
                                            [allowStateChange]="false"
                                            [previewMode]="true"
                                            [input]="{state: hoursState, val: modalInfo.groups.actual[w.day_of_yr].effective_time || null}"
                                            [placeholder]="'N/A'"
                                        ></time-input>
                                    </td>
                                    <td class="table-td-border align-middle py-0">
                                        <span *ngIf="!modalInfo.groups.ts[w.day_of_yr].disabled; else disabledHours">
                                            {{ timeUtility.secondsToHuman([TIME_INPUT_STATE.DAY_HOURS.v, TIME_INPUT_STATE.SPLIT_SHIFT.v].includes(modalInfo.groups.ts[w.day_of_yr].hours_state) ? modalInfo.groups.ts[w.day_of_yr].day_seconds : 0) }}
                                        </span>
                                        <ng-template #disabledHours>N/A</ng-template>
                                    </td>
                                    <td class="table-td-border align-middle py-0 p-x-3px">
                                        <span *ngIf="modalInfo.groups.ts[w.day_of_yr].disabled">N/A</span>
                                        <span *ngIf="!modalInfo.groups.ts[w.day_of_yr].disabled">
                                            {{ ([TIME_INPUT_STATE.NIGHT_HOURS.v, TIME_INPUT_STATE.SPLIT_SHIFT.v].includes(modalInfo.groups.ts[w.day_of_yr].hours_state) ? (modalInfo.groups.ts[w.day_of_yr].night_seconds / 3600) : 0) | number:'1.0-1' }}h
                                        </span>
                                    </td>
                                    <td class="table-td-border align-middle py-0">
                                        <span class="line-h">{{ timeUtility.secondsToHuman((modalInfo.groups.ts[w.day_of_yr].day_seconds || 0) + modalInfo.groups.ts[w.day_of_yr].night_seconds || 0) }}</span>
                                    </td>
                                    <td class="table-td-border align-middle py-0">
                                        <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.ts[w.day_of_yr].overtime_seconds || null) }}</span>
                                    </td>
                                    <td class="table-td-border align-middle py-0">
                                        <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.ts[w.day_of_yr].travel_seconds || null) }}</span>
                                    </td>
                                    <td class="table-td-border align-middle py-0">
                                        <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.ts[w.day_of_yr].training_seconds || null) }}</span>
                                    </td>
                                    <td class="table-td-border align-middle py-0">
                                        <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.ts[w.day_of_yr].manager_auth_seconds || null) }}</span>
                                    </td>
                                    <td class="table-td-border align-middle py-0">
                                        <span class="line-h">
                                            {{ modalInfo.groups.ts[w.day_of_yr].price_work_amount | currency:currencyCode }}
                                        </span>
                                    </td>
                                </tr>
                            </ng-container>
                            <tr>
                                <td class="table-td-border align-middle py-0"><strong>Total</strong></td>
                                <td class="table-td-border align-middle py-0">
                                    <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.effective_time) }}</span>
                                </td>
                                <td class="table-td-border align-middle py-0">
                                    <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.day_seconds) }}</span>
                                </td>
                                <td class="table-td-border align-middle py-0">
                                    <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.night_seconds) }}</span>
                                </td>
                                <td class="table-td-border align-middle py-0">
                                    <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.day_seconds + modalInfo.groups.total.night_seconds) }}</span>
                                </td>
                                <td class="table-td-border align-middle py-0">
                                    <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.overtime_seconds) }}</span>
                                </td>
                                <td class="table-td-border align-middle py-0">
                                    <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.travel_seconds) }}</span>
                                </td>
                                <td class="table-td-border align-middle py-0">
                                    <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.training_seconds) }}</span>
                                </td>
                                <td class="table-td-border align-middle py-0">
                                    <span class="line-h">{{ timeUtility.secondsToHuman(modalInfo.groups.total.manager_auth_seconds) }}</span>
                                </td>
                                <td class="table-td-border align-middle py-0">
                                    {{ modalInfo.groups.total.price_work_amount | currency:currencyCode }}
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!-- END: Timesheet Table -->

                    <ng-container *ngIf="false">
                        <div>
                            <p class="mb-1 font-weight-bold x-large-font fw-500">Activity Log</p>
                        </div>
                        <div class="table-wrapper">
                            <table class="table table-outer-border mb-4 table-custom">
                                <thead>
                                <tr class="table-header-bg">
                                    <th class="date-col-w py-2 table-th-border border-bottom-0 border-top-0" scope="col">
                                        Date Changed
                                    </th>
                                    <th class="comments-by-col-w py-2 table-th-border border-bottom-0 border-top-0" scope="col">
                                        Changed By
                                    </th>
                                    <th class="comments-by-col-w py-2 table-th-border border-bottom-0 border-top-0" scope="col">
                                        Changed From
                                    </th>
                                    <th class="border-bottom-0 border-top-0 py-2" scope="col">Changed To</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr *ngFor="let activityLog of activityLogData">
                                    <td class="table-td-border py-2">{{dayjsDisplayDateOrTime(+activityLog.timestamp)}}
                                        <small class="text-muted d-block">({{dayjsDisplayDateOrTime(+activityLog.timestamp, false)}})</small>
                                    </td>
                                    <td class="table-td-border py-2">{{activityLog.changedBy}}</td>
                                    <td class="table-td-border py-2">{{activityLog.changedFrom}}</td>
                                    <td class="py-2">{{activityLog.changedTo}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </ng-container>

                    <!-- start: comment-section -->
                    <ng-container>
                        <div>
                            <p class="mb-1 font-weight-bold x-large-font fw-500">Comments</p>
                        </div>
                        <table class="table table-outer-border mb-4 table-custom">
                            <thead>
                            <tr class="table-header-bg">
                                <th class="date-col-w py-2 table-th-border border-bottom-0 border-top-0" scope="col">Date</th>
                                <th class="comments-by-col-w py-2 table-th-border border-bottom-0 border-top-0" scope="col">Comments by</th>
                                <th class="border-bottom-0 border-top-0 py-2" scope="col">Details</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr *ngFor="let comment of comments">
                                <td class="table-td-border py-2">
                                    {{ dayjsDisplayDateOrTime(+comment.timestamp) }}
                                    <small class="text-muted d-block">({{ dayjsDisplayDateOrTime(+comment.timestamp, false) }})</small>
                                </td>
                                <td class="table-td-border py-2">{{comment.name}}</td>
                                <td class="py-2">{{comment.note}}</td>
                            </tr>
                            <tr *ngIf="!comments.length">
                                <td colspan="3" class="text-center text-muted">No comments to display yet.</td>
                            </tr>
                            </tbody>
                        </table>
                    </ng-container>
                    <!-- end: comment-section -->
                </ng-container>
            </ng-container>
        </form>
    </div>
</i-modal>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

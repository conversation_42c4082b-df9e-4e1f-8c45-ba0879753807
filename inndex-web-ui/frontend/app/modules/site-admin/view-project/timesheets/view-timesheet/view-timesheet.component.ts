import {Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild} from "@angular/core";
import { AppConstant } from "@env/environment";
import * as dayjs from "dayjs";
import {getCurrencySymbol} from "@angular/common";
import {
    DailyLogForTimesheet,
    ProjectService,
    TIME_INPUT_STATE,
    TIMESHEET_STATUS,
    TimesheetInductionsRow,
    TimeUtility,
    ToastService,
    TimesheetLog,
    Comment, AuthService, User, TIME_HOUR_STATES_VALUE, WeeklyTimesheetLog, ProjectTimesheetConfigure,
} from "@app/core";
import {GenericConfirmationModalComponent, IModalComponent} from "@app/shared";
import { NgbDateStruct } from "@ng-bootstrap/ng-bootstrap";
import { innDexConstant } from "@env/constants";

class TsLog extends TimesheetLog {
    disabled?: boolean;
    changed?: boolean;
    valid?: {
        [key:string]: boolean;
    };
    updated?: {
        [key:string]: boolean;
    };
}

class DefaultTotal {
    effective_time: number = 0;
    day_seconds: number = 0;
    night_seconds: number = 0;
    travel_seconds: number = 0;
    overtime_seconds: number = 0;
    training_seconds: number = 0;
    manager_auth_seconds: number = 0;
    price_work_amount: number = 0;
    early_overtime_seconds: number = 0;
    late_overtime_seconds: number = 0;
}
interface WorkerRecordPayload {
    user_ref: number;
    week_end_date: string;
    employee_number?: string;
    hourly_rate?: number;
}

@Component({
    selector: "view-timesheet",
    templateUrl: "./view-timesheet.component.html",
    styleUrls: ["./view-timesheet.component.scss"],
})
export class ViewTimesheetComponent implements OnInit {
    @Input()
    projectId: number;
    @Input()
    currencyCode: string;
    @Input()
    isMobile: boolean;
    @Input()
    tz: string;
    @Input()
    from: string;
    @Input()
    to: string;

    @Output()
    changed: EventEmitter<any> = new EventEmitter<any>();

    modalInfo: {
        ready: boolean;
        groups?: {
            valid: boolean;
            actual: {
                [day_of_yr: string]: DailyLogForTimesheet
            };
            ts: {
                [day_of_yr: string]: TsLog;
            };
            activities: Array<any>;
            comments: Array<any>;
            approved_ts: Array<number>;
            total: DefaultTotal;
        };
    } = {
        ready: false,
    };
    authUser$: User;
    commentNote: string;

    hoursState: number = TIME_INPUT_STATE.DAY_HOURS.v;
    TIME_INPUT_STATE = TIME_INPUT_STATE;
    status = TIMESHEET_STATUS;
    loading: boolean = false;
    processing: boolean = false;
    currencySymbol: string;

    @Input()
    weekDays: Array<{ id: number; label: string; sub_label?: string; day_of_yr?: string; }> = [];
    record: TimesheetInductionsRow;

    dateFormat: string = AppConstant.displayDateFormat;
    dateDisplayFormat = AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS;
    dateFormat_DD_MM_YYYY: string = innDexConstant.dateFormat_DD_MM_YYYY;
    activityLogData = [
    ];
    comments: Array<Comment> = [];
    weekly_timesheet: WeeklyTimesheetLog;
    shift_config = null;
    timesheet_status: number = 1;
    is_config_changed: boolean = false;
    @Input()
    timesheet_shift_configs :Array<ProjectTimesheetConfigure> = [];

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;

    @Input()
    records: Array<TimesheetInductionsRow> = [];

    selectedUser: number;
    employeeWorkerNumber: any;
    hourlyRate: any;

    // --- Week date selector properties ---
    timesheetDate: NgbDateStruct | null = null;

    constructor(
        private authService: AuthService,
        private toastService: ToastService,
        private projectService: ProjectService,
        public timeUtility: TimeUtility,
    ) {}

    ngOnInit(): void {
        // console.log('modal init');
        this.currencySymbol = getCurrencySymbol(this.currencyCode, 'narrow');
        this.shift_config = this.timesheet_shift_configs.find(item => item.is_default);
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });
    }

    viewWeek(day_of_yr: string, format: string = this.dateFormat){
        return dayjs(day_of_yr, AppConstant.apiRequestDateFormat).format(format);
    }

    applyShiftConfig(shift_config_id:string){
        this.loading = true;
        this.projectService.getUserTimesheetDetails(this.projectId, this.record.user_ref, {
            from_date: this.from,
            to_date: this.to,
            shiftConfigId: shift_config_id,
        }).subscribe((data: any) => {
            this.loading = false;
            if (data.success) {
                this.manageResponse(data, true);
                this.is_config_changed = true;
            } else {
                const message = data.message || 'Failed to get timesheet info.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        })
    }


    @ViewChild('messageDetailsHtml') private messageDetailsHtmlGenericModal: IModalComponent;

    openTimeSheetModalWithConfig(param: TimesheetInductionsRow) {
        this.loading = true;
        let queryParams: any = {
            from_date: this.from,
            to_date: this.to,
        }
        let shiftConfigId = param.timesheet_shift_config_ref || this.shift_config?.id;
        if(shiftConfigId){
            queryParams.shiftConfigId = shiftConfigId;
        }
        this.projectService.getUserTimesheetDetails(this.projectId, param.user_ref, queryParams).subscribe((data: any) => {
            this.loading = false;
            if (data.success) {
                console.log('data', data);
                this.record = param;
                this.manageResponse(data);
                this.modalInfo.ready = true;
                this.commentNote = null;
                this.messageDetailsHtmlGenericModal.open();
            } else {
                const message = data.message || 'Failed to get timesheet info.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        })
    }

    manageResponse(data, config_changed:boolean = false){
        const [weekly_timesheet = {}] = data.weekly_timesheets || [];
        this.weekly_timesheet = weekly_timesheet;
        this.timesheet_status = this.weekly_timesheet?.status || 1;
        if(data.weekly_timesheets.length > 0 && this.weekly_timesheet.timesheet_shift_config_ref && !config_changed){
            this.shift_config = this.timesheet_shift_configs.find(item => (item.id === this.weekly_timesheet.timesheet_shift_config_ref));
        }
        this.groupInfoForRendering(data.daily_logs, weekly_timesheet);
        this.deriveComments(data.weekly_timesheets);
    }

    private groupInfoForRendering(daily_logs: Array<DailyLogForTimesheet>, weekly_timesheets: WeeklyTimesheetLog) {
        let groups = this.weekDays.reduce((out, week) => {
            let log = daily_logs.find(l => l.day_of_yr === week.day_of_yr);
            out.actual[week.day_of_yr] = log || {};
            let ts: TsLog = (weekly_timesheets?.timesheets || []).find(t => t.day_of_yr === week.day_of_yr);
            if (!ts) {
                ts = {
                    day_of_yr: week?.day_of_yr,
                    user_ref: this.record?.user_ref,
                    hours_state: TIME_INPUT_STATE.DAY_HOURS.v,
                    day_seconds: 0,
                    night_seconds: 0,
                    travel_seconds: 0,
                    overtime_seconds: 0,
                    training_seconds: 0,
                    manager_auth_seconds: 0,
                    early_overtime_seconds:log?.derived_early_overtime?.value || 0,
                    late_overtime_seconds:log?.derived_late_overtime?.value || 0,
                    price_work_amount: 0,
                }
            } else if(ts && ts.status === 1){
                ts.early_overtime_seconds = ts.early_overtime_seconds ? ts.early_overtime_seconds : log?.derived_early_overtime?.value;
                ts.late_overtime_seconds = ts.late_overtime_seconds ? ts.late_overtime_seconds : log?.derived_late_overtime?.value;
            }
            ts.disabled = (ts.status === TIMESHEET_STATUS.APPROVED);
            ts.valid = {};
            ts.updated = {};
            ts.changed = false;
            ts.actual_seconds = (log && log.effective_time) || 0;
            if (log?.shift_type) {
                const isDayShift = log.shift_type === 1;
                const targetField = isDayShift ? 'day_seconds' : 'night_seconds';
                const targetState = isDayShift ? TIME_INPUT_STATE.DAY_HOURS.v : TIME_INPUT_STATE.NIGHT_HOURS.v;
                if(!ts[targetField]){
                    ts.hours_state = targetState;
                }
                ts[targetField] = ts[targetField] || log.derived_seconds?.value || 0;
            }

            out.ts[week.day_of_yr] = ts;
            if(weekly_timesheets?.comments && weekly_timesheets?.comments.length){
                out.comments.push(...weekly_timesheets.comments);
            }
            // if(ts.change_logs && ts.change_logs.length){
            //     out.activities.push(...ts.change_logs);
            // }
            if(weekly_timesheets?.id && weekly_timesheets?.status === this.status.APPROVED){
                out.approved_ts.push(weekly_timesheets?.id);
            }
            return out;
        }, {
            valid: false,
            actual: {},
            ts: {},
            activities: [],
            comments: [],
            approved_ts: [],
            total: new DefaultTotal(),
        });

        groups.total = this.deriveTotals(groups);
        console.log('group', groups);
        this.modalInfo.groups = groups;
    }
    private deriveTotals(groups): DefaultTotal {
        let total = this.weekDays.reduce((out, week) => {
            let log = groups.actual[week.day_of_yr];
            let ts = groups.ts[week.day_of_yr];
            if (log && log.effective_time) {
                out.effective_time += +log.effective_time;
            }

            if (TIME_HOUR_STATES_VALUE.includes(ts.hours_state) && ts.day_seconds) {
                out.day_seconds += +ts.day_seconds;
            }

            if (TIME_HOUR_STATES_VALUE.includes(ts.hours_state) && ts.night_seconds) {
                out.night_seconds += +ts.night_seconds;
            }
            out.travel_seconds += (+ts.travel_seconds || 0);
            out.overtime_seconds += (+ts.overtime_seconds || 0);
            out.early_overtime_seconds += (+ts.early_overtime_seconds || 0);
            out.late_overtime_seconds += (+ts.late_overtime_seconds || 0);
            out.training_seconds += (+ts.training_seconds || 0);
            out.manager_auth_seconds += (+ts.manager_auth_seconds || 0);
            out.price_work_amount += (+ts.price_work_amount || 0);

            return out;
        }, new DefaultTotal());

        // console.log(`totals are`, total);
        return total;
    }

    private deriveComments(list: Array<TimesheetLog> = []){
        const comments = list.reduce((all, ts: WeeklyTimesheetLog) => {
            if(ts && ts.comments){
                all.push(...ts.comments)
            }
            return all;
        }, []);
        this.comments = comments;
    }

    disableInput(day_of_yr) {
        return (this.modalInfo.groups.ts[day_of_yr]?.hours_state && ![TIME_INPUT_STATE.DAY_HOURS.v, TIME_INPUT_STATE.NIGHT_HOURS.v, TIME_INPUT_STATE.SPLIT_SHIFT.v].includes(this.modalInfo.groups.ts[day_of_yr].hours_state)) //||
            // (!this.modalInfo.groups.ts[day_of_yr]?.hours_state && this.modalInfo.groups.ts[day_of_yr].id && ![TIME_INPUT_STATE.DAY_HOURS.v, TIME_INPUT_STATE.NIGHT_HOURS.v, TIME_INPUT_STATE.SPLIT_SHIFT.v].includes(this.modalInfo.groups.ts[day_of_yr].hours_state));
    }

    timeInputChanged($event, day_of_yr, key){
        const effective_time = this.modalInfo.groups.actual[day_of_yr].effective_time || 0;
        let previous = this.modalInfo.groups.ts[day_of_yr];
        console.log(`input changed`, day_of_yr, key, $event);

        previous = {
            ...previous,
            [key]: $event.value||0,
            changed: true,
            updated: {
                ...(previous.updated || {}),
                [key]: $event.changed,

                // both of below are needed to keep them in parity,
                // when state is switched to Holiday/Sick/Hour in either of input
                day_seconds: true,
                night_seconds: true,
            },
            valid: {
                ...(previous.valid || {}),
                [key]: $event.valid,
            }
        }
        if(key === 'day_seconds' || key === 'night_seconds'){
            previous.hours_state = $event.state;
            if(!TIME_HOUR_STATES_VALUE.includes(previous.hours_state)){
                previous.travel_seconds = 0;
                previous.overtime_seconds = 0;
                previous.training_seconds = 0;
                previous.manager_auth_seconds = 0;
                previous.price_work_amount = 0;
            }

            if([TIME_INPUT_STATE.DAY_HOURS.v, TIME_INPUT_STATE.NIGHT_HOURS.v].includes(previous.hours_state)){
                if(previous.day_seconds && previous.night_seconds){
                    previous.hours_state = TIME_INPUT_STATE.SPLIT_SHIFT.v;
                }
                else if(previous.day_seconds){
                    previous.hours_state = TIME_INPUT_STATE.DAY_HOURS.v;
                }
                else if(previous.night_seconds){
                    previous.hours_state = TIME_INPUT_STATE.NIGHT_HOURS.v;
                }
            }else{
                // empty out Adjusted Hours, when Holiday/Sick is selected
                previous.day_seconds = 0;
                previous.night_seconds = 0;
            }
        }
        console.log(previous);
        this.modalInfo.groups.ts[day_of_yr] = previous;

        this.modalInfo.groups.valid = Object.values(this.modalInfo.groups.ts).findIndex((column:any) => {
            return Object.values(column.valid).findIndex(v => !v) > -1
        }) === -1;
        console.log(this.modalInfo.groups);
        this.modalInfo.groups.total = this.deriveTotals(this.modalInfo.groups);
    }

    onSaveFn({closeFn}){
        let payload = {
            from_date: this.from,
            to_date: this.to,
            timesheet_shift_config_ref: this.shift_config ? this.shift_config.id : null,
            timesheets: Object.values(this.modalInfo.groups.ts || {}).reduce((list, c: any) => {
                // commenting this to save shift config without any input changes
                // if(!c.changed){
                //     return list;
                // }
                let {day_seconds, actual_seconds, night_seconds, hours_state, travel_seconds, overtime_seconds, training_seconds, manager_auth_seconds,
                    price_work_amount, early_overtime_seconds, late_overtime_seconds} = c;

                list.push({
                    day_of_yr: c.day_of_yr,
                    user_ref: this.record.user_ref,
                    hours_state,
                    actual_seconds,
                    day_seconds, night_seconds,

                    travel_seconds,
                    overtime_seconds,
                    training_seconds,
                    manager_auth_seconds,
                    price_work_amount,
                    early_overtime_seconds,
                    late_overtime_seconds
                });
                return list;
            }, []),
        };
        console.log(payload);
        this.processing = true;
        this.projectService.bulkSaveTimesheetInfo(this.projectId, payload).subscribe(this.afterSaving.bind(this, closeFn));
    }

    onUnApproveFn({closeFn}){
        console.log(this.modalInfo.groups?.approved_ts);
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Confirm',
            title: `You're about to unapprove this timesheet. Are you sure you would like to continue?`,
            confirmLabel: 'Continue',
            onConfirm: () => {
                this.processing = true;
                this.projectService.approveUserTimesheets(this.projectId, {timesheetIds: this.modalInfo.groups?.approved_ts, status: TIMESHEET_STATUS.PENDING}).subscribe(this.afterSaving.bind(this, closeFn));
            }
        });
    }

    private afterSaving(closeFn, data){
        this.processing = false;
        console.log(data);
        if (data.success) {
            this.changed.emit({});
            closeFn && closeFn();
            this.modalInfo.ready = false;
            this.is_config_changed = false;
        } else {
            const message = data.message || 'Failed to save timesheet info.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
        }
    }

    myCancelFn($event){
        console.log($event)
        this.modalInfo.ready = false;
        this.is_config_changed = false;
    }

    dayjsDisplayDateOrTime(n: number, onlyDate = true) {
        return dayjs(n).tz(this.tz).format(onlyDate ? AppConstant.defaultDateFormat : AppConstant.defaultTimeFormat);
    }
    displayUnixTime(n: number| string){
        return n ? dayjs((+n)* 1000).tz(this.tz).format(AppConstant.timFormatWithoutSecs) : '';
    }

    saveTimesheet(){
        let comment = new Comment();
        comment.timestamp = dayjs().valueOf();
        comment.note = this.commentNote;
        comment.user_id = this.authUser$.id;
        comment.name = this.authUser$.name;
        comment.origin = 'admin';
        console.log(this.from, this.to, comment);
        this.processing = true;
        this.projectService.saveTimesheetComment(this.projectId, {
            week_end_date: this.to,
            user_ref: this.record.user_ref,
            comment: comment,
        }).subscribe((data: any) => {
            this.processing = false;
            if (data.success) {
                this.commentNote = null;
                this.comments = [comment].concat(...(this.comments || []));
                // closeFn && closeFn();
                // this.modalInfo.ready = false;
            } else {
                const message = data.message || 'Failed to save timesheet info.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    @ViewChild('viewWorkerRecordHtml') private viewWorkerRecordHtmlGenericModal: IModalComponent
    openViewWorkerRecordModal(row: TimesheetInductionsRow | null, date: string): void {
        this.resetWorkerDetails();
        this.modalInfo.ready = false;

        if (!row) {
            this.resetViewState();
            this.viewWorkerRecordHtmlGenericModal.open();
            return;
        }

        this.initializeWorkerRecord(row, date);
        this.viewWorkerRecordHtmlGenericModal.open();
    }

    private initializeWorkerRecord(row: TimesheetInductionsRow, date: string | null): void {
        this.record = row;
        this.selectedUser = row.user_ref;

        if (!date) {
            this.resetDateState();
            return;
        }

        const dateObj = dayjs(date);
        this.setTimesheetDateInfo(dateObj);
        this.fetchTimesheetDetails();
    }

    private setTimesheetDateInfo(dateObj: dayjs.Dayjs): void {
        this.timesheetDate = {
            year: dateObj.year(),
            month: dateObj.month() + 1,
            day: dateObj.date()
        };
        
        const weekStartDate = dateObj.subtract(6, 'day');
        this.to = dateObj.format(AppConstant.apiRequestDateFormat);
        this.from = weekStartDate.format(AppConstant.apiRequestDateFormat);
        this.weekDays = this.generateWeekDays(weekStartDate, dateObj);
    }

    private resetDateState(): void {
        this.timesheetDate = null;
        this.to = null;
        this.from = null;
    }

    private resetViewState(): void {
        this.selectedUser = null;
        this.resetDateState();
    }

    // Disable all days except Sundays
    isSunday = (date: NgbDateStruct): boolean => {
        const jsDate = new Date(date.year, date.month - 1, date.day);
        return jsDate.getDay() !== 0;
    };

    onDateSelect(date: NgbDateStruct): void {
        const selectedDate = this.convertToDateObject(date);
        const weekStartDate = selectedDate.subtract(6, 'day');
        
        this.updateDateRange(selectedDate, weekStartDate);
        this.weekDays = this.generateWeekDays(weekStartDate, selectedDate);
        
        if (this.record) {
            this.fetchTimesheetDetails();
        }
    }

    private convertToDateObject(date: NgbDateStruct): dayjs.Dayjs {
        return dayjs(new Date(date.year, date.month - 1, date.day));
    }

    private updateDateRange(selectedDate: dayjs.Dayjs, weekStartDate: dayjs.Dayjs): void {
        this.to = selectedDate.format(AppConstant.apiRequestDateFormat);
        this.from = weekStartDate.format(AppConstant.apiRequestDateFormat);
    }

    private fetchTimesheetDetails(): void {
        if (!this.validateTimesheetParams()) {
            return;
        }

        this.loading = true;
        
        this.projectService.getUserTimesheetDetails(
            this.projectId, 
            this.selectedUser, 
            { from_date: this.from, to_date: this.to }
        ).subscribe({
            next: this.handleTimesheetResponse.bind(this),
            error: this.handleTimesheetError.bind(this),
            complete: () => this.loading = false
        });
    }

    private validateTimesheetParams(): boolean {
        if (!this.projectId || !this.selectedUser || !this.from || !this.to) {
            this.toastService.show(
                this.toastService.types.ERROR,
                'Missing required parameters for fetching timesheet details'
            );
            return false;
        }
        return true;
    }

    private handleTimesheetResponse(data: any): void {
        if (!data.success) {
            this.handleTimesheetError(new Error(data.message || 'Failed to get timesheet info.'));
            return;
        }

        const [weekly_timesheet = {}] = data.weekly_timesheets || [];
        
        this.groupInfoForRendering(data.daily_logs, weekly_timesheet);
        this.deriveComments(data.weekly_timesheets);
        this.updateWorkerDetails(weekly_timesheet);
        this.modalInfo.ready = true;
        this.commentNote = null;
    }

    private updateWorkerDetails(weeklyTimesheet: any): void {
        this.employeeWorkerNumber = weeklyTimesheet?.employee_number || null;
        this.hourlyRate = weeklyTimesheet?.hourly_rate || null;
    }

    private handleTimesheetError(error: Error): void {
        this.toastService.show(
            this.toastService.types.ERROR,
            'Error fetching timesheet details',
            { data: error }
        );
    }

    saveWorkerRecord(): void {
        const validationErrors = this.validateWorkerRecord();
        if (validationErrors) {
            this.toastService.show(this.toastService.types.ERROR, validationErrors);
            return;
        }

        this.processing = true;

        const payload: WorkerRecordPayload = {
            user_ref: this.selectedUser,
            week_end_date: this.to,
            employee_number: this.employeeWorkerNumber,
            hourly_rate: this.hourlyRate
        };

        this.projectService.saveWorkerRecord(this.projectId, payload).subscribe({
            next: this.handleSaveWorkerRecordSuccess.bind(this),
            error: this.handleSaveWorkerRecordError.bind(this),
            complete: () => this.processing = false
        });
    }

    private validateWorkerRecord(): string | null {
        if (!this.selectedUser) {
            return 'User reference is required';
        }

        if (!this.to) {
            return 'Week end date is required';
        }

        if (!this.employeeWorkerNumber && !this.hourlyRate) {
            return 'Either employee number or hourly rate must be provided';
        }

        return null;
    }

    private handleSaveWorkerRecordSuccess(response: any): void {
        if (response.success) {
            this.toastService.show(
                this.toastService.types.SUCCESS,
                'Worker record saved successfully'
            );
            this.viewWorkerRecordHtmlGenericModal.close();
        } else {
            this.handleSaveWorkerRecordError(new Error(response.message));
        }
    }

    private handleSaveWorkerRecordError(error: Error): void {
        this.toastService.show(
            this.toastService.types.ERROR,
            error.message || 'Error saving worker record',
            { data: error }
        );
    }

    private generateWeekDays(start: dayjs.Dayjs, end: dayjs.Dayjs): Array<{
        id: number;
        label: string;
        sub_label: string;
        day_of_yr: string;
    }> {
        const weeks = [];
        let currentDay = start.clone();

        for (let i = 0; i < 7; i++) {
            weeks.push({
                id: currentDay.isoWeekday(),
                label: currentDay.format('ddd'),
                sub_label: currentDay.format('Do MMM'),
                day_of_yr: currentDay.format(AppConstant.apiRequestDateFormat),
            });
            currentDay = currentDay.add(1, 'day');
        }
        return weeks;
    }

    onUserChange(record: TimesheetInductionsRow | null): void {
        this.selectedUser = record?.user_ref;
        this.record = record;
    }

    private resetWorkerDetails(): void {
        this.employeeWorkerNumber = null;
        this.hourlyRate = null;
    }

    onEmployeeWorkerNumberChange(workerNumber: string): void {
        this.employeeWorkerNumber = workerNumber?.trim() || null;
    }

    onHourlyRateChange(rate: number): void {
        this.hourlyRate = rate ? Number(rate.toFixed(2)) : null;
    }
}

.table-outer-border {
    border-radius: 4px;
    border-collapse: collapse;
    //border-spacing: 0;
    overflow: hidden;
    border: 0.5px solid var(--spanish-gray);
    box-shadow: 0 0 0 0.5px var(--spanish-gray);
    table-layout: fixed;

    tr td, tr th{
        border: 0.5px solid var(--spanish-gray);
        font-size: 12px;
        padding: 8px 7px;
    }
    .p-x-3px{
        // td padding - input padding, to show it consistent with text
        padding-left: 3px !important;
        padding-right: 3px !important;
    }
    .wide-input{
        display: block;
        margin-left: -4px;
        margin-right: -4px;
    }
    tr th:first-child{
        width: 150px;
    }
}
.table-wrapper{
    display: inline-block;
    padding: 0 20px 0 0;
    & > table{
        min-width: 1096px;
    }
}
.table-header-bg {
    background-color: var(--cultured);
    font-weight: 500;
    //padding: 8px 8px;
}
.table thead th{
    font-size: 12px;
    //@extend .small-font;
}
.table td,
table td {
    font-size: 12px;
    //@extend .small-font;
    padding: 0 8px;
}
.timesheet-td-v-align {
    vertical-align: middle !important;
}
.add-btn-td {
    background-color: var(--Lavender);
    line-height: 34px;
    margin-left: -7.5px;
    margin-right: -7.5px;
    &:not([hidden]){
        display: block;
    }
}
.brandis-blue {
    color: var(--brandeis-blue);
}
.spanish-gray-label {
    color: var(--spanish-gray);
}
.timesheet-dm {
    box-shadow: 0px 1px 1px 0px #0000001a, 0px 3px 3px 0px #00000017,
        0px 6px 4px 0px #0000000d, 0px 17px 5px 0px #00000000;
}
.date-col-w {
    width: 20%;
}
.comments-by-col-w {
    width: 26%;
}
.details-col-w {
    width: 56%;
}
.w-12-per {
    width: 12%;
}
.edit-mt-2 {
    margin-top: 2px;
}
.inline-input {
    padding: 0.085rem 0.45rem !important;
    height: auto !important;
}
.dropdown-position {
    margin-top: -38px !important;
}
.label-dark {
    color: var(--black) !important;
}
.table-custom {
    tr th:first-child{
        width: 60px !important;
    }
}
.table-custom th {
    width: 62px;
    height: 40px;
    padding: 10px 10px 10px 8px;
    vertical-align: middle;
    font-weight: 600 !important;
}
.table-custom td {
    height: 30px;
    padding: 10px 40px 10px 8px;
    vertical-align: middle;
}
.table-custom .cell-flex {
    display: flex;
    width: 62px;
    height: 40px;
    padding: 10px 10px 10px 8px;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
}
.table-custom td .cell-flex {
    height: 30px;
    padding: 10px 40px 10px 8px;
    align-self: stretch;
}

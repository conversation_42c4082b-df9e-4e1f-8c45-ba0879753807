<i-modal #travelTimeModalRef [title]="travelTimeRow?.creator_name + ' ID: ' + travelTimeRow?.user_ref" rightPrimaryBtnTxt="Save" 
  (onClickRightPB)="storeTravelTimeOverride($event)" [size]="'lg-125'" [rightPrimaryBtnDisabled]="!travelTimeForm.valid" cancelBtnText="Close">
        <form class="form-container setup-content" novalidate id="travelTimeForm" #travelTimeForm="ngForm">
            <div class="py-2">
                <span class="font-weight-bold">Modify travel time ({{day.format(displayDateFormat)}})</span>
            </div>
            <ng-container *ngIf="updateTravelTimeRequest.timestamp">
                <div class="form-group d-flex justify-content-between">
                    <p class="mb-0 small">You are modifying existing travel time override defined by <b>{{ updateTravelTimeRequest.creator }}</b>.</p>
                    <span class="material-symbols-outlined cursor-pointer text-danger" (click)="removeTravelTimeOverride(d)">
                        delete
                    </span>
                </div>
            </ng-container>

            <div class="form-group row">
                <label class="col-md-6 col-form-label form-control-label"><span i18n="@@tfpc">Travelling from ({{labelData.label}})</span>: </label>
                <div class="col-md-6">
                    <input class="form-control input-md"
                           #fromPostcode="ngModel" required
                           name="fromPostcode" type="text"
                           placeholder="{{labelData.placeholder}}" pattern="^(?!\s*$)[A-Za-z0-9\s\-]+$" (change)="sameAsFromChekBx.checked ? updateTravelTimeRequest.to_postcode = updateTravelTimeRequest.from_postcode : true"
                           [(ngModel)]="updateTravelTimeRequest.from_postcode"
                           ng-value="updateTravelTimeRequest.from_postcode">
                    <div class="alert alert-danger" [hidden]="fromPostcode.valid || fromPostcode.pristine">
                        <div *ngIf="fromPostcode.errors?.required">{{labelData.error}} is required</div>
                        <div *ngIf="fromPostcode.errors?.pattern">Invalid {{labelData.error}} format</div>
                    </div>
                  </div>
            </div>
            <div class="form-group row">
                <div class="col-md-6">
                    <label class="col-form-label form-control-label"><span i18n="@@ttpc">Traveling to ({{labelData.label}})</span>:</label>
                </div>
                <div class="col-md-6">
                    <input class="form-control input-md"
                        #toPostcode="ngModel"
                        required
                        name="toPostcode"
                        type="text"
                        [readOnly]="sameAsFromChekBx.checked"
                        placeholder="{{labelData.placeholder}}"
                        pattern="^(?!\s*$)[A-Za-z0-9\s\-]+$"
                        [(ngModel)]="updateTravelTimeRequest.to_postcode"
                        ng-value="updateTravelTimeRequest.to_postcode">
                    <div class="alert alert-danger" [hidden]="toPostcode.valid || toPostcode.pristine">
                        <div *ngIf="toPostcode.errors?.required">{{labelData.error}} is required</div>
                        <div *ngIf="toPostcode.errors?.pattern">Invalid {{labelData.error}} format</div>
                    </div>
                </div>
            </div>
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" (click)="preFillTravelingToPostcode($event)" #sameAsFromChekBx
                       id="toggleDefaultAddress">
                <label class="custom-control-label small" for="toggleDefaultAddress">Same as 'Travelling from' {{labelData.label}}</label>
            </div>
            <hr>
            <div class="form-group row align-items-center">
                <label class="col-sm-7 col-form-label">Travel time to work <small class="required-asterisk">*</small></label>
                    <div class="col-sm-5 horizontal-center justify-content-end">
                        <app-duration-picker [stepSize]="1" [(value)]="updateTravelTimeRequest._travel_time_to_work"></app-duration-picker>
                        <span class="ml-2">minutes</span>
                    </div>   
                    <!--<ngb-timepicker name="_travel_time_to_work" [(ngModel)]="induction_request._travel_time_to_work" [meridian]="true" required></ngb-timepicker>
                <div class="alert alert-danger d-none">Travel Time to work is required</div>-->
            </div>
            <div class="form-group row align-items-center">
                <label class="col-sm-7 col-form-label">Travel time to home from work <small class="required-asterisk">*</small></label>
                <div class="col-sm-5 horizontal-center justify-content-end">
                    <app-duration-picker [stepSize]="1" [(value)]="updateTravelTimeRequest._travel_time_to_home"></app-duration-picker>
                    <span class="ml-2">minutes</span>
                </div>
                <!--<ngb-timepicker name="_travel_time_to_home" [(ngModel)]="induction_request._travel_time_to_home" [meridian]="true" required></ngb-timepicker>-->
                <div class="alert alert-danger d-none">Travel Time to Home is required</div>
            </div>
            <div class="form-group row">
                <label class="col-sm-6 col-form-label">Method of travel <small class="required-asterisk">*</small></label>
                <div class="col-md-6">
                    <ng-select [items]="available_travel_methods" [(ngModel)]="updateTravelTimeRequest.travel_method" #gender="ngModel" name="travelMethod" class="dropdown-list" (change)="resetVehicleInfo()" appendTo="body" required>
                    </ng-select>
                </div>
                <!--<input type="text" class="form-control" required #travelMethod="ngModel" [(ngModel)]="induction_request.travel_method" name="travelMethod"
                       placeholder="Enter Your travel Method" />
                <div class="alert alert-danger" [hidden]="(travelMethod.valid)">Travel Method Name is required</div>-->
            </div>
            <div class="form-group row" *ngIf="isVehicleRegRequired(updateTravelTimeRequest.travel_method)">
                <label class="col-sm-6 col-form-label" i18n="@@vrn">Vehicle Registration Number</label>
                <div class="col-md-6">
                    <input type="text" class="form-control" #vehicleRegNum="ngModel" [(ngModel)]="updateTravelTimeRequest.vehicle_reg_number" name="vehicleRegNum"
                        (ngModelChange)="onVehicleRegChange($event)" (blur)="onBlur($event)" pattern="^(?!\s*$).+"
                        placeholder="Enter Your Vehicle Registration Number" i18n-placeholder="@@eyvrn" [required]="isVehicleRegRequired(updateTravelTimeRequest.travel_method)" />
                    <div class="alert alert-danger" [hidden]="(vehicleRegNum.valid)">Vehicle Registration # is required</div>
                </div>

            </div>
            <small class="form-text text-muted" *ngIf="updateTravelTimeRequest?.vehicle_info?.errorMessage">{{ updateTravelTimeRequest.vehicle_info.errorMessage }}</small>
            <div class="form-group row" *ngIf="updateTravelTimeRequest?.vehicle_info?.make">
                <div class="col-sm-4 mb-2">
                    <label class="mb-1">Make</label>
                    <div class="font-italic">{{updateTravelTimeRequest.vehicle_info?.make}}</div>
                </div>
                <div class="col-sm-4 mb-2">
                    <label class="mb-1">Fuel Type</label>
                    <div class="font-italic">{{updateTravelTimeRequest.vehicle_info?.fuelType}}</div>
                </div>
                <div class="col-sm-4 mb-2">
                    <label class="mb-1">CO2 Emissions</label>
                    <div class="font-italic">{{updateTravelTimeRequest.vehicle_info?.co2Emissions}} g/km</div>
                </div>
            </div>
            <!--
            <div class="row">
                <label class="col-md-4 col-form-label form-control-label">Comment: </label>
                <textarea class="col-md-8 form-control" name="note" required [(ngModel)]="adjustmentTimeRequest.comment.note" placeholder="Comment"></textarea>
            </div>
            <div class="row" *ngIf="previousComment?.user_id">
                <label class="col-md-4 col-form-label form-control-label">Adjusted By: </label>
                <div class="col-md-6 col-form-label pl-0">{{ previousComment.name }}</div>
            </div>-->
            <hr>
            <div class="form-group mb-1">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="modifyValidityChkBx" name="hasLongerValidity"
                           #hasLongerValidity="ngModel" [(ngModel)]="updateTravelTimeRequest._has_day_validity" (change)="validityToggled($event)" />
                    <label class="custom-control-label" for="modifyValidityChkBx">Single day only</label>
                </div>
                <small class="form-text text-muted d-none">Create travel time override for {{ day.format(defaultDateFormat) }} <span [style.text-decoration]="(!updateTravelTimeRequest._has_day_validity ? 'line-through': undefined)">only</span>.</small>
            </div>
            <div class="form-group row mb-1">
                <ng-container *ngIf="!updateTravelTimeRequest._has_day_validity">
                    <label class="col-md-6 col-form-label form-control-label">Effective Till: <br/><small>(Date & time)</small></label>
                    <div class="col-md-6">
                        <div class="">
                            <div class="input-group">
                                <input class="form-control" placeholder="yyyy-mm-dd"
                                       name="validTillDate" readonly
                                       [(ngModel)]="updateTravelTimeRequest._validTillDate"
                                       ng-value="newShiftConfigRequest.validTillDate"
                                       [minDate]="validTillMinDate"
                                       ngbDatepicker #validTillDateDp="ngbDatepicker">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary calendar" [disabled]="updateTravelTimeRequest._has_indefinite_validity" (click)="validTillDateDp.toggle()" type="button"> <i class="fa fa-calendar"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="custom-control custom-checkbox mt-2">
                            <input type="checkbox" class="custom-control-input" id="indefiniteDateChkBx" name="indefiniteValidTillDate"
                                   #indefiniteValidTillDate="ngModel" [(ngModel)]="updateTravelTimeRequest._has_indefinite_validity" (change)="inDefiniteValidityToggled($event)" />
                            <label class="custom-control-label" for="indefiniteDateChkBx">Indefinite</label>
                        </div>
                    </div>
                </ng-container>
                <small class="col-sm-12 form-text text-muted">
                    <ng-container *ngIf="updateTravelTimeRequest._has_indefinite_validity">It will be considered for Indefinite time, till modified again.</ng-container>
                    <!--
                    <ng-container *ngIf="!updateTravelTimeRequest._has_indefinite_validity">It will be considered till {{ ngbMomentjsAdapter.ngbDateToDayJs(updateTravelTimeRequest._validTillDate).format(defaultDateFormat) }} only, thereafter user defined travel time will get consideration.</ng-container>
                    -->
                </small>
            </div>
            <div class="clearfix"></div>
            <block-loader [show]="storingTravelTime" [showBackdrop]="true"></block-loader>
        </form>
</i-modal>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

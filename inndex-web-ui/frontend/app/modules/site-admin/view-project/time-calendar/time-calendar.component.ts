/**
 * Created by spatel on 13/4/19.
 */


import {Component, Input, OnInit, ViewChild} from '@angular/core';
import {
    OptimaService, TimeUtility, Project, ProcoreService,
    ToastService
} from "@app/core";
import { Subject} from "rxjs";
import {debounceTime, distinctUntilChanged, filter, tap} from "rxjs/operators";
import * as dayjs from 'dayjs';
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import {NgbDateStruct, NgbTimepickerConfig} from "@ng-bootstrap/ng-bootstrap";
import { SearchWithFiltersComponent, ShiftConfigurationProviderComponent, TimeEntryProviderComponent, ViewGeoFenceLogComponent } from "@app/modules/common";
import {TravelTimeModifierComponent} from "./travel-time-modifier.component";
import { filterData } from '@app/core';
import { Router, ActivatedRoute } from '@angular/router';

@Component({
    selector: 'time-calendar',
    templateUrl: './time-calendar.component.html',
    providers: [NgbTimepickerConfig],
    // changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TimeCalendarComponent implements OnInit {

    dbDateFormat: string = 'YYYY-MM-DD';
    navActivetab: string = 'member-tab';

    minDate: dayjs.Dayjs;

    maxDate: dayjs.Dayjs;// = dayjs();

    @Input()
    projectId: number;

    @Input()
    project: Project;
    @Input()
    biometric_preference: {
        force_onsite_for_an_out?: boolean;
    } = {};

    projectInfo : Project;
    fatigueSiteHours = -1;
    fatigueTotalHours = -1;

    membersReportForDate: Array<{
        travel_time?: any;
        id?: number;
        user_ref?: number;
        project_ref?: number;
        optima_badge_number?: number;
        creator_name?: string;
        employer?: string;
        job_role?: string;
    }> = [];
    tempMembersReportForDate: Array<{
        travel_time?: any;
        id?: number;
        user_ref?: number;
        project_ref?: number;
        optima_badge_number?: number;
        creator_name?: string;
        employer?: string;
    }> = [];

    membersReportLoading: Boolean = false;
    selectedDay: NgbDateStruct = null;
    shiftConfigLoaded: Boolean = false;
    shiftConfigs: Array<any> = [];
    tableOffset:number = 0;
    country_code: string;

    default_in_time: {
        duration?: number;
        label?: string;
    } = {};

    sum_of_total_time: {
        sumOfTotalTime?: number;
        sumOfTotalTimeIncTravel?: number;
    } = {
        sumOfTotalTime : 0,
        sumOfTotalTimeIncTravel: 0
    }
    inductedEmployers: Array<any> = [];
    filter:{
        search:string,
        company:any[],
        visitorSearch: string,
        visitorCompany: any[]
    } = this.initFilters();
    filterData:filterData[] = this.renderFilterData();
    isInitTimeCalender: boolean = false;
    loadingInlineTimeCalender: boolean = false;

    constructor(private optimaService: OptimaService,
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private procoreService: ProcoreService,
        public timeUtility: TimeUtility,
        public ngbMomentjsAdapter: NgbMomentjsAdapter,
        private toastService: ToastService,
    ) {
        // this.selectedDay = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        let viewId = this.activatedRoute.snapshot.queryParams['view'];
        if(viewId) { this.navActivetab = viewId; }
    }

    unix(n: number) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs.unix(n).tz(tz);
    };

    getTimeFromSeconds(n: number) {
        return dayjs().startOf('day').add(dayjs.duration(+n, 'seconds').asSeconds(), 'second');
    };

    ngOnInit() {
        this.membersReportLoading = true;
        this.getProject(this.initializeMembersReport.bind(this));
        this.country_code = this.project?.custom_field?.country_code || undefined;
        this.initNavigationListener();
    }

    initFilters() {
         return {
            search: '',
            company: [],
            visitorSearch: '',
            visitorCompany: []
        }
    }

    isToday(row: any = {}): boolean {
        let selected_today = this.selectedDay && this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).isSame(dayjs(), 'day');
        let still_in = false;
        if (row.clock_in) {
            let hrs_since_started = dayjs().diff(dayjs.unix(+row.clock_in), 'h');
            if (hrs_since_started <= 16) {
                // seems time log of today
                still_in = true;
            }
        }
        return still_in || selected_today;
    }

    timeSoFar(recentIn: any, secondsOnly = false): string | number {
        let seconds = dayjs().unix() - (+recentIn);
        if (secondsOnly) {
            return seconds;
        }
        return seconds ? this.timeUtility.showDuration(seconds) : '-';
    }

    checkSiteFatigue(checkIn) {
        let seconds: any = this.timeSoFar(checkIn, true);
        let hours: any = seconds/3600;
        if(this.projectInfo.fatigue_management_status && this.projectInfo.site_hours_daily) {
            if(hours > this.fatigueSiteHours) {
                return true;
            }
        }
        return false;
    }

    checkTotalFatigue(totalDuration) {
        let hours = (totalDuration ? totalDuration.asHours() : 0);
        if(this.projectInfo.fatigue_management_status && this.projectInfo.total_hours_daily) {
            if(hours > this.fatigueTotalHours) {
                return true;
            }
        }
        return false;
    }

    getProject(onComplete = () => {}) {

        this.projectInfo = this.project;
        if(this.projectInfo.fatigue_management_status) {
            this.fatigueSiteHours = this.projectInfo.site_hours_daily;
            this.fatigueTotalHours = this.projectInfo.total_hours_daily;
        }
        this.minDate = dayjs(this.project.createdAt).startOf('d');
        this.maxDate = dayjs().tz(this.project?.custom_field?.timezone);
        this.selectedDay = this.ngbMomentjsAdapter.dayJsToNgbDate(this.maxDate);
        this.default_in_time.duration = this.timeUtility.convertDecimalHrToDuration(this.project.default_in_duration);
        this.default_in_time.label = this.default_in_time.duration ? this.timeUtility.showDuration(this.default_in_time.duration) : '-';
        onComplete();

    }

    moveToNextDay(){
        let currentSelection = this.selectedDay ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay) : dayjs();
        let nextDay = currentSelection.add(1, 'day');
        if(nextDay.valueOf() < this.maxDate.valueOf()){
            this.selectedDay = this.ngbMomentjsAdapter.dayJsToNgbDate(nextDay);
            this.dateAsSubject.next({ dateValue: nextDay.valueOf(), isLoading: false });
        }
    }

    moveToPrevDay(){
        let currentSelection = this.selectedDay ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay) : dayjs();
        let prevDay = currentSelection.subtract(1, 'day');
        if(prevDay.valueOf() >= this.minDate.valueOf()){
            this.selectedDay = this.ngbMomentjsAdapter.dayJsToNgbDate(prevDay);
            this.dateAsSubject.next({ dateValue: prevDay.valueOf(), isLoading: false })
        }
    }

    dateAsSubject: Subject<{dateValue: number, isLoading: boolean}> = new Subject<{dateValue: number, isLoading: boolean}>();
    initNavigationListener(){
        this.dateAsSubject.pipe(
            filter((value) =>
                value != null &&
                typeof value.dateValue === 'number' &&
                typeof value.isLoading === 'boolean'
            ),
            debounceTime(500),
            distinctUntilChanged() as any,
            tap(({ isLoading } ) => {
                this.initializeMembersReport(isLoading);
            })
        ).subscribe()
    }

    @ViewChild('searchFilters') searchFilters: SearchWithFiltersComponent;
    initializeMembersReport(showLoader: boolean = false) {
        let dayOfYr = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dbDateFormat);
        console.log('Current value of selectedDay', this.selectedDay);
        console.log('Project:', this.projectId, 'Day Of Yr', dayOfYr);
        if (showLoader) {
          this.membersReportLoading = true;
        }
        this.optimaService.getProjectMemberDataByDays(this.projectId, String(dayOfYr)).subscribe(data => {
            this.membersReportLoading = false;
            if(data.badge_logs){
                let employers = (data.induction_requests).reduce((arr, ir) => {
                    if(ir.employer) {
                        arr[ir.employer] = {name: ir.employer};
                    }
                    return arr;
                }, []);
                this.inductedEmployers = Object.values(employers).sort((a:any, b:any) => {
                    return a.name.localeCompare(b.name);
                });
                this.filterData = this.renderFilterData();
                this.membersReportForDate = this.mergeBadgeLogWithRequest(data.induction_requests, data.badge_logs);
                this.tempMembersReportForDate = [...this.membersReportForDate];
                this.getUserSiteConfigs(this.membersReportForDate);
            }else{
                this.membersReportForDate = [];
                this.tempMembersReportForDate = [];
                this.getUserSiteConfigs([]);
                const message = data.message || 'Failed to fetch data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
            this.searchFilters.clearFilters();

        })
    }

    /*calculateSumOfTotalTime(inductionRequests) {
        this.sum_of_total_time.sumOfTotalTime = 0;
        this.sum_of_total_time.sumOfTotalTimeIncTravel = 0;
        //sum total time and total time including travel time
        (inductionRequests || []).map(ir => {
            if (ir.record_of_date.effective_time) {
                this.sum_of_total_time.sumOfTotalTime += ir.record_of_date.effective_time;
                this.sum_of_total_time.sumOfTotalTimeIncTravel += this.timeUtility.getTotalTime(ir.record_of_date.effective_time, ir._active_travel_time, true).asSeconds();
            }
        });
    }*/

    private mergeBadgeLogWithRequest(induction_requests, allBadgeDailyLogs = []) {
        this.sum_of_total_time.sumOfTotalTime = 0;
        this.sum_of_total_time.sumOfTotalTimeIncTravel = 0;

        let dayOfYr = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay);
        return (induction_requests || []).map(ir => {
            let travelTimeOverride = this.timeUtility.getActiveTravelTime(ir, dayOfYr);
            ir._active_travel_time = travelTimeOverride.travel_time;
            ir.record_of_date = allBadgeDailyLogs
                .find(r => (r.badge_number && +r.badge_number === +ir.optima_badge_number) || (!r.badge_number && r.user_id === ir.user_ref));

            if(ir.record_of_date){
                //sum total time and total time including travel time
                if (ir.record_of_date.effective_time) {
                    this.sum_of_total_time.sumOfTotalTime += ir.record_of_date.effective_time;
                    this.sum_of_total_time.sumOfTotalTimeIncTravel += this.timeUtility.getTotalTime(ir.record_of_date.effective_time, ir._active_travel_time, true).asSeconds();
                }

                ir.clock_in = ir.record_of_date.clock_in;
                ir.clock_out = ir.record_of_date.clock_out;
                let out = ir.record_of_date.clock_out;
                ir.recent_in = ir.record_of_date.recent_in;
                ir.duration_in_sec = ir.record_of_date.duration_in_sec;
                ir.adjustment = ir.record_of_date.adjustment;
                ir.effective_time = ir.record_of_date.effective_time;
                if(ir.clock_in){
                    let hrs_since_started = dayjs().diff(dayjs.unix(+ir.clock_in), 'h');
                    if(hrs_since_started <= 16){
                        // seems time log of today
                        ir.is_today = true;
                    }
                }
                if(ir.recent_in && ir.clock_out && ir.is_today && +ir.recent_in > +ir.clock_out){
                    // Still on site?
                    // Continue showing counter, RC 486
                    ir.effective_time = undefined;
                    ir.duration_in_sec = undefined;
                    ir.clock_out = undefined;
                }
                ir.geo_fence = {
                    clock_in: this.timeUtility.isGeoFenceLog(ir.record_of_date.events, 'IN', ir.clock_in),
                    clock_out: this.timeUtility.isGeoFenceLog(ir.record_of_date.events, 'OUT', out)
                };
            }

            return ir;
        }).filter(r => r.record_of_date)
        .sort((a, b) => ( (a.clock_in || 0) - (b.clock_in || 0) ));
    }

    @ViewChild('memberCalendarTable') memberCalendarTable: any;

    filterMembersReport() {
        const val = this.filter.search.toLowerCase();
        let values = [...this.filter.company];
        // filter our data
        const tempMembersReportForDate: any = this.tempMembersReportForDate.filter(function (d) {
            let searchFlag = true
            if (val != "") {
                searchFlag = (
                    (d.creator_name && d.creator_name.toLowerCase().indexOf(val) !== -1) ||
                    (d.employer && d.employer.toLowerCase().indexOf(val) !== -1) ||
                    !val
                )
            }
            let employerFlag = true
            if (values.length !== 0) {
                employerFlag = (d.employer && values.includes(d.employer.toLowerCase()))
            }
            return (searchFlag && employerFlag);
        });

        this.sum_of_total_time.sumOfTotalTime = 0;
        this.sum_of_total_time.sumOfTotalTimeIncTravel = 0;

        tempMembersReportForDate.forEach((ir) => {
            if(ir.record_of_date){
                if (ir.record_of_date.effective_time) {
                    this.sum_of_total_time.sumOfTotalTime += ir.record_of_date.effective_time;
                    this.sum_of_total_time.sumOfTotalTimeIncTravel += this.timeUtility.getTotalTime(ir.record_of_date.effective_time, ir._active_travel_time, true).asSeconds();
                }
            }
        })

        // update the rows
        this.membersReportForDate = tempMembersReportForDate;
        // Whenever the filter changes, always go back to the first page
        this.tableOffset = 0;
    }

    @ViewChild('shiftConfiguration', { static: true }) shiftConfigurationRef: ShiftConfigurationProviderComponent;

    manageUserShiftConfig(event, row, user_meta = {}){
        if(this.shiftConfigurationRef && this.shiftConfigurationRef.manageUserShiftConfig){
            let shiftConfigReadOnly = (this.shiftConfigs || []).find(c => c.user_ref === row.user_ref) || {};
            this.shiftConfigurationRef.manageUserShiftConfig(event, row, shiftConfigReadOnly);
        }
    }

    onShiftChange($event){
        this.initializeMembersReport();
    }

    tabChange(tab: string) {
        this.selectedDay = this.ngbMomentjsAdapter.dayJsToNgbDate(this.maxDate);
        this.filter = this.initFilters();
        this.searchFilters.clearFilters();
        if (tab === "member-tab") {
            this.membersReportLoading = true;
            this.dateAsSubject.next({ dateValue: this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).valueOf(), isLoading: true });
        }
        this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { view: tab }, queryParamsHandling: 'merge' });
    }

    private getUserSiteConfigs(induction_requests){
        this.shiftConfigLoaded = false;
        if(induction_requests && induction_requests.length){

            let timestamp =  this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).add(1, 'h').unix();
            let payload = induction_requests.map(user_log => {
                return {
                    user_ref: +user_log.user_ref,
                    timestamp: (+user_log.clock_in || timestamp)
                }
            });
            this.optimaService.getUserShiftConfigs(this.projectId, payload).subscribe(data => {
                this.shiftConfigLoaded = true;
                if(data.configs){
                    this.shiftConfigs = data.configs;
                }else{
                    const message = data.message || 'Failed to fetch shift config.';
                    this.toastService.show(this.toastService.types.ERROR, message, { data: data });  
                }
            });
        }else{
            this.shiftConfigLoaded = true;
            this.shiftConfigs = [];
        }
    }

    getHhMmSs = this.timeUtility.getHhMmSs;
    createStartTime = this.timeUtility.createStartTime;

    getUserShift(userId){
        let config = (this.shiftConfigs || []).find(c => c.user_ref === userId) || {};
        return config.id ? this.getHhMmSs(this.createStartTime(config.start_time, config.tolerance_hrs)) : '-';
    }

    get getCalendarSelection(): string{
        return this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dbDateFormat);
    }

    @ViewChild('viewGeoFenceLogModal', { static: true }) viewGeoFenceLogModalRef: ViewGeoFenceLogComponent;

    openTimeLogDetail(event, row, {prop}){
        // console.log(row);        console.log(prop);
        event.target.closest('datatable-body-cell').blur();
        if(this.viewGeoFenceLogModalRef && this.viewGeoFenceLogModalRef.openTimeLogDetail){
            this.viewGeoFenceLogModalRef.openTimeLogDetail(row.user_ref, prop, row.record_of_date.day_of_yr, undefined, () => {
                this.initializeMembersReport();
            });
        }

    }

    @ViewChild('travelTimeModifier', { static: true }) travelTimeModifierRef: TravelTimeModifierComponent;

    openTravelTimeModifierModal(event, row){
        let index = this.memberCalendarTable.bodyComponent.getRowIndex(row);   // row being data object passed into the template
        console.log(index)
        let dayOfYr = this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay);
        let travelTimeOverride = this.timeUtility.getActiveTravelTime(row, dayOfYr);
        console.log(travelTimeOverride)
        console.log(row);        //console.log(prop);
        event.target.closest('datatable-body-cell').blur();
        this.travelTimeModifierRef.openTravelTimeModifyModal(Object.assign({}, row), index, Object.assign({}, travelTimeOverride));

    }

    travelTimeRowUpdated({row, index}){
        // console.log(index, row);
        this.initializeMembersReport();
    }

    @ViewChild('timeEntryProvider', { static: false }) timeEntryProvider: TimeEntryProviderComponent;
    openTimeEntryProviderModal(event, row, {prop}){
        // console.log(row);        console.log(prop);
        event.target.closest('datatable-body-cell').blur();
        this.timeEntryProvider.openTimeEntryModal({
            selected_day: this.selectedDay,
            user_ref: row.user_ref,
            name: row.creator_name,
            event_type: (prop === 'clock_in' ? 'IN' : 'OUT'),
            user_location: {
                lat: 100,
                long: 100,
                distance: 1000
            },
        });
    }

    onManualTimeEntrySave(apiData){
        this.membersReportLoading = true;
        this.initializeMembersReport();
    }

    exportInProgress: boolean = false;
    exportToProcore(){
        let users = this.tempMembersReportForDate.map(ir => ir.user_ref);
        this.exportInProgress = true;
        this.procoreService.triggerExportToProcore(this.projectId, {
            users: users.join(','),
            date: this.ngbMomentjsAdapter.ngbDateToDayJs(this.selectedDay).format(this.dbDateFormat)
        }).subscribe((data: any) => {
            this.exportInProgress = false;
            if (data.createdEntries) {
                const message = `Exported ${data.createdEntries.length} successfully.`;
                this.toastService.show(this.toastService.types.SUCCESS, message);
            } else {
                const message = data.message || 'Failed to export data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });

    }

    addFullManualEntries() {
    let present_users = (this.tempMembersReportForDate || []).reduce((list, r) => {
            list[r.user_ref] = +r.user_ref;
            return list;
        }, {});
        this.timeEntryProvider.openBulkTimeEntryModal(this.selectedDay,{
            lat: 100,
            long: 100,
            distance: 1000
        }, present_users);

    }

    onFilterSelection(data) {
        try {
            this.checkInitialized();
            if (this.navActivetab === "member-tab") {
                this.filter.company = data.company.map(a => a.name.toLowerCase());
                this.filterMembersReport();
            } else {
                this.filter.visitorCompany = data.company.map(a => a.name.toLowerCase());
            }
        } catch (error) {
            console.error('Something went wrong while filtering:', error);
        } finally {
            this.resetInlineLoader();
        }
    }

    searchFunction(data){
        try {
            this.checkInitialized();
            if (this.navActivetab === "member-tab") {
              this.filter.search = data.search;
              this.filterMembersReport();
            } else {
              this.filter.visitorSearch = data.search;
            }
        } catch (error) {
            console.error('Something went wrong while search:', error);
        } finally {
            this.resetInlineLoader();
        }
    }

    private checkInitialized(): void {
        if(!this.isInitTimeCalender) {
            this.isInitTimeCalender = true;
        } else {
            this.loadingInlineTimeCalender = true;
        }
    }

    private resetInlineLoader(): void {
        setTimeout(() => {
            this.loadingInlineTimeCalender = false;
        }, 100);
    }

    renderFilterData(){
        return [
            {
                name:'company',
                list:this.inductedEmployers,
                enabled:true,
                state:false
            }
        ];
    }
}

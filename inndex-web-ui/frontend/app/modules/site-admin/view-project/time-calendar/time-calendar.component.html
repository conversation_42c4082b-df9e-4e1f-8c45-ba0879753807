<div class="col-sm-12 my-2 px-0">
    <ul ngbNav #nav="ngbNav" [(activeId)]="navActivetab" (activeIdChange)="tabChange($event)"
    class="nav-tabs n-tab nav">
        <li [ngbNavItem]="'member-tab'" [domId]="'member-tab'">
            <a ngbNavLink class="nav-a cursor-pointer">
                Member Records
            </a>
            <ng-template ngbNavContent>
                <div class="form-group row">
                    <label class="col-md-2 col-form-label form-control-label mb-3">Choose a Day</label>
                    <div class="d-flex justify-content-between flex-wrap flex-column flex-md-row col-md-10">
                        <div class="col-md-6 p-0 d-flex align-content-center mb-3">
                            <i class="fas fa-angle-double-left fa-2x d-inline-flex cursor-pointer text-muted"
                                (click)="moveToPrevDay()"></i>
                            <div class="input-group d-inline-flex col-9 px-1">
                                <input class="form-control" placeholder="dd-mm-yyyy" readonly
                                        name="selectedDob" [(ngModel)]="selectedDay" ngbDatepicker
                                        [minDate]="ngbMomentjsAdapter.dayJsToNgbDate(minDate)"
                                        [maxDate]="ngbMomentjsAdapter.dayJsToNgbDate(maxDate)"
                                        #d="ngbDatepicker" ng-value="selectedDay" (dateSelect)="initializeMembersReport()">
                                <div class="input-group-append d-block">
                                    <button class="btn btn-outline-secondary calendar" (click)="d.toggle()" type="button">
                                        <i class="fa fa-calendar"></i>
                                    </button>
                                </div>
                            </div>
                            <i class="fas fa-angle-double-right fa-2x d-inline-flex cursor-pointer text-muted"
                                (click)="moveToNextDay()"></i>
                        </div>
                        <div>
                            <button *ngIf="projectInfo?.custom_field?.manual_time_entry" class="btn w-100 btn-sm other-action-btn btn-brandeis-blue d-flex justify-content-center align-items-center btn-invite pointer-cursor float-md-right" (click)="addFullManualEntries()" >
                                <span class="x-large-font material-symbols-outlined mr-1">add</span>
                                <div class="medium-font m-font-size">Add Time Log</div>
                            </button>
                            <button *ngIf="projectInfo?.custom_field?.procore_integration && tempMembersReportForDate.length" class="d-none btn btn-sm float-right" (click)="exportToProcore()">
                                <i class="fas fa-file-export"></i> Export to Procore
                            </button>
                        </div>
                    </div>
                </div>
                <search-with-filters #searchFilters [filterData]="filterData" (searchEmitter)="searchFunction($event)" (filterEmitter)="onFilterSelection($event)"></search-with-filters>
                <div class="table-responsive-sm" *ngIf="!membersReportLoading">
                    <ngx-datatable #memberCalendarTable
                        class="bootstrap table table-hover table-sm expandable ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                        [scrollbarV]="true" [virtualization]="false" [loadingIndicator]="loadingInlineTimeCalender"
                        [rows]="membersReportForDate" [columns]="[
                            {name:'Member', prop: 'creator_name', cellTemplate: memberCell, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                            {name:'Company', prop: 'employer', cellTemplate: companyCell, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                            {name:'Job Role', prop: 'job_role', cellTemplate: jobRoleCell, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                            {name:'In time', prop: 'clock_in', cellTemplate: timestampCellTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                            {name:'Out time', prop: 'clock_out', cellTemplate: timestampCellTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                            {name:'Total time', prop: 'effective_time', cellTemplate: totalInTimeCell, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                            {name:'Travel time', prop: '_active_travel_time', cellTemplate: travelTimeCell, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                            {name:'total', prop: 'effective_time', cellTemplate: totalTimeCell, headerTemplate: totalTimeHeadTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                            {name:'Shift starts At', sortable: false, cellTemplate: shiftStartTimeCell, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', minWidth:'100'}
                            ]" [columnMode]="'force'" [headerHeight]="50" [footerHeight]="36" [offset]="tableOffset"
                        [rowHeight]="'auto'">
                        <ng-template #memberCell let-value="value">
                            <span appTooltip>{{ value }}</span>
                        </ng-template>
                        <ng-template #companyCell let-value="value">
                            <span appTooltip>{{ value }}</span>
                        </ng-template>
                        <ng-template #jobRoleCell let-value="value">
                            <span appTooltip>{{ value }}</span>
                        </ng-template>
                        <ng-template #timestampCellTemplate let-value="value" let-row="row" let-column="column">
                            {{ value ? unix(+value).format('HH:mm:ss') : ''}}
                            <button *ngIf="!value" class="btn btn-xs"
                                (click)="openTimeEntryProviderModal($event, row, column)">-</button>
                            <small
                                *ngIf="(column.prop === 'clock_in') && row.record_of_date && row.record_of_date.clock_in_temperature">({{
                                (+row.record_of_date.clock_in_temperature).toFixed(1)}} &deg;C)</small>
                            <i *ngIf="row.geo_fence[column.prop]" class="cursor-pointer fas fa-info-circle"
                                (click)="openTimeLogDetail($event, row, column)"></i>
                        </ng-template>
                        <ng-template #totalInTimeCell let-row="row" let-value="value">
                            <div *ngIf="value"
                                [ngClass]="{'text-danger': +value/3600 > fatigueSiteHours && (projectInfo.fatigue_management_status && projectInfo.site_hours_daily) }">
                                {{ getTimeFromSeconds(+value).format('HH:mm:ss') }}
                                <span *ngIf="row.adjustment" class="small"> (<ng-container *ngIf="row.adjustment > 0">+</ng-container>{{
                                    row.adjustment }} minutes)</span>
                            </div>
                            <div *ngIf="!value" title="This doesn't include adjusted time">
                                <div *ngIf="row.clock_in && (isToday(row))"
                                    [ngClass]="{'text-success': !checkSiteFatigue(row.clock_in), 'text-danger': checkSiteFatigue(row.clock_in)}">
                                    {{ timeSoFar(row.clock_in) }}*</div>
                                <div *ngIf="!(row.clock_in && isToday(row))" class="text-orange"> {{ default_in_time.label }} </div>
                            </div>
                        </ng-template>
                        <ng-template #travelTimeCell let-row="row" let-value="value">
                            <ng-container *ngIf="timeUtility.getTotalTravelDuration(value, 'minutes') as timeDuration; else noValue">
                                {{ timeDuration }} <i *ngIf="!row.is_shadow_user" class="fa fa-pencil-alt cursor-pointer"
                                    aria-hidden="true" (click)="openTravelTimeModifierModal($event, row)"></i>
                            </ng-container>
                            <ng-template #noValue> - </ng-template>
                        </ng-template>
                        <ng-template #totalTimeHeadTemplate>
                            Total Time <br /><small>(inc. Travel)</small>
                        </ng-template>
                        <ng-template #totalTimeCell let-row="row">
                            <div *ngIf="row.effective_time"
                                [ngClass]="{'text-danger': checkTotalFatigue(timeUtility.getTotalTime(row.effective_time, row._active_travel_time, true))}">
                                {{ timeUtility.getTotalTime(row.effective_time, row._active_travel_time) }} </div>
                            <div *ngIf="!row.effective_time" title="This doesn't include adjusted time">
                                <div *ngIf="row.clock_in && (isToday(row))"
                                    [ngClass]="{'text-success': !checkTotalFatigue(timeUtility.getTotalTime(timeSoFar(row.clock_in, true), row._active_travel_time, true)), 'text-danger': checkTotalFatigue(timeUtility.getTotalTime(timeSoFar(row.clock_in, true), row._active_travel_time, true))}">
                                    {{ timeUtility.getTotalTime(timeSoFar(row.clock_in, true), row._active_travel_time) }}*</div>
                                <div *ngIf="!(row.clock_in && isToday(row))" class="text-orange">{{
                                    timeUtility.getTotalTime(default_in_time.duration, row._active_travel_time) }}</div>
                            </div>
                        </ng-template>
                        <ng-template #shiftStartTimeCell let-row="row">
                            <div *ngIf="shiftConfigLoaded" (click)="manageUserShiftConfig($event, row)"
                                class="cursor-pointer text-info btn-link1">{{ getUserShift(row.user_ref) }}</div>
                        </ng-template>
                        <ngx-datatable-footer *ngIf="true">
                            <ng-template ngx-datatable-footer-template let-rowCount="rowCount" let-pageSize="pageSize"
                                let-selectedCount="selectedCount" let-curPage="curPage" let-offset="offset">
                                <div style="padding: 5px 10px; width: 100%; display: flex;">
                                    <div style="width: 12.8%">{{ rowCount }} Total</div>
                                    <div style="width: 12.7%"></div>
                                    <div style="width: 12.7%"></div>
                                    <div style="width: 12.7%"></div>
                                    <div style="width: 12.8%">
                                        <span *ngIf="sum_of_total_time.sumOfTotalTime">
                                            {{ timeUtility.showDurationAsHours(sum_of_total_time.sumOfTotalTime, false) }}
                                        </span>
                                    </div>
                                    <div style="width: 12.7%"></div>
                                    <div style="width: 12.7%">
                                        <span *ngIf="sum_of_total_time.sumOfTotalTimeIncTravel">
                                            {{ timeUtility.showDurationAsHours(sum_of_total_time.sumOfTotalTimeIncTravel, false) }}
                                        </span>
                                    </div>
                                    <div style="width: 8%;"></div>
                                </div>
                            </ng-template>
                        </ngx-datatable-footer>
                    </ngx-datatable>
                </div>
            </ng-template>
        </li>
        <li [ngbNavItem]="'visitor-tab'" [domId]="'visitor-tab'">
            <a ngbNavLink class="nav-a cursor-pointer">
                Visitors
            </a>
            <ng-template ngbNavContent>
                <div class="form-group row">
                    <label class="col-md-2 col-form-label form-control-label mb-3">Choose a Day</label>
                    <div class="d-flex justify-content-between flex-wrap flex-column flex-md-row col-md-10">
                        <div class="col-md-6 p-0 d-flex align-content-center mb-3">
                            <i class="fas fa-angle-double-left fa-2x d-inline-flex cursor-pointer text-muted"
                                (click)="moveToPrevDay()"></i>
                            <div class="input-group d-inline-flex col-9 px-1">
                                <input class="form-control" placeholder="dd-mm-yyyy" readonly
                                        name="selectedDob" [(ngModel)]="selectedDay" ngbDatepicker
                                        [minDate]="ngbMomentjsAdapter.dayJsToNgbDate(minDate)"
                                        [maxDate]="ngbMomentjsAdapter.dayJsToNgbDate(maxDate)"
                                        #d="ngbDatepicker" ng-value="selectedDay" (dateSelect)="initializeMembersReport()">
                                <div class="input-group-append d-block">
                                    <button class="btn btn-outline-secondary calendar" (click)="d.toggle()" type="button">
                                        <i class="fa fa-calendar"></i>
                                    </button>
                                </div>
                            </div>
                            <i class="fas fa-angle-double-right fa-2x d-inline-flex cursor-pointer text-muted"
                                (click)="moveToNextDay()"></i>
                        </div>
                        <div>
                            <button *ngIf="projectInfo?.custom_field?.manual_time_entry" class="btn w-100 btn-sm other-action-btn btn-brandeis-blue d-flex justify-content-center align-items-center btn-invite pointer-cursor float-md-right" (click)="addFullManualEntries()" >
                                <span class="x-large-font material-symbols-outlined mr-1">add</span>
                                <div class="medium-font m-font-size">Add Time Log</div>
                            </button>
                            <button *ngIf="projectInfo?.custom_field?.procore_integration && tempMembersReportForDate.length" class="d-none btn btn-sm float-right" (click)="exportToProcore()">
                                <i class="fas fa-file-export"></i> Export to Procore
                            </button>
                        </div>
                    </div>
                </div>
                <search-with-filters #searchFilters [filterData]="filterData" (searchEmitter)="searchFunction($event)" (filterEmitter)="onFilterSelection($event)"></search-with-filters>
                <div class="table-responsive-sm" *ngIf="!membersReportLoading">
                    <visitor-logs-for-date [hideTitle]="true" [filterCompany]="filter.visitorCompany" [searchText]="filter.visitorSearch" [projectId]="projectId"
                        [project]="project" [day_of_yr]="getCalendarSelection"
                        [showMedicalInfo]="!project?.custom_field?.disable?.view_medication_modal"></visitor-logs-for-date>
                </div>
            </ng-template>
        </li>
    </ul>
    <div [ngbNavOutlet]="nav" class="col-sm-12 my-2 pt-4 nav-panel custom-tab-radius tab-content time-mgt-tab-min-h"></div>
</div>
<block-loader [show]="membersReportLoading || exportInProgress" [showBackdrop]="true" alwaysInCenter="true"></block-loader>

<shift-configuration-provider
    #shiftConfiguration
    [projectId]="projectId"
    [selectedDay]="selectedDay"
    (shiftChanged)="onShiftChange($event)">
</shift-configuration-provider>

<view-geo-fence-log-modal
    #viewGeoFenceLogModal
    [showDeleteBtn]="true"
    [showCommentArea]="true"
    [tz]="projectInfo?.custom_field?.timezone"
    [projectId]="projectId">
</view-geo-fence-log-modal>

<travel-time-modifier
    #travelTimeModifier
    [projectId]="projectId"
    [projectPostcode]="projectInfo?.postcode"
    [day]="ngbMomentjsAdapter.ngbDateToDayJs(selectedDay)"
    (rowUpdated)="travelTimeRowUpdated($event)"
    [country_code]="country_code"
></travel-time-modifier>

<time-entry-provider
    *ngIf="project"
    #timeEntryProvider
    [projectId]="projectId"
    [projectInfo]="project"
    [country_code]="country_code"
    [force_onsite_for_an_out]="biometric_preference?.force_onsite_for_an_out"
    (onSave)="onManualTimeEntrySave($event)"
></time-entry-provider>

<style>
    agm-map {
        height: calc(100vh - 144px);
        width: 100%;
    }
    .gm-style img.rounded-circle{
        object-fit: cover;
        width: 100%;
        height: 70px;
    }
    .text-color-grey { color: #B2B2B2; }
    .flip_icon { transform: scaleX(-1); }
    .profile-img {
        position: absolute;
        width: 70px;
        height: 70px;
    }
    @media (max-width: 414px) {
        .width-75 {
            width: 75% !important;
        }
    }
    .img-border { border: 4px solid #B2B2B2;}
    .bg-color-grey { background-color: #B2B2B2;}
</style>
<div class="ml-auto d-flex align-items-center justify-content-end">
    <action-button 
        [actionList]="actionButtonMetaData.actionList"
        (selectedActionEmmiter)="onActionSelection($event)"
        [hideNewFeatureBtn]="true">
    </action-button>
</div>
<block-loader [show]="exportInProgress" alwaysInCenter="true"></block-loader>

<i-modal #onSiteUsersMapModalRef [title]="'On Site Operatives ' + (!fetchingOnSiteEvents ? ('(' + (onsite_events.length) + ')') : '')" size="xl" 
    [showCancel]="false" [showFooter]="false" [windowClass]="'xl-modal'" modalBodyClass="p-3">
        <ngx-skeleton-loader *ngIf="fetchingOnSiteEvents" count="1" [theme]="{ 'border-radius': '0', height: 'calc(100vh - 248px)'}"></ngx-skeleton-loader>
        <agm-map *ngIf="biometric_meta.has_geo_fence_source && !fetchingOnSiteEvents"
                [latitude]="biometric_meta._first_geo_fence_location.lat"
                [longitude]="biometric_meta._first_geo_fence_location.long"
                [zoom]="20"
                [disableDefaultUI]="false"
                [zoomControl]="true"
                [fitBounds]="true"
                [streetViewControl]="false">
            <agm-marker
                    [latitude]="biometric_meta._first_geo_fence_location.lat"
                    [longitude]="biometric_meta._first_geo_fence_location.long"
                    [agmFitBounds]="true" [iconUrl]="getIcons(project.project_type)"
                    [title]="project.name" [openInfoWindow]="false">
            </agm-marker>
            <agm-marker-spider [keepSpiderfied]="true">
                <agm-marker
                        *ngFor="let m of onsite_events; let i = index"
                        [latitude]="m.lat"    
                        [longitude]="m.lng"
                        iconUrl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAA3QAAAN0BcFOiBwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIUSURBVFiFrZexTlVBEIa/vVyEQAy0hgTtpFMICQWFoZVoh09wC3tMfAAaa2ztTHwBCisSSUhoVChMjAaDWogFJJJoIBR3LJgbT3DmnNk9bDLF3fPvP9/O7tk9N4kI0ZZSGgfuAfOVAHhbiS0R+RM2FZFQAEvAASANcQAshX0DiceAdaAfSD6Ivo4ZawUAdIDtjMSXYxvotAFYbZF8EKt1OZK3CVNKt4E9YNR4fAJsAO/19xzwAJgwtGfAXRH5ZCaqmf2mM6PXwJShn9Jn1pjNrCUARoBzw+gd0K2B7qrm8rhzYCQHYMGZyWzgrZl1xi5Y+o65Lv8OmGo7FJFdR19d0l3gMOjpAtwx+vaakjdoLU8X4LfRdzMDwNJani7AN6NvJqU02ZRZNTNBTxfAKmEHeNYEoBrL194/zk5OwD72Gd+reQN62HfGZ7g49MJHMfDUMBrEBrAM3NBY1j5P/yT7LgCuAz9rTKPxAxgvvYweXwGAu2QRgC7wsUXyD8BQMYBCPGwBcL/Rv0mgEG8Kkrs3YAnAvPN6edEH5q4MQCFeZQC8DPtmANzi4uumKfkpMB319Y7i/5qIfAWeB6TrIvI96huugFZhEjiumf0RMJHjGa6Awv4C1mokayJykuOZVQGtwjXgizH7fWA42y93gEI8MgBWirxKBinETiX5TrFPC4DFCsBiqY/7zyjSUkovdB/1Sj3+AjXj8zB5h+6ZAAAAAElFTkSuQmCC"
                        [agmFitBounds]="true"
                        [title]="m.name"
                    >
                    <agm-info-window>
                        <div style="min-width: 265px; max-height: 170px;">
                            <table class="table table-borderless mb-0">
                                <tbody>
                                    <tr>
                                        <td width="30%" class="p-0">
                                            <img class="rounded-circle profile-img img-border bg-color-grey" [src]="m?.info?.url ? m?.info?.url : avatar_dummy_pic" onerror="this.src='/assets/images/avatar-dummy-img.jpeg'">
                                        </td>
                                        <td width="70%" class="p-0 pl-1 pt-1">
                                            <div>
                                                <h6 class="m-0">{{ m.name }}</h6>
                                                <span><small><i>{{ m.info.user?.job_role }}</i></small></span>
                                            </div>
                                            <div class="mt-2">
                                                <span *ngIf="m.info.user">
                                                    <small><i class="fa fa-phone flip_icon text-color-grey" aria-hidden="true"></i> 
                                                        <div *ngIf="m.info.user?.mobile_number || m.info.user?.mobile_no">
                                                            <span>
                                                              {{ m.info.user.mobile_number?.code ? '(+' + m.info.user.mobile_number.code + ')' : '' }} {{ m.info.user.mobile_number?.number }}
                                                            </span>
                                                            <span *ngIf="!m.info.user?.mobile_number && m.info.user?.mobile_no">
                                                              {{ m.info.user.mobile_no }}
                                                            </span>
                                                          </div>
                                                    </small>
                                                </span>
                                            </div>
                                            <div *ngIf="m.info.user?.employer">
                                                <span class="text-color-grey"><b><small>Employer: </small></b></span>
                                                <span><small>{{m.info.user?.employer}}</small></span>
                                            </div>
                                            <div>
                                                <span class="text-color-grey"><b><small>Clock In Time: </small></b></span>
                                                <span><small>{{ m.info.in_time ? unix(m.info.in_time).format('HH:mm:ss') : '-' }}</small></span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </agm-info-window>
                </agm-marker>
            </agm-marker-spider>
        </agm-map>
</i-modal>

<i-modal #accessPointsListModalRef [title]="'Location List ' + (!fetchingAccessPointsList ? ('(' + (access_points_w_users.length) + ')') : '')" size="xl" 
    [showCancel]="false" [showFooter]="false" [windowClass]="'xl-modal'" modalBodyClass="p-3">
        <ngx-skeleton-loader *ngIf="fetchingAccessPointsList" count="8" [theme]="{'border-radius': '0',height:'37px',margin:'0px'}"></ngx-skeleton-loader>
        <div *ngIf="!fetchingAccessPointsList">
            <div *ngIf="!(access_points_w_users || []).length" class="text-center">
                No on-site operatives
            </div>
            <ngb-accordion *ngIf="(access_points_w_users || []).length" #access_point_accordion="ngbAccordion">
                <ngb-panel *ngFor="let ap of access_points_w_users;let i = index;" [id]="'head-'+ i" cardClass="dark-panel-head">
                    <ng-template ngbPanelTitle>
                        <span class="panel-title">
                            <i class="fas fa-chevron-down pr-2" *ngIf="access_point_accordion.isExpanded('head-'+ i)"></i>
                            <i class="fas fa-chevron-right pr-2" *ngIf="!access_point_accordion.isExpanded('head-'+ i)"></i>
                            <b>{{ap.name || ''}}</b> <small> (On-site <span i18n="@@operatives">Operatives</span>: {{ (ap.users || []).length }})</small>
                        </span>
                    </ng-template>
                    <ng-template ngbPanelContent>
                        <div class="row">
                            <ngx-datatable
                                    class="bootstrap table table-hover table-sm w-100"
                                    [rows]="ap.users"
                                    [columns]="[
                                    {name:'Id', prop: 'id', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', width: 15},
                                    {name:'Name', prop: 'name', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1'},
                                    {name:'Company', prop: 'employer', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1'},
                                    {name:'Job Role', prop: 'job_role', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1'},
                                    {name:'in Time', prop: 'recent_in', cellTemplate: inTimeCellTemplate, width: 20, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1'}
                                ]"
                                    [columnMode]="'force'"
                                    [footerHeight]="30"
                                    [sorts]="[{prop: 'recent_in', dir: 'asc'}]"
                                    [limit]="100">
                                <ng-template #inTimeCellTemplate let-value="value" let-row="row">
                                    {{ value ? unix(+value).format('HH:mm:ss') : '-'}}
                                </ng-template>
                            </ngx-datatable>
                        </div>
                    </ng-template>
                </ngb-panel>
            </ngb-accordion>
        </div>
</i-modal>

<i-modal #randomOperativeListModalRef title="Random Operative List" i18n-title size="md" cancelBtnText="Close"
    (onClickRightPB)="exportRandomOperativeList($event)" rightPrimaryBtnTxt="Download" [rightPrimaryBtnDisabled]="!numOperatives.valid || randomExportInProgress">
    <div class="form-group">
        <label>No. of <span i18n="@@operatives">Operatives</span></label>
        <input class="form-control" id="numOperatives" type="number" name="num_operatives" [(ngModel)]="noOfOperatives" palceholder="No. of Ops" min="1" step="1" [max]="totalOnSiteOperatives"
            #numOperatives="ngModel" required pattern="^[1-9]\d*$">
        <label>Total on-site operatives: {{ totalOnSiteOperatives }}</label>
    </div>
    <block-loader [show]="randomExportInProgress" alwaysInCenter="true"></block-loader>
</i-modal>

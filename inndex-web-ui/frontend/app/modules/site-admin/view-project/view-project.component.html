<div class="col-12 px-4">
    <div class="row">
        <project-header [projectData]="projectInfo" class="col-12 mt-3 nav-tabs"></project-header>
        <div class="col-12 my-3">
            <project-inductions
                #pInductions
                [projectId]="projectId"
                [detailsEnabled]="projectSectionAccess"
                [inductionPermission]="inductionPermission"
                [medication_block_disabled]="medication_block_disabled"
                [rtw_status]="projectContractor.rtw_status"
                [fr_setting]="projectContractor.fr_setting"
                [projectInfo]="projectInfo"
                [optima_meta]="optima_meta"
                [conductCardsList]="conductCardsList"
                (onOpenInviteToInductionModal)="openInviteToInductionModal()"
                (onOpenMedicalConditionsModal)="openMedicalConditionsModal($event, null)"
                (onOpenPopup)="checkDuplicateIfFREnabled($event.row)"
                (onIrBlockReason)="irBlockReason($event)"
                (onOpenInductionForm)="openInductionForm($event.row, $event.target)"
                (onAddMedicationPopup)="addMedicationPopup($event)"
                (onEditUserCompanyPopup)="editUserCompanyPopup($event)"
                (onOpenMoreUploadPopup)="openMoreUploadPopup($event)"
                (onAssignBadgeNumberModel)="assignBadgeNumberModel($event)"
                (onInitFingerprintEnrolmentModal)="initFingerprintEnrolmentModal($event)"
                (onViewDeclarationAnswers)="viewDeclarationAnswers($event)"
                (onBlackListUserInduction)="blackListUserInduction($event)"
                (onOpenConductCardModal)="openConductCardModal($event)"
                (downloadQRPoster)="downloadQRPoster()"
                (onViewInduction)="viewInductionModal($event)"
                [showQRDownloadButton]="showQRDownloadButton"
            ></project-inductions>
        </div>
    </div>
</div>


<additional-doc-uploader [country_code]="(projectInfo?.custom_field?.country_code || undefined)" [user]="authUser$" (uploadDone)="onDocumentUpload($event)" #additionalDocUploaderComponent></additional-doc-uploader>
<clock-in-declaration-answers [projectId]="projectId" #declarationAnswersComponent></clock-in-declaration-answers>
<manage-tb-site-access
    [projectId]="this.projectId"
    #manageTbSiteAccessComponent
></manage-tb-site-access>
<manage-blacklisting
    #manageBlacklistingModal
    [projectId]="projectInfo.id"
    [projectCompany]="projectContractor"
    [adminUser]="authUser$"
    [hasOptimaEnabled]="optima_meta && optima_meta.has_optima_source"
    [optimaAccessGroup]="optima_access_group"
    (onSave)="onBlackListingResponse($event)"
></manage-blacklisting>

<i-modal #inviteToInductionModal [title]="'Invite to '+ projectInfo.custom_field.induction_phrase_singlr" [rightPrimaryBtnTxt]="'Send Invite'" [rightPrimaryBtnDisabled]="inviteBtnStatus || isDuplicateEmails" (onClickRightPB)="sendInductionInvitation()">
        <form class="m-3" novalidate #inviteToInductionForm="ngForm">
            <div class="row mb-3" *ngFor="let item of inductionInvitationEmail; trackBy : trackByRowIndex; let i = index; let first = first">
                <div class="col-11 pr-0">
                    <input type="email" #emailInput="ngModel" 
                            class="form-control" [ngClass]="{ 'is-invalid': item.isDuplicate }"
                            placeholder="Email Address*"
                            [(ngModel)]="inductionInvitationEmail[i].email"
                            ng-value="inductionInvitationEmail[i].email"
                            name="{{'email_' + i}}" (ngModelChange)="checkForDuplicateEmails()"
                            required email [attr.autocomplete]="'off email-'+i">
                </div>
                <div class="col-1 d-flex justify-content-center align-items-center">
                    <i *ngIf="first" class="fa fa-plus-circle text-brandeis-blue float-right cursor-pointer"
                        (click)="addEmailField()"></i>
                    <span *ngIf="!first" class="material-symbols-outlined cursor-pointer text-danger font-larger" (click)="removeEmailField($event, i)">delete</span>
                </div>
                <div *ngIf="emailInput.invalid && (emailInput.dirty || emailInput.touched)" class="ml-3">
                    <p *ngIf="emailInput.errors?.required" class="text-danger">
                        Email address is required.
                    </p>
                    <p *ngIf="emailInput.errors?.email" class="text-danger">
                        Please provide a valid email address.
                    </p>
                </div>
                <div *ngIf="item.isDuplicate as isDuplicate" class="ml-3">
                    <p class="text-danger">
                        This email is already entered, please use a different email address.
                    </p>
                </div>
            </div>
        </form>
</i-modal>
<i-modal #viewInductionModalRef
         (onTitleClick)="onClickingViewerBack($event)"
         [titleClass]="(inductionViewerData.from === 'review') ? 'cursor-pointer': ''"
         [titleBtnIcon]="(inductionViewerData.from === 'review') ? {icon: 'chevron_left'}: null"
         [title]="inductionViewerData.ir?.creator_name" size="xl"
         rightPrimaryBtnTxt="Done"
         [showCancel]="false"
         modalBodyClass="p-0"
         (onClickRightPB)="$event.closeFn()"
         [leftSecondaryBtnIcon]="{icon: 'download', class: ''}"
         leftSecondaryBtnTxt="Download PDF"
         (onClickLeftSB)="openInductionForm(inductionViewerData.ir, 'pdf')">
    <ng-container *ngIf="inductionViewerData.ir && !processingLoader">
        <view-induction [inductionRequestData]="inductionViewerData.ir" [verifiableCompetencies]="verifiableCompetencies" [projectId]="projectId">
        </view-induction>
    </ng-container>
</i-modal>

<block-loader [show]="(processingLoader) || sendingInvitationInProgress" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
<block-loader [show]="(!dataLoading && loadingInProgress)" [showBlockBackdrop]="true" [alwaysInCenter]="true" [showBackdrop]="true" class="loading-spinner-centered"></block-loader>
<i-modal #content
         [title]="change_request._modal_title"
         size="lg"
         [leftSecondaryBtnIcon]="{icon: 'person', class: ''}"
         leftSecondaryBtnTxt="View Profile"
         (onClickLeftSB)="openInductionViewer($event)"
         [rightPrimaryBtnTxt]="(change_request.code !== null) ? ((change_request.code === STATUS_CODES.CHANGE_REQUESTED) ? 'Submit Request' : ((change_request.code === STATUS_CODES.REJECTED) ? 'Reject' : 'Approve')) : 'Submit'"
         [rightPrimaryBtnDisabled]="!changeRequestForm.valid"
         [rightPrimaryBtnClass]="'btn-primary'"
         (onClickRightPB)="notifyChangeRequest(changeRequestForm, modalLoader, $event)"
         (onCancel)="closeNotifyChangeRequestModal()">
    <ng-container sub-title>
        <span class="text-muted">(User ID {{change_request?.row?.user_ref}})</span>
    </ng-container>
        <form novalidate #changeRequestForm="ngForm">
        <div *ngIf="showModal">
            <div class="form-group">
                <div class="fw-500 mb-1 d-inline-block">
                    Profile Photo Verification
                    <div
                            ngbTooltip="The user's profile photo matches the photo from the CSCS Smart Check" placement="bottom"
                            *ngIf="(change_request.row.fr_similarity_result && change_request.row.fr_similarity_result.success)"
                            class="v-badge text-info">
                        <span class="material-symbols-outlined">ar_on_you</span> Verified
                    </div>
                    <div
                            ngbTooltip="The user's profile photo does not match the photo from the CSCS Smart Check" placement="bottom"
                            *ngIf="projectContractor.fr_setting?.enabled && projectContractor.cscs_status?.enabled && (!change_request.row.fr_similarity_result || !change_request.row.fr_similarity_result.success)"
                            class="v-badge text-secondary">
                        <span class="material-symbols-outlined">ar_on_you</span> Not verified
                    </div>
                </div>
                <div class="d-flex">
                    <div class="d-flex flex-fill align-items-center">
                        <div class="mr-3">
                            <profile-pic-viewer
                                    [photo]="change_request._profile_photo"
                            ></profile-pic-viewer>
                        </div>
                        <div class="d-flex flex-column">
                            <span class="fw-500">innDex profile photo</span>
                            <small class="fw-500">ID: {{ change_request?.row?.user_ref }}</small>
                        </div>
                    </div>

                    <div class="d-flex flex-fill align-items-center" *ngIf="!change_request._smart_check_photo.disabled">
                        <div class="mr-3">
                            <profile-pic-viewer
                                    [photo]="change_request._smart_check_photo"
                            ></profile-pic-viewer>
                        </div>
                        <div class="d-flex flex-column">
                            <span class="fw-500">CSCS Smartcheck</span>
                            <small class="fw-500">ID: {{ change_request?.row?.user_ref }}</small>
                        </div>
                    </div>

                </div>
                <div class="custom-control custom-checkbox pt-3" *ngIf="change_request._status_options && change_request._status_options.length">
                    <input type="checkbox" name="photo-reviewed"
                           [required]="(![STATUS_CODES.REJECTED, STATUS_CODES.CHANGE_REQUESTED].includes(change_request.code))" [(ngModel)]="change_request._photo_reviewed"
                           class="custom-control-input" id="photo-reviewed">
                    <label class="small custom-control-label" for="photo-reviewed">
                        <ng-container *ngIf="!change_request._smart_check_photo.disabled; else noSmartCheck">
                            I confirm that I’ve reviewed the profile photo and it matches the named worker, and that it also shows the same person as the CSCS Smart Check image.
                        </ng-container>
                        <ng-template #noSmartCheck>
                            I confirm that I’ve reviewed the profile photo and it matches the identity of the named user.
                        </ng-template>
                    </label>
                </div>
            </div>
            <div class="form-group" *ngIf="change_request.row.induction_slot && change_request.row.induction_slot.seconds">
                <label class="fw-500">{{ projectInfo.custom_field.induction_phrase_singlr }} Booking</label>
                <small class="d-block">
                    {{ unix(+change_request.row.induction_slot.seconds).format(displayDateTimeFormat) }}
                    {{ change_request.row.induction_slot.location ? '(' + change_request.row.induction_slot.location + ')' : ''}}
                </small>
            </div>

            <div class="form-group">
                <ng-container *ngIf="projectInfo.is_passport_require">
                    <label class="fw-500">Right to Work Check</label>
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" name="passport_required"
                               [(ngModel)]="change_request.passport_required"
                               [required]="(![STATUS_CODES.REJECTED, STATUS_CODES.CHANGE_REQUESTED].includes(change_request.code))"
                               #passport_required="ngModel"
                               class="custom-control-input" id="passport_required">
                        <label class="custom-control-label" for="passport_required">As part of the right to work
                            check, please tick the box to confirm that you have checked <b>{{
                                    change_request?.row?.first_name }}</b>'s passport/visa.</label>
                    </div>
                </ng-container>

                <ng-container *ngIf="projectContractor.rtw_status?.enabled">
                    <label class="fw-500">Right to Work Check</label>
                    <input type="hidden"
                           name="has-ppac-code" id="has-ppac-code"
                           [required]="(![STATUS_CODES.REJECTED, STATUS_CODES.CHANGE_REQUESTED].includes(change_request.code))"
                           [ngModel]="change_request.row?.rtw_doc_code" />
                    <!--
                    <input type="hidden"
                           name="ppac-verification-done" id="ppac-verification-done"
                           required
                           [ngModel]="change_request._need_rtw_check ? '' : 'done'" />
                           -->
                    <input type="hidden"
                           name="ppac-is-active" id="ppac-is-active"
                           [required]="(![STATUS_CODES.REJECTED, STATUS_CODES.CHANGE_REQUESTED].includes(change_request.code))"
                           [ngModel]="change_request._can_approve ? 'yes' : ''" />

                    <ppac-status-card
                            [showInput]="false"
                            [fromApproval]="true"
                            [authUser$]="change_request.row?.additional_data?.user_info || {}"
                            [induction_phrase]="projectInfo.custom_field.induction_phrase_singlr"
                            [parent_company]="projectInfo.parent_company"
                            [contractor_rtw_config]="projectContractor.rtw_status"
                            [ppac_data]="{doc_id: change_request.row?.rtw_doc_code, rtw_result: change_request.row?.rtw_check_result}"
                            [verificationPayload]="{project_ref: projectId, induction_id: change_request.row?.id}"
                            (docIdChanged)="change_request._need_rtw_check = true"
                            (onResult)="onRtwValidation($event)"
                    ></ppac-status-card>
                    <div *ngIf="!change_request.row?.rtw_doc_code" class="alert alert-danger" [hidden]="change_request.row?.rtw_doc_code">PPAC Code must be provided to approve.</div>
<!--                    <div *ngIf="change_request.row?.rtw_doc_code && change_request._need_rtw_check && change_request._can_approve" class="alert alert-danger" [hidden]="!change_request._need_rtw_check">PPAC Code must be re-verified before approving.</div>-->
                    <div *ngIf="change_request.row?.rtw_doc_code && !change_request._can_approve" class="alert alert-danger" [hidden]="change_request._can_approve">To approve worker {{projectInfo.custom_field.induction_phrase_singlr}}, the PPAC status must be: <b [innerHTML]="(projectContractor.rtw_status.induction_approval_on || []).join(' or ')">Active</b>.</div>
                </ng-container>

                <ng-container *ngIf="projectContractor.ccl_status?.enabled">
                    <div class="mt-4">
                        <ccl-status-tile [config]="cclStatusConfig"></ccl-status-tile>
                    </div>
                </ng-container>
            </div>

            <div class="form-group" *ngIf="change_request.show_change_log && change_request.recent_changes.length">
                <label class="fw-500">Change Log</label>
                <induction-change-log [activeIds]="activeIds" [changes]="change_request.recent_changes" [tz]="projectInfo?.custom_field?.timezone"></induction-change-log>
            </div>

            <div class="form-group">
                <project-comments
                    [projectComments]="inductionComments"
                    [currentUser]="authUser$"
                    [isFromChangeLog]="true"
                ></project-comments>
            </div>

            <div class="form-group inline-selection" *ngIf="change_request._status_options && change_request._status_options.length">
                <label class="fw-500">Select Action <small class="required-asterisk">*</small></label>
                <ng-container *ngFor="let status_option of change_request._status_options">
                    <div class="custom-control custom-radio border rounded cursor-pointer" (click)="change_request.code = status_option.code">
                        <input type="radio"
                               [id]="'customRadio1-'+status_option.code"
                               name="status"
                               [(ngModel)]="change_request.code"
                               [value]="status_option.code"
                               required
                               (change)="onStatusSelection($event)"
                               class="custom-control-input">
                        <label class="custom-control-label" [for]="'customRadio1-'+status_option.code">{{ status_option.label }}</label>
                    </div>
                </ng-container>
            </div>

            <div class="form-group" *ngIf="change_request.code !== null">
                <label class="fw-500">Comments <small class="required-asterisk" *ngIf="[STATUS_CODES.REJECTED, STATUS_CODES.CHANGE_REQUESTED].indexOf(change_request.code) !== -1">*</small></label>
                <input type="hidden" name="induction_request_id" id="row_id"
                       [(ngModel)]="change_request.row"/>
                <input type="hidden" name="change-status-code" required
                       [ngModel]="change_request.code"/>
                <textarea class="form-control" name="note"
                          [required]="[STATUS_CODES.REJECTED, STATUS_CODES.CHANGE_REQUESTED].indexOf(change_request.code) !== -1"
                          [(ngModel)]="change_request.note"
                          placeholder="Add a detailed comment to notify the user"></textarea>
            </div>

            <div class="mt-4" *ngIf="change_request.code === STATUS_CODES.APPROVED">
                <ngb-accordion id="access-admin-accordion" class="i-accordion" [activeIds]="activeAccessAccordion">
                    <ngb-panel class="toggle-admin-access" id="accordion-1">
                        <ng-template ngbPanelHeader let-opened="opened">
                            <button ngbPanelToggle class="btn p-0" (click)="toggleAccordion()">
                                <div class="w-100 d-flex justify-content-between">
                                    <div>
                                        <p class="m-0 fw-600">Give admin access/permission</p>
                                    </div>
                                    <div>
                                        <i [ngClass]="opened ? 'fas fa-angle-up' : 'fas fa-angle-down'"></i>
                                    </div>
                                </div>
                            </button>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div>
                                <table class="table table-bordered mb-0 border-0 medium-font">
                                    <tbody>
                                    <td class="text-center" style="width: 45%;">
                                        <ng-select class="mt-1 d-inline-block"
                                                   [ngStyle]="{'width': (projectInfo._limited_access_change && (!isProjectAdminV1 && !projectInfo._company_admin)) ? '93.5%' : '100%' }"
                                                   [clearable]="false"
                                                   appendTo="body"
                                                   placeholder="Select Access Level"
                                                   #accessLevel
                                                   [name]="'acccessLevel'" [ngModel]="returnAccessLevelValue(adminAccessData)"
                                                   (change)="onAccessLevelSelect($event)">
                                            <ng-container *ngFor="let t of acccessLevelOpts">
                                                <ng-option [value]="t.designation" [disabled]="(!['', designations.DELIVERY_MANAGER].includes(t.designation) && (projectInfo._limited_access_change && (!isProjectAdminV1 && !projectInfo._company_admin)))">
                                                    <span *ngIf="((designations.DELIVERY_MANAGER != t.designation) && (projectInfo._limited_access_change && (!isProjectAdminV1 && !projectInfo._company_admin))); else labelOnly" ngbTooltip="Only a company level admin can assign this admin access level">
                                                        {{ t.label }}
                                                    </span>
                                                    <ng-template #labelOnly>
                                                        <span>{{ t.label }}</span>
                                                    </ng-template>
                                                </ng-option>
                                            </ng-container>
                                        </ng-select>
                                        <span *ngIf="projectInfo._limited_access_change && (!isProjectAdminV1 && !projectInfo._company_admin)" class="ml-1 material-symbols-outlined small-font d-inline-block" ngbTooltip="Only a company level admin can assign Full Access, Restricted Access, Customizable admin access levels.">info</span>
                                    </td>
                                    <td class="text-center p-3">
                                        <div *ngIf="adminAccessData.designation.includes(designations.FULL_ACCESS)" class="d-flex justify-content-between">
                                            <div class="custom-control custom-checkbox d-inline-block">
                                                <input type="checkbox" class="custom-control-input"
                                                       [id]="'inductor_role'"
                                                       [disabled]="(projectInfo._limited_access_change && (!isProjectAdminV1 && !projectInfo._company_admin))"
                                                       (change)="changeModel($event, designations.INDUCTOR)" [name]="'inductor_role'"
                                                       value="inductor" [checked]="adminAccessData.designation.includes(designations.INDUCTOR)">
                                                <label class="custom-control-label" [for]="'inductor_role'">Inductor</label>
                                            </div>
                                            <div class="custom-control custom-checkbox d-inline-block ml-3">
                                                <input type="checkbox" class="custom-control-input"
                                                       [id]="'nominated_role'"
                                                       [disabled]="(projectInfo._limited_access_change && (!isProjectAdminV1 && !projectInfo._company_admin))"
                                                       (change)="changeModel($event, designations.NOMINATED)" [name]="'nominated_role'"
                                                       value="nominated" [checked]="adminAccessData.designation.includes(designations.NOMINATED)">
                                                <label class="custom-control-label" [for]="'nominated_role'">Email Notifications</label>
                                            </div>
                                            <div class="d-flex">
                                                <button title="Customize User Access" class="btn btn-sm mr-1" (click)="showCustomizeAccessModal('notification')">
                                                    <i class="fa fa-edit"> </i>
                                                </button>
                                            </div>
                                        </div>
                                        <div *ngIf="adminAccessData.designation.includes(designations.CUSTOM)">
                                            <button title="Customize User Access" class="btn btn-sm mr-1" (click)="showCustomizeAccessModal()">
                                                <i class="fa fa-edit mr-2"> </i> Customize Access
                                            </button>
                                        </div>
                                    </td>
                                    </tbody>
                                </table>
                            </div>
                            <div *ngIf="procorePermissionTemplates.length" class="list-group-item" style="padding: 10px 12.5px;">
                                <p class="mb-1 mt-2 font-weight-bold"><img class="" [src]="AssetsUrlSiteAdmin.Procore_Logo_FC_Black_CMYK_1_1" alt="Logo"  style="max-height: 20px;max-width: 98px;margin-top: -4px;margin-left: 2px;" /> Permissions</p>
                                <div>
                                    <ng-select class="mt-1"
                                               style="width: 38%;" [clearable]="false"
                                               appendTo="body"
                                               placeholder="Select Procore Permission" [items]="procorePermissionTemplates"
                                               bindValue="id" bindLabel="name" [(ngModel)]="procorePermissionId"
                                               [name]="'procorePermission'">
                                    </ng-select>
                                </div>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
            </div>
        </div>
        </form>
    <block-loader [show]="(false)" #modalLoader [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>
</i-modal>
<duplicate-photo-review-modal
        #reviewDuplicatePhoto
        (saved)="onInductionRCDone($event)"
></duplicate-photo-review-modal>

<ng-template #createBadgeModal let-c="close" let-d="dismiss">
    <div class="modal-body">
        <form novalidate #addOptimaForm="ngForm">
            <!-- This is when API is not called -->
            <div *ngIf="!optima_badge_number">
                <p class="text-center">Assigning Badge number, please wait.</p>
            </div>
            <!-- This is when API is called & has responded -->
            <div *ngIf="optima_badge_number" class="text-center">
                <p class="text-success">Success!</p>
                <p [title]="'Badge number: ' + optima_badge_number"><b>{{optima_modal_row?.name}}</b><b *ngIf="!optima_modal_row?.name">{{ optima_modal_row?.user_ref?.first_name }} {{ optima_modal_row?.user_ref?.last_name }}</b>  has been Approved.</p>
            </div>
        </form>
    </div>
    <block-loader [show]="(createBadgeInProgress)" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>

    <!-- This is when API is called & has responded -->
    <div class="modal-footer" *ngIf="optima_badge_number">
        <button type="button" class="btn btn-primary m-auto" (click)="d('Cross click')">Close</button>
    </div>
</ng-template>
<ng-template #defaultSuccessModal let-c="close" let-d="dismiss">
    <div class="modal-body">
        <div class="text-center">
            <p class="text-success">Success!</p>
            <p><b>{{optima_modal_row?.name}}</b><b *ngIf="!optima_modal_row?.name">{{ optima_modal_row?.user_ref?.first_name }} {{ optima_modal_row?.user_ref?.last_name }}</b>  has been Approved.</p>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-primary m-auto" (click)="d('Cross click')">Close</button>
    </div>
</ng-template>
<ng-template #assignBadgeNumberModal let-c="close" let-d="dismiss">
    <div class="modal-header">
        <h4 class="modal-title">Assign Badge Number</h4>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <form novalidate #addBadgeNumberForm="ngForm">
            <div class="form-group">
                <label>Search Badge Number</label>
                <div class="input-group">
                    <input type="text" class="form-control" [(ngModel)]="optima_badge_search.badge_number"
                           name="badge_number"
                           placeholder="Enter Badge Number"/>
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="button" (click)="searchBadge()">Search Badge
                        </button>
                    </div>
                </div>
            </div>
        </form>

        <div class="row"
             *ngIf="badge_detail && badge_detail.badgeOwnerStatus && badge_detail.induction_requests">
            <div class="col-12 font-weight-bold">This badge is already assigned to someone else in the project.</div>
        </div>

        <div class="row"
             *ngIf="badge_detail && badge_detail.badgeOwnerStatus && !badge_detail.induction_requests">
            <div class="col-sm-9">
                <div class="col-8 col-sm-8">
                    <strong>Name:</strong> {{ badge_detail.badgeOwnerFirstName }} {{
                    badge_detail.badgeOwnerLastName }}
                </div>
                <div class="col-4 col-sm-8">
                    <strong>Status:</strong> {{ badge_detail.badgeOwnerStatus }}
                </div>
            </div>
        </div>

        <div class="modal-footer justify-content-between">
            <button type="button" class="btn btn-outline-danger" (click)="initiateBadgeCreation(optima_modal_row, c)"> Generate New Badge</button>
            <button type="button" class="btn btn-outline-primary"
                    [disabled]="!addBadgeNumberForm.valid || !badge_detail.badgeOwnerFirstName || badge_detail.induction_requests"
                    (click)="assignBadgeRequest(addBadgeNumberForm, c)">Assign Badge
            </button>
        </div>
    </div>
    <block-loader [show]="(searchBadgeInProgress)" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>

    <!-- This is when API is called & has responded -->
    <div class="modal-footer" *ngIf="optima_badge_number">
        <button type="button" class="btn btn-primary m-auto" (click)="d('Cross click')">Close</button>
    </div>
</ng-template>

<i-modal
    #viewBadgeInfoModal
    [size]="'lg'"
    [showCancel]="false"
    [leftSecondaryBtnTxt]="(optima_meta && optima_meta.optima_online && optima_meta.allow_cards && badge_information.badge) ? 'Link Access Card' : ''"
    [leftSecondaryBtnIcon]="{icon: 'id_card'}"
    (onClickLeftSB)="openAccessCardLinkModal(optima_modal_row, badge_information.badge.printsModel)"

    [rightSecondaryBtnTxt]="(badge_information && !badge_information.has_enrolment && badge_information._remote_enrolment_enabled && badge_information._enrolment_mode) ? 'Back' : 'Cancel'"
    (onClickRightSB)="onBadgeInfoCancelClick($event)"

    rightPrimaryBtnTxt="Done"
    [rightPrimaryBtnDisabled]="badge_information && !badge_information.has_enrolment"
    (onClickRightPB)="$event.closeFn()"

    [title]="'Enrolment & Access Control:'">
    <ng-container sub-title>
        <span class="text-muted">{{ optima_modal_row?.name }} (User ID {{optima_modal_row?.user_ref}})</span>
    </ng-container>
    <div *ngIf="badge_information.badge">
        <div>
            <form novalidate #toggleAccessGForm="ngForm">
                <div class="fw-500 text-center mb-2" [title]="'Access for badge # '+ optima_modal_row.optima_badge_number">Access</div>
                <div class="form-group d-flex">
                    <div class="flex-grow-1">
                        <ng-select required
                                   class="dropdown-list"
                                   appendTo="body"
                                   name="access_group"
                                   placeholder="Access group"
                                   bindValue="id"
                                   bindLabel="libelle"
                                   [(ngModel)]="badge_access_info.groupId"
                                   [items]="optima_access_group"
                        ></ng-select>
                    </div>

                    <div class="d-flex align-items-end px-3">
                        <button type="button" class="btn btn-link"
                                [disabled]="!badge_access_info.groupId"
                                (click)="toggleOptimaAccess($event, optima_modal_row, null, 'selectbox')">
                            Apply
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <hr />
        <div>
            <div class="fw-500 text-center mb-2">Enrolment</div>
            <div class="row enrolment-cards mb-2" *ngIf="!badge_information.has_enrolment">
                <ng-container *ngIf="!badge_information._enrolment_mode && badge_information._remote_enrolment_enabled">
                    <div class="col-sm-6">
                        <div class="card">
                            <div class="card-body">
                                <div class="card-title large-font fw-600">Profile Photo Enrolment</div>
                                <div class="card-text">
                                    Enrol using the users profile photo they submitted during the induction process.
                                </div>
                                <button class="btn btn-secondary float-right" (click)="badge_information._enrolment_mode = 'remote'">Select</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="card">
                            <div class="card-body">
                                <div class="card-title large-font fw-600">Reader Enrolment</div>
                                <div class="card-text">
                                    Enrol workers directly at a facial recognition reader with a live face scan.
                                </div>
                                <button class="btn btn-secondary float-right" (click)="badge_information._enrolment_mode = 'manual'">Select</button>
                            </div>
                        </div>
                    </div>
                </ng-container>
                <ng-container *ngIf="badge_information._enrolment_mode === 'manual'">
                    <div class="col-12 text-center">
                        <div>
                            <ng-select
                                    class="dropdown-list"
                                    placeholder="Enrolment Reader"
                                    appendTo="body"
                                    [(ngModel)]="fingerprint_enrolment_request.readerId"
                                    name="facialEnrolementReader"
                                    [items]="optima_readers"
                                    bindLabel="libelle"
                                    bindValue="id"
                                    id="facialReader">
                            </ng-select>
                        </div>
                        <button
                                class="btn btn-secondary mt-2"
                                [disabled]="!fingerprint_enrolment_request.readerId"
                                (click)="submitFingerprintEnrolement()"
                        >Start enrolment</button>
                    </div>
                </ng-container>
                <ng-container *ngIf="badge_information._remote_enrolment_enabled && badge_information._enrolment_mode === 'remote'">
                    <div class="d-flex justify-content-center w-100">
                        <div class="text-center">
                            <div class="mb-1">
                                <profile-pic-viewer
                                        [photo]="{
                                            title: 'Profile Photo',
                                            thumbnail: badge_information._enrolment_face_url,
                                            full_img: badge_information._enrolment_face_url,
                                            object_fit: 'contain',
                                            disabled: !badge_information._enrolment_face_url
                                        }"
                                ></profile-pic-viewer>
                            </div>
                            <div class="d-flex flex-column mb-1">
                                <span class="fw-500">innDex profile photo</span>
                                <small class="fw-500">Uploaded by user</small>
                            </div>
                            <button
                                    class="btn btn-secondary float-right mt-2"
                                    [disabled]="!badge_information._enrolment_face_url"
                                    (click)="submitRemoteEnrolment()"
                            >Start enrolment</button>
                        </div>
                    </div>
                </ng-container>
            </div>

            <div class="enrolment-status d-flex justify-content-center mb-2" *ngIf="badge_information.has_enrolment">
                <div class="text-center">
                    <ng-container *ngIf="badge_information.has_remote_enrolment">
                        <div class="mb-1">
                            <profile-pic-viewer
                                [photo]="badge_information._remote_enrolled_photo"
                            ></profile-pic-viewer>
                        </div>
                        <div class="d-flex flex-column mb-1">
                            <span class="fw-500">Enrollment Photo</span>
                            <!--<small class="fw-500">Uploaded by user</small>-->
                        </div>
                    </ng-container>

                    <div class="d-flex my-2">
                        <span class="material-symbols-outlined text-success">check_circle</span>
                        User successfully enrolled
                    </div>
                    <button class="btn btn-outline-primary" (click)="deleteFingerPrintEnrolment(0)">Remove enrolment</button>
                </div>
            </div>
        </div>
    </div>
    <button class="btn-sm btn pl-0 d-none" (click)="deleteBadgeNumber(optima_modal_row)">
        <span class="material-symbols-outlined align-bottom text-danger">
            delete
        </span>
        Delete the user badge number
    </button>
</i-modal>

<!--<ng-template #fingerprintEnrolmentModal let-c="close" let-d="dismiss">
    <div class="modal-header">
        <h5 class="modal-title">Enrolment and Access Control - Record #{{ optima_modal_row?.record_id }}</h5>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <form novalidate #fingerprintEnrolmentForm="ngForm">
            <p class="text-center border-bottom pb-3">
                <strong>Access - <span>{{optima_modal_row?.name}}</span><span *ngIf="!optima_modal_row?.name">{{ optima_modal_row?.user_ref?.first_name }} {{ optima_modal_row?.user_ref?.last_name }}</span></strong>
            </p>
            <div class="form-group row">
                <label class="col-sm-4 pr-0 col-form-label">Remove Access:</label>
                <div class="custom-control custom-checkbox mt-2 ml-3">
                    <input type="checkbox" class="custom-control-input" id="toggleAccess" [checked]="(badge_access_info.groupName == 'Deny Access')" (click)="toggleOptimaAccess($event, optima_modal_row, c, 'checkbox')">
                    <label class="custom-control-label" for="toggleAccess"></label>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-4 pr-0 col-form-label">Access Group:</label>
                <div class="col-sm-6">
                    <ng-select [items]="optima_access_group" bindLabel="libelle" bindValue="id" [(ngModel)]="badge_access_info.groupId" name="access_group" class="form-control" #accessGroup="ngModel" [disabled]="(badge_access_info.groupName == 'Deny Access')" (change)="toggleOptimaAccess($event, optima_modal_row, c, 'selectbox')">
                    </ng-select>
                </div>
            </div>

            <p class="text-center border-bottom border-top pb-3 pt-3">
                <strong><ng-container *ngIf="!has_facial_enrolment_type">Fingerprint</ng-container>
                    <ng-container *ngIf="has_facial_enrolment_type">Facial</ng-container> Enrolment - <span>{{optima_modal_row?.name}}</span><span *ngIf="!optima_modal_row?.name">{{ optima_modal_row?.user_ref?.first_name }} {{ optima_modal_row?.user_ref?.last_name }}</span></strong>
            </p>

            <div class="form-group row" *ngIf="(!has_facial_enrolment_type || !badge_information?.usagerResponseModelApi?.printsModel?.faceEnrolled)">
                <label for="fingerprintEnrolementReader" class="col-sm-2 col-form-label">Reader:</label>
                <div class="col-sm-6 ml-4">
                    <select [(ngModel)]="fingerprint_enrolment_request.readerId" [required]="!fingerprint_enrolment_request.readerId" name="fingerprintEnrolementReader" class="form-control" id="fingerprintEnrolementReader" #fingerprintEnrolementReader="ngModel">
                        <option *ngFor="let t of optima_readers"
                                [ngValue]="t.id">
                            {{ t.name || t.libelle }}
                        </option>
                    </select>
                    <div class="alert alert-danger" [hidden]="(fingerprintEnrolementReader.valid)">Please select an reader.</div>
                </div>
            </div>

            <div class="form-group row" *ngIf="optima_meta && optima_meta.has_optima_source && !has_facial_enrolment_type">
                <div class="col-sm-6 d-flex">
                    <label class="col-sm-5 pl-0 pr-0 col-form-label">Left Hand:</label>
                    <div class="col-sm-4">
                        <svg id="leftHandFingerId" [ngClass]="{'svgGreen': ((fingerprint_enrolment_request.fingerprintIndex == leftHandFingerIndex) || leftHandEnroled), 'svgRed': ((fingerprint_enrolment_request.fingerprintIndex == leftHandFingerIndex) && leftHandEnroledFailed), 'svgBlack': (fingerprint_enrolment_request.fingerprintIndex == leftHandFingerIndex), fingerPrint: true}" (click)="selectFinger('leftHandFinger')" width="32px" enable-background="new 0 0 24 24" viewBox="0 0 24 24"  xmlns="http://www.w3.org/2000/svg">
                            <path d="m18.741 6.58c-.178 0-.356-.062-.499-.19-1.728-1.541-3.945-2.39-6.242-2.39s-4.514.849-6.242 2.39c-.309.274-.784.248-1.059-.061-.276-.309-.249-.784.061-1.059 2.003-1.786 4.574-2.77 7.24-2.77s5.237.984 7.24 2.77c.31.276.337.75.061 1.059-.148.166-.354.251-.56.251z"/>
                            <path d="m19.751 10.391c-.239 0-.474-.114-.619-.326-1.626-2.368-4.292-3.782-7.132-3.782s-5.506 1.414-7.132 3.783c-.234.341-.699.431-1.043.194-.341-.234-.428-.701-.193-1.042 1.905-2.778 5.033-4.435 8.368-4.435s6.463 1.658 8.368 4.434c.234.341.147.808-.193 1.042-.13.09-.279.132-.424.132z"/>
                            <path d="m10.117 21.5c-.19 0-.381-.072-.527-.216-.132-.13-3.235-3.23-3.235-6.24 0-3.141 2.532-5.696 5.646-5.696s5.646 2.555 5.646 5.696c0 .414-.336.75-.75.75s-.75-.336-.75-.75c0-2.313-1.859-4.196-4.146-4.196s-4.146 1.882-4.146 4.196c0 2.382 2.763 5.146 2.79 5.173.294.292.297.766.006 1.061-.147.148-.34.222-.534.222z"/>
                            <path d="m13.883 21.5c-1.478 0-4.902-2.739-5.233-5.79-.118-1.1.221-2.187.93-2.981.628-.704 1.481-1.094 2.403-1.099.903-.005 1.754.345 2.396.984.65.646 1.008 1.509 1.008 2.429v.38c0 .845.678 1.533 1.51 1.533s1.51-.688 1.51-1.533v-.133c0-3.544-2.626-6.493-5.978-6.711-1.791-.122-3.479.491-4.779 1.709-1.305 1.223-2.054 2.956-2.054 4.754 0 1.503.521 2.967 1.465 4.123.262.321.214.793-.106 1.055-.321.262-.792.215-1.056-.106-1.162-1.423-1.803-3.225-1.803-5.072 0-2.212.922-4.344 2.528-5.849 1.608-1.506 3.713-2.253 5.902-2.112 4.139.271 7.38 3.876 7.38 8.208v.133c0 1.672-1.351 3.033-3.01 3.033s-3.01-1.36-3.01-3.033v-.38c0-.518-.201-1.003-.565-1.366-.354-.353-.823-.547-1.32-.547-.004 0-.007 0-.01 0-.63.003-1.05.327-1.292.598-.421.472-.629 1.152-.557 1.821.248 2.296 3.011 4.452 3.741 4.452.414 0 .75.336.75.75s-.336.75-.75.75z"/>
                            <path d="m16.096 20.359c-2.216 0-4.893-2.541-4.893-5.696 0-.414.336-.75.75-.75s.75.336.75.75c0 2.316 2.053 4.196 3.393 4.196.414 0 .75.336.75.75s-.336.75-.75.75z"/>
                            <path d="m23.25 8.348c-.414 0-.75-.336-.75-.75v-4.848c0-.689-.561-1.25-1.25-1.25h-4.848c-.414 0-.75-.336-.75-.75s.336-.75.75-.75h4.848c1.517 0 2.75 1.234 2.75 2.75v4.848c0 .414-.336.75-.75.75z"/>
                            <path d="m.75 8.348c-.414 0-.75-.336-.75-.75v-4.848c0-1.516 1.233-2.75 2.75-2.75h4.848c.414 0 .75.336.75.75s-.336.75-.75.75h-4.848c-.689 0-1.25.561-1.25 1.25v4.848c0 .414-.336.75-.75.75z"/>
                            <path d="m21.25 24h-4.848c-.414 0-.75-.336-.75-.75s.336-.75.75-.75h4.848c.689 0 1.25-.561 1.25-1.25v-4.848c0-.414.336-.75.75-.75s.75.336.75.75v4.848c0 1.516-1.233 2.75-2.75 2.75z"/>
                            <path d="m7.598 24h-4.848c-1.517 0-2.75-1.234-2.75-2.75v-4.848c0-.414.336-.75.75-.75s.75.336.75.75v4.848c0 .689.561 1.25 1.25 1.25h4.848c.414 0 .75.336.75.75s-.336.75-.75.75z"/>
                        </svg>
                        <input type="hidden" [(ngModel)]="fingerprint_enrolment_request.fingerprintIndex" name="left_hand_finger" [required]="!fingerprint_enrolment_request.fingerprintIndex" #leftHandFinger="ngModel">
                    </div>
                </div>
                <div class="col-sm-6 d-flex">
                    <label class="col-sm-5 pl-0 pr-0 col-form-label">Right Hand:</label>
                    <div class="col-sm-4">
                        <svg id="rightHandFingerId" [ngClass]="{'svgGreen': ((fingerprint_enrolment_request.fingerprintIndex == rightHandFingerIndex) || rightHandEnroled), 'svgRed': ((fingerprint_enrolment_request.fingerprintIndex == rightHandFingerIndex) && rightHandEnroledFailed), 'svgBlack': (fingerprint_enrolment_request.fingerprintIndex == rightHandFingerIndex), fingerPrint: true}" (click)="selectFinger('rightHandFinger')" width="32px" enable-background="new 0 0 24 24" viewBox="0 0 24 24"  xmlns="http://www.w3.org/2000/svg">
                            <path d="m18.741 6.58c-.178 0-.356-.062-.499-.19-1.728-1.541-3.945-2.39-6.242-2.39s-4.514.849-6.242 2.39c-.309.274-.784.248-1.059-.061-.276-.309-.249-.784.061-1.059 2.003-1.786 4.574-2.77 7.24-2.77s5.237.984 7.24 2.77c.31.276.337.75.061 1.059-.148.166-.354.251-.56.251z"/>
                            <path d="m19.751 10.391c-.239 0-.474-.114-.619-.326-1.626-2.368-4.292-3.782-7.132-3.782s-5.506 1.414-7.132 3.783c-.234.341-.699.431-1.043.194-.341-.234-.428-.701-.193-1.042 1.905-2.778 5.033-4.435 8.368-4.435s6.463 1.658 8.368 4.434c.234.341.147.808-.193 1.042-.13.09-.279.132-.424.132z"/>
                            <path d="m10.117 21.5c-.19 0-.381-.072-.527-.216-.132-.13-3.235-3.23-3.235-6.24 0-3.141 2.532-5.696 5.646-5.696s5.646 2.555 5.646 5.696c0 .414-.336.75-.75.75s-.75-.336-.75-.75c0-2.313-1.859-4.196-4.146-4.196s-4.146 1.882-4.146 4.196c0 2.382 2.763 5.146 2.79 5.173.294.292.297.766.006 1.061-.147.148-.34.222-.534.222z"/>
                            <path d="m13.883 21.5c-1.478 0-4.902-2.739-5.233-5.79-.118-1.1.221-2.187.93-2.981.628-.704 1.481-1.094 2.403-1.099.903-.005 1.754.345 2.396.984.65.646 1.008 1.509 1.008 2.429v.38c0 .845.678 1.533 1.51 1.533s1.51-.688 1.51-1.533v-.133c0-3.544-2.626-6.493-5.978-6.711-1.791-.122-3.479.491-4.779 1.709-1.305 1.223-2.054 2.956-2.054 4.754 0 1.503.521 2.967 1.465 4.123.262.321.214.793-.106 1.055-.321.262-.792.215-1.056-.106-1.162-1.423-1.803-3.225-1.803-5.072 0-2.212.922-4.344 2.528-5.849 1.608-1.506 3.713-2.253 5.902-2.112 4.139.271 7.38 3.876 7.38 8.208v.133c0 1.672-1.351 3.033-3.01 3.033s-3.01-1.36-3.01-3.033v-.38c0-.518-.201-1.003-.565-1.366-.354-.353-.823-.547-1.32-.547-.004 0-.007 0-.01 0-.63.003-1.05.327-1.292.598-.421.472-.629 1.152-.557 1.821.248 2.296 3.011 4.452 3.741 4.452.414 0 .75.336.75.75s-.336.75-.75.75z"/>
                            <path d="m16.096 20.359c-2.216 0-4.893-2.541-4.893-5.696 0-.414.336-.75.75-.75s.75.336.75.75c0 2.316 2.053 4.196 3.393 4.196.414 0 .75.336.75.75s-.336.75-.75.75z"/>
                            <path d="m23.25 8.348c-.414 0-.75-.336-.75-.75v-4.848c0-.689-.561-1.25-1.25-1.25h-4.848c-.414 0-.75-.336-.75-.75s.336-.75.75-.75h4.848c1.517 0 2.75 1.234 2.75 2.75v4.848c0 .414-.336.75-.75.75z"/>
                            <path d="m.75 8.348c-.414 0-.75-.336-.75-.75v-4.848c0-1.516 1.233-2.75 2.75-2.75h4.848c.414 0 .75.336.75.75s-.336.75-.75.75h-4.848c-.689 0-1.25.561-1.25 1.25v4.848c0 .414-.336.75-.75.75z"/>
                            <path d="m21.25 24h-4.848c-.414 0-.75-.336-.75-.75s.336-.75.75-.75h4.848c.689 0 1.25-.561 1.25-1.25v-4.848c0-.414.336-.75.75-.75s.75.336.75.75v4.848c0 1.516-1.233 2.75-2.75 2.75z"/>
                            <path d="m7.598 24h-4.848c-1.517 0-2.75-1.234-2.75-2.75v-4.848c0-.414.336-.75.75-.75s.75.336.75.75v4.848c0 .689.561 1.25 1.25 1.25h4.848c.414 0 .75.336.75.75s-.336.75-.75.75z"/>
                        </svg>
                        <input type="hidden" name="right_hand_finger" [(ngModel)]="fingerprint_enrolment_request.fingerprintIndex" [required]="!fingerprint_enrolment_request.fingerprintIndex" #rightHandFinger="ngModel">
                    </div>
                </div>
            </div>
            <div class="form-group row" *ngIf="!has_facial_enrolment_type">
                <div class="col-sm-12 text-center">
                    <button type="button" class="btn btn-outline-primary float-center"
                            [disabled]="!fingerprintEnrolmentForm.valid"
                            (click)="submitFingerprintEnrolement(c)">Start Enrolment
                    </button>
                </div>
            </div>

            <div class="form-group row" *ngIf="has_facial_enrolment_type">
                <div class="col-sm-12 text-center" *ngIf="!badge_information?.usagerResponseModelApi?.printsModel?.faceEnrolled">
                    <button type="button" class="btn btn-outline-primary float-center"
                            [disabled]="!fingerprintEnrolmentForm.valid"
                            (click)="submitFingerprintEnrolement(c)">Start Enrolment
                    </button>
                </div>
                <div class="col-sm-12 text-center" *ngIf="badge_information?.usagerResponseModelApi?.printsModel?.faceEnrolled">
                    <h6 [title]="'Badge # '+optima_modal_row?.optima_badge_number"> <i class="fas fa-check text-success"></i> User Successfully Enrolled</h6>
                    <button type="button" class="btn btn-outline-primary float-center"
                            (click)="deleteFingerPrintEnrolment(0)">Remove Enrolment
                    </button>
                </div>
            </div>
        </form>

        <div class="border-top pt-3 pb-3">
            <button class="btn-sm btn pl-0 d-none" (click)="deleteBadgeNumber(optima_modal_row, c)">
                <span class="material-symbols-outlined text-danger">
                    delete
                </span>
                Delete the user badge number
            </button>
            <button class="btn-sm btn pl-0 btn-link text-primary"
                    *ngIf="optima_meta && optima_meta.optima_online && optima_meta.allow_cards && badge_information?.usagerResponseModelApi"
                    (click)="openAccessCardLinkModal(optima_modal_row, c, badge_information.usagerResponseModelApi?.printsModel)">
                <i class="fa fa-address-card"></i>
                Link Access Card
            </button>
            <button type="button" class="btn btn-outline-primary float-right"
                    (click)="d('Cross click')">Update
            </button>
        </div>
    </div>
    <block-loader [show]="(searchBadgeInProgress)" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>
</ng-template>-->

<ng-template #accessCardLinkModal let-c="close" let-d="dismiss">
    <div class="modal-header">
        <h5 class="modal-title">Link Access Card - Record #{{ selectedInductionRef.record_id }}</h5>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <form novalidate #accessCardLinkForm="ngForm">
            <div class="form-group">
                <label class="d-block">Select an Event</label>
                <ng-select [items]="recentUnrecognizedEvents"
                           class="dropdown-list"
                           appendTo="body"
                           name="event_ref"
                           bindLabel="badge_number"
                           [bindValue]="'badge_number'"
                           [loading]="accessCardsLoading"
                           required class="events-selector"
                           placeholder="Select recent access card event"
                           [(ngModel)]="selectedAccessCardNo">
                    <ng-template ng-label-tmp ng-option-tmp let-item="item">
                        <span>{{ unix(+item.event_date_time).format(displayDateTimeFormat)}}</span>
                        <span class="badge badge-secondary float-right">Badge # {{ item.badge_number }}</span>
                        <br />
                        <small><b>Reader:</b> {{item.reader_name}}</small>
                        <small class="float-right"><b>Unit:</b> {{ item.unit_name }}</small>
                    </ng-template>
                </ng-select>
                <small class="form-text text-muted" *ngIf="selectedAccessCardNo">
                    You are about to associated induction from <b>{{selectedInductionRef.name}}</b> with above selected Access Card.
                    <ng-container *ngIf="selectedInductionRef.has_enrolment">It will also remove previous enrolment.</ng-container>
                </small>
            </div>

        </form>
    </div>
    <block-loader [show]="(accessCardsLoading)" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>
    <div class="modal-footer">
        <button type="button" class="btn btn-outline-primary" (click)="d('Cross click')">Close</button>
        <button type="button" class="btn btn-primary" [disabled]="accessCardLinkForm.invalid" (click)="switchInductionBadgeWith(c)">Save</button>
    </div>
</ng-template>
<block-loader [show]="modalActionInProgress" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>

<i-modal #manageMedicationModalRef [title]="'Medications - ' + medication_edit_row?.additional_data?.user_info?.name" size="md" cancelBtnText="Close"
    (onClickRightPB)="saveInductionMedications($event)" rightPrimaryBtnTxt="Save" [rightPrimaryBtnDisabled]="!healthAssessModalForm.valid || medicationValid()">
        <form novalidate #healthAssessModalForm="ngForm">
            <div class="form-group row">
                <label class="col-md-8 col-form-label font-weight-bold form-control-label">Any new reportable medical conditions?</label>
                <div class="col-md-4 form-inline mb-4 d-flex justify-content-md-end">
                    <div class="custom-control custom-radio mr-3">
                        <input type="radio" class="custom-control-input" name="rmc" id="rmc-yes"
                               required [(ngModel)]="medication_edit_row.reportable_medical_conditions"  [value]="'yes'" />
                        <label class="custom-control-label" for="rmc-yes"> Yes</label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" class="custom-control-input" name="rmc" id="rmc-no"
                               required [(ngModel)]="medication_edit_row.reportable_medical_conditions"  [value]="'no'" />
                        <label class="custom-control-label" for="rmc-no"> No </label>
                    </div>
                </div>
            </div>
            <div class="form-group row" *ngIf="(!isDisabled('reportable_medical_conditions'))">
                <label class="col-md-5 col-form-label form-control-label">Please provide detail</label>
                <div class="col-md-7">
                        <textarea name="rmc_detail" style="min-height: 100px;"
                                  [required]="!isDisabled('reportable_medical_conditions')"
                                  class="form-control" #rmc_detail="ngModel" [(ngModel)]="medication_edit_row.rmc_detail"
                                  placeholder="Medical Condition details here"
                                  [disabled]="isDisabled('reportable_medical_conditions')"
                                  ng-value="induction_request.rmc_detail"></textarea>
                    <div class="alert alert-danger m-0" [hidden]="(isDisabled('reportable_medical_conditions') || rmc_detail.valid)">Medical Condition details is required</div>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-8 col-form-label font-weight-bold form-control-label">Any new regular medication, prescribed or otherwise?</label>
                <div class="col-md-4 form-inline mb-4 d-flex justify-content-md-end">
                    <div class="custom-control custom-radio mr-3">
                        <input type="radio" class="custom-control-input" name="on_long_medication" id="olm-yes"
                               (change)="onLongMedicationChanged(yesOlm)" #yesOlm
                               required [(ngModel)]="medication_edit_row.on_long_medication" [value]="'yes'" />
                        <label class="custom-control-label" for="olm-yes"> Yes</label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" class="custom-control-input" name="on_long_medication" id="olm-no"
                               (change)="onLongMedicationChanged(yesOlm)"
                               required [(ngModel)]="medication_edit_row.on_long_medication" [value]="'no'" />
                        <label class="custom-control-label" for="olm-no"> No </label>
                    </div>
                </div>
            </div>

            <ng-template ngFor let-item [ngForOf]="(medication_edit_row.medications)" let-i="index">
                <medication-detail-component
                    [isDisabled]="isDisabled('on_long_medication')"
                    [initialValue]="item"
                    (store)="updateMedicationRow($event, i)"
                    (isInvalid)="updateValidationStatus($event, i)"
                    [removable]="i > 0"
                    (remove)="removeMedicationRow($event, i)"
                ></medication-detail-component>
                <div class="alert alert-danger" [hidden]="isDisabled('on_long_medication') || medicationValid(i)">Medication details are required</div>
            </ng-template>
            <!--<pre>{{ medicationValidations|json}}</pre>-->

            <div class="form-group" *ngIf="!isDisabled('on_long_medication')">
                <label>Add further medication</label>
                <i class="fa fa-plus-circle text-primary float-right cursor-pointer" (click)="addMedicationBlock()"></i>
            </div>

            <ng-container *ngIf="!isDisabled('on_long_medication')">
            <div class="form-group row">
                <label class="col-md-7 col-form-label font-weight-bold form-control-label">Any side effects?</label>
                <div class="col-md-5 form-inline d-flex justify-content-md-end pl-0">
                    <div class="custom-control custom-radio mr-3">
                        <input type="radio" class="custom-control-input"
                               name="any_side_effects" id="ase-yes"
                               required [(ngModel)]="medication_edit_row.any_side_effects" [value]="'yes'" />
                        <label class="custom-control-label" for="ase-yes"> Yes</label>
                    </div>
                    <div class="custom-control custom-radio mr-3">
                        <input type="radio" class="custom-control-input"
                               name="any_side_effects" id="ase-no"
                               required [(ngModel)]="medication_edit_row.any_side_effects" [value]="'no'" />
                        <label class="custom-control-label" for="ase-no"> No </label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" class="custom-control-input"
                               name="any_side_effects" id="ase-na"
                               required [(ngModel)]="medication_edit_row.any_side_effects" [value]="'n/a'" />
                        <label class="custom-control-label" for="ase-na"> N/A </label>
                    </div>
                </div>
            </div>
            <div class="form-group row" *ngIf="!isDisabled('any_side_effects')">
                <label class="col-md-6 col-form-label form-control-label">Please provide details of the side effects</label>
                <div class="col-md-6">
                    <input class="form-control input-md"
                           [disabled]="isDisabled('any_side_effects')"
                           #ase_detail="ngModel"
                           [required]="!isDisabled('any_side_effects')"
                           name="ase_detail" type="text"
                           placeholder="migraines, stomach pain etc"
                           [(ngModel)]="medication_edit_row.any_side_effect_detail"
                           ng-value="induction_request.any_side_effect_detail">
                    <div class="alert alert-danger"
                         [hidden]="(isDisabled('any_side_effects') || ase_detail.valid)">Side effects details is required</div>
                </div>
            </div>
            </ng-container>
        </form>
 </i-modal>
<i-modal #editUserCompanyModalRef [title]="'Employment Details - ' + company_edit_row?.additional_data?.user_info?.name" size="lg" cancelBtnText="Close"
    (onClickRightPB)="saveInductionCompanyChange($event)" (onCancel)="onCloseCompanyModal()" rightPrimaryBtnTxt="Save" [rightPrimaryBtnDisabled]="!editCompanyModalForm.valid">
    <form novalidate #editCompanyModalForm="ngForm">
        <ng-container *ngIf="showEditUserCompanyForm">
            <div class="form-group row">
                <label class="col-md-4 col-form-label form-control-label">Existing Job Role:</label>
                <div class="col-md-8 d-flex align-self-center">
                    <b>{{ company_edit_row?.additional_data?.employment_detail?.job_role }}</b>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-4 col-form-label form-control-label">Change User Job Role<small class="required-asterisk ">*</small></label>
                <div [ngClass]="otherJobRoleNotRequired() ? 'col-md-8' : 'col-md-4'">
                    <ng-select class="w-100" [items]="allowedJobRoles"
                               bindLabel="name"
                               [bindValue]="'name'" required
                               placeholder="Select Job Role"
                               name="job_role" #job_role="ngModel"
                               ng-value="user_emp_detail.job_role"
                               [(ngModel)]="user_emp_detail.job_role"
                               [disabled]="!user_emp_detail?.employer">
                    </ng-select>
                    <div class="alert alert-danger" [hidden]="(job_role.valid)">Job Role is required</div>
                </div>
                <div class="col-md-4" *ngIf="!otherJobRoleNotRequired()">
                    <input class="form-control input-md" #other_jr="ngModel"
                           [required]="!otherJobRoleNotRequired()" name="other_jr"
                           type="text" placeholder="Job Role"
                           [(ngModel)]="user_emp_detail.other_job_role"
                           ng-value="employment_detail.other_job_role">
                    <div class="alert alert-danger" [hidden]="(otherJobRoleNotRequired() || other_jr.valid)">Job Role is required</div>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-4 col-form-label form-control-label">Existing Company:</label>
                <div class="col-md-8 d-flex align-self-center">
                    <b>{{ company_edit_row?.additional_data?.employment_detail?.employer }}</b>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-4 col-form-label form-control-label">Change User Company<small class="required-asterisk ">*</small></label>
                <div class="col-md-8">
                    <company-selector-v2 class="w-100" name="employer1"
                        [projectId]="projectId"
                        [country_code]="projectInfo?.custom_field?.country_code || undefined"
                        [selectedCompany]="user_emp_detail.employer"
                        [required]="true"
                        [showHelper]="!has_supply_chain_companies"
                        (selectionChanged)="inductionCompanySelected($event, 'employer')">
                    </company-selector-v2> 
                    <input type="hidden" name="employer" required #employerHiddenField="ngModel" [ngModel]="user_emp_detail.employer"/>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-4 col-form-label form-control-label">Type of Employment<small class="required-asterisk ">*</small></label>
                <div class="col-md-8">
                    <ng-select
                        [items]="typeOfEmployments"
                        [bindLabel]="'label'"
                        [bindValue]="'value'" required
                        [placeholder]="'Select Employment'"
                        [searchable]="false" [clearable]="false"
                        name="type_of_employment" #type_of_employment="ngModel"
                        class="w-md-100 dropdown-list"
                        appendTo="body"
                        [(ngModel)]="user_emp_detail.type_of_employment"
                        (ngModelChange)="resetForOther(type_of_employment.value, 'other_type_of_employment')"
                    ></ng-select>
                    <div class="alert alert-danger" [hidden]="(type_of_employment.valid)">Type of Employment is required</div>
                </div>
            </div>
            <div class="form-group row" *ngIf="!otherTypeOfEmpNotRequired()">
                <label class="col-md-4 col-form-label form-control-label"></label>
                <div class="col-md-8">
                    <input class="form-control input-md col-md-12" #other_toe="ngModel"
                           [required]="!otherTypeOfEmpNotRequired()"
                           name="other_toe"
                           type="text"
                           placeholder="Type of Employment"
                           [hidden]="otherTypeOfEmpNotRequired()"
                           [(ngModel)]="user_emp_detail.other_type_of_employment"
                           ng-value="user_emp_detail.other_type_of_employment">
                    <div class="alert alert-danger" [hidden]="(otherTypeOfEmpNotRequired() || other_toe.valid)">Type of Employment is required</div>
                </div>
            </div>
            <div class="form-group row" *ngIf="showEmploymentCompanyPicker()">
                <label class="col-md-4 col-form-label form-control-label">{{ employmentCompanyFieldLabel }}<small class="required-asterisk ">*</small></label>
                <div class="col-md-8">
                    <company-selector-v2 class="w-100" name="employmentCompany"
                        [selectedCompany]="user_emp_detail.employment_company"
                        [requiredMessage]="employmentCompanyFieldLabel+' is required'"
                        [country_code]="projectInfo?.custom_field?.country_code"
                        [placeholder]="'Select '+employmentCompanyFieldLabel" 
                        [required]="true"
                        [showHelper]="!has_supply_chain_companies"
                        (selectionChanged)="inductionCompanySelected($event, 'employment_company')"
                    ></company-selector-v2>
                    <input type="hidden" name="employment_company" required #employmentCompanyHiddenField="ngModel" [ngModel]="user_emp_detail.employment_company"/>
                </div>
            </div>
            <small class="form-text text-muted">Changes you make here to the user’s company and/or employment company will be reflected on their {{projectInfo?.custom_field.induction_phrase_singlr}} form.</small>
        </ng-container>
    </form>
</i-modal>
<ng-template #editSuccessModal let-c="close" let-d="dismiss">
    <div class="modal-body">
        <div class="text-center">
            <p class="text-success">Success!</p>
            <p>{{ editSuccessMessage }}</p>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-primary m-auto" (click)="d('Cross click')">Close</button>
    </div>
</ng-template>
<ng-template #showEnrolmentProgressModal let-c="close" let-d="dismiss">
    <button type="button" class="close position-absolute" style="right: 2px; z-index: 2;" aria-label="Close" (click)="d('Cross click')">
        <span aria-hidden="true">&times;</span>
    </button>
    <div class="modal-body fp-processing-wrapper">
                <span class="fa-stack my-4" *ngIf="!has_facial_enrolment_type">
                    <i [ngClass]="{'fa-4x fa-stack-1x animate-flicker fas fa-fingerprint mb-n1': true, 'text-success': optima_modal_row.enrolled}"></i>
                </span>
        <span class="my-4" *ngIf="has_facial_enrolment_type">
                    <svg [attr.fill]="optima_modal_row.enrolled ? 'green': undefined" class="facial-face" enable-background="new 0 0 512 512" viewBox="0 0 512 512" width="64px" xmlns="http://www.w3.org/2000/svg"><g><path d="m451.364 306.904c3.357 13.352 15.46 23.269 29.839 23.269 16.981 0 30.796-13.803 30.796-30.769s-13.815-30.769-30.796-30.769c-14.379 0-26.482 9.916-29.839 23.269h-50.86c.372-3.386.567-6.73.567-10.016v-41.354c0-48.312-15.66-88.99-45.288-117.637-26.362-25.49-61.805-39.528-99.798-39.528-37.994 0-73.436 14.038-99.798 39.528-29.627 28.648-45.287 69.326-45.287 117.638v41.354c0 3.305.189 6.648.552 10.016h-50.817c-3.361-13.352-15.475-23.269-29.867-23.269-16.966 0-30.768 13.802-30.768 30.768s13.802 30.769 30.768 30.769c14.392 0 26.506-9.916 29.867-23.269h53.547c11.945 47.224 55.069 95.936 104.739 114.338 13.346 4.943 25.214 7.415 37.079 7.415 11.862 0 23.721-2.471 37.044-7.412 10.329-3.812 20.646-9.058 30.664-15.593 3.469-2.263 4.447-6.91 2.184-10.379-2.264-3.47-6.91-4.447-10.379-2.184-9.079 5.922-18.386 10.661-27.673 14.088-23.206 8.606-40.473 8.606-63.708 0-43.383-16.073-82.347-59.061-94.402-100.272h252.405c-6.955 23.467-22.726 48.401-44.013 68.338-3.023 2.832-3.179 7.578-.347 10.601 1.477 1.577 3.474 2.373 5.476 2.373 1.837 0 3.679-.671 5.125-2.026 24.771-23.2 42.378-51.928 49.361-79.286h53.627zm29.84-23.268c8.71 0 15.796 7.074 15.796 15.769s-7.086 15.769-15.796 15.769c-8.694 0-15.768-7.074-15.768-15.769s7.073-15.769 15.768-15.769zm-450.436 31.537c-8.694 0-15.768-7.074-15.768-15.769s7.074-15.769 15.768-15.769c8.71 0 15.796 7.074 15.796 15.769s-7.086 15.769-15.796 15.769zm95.791-23.269c-.43-3.381-.659-6.726-.659-10.016v-22.148c33.202-.016 39.943-21.041 44.865-36.396 4.404-13.736 6.816-18.542 16.585-18.542h52.574c4.142 0 7.5-3.358 7.5-7.5s-3.358-7.5-7.5-7.5h-52.574c-21.584 0-26.732 16.06-30.869 28.963-4.65 14.505-8.322 25.963-30.581 25.976v-4.206c0-97.677 67.433-142.165 130.086-142.165s130.085 44.488 130.085 142.165v4.206c-22.253-.013-25.921-11.467-30.566-25.966-4.135-12.908-9.282-28.973-30.883-28.973h-49.896c-4.142 0-7.5 3.358-7.5 7.5s3.358 7.5 7.5 7.5h49.896c9.783 0 12.196 4.808 16.598 18.549 4.918 15.351 11.654 36.372 44.851 36.389v22.149c0 3.256-.243 6.605-.693 10.016h-258.819z"/><path d="m8.497 119.987c4.142 0 7.5-3.358 7.5-7.5v-87.977c0-4.69 3.816-8.506 8.506-8.506h87.974c4.142 0 7.5-3.358 7.5-7.5s-3.358-7.5-7.5-7.5h-87.975c-12.961 0-23.506 10.545-23.506 23.506v87.977c.001 4.142 3.358 7.5 7.501 7.5z"/><path d="m399.495 16.004h88.002c4.674 0 8.477 3.816 8.477 8.506v87.977c0 4.142 3.358 7.5 7.5 7.5s7.5-3.358 7.5-7.5v-87.977c0-12.961-10.532-23.506-23.477-23.506h-88.002c-4.142 0-7.5 3.358-7.5 7.5s3.358 7.5 7.5 7.5z"/><path d="m112.477 495.996h-87.975c-4.69 0-8.506-3.803-8.506-8.478v-88.005c0-4.142-3.358-7.5-7.5-7.5s-7.5 3.358-7.5 7.5v88.005c0 12.945 10.545 23.478 23.506 23.478h87.974c4.142 0 7.5-3.358 7.5-7.5s-3.357-7.5-7.499-7.5z"/><path d="m503.475 392.013c-4.142 0-7.5 3.358-7.5 7.5v88.005c0 4.674-3.803 8.478-8.477 8.478h-88.002c-4.142 0-7.5 3.358-7.5 7.5s3.358 7.5 7.5 7.5h88.002c12.945 0 23.477-10.532 23.477-23.478v-88.005c0-4.142-3.358-7.5-7.5-7.5z"/></g></svg>
                </span>
        <p *ngIf="!optima_modal_row.enrolled && !has_facial_enrolment_type">Present user's finger on reader</p>
        <p *ngIf="!optima_modal_row.enrolled && has_facial_enrolment_type">Present user's face to scanner</p>
        <p *ngIf="optima_modal_row.enrolled">Enrolment Successful</p>
    </div>
</ng-template>
<ng-template #facialEnrolmentDoneModal let-c="close" let-d="dismiss">
    <button type="button" class="close position-absolute" style="right: 2px; z-index: 2;" aria-label="Close" (click)="d('Cross click')">
        <span aria-hidden="true">&times;</span>
    </button>
    <div class="modal-body fp-processing-wrapper">
        <i class="mb-2 mt-4 fa-2x fas fa-thumbs-up text-success"></i>
        <h6>Success</h6>
        <small class="text-center mb-5"><b>{{ optima_modal_row?.name || optima_modal_row?.user_ref?.name || optima_modal_row?.user_ref?.first_name }}</b> has been successfully enrolled</small>
        <button type="button" class="btn btn-sm btn-primary" (click)="d('Cross click')">Continue</button>
    </div>
</ng-template>
<ng-template #projectLoading>
    <block-loader [show]="(true)" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>
</ng-template>
<i-modal #showMedicalConditionsModal [title]="'Health Concerns'" size="lg" [showCancel]="false"
    rightPrimaryBtnTxt="OK" (onClickRightPB)="closeHealthConcernModal($event)">
    <div *ngIf="record?.health_issues">
        <ng-container *ngFor="let rec of record.health_issues | keyvalue">
            <div class="d-flex">
                <div class="col-12 pl-0 pr-0">
                    <div class="w-100 mb-1 fw-500">
                        {{ rec.key }}
                    </div>
                    <div class="d-flex justify-content-between w-100" *ngFor="let question of rec?.value">
                        <div class="m-0 mb-2 contant-font-size contant-line-hight">
                            {{ question?.question_ref?.question }}
                        </div>
                        <div class="pl-0 pr-0">
                            <span class="badge-pill badge-light-grey text-center">Yes</span>
                        </div>
                    </div>
                </div>
            </div>
            <div  *ngIf="record?.medical_issues?.length" class="line my-2"></div>
        </ng-container>
    </div>
    <div *ngIf="record?.medical_issues?.length">
        <div class="mb-3">
            <div class="col-12 pl-0 pr-0">
                <div class="w-100 mb-1">
                    <span class="fw-500"> Medical Assessments </span>
                </div>
                <ng-container *ngFor="let question of record.medical_issues">
                    <div class="d-flex justify-content-between w-100">
                        <div class="m-0 mb-2 contant-font-size contant-line-hight">
                            <span class="d-block w-100"> {{ question?.question }} </span>
                            <div *ngIf="question?.ans_details" class="w-100 contant-font-size contant-line-hight">
                                <p class="m-0">
                                    <span class="fw-500"> Details: </span>
                                    {{ question.ans_details }}
                                </p>
                            </div>
                        </div>
                        <div class="pl-0 pr-0">
                            <span class="badge-pill badge-light-grey text-center">Yes</span>
                        </div>
                    </div>
                </ng-container>
            </div>
        </div>
        <div *ngIf="record?.reportable_medical_conditions === 'yes'" class="line my-2"></div>
    </div>
    <div *ngIf="record?.reportable_medical_conditions === 'yes'">
        <div class="mb-3">
            <div class="d-flex">
                <div class="col-12 pl-0 pr-0">
                    <div class="w-100 mb-1">
                        <span class="fw-500">Reportable Medical Conditions</span>
                    </div>
                    <div class="w-100 contant-line-hight">
                        <span> {{ record?.rmc_detail ? record.rmc_detail : '-' }} </span>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="record?.long_medication_detail?.length" class="line my-2"></div>
    </div>
    <div *ngIf="record?.long_medication_detail?.length">
        <div class="d-flex">
            <div class="col-12 pl-0 pr-0">
                <span class="fw-500">Medication</span>
            </div>
        </div>
        <ng-container *ngFor="let rec of record?.long_medication_detail">
            <div class="mb-3 contant-font-size">
                <div> Name: <span class="fw-500"> {{ rec.medication_name }} </span></div>
                <div> Dosage: <span class="fw-500"> {{ rec.medication_dosage }} </span></div>
                <div> Frequency: <span class="fw-500"> {{ rec.medication_frequency }} </span></div>
                <div> Date Commenced: <span class="fw-500"> {{  dayjs(rec.medication_date_commenced, this.dateStorageFormat).format(this.dateFormat) }} </span></div>
                <div> Completion Date:
                    <span *ngIf="rec.infinite_completion" class="fw-500">Indefinite</span>
                    <span *ngIf="!rec.infinite_completion" class="fw-500"> {{ dayjs(rec.medication_date_of_completion, this.dateStorageFormat).format(this.dateFormat) }} </span></div>
            </div>
        </ng-container>
        <div class="w-100 mb-3 contant-font-size contant-line-hight" *ngIf="record?.any_side_effect_detail">
            Side Effects: <span class="fw-500"> {{ record?.any_side_effect_detail }} </span>
        </div>
    </div>
</i-modal>
<i-modal #customizeAccessRef [title]="modalTitle" size="lg" [showCancel]="false" (onCancel)="closeCustomAccessModal()" (onClickRightPB)="closeCustomAccessModal($event)"
    rightPrimaryBtnTxt="Save" [leftSecondaryBtnTxt]="(!isSelectAllActive) ? 'Select All' : 'Deselect All'" (onClickLeftSB)="toggleSelectDeselectAll()">
    <table class="table custom-access-level table-bordered" *ngIf="showCustomAccessModal">
        <thead>
            <tr>
                <th class="text-center">Tools</th>
                <th *ngIf="blockType != 'notification'" class="text-center">Web Access</th>
                <th *ngIf="blockType != 'notification'" class="text-center">App Access</th>
                <th class="text-center email-notif-col">Email Notifications</th>
            </tr>
        </thead>
        <tbody>
            <ng-container *ngFor="let tool of toolsList; trackBy : trackByRowIndex; let i = index;" >
                <tr *ngIf="(blockType == 'notification' && tool.has_nom_email) || blockType !== 'notification'">
                    <td class="text-center">
                        {{(tool.phrase_key && projectInfo[tool.container] && projectInfo[tool.container][tool.phrase_key]) ? projectInfo[tool.container][tool.phrase_key] : tool.label}}
                    </td>
                    <td *ngIf="blockType != 'notification'" class="text-center">
                        <div class="custom-control custom-switch d-inline-block" *ngIf="tool.target.includes('web')">
                            <input type="checkbox" class="custom-control-input" [id]="'custom_access_'+tool.key" [name]="tool.key"
                            (change)="toggleFeatureAccess($event, tool.key)"
                            [checked]="adminAccessData.permission && adminAccessData.permission.includes(tool.key+':'+OPERATIONS.ANYTHING)">
                            <label class="custom-control-label" [for]="'custom_access_'+tool.key"></label>
                        </div>
                    </td>
                    <td *ngIf="blockType != 'notification'" class="text-center">
                        <div class="custom-control custom-switch d-inline-block" *ngIf="tool.target.includes('app')">
                            <input type="checkbox" class="custom-control-input" [id]="'custom_access_'+tool.m_key" [name]="tool.m_key"
                                   (change)="toggleFeatureAccess($event, tool.m_key)"
                                   [checked]="adminAccessData.permission && adminAccessData.permission.includes(tool.m_key+':'+OPERATIONS.ANYTHING)">
                            <label class="custom-control-label" [for]="'custom_access_'+tool.m_key"></label>
                        </div>
                    </td>
                    <td class="text-center">
                        <div *ngIf="tool.has_nom_email" class="custom-control custom-switch d-inline-block">
                            <input type="checkbox" class="custom-control-input" [id]="'email_notify_'+tool.key" [name]="tool.key"
                            (change)="toggleEmailNotify($event, tool.key)"
                            [checked]="adminAccessData.permission && adminAccessData.permission.includes(tool.key+':notify')">
                            <label class="custom-control-label" [for]="'email_notify_'+tool.key"></label>
                        </div>
                    </td>
                </tr>
            </ng-container>
        </tbody>
    </table>
</i-modal>

<i-modal #cautionCardModalRef [title]="userDetails?.additional_data?.user_info?.name +': Conduct Cards'" size="md"
  [showCancel]="false" (onClickRightPB)="onSubmit(conductCardForm)" rightPrimaryBtnTxt="Assign" [rightPrimaryBtnDisabled]="!conductCardForm.valid">
        <div class="w-100 p-2">
            <form novalidate #conductCardForm="ngForm">
                <div class="form-group mb-2">
                    <h5 class="mb-0">Select Card</h5>
                    <label class="heading mb-2 text-muted">
                        Select the relevant conduct card from the dropdown below
                    </label>
                    <ng-select class="w-100 dropdown-list" appendTo="body"
                        name="id"
                        bindLabel="card_name"
                        bindValue="id"
                        ng-value="selectedConductCard"
                        placeholder="Please select conduct card"
                        [items]="conductCardsList"
                        [clearable]="false"
                        [(ngModel)]="selectedConductCard"
                        #selectedConductCardValue="ngModel"
                        (change)="onChange(conductCardForm)" required>
                        <ng-template ng-label-tmp let-item="item">
                            <div class="heading flex-center">
                                <div class="color-circle" [style.background-color]="item.card_color"></div>
                                {{ item.card_name }}
                            </div>
                        </ng-template>
                    </ng-select>
                    <div class="alert alert-danger" [hidden]="(selectedConductCardValue.valid)">Card type required.</div>
                </div>
                <div *ngIf="selectedConductCard" class="form-group mb-2">
                    <h5 class="my-2">Action</h5>
                    <input type="email" class="form-control" [(ngModel)]="cardAction" [ngModelOptions]="{standalone: true}" readonly />
                </div>
                <div *ngIf="selectedConductCard" class="form-group mb-2">
                    <h5 class="my-2">Comments</h5>
                    <textarea class="form-control" id="textarea" rows="3"
                        placeholder="Enter Comment"
                        #comment="ngModel"
                        [(ngModel)]="conductComment"
                        name="comment" required>
                    </textarea>
                    <div class="alert alert-danger mb-0 mt-1 py-1" [hidden]="comment.valid">Comment is required.</div>
                </div>
                <div class="form-group mb-2">
                    <h5 class="mb-0">Notify</h5>
                    <label class="heading mb-2 text-muted">
                        You can notify other member(s) of the team by selecting one or more from the dropdown of admins below
                    </label>
                    <ng-select class="w-100 dropdown-list" appendTo="body"
                               name="projectAdmins"
                               [multiple]="true"
                               placeholder="Please select project admin"
                               [clearable]="true"
                               [(ngModel)]="selectedProjectAdminIds"
                    >
                        <ng-option *ngFor="let item of projectAdmins" [value]="item.user_ref.id" >{{ item.user_ref.first_name }} {{item.user_ref.last_name}} ({{item.email}})</ng-option>
                    </ng-select>
                </div>
            </form>
        </div>
        <div class="w-100 p-2" *ngIf="userDetails?.conduct_cards?.length">
            <div class="d-flex flex-column">
                <h5 class="my-2">Previously Issued</h5>
                <div *ngFor="let item of userDetails?.conduct_cards; let i=index;" class="d-flex mb-2">
                    <ngb-accordion #acc="ngbAccordion">
                        <ngb-panel id="toggle-1">
                            <ng-template ngbPanelHeader>
                                <button ngbPanelToggle class="btn p-0" (click)="cardsToggle(i)">
                                    <div class="d-flex align-items-center cursor-pointer main-drop w-100">
                                        <div class="">
                                            <span class="d-inline-block rounded-circle dot mr-2"
                                                [ngStyle]="{'background-color': item?.card_detail?.card_color}"></span>
                                            <span class="heading">{{ item?.card_detail?.card_name }}</span>
                                        </div>
                                        <div class="ml-auto">
                                            <div class="ml-auto">
                                                <ng-container *ngIf="cardToggle.includes(i); else ArrowDown">
                                                    <img class="arrow-triangle" [src]="AssetsUrlSiteAdmin.arrowUpFilledTriangle" alt="">
                                                </ng-container>
                                                <ng-template #ArrowDown>
                                                    <img class="arrow-triangle" [src]="AssetsUrlSiteAdmin.arrowDownFilledTriangle" alt="">
                                                </ng-template>
                                            </div>
                                        </div>
                                    </div>
                                </button>
                            </ng-template>
                            <ng-template ngbPanelContent>
                                <div class="w-100 p-2">
                                    <div class="form-group mb-2" *ngIf="item?.card_detail?.card_name">
                                        <label class="heading mb-0">
                                            Card Name
                                        </label>
                                        <div class="input-group">
                                            <input type="text" class="form-control"
                                                [(ngModel)]="item.card_detail.card_name" readonly>
                                        </div>
                                    </div>
                                    <div class="form-group mb-2" *ngIf="item?.dateIssued">
                                        <label class="heading mb-0">
                                            Date Issued
                                        </label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" [(ngModel)]="item.dateIssued" readonly>
                                        </div>
                                    </div>
                                    <div class="form-group mb-2" *ngIf="item?.dateIssued">
                                        <label class="heading mb-0">
                                            Issued By
                                        </label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" [(ngModel)]="item.assigned_by_ref.name" readonly>
                                        </div>
                                    </div>
                                    <div class="form-group mb-2" *ngIf="item?.card_detail?.card_action">
                                        <label class="heading mb-0">
                                            Action
                                        </label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" [(ngModel)]="item.card_detail.card_action" readonly>
                                        </div>
                                    </div>
                                    <div class="form-group mb-2" *ngIf="item?.expire">
                                        <label class="heading mb-0">
                                            Expiry
                                        </label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" [(ngModel)]="item.expire" readonly>
                                        </div>
                                    </div>
                                    <div class="form-group mb-2" *ngIf="item?.comment">
                                        <label class="heading mb-0">
                                            Comment
                                        </label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" [(ngModel)]="item.comment" readonly>
                                        </div>
                                    </div>
                                </div>
                            </ng-template>
                        </ngb-panel>
                    </ngb-accordion>
                </div>
            </div>
        </div>
</i-modal>

<ng-template #irBlockedReasonModal let-c="close" let-d="dismiss">
    <div class="modal-body m-3 p-1">
        <div class="row mx-0" *ngIf="blockReasonHeader">
            <p class="mb-0 h6">{{blockReasonHeader}}</p>
        </div>
        <div class="row mx-0 mt-2">
            <p class="mb-0 medium-font" [innerHTML]="blockReason"></p>
        </div>
        <div class="row mx-0 mt-3 d-flex float-right">
            <div class="d-flex justify-content-end confirmation-action-btns w-100">
                <button *ngIf="isCompetencyBlock || (currentIr.status_code != STATUS_CODES.BLACKLISTED)" type="button" tabindex="-1" class="btn modal-btn px-3 mr-2 text-brandeis-blue fw-500" (click)="d()">Cancel</button>
                <button *ngIf="(!isCompetencyBlock && !isConductCardBlock) || (isConductCardBlock && currentIr.status_code == STATUS_CODES.BLACKLISTED)" type="button" tabindex="-1" class="btn btn-outline-primary btn-brandeis-blue fw-500" (click)="d()">OK</button>
                <button *ngIf="isCompetencyBlock" type="button" tabindex="-1" class="btn btn-outline-primary btn-brandeis-blue fw-500" (click)="unblockInduction(c)">Remove</button>
                <button *ngIf="isConductCardBlock && currentIr.status_code != STATUS_CODES.BLACKLISTED" type="button" tabindex="-1" class="btn btn-outline-primary btn-brandeis-blue fw-500" (click)="unAssignConductCard(c)">Remove</button>
            </div>
        </div>
    </div>
</ng-template>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

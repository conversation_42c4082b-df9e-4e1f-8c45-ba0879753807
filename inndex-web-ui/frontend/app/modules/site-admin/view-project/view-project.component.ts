/**
 * Created by spatel on 16/10/18.
 */
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from "@angular/router";
import {
    AuthService,
    BioMetricSettingResponse,
    Comment,
    Common,
    ConductCardsService,
    ExtendedCompanyInfo, NotFoundRtwCheckResult,
    InductionRequest,
    META_STATUS_CODES,
    STATUS_CAN_TRANSIT,
    META_STATUS_CODES_LIST,
    OptimaService,
    ProcoreService,
    Project,
    ProjectSectionAccess,
    ProjectService,
    ResourceService,
    OPERATIONS,
    TimeUtility,
    UACLevels,
    UACProjectDesignations,
    User,
    UserService,
    OptimaBadge,
    UserDocument,
    PermissionUtility,
    ProjectInductionsListRow,
    InductionRecentChange, UserRolePermission,
    ToastService
} from "@app/core";
import { AdditionalDocUploaderComponent, DeclarationAnswersComponent, ManageBlacklistingComponent, CCLStatusTileConfig } from "@app/modules/common";
import {AssetsUrl, IModalComponent, GenericConfirmationModalComponent, PicViewerInput} from '@app/shared';
import { NgbModal, NgbModalConfig } from '@ng-bootstrap/ng-bootstrap';
import * as dayjs from 'dayjs';
import { Observable } from "rxjs";
import { ManageTbSiteAccessComponent } from "./manage-tb-site-access/manage-tb-site-access.component";
import {AppConstant} from "@env/environment";
import {InductionsComponent} from "./inductions/inductions.component";
import {DuplicatePhotoReviewComponent} from './inductions/duplicate-photo-review/duplicate-photo-review.component';
interface InductionInvitation {
    email: string;
    isDuplicate?: boolean;
}
@Component({
    selector: 'view-project',
    templateUrl: './view-project.component.html',
    styleUrls: ['./view-project.component.scss'],
    // add NgbModalConfig and NgbModal to the component providers
    providers: [NgbModalConfig, NgbModal]
})
export class ViewProjectComponent implements OnInit {
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    AssetsUrlSiteAdmin = AssetsUrl.siteAdmin;
    dateFormat: string = AppConstant.defaultDateFormat;
    dbDateFormat: string = AppConstant.apiRequestDateFormat;
    displayDateTimeFormat: string = AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS;
    dateStorageFormat: string = AppConstant.dateStorageFormat;
    dateDisplayFormat: string = AppConstant.displayDateFormat;
    OPERATIONS = OPERATIONS;
    loadingInProgress: boolean = false;
    dataLoading: boolean = false;
    authUser$: User;
    STATUS_CODES: any = META_STATUS_CODES;
    STATUS_CAN_TRANSIT = STATUS_CAN_TRANSIT;
    STATUS_CODES_LIST: Array<any> = META_STATUS_CODES_LIST;
    STATUS_COLORS: any = {0 : "danger", 2 : "success", 4: "danger", 5: "dark"};
    blobType: string = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    record: any = null;
    projectSectionAccess: boolean = false;
    createBadgeInProgress = false;
    searchBadgeInProgress = false;
    optima_modal_row: any = {};
    optima_meta: BioMetricSettingResponse;
    has_facial_enrolment_type: boolean = false;
    isSelectAllActive: boolean = false;
    showCustomAccessModal: boolean = false;
    optima_badge_request: any = {};
    optima_badge_search: any = {};
    optima_badge_number: number = null;
    optima_readers: Array<any> = [];
    optima_access_group: Array<any> = [];
    badge_information: {
        badge?: OptimaBadge;
        has_enrolment?: boolean;
        has_remote_enrolment?: boolean;
        remote_enrolment?: {
            base64: string;
            fileType: string;
            badgeNo: number;
        }
        _enrolment_face_url?: string;
        _remote_enrolment_enabled?: boolean;
        _remote_enrolled_photo?: PicViewerInput;
        _enrolment_mode?: string;
    } = {};
    badge_access_info: any = {};
    fingerprint_enrolment_request: any = {};
    leftHandFingerIndex: number = 4;
    rightHandFingerIndex: number = 7;
    leftHandEnroled: boolean = false;
    rightHandEnroled: boolean = false;
    leftHandEnroledFailed: boolean = false;
    rightHandEnroledFailed: boolean = false;
    indexFingerPrint: any = {};
    project: Observable<Project>;
    induction_requests: Observable<{}>;
    processingLoader: boolean = false;
    paginationData = new Common();
    page = this.paginationData.page;
    badge_detail: any = {};
    searchText:any = null;
    // statusCode:any = null;
    employerName:any = null;
    timeout: any = null;
    records: Array<any> = [];
    temp: Array<any> = [];
    projectContractor: ExtendedCompanyInfo = {};
    medication_block_disabled: Boolean = false;
    forceReload: boolean = true;
    change_request: {
        row?: any;
        note?: string;
        code?: number;
        show_change_log?: boolean;
        recent_changes?: InductionRecentChange[];
        passport_required?: boolean;

        _photo_reviewed?: boolean;
        _need_rtw_check?: boolean;
        _can_approve?: boolean;
        _status_options?:any[];
        _modal_title?:string;

        _profile_photo?: PicViewerInput;
        _smart_check_photo?: PicViewerInput;
    } = {
        row: null,
        note: null,
        code: null,
        show_change_log: false,
        recent_changes: [],
    };
    inductedEmployers: Array<any> = [];
    verifiableCompetencies: Array<string> = [];

    update_row: any = {};
    // additionalDocuments: Array<UserDocument> = [];
    // fetching_additional_docs: Boolean = false;

    allBadgeEventsRows: Array<any> = [];
    allEventsInductionRequests: Array<InductionRequest> = [];
    allEventTablePage: any = {};

    allOptimaMembersInductionRequests: Array<InductionRequest> = [];
    tempAllOptimaMembersInductionRequests: Array<InductionRequest> = [];

    sendingInvitationInProgress = false;
    tableOffset: number = 0;
    inductionInvitationEmail: Array<InductionInvitation> = [{email: ""}];

    designations = UACProjectDesignations;
    inductedUsers: Array<any>;
    procorePermissionTemplates: Array<any> = [];
    procorePermissionId: number;

    accessUser:any;
    blockType:string = 'access';
    modalTitle:string = 'Customize Access';

    acccessLevelOpts: Array<any> = UACLevels;
    toolsList: Array<any> = [];
    adminAccessData:any = {
        designation: [],
        permission: []
    };
    inductionComments: Array<Comment> = [];
    user_emp_detail: {
        job_role?: string;
        other_job_role?: string;
        type_of_employment?: string;
        employment_company?: string;
        other_type_of_employment?: string;
        employer?: string;
        parent_company?: number;
    } = {};
    typeOfEmployments: Array<object> = new Common().typeOfEmployments();
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    isProjectPortal: boolean = false;
    allowedJobRoles: Array<string> = ['Other'];
    inductionPermission: boolean = false;
    selectedConductCard: any;
    cardAction: string;
    conductComment: string;
    userDetails: any;
    conductCardsList: Array<any> = [];
    cardToggle: Array<any> = [];
    currentIr: InductionRequest = new InductionRequest();
    isCompetencyBlock: any = undefined;
    isConductCardBlock: any = undefined;
    blockReason: string = null;
    blockReasonHeader: string = null;
    isCollapse: boolean = false;
    supply_chain_companies: Array<any>;
    has_supply_chain_companies: boolean;
    showQRDownloadButton: boolean = false;
    inviteBtnStatus: boolean = true;
    showModal: boolean = false;
    activeAccessAccordion: string[] = [];
    activeIds: string[] = ['list0'];
    isProjectAdminV1: boolean = false;
    isDuplicateEmails: boolean = false;
    showEditUserCompanyForm: boolean = false;
    projectAdmins: Array<UserRolePermission> = [];
    selectedProjectAdminIds: number[] = [];

    inductionViewerData: {ir?: InductionRequest; from?: string;} = {};
    cclStatusConfig: CCLStatusTileConfig;

    constructor(
        private authService: AuthService,
        private optimaService: OptimaService,
        private projectService: ProjectService,
        private permissionUtility: PermissionUtility,
        private userService: UserService,
        private router: Router,
        private route: ActivatedRoute,
        private modalService: NgbModal,
        public timeUtility: TimeUtility,
        private resourceService: ResourceService,
        private procoreService: ProcoreService,
        private conductCardsService: ConductCardsService,
        private toastService: ToastService,
    ) {
        this.isProjectPortal = this.route.snapshot.data.is_project_portal || false;
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.route.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            console.log(this.projectInfo);
            this.projectId = this.projectInfo.id;
            this.medication_block_disabled = this.projectResolverResponse.medication_block_disabled;
            this.projectContractor = this.projectResolverResponse.contractor;
            this.getAdmins();
        }
    }

    ngOnInit() {
        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
            }
        });
        this.route.params
            .subscribe(params => {
                this.initialize();
            });

        this.fetchJobRoles();

        this.getConductCardsByProjectId();
        this.getQRPosterData();
        this.getVerifiableCompetencies();

        this.isProjectAdminV1 = this.authService.isProjectAdminV1(this.authUser$, this.projectInfo)
    }

    dayjs(n: number, format?: any) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(n, format).tz(tz);
    }

    unix(n: number) {
        return dayjs.unix(n);
    };

    initialize() {
        this.dataLoading = true;
        if(this.projectInfo._my_designations && this.projectInfo._my_designations.includes(UACProjectDesignations.RESTRICTED)){
            const message = 'You are not authorized to access this information.';
            this.toastService.show(this.toastService.types.INFO, message);
            return this.router.navigate(['/site-admin']);
        }
        this.getProjectSectionAccess();
        if(!(this.projectContractor && this.projectContractor.features_status && this.projectContractor.features_status.user_blacklisting)){
            // Not enabled.
            this.STATUS_CODES_LIST = META_STATUS_CODES_LIST.filter(l => (l.code !== 5));
        }
        if(this.projectInfo._my_designations && this.projectInfo._my_designations.includes(UACProjectDesignations.INDUCTOR)){
            this.inductionPermission = true;
            // No need to get procore templates for non-inductor users.
            this.getProCorePermissionTemplates();
            // No need to fetch tools list if user is not inductor
            this.resourceService.getToolList().subscribe((data: any) => {
                this.toolsList = data.tool_list || [];
            });
        }
        this.getOptimaSetting();
        this.dataLoading = false;
        if(this.projectInfo.custom_field?.has_supply_chain_companies === true) {
            this.has_supply_chain_companies = true;
            this.getEmployersList();
        }
    }

    getEmployersList(){
        let country_code = this.projectInfo.custom_field.country_code;
        this.userService.getEmployer(country_code, false, false).subscribe((data: any) => {
            if(data.employerlist){
                this.supply_chain_companies = (data.employerlist || []).filter(e => this.projectInfo.custom_field?.supply_chain_companies.includes(e.id));
            } else {
                const message = 'Unable to load supply chain data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    getVerifiableCompetencies() {
        if (!this.projectContractor.cscs_status?.enabled) {
            return true;
        }
        let country_code = (this.projectInfo?.custom_field?.country_code || undefined);
        this.resourceService.getCompetencies({country_code: country_code})
            .subscribe(data => {
                if (data.competencieslist) {
                    this.verifiableCompetencies = (data.competencieslist || []).reduce((names, c) => {
                        if (c.name && c.scheme_id) {
                            names.push(c.name);
                        }
                        return names;
                    }, []);
                } else {
                    const message = 'Unable to load meta competencies info.';
                    this.toastService.show(this.toastService.types.ERROR, message, {data: data});
                }
            });
    }

    private getProjectSectionAccess() {
        const projectSectionAccess: ProjectSectionAccess = this.projectInfo?.project_section_access;
        this.projectSectionAccess = projectSectionAccess?.rams ||
        projectSectionAccess?.task_briefings ||
        projectSectionAccess?.toolbox_talks ||
        projectSectionAccess?.work_package_plan;
    }

    getProCorePermissionTemplates() {
        if(!this.procorePermissionTemplates.length) {
            this.procoreService.getProjectPermissionTemplates(this.projectId).subscribe((data: any) => {
                if(data.templates){
                    this.procorePermissionTemplates = data.templates;
                } else {
                    console.log(data.message ? data.message : 'Failed to get your procore permission templates, please try after sometime.');
                    console.log('Procore templates API error response ', data);
                }
            });
        }
    }

    getOptimaSetting(){
        this.optimaService.getBioMetricSetting(this.projectId, { expand_optima: true}).subscribe((data: BioMetricSettingResponse) => {
            if(data.has_data){
                this.optima_meta = data;
                this.optima_meta.has_optima_source = (data.has_optima_source && !!data.optima && data.optima_online);
                this.has_facial_enrolment_type = (data.enrolment_type === 'facial');
                if(data.has_optima_source){
                    // has optima configured
                    this.getOptimaAccessGroupAndReader(this.projectId);
                }
            }else{
                // don't have any Bio-metric setting configured
                this.optima_meta = null;
            }
        });
    }

    getOptimaAccessGroupAndReader(projectId) {
        this.optimaService.getOptimaAccessGroupAndReader(projectId).subscribe(data => {
            if (data.success) {
                this.optima_access_group = data.access_groups;
                this.optima_readers = data.readers;
            }
        });
    }


    @ViewChild('pInductions', { static: true })
    private inductionsComponentRef: InductionsComponent;

    private initializeTable(isPageChange?: boolean) {
        return this.inductionsComponentRef.initializeTable(isPageChange);

    }

    @ViewChild('reviewDuplicatePhoto') private reviewDuplicatePhotoRef: DuplicatePhotoReviewComponent;

    checkDuplicateIfFREnabled(row){
        this.processingLoader = true;
        this.projectService.sa_checkDuplicateFaceIntoFR(this.projectId, row.id).subscribe((data: any) => {
            this.processingLoader = false;
            if(data.error && (!data.matching_inductions || data.matching_inductions.length === 0)){
                const message = data.message || 'Given face already exists';
                this.toastService.show(this.toastService.types.ERROR, message);
                return;
            }
            else if(data.error && data.matching_inductions){
                const message = data.message || 'Given face does not exists';

                // Title: Duplicate Profile Photo Detected
                // Content: This user's profile photo appears to be a duplicate of another inducted user's photo. Please update one of the profile photos to resolve the duplication.
                // this.reviewDuplicatePhotoRef.open({});
                this.confirmationModalRef.openConfirmationPopup({
                    headerTitle: `Duplicate Profile Photo Detected`,
                    title: `This user's profile photo appears to be a duplicate of another inducted user's photo. Please update one of the profile photos to resolve the duplication.`,
                    confirmLabel: 'Review',
                    onConfirm: () => {
                        this.reviewDuplicatePhotoRef.open({matching_inductions: data.matching_inductions, selected: row});
                    }
                });
            }else{
                this.openPopup(row, null);
            }
        })
    }

    canTransitInductionStatus(status_code) {
        return this.STATUS_CAN_TRANSIT(status_code) && (this.inductionPermission || this.isProjectAdminV1);
    }

    @ViewChild('viewInductionModalRef') private viewInductionModalRef: IModalComponent;
    viewInductionModal({row}) {
        this.inductionViewerData = {};
        this.processingLoader = true;
        this.projectService.getSAProjectInductionById(this.projectId, row.id).subscribe({
            next: ({ success, induction_request, message, error }: any) => {
                if (success && !error) {
                    this.openInductionPreview({induction_request}, 'viewer');
                } else {
                    const message = 'Failed while retrieving record, please refresh the page and try again.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                }
            },
            error: (err: any) => {
                const message = 'Failed while retrieving record, please refresh the page and try again.';
                this.toastService.show(this.toastService.types.ERROR, message, {data: err});
            },
            complete: () => {
                this.processingLoader = false;
            }
        });
    }
    private openInductionPreview({induction_request}, from: string){
        this.inductionViewerData = {ir: induction_request, from};
        this.processingLoader = false;
        this.viewInductionModalRef?.open();
    }

    openInductionViewer({closeFn}){
        closeFn && closeFn();
        this.processingLoader = true;
        setTimeout(() => {
            this.openInductionPreview({induction_request: this.change_request.row}, 'review');
        }, 0);
    }

    onClickingViewerBack({closeFn}){
        if(this.inductionViewerData.from !== 'review'){
            return false;
        }
        closeFn && closeFn();
        if(!this.canTransitInductionStatus(this.inductionViewerData.ir.status_code)){
            return false;
        }
        this.checkDuplicateIfFREnabled(this.inductionViewerData.ir);
    }

    @ViewChild('content')
    private content: IModalComponent;
    openPopup(row, target_status) {
        //reset modal
        // this.change_request.inductor_email = '';
        this.change_request._status_options = [];
        this.change_request.passport_required = null;
        this.change_request.note = null;
        this.change_request.show_change_log = false;
        this.change_request.recent_changes = [];
        this.change_request._need_rtw_check = true;
        this.change_request._can_approve = false;

        this.acccessLevelOpts = [{label: "None", designation: ''}, ...UACLevels];

        if(!target_status){
            this.change_request._status_options = [
                {label: 'Approve', code: META_STATUS_CODES.APPROVED},
                {label: 'Reject', code: META_STATUS_CODES.REJECTED},
                {label: 'Request change', code: META_STATUS_CODES.CHANGE_REQUESTED},
            ];
        }

        this.change_request.note = null;
        this.change_request.code = target_status;

        this.processingLoader = true;
        this.projectService.getSAProjectInductionById(this.projectId, row.id, {extra: 'uac'}).subscribe((data: any) => {
            this.processingLoader = false;
            if(data.error || !data.induction_request || !data.induction_request.id){
                const message = data.message || 'Failed while retrieving given record, please refresh the page and try again.';
                this.toastService.show(this.toastService.types.ERROR, message);
                return;
            }
            this.change_request._modal_title = `${this.projectInfo.custom_field.induction_phrase_singlr} Review: ${data.induction_request.creator_name}`;
            this.change_request._photo_reviewed = null;
            this.change_request.show_change_log = true;

            this.change_request.row = data.induction_request;
            this.change_request.row.name = this.change_request.row.name || data.induction_request.creator_name;
            const _md_url = (data.induction_request.additional_data?.user_info?.profile_pic_ref?.md_url) || (data.induction_request.additional_data?.user_info?.profile_pic_ref?.file_url);
            this.change_request._profile_photo = {
                title: 'innDex profile photo',
                thumbnail: (data.induction_request.additional_data?.user_info?.profile_pic_ref?.sm_url) || _md_url,
                full_img: _md_url,
                object_fit: 'contain',
                disabled: !_md_url
            }
            const _smart_check_pic = this.getImageFromSmartCheck();
            this.change_request._smart_check_photo = {
                title: 'CSCS Smartcheck photo',
                thumbnail: _smart_check_pic,
                full_img: _smart_check_pic,
                object_fit: 'none',
                disabled: !_smart_check_pic
            };
            this.change_request.recent_changes = data.induction_request.recent_changes || [];
            // this.change_request.row.comments = (this.change_request.row.comments || []);
            this.inductionComments = [...this.change_request.row.comments || []].filter(c => !(c.origin === 'system' && c.user_id === this.change_request.row.user_ref)).reverse();
            if(this.projectContractor.rtw_status?.enabled){
                this.projectContractor.rtw_status._company_name = this.projectContractor.name;
                if((!this.change_request.row.rtw_doc_code || !this.change_request.row.rtw_check_result?.fetchedAt)){
                    this.change_request.row.rtw_check_result = new NotFoundRtwCheckResult();
                }else if(this.projectContractor.rtw_status.induction_approval_on.includes(this.change_request.row.rtw_check_result?.status)){
                    this.change_request._can_approve = true;
                }
            }
            // Prepare Completed Competence Status config after getting the induction request data
            this.setupCclStatusConfig();
            this.adminAccessData = data.induction_request.uac || {
                designation: [],
                permission: [],
                user_ref: data.induction_request.user_ref
            };
            this.showModal = true;
            this.isCollapse = false;
            this.activeAccessAccordion = ['accordion-1'];
            this.activeIds = ['list0'];
            this.content.open();
        });

    }

    private setupCclStatusConfig(): void {
		// Create the consolidated config object
		this.cclStatusConfig = {
			cclCheckData: this.change_request.row?.ccl_check_data,
			parentCompany: this.projectInfo?.parent_company,
			contractorCclConfig: this.projectContractor.ccl_status || {
				enabled: false,
				is_excluded_project: false
			},
			inductionPhrase: this.projectInfo?.custom_field?.induction_phrase_singlr || '',
			authUser: this.authUser$,
			fromApproval: true
		};
	}

    onStatusSelection($event){
        $event && setTimeout(() => {
            let elem: HTMLElement = <HTMLElement>document.querySelectorAll(`#access-admin-accordion #accordion-1:last-child`)[0];
            elem && elem.scrollIntoView({behavior: 'smooth', block: 'center'});
        }, 150);    // animation time of accordion rendering
    }

    getImageFromSmartCheck(): string {
        if(!this.change_request.row){
            return null;
        }
        const documents_list = this.change_request.row?.additional_data?.user_docs || [];
        let valid_cscs = documents_list.find((d: UserDocument) => d.is_verified && d.verification_info && d.verification_info.source === 'cscs');
        // console.log('valid_cscs: ', valid_cscs);
        if(valid_cscs && valid_cscs.verification_info){
            return valid_cscs.verification_info.photo;
        }
        return null;
    }

    onInductionRCDone($event){
        // console.log('Induction RC done', $event);
        this.initializeTable(true);
    }

    onRtwValidation($event){
        this.change_request._need_rtw_check = $event._need_rtw_check;
        this.change_request._can_approve = $event._can_approve;
        this.change_request.row.rtw_doc_code = $event.rtw_doc_code;
        this.change_request.row.rtw_check_result = $event.rtw_check_result || {};
    }

    @ViewChild('declarationAnswersComponent')
    private declarationAnswersComponentRef: DeclarationAnswersComponent;
    viewDeclarationAnswers(row){
        return this.declarationAnswersComponentRef.openDeclarationPopup(row);
    }

    @ViewChild('additionalDocUploaderComponent')
    private additionalDocUploaderComponentRef: AdditionalDocUploaderComponent;
    openMoreUploadPopup(row){
        this.update_row = row;
        this.additionalDocUploaderComponentRef.openMoreUploadPopup(row);
    }

    onDocumentUpload($event){
        console.log('Extra doc upload done');
        if($event){
            this.update_row = $event;
        }
        this.initializeTable(true);
    }

    notifyChangeRequest(form, $modalLoader, event) {
        let code = this.change_request.code;
        if (!form.valid) {
            console.log('Modal form is not valid');
            return;
        }
        let headerTxt = '';
        let title = '';
        if(code === 2){
            headerTxt = `Approve ${this.projectInfo.custom_field.induction_phrase_singlr}`;
            title = `You are about to approve <span class="fw-500">${this.change_request.row.name}</span>'s induction and they will be notified of this update. Do you wish to continue?`
        } else if (code === 3) {
            headerTxt = 'Request Change';
            title = `You are about to request changes to <span class="fw-500">${this.change_request.row.name}</span>'s induction and they will be notified of this update. Do you wish to continue?`
        } else if (code === 0) {
            headerTxt = `Reject ${this.projectInfo.custom_field.induction_phrase_singlr}`;
            title = `You are about to reject <span class="fw-500">${this.change_request.row.name}</span>'s induction and they will be notified of this update. Do you wish to continue?`
        }
        console.log(this.change_request);
        let comment = new Comment();
        comment.timestamp = dayjs().valueOf();
        comment.note = this.change_request.note;
        comment.user_id = this.authUser$.id;
        comment.name = this.authUser$.name;
        comment.origin = 'admin'; // as comment was admin generated
        if (this.change_request.row && this.change_request.row.id) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: `${headerTxt}`,
                title: `${title}`,
                confirmLabel: 'Continue',
                onConfirm: () => {
                    let row = this.change_request.row;
                    if (!row.comments) {
                        row.comments = [];
                    }

                    if (comment.note) {
                        row.comments.push(comment);
                    }

                    if (this.projectInfo.is_passport_require && this.change_request.passport_required) {
                        let userName = row.name || row.user_ref?.name;
                        let additionalComment = 'I have checked '+userName+'\'s passport/visa as part of the right to work check and I happy that the document is valid and belongs to '+userName+'. See passport/visa above in Competency/ID section which is a copy and clear reflection of the physical document.';
                        let commentTwo = new Comment();
                        commentTwo.timestamp = dayjs().valueOf();
                        commentTwo.note = additionalComment;
                        commentTwo.user_id = this.authUser$.id;
                        commentTwo.name = this.authUser$.name;
                        commentTwo.origin = 'admin';
                        row.comments.push(commentTwo);
                    }

                    let update = {
                        status_code: this.change_request.code,
                        comments: row.comments,
                        inductor_email: this.authUser$.email
                    };

                    if(this.change_request.code == this.STATUS_CODES.APPROVED) {
                        update['procorePermissionId'] = this.procorePermissionId;
                        update = {...update, ...(this.adminAccessData && this.adminAccessData.designation.length && {access: this.adminAccessData})};
                    }

                    $modalLoader.show = true;
                    this.userService.updateInductionRequest(row.id, update, 'admin').subscribe(data => {
                        $modalLoader.show = false;
                        event.closeFn();
                        this.closeNotifyChangeRequestModal();
                        if (!data.success) {
                            const message = data.message || 'Failed to update data.';
                            this.toastService.show(this.toastService.types.ERROR, message);
                        }
                        this.initializeTable(true);
                        if(data.induction_request){
                            // here needs user ref with details to resume optima flow.
                            if(data.induction_request.additional_data && data.induction_request.additional_data.user_info){
                                // data.induction_request.user_ref = data.induction_request.additional_data.user_info;
                                data.induction_request.first_name = data.induction_request.additional_data.user_info.first_name;
                                data.induction_request.last_name = data.induction_request.additional_data.user_info.last_name;
                                data.induction_request.name = data.induction_request.creator_name;
                            }
                            if(
                                data.induction_request.status_code === this.STATUS_CODES.APPROVED &&
                                this.optima_meta &&
                                !data.induction_request.optima_badge_number //  ??
                            ){

                                if(this.optima_meta.has_optima_source){
                                    this.initiateBadgeCreation(data.induction_request);
                                }else if(this.optima_meta.has_touch_byte_source){
                                    this.initiateManageTbAccess(data.induction_request);
                                }else{
                                    this.showDefaultApprovalModal(data.induction_request);
                                }
                            }else if(data.induction_request.status_code === this.STATUS_CODES.APPROVED){
                                // show simple approved status modal
                                // no need of badge generation/association process
                                this.showDefaultApprovalModal(data.induction_request);
                            }
                        }
                    });
                }
            });
        }
    }

    closeNotifyChangeRequestModal() {
        setTimeout(() => this.showModal = false, 0);
    }

    @ViewChild('defaultSuccessModal') private defaultSuccessModalRef: TemplateRef<any>;
    showDefaultApprovalModal(induction_request){
        this.optima_modal_row = induction_request;
        this.optima_badge_number = null;
        const modalRef = this.modalService.open(this.defaultSuccessModalRef, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
        });
    }

    @ViewChild('manageTbSiteAccessComponent')
    private manageTbSiteAccessComponentRef: ManageTbSiteAccessComponent;

    initiateManageTbAccess(row){
        // this.optima_modal_row = row;
        // this.optima_badge_number = null;
        return this.manageTbSiteAccessComponentRef.openManageTbUserModal(row.additional_data.user_info || row.user_ref, {
            force_create: true,
            induction_id: row.id
        }, () => {
            this.initializeTable(true);
        });
    }

    private getFirstName(optima_modal_row) {
        let f_name = [];
        if (optima_modal_row.first_name) {
            f_name.push(optima_modal_row.first_name);
        }
        if (optima_modal_row.middle_name && optima_modal_row.middle_name.trim()) {
            f_name.push(optima_modal_row.middle_name);
        }
        return f_name.join(' ');
    }

    @ViewChild('createBadgeModal')
    private createBadgeModalRef: TemplateRef<any>;

    initiateBadgeCreation(row, closeFn = () => {}){
        this.optima_modal_row = row;
        this.optima_badge_number = null;
        closeFn();
        return this.openCreateBadgeModal(this.createBadgeModalRef);
    }

    openCreateBadgeModal(content){
        let {groupId, statusId, timeSlotsId, error} = this.getBoxMetaInfo();
        if(error){
            return false;
        }
        this.optima_badge_number = null;
        this.optima_badge_request = {
            badgeNumber: 0,
            lastName: this.optima_modal_row.last_name,
            firstName: this.getFirstName(this.optima_modal_row),
            groupId,
            statusId,
            timeSlotsId,
        };
        const modalRef = this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
        });
        return this.createBadgeHandler(modalRef.close);
    }

    createBadgeHandler(closeFn){
        this.createBadgeInProgress = true;
        console.log(this.optima_badge_request);
        this.optimaService.createOptimaBadge(this.projectId, this.optima_modal_row.id, this.optima_badge_request).subscribe(data => {
            this.createBadgeInProgress = false;
            if(data.optima_badge_number){
                this.optima_badge_number = data.optima_badge_number;
                this.initializeTable(true);
            }else{
                const message = data.message || 'Failed to update data, please refresh the page and try again.';
                this.toastService.show(this.toastService.types.ERROR, message, { data:  data });
            }
        })
    }

    @ViewChild('assignBadgeNumberModal')
    private assignBadgeNumberModalRef: TemplateRef<any>;
    assignBadgeNumberModel(row){
        this.optima_modal_row = row;
        return this.openAssignBadgeNumberModal(this.assignBadgeNumberModalRef);
    }

    openAssignBadgeNumberModal(content){
        const modalRef = this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
        });
        return false;
    }

    searchBadge() {
        this.searchBadgeInProgress = true;
        this.badge_detail = {};
        let badge_number_to_search = this.optima_badge_search.badge_number;
        if(badge_number_to_search) {

            this.optimaService.getOptimaBadgeDetail(this.projectId, this.optima_badge_search.badge_number).subscribe((data: any) => {
                if(data.usagerResponseModelApi){
                    this.badge_detail.badgeOwnerStatus = data.usagerResponseModelApi.status;
                    this.badge_detail.badgeOwnerFirstName = data.usagerResponseModelApi.firstName;
                    this.badge_detail.badgeOwnerLastName = data.usagerResponseModelApi.lastName;
                    this.badge_detail.badgeOwnerLastName = data.usagerResponseModelApi.lastName;
                    this.badge_detail.induction_requests = data.induction_requests ? true : false;
                }else {
                    const message = data.message || 'Failed to fetch detail.';
                    this.toastService.show(this.toastService.types.ERROR, message, { data: data });
                }
                this.searchBadgeInProgress = false;
            });
        } else {
            const message = 'Badge number is required.';
            this.toastService.show(this.toastService.types.INFO, message);
        }

        return;
    }

    assignBadgeRequest(form, closeFn) {
        if (!form.valid) {
            console.log('Modal form is not valid');
            return;
        }

        let update = {
            optima_badge_number: form.value.badge_number
        };
        this.searchBadgeInProgress = true;
        this.userService.updateInductionRequest(this.optima_modal_row.id, update, 'admin').subscribe(data => {
            this.searchBadgeInProgress = false;
            if (!data.success) {
                const message = data.message || 'Failed to update record.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            } else {
                closeFn('close');
            }
            this.initializeTable(true);
        });
    }

    filteredUser(u){
        return u.filter(r => r.designation && r.designation.includes(UACProjectDesignations.INDUCTOR));
    }

    trackByRowIndex(index: number, obj: any){
        return index;
    }

    @ViewChild('inviteToInductionModal') private inviteToInductionModalRef: IModalComponent;
    @ViewChild('inviteToInductionForm') inviteToInductionForm;
    openInviteToInductionModal(){
        this.inductionInvitationEmail = [{email: ""}];
        if(this.inviteToInductionForm) {
            this.inviteToInductionForm.reset();
            this.inviteToInductionForm.form.statusChanges.subscribe(status => {
                this.inviteBtnStatus = status !== 'VALID';
            });
        }
        this.inviteToInductionModalRef.open();
    }

    addEmailField(){
        if(!this.inductionInvitationEmail){
            this.inductionInvitationEmail = [{email: ""}];
        }
        this.inductionInvitationEmail.push({
            email: ""
        });
    }

    removeEmailField(e: any, i: number){
        this.inductionInvitationEmail.splice(i, 1);
        this.checkForDuplicateEmails();
    }

    checkForDuplicateEmails(){
        let emails = this.inductionInvitationEmail.map((item) => item.email?.toLowerCase().trim());
        let duplicates = emails.filter((email, index) => emails.indexOf(email) !== index);
        this.isDuplicateEmails = duplicates.length ? true : false;
        this.inductionInvitationEmail.forEach((item) => {
          item['isDuplicate'] = duplicates.includes(item.email?.toLowerCase().trim());
        });
    }

    sendInductionInvitation() {
        this.inductionInvitationEmail = this.inductionInvitationEmail.map(({ isDuplicate, ...rest }) => rest);
        let invite_request = {'recipients':this.inductionInvitationEmail}
        this.sendingInvitationInProgress = true;
        this.userService.inviteToInductAlert(this.projectInfo.id, invite_request).subscribe(data => {
            this.sendingInvitationInProgress = false;
            if (!data.success) {
                const message = data.message || 'Failed to send invitation.';
                this.toastService.show(this.toastService.types.ERROR, message);
            } else {
                this.inviteToInductionModalRef.close();
                const message = 'Invites successfully sent.';
                this.toastService.show(this.toastService.types.SUCCESS, message);
                this.inductionInvitationEmail = [{email: ""}];
            }
            this.initializeTable(true);
        });
    }

    @ViewChild('fingerprintEnrolmentModal')
    private fingerprintEnrolmentModalRef: TemplateRef<any>;
    @ViewChild('viewBadgeInfoModal') private viewBadgeInfoModalRef: IModalComponent;
    initFingerprintEnrolmentModal(row){
        this.optima_modal_row = row;
        this.leftHandEnroled = false;
        this.rightHandEnroled = false;
        this.leftHandEnroledFailed = false;
        this.rightHandEnroledFailed = false;
        this.fingerprint_enrolment_request = {};
        this.badge_information = {};

        this.retrieveBadgeInformation(row.optima_badge_number, true);

    }
    submitRemoteEnrolment(){
        this.processingLoader = true;
        this.optimaService.remoteEnrolUserToOptima(this.projectId, this.optima_modal_row.id).subscribe(data => {
            this.processingLoader = false;
            if(data.success){
                this.retrieveBadgeInformation(this.optima_modal_row.optima_badge_number);
            }else{
                const message = data.message || 'Failed to do remote enrolment.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }
    /*
    openFingerprintEnrolmentModal(content, row){
        const modalRef = this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
        });
        this.retrieveBadgeInformation(row.optima_badge_number);
        return false;
    }*/

    selectFinger(fingerType) {
        if(fingerType == 'leftHandFinger') {
            this.fingerprint_enrolment_request.fingerprintIndex = this.leftHandFingerIndex;
            if(this.leftHandEnroled){
                return this.deleteFingerPrintEnrolment(this.leftHandFingerIndex);
            }
        } else {
            this.fingerprint_enrolment_request.fingerprintIndex = this.rightHandFingerIndex;
            if(this.rightHandEnroled){
                return this.deleteFingerPrintEnrolment(this.rightHandFingerIndex);
            }
        }
    }

    submitFingerprintEnrolement() {
        this.fingerprint_enrolment_request.badgeNumber = this.optima_modal_row.optima_badge_number;
        this.processingLoader = true;
        this.optima_modal_row.enrolled = false;
        this.optimaService.optimaFingerprintEnrolment(this.projectId, this.fingerprint_enrolment_request).subscribe(data => {
            this.processingLoader = false;
            if (data.success && this.has_facial_enrolment_type) {
                this.showFacialEnrolmentSuccess();
            } else if(data.success){
                this.continuousCheckStatus();
            }else {
                const message = data.message;
                if(data.already_enrolled){
                    this.toastService.show(this.toastService.types.INFO, message);
                    this.deleteFingerPrintEnrolment(0);
                }
                else if(data.message) {
                    this.toastService.show(this.toastService.types.INFO, message);
                } else {
                    const message = 'Failed to do fingerprint enrolement.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                }
            }
            //cb();
        });
    }

    private getAccessGroup(deny = false){
        let label = deny ? 'Deny Access' : 'Full Access';
        let access_group = this.optima_access_group.find((access_group => (access_group.libelle == label))) || {};
        if(!access_group.id) {
            const message = `${deny ? 'Deny' : 'Full'} access group not available.`;
            this.toastService.show(this.toastService.types.INFO, message);
        }
        return access_group;
    }

    onBadgeInfoCancelClick({closeFn}){
        // fallback condition
        if (!this.badge_information) {
            closeFn();
        }
        // when is already enrolled
        // OR
        // On first step of enrolment
        if (
            this.badge_information.has_enrolment ||
            !this.badge_information._enrolment_mode
        ) {
            closeFn();
        }
        // when NOT enrolled
        // &&
        // when one of enrolment is chosen
        // &&
        // when remote enrolment is NOT enabled
        if (this.badge_information._enrolment_mode && !this.badge_information._remote_enrolment_enabled) {
            closeFn();
        } else if (this.badge_information._enrolment_mode) {
            // when NOT enrolled && when one of enrolment is chosen && remote enrolment is enabled
            this.badge_information._enrolment_mode = null;
        }
    }

    private onToggleAccessConfirm(event, row, fieldType, withoutAlert = false){
        let access_group: {
            id?: any;
            libelle?: any;
        } = {};
        if (fieldType == 'checkbox') {
            if(event.target.checked) {
                access_group = this.getAccessGroup(true);
                if(!access_group.id) {
                    return;
                }
            } else {
                access_group = this.getAccessGroup();
                if(!access_group.id) {
                    return;
                }
            }
        }

        if (fieldType == 'selectbox') {
            access_group = this.optima_access_group.find((access_group => (access_group.id == this.badge_access_info.groupId))) || {};
        }

        this.badge_access_info.groupId = access_group.id;
        this.badge_access_info.groupName = access_group.libelle;
        this.processingLoader = true;
        let firstName = this.getFirstName(this.optima_modal_row);
        let lastName = this.optima_modal_row.last_name;
        if(withoutAlert){
            firstName = row.first_name;
            lastName = row.last_name;
        }
        this.optimaService.toggleOptimaAccess(row.optima_badge_number, this.badge_access_info.groupId, this.projectId, {
            lastName,firstName
        }).subscribe(data => {
            this.processingLoader = false;
            if (data.success) {
                const message = data.message || 'Access group updated successfully.';
                !withoutAlert && this.toastService.show(this.toastService.types.SUCCESS, message);
            } else {
                const message = data.message || 'Failed to update access group of the badge number.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    toggleOptimaAccess(event, row, cb, fieldType, withoutAlert = false) {
        if(withoutAlert){
            return this.onToggleAccessConfirm(event, row, fieldType, withoutAlert);
        }
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Warning',
            title: `Do you really want to change the user access?`,
            confirmLabel: 'Continue',
            onConfirm: () => {
                this.onToggleAccessConfirm(event, row, fieldType, withoutAlert);
            }
        });
    }

    retrieveBadgeInformation(badge_number, openModal = false) {
        this.processingLoader = true;
        this.optimaService.retrieveBadgeInformation(badge_number, this.projectId).subscribe(data => {
            this.processingLoader = false;
            if(data.badge_information && data.badge_information.usagerResponseModelApi){
                const _md_url = (this.optima_modal_row.additional_data?.user_info?.profile_pic_ref?.md_url) || (this.optima_modal_row.additional_data?.user_info?.profile_pic_ref?.file_url);
                // console.log(`enrolment_face_url`, _md_url);
                this.badge_information = {
                    _remote_enrolment_enabled: (this.projectContractor.fr_setting?.optima_auto_enrol && _md_url),
                    badge: data.badge_information.usagerResponseModelApi,
                    has_enrolment: data.has_enrolment,
                    remote_enrolment: data.remote_enrolment,
                    has_remote_enrolment: data.has_remote_enrolment,
                    _remote_enrolled_photo: {
                        title: 'Enrolled Photo',
                        thumbnail: data.remote_enrolment?.base64,
                        full_img: data.remote_enrolment?.base64,
                        object_fit: 'contain',
                        // thumbnail_class: 'photo-contain',
                        disabled: !data.has_remote_enrolment
                    },
                    _enrolment_face_url: _md_url,
                };
                this.badge_access_info.groupId = data.badge_information.usagerResponseModelApi.groupId;
                this.badge_access_info.groupName = data.badge_information.usagerResponseModelApi.groupName;
                if(this.has_facial_enrolment_type && data.badge_information.usagerResponseModelApi && data.badge_information.usagerResponseModelApi.printsModel && data.badge_information.usagerResponseModelApi.printsModel.faceEnrolled){
                    this.optima_modal_row.enrolled = true;
                }
                if(!data.has_enrolment && !(this.badge_information._remote_enrolment_enabled)){
                    // FR enabled + with optima_auto_enrol NOT enabled
                    this.badge_information._enrolment_mode = 'manual';
                }
                if(openModal){
                    this.viewBadgeInfoModalRef.open();
                }
            } else {
                const err_msg = data.message || `Failed while retrieving badge information.`;
                this.toastService.show(this.toastService.types.ERROR, err_msg, { data: data });
            }
        });
    }

    getEnrollmentStatus(_modelRef) {
        this.optimaService.getEnrollmentStatus(this.projectId, this.optima_modal_row.optima_badge_number).subscribe(data => {
            if (data.success) {
                // got response
                this.optima_modal_row.enrolled = data.enrolled || false;
                if(this.optima_modal_row.enrolled){
                    this.closeModalAfterSometime(_modelRef);
                }
            } else {
                const message = data.message;
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    closeModalAfterSometime(_modelRef, seconds = 3){
        setTimeout(() => {
            if(_modelRef && _modelRef.dismiss){
                _modelRef.dismiss();
            }
        }, seconds * 1000)
    }

    @ViewChild('showEnrolmentProgressModal', { static: true })
    private showEnrolmentProgressModalRef: TemplateRef<any>;


    intervalInstance: any;
    continuousCheckStatus(){
        let attempt = 0;
        this.processingLoader = true;
        const modalRef = this.modalService.open(this.showEnrolmentProgressModalRef, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: 'sm',
            beforeDismiss: () => {
                if (intervalInstance) {
                    this.processingLoader = false;
                    clearInterval(intervalInstance);
                }
                this.retrieveBadgeInformation(this.optima_modal_row.optima_badge_number);
                return true;
            }
        });
        let intervalInstance = setInterval(() => {
            if(this.optima_modal_row.enrolled){
                clearInterval(intervalInstance);
                return;
            }
            if(attempt >= 36){ // 36 = equivalent to 3 min interval (180/5)
                this.processingLoader = false;
                modalRef.dismiss();
                clearInterval(intervalInstance);
                return;
            }

            attempt++;
            this.getEnrollmentStatus(modalRef);

        }, 5 * 1000)
    }

    @ViewChild('facialEnrolmentDoneModal', { static: true })
    private facialEnrolmentDoneModalRef: TemplateRef<any>;

    showFacialEnrolmentSuccess(){
        const modalRef = this.modalService.open(this.facialEnrolmentDoneModalRef, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: 'sm',
            beforeDismiss: () => {
                this.retrieveBadgeInformation(this.optima_modal_row.optima_badge_number);
                return true;
            }
        });
    }

    deleteBadgeNumber(row) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Are you sure you want to delete the user badge number?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.optimaService.deleteBadgeNumber(row.optima_badge_number, this.projectId).subscribe(data => {
                    if (data.success) {
                        const message = data.message || 'Badge has been deleted.';
                        this.toastService.show(this.toastService.types.SUCCESS, message);
                    } else {
                        const message = 'Failed to delete the badge.';
                        this.toastService.show(this.toastService.types.ERROR, message);
                    }
                    this.viewBadgeInfoModalRef.close();
                });
            }
        });
    }

    deleteFingerPrintEnrolment(fingerIndex){
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Are you sure you want to delete this user's enrolment?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.processingLoader = true;
                this.optimaService.deleteFingerprintEnrolment(this.projectId, this.optima_modal_row.optima_badge_number, fingerIndex).subscribe(data => {
                    this.processingLoader = false;
                    if (data.success) {
                        const message = data.message || 'Enrolment removed.';
                        this.toastService.show(this.toastService.types.SUCCESS, message);
                    } else {
                        const message = 'Failed to delete fingerprint.';
                        this.toastService.show(this.toastService.types.ERROR, message);
                    }
                    this.retrieveBadgeInformation(this.optima_modal_row.optima_badge_number);
                });
            }
        });
    }

    private getBoxMetaInfo(showError = true){
        let boxInfo = this.optima_meta?.optima;
        if(!boxInfo || !boxInfo.timeSlots || !boxInfo.groups || !boxInfo.status){
            const message = 'Optima box is either offline or having some problem.<br>Please try after sometime.';
            showError && this.toastService.show(this.toastService.types.INFO, message);
            return {
                error: true,
            };
        }
        let group = boxInfo.groups.find(access_group => (access_group.libelle == 'Full Access'));
        let status = boxInfo.status.find(s => (s.libelle == 'In service'));
        let timeSlots = boxInfo.timeSlots.find(ts => (ts.libelle == 'Group slot'));
        if(!group || !status || !timeSlots){
            const message = 'Optima box doesn\'t have valid "Access Group / Status Group / Time slot".';
            showError && this.toastService.show(this.toastService.types.INFO, message);
            return {
                error: true,
            };
        }
        return {
            groupId: group && group.id,
            statusId: status && status.id,
            timeSlotsId: timeSlots && timeSlots.id
        }
    }

    accessCardsLoading: boolean = false;
    selectedAccessCardNo: number;
    recentUnrecognizedEvents: Array<{
        event_date_time: number;
        badge_number: number;
        entity: string;
        unit_name: string;
        reader_name: string;
        event_type: string;
        user_ref: number;
    }> = [];
    selectedInductionRef: any = {};
    @ViewChild('accessCardLinkModal', {  })
    private accessCardLinkModalRef: TemplateRef<any>;

    openAccessCardLinkModal(induction_ref, {faceEnrolled, indexFingerPrint}: {faceEnrolled?: number; indexFingerPrint?:any[]} = {faceEnrolled:0, indexFingerPrint:[]}){
        let has_enrolment = (faceEnrolled || (indexFingerPrint && indexFingerPrint.length));
        console.log('Access card linking record_id:', induction_ref.record_id, 'has any enrolment?', has_enrolment);
        if(has_enrolment) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Warning',
                title: `Carrying out this action will remove previous enrolment, would you like to continue?`,
                confirmLabel: 'Continue',
                onConfirm: () => {
                    this.viewBadgeInfoModalRef.close();
                    this.openAccessCardLinkModalConfirm(induction_ref, has_enrolment);
                }
            });
        } else {
            this.viewBadgeInfoModalRef.close();
            this.openAccessCardLinkModalConfirm(induction_ref, has_enrolment);
        }
    }

    openAccessCardLinkModalConfirm(induction_ref, has_enrolment) {
        let {groupId, statusId, timeSlotsId, error} = this.getBoxMetaInfo();
        if(error){
            return false;
        }
        this.selectedInductionRef = {
            id: induction_ref.id,
            record_id: induction_ref.record_id,
            name: induction_ref?.additional_data?.user_info?.name || induction_ref?.additional_data?.user_info?.first_name,
            has_enrolment,
            groupId,
            statusId,
            timeSlotsId,
        };
        this.selectedAccessCardNo = undefined;
        // closeFn && closeFn();
        const modalRef = this.modalService.open(this.accessCardLinkModalRef, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
        });
        this.accessCardsLoading = true;
        this.optimaService.sa_getRecentEvents(this.projectId).subscribe((data) => {
            this.accessCardsLoading = false;
            if(data.success){
                this.recentUnrecognizedEvents = data.events;
            }else{
                const message = data.message || 'Failed to get recent events.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    switchInductionBadgeWith(closeFn){
        console.log(`Switch induction id ${this.selectedInductionRef.id} badge with`, this.selectedAccessCardNo);
        this.accessCardsLoading = true;
        this.optimaService.sa_switchInductionBadgeWith(this.projectId, this.selectedInductionRef.id, this.selectedAccessCardNo, this.selectedInductionRef).subscribe((data) => {
            this.accessCardsLoading = false;
            if(data.success){
                closeFn && closeFn();
                this.openDefaultSuccessModal(data.message || `Record has been updated`);
                this.initializeTable(true);
            }else{
                const message = data.message || 'Failed to update record.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });

    }

    //
    // // projectTabset
    // @ViewChild('projectTabSet')
    // projectTabSetRef: TemplateRef<any>;
    //
    // // timeManagementTabSet
    // @ViewChild('timeManagementTabSet')
    // timeManagementTabSetRef: TemplateRef<any>;

    openInductionForm(row, target = 'html'){
        let params: any = {
            nowMs: dayjs().valueOf(),
        };
        if (this.authUser$.timezone) {
            params.tz = this.authUser$.timezone;
        }
        this.loadingInProgress = true;
        this.userService.downloadInductionForm(row.id, row.createdAt, params, target, (data) => {
            this.loadingInProgress = false;
        });
    }

    getQRPosterData(){
        this.resourceService.getCompanySettingByName('qr_code_config', this.projectResolverResponse.contractor.id).subscribe((data: any) => {
            if (data.success && data.record && data.record.value) {
                this.showQRDownloadButton = true
            }
        }, error => {
            const message = 'An error occurred while fetching data.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: error});
        }, () => {
            this.processingLoader = false;
        });
    }

    downloadQRPoster(){
        this.loadingInProgress = true;
        this.userService.downloadQRPoster(this.projectResolverResponse.contractor.id, this.projectId, (data) => {
            this.loadingInProgress = false;
        });
    }

    downloadInvitesSent() {
        this.sendingInvitationInProgress = true;
        this.userService.downloadInvitesSent(this.projectId, `${this.projectInfo.custom_field.induction_phrase_singlr} Invite List - Project ${this.projectId} ${dayjs().format(this.dateFormat)}.xlsx`, () => {
            this.sendingInvitationInProgress = false;
        });
    }

    @ViewChild('manageBlacklistingModal')
    private manageBlacklistingModalRef: ManageBlacklistingComponent;
    blackListUserInduction(induction_row){
        if(induction_row.status_code === this.STATUS_CODES.BLACKLISTED){
            const message = 'Reinstating users access can only be carried out by a company portal admin.';
            this.toastService.show(this.toastService.types.WARNING, message);
        } else {
            let payload = {
                id: induction_row.id,
                record_id: induction_row.record_id,
                status_code: induction_row.status_code,
                optima_badge_number: induction_row.optima_badge_number,
                last_name: induction_row.last_name,
                optima_first_name: this.getFirstName(induction_row),
                comments: (induction_row.comments || []),
                admin_id: this.authUser$.id,
            };
            this.manageBlacklistingModalRef.blackListUserInduction(payload);
        }
    }

    onBlackListingResponse(data){
        // console.log(data);
        this.initializeTable(true);
    }

    modalActionInProgress: boolean = false;
    medication_edit_row: any = {};
    medicationValidations: Array<any> = [];

    @ViewChild('manageMedicationModalRef')
    private manageMedicationModalRef: IModalComponent;
    addMedicationPopup(row){
        this.medication_edit_row = {};
        this.medicationValidations = [];
        this.modalActionInProgress = true;
        this.userService.getInductionMedications(this.projectId, row.id).subscribe(data => {
            this.modalActionInProgress = false;
            if (data.success) {
                this.medication_edit_row = {
                    ...row,
                    ...(data.induction)
                };
                this.medicationValidations = (this.medication_edit_row.medications || []).map(m => false);
                this.manageMedicationModalRef.open();
            } else {
                const message = data.message || 'Failed to fetch data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });

    }

    saveInductionMedications(event){
        let payload = {
            reportable_medical_conditions: this.medication_edit_row.reportable_medical_conditions,
            rmc_detail: this.medication_edit_row.rmc_detail,
            on_long_medication: this.medication_edit_row.on_long_medication,
            any_side_effects: this.medication_edit_row.any_side_effects,
            any_side_effect_detail: this.medication_edit_row.any_side_effect_detail,
            medications: this.medication_edit_row.medications,
        };
        let name = this.medication_edit_row.additional_data && this.medication_edit_row.additional_data.user_info && this.medication_edit_row.additional_data.user_info.name;
        this.modalActionInProgress = true;
        this.userService.updateInductionMedications(this.projectId, this.medication_edit_row.id, payload).subscribe(data => {
            this.modalActionInProgress = false;
            if (data.success) {
                event.closeFn();
                this.openDefaultSuccessModal(`${name} medication records have been updated`);
                this.initializeTable(true);
            } else {
                const message = data.message || 'Failed to update medication data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    isDisabled(key:string){
        return this.medication_edit_row[key] !== 'yes';
    }

    addMedicationBlock(){
        this.medication_edit_row.medications.push(this.emptyMedicationRow());
        this.medicationValidations.push(true);
    }

    private emptyMedicationRow(){
        return {
            identifier: (new Date()).getTime()
        };
    }

    onLongMedicationChanged(yesOlmCheckboxRef){
        let isEnabled = !this.isDisabled('on_long_medication');
        let name = this.medication_edit_row.additional_data && this.medication_edit_row.additional_data.user_info && this.medication_edit_row.additional_data.user_info.name;
        if(isEnabled && this.medication_edit_row.medications.length === 0){
            this.addMedicationBlock();
        }
        else if(!isEnabled){
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Remove Medication',
                title: `By doing this, all medication will be cleared from the ${this.projectInfo.custom_field.induction_phrase_singlr} records of <span class="fw-500">${name}</span>. Do you wish to continue?`,
                confirmLabel: 'Continue',
                onConfirm: () => {
                    this.medication_edit_row.medications = [];
                    this.medicationValidations = [];
                },
                onClose: () => {
                    this.medication_edit_row.on_long_medication = 'yes';
                    yesOlmCheckboxRef.checked = true;
                }
            });
        }
    }

    updateMedicationRow(data: any, index: number){
        this.medication_edit_row.medications[index] = data;
    }

    removeMedicationRow(data: any, index: number){
        return this.medication_edit_row.medications[index] ? this.medication_edit_row.medications.splice( index, 1 ) : null;
    }

    updateValidationStatus($event, i){
        console.log('$event', i, '=', $event);
        this.medicationValidations[i] = $event;
    }

    medicationValid(index?: number): boolean {
        if(index !== undefined){
            return this.medicationValidations[index] === false;
        }
        return this.medicationValidations.findIndex(r => r === true) !== -1;
    }

    company_edit_row: any = {};
    company_update_request: {
        employer?: string;
        employer_id?: number;
    } = {};
    companiesList: Array<any> = [];

    @ViewChild('editUserCompanyModalRef')
    private editUserCompanyModalRef: IModalComponent;
    editUserCompanyPopup(row){
        this.company_edit_row = row;
        //set employement info

        this.user_emp_detail.job_role = row?.additional_data?.employment_detail?.job_role;
        if(this.user_emp_detail.job_role && this.user_emp_detail.job_role.indexOf('Other') !== -1){
            this.user_emp_detail.other_job_role = this.user_emp_detail.job_role.replace(/Other|\(|\)/ig, '').trim();
            this.user_emp_detail.job_role = 'Other';
        }
        this.user_emp_detail.type_of_employment = row?.additional_data?.employment_detail?.type_of_employment;
        this.user_emp_detail.employment_company = row?.additional_data?.employment_detail?.employment_company;
        if(this.user_emp_detail.type_of_employment && this.user_emp_detail.type_of_employment.indexOf('Other') !== -1){
            this.user_emp_detail.other_type_of_employment = this.user_emp_detail.type_of_employment.replace(/Other|\(|\)/ig, '').trim();
            this.user_emp_detail.type_of_employment = 'Other';
        }
        this.user_emp_detail.employer = row?.additional_data?.employment_detail?.employer;
        this.user_emp_detail.parent_company = (row?.additional_data?.user_info?.parent_company?.id) || (row?.additional_data?.user_info?.parent_company);
        this.showEditUserCompanyForm = true;
        this.editUserCompanyModalRef.open();
    }

    onCloseCompanyModal() {
        this.showEditUserCompanyForm = false;
    }

    inductionCompanySelected($event, field) {
        if (field == 'employer') {
            this.user_emp_detail.employer = $event?.record?.name;
            this.user_emp_detail.parent_company = $event?.record?.id;
        } else if (field == 'employment_company') {
            this.user_emp_detail.employment_company = $event?.record?.name;
        }
    }

    saveInductionCompanyChange(event) {
        let name = this.company_edit_row.additional_data && this.company_edit_row.additional_data.user_info && this.company_edit_row.additional_data.user_info.name;
        this.modalActionInProgress = true;
        this.userService.updateUserEmployment(this.projectId, this.company_edit_row.id, this.company_edit_row.createdAt, this.user_emp_detail).subscribe(data => {
            this.modalActionInProgress = false;
            if (data.success) {
                event.closeFn();
                this.showEditUserCompanyForm = false;
                this.openDefaultSuccessModal(`Company information has been updated for ${name}`);
                this.initializeTable(true);
            } else {
                const message = data.message || 'Failed to update company information.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    editSuccessMessage: string;
    @ViewChild('editSuccessModal') private editSuccessModalRef: TemplateRef<any>;
    openDefaultSuccessModal(message){
        this.editSuccessMessage = message;
        this.modalService.open(this.editSuccessModalRef, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
        });
    }

    @ViewChild('showMedicalConditionsModal') private showMedicalConditionsModalRef: IModalComponent;
    openMedicalConditionsModal(row: any, firstChild: any = null) {
        this.record = row;
        firstChild && firstChild.parentElement && firstChild.parentElement.parentElement && firstChild.parentElement.parentElement.blur();
        this.showMedicalConditionsModalRef.open();
    }

    changeModel(ev, val) {
        if (ev.target.checked) {
            this.adminAccessData.designation.push(val);
            if(val == UACProjectDesignations.NOMINATED) {
                for (let i = 0; i < this.toolsList.length; i++) {
                    if(this.toolsList[i].has_nom_email) {
                        this.adminAccessData.permission.push(`${this.toolsList[i].key}:notify`);
                    }
                }
            }
        } else {
            let i = this.adminAccessData.designation.indexOf(val);
            this.adminAccessData.designation.splice(i, 1);
            if(val == UACProjectDesignations.NOMINATED) {
                for (let i = 0; i < this.toolsList.length; i++) {
                    if(this.toolsList[i].has_nom_email) {
                        let j = this.adminAccessData.permission.indexOf(`${this.toolsList[i].key}:notify`);
                        this.adminAccessData.permission.splice(j, 1);
                    }
                }
            }
        }
        return this.adminAccessData.designation;
    }

    selectAll() {
        this.adminAccessData.permission = [];
        if(this.adminAccessData.designation.includes(UACProjectDesignations.CUSTOM)) {
            for (let i = 0; i < this.toolsList.length; i++) {
                let anythingKey = this.permissionUtility.getPermissionKey(this.toolsList[i].key, OPERATIONS.ANYTHING);
                let addKey = this.permissionUtility.getPermissionKey(this.toolsList[i].key, OPERATIONS.ADD);
                this.adminAccessData.permission.push(anythingKey);
                this.adminAccessData.permission.push(addKey);
            }
        }
        for (let i = 0; i < this.toolsList.length; i++) {
            if(this.toolsList[i].has_nom_email) {
                this.adminAccessData.permission.push(`${this.toolsList[i].key}:notify`);
            }
        }
    }

    deselectAll() {
        this.adminAccessData.permission = [];
    }

    onAccessLevelSelect($event) {
        this.adminAccessData.designation = ($event != '') ? [$event] : [];
        if([UACProjectDesignations.FULL_ACCESS].includes($event)) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Warning',
                title: `This access level will permit the user to see all project ${this.projectInfo?.custom_field.induction_phrase_singlr} records which may contain sensitive data. Would you still like to assign this access level to the user?`,
                confirmLabel: 'Yes',
                cancelLabel: 'No',
                onConfirm: () => {},
                onClose: () => {
                    let i = this.adminAccessData.designation.indexOf($event);
                    this.adminAccessData.designation.splice(i, 1);
                }
            });
        }

        return this.adminAccessData.designation;
    }


    returnAccessLevelValue(user) {
        if(user.designation.includes(UACProjectDesignations.FULL_ACCESS)) {
            return UACProjectDesignations.FULL_ACCESS;
        } else if(user.designation.includes(UACProjectDesignations.RESTRICTED)) {
            return UACProjectDesignations.RESTRICTED;
        } else if(user.designation.includes(UACProjectDesignations.DELIVERY_MANAGER)) {
            return UACProjectDesignations.DELIVERY_MANAGER;
        } else if(user.designation.includes(UACProjectDesignations.CUSTOM)) {
            return UACProjectDesignations.CUSTOM;
        } else {
            return null;
        }
    }

    @ViewChild('customizeAccessRef') customizeAccessModalRef: IModalComponent;
    showCustomizeAccessModal(type = 'access'){
        this.accessUser = this.adminAccessData || {};
        this.blockType = type;
        this.modalTitle = (type == 'notification') ? "Customize Notifications" : this.modalTitle;

        this.showCustomAccessModal = true;
        this.customizeAccessModalRef.open();
    }

    toggleSelectDeselectAll() {
        this.isSelectAllActive = !this.isSelectAllActive;
        (this.isSelectAllActive) ? this.selectAll() : this.deselectAll();
    }

    closeCustomAccessModal(event?) {
        this.isSelectAllActive = false;
        this.showCustomAccessModal = false;
        if(event) {
            event.closeFn();
        }
    }

    closeHealthConcernModal(event) {
        event.closeFn();
    }

    toggleEmailNotify(ev, feature) {
        if (ev.target.checked) {
            this.adminAccessData.permission.push(`${feature}:notify`);
        } else {
            let k = this.adminAccessData.permission.indexOf(`${feature}:notify`);
            this.adminAccessData.permission.splice(k, 1);
        }

        return this.adminAccessData.permission;
    }

    toggleFeatureAccess(ev, feature) {
        let anythingKey = this.permissionUtility.getPermissionKey(feature, OPERATIONS.ANYTHING);
        let addKey = this.permissionUtility.getPermissionKey(feature, OPERATIONS.ADD);
        if (ev.target.checked) {
            this.adminAccessData.permission.push(anythingKey);
            this.adminAccessData.permission.push(addKey);

            if(feature == 'induction') {
                this.confirmationModalRef.openConfirmationPopup({
                    headerTitle: 'Warning',
                    title: `This access level will permit the user to see all project ${this.projectInfo?.custom_field.induction_phrase_singlr} records which may contain sensitive data. Would you still like to assign this access level to the user?`,
                    confirmLabel: 'Yes',
                    cancelLabel: 'No',
                    onConfirm: () => {},
                    onClose: () => {
                        let i = this.adminAccessData.permission.indexOf(anythingKey);
                        this.adminAccessData.permission.splice(i, 1);
                        let k = this.adminAccessData.permission.indexOf(addKey);
                        this.adminAccessData.permission.splice(k, 1);
                    }
                });
            }
        } else {
            let i = this.adminAccessData.permission.indexOf(anythingKey);
            this.adminAccessData.permission.splice(i, 1);
            let k = this.adminAccessData.permission.indexOf(addKey);
            this.adminAccessData.permission.splice(k, 1);
        }

        return this.adminAccessData.permission;
    }

    showEmploymentCompanyPicker(){
        return ['Agency', 'Self Employed', 'Contractor'].includes(this.user_emp_detail.type_of_employment);
    }

    resetForOther(ddSelection: string, key: string){
        if(ddSelection !== 'Other'){
            this.user_emp_detail[key] = null;
        }
        if(!this.showEmploymentCompanyPicker()){
            this.user_emp_detail.employment_company = null;
        }
    }

    otherTypeOfEmpNotRequired(){
        return (this.user_emp_detail.type_of_employment !== 'Other');
    }

    employmentCompanySelected($event){
        console.log('got emp company selection', $event);
        this.user_emp_detail.employment_company = $event.name;
    }

    get employmentCompanyFieldLabel(){
        let t = this.user_emp_detail.type_of_employment;
        if(t === 'Agency'){
            return 'Agency';
        }else if (t === 'Contractor'){
            return 'Direct Employer';
        }else if(t === 'Self Employed'){
            return 'Company name';
        }
        return '';
    }

    otherJobRoleNotRequired(){
        return (this.user_emp_detail.job_role !== 'Other');
    }

    private fetchJobRoles(){
        let country_code = (this.projectInfo?.custom_field?.country_code || undefined);
        let params = {country_code: country_code};
        this.userService.getJobRoles(params).subscribe((data: any) => {
            if (data.success) {
                this.allowedJobRoles = data.jobrolelist;
            } else {
                const message = data.message || 'Failed to fetch job roles.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    @ViewChild('cautionCardModalRef')
    private cautionCardModalRef: IModalComponent;
    openConductCardModal(row: any) {
        this.userDetails = row;
        if (row.status_code === this.STATUS_CODES.APPROVED) {
            this.clearConductCardForm();
            this.userDetails.conduct_cards = this.userDetails?.conduct_cards.map((el: any) => {
                el.dateIssued = dayjs(el.createdAt).format(AppConstant.displayDateFormat);
                el.expire = el?.conduct_card_ref?.expire_in?.month ? el.conduct_card_ref.expire_in.month+' Months' : null;
                return el;
            });
            this.userDetails.conduct_cards.sort((a, b) => b.updatedAt - a.updatedAt);
            this.selectedProjectAdminIds = [];
            this.cautionCardModalRef.open();
        } else if (row.status_code === this.STATUS_CODES.BLACKLISTED) {
            this.irBlockReason(row);
        } else if (row.status_code === this.STATUS_CODES.BLOCKED) {
            this.irBlockReason(row);
        }
    }

    getAdmins(){
        this.userService.getProjectAdmins(this.projectId, false, true, undefined, ['other', 'restricted', 'delivery_management']).subscribe((data: any) => {
            if(data.success){
                this.projectAdmins = data.admins;
            }else{
                const message = data.message || 'Failed to fetch project admins.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        })
    }

    clearConductCardForm() {
        this.selectedConductCard = null;
        this.cardAction = null;
        this.conductComment = null;
    }

    openModal(modalRef, size='md', windowClass="modal_v2") {
        return this.modalService.open(modalRef, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            centered: true,
            size: size,
            windowClass: windowClass,
            beforeDismiss: () => {
                console.log("Modal close");
                return true;
            }
        });
    }

    onSubmit(c_card) {
        const form = c_card.form;
        const id = form.value.id;
        const params = {
            user_ref : this.userDetails.user_ref,
            project_ref : this.projectId,
            comment : form.value.comment,
            induction_id: this.userDetails.id,
            project_admins : this.selectedProjectAdminIds,
        }
        this.assignConductCards(id, params);
        this.modalService.dismissAll();
    }

    onChange(c_card) {
        const form = c_card.form;
        this.cardAction = this.conductCardsList.find(el => el.id === form.value.id).card_action;
    }

    getConductCardsByProjectId() {
        this.processingLoader = true;
        this.conductCardsService.getConductCardsByProjectId(this.projectId).subscribe({
            next: (res: any) => {
                this.conductCardsList = res?.card_conducts ?? [];
            },
            error: (err: any) => {
                this.processingLoader = false;
                console.log('Failed API response ', err);
            },
            complete: () => {
                this.processingLoader = false;
            }
        });
    }

    assignConductCards(id: number, params: any) {
        this.processingLoader = true;
        this.conductCardsService.assignConductCards(this.projectId, id, params).subscribe({
            next: (res: any) => {
                const message = `Conduct card assigned to ${this.userDetails.name}.`;
                this.toastService.show(this.toastService.types.SUCCESS, message);
                this.initializeTable(true);
            },
            error: (err: any) => {
                this.processingLoader = false;
                console.log('Failed API response ', err);
            },
            complete: () => {
                this.processingLoader = false;
            }
        });
    }

    cardsToggle(i) {
        const index = this.cardToggle.indexOf(i);
        if (index !== -1) {
            this.cardToggle.splice(index, 1);
        } else {
            this.cardToggle.push(i);
        }
    }

    @ViewChild('irBlockedReasonModal') private irBlockedReasonModalRef: TemplateRef<any>;
    irBlockReason(ir) {
        this.blockReasonHeader = '';
        this.currentIr = ir;
        let comments = (ir.comments || []).filter(comment => (comment.module && comment.origin == 'system' && ['conduct-card-assign', 'competency-expiry'].includes(comment.module)));
        let lastComment = (comments.length) ? comments[comments.length-1] : null;
        this.isCompetencyBlock = (lastComment && lastComment.module == 'competency-expiry') ? true : undefined;
        this.isConductCardBlock = (lastComment && lastComment.module == 'conduct-card-assign') ? true : undefined;
        if (this.isConductCardBlock && this.currentIr.status_code === this.STATUS_CODES.BLACKLISTED) {
            this.blockReasonHeader = `${lastComment.note} ${dayjs(lastComment.timestamp).format(this.dateDisplayFormat)}`;
            this.blockReason = 'Reinstating users access can only be carried out by a company portal admin';
        } else {
            this.blockReasonHeader = 'User Blocked';
            this.blockReason = (this.isCompetencyBlock) ? `Project block has been applied to ${ir.name} due to ${lastComment.note}. Would you like to remove the project block?` :
                (this.isConductCardBlock) ? `Project block has been applied to ${ir.name} due to ${lastComment.note}. To remove block please remove the associated conduct card.` : 'No reason available of blocking';
        }
        this.openModal(this.irBlockedReasonModalRef, 'sm', 'modal_info');
    }


    unblockInduction(cb) {
        let request = {
            'status_code':  this.STATUS_CODES.APPROVED,
            'comments': [
                ...(this.currentIr.comments || []),
                {
                "timestamp":dayjs().valueOf(),
                "user_id":this.authUser$.id,
                "name":this.authUser$.name,
                "origin":"system",
                "note":"Removed competency project block",
                "module": "competency-block-removed"
            }],
        };
        this.processingLoader = true;
        this.userService.updateInductionRequest(this.currentIr.id, request, 'admin').subscribe(data => {
            this.processingLoader = false;
            cb();
            if (!data.success) {
                const message = data.message || 'Failed to unblock induction.';
                return this.toastService.show(this.toastService.types.ERROR, message);
            }
            if(this.currentIr.optima_badge_number && this.optima_meta.has_optima_source){
                const e = {
                    target:{
                        checked: false
                    }
                }
                this.toggleOptimaAccess(e, this.currentIr, cb, 'checkbox', true);
            }
            this.initializeTable(true);
        });
    }

    getNegativeConductCard() {
        let nowEpoch = dayjs().startOf('day').valueOf();
        return (this.currentIr.conduct_cards).find(cc => (
            ((cc.expire_on && cc.expire_on > nowEpoch) || cc.expire_on == null) &&
            cc.card_detail.card_type === "Negative" &&
            ['Project Block', 'Company Block'].includes(cc.card_detail.card_action))
        );
    }

    unAssignConductCard(cb) {
        const id = this.currentIr.conduct_cards;
        let conductCard = this.getNegativeConductCard();

        if (!conductCard || !conductCard.id) {
            const message = 'Something went wrong, No negative conduct card found.';
            this.toastService.show(this.toastService.types.ERROR, message);
            return;
        }

        const req = {
            user_ref : this.currentIr.user_ref,
            assigned_id : conductCard.id,
        }
        this.processingLoader = true
        this.userService.unassignedConductCardPp(this.projectId, conductCard.conduct_card_ref, req).subscribe(data => {
            this.processingLoader = false;
            cb();
            if (!data.success) {
                const message = data.message || 'Failed to unassign conduct card.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
            this.initializeTable(true);
        });
    }

    toggleAccordion() {
        this.isCollapse = !this.isCollapse;
        this.activeAccessAccordion = (this.isCollapse === false) ? [] : ['accordion-1'];
    }
}

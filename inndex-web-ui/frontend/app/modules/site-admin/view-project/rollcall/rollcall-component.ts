import {Component, Input, OnInit, ViewChild} from '@angular/core';
import {ProjectService, RollCallService, Common, Project} from "@app/core";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import * as dayjs from 'dayjs';
import {AppConstant} from "@env/environment";
import { ActionBtnEntry, IModalComponent } from '@app/shared';

@Component({
    selector: 'rollcall-component',
    templateUrl: './rollcall-component.html',
})

export class RollCallComponent implements OnInit {

    @Input()
    projectId;
    
    @Input()
    project: Project;

    displayDateTimeFormat: string = AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS;
    common = new Common();
    rollCallEventTablePage = this.common.page;
    loading: boolean = false;
    downloadLoading: boolean = false;
    isInitRollCall: boolean = false;
    loadingInlineRollCall: boolean = false;
    iframe_content_loading: boolean = false;
    rollCallRecords: any = [];
    baseButtonConfig: ActionBtnEntry[] = [
        {
            key: 'view',
            label: '',
            title: 'View RollCall Report',
            mat_icon: 'search',
        },
        {
            key: 'download',
            label: '',
            title: 'Download RollCall Report PDF',
            mat_icon: 'download',
        }
    ];

    constructor(
        private rollCallService: RollCallService,
        private modalService: NgbModal,) {

    }

    ngOnInit() {
        this.rollCallEventTablePage.limit = '12';
        this.getRollCallRecords();
    }

    getRollCallRecords(isPageChange?: boolean) {
        if (!isPageChange) {
          this.loading = true;
        } else {
          this.loadingInlineRollCall = true;
        }
        let pageNumber = this.rollCallEventTablePage.pageNumber;
        let limit = this.rollCallEventTablePage.limit;
        this.rollCallService.getProjectRollCallRecords(this.projectId, pageNumber, limit).subscribe((res: any) => {
            if(res.success) {
                this.rollCallRecords = res.projectRollCallRecords;
                this.rollCallEventTablePage.total = res.total;
                this.rollCallEventTablePage.pageNumber = res.pageNumber;
            }
            this.loading = false;
            this.loadingInlineRollCall = false;
        });
    }

    getPresentUsers(record) {
        return record.present_users.length + record.present_visitors.length;
    }

    
    getUnAccountedUsers(record) {
        return record.un_accounted_users.length + record.un_accounted_visitors.length;
    }

    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange) {
        this.rollCallEventTablePage.pageNumber = pageInfo.offset;
        if (!this.isInitRollCall) {
          this.isInitRollCall = true;
          return;
        }
        this.getRollCallRecords(isPageChange);
    }

    dayjs(n) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs(n).tz(tz).format(this.displayDateTimeFormat);
    }

    downloadRollCallReport(rollCall) {
        this.downloadLoading = true;
        this.downloadRollCall('pdf', rollCall);
    }

    downloadRollCall(type, rollCall) {
        let req = {
            type: type
        };
        return this.rollCallService.downloadRollCall(req, this.projectId, rollCall.id, () => {
            this.downloadLoading = false;
        });
    }

    @ViewChild('rollCallViewContentRef') private rollCallViewContentRef: IModalComponent;
    rollCallDetailModal(rollCall) {
        this.rollCallViewContentRef.open();
        this.iframe_content_loading = true;
        this.downloadRollCall('html', rollCall).subscribe((html:any) => {
            let iframe = document.getElementById('rollCallReportFrame') as HTMLIFrameElement;
            let doc = iframe.contentDocument;
            doc.write(html);
            this.iframe_content_loading = false;
        });
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actions = {
            'view': () => this.rollCallDetailModal(row),
            'download': () => this.downloadRollCallReport(row)
        };

        const action = actions[entry.key];
        if (action) {
            action();
        }
    }
};

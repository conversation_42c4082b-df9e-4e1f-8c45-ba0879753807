<block-loader [show]="loading" [showBackdrop]="true" alwaysInCenter="true"></block-loader>
<ng-container *ngIf="!loading">
<h5 class="float-md-left">
    Completed Roll Calls: <small> {{ rollCallEventTablePage.total }} </small>
</h5>

<div class="clearfix"></div>
<div class="table-responsive-sm" >
    <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w roll-call-table-body sm-pager-view ngx-datatable-custom-h"
        [rows]="rollCallRecords"
        [scrollbarV]="true"
        [virtualization]="false"
        [loadingIndicator]="loadingInlineRollCall"
        [limit]="50"
        [footerHeight]="40"
        [columnMode]="'force'"
        [rowHeight]="'auto'"
        [externalPaging]="true"
        [count]="rollCallEventTablePage.total"
        [offset]="rollCallEventTablePage.pageNumber"
        [limit]="rollCallEventTablePage.limit"
        (page)='pageCallback($event, true)'
    >
        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="70">
            <ng-template let-column="column" ngx-datatable-header-template>
                Roll Call #
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                {{ row.id }}
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
            <ng-template let-column="column" ngx-datatable-header-template>
                Date & Time
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                {{ dayjs(row.createdAt) }}
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
            <ng-template let-column="column" ngx-datatable-header-template>
                Carried out by
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
               <span appTooltip>{{ row.created_by.name}}</span>
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
            <ng-template let-column="column" ngx-datatable-header-template>
                Present
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                {{ getPresentUsers(row) }}
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
            <ng-template let-column="column" ngx-datatable-header-template>
                Not accounted for
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                {{ getUnAccountedUsers(row) }}
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column headerClass="font-weight-bold" cellClass="no-ellipsis" [width]="70" [sortable]="false" minWidth="100">
            <ng-template let-column="column" ngx-datatable-header-template>
                Actions
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                <button-group
                    [buttons]="baseButtonConfig"
                    (onActionClick)="rowBtnClicked($event, row)"
                ></button-group>
            </ng-template>
        </ngx-datatable-column>
    </ngx-datatable>
    <block-loader [show]="downloadLoading" alwaysInCenter="true"></block-loader>
</div>
</ng-container>

<i-modal #rollCallViewContentRef title="Roll Call Report" size="xl" [showCancel]="false" [windowClass]="'xl-modal'" [showFooter]="false">
    <div class="scroll-wrapper">
        <iframe #rollCallReportFrame class="border-0" id='rollCallReportFrame' style="width: 98%;height: 100%;position: absolute;"></iframe>
    </div>
    <block-loader alwaysInCenter="true" [show]="iframe_content_loading"></block-loader>
</i-modal>

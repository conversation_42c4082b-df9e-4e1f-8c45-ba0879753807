<block-loader [show]="recordsLoading || downloadLoading" [showBackdrop]="!downloadLoading" alwaysInCenter="true"></block-loader>
<ng-container *ngIf="!recordsLoading">
<div class="d-flex flex-wrap flex-column flex-sm-row justify-content-between mb-2">
<h5 class="float-md-left">Fatigue Records Total
    <small>
        ({{ fatigueEventTablePage.total }})
    </small>
</h5>
    <button class="btn btn-sm btn-brandeis-blue d-flex other-action-btn justify-content-center align-items-center pointer-cursor" (click)="openTimeManagementFatigueReportModal()" id="dropdownDlReport1">
        <span class="medium-font m-font-size material-symbols-outlined mr-2">download</span>
        <div class="medium-font m-font-size">Download Report</div>
    </button>
</div>
<div class="clearfix"></div>
<div>
    <search-with-filters (searchEmitter)="searchFunction($event, true)"></search-with-filters>
</div>
<div class="table-responsive-sm">
    <!-- Toolbox Talk table for Site Admin -->
    <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
        [scrollbarV]="true"
        [virtualization]="false"
        [loadingIndicator]="loadingInlineFatigue"
        [rows]="fatigueBreachesList"
        [limit]="50"
        [footerHeight]="40"
        [columnMode]="'force'"
        [rowHeight]="'auto'"
        [externalPaging]="true"
        [count]="(searchText) ? fatigueBreachesList.length : fatigueEventTablePage.total"
        [offset]="fatigueEventTablePage.offset"
        [limit]="fatigueEventTablePage.limit"
        (page)='getFatigueRecordsPageCallback($event, true)'
    >
        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
            <ng-template let-column="column" ngx-datatable-header-template>
                Date of breach
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                {{unix(row.event_timestamp, displayDateFormat)}}
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
            <ng-template let-column="column" ngx-datatable-header-template>
                Name
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                <span appTooltip>{{ row.user_ref? row.user_ref.name: '' }}</span>
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
            <ng-template let-column="column" ngx-datatable-header-template>
                Employer
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                <span appTooltip>{{ row.user_company_ref? row.user_company_ref.name: '' }}</span>
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
            <ng-template let-column="column" ngx-datatable-header-template>
                Type of breach
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                <span appTooltip *ngIf="row.category === 'Fatigue_PeriodOfDuty'"> Consecutive days in a 14 day period </span>
                <span appTooltip *ngIf="row.category === 'Fatigue_ShiftGap'"> Minimum rest period </span>
                <span appTooltip *ngIf="row.category === 'Fatigue_WeeklyHours'"> Site Time in a 7 day period </span>
                <span appTooltip *ngIf="row.category === 'Fatigue_DailyTotalHours'"> Daily Site & Travel Time </span>
                <span appTooltip *ngIf="row.category === 'Fatigue_DailySiteHours'">  Daily Site Time </span>
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
            <ng-template let-column="column" ngx-datatable-header-template>
                Details
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                <span *ngIf="row.category === 'Fatigue_PeriodOfDuty'"> Day {{row.details.spentDays}} ({{unix(row.event_timestamp, displayDateFormat)}}) </span>
                <span *ngIf="row.category === 'Fatigue_ShiftGap'"> {{timeConvert(row.details.shiftGap)}} </span>
                <span *ngIf="row.category === 'Fatigue_WeeklyHours'">
                    {{timeConvert(row.details.totalHours)}}
                 </span>
                <span *ngIf="row.category === 'Fatigue_DailyTotalHours'"> {{timeConvert(row.details.totalHours)}} </span>
                <span *ngIf="row.category === 'Fatigue_DailySiteHours'">  {{timeConvert(row.details.siteHours)}} </span>
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column headerClass="font-weight-bold action-column text-center"
                                              cellClass="action-column" [sortable]="false" minWidth="100">
            <ng-template let-column="column" ngx-datatable-header-template>
                Status
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                <div class="text-center pr-3">
                    <span *ngIf="row.status_message != 'Open'" class="btn-sm badge-pill badge-success">
                        {{ row.status_message }}
                    </span>
                    <button  *ngIf="(row.status_message === 'Open')"  class="btn-sm badge-pill badge-danger cursor-pointer close-btn" (click)="openCloseOutModal(closeOutForm, row)">
                        {{ row.status_message }}
                    </button>
                    
                </div>
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column headerClass="font-weight-bold" cellClass="no-ellipsis" [width]="70" [sortable]="false" minWidth="70">
            <ng-template let-column="column" ngx-datatable-header-template>
                Actions
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                <button-group
                    [buttons]="baseButtonConfig"
                    [btnConditions]="[row.status_message !== 'Open', row.status_message === 'Open']"
                    (onActionClick)="rowBtnClicked($event, row)">
                </button-group>
            </ng-template>
        </ngx-datatable-column>
    </ngx-datatable>
    <block-loader [show]="recordsLoading" [showBackdrop]="true" alwaysInCenter="true"></block-loader>

    <i-modal #detailsModalRef [title]="'Fatigue Breach - ' + (fatigueRecord.user_ref? fatigueRecord.user_ref.name: '')" size="lg" cancelBtnText="Close">
        <div *ngIf="showModal">
            <table class="table table-sm table-bordered" style="font-size: 14px;">
                <tbody>
                <tr>
                    <td class="tr-bg-dark-color" style="width: 25%;">
                        <strong>Date of breach:</strong>
                    </td>
                    <td>
                        {{unix(fatigueRecord.event_timestamp, displayDateFormat)}}
                    </td>
                </tr>
                <tr>
                    <td class="tr-bg-dark-color" style="width: 25%;">
                        <strong>Type of breach:</strong>
                    </td>
                    <td>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_PeriodOfDuty'"> Working days in 14 day period </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_ShiftGap'"> Minimum rest period </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_WeeklyHours'"> Site hours in 7 day period </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_DailyTotalHours'"> Site hours & travel time </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_DailySiteHours'">  Site hours </span>
                    </td>
                </tr>
                <tr>
                    <td class="tr-bg-dark-color" style="width: 25%;">
                        <strong>Details:</strong>
                    </td>
                    <td>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_PeriodOfDuty'"> Working day number {{fatigueRecord.details.spentDays}} = {{unix(fatigueRecord.event_timestamp, displayDateFormat)}} </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_ShiftGap'"> {{timeConvert(fatigueRecord.details.shiftGap)}} </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_WeeklyHours'">
                            {{timeConvert(fatigueRecord.details.totalHours)}}
                            <div style="font-size: 11px;">
                                ({{dayjs(fatigueRecord.details.fromDate, 'YYYY-MM-DD')}} - {{dayjs(fatigueRecord.details.to_date, 'YYYY-MM-DD')}})
                            </div>
                            </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_DailyTotalHours'"> {{timeConvert(fatigueRecord.details.totalHours)}} </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_DailySiteHours'">  {{timeConvert(fatigueRecord.details.siteHours)}} </span>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <h4 class="m-0">Closeout details</h4>
                    </td>
                </tr>
                <tr>
                    <td class="tr-bg-dark-color" style="width: 25%;">
                        <strong>Closeout Date:</strong>
                    </td>
                    <td>
                        {{unix(fatigueRecord.closed_out_date, displayDateFormat)}}
                    </td>
                </tr>
                <tr>
                    <td class="tr-bg-dark-color" style="width: 25%;">
                        <strong>Closeout By:</strong>
                    </td>
                    <td>
                        {{fatigueRecord.closed_out_by}}
                    </td>
                </tr>
                <tr>
                    <td class="tr-bg-dark-color" style="width: 25%;">
                        <strong>Closeout Details:</strong>
                    </td>
                    <td>
                        {{fatigueRecord.closed_out_details}}
                    </td>
                </tr>
                <tr *ngIf="fatigueRecord.closeout_file_id">
                    
                    <td class="tr-bg-dark-color" style="width: 25%;">
                        <strong>Attachment:</strong>
                    </td>
                    <td>
                        <a [title]="fatigueRecord?.closeout_file_id?.name" [href]="fatigueRecord?.closeout_file_id?.file_url" target="_blank"
                        >
                            {{ fatigueRecord?.closeout_file_id?.name }}
                        </a>
                    </td>
                </tr>
            </table>
        </div>
    </i-modal>

    <i-modal #closeOutModalRef [title]="'Fatigue Breach Closeout - ' + (fatigueRecord.user_ref? fatigueRecord.user_ref.name: '')" rightPrimaryBtnTxt="Close Out" 
        (onCancel)="onCancelCloseOut($event, closeOutForm)" (onClickRightPB)="closeOutViolation(closeOutForm, $event)" [rightPrimaryBtnDisabled]="!closeOutForm.valid" cancelBtnText="Close">
            <table class="table table-sm table-bordered" style="font-size: 14px;">
                <tbody>
                <tr>
                    <td class="tr-bg-dark-color" style="width: 25%;">
                        <strong>Date of breach:</strong>
                    </td>
                    <td>
                        {{unix(fatigueRecord.event_timestamp, displayDateFormat)}}
                    </td>
                </tr>
                <tr>
                    <td class="tr-bg-dark-color" style="width: 25%;">
                        <strong>Type of breach:</strong>
                    </td>
                    <td>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_PeriodOfDuty'"> Working days in 14 day period </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_ShiftGap'"> Minimum rest period </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_WeeklyHours'"> Site hours in 7 day period </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_DailyTotalHours'"> Site hours & travel time </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_DailySiteHours'">  Site hours </span>
                    </td>
                </tr>
                <tr>
                    <td class="tr-bg-dark-color" style="width: 25%;">
                        <strong>Details:</strong>
                    </td>
                    <td>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_PeriodOfDuty'"> Working day number {{fatigueRecord.details.spentDays}} = {{unix(fatigueRecord.event_timestamp, displayDateFormat)}} </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_ShiftGap'"> {{timeConvert(fatigueRecord.details.shiftGap)}} </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_WeeklyHours'">
                            {{timeConvert(fatigueRecord.details.totalHours)}}
                            <div style="font-size: 11px;">
                                ({{dayjs(fatigueRecord.details.fromDate, 'YYYY-MM-DD')}} - {{dayjs(fatigueRecord.details.to_date, 'YYYY-MM-DD')}})
                            </div>
                            </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_DailyTotalHours'"> {{timeConvert(fatigueRecord.details.totalHours)}} </span>
                        <span *ngIf="fatigueRecord.category === 'Fatigue_DailySiteHours'">  {{timeConvert(fatigueRecord.details.siteHours)}} </span>
                    </td>
                </tr>
                </tbody>
            </table>
            <form novalidate #closeOutForm="ngForm">
                <div *ngIf="showModal">
                <div class="mb-2">
                    <p class="mb-1 font-weight-bold">Closeout Details</p>
                    <textarea class="form-control" name="note"
                        [(ngModel)]="comment" rows="4" placeholder="Add comment" required></textarea>
                </div>
                <div class="mb-2 mt-2 col-md-12 p-0">
                    <p class="mb-1 font-weight-bold">Add Attachments</p>
                    <input type="hidden" name="corrective_images" id="corrective_images"
                           [(ngModel)]="corrective_images"/>
                </div>
                <div class="col-md-12 p-0 flex-grow-1 mb-4">
                    <file-uploader-v2
                        [disabled]="false"
                        [multipleUpload]="false"
                        [init]="closeoutAttachmentFile"
                        [category]="'fatigue-violation-closeout'"
                        [dragnDropTxt]="'Drag and drop attachment here'"
                        (uploadDone)="uploadDone($event)"
                        [allowedMimeType]="allowedMime"
                        (deleteFileDone)="fileDeleteDone($event)"
                        [showDeleteBtn]="true"
                        [showFileName]="false"
                        [hasImgAndDoc]="true"
                    ></file-uploader-v2>
                </div>
                </div>
            </form>
            
            <block-loader [show]="commentsLoading"></block-loader>
    </i-modal>

</div>
</ng-container>

<report-downloader #reportDownloader 
    [xlsxOnly]="true" 
    (onFilterSelection)="timeManagementFatigueReportDownload($event)"
    >
</report-downloader>
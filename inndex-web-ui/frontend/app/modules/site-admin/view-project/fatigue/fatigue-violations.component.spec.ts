import { TitleCasePipe, UpperCasePipe } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { By } from "@angular/platform-browser";
import { FormsModule } from '@angular/forms';
import {
  AuthService,
  DoNotAskAgainService,
  Project,
  ProjectService,
  ToastService,
} from '@app/core';
import { NgbMomentjsAdapter } from '@app/core/ngb-moment-adapter';
import {
  ReportDownloaderComponent,
  SearchWithFiltersComponent,
} from '@app/modules/common';
import {
  FileUploaderV2Component,
  GenericConfirmationModalComponent,
  IModalComponent,
} from '@app/shared';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { of } from 'rxjs';
import { FatigueViolationsComponent } from './fatigue-violations.component';

dayjs.extend(utc);
dayjs.extend(timezone);

describe('FatigueViolationsComponent', () => {
  let component: FatigueViolationsComponent;
  let fixture: ComponentFixture<FatigueViolationsComponent>;
  let projectService: jasmine.SpyObj<ProjectService>;
  let toastService: jasmine.SpyObj<ToastService>;

  const mockProject: Project = {
    id: 1,
    name: 'Test Project',
    custom_field: {
      timezone: 'UTC',
    },
  };

  const mockFatigueRecords = {
    fatigueRecords: [
      {
        id: 1,
        user_ref: { name: 'Test UserA' },
      },
    ],
    total: 1,
    pageNumber: 1,
    limit: 10,
  };

  beforeEach(async () => {
    const projectServiceSpy = jasmine.createSpyObj('ProjectService', [
      'getProjectFatigueRecords',
      'downloadProjectFatigueRecords',
      'updateFatigueRecord',
    ]);

    const authServiceSpy = {
      authUser: of({ id: 1, name: 'Test User', user_onboard_status: {} }),
    };

    const toastServiceSpy = jasmine.createSpyObj('ToastService', [
      'show',
      'types',
    ]);

    const ngbMomentjsAdapterSpy = jasmine.createSpyObj('NgbMomentjsAdapter', [
      'dayJsToNgbDate',
      'ngbDateToDayJs',
    ]);

    await TestBed.configureTestingModule({
      declarations: [
        FatigueViolationsComponent,
        ReportDownloaderComponent,
        SearchWithFiltersComponent,
        FileUploaderV2Component,
        IModalComponent,
        GenericConfirmationModalComponent,
      ],
      imports: [
        FormsModule,
        HttpClientTestingModule,
        NgxDatatableModule,
        NgbModule,
      ],
      providers: [
        { provide: ProjectService, useValue: projectServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: ToastService, useValue: toastServiceSpy },
        { provide: NgbMomentjsAdapter, useValue: ngbMomentjsAdapterSpy },
        TitleCasePipe,
        UpperCasePipe,
        DoNotAskAgainService,
      ],
    }).compileComponents();

    projectService = TestBed.inject(
      ProjectService
    ) as jasmine.SpyObj<ProjectService>;
    toastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FatigueViolationsComponent);
    component = fixture.componentInstance;
    component.projectId = 1;
    component.project = mockProject;
    projectService.getProjectFatigueRecords.and.returnValue(
      of(mockFatigueRecords)
    );
    fixture.detectChanges();
  });

  it('should create component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.searchText).toBeNull();
    expect(component.comment).toBeNull();
    expect(component.showModal).toBeFalsy();
    expect(component.loadingInlineFatigue).toBeFalsy();
  });

  it('should load fatigue records on init', fakeAsync(() => {
    projectService.getProjectFatigueRecords.and.returnValue(
      of(mockFatigueRecords)
    );

    component.ngOnInit();
    tick();

    expect(projectService.getProjectFatigueRecords).toHaveBeenCalledWith(
      1,
      '1',
      '10'
    );
    expect(component.fatigueBreachesList).toEqual(
      mockFatigueRecords.fatigueRecords
    );
    expect(component.fatigueList).toEqual(mockFatigueRecords.fatigueRecords);
    expect(component.fatigueEventTablePage.total).toBe(
      mockFatigueRecords.total
    );
  }));

  it('should display "Fatigue Records Total" title', () => {
    fixture.detectChanges();
    const titleElement = fixture.debugElement.query(By.css('h5'));
    expect(titleElement.nativeElement.textContent).toContain('Fatigue Records Total');
  });

  it('should display the total count in parentheses', () => {
    component.fatigueEventTablePage.total = 1;
    const smallElement = fixture.debugElement.query(By.css('small'));
    expect(smallElement.nativeElement.textContent.trim()).toBe(`(${1})`);
  });

  it('should handle search functionality', () => {
    component.fatigueList = [
      { user_ref: { name: 'Test UserA' } },
      { user_ref: { name: 'Test UserB' } },
    ];

    component.searchFn('UserA');
    expect(component.fatigueBreachesList.length).toBe(1);
    expect(component.fatigueBreachesList[0].user_ref.name).toBe('Test UserA');
  });

  it('should convert time correctly', () => {
    const result = component.timeConvert(2.5);
    expect(result).toBe('2 hour(s) 30 minute(s)');
  });

  it('should show success toast when closing out fatigue violation', fakeAsync(() => {
    const mockResponse = { success: true };
    projectService.updateFatigueRecord.and.returnValue(of(mockResponse));

    component.fatigueRecord = {
      id: 1,
      user_ref: { name: 'Test User' },
      category: 'Fatigue_DailyTotalHours',
      closed_out_by: null,
      closed_out_date: null,
      closed_out_details: null,
      event_timestamp: 1234567890,
      closeout_file_id: null,
      details: {
        totalHours: 10,
        siteHours: 8,
        shiftGap: 2,
        spentDays: 1,
      },
    };
    component.comment = 'Test comment';
    component.authUser$ = { id: 1, name: 'Test User' };

    component.closeOutViolation({ reset: () => {} }, { closeFn: () => {} });
    tick();

    expect(toastService.show).toHaveBeenCalledWith(
      toastService.types.SUCCESS,
      'Fatigue breach closed out successfully.'
    );
  }));
});

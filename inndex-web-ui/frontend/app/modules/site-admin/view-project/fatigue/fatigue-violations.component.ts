import {Component, Input, OnInit, ViewChild} from '@angular/core';
import {InductionRequest, ProjectService, User, AuthService, Project, FatigueRecords, ToastService} from "@app/core";
import * as dayjs from 'dayjs';
import {NgbDateStruct} from '@ng-bootstrap/ng-bootstrap';
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import {AppConstant} from "@env/environment";
import { ReportDownloaderComponent } from '@app/modules/common';
import { ActionBtnEntry, IModalComponent } from '@app/shared';
import { NgForm } from '@angular/forms';

@Component({
    selector: 'fatigue-violations',
    templateUrl: './fatigue-violations.component.html',
    styleUrls: ['./fatigue-violations.component.scss']
})
export class FatigueViolationsComponent implements OnInit {
    @ViewChild('reportDownloader') private reportDownloader: ReportDownloaderComponent;
    @Input()
    projectId: number;

    @Input()
    project: Project;

    allowedMime : Array<any> = ['application/pdf', 'application/x-pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/jpg', 'image/png'];
    displayDateFormat: string = AppConstant.defaultDateFormat;
    recordsLoading: boolean = false;
    downloadLoading: boolean = false;
    fatigueBreachesList: any = [];
    fatigueList: any = [];
    searchText: string =  null;
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;
    fatigueEventTablePage: any = {};
    authUser$: User;
    commentsLoading: boolean = false;
    fatigueRecord: FatigueRecords;
    comment = null;
    closeoutAttachmentFile = null;
    showModal: boolean = false;
    isInitFatigue: boolean = false;
    loadingInlineFatigue: boolean = false;
    baseButtonConfig: ActionBtnEntry[] = [
        {
            key: 'view',
            label: '',
            title: 'View',
            mat_icon: 'search',
        },
        {
            key: 'close',
            label: '',
            title: 'Close',
            mat_icon: 'close',
        }
    ];

    constructor(
        private projectService: ProjectService,
        public ngbMomentjsAdapter: NgbMomentjsAdapter,
        private authService: AuthService,
        private toastService: ToastService,

    ) {
        this.from_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.to_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
    }

    ngOnInit() {
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });

        this.getFatigueRecords({});
        this.fatigueRecord = new FatigueRecords();
    }

    getFatigueRecords($event, isPageChange?: boolean) {
        if (!isPageChange) {
          this.recordsLoading = true;
        } else {
          this.loadingInlineFatigue = true;
        }
        let pageNumber = (($event.offset || 0) + 1).toString();
        let limit = ($event.limit || 10).toString();
        this.projectService.getProjectFatigueRecords(this.projectId, pageNumber, limit).subscribe((response:any) => {
            this.recordsLoading = false;
            this.loadingInlineFatigue = false;
            if(response.fatigueRecords) {
                this.fatigueBreachesList = response.fatigueRecords;
                this.fatigueList = response.fatigueRecords;
                this.fatigueEventTablePage.total = response.total;
                this.fatigueEventTablePage.offset = (response.pageNumber - 1);
                this.fatigueEventTablePage.limit = response.limit;
            } else {
                const message = 'Failed to list data.';
                return this.toastService.show(this.toastService.types.ERROR, message);
            }

        });
    }

    public getFatigueRecordsPageCallback($event, isPageChange: boolean): void {
        if (!this.isInitFatigue) {
            this.isInitFatigue = true;
            return;
        }
        this.getFatigueRecords($event, isPageChange);
    }

    async initiateDownload(resp) {
        this.downloadLoading = true;
        this.from_date = resp.fromDate;
        this.to_date = resp.toDate;
        this.downloadFatigueReport();
    }

    downloadFatigueReport() {
        const fromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.from_date).startOf('day').valueOf();
        const toDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.to_date).endOf('day').valueOf();
        let data = {
            fromDate: fromDate,
            toDate: toDate,
        }
        this.projectService.downloadProjectFatigueRecords(this.projectId, data, () => {
            this.downloadLoading = false;
        });
    }

    timeConvert(numOfHours) {
        let mins = numOfHours * 60;
        let hours = (mins / 60);
        let rhours = Math.floor(hours);
        let minutes = (hours - rhours) * 60;
        let rminutes = Math.round(minutes);
        return rhours + " hour(s) " + rminutes + " minute(s)";
    }

    unix(n?: number, format?: any) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs.unix(n).tz(tz).format(format);
    };

    dayjs(n, format) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs(n, format).tz(tz).format(this.displayDateFormat);
    }

    searchTextChanged($event) {
        const val = $event.target.value.toLowerCase();
        this.searchFn(val)
    }

    customSearchFn(term: string, item: InductionRequest){
        term = term.toLocaleLowerCase();
        return (item.additional_data && item.additional_data.user_info && item.additional_data.user_info.name && item.additional_data.user_info.name.toLocaleLowerCase().indexOf(term) > -1) ||
            (item.additional_data && item.additional_data.employment_detail && item.additional_data.employment_detail.job_role && item.additional_data.employment_detail.job_role.toLocaleLowerCase().indexOf(term) > -1);
    }


    @ViewChild('detailsModalRef')
    private detailsModalRef: IModalComponent;
    openDetailsModal(row) {
        this.fatigueRecord = row;
        this.showModal = true;
        this.detailsModalRef.open();
    }

    onCancelCloseOut(event, form) {
        form.reset();
        this.showModal = false;
        this.closeoutAttachmentFile = null;
    }

    closeOutViolation(form, event) {
        this.commentsLoading = true;
        let record = {
            id: this.fatigueRecord.id,
            closed_out_by: this.authUser$.name,
            closed_out_date: dayjs().unix(),
            closed_out_details: this.comment,
            status: 2,
            closeout_file_id: this.closeoutAttachmentFile? this.closeoutAttachmentFile.id : null
        };
        this.projectService.updateFatigueRecord(this.projectId, this.fatigueRecord.id, record).subscribe((response:any) => {
            this.commentsLoading = false;
            if(response && response.success) {
                this.comment = null;
                this.closeoutAttachmentFile = null;
                this.getFatigueRecords({});
                const message = 'Fatigue breach closed out successfully.';
                this.toastService.show(this.toastService.types.SUCCESS, message);
                form.reset();
                event.closeFn();
                this.showModal = false;
            } else {
                const message = response.message || 'Failed to add comment.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: response });
                this.showModal = false;
            }
        });
    }

    modalRef;
    @ViewChild('closeOutModalRef')
    private closeOutModalRef: IModalComponent;
    async openCloseOutModal(form, row) {
        form.reset();
        this.showModal = true;
        this.fatigueRecord = row;
        this.closeOutModalRef.open();
    }

    uploadDone($event) {
        if($event && $event.userFile) {
            this.closeoutAttachmentFile = $event.userFile;
        }
    }

    fileDeleteDone($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.closeoutAttachmentFile = null;
        }
    }
    searchFunction(data, isPageChange: boolean){
        this.searchText = data?.search?.trim() || '';
        if (!this.searchText) {
          this.getFatigueRecords({}, isPageChange);
        }
        this.fatigueEventTablePage.offset = 0;
        this.searchFn(data.search, isPageChange);
    }
    searchFn(val, isPageChange?: boolean){
        try {
            val = val.toLowerCase()
            if (isPageChange) {
              this.loadingInlineFatigue = true;
            }
            const tempData = this.fatigueList.filter(function (d) {
                return (
                    (d.user_ref && d.user_ref.name && d.user_ref.name.toLowerCase().indexOf(val) !== -1) ||
                    (d.user_company_ref && d.user_company_ref.name && d.user_company_ref.name.toLowerCase().indexOf(val) !== -1) ||
                    !val
                );
            });
            this.fatigueBreachesList = tempData;
        } catch (error) {
            console.log('Something went wrong while searching fatigue:', error);
        } finally {
            this.loadingInlineFatigue = false;
        }
    }
    
    async timeManagementFatigueReportDownload(event) {
        await this.initiateDownload(event.selection);
        event.closeFn();
    }

    public openTimeManagementFatigueReportModal() {
        this.reportDownloader.openModal();
    }

    @ViewChild('closeOutForm') closeOutForm: NgForm;
    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actions = {
            'view': () => this.openDetailsModal(row),
            'close': () => this.openCloseOutModal(this.closeOutForm, row),
        };

        const action = actions[entry.key];
        if (action) {
            action();
        }
    }
}

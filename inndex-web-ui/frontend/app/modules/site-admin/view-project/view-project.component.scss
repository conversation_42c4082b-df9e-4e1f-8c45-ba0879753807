@use "assets/scss/variables";

.btn:hover svg.facial-face {
    fill: var(--white);
}
.filter-select{
    max-width: 204px;
}
.mb-n1 {
    margin-left: -12px;
}
.fp-check-mark{
    bottom: -20px;
    margin-left: 20px;
}
.hide-bullet {
    list-style: none;
    margin-left: -40px;
}
.fp-processing-wrapper {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
    align-content: center;
}
@keyframes flickerAnimation {
    0%   { opacity:0.25; }
    25%   { opacity:0.5; }
    50%  { opacity:0.8; }
    100% { opacity:0.25; }
}
@-o-keyframes flickerAnimation{
    0%   { opacity:0.25; }
    25%   { opacity:0.5; }
    50%  { opacity:0.8; }
    100% { opacity:0.25; }
}
@-moz-keyframes flickerAnimation{
    0%   { opacity:0.25; }
    25%   { opacity:0.5; }
    50%  { opacity:0.8; }
    100% { opacity:0.25; }
}
@-webkit-keyframes flickerAnimation{
    0%   { opacity:0.25; }
    25%   { opacity:0.5; }
    50%  { opacity:0.8; }
    100% { opacity:0.25; }
}
.animate-flicker:not(.text-success) {
    -webkit-animation: flickerAnimation 1.5s infinite;
    -moz-animation: flickerAnimation 1.5s infinite;
    -o-animation: flickerAnimation 1.5s infinite;
    animation: flickerAnimation 1.5s infinite;
}

table.custom-access-level .custom-control-label::before {height: 1.7rem;}
.access-accordion > .accordion ::ng-deep >div.card {overflow: visible !important;}
table.custom-access-level.table-bordered {
    border-collapse: unset;
    border-spacing: 0;
    border-radius: 6px;
}
table.custom-access-level.table-bordered thead th {
    border-bottom-width: 1px;
    background-color: rgb(228, 228, 228);
}
table.custom-access-level.table-bordered thead th:first-child {
    border-top-left-radius: 4px;
}
table.custom-access-level.table-bordered thead th:last-child {
    border-top-right-radius: 4px;
}
table.custom-access-level.table-bordered tr:last-child td:first-child {
    border-bottom-left-radius: 4px;
}
table.custom-access-level.table-bordered tr:last-child td:last-child {
    border-bottom-right-radius: 4px;
}
.access-btn-bg-color {background-color: var(--bright-gray) !important;}

//.font-inherit { font-size: inherit; }
//.background-orange { background-color: var(--orange) !important; }
//.btn-warning { color: var(--white) !important; background-color: var(--orange) !important; border-color: var(--orange) !important; }
.bg-white { background-color: var(--white) !important; }
.btn-active:active { color: var(--black) !important; }
.bg-light-grey { background-color: var(--rose-ebony); }
.box-shadow-none:focus { outline: none !important; box-shadow: none !important; }

//New Model CSS
.modal-header-background { background-color: var(--cultured); }
.line { border-bottom: 1px solid var(--bright-gray); }
.badge-light-grey {
    color: var(--black);
    background-color: var(--cultured);
    border-color: var(--cultured);
    width: 70px;
    font-size: 12px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.contant-font-size { font-size: 14px; }
.contant-line-hight { line-height: 18px; }
.width-18 { width: 18px; }
.font-20 { font-size: 20px; }

.modal-header-background {
    background-color: var(--cultured);
    border-bottom: none;
}

.change-log-cancel-btn {
    border: none;
    background-color: transparent;
}
/* sub table css */
//:host ::ng-deep .detail-fixed-height .ngx-datatable .datatable-body{
//    overflow-x: hidden !important;
//}
//.table-sub-heading-bg-grey { background-color: var(--lotion); }
//.search-input {
//    height: 35px; margin: 5px 0;
//    min-width: 210px;
//    background-color: var(--bright-gray);
//    font-size: 12px;
//    border: none;
//    box-shadow: none;
//    height: 30px;
//}
// .block-user-access-btn:hover, .block-user-access-btn:focus, .btn-action:hover, .btn-action:focus {
//     box-shadow: 0.4em 0.4em 0.4em -0.4em #6d6d6d;
//     // transform: translateY(-0.10em);
// }
// .dropdown-toggle::after { display:none; } arrow - hide
//.arrow-small { height: 8px; }
//.arrow-drop-down { height: 8px; }
//.btn-status { min-width: 165px; }
//.row-details-border { border: 1px solid var(--light-gray); }

.btn.disabled, .btn:disabled {
    opacity: 0.5;
}

//.btn-action { height: 31px; }
/*.caution-card { z-index: 2; top: 0; position: inherit; }
.caution-card:focus { box-shadow: none; }
.colors-list {
    width: 40px;
    z-index: 1;
    border-radius: 5px;
    height: 100%;
    position: absolute;
    line-height: inherit;
}*/
.count-card {
    z-index: 2;
    top: 0;
    height: 100%;
    position: inherit;
}

.modal-title {
    span { font-size: 16px; font-weight: 500; }
}
.color-circle {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 10px;
}
.flex-center {
    display: flex;
    align-items: center;
}

/* up-down arrow css */
.arrow-triangle {
    height: 10px;
    width: 10px;
}

.main-drop {
    height: 40px;
}

/* accordion css */
.accordion {
    width: 100%;
}

.accordion button {
    box-shadow: none;
    display: flex;
    align-items: center;
    width: 100%;
    text-align: left;
    border: 0;
    border-radius: 0;
    overflow-anchor: none;
}

.flex-center {
    display: flex;
    align-items: center;
}

/* Color dot css */
.dot {
    height: 8px;
    width: 8px;
}

/* Readonly input css */
.form-control:disabled,
.form-control[readonly] {
    background-color: var(--anti-flash-white);
    color: var(--spanish-gray);
    opacity: 1;
}

.btn.disabled, .btn:disabled {
    opacity: 0.5;
}

.inline-selection{
    .custom-radio{
        font-size: 14px;
        padding: 8px 8px 8px calc(1.5rem + 10px);
        .custom-control-label{
            cursor: pointer;
        }
        .custom-control-label::before,
        .custom-control-label::after{
            top: 0.20rem;
        }
    }
    .custom-radio + .custom-radio{
        margin-top: 12px;
    }
}
.modal-header-background {
    background-color: var(--cultured);
    border-bottom: none;
}

.induct-booking-sec {
    border: 1px solid var(--light-gray);
    border-radius: 5px;
}

.change-log-cancel-btn {
    border: none;
    background-color: transparent;
}

.modal-header-height {
    height: 52px;
}

.enrolment-cards {
    font-size: 0.875em;

    ul {
        padding-inline-start: 12px;
    }

    .card:hover {
        background-color: variables.$primary-bg-color;
    }
}

// Loading spinner fixed position above all other elements like modals.
.loading-spinner-centered {
    position: fixed;
    z-index: 1055 !important;
}

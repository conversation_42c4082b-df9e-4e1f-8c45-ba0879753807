<ng-container *ngIf="isCompanyHeader; else projectHeader">
    <div class="col-md-12 mt-2 px-0" *ngIf="!parentCompany">
        <div class="col-sm-3 text-right px-0 float-right">
            <ngx-skeleton-loader appearance="circle" [theme]="{width: '200px',height: '200px','border-radius': '4px'}"></ngx-skeleton-loader>
        </div>
    </div>
    <div class="col-md-12 text-md-left pl-0 pr-0" *ngIf="parentCompany" [style.text-align]="isMobile ? 'center': null">
        <div class="col-md-3 pl-0 pr-0 float-md-right mb-3">
            <div class="logo float-right" style="max-width: 125px;">
                <img [src]="company_logo" width="100%;"/>
            </div>
        </div>
        <h4 *ngIf="showEmployerName" class="col-md-7 ml-3 mt-3 text-left pl-0 pr-0 float-left font-weight-bold">
            {{ parentCompany.name }}<br>
        </h4>
    </div>
</ng-container>
<ng-template #projectHeader>
    <div class="col-md-12 mt-2 px-0" *ngIf="!projectData">
        <div class="col-md-7 px-0 float-left">
            <ngx-skeleton-loader [theme]="{ 'border-radius': '0', height: '50px', width: '300px' }"></ngx-skeleton-loader>
            <div>
                <ngx-skeleton-loader [theme]="{ 'border-radius': '0', height: '20px', width: '200px' }"></ngx-skeleton-loader>
            </div>
        </div>
        <div class="col-sm-3 text-right px-0 float-right">
            <ngx-skeleton-loader appearance="circle" [theme]="{width: '200px',height: '200px','border-radius': '4px'}"></ngx-skeleton-loader>
        </div>
    </div>
    <div class="col-md-12 text-md-left pl-0 pr-0" *ngIf="projectData" [style.text-align]="isMobile ? 'center': null">
        <div class="col-md-3 pl-0 pr-0 float-md-right mb-3">
            <div class="logo float-right" style="max-width: 125px;">
                <img [src]="company_logo" width="100%;" />
            </div>
        </div>
        <h4 class="col-md-7 pl-0 pr-0 float-md-left" style="font-weight: bold;">
                {{projectData.project_number != null ? projectData.project_number + ' - ' + projectData.name : projectData.name}}<br>
            <span class="mb-2 text-muted small mr-2" style="height: 2em; font-size: 75%;"> Project ID: {{ projectData.record_id || projectData.id }}</span>
        </h4>
    </div>
</ng-template>

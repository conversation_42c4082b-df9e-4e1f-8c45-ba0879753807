import {Component, OnInit, Input, ViewChild} from '@angular/core';
import * as dayjs from 'dayjs';
import { Project, OptimaService, TimeUtility, AuthService, User, BioMetricSettingResponse, Location, ToastService} from "@app/core";
import {DatatableComponent} from "@swimlane/ngx-datatable";
import {TimeAdjustmentProviderComponent, TimeEntryProviderComponent} from "@app/modules/common";
import {AppConstant} from "@env/environment";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import { ActivatedRoute } from '@angular/router';
import { Router } from '@angular/router';

@Component({
    selector:'members-component',
    templateUrl: './members.component.html',
})
export class MembersComponent implements OnInit {
    navActivetab: string = "member-tab";
    searchMemberValue: string = '';
    private expandedRow: any = null;
    avatar_dummy_pic = "/assets/images/avatar-dummy-img.jpeg"
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    constructor(
                private router: Router,
                private activatedRoute: ActivatedRoute,        
                private authService: AuthService,
                private optimaService: OptimaService,
                public timeUtility: TimeUtility,
                public ngbMomentjsAdapter: NgbMomentjsAdapter,
                private toastService: ToastService,
    ) {
        /*this.projectResolverResponse = this.activatedRoute.snapshot.data.projectResolverResponse;
        this.projectInfo = this.projectResolverResponse.project;
        this.projectId = this.projectInfo.id;*/
        let viewId = this.activatedRoute.snapshot.queryParams['view'];
        if(viewId) { this.navActivetab = viewId; }
    }

    dayOfYrDateFormat: string = AppConstant.displayDateFormat;
    dayjs(n?: number, format?: any) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs(n, format).tz(tz);
    };

    unix(n: number) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs.unix(n).tz(tz);
    };

    getTimeFromSeconds(n: number) {
        return dayjs().startOf('day').add(dayjs.duration(+n, 'seconds').asSeconds(), 'second');
    };

    abs(n){
        return Math.abs(n);
    }

    @Input()
    projectId: number;

    @Input()
    project: Project;

    tableLoading: boolean = false;
    allMembers: Array<any> = [];
    tempAllMembers: Array<any> = [];
    membersMap: any = {};
    dbDateFormat: string = 'YYYY-MM-DD';

    authUser$: User;

    @Input()
    biometric_meta: BioMetricSettingResponse;
    fetchingAccessPointsList: boolean = false;
    access_points_w_users: Array<{ name: string; users: Array<any> }> = [];
    fetchingOnSiteEvents: boolean = false;
    onsite_events: Array<Location> = [];
    default_in_time: {
        duration?: number;
        label?: string;
    } = {};
    exportInProgress: boolean = false;
    randomExportInProgress: boolean = false;
    noOfOperatives = null;
    randomOperativeModal: any = null;
    totalOnSiteOperatives: number = 0;
    searchValue: string = '';
    isInitMembers: boolean = false;
    loadingInlineMembers: boolean = false;

    ngOnInit() {
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });

        this.initializeTable();
        this.getMembers();
        // this.getOptimaSetting();
    }

    private initializeTable() {
        this.default_in_time.duration = this.timeUtility.convertDecimalHrToDuration(this.project.default_in_duration);
        this.default_in_time.label = this.default_in_time.duration ? this.timeUtility.showDuration(this.default_in_time.duration) : '-';
    }

    private getMembers(){
        if (!this.isInitMembers) {
          this.isInitMembers = true;
          this.tableLoading = true;
        } else {
          this.loadingInlineMembers = true;
        }
        this.optimaService.getProjectMembers(this.projectId, dayjs().valueOf()).subscribe(data => {
            this.tableLoading = false;
            this.loadingInlineMembers = false;
            if(data.members){
                this.allMembers = data.members;
                this.tempAllMembers = [...data.members];
            }else{
                this.allMembers = [];
                this.tempAllMembers = [];
                const message = data.message || 'Failed to fetch members data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        })
    }

    totalCountOf(key: string, key2?: string){
        if(key2){
            return this.allMembers.filter(a => a[key] && a[key2]).length;
        }
        return this.allMembers.filter(a => a[key]).length;
    }

    @ViewChild('allMemberTable') allMemberTable: any;
    toggleExpandRow(row, expanded) {
        if (this.expandedRow && this.expandedRow !== row) {
            this.allMemberTable.rowDetail.toggleExpandRow(this.expandedRow);
        }
        this.expandedRow = expanded ? null : row;
        this.allMemberTable.rowDetail.toggleExpandRow(row);
    }


    tabChange(tab) {
        // collapse expanded row if exists
        if (this.expandedRow) {
            this.allMemberTable.rowDetail.toggleExpandRow(this.expandedRow);
            this.expandedRow = null;
        }
        
        this.searchValue = '';
        if(tab === 'member-tab') {
            this.searchMemberValue = '';
            this.filterFn(this.searchMemberValue);
        }
        this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { view: tab }, queryParamsHandling: 'merge' });
    }

    @ViewChild(DatatableComponent) table: DatatableComponent;

    memberListFilter(event){
        const val = event.target.value.toLowerCase();

        // filter our data
        this.filterFn(val)
       
        // Whenever the filter changes, always go back to the first page
        // this.tableOffset = 0;
    }


    onDetailToggle({value}) {
        // console.log('Detail Toggled', value);
        if(value.daily_logs){
            // no need to fetch data when collapsing detail row
            value.daily_logs = undefined;
            return true;
        }
        value.loading = true;
        this.optimaService.getProjectMemberDailyEvents(this.projectId, [value.user_id]).subscribe((data) => {
            if(data.daily_logs){
                value.daily_logs = data.daily_logs;
            }else{
                value.daily_logs = [];
                const message = data.message || 'Failed to fetch member time data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
            value.loading = false;
        });
    }

    @ViewChild('timeAdjustment', { static: true }) timeAdjustmentRef: TimeAdjustmentProviderComponent;

    manageAdjustmentTime(event, row, user_meta){
        if(this.timeAdjustmentRef && this.timeAdjustmentRef.manageAdjustmentTime){
            let name = user_meta.name;//user_meta.additional_data && user_meta.additional_data.user_info && user_meta.additional_data.user_info.name;
            this.timeAdjustmentRef.manageAdjustmentTime(event, row, name, this.project);
        }

    }

    @ViewChild('timeEntryProvider', { static: false }) timeEntryProvider: TimeEntryProviderComponent;
    openTimeEntryProviderModal(event, row, {prop}, record){
        // console.log(row);        console.log(prop);        console.log(record);

        event.target.closest('datatable-body-cell').blur();

        this.timeEntryProvider.openTimeEntryModal({
            selected_day: this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(record.day_of_yr, this.dbDateFormat)),
            user_ref: row.user_id,
            name: row.name,
            event_type: (prop === 'clock_in' ? 'IN' : 'OUT'),
            user_location: {
                lat: 100,
                long: 100,
                distance: 1000
            },
        });
    }

    onManualTimeEntrySave(apiData){
        console.log(apiData);
        this.getMembers();
    }
    searchFunction(data) {
        try {
            this.loadingInlineMembers = true;
            let val= data.search.toLowerCase();
            this.searchValue = val;
            this.filterFn(val);
        } catch (error) {
            console.error('Something went wrong:', error);
        } finally {
            setTimeout(() => {
                this.loadingInlineMembers = false;
            }, 100);
        }
    }

    searchMemberFunction(data) {
        try {
            this.loadingInlineMembers = true;
            let val= data.search.toLowerCase();
            this.searchMemberValue = val;
            this.filterFn(val);
        } catch (error) {
            console.error('Something went wrong:', error);
        } finally {
                this.loadingInlineMembers = false;
        }
    }

    filterFn(val){
        const temp = this.tempAllMembers.filter(function (d) {
            return (
                (d.name.toLowerCase().indexOf(val) !== -1) ||
                (d.employer && d.employer.toLowerCase().indexOf(val) !== -1) ||
                (d.job_role && d.job_role.toLowerCase().indexOf(val) !== -1) ||
                !val
            );
        });

        // update the rows
        this.allMembers = temp;
    }
}


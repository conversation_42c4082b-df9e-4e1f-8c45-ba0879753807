<style>
    .detail-fixed-height{
        position: relative;
    }
    :host {
        .ngx-datatable {
            width: 100% !important;
        }
        .datatable-row-detail {
            width: 100% !important;
        }
        .datatable-row-detail-inner {
            width: 100% !important;
            margin: 0 !important;
            padding: 15px !important;
        }
        .datatable-body-row {
            width: 100% !important;
        }
        .datatable-scroll {
            width: 100% !important;
        }
    }
    agm-map {
        height: calc(100vh - 230px);
        width: 100%;
    }
    .gm-style img.rounded-circle{
        object-fit: cover;
        width: 100%;
        height: 70px;
    }
    .text-color-grey { color: #B2B2B2; }
    .flip_icon { transform: scaleX(-1); }
    .profile-img {
        position: absolute;
        width: 70px;
        height: 70px;
    }
    @media (max-width: 414px) {
        .width-75 {
            width: 75% !important;
        }
    }
    .img-border { border: 4px solid #B2B2B2;}
    .bg-color-grey { background-color: #B2B2B2;}
</style>
<block-loader [show]="tableLoading" [showBackdrop]="true" alwaysInCenter="true"></block-loader>
<div class="col-sm-12 p-0">
    <div class="col-sm-12 px-0 my-2">
        <ul ngbNav #nav="ngbNav" [(activeId)]="navActivetab" (activeIdChange)="tabChange($event)" class="nav-tabs n-tab nav">
            <li [ngbNavItem]="'member-tab'" [domId]="'member-tab'">
                <a ngbNavLink class="nav-a cursor-pointer">
                    Member Records
                </a>
                <ng-template ngbNavContent>
                    <ng-container *ngIf="!tableLoading">
                        <div class="d-flex justify-content-between flex-wrap flex-column flex-sm-row mb-2 pb-2">
                            <search-with-filters [searchWidth]="'col-md-12'" (searchEmitter)="searchMemberFunction($event)"></search-with-filters>
                            <member-list-buttons [projectId]="projectId" [project]="project" [allMembers]="allMembers" [biometric_meta]="biometric_meta"></member-list-buttons>
                        </div>
                        <div class="table-responsive-sm">
                            <ngx-datatable #allMemberTable
                                class="bootstrap table table-hover table-sm expandable timeManagementTable ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                [scrollbarV]="true"
                                [virtualization]="false"
                                [loadingIndicator]="loadingInlineMembers"
                                [rows]="allMembers"
                                [columnMode]="'force'"
                                [headerHeight]="50"
                                [sorts]="[{ prop: 'is_still_on_site', dir: 'desc' }]"
                                [footerHeight]="36"
                                [rowHeight]="'auto'"
                                [columns]="[
                                 {
                                     name:'', prop: 'id', width: 40, sortable: false,
                                     cellClass: 'py-2 p-2',
                                     cellTemplate: toggleBtnColumn, headerClass: 'font-weight-bold p-2',
                                     minWidth:'40',
                                     maxWidth:'50'
                                 },
                                 {
                                     name:'Name', prop: 'name',
                                     cellClass: 'py-2 p-2',
                                     cellTemplate: nameColumn,
                                     headerClass: 'font-weight-bold p-2',
                                     minWidth:'100'
                                 },
                                 {
                                     name:'Company', prop: 'employer',
                                     cellClass: 'py-2 p-2',
                                     cellTemplate: companyColumn,
                                     headerClass: 'font-weight-bold p-2',
                                     minWidth:'100'
                                 },
                                 {
                                     name:'Job Role', prop: 'job_role',
                                     cellClass: 'py-2 p-2',
                                     cellTemplate: jobRoleColumn,
                                     headerClass: 'font-weight-bold p-2',
                                     minWidth:'100'
                                 },
                                 {
                                     name:'On Site', prop: 'is_still_on_site',
                                     cellClass: 'py-2 p-2 text-center', maxWidth: 110,
                                     headerTemplate: countHeadTemplate,
                                     cellTemplate: countColumnTemplate,
                                     headerClass: 'p-2 text-center',
                                     minWidth:'100'
                                 },
                                 {
                                     name:'First Aider', prop: 'is_first_aider',
                                     cellClass: 'py-2 p-2 text-center', maxWidth: 130,
                                     headerTemplate: countHead2Template,
                                     cellTemplate: countColumnTemplate,
                                     headerClass: 'p-2 text-center',
                                     minWidth:'100'
                                 },
                                 {
                                     name:'Supervisor', prop: 'has_SMSTS_or_SSSTS',
                                     cellClass: 'py-2 p-2 text-center', maxWidth: 160,
                                     headerTemplate: countHead2Template,
                                     cellTemplate: countColumnTemplate,
                                     headerClass: 'p-2 text-center',
                                     minWidth:'100'
                                 }
                                ]">
                                <ng-template #toggleBtnColumn let-row="row" let-value="value" let-expanded="expanded">
                                    <i [class.fa-plus-square]="!expanded"
                                       [class.fa-minus-square]="expanded"
                                       title="Expand/Collapse Row"
                                       class="fa cursor-pointer"
                                       (click)="toggleExpandRow(row, expanded)">
                                    </i>
                                </ng-template>
                                <ng-template #nameColumn let-value="value">
                                    <span appTooltip>{{ value }}</span>
                                </ng-template>
                                <ng-template #companyColumn let-value="value">
                                    <span appTooltip>{{ value }}</span>
                                </ng-template>
                                <ng-template #jobRoleColumn let-value="value">
                                    <span appTooltip>{{ value }}</span>
                                </ng-template>
                                <ng-template #countHeadTemplate let-column="column">
                                    <strong>{{column.name}}</strong> ({{totalCountOf(column.prop)}})
                                </ng-template>
                                <ng-template #countHead2Template let-column="column">
                                    <strong>{{column.name}}</strong> <!--<span ngbTooltip="On site operatives" container="body">({{totalCountOf(column.prop, 'is_still_on_site')}})</span> -->
                                </ng-template>
                                <ng-template #countColumnTemplate let-row="row" let-value="value">
                                    <ng-container *ngIf="value">
                                        <i class="fa fa-check-circle text-success"> </i>
                                    </ng-container>
                                    <ng-container *ngIf="!value">
                                        <i class="fa fa-times-circle text-danger"> </i>
                                    </ng-container>
                                </ng-template>
                    
                                <!-- Row Detail Template -->
                                <ngx-datatable-row-detail
                                    #rowDetail
                                    [rowHeight]="'auto'"
                                    (toggle)="onDetailToggle($event)">
                                    <ng-template let-row="row" let-expanded="expanded" ngx-datatable-row-detail-template>
                                        <ng-container *ngIf="row.loading">
                                            <ngx-skeleton-loader count="5" [theme]="{'border-radius': '0',height:'36px',margin:'0px'}"></ngx-skeleton-loader>
                                        </ng-container>
                                        <ng-container *ngIf="!row.loading">
                                            <div *ngIf="!row.daily_logs || !row.daily_logs.length" class="text-center my-2"> No data to display</div>
                                            <div class="detail-fixed-height pl-4" *ngIf="row.daily_logs && row.daily_logs.length">
                                                <ngx-datatable
                                                        class="bootstrap table table-hover table-sm memberTimeDetail w-100"
                                                        [scrollbarH]="true"
                                                        [rows]="row.daily_logs"
                                                        [footerHeight]="38"
                                                        [limit]="10"
                                                        [sorts]="[{prop: 'day_of_yr', dir: 'desc'}]"
                                                        [columns]="[
                                                            {name:'Day', prop: 'day_of_yr', cellTemplate: dayOfYr, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', minWidth:'100'},
                                                            {name:'In time', prop: 'clock_in', cellTemplate: timestampCellTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', minWidth:'100'},
                                                            {name:'Out time', prop: 'clock_out', cellTemplate: timestampCellTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', minWidth:'100'},
                                                            {name:'Total time', prop: 'duration_in_sec', cellTemplate: totalInTimeCell, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', minWidth:'100'},
                                                            {name:'Adjust Hours', prop: 'adjustment', cellTemplate: adjustmentCellTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', minWidth:'100'}
                                                        ]"
                                                        [columnMode]="'force'"
                                                        [rowHeight]="'auto'">
                                                    <ng-template #totalInTimeCell let-value="value" let-row="row">
                                                        <div>
                                                            <span *ngIf="value">
                                                                {{ getTimeFromSeconds(row.effective_time).format('HH:mm:ss') }}
                                                            </span>
                                                            <span *ngIf="!value" [ngClass]="{'text-orange': default_in_time.duration}">
                                                                <ng-container *ngIf="row.adjustment" class="adjusted-time"><ng-container *ngIf="(row.adjustment < 0)">-</ng-container>{{ timeUtility.showDuration(abs(row.adjustment * 60)) }}</ng-container>
                                                                <ng-container *ngIf="!row.adjustment">{{ default_in_time.label }}</ng-container>
                                                            </span>
                                                            <span *ngIf="row.adjustment" class="small adjusted-time"> (<ng-container *ngIf="row.adjustment > 0">+</ng-container>{{ row.adjustment }} minutes)</span>
                                                        </div>
                                                    </ng-template>
                                                    <ng-template #dayOfYr let-value="value">
                                                        {{ value ? dayjs(value, dbDateFormat).format(dayOfYrDateFormat) : '-'}}
                                                    </ng-template>
                                                    <ng-template #timestampCellTemplate
                                                                 let-value="value" let-row2="row" let-column="column">
                                                        {{ value ? unix(+value).format('HH:mm:ss') : ''}}
                                                        <button *ngIf="!value" class="btn btn-xs" (click)="openTimeEntryProviderModal($event, row, column, row2)">-</button>
                                                    </ng-template>
                                                    <ng-template #adjustmentCellTemplate let-value="value" let-row2="row">
                                                        <div class="cursor-pointer text-info btn-link" (click)="manageAdjustmentTime($event, row2, row)">+/-</div>
                                                    </ng-template>
                                                </ngx-datatable>
                                            </div>
                                        </ng-container>
                                    </ng-template>
                                </ngx-datatable-row-detail>
                            </ngx-datatable>
                        </div>
                    </ng-container>
                </ng-template>
            </li>
            <li [ngbNavItem]="'visitor-tab'" [domId]="'visitor-tab'">
                <a ngbNavLink class="nav-a cursor-pointer">
                    Visitors
                </a>
                <ng-template ngbNavContent>
                    <ng-container *ngIf="!tableLoading">
                        <div class="d-flex justify-content-between flex-wrap flex-column flex-sm-row mb-2 pb-2">
                            <search-with-filters [searchWidth]="'col-md-12'" (searchEmitter)="searchFunction($event)"></search-with-filters>
                            <member-list-buttons [projectId]="projectId" [project]="project" [allMembers]="allMembers" [biometric_meta]="biometric_meta"></member-list-buttons>
                        </div>
                        <div class="mb-2">
                            <visitor-daily-time-logs [hideTitle]="true" [searchValue]="searchValue" [project]="project" [projectId]="projectId" [showMedicalInfo]="!project?.custom_field?.disable?.view_medication_modal"></visitor-daily-time-logs>
                        </div>
                    </ng-container>
                </ng-template>
            </li>
        </ul>
        <div [ngbNavOutlet]="nav" class="col-sm-12 my-2 pt-4 nav-panel custom-tab-radius tab-content time-mgt-tab-min-h"></div>
    </div>
</div>

<block-loader [show]="exportInProgress" alwaysInCenter="true"></block-loader>

<time-adjustment-provider #timeAdjustment></time-adjustment-provider>

<time-entry-provider
    #timeEntryProvider
    [projectId]="projectId"
    [projectInfo]="project"
    [country_code]="project?.custom_field?.country_code"
    (onSave)="onManualTimeEntrySave($event)"
></time-entry-provider>
<div class="d-flex">
    <modal-sidebar [modalSidebarOptions]="modalSidebarOptions" (selectedSidebarOption)="onSelection($event)">
    </modal-sidebar>
    <div class="w-100 p-4 modal-content-scroll overflow-auto">
        <div *ngIf="inductionSections.personalDetails === activeSidebarOption.key">
            <ng-container *ngTemplateOutlet="personalDetails"></ng-container>
        </div>
        <div *ngIf="inductionSections.employmentDetails === activeSidebarOption.key">
            <ng-container *ngTemplateOutlet="employmentDetails"></ng-container>
        </div>
        <div *ngIf="inductionSections.safetyAssessment === activeSidebarOption.key">
            <ng-container *ngTemplateOutlet="safetyAssessment"></ng-container>
        </div>
        <div *ngIf="inductionSections.healthMedical === activeSidebarOption.key">
            <ng-container *ngTemplateOutlet="healthMedical"></ng-container>
        </div>
        <div *ngIf="inductionSections.additionalQuestions === activeSidebarOption.key">
            <ng-container *ngTemplateOutlet="additionalQuestions"></ng-container>
        </div>
        <div *ngIf="inductionSections.rightToWork === activeSidebarOption.key">
            <ng-container *ngTemplateOutlet="rightToWork"></ng-container>
        </div>
        <div *ngIf="inductionSections.competenciesCerts === activeSidebarOption.key">
            <ng-container *ngTemplateOutlet="competenciesCerts"></ng-container>
        </div>
        <div *ngIf="inductionSections.conductCards === activeSidebarOption.key">
            <ng-container *ngTemplateOutlet="conductCards"></ng-container>
        </div>
        <div *ngIf="inductionSections.briefings === activeSidebarOption.key">
            <ng-container *ngTemplateOutlet="briefings"></ng-container>
        </div>
        <div *ngIf="inductionSections.inductionSummary === activeSidebarOption.key">
            <ng-container *ngTemplateOutlet="inductionSummary"></ng-container>
        </div>
        <div *ngIf="inductionSections.changeLog === activeSidebarOption.key">
            <ng-container *ngTemplateOutlet="changeLog">
            </ng-container>
        </div>
    </div>
</div>

<ng-template #commonInfo>
    <div class="tableV2 mb-4">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th>
                    {{ inductionRequestData.creator_name }}
                </th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="py-3 pl-3" style="height: 100px;">
                    <div class="d-flex flex-wrap">
                        <ng-container *ngIf="profileImageUrl; else placeholderImg">
                            <img
                                    class="rounded-circle profile-img mr-3 cursor-pointer"
                                    (click)="openProfileImage()"
                                    [src]="profileImageUrl"
                                    (error)="onImageError($event)"
                                    alt="Profile Image"
                            />
                        </ng-container>
                        <ng-template #placeholderImg>
                            <div class="rounded-circle placeholderImg mr-3 p-3">
                                <svg style="height: 54px; width: 58px">
                                    <use [attr.xlink:href]="alt_img_link"></use>
                                </svg>
                            </div>
                        </ng-template>
                        <div class="d-flex flex-column justify-content-center">
                            <div>User ID: <span class="fw-500">{{ inductionRequestData.additional_data.user_info.id }}</span></div>
                            <div>Project: <span class="fw-500">{{ inductionRequestData.additional_data.project.name }}</span></div>
                            <div>Record Number: <span class="fw-500">{{ inductionRequestData.record_id }}</span></div>
                            <div>
                                <div
                                        ngbTooltip="The user's profile photo matches the photo from the CSCS Smart Check" placement="bottom"
                                        *ngIf="(inductionRequestData.fr_similarity_result && inductionRequestData.fr_similarity_result.success)"
                                        class="v-badge m-0 text-info">
                                    <span class="material-symbols-outlined">ar_on_you</span> Verified
                                </div>
                                <div
                                            ngbTooltip="The user's profile photo does not match the photo from the CSCS Smart Check" placement="bottom"
                                            *ngIf="projectContractor.fr_setting?.enabled && projectContractor.cscs_status?.enabled && (!inductionRequestData.fr_similarity_result || !inductionRequestData.fr_similarity_result.success)"
                                            class="v-badge m-0 text-secondary">
                                        <span class="material-symbols-outlined">ar_on_you</span> Not verified
                                    </div>
                            </div>

                        </div>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</ng-template>

<ng-template #personalDetails>
    <ng-container *ngTemplateOutlet="commonInfo"></ng-container>
    <!-- Personal Details -->
    <div class="tableV2 mb-4">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th colspan="2">Personal Details</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td width="50%">Title</td>
                <td class="fw-500" width="50%">{{ inductionRequestData.additional_data.user_info.title }}</td>
            </tr>
            <tr>
                <td width="50%">Name</td>
                <td class="fw-500" width="50%">{{ inductionRequestData.additional_data.user_info.name }}</td>
            </tr>
            <tr>
                <td width="50%">Date of birth</td>
                <td class="fw-500" width="50%">
                    {{ displayDate(inductionRequestData.additional_data.user_info.dob) }}<span *ngIf="canShowUnder18Badge" class="under18Sign">18</span>
                </td>
            </tr>
            <tr>
                <td width="50%">Gender</td>
                <td class="fw-500" width="50%">{{ inductionRequestData.additional_data.user_info.gender }}</td>
            </tr>
            <tr>
                <td width="50%">Nationality</td>
                <td class="fw-500" width="50%">{{ inductionRequestData.additional_data.user_info.country }}</td>
            </tr>
            <tr *ngIf="false">
                <td width="50%">National insurance number</td>
                <td class="fw-500" width="50%">{{ inductionRequestData.additional_data.user_info.nin }}</td>
            </tr>
            </tbody>
        </table>
    </div>

    <!-- Contact Details -->
    <div class="tableV2 mb-4">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th colspan="2">Contact Details</th>
            </tr>
            </thead>
            <tbody>
            <!--<tr>
                <td width="50%"><div class="h-100">Address</div></td>
                <td class="fw-500" width="50%">
                    <ng-container *ngIf="inductionRequestData?.additional_data?.contact_detail">
                        {{ inductionRequestData.additional_data.contact_detail?.house_no || '' }}
                        {{ inductionRequestData.additional_data.contact_detail?.house_no ? ',' : '' }}
                        {{ inductionRequestData.additional_data.contact_detail?.street || '' }}
                        {{ inductionRequestData.additional_data.contact_detail?.street ? ',' : '' }}
                        {{ inductionRequestData.additional_data.contact_detail?.city || '' }}
                        {{ inductionRequestData.additional_data.contact_detail?.city ? ',' : '' }}
                        {{ inductionRequestData.additional_data.contact_detail?.country || '' }}
                    </ng-container>
                </td>
            </tr>-->
            <tr>
                <td width="50%">City/Town</td>
                <td class="fw-500" width="50%"> {{ inductionRequestData.additional_data.contact_detail?.city }} </td>
            </tr>
            <tr>
                <td width="50%">Postcode/Zipcode</td>
                <td class="fw-500" width="50%"> {{ inductionRequestData.additional_data.contact_detail?.post_code }} </td>
            </tr>
            <tr>
                <td width="50%">Local worker</td>
                <td class="fw-500" width="50%"> {{ inductionRequestData.is_local_worker }} </td>
            </tr>
            <tr *ngIf="inductionRequestData.additional_data.contact_detail.home_no">
                <td width="50%">Home number</td>
                <td class="fw-500" width="50%">
                        <span *ngIf="inductionRequestData.additional_data.contact_detail.home_number?.code">
                            ({{
                                (inductionRequestData.additional_data.contact_detail.home_number.code).includes('+') ?
                                    inductionRequestData.additional_data.contact_detail.home_number.code :
                                    '+'+inductionRequestData.additional_data.contact_detail.home_number.code
                            }})
                        </span>
                    {{ inductionRequestData.additional_data.contact_detail?.home_number?.number || 'N/A' }}
                </td>
            </tr>
            <tr>
                <td width="50%">Mobile number</td>
                <td class="fw-500" width="50%">
                        <span *ngIf="inductionRequestData.additional_data.contact_detail.mobile_number?.code">
                            ({{
                                (inductionRequestData.additional_data.contact_detail.mobile_number.code).includes('+') ?
                                    inductionRequestData.additional_data.contact_detail.mobile_number.code :
                                    '+'+inductionRequestData.additional_data.contact_detail.mobile_number.code
                            }})
                        </span>
                    {{ inductionRequestData.additional_data.contact_detail.mobile_number?.number }}
                </td>
            </tr>
            <tr *ngIf="!isShadowUser">
                <td width="50%">Email</td>
                <td class="fw-500" width="50%">{{ inductionRequestData.additional_data.user_info?.email }}
                </td>
            </tr>
            <tr>
                <td width="50%">Emergency Contact</td>
                <td class="fw-500" width="50%">{{ inductionRequestData.additional_data.contact_detail.emergency_contact }}
                </td>
            </tr>
            <tr>
                <td width="50%">Emergency Contact No.</td>
                <td class="fw-500" width="50%">
                        <span *ngIf="inductionRequestData.additional_data.contact_detail.emergency_contact_number?.code">
                            ({{
                                (inductionRequestData.additional_data.contact_detail.emergency_contact_number.code).includes('+') ?
                                    inductionRequestData.additional_data.contact_detail.emergency_contact_number.code :
                                    '+'+inductionRequestData.additional_data.contact_detail.emergency_contact_number.code
                            }})
                        </span>
                    {{ inductionRequestData.additional_data.contact_detail.emergency_contact_number?.number || inductionRequestData.additional_data.contact_detail.emergency_contact_no }}
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <!-- Travel Details -->
    <div class="tableV2" *ngIf="!isShadowUser">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th colspan="2">Travel Details</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td width="50%">Total travel time</td>
                <td class="fw-500" width="50%">{{ travelTime(inductionRequestData) }}</td>
            </tr>
            <tr>
                <td width="50%">Travel time to work</td>
                <td class="fw-500" width="50%">{{ travelTime(inductionRequestData, 'to_work') }}</td>
            </tr>
            <tr>
                <td width="50%">Travel time from work</td>
                <td class="fw-500" width="50%">{{ travelTime(inductionRequestData, 'to_home') }}</td>
            </tr>
            <tr>
                <td width="50%">Method of travel</td>
                <td class="fw-500" width="50%">{{ inductionRequestData.travel_method }}</td>
            </tr>
            <tr>
                <td width="50%">Vehicle registration number</td>
                <td class="fw-500" width="50%">{{ inductionRequestData.vehicle_reg_number || 'N/A' }}</td>
            </tr>
            </tbody>
        </table>
    </div>
</ng-template>

<ng-template #employmentDetails>
    <ng-container *ngTemplateOutlet="commonInfo"></ng-container>

    <div class="tableV2">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th colspan="2">Employment Details</th>
            </tr>
            </thead>
            <tbody *ngIf="inductionRequestData?.additional_data?.employment_detail as detail">
            <tr>
                <td width="50%">Company</td>
                <td class="fw-500" width="50%">
                    {{ detail.type_of_employment === 'Agency' ? detail.employment_company : detail.employer }}
                </td>
            </tr>
            <tr>
                <td width="50%">Job role</td>
                <td class="fw-500" width="50%">{{ detail.job_role }}</td>
            </tr>
            <tr *ngIf="showTypeOfEmployment">
                <td width="50%">Type of employment</td>
                <td class="fw-500" width="50%">{{ detail.type_of_employment }}</td>
            </tr>
            <tr *ngIf="detail.start_date_with_employer && showEmploymentStartDate">
                <td width="50%">Time with employer</td>
                <td class="fw-500" width="50%">{{ duration(detail.start_date_with_employer) }}</td>
            </tr>
            <tr *ngIf="showMinWage">
                <td width="50%">Do you earn above the min. living wage</td>
                <td class="fw-500" width="50%">{{ detail.earn_mlw_e783 ? 'Yes' : 'No' }}</td>
            </tr>
            <tr *ngIf="showEmpNbr">
                <td width="50%">Employee number</td>
                <td class="fw-500" width="50%">{{ detail.employee_number ? detail.employee_number : 'N/A'}}</td>
            </tr>
            <tr *ngIf="showNIN">
                <td width="50%">National insurance number</td>
                <td class="fw-500" width="50%">{{ inductionRequestData.additional_data.user_info.nin ? inductionRequestData.additional_data.user_info.nin : 'N/A' }}</td>
            </tr>
            <tr *ngIf="detail.comment">
                <td width="50%">Comments</td>
                <td class="fw-500" width="50%">
                    <span [innerHTML]="detail.comment"></span>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</ng-template>

<ng-template #safetyAssessment>
    <ng-container *ngTemplateOutlet="commonInfo"></ng-container>
    <div class="tableV2">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th>Safety Assessment</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="p-0">
                    <div class="pl-4">
                        <div class="row mx-0 py-3">
                            <span class="col-11 px-0">I confirm I am fit and able to undertake the work activities my role requires?</span>
                            <span class="col-1 fw-500">{{ inductionRequestData.fit_undertake_role ? "Yes" : "No" }}</span>
                        </div>
                        <hr class="m-0">
                        <div class="row mx-0 py-3">
                            <span class="col-11 px-0">I confirm I am fit to work safely?</span>
                            <span class="col-1 fw-500">{{ inductionRequestData.fit_to_work ? "Yes" : "No" }}</span>
                        </div>
                        <hr class="m-0">
                        <ng-container *ngFor="let further_policy of inductionRequestData.additional_data?.project?.further_policies || []">
                            <div class="row mx-0 py-3" *ngIf="further_policy.key === 'working_hr_agreement'">
                                <span class="col-11 px-0">I will comply with the working hours agreement on site?</span>
                                <span class="col-1 fw-500">{{ inductionRequestData.comply_hour_agreement ? "Yes" : "No" }}</span>
                            </div>
                            <hr *ngIf="further_policy.key === 'working_hr_agreement'" class="m-0">
                            <div class="row mx-0 py-3" *ngIf="further_policy.key === 'c_lens_policy'">
                                <span class="col-11 px-0">After reading the contact lens site directive, selected option:</span>
                                <span class="col-1 fw-500">{{ inductionRequestData.site_directive_selection ? "Yes" : "No" }}</span>
                            </div>
                            <hr *ngIf="further_policy.key === 'c_lens_policy'" class="m-0">
                            <div class="row mx-0 py-3" *ngIf="further_policy.key === 'd_and_a_policy'">
                                <span class="col-11 px-0">Do you confirm that you have read and understood the drugs and alcohol policy?</span>
                                <span class="col-1 fw-500">{{ inductionRequestData.accept_drug_alcohol_pol ? "Yes" : "No" }}</span>
                            </div>
                            <hr *ngIf="further_policy.key === 'd_and_a_policy'" class="m-0">
                            <div class="row mx-0 py-3">
                                <span class="col-11 px-0">Do you confirm that you have read and understood the {{ further_policy.policy_name }}?</span>
                                <span class="col-1 fw-500">Yes</span>
                            </div>
                            <hr class="m-0">
                        </ng-container>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</ng-template>

<ng-template #healthMedical>
    <ng-container *ngTemplateOutlet="commonInfo"></ng-container>
    <div class="tableV2 mb-4" *ngIf="!isShadowUser && showSiteHealthAssessment">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th>Site Health Assessment</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="p-0">
                    <div class="pl-4">
                        <div class="row mx-0 py-3">
                            <span class="col-11 px-0">Do you have any reportable medical conditions? </span>
                            <span class="col-1 fw-500" [ngClass]="{'text-danger': inductionRequestData.reportable_medical_conditions?.toString().toLowerCase().trim() === 'yes'}">{{ (inductionRequestData.reportable_medical_conditions | titlecase) || '-' }}</span>
                            <div class="col-12 px-0" *ngIf="inductionRequestData?.reportable_medical_conditions && inductionRequestData.reportable_medical_conditions?.toString().toLowerCase().trim() === 'yes'">
                                <span class="fw-500">Detail:</span>
                                <p class="mb-0">{{ inductionRequestData.rmc_detail || 'No details provided' }}</p>
                            </div>
                        </div>
                        <hr class="m-0">
                        <div class="row mx-0 py-3">
                            <span class="col-11 px-0">Are you taking any regular medication, prescribed or otherwise?</span>
                            <span class="col-1 fw-500" [ngClass]="{'text-danger': inductionRequestData.on_long_medication?.toString().toLowerCase().trim() === 'yes'}">{{ (inductionRequestData.on_long_medication | titlecase) || '-' }}</span>
                            <div class="col-12 px-0" *ngIf="inductionRequestData.on_long_medication && inductionRequestData.on_long_medication?.toString().toLowerCase().trim() === 'yes'">
                                <ng-container *ngFor="let med of inductionRequestData.medications || []">
                                    <div class="row mx-0">
                                        <span class="col-12 px-0">Name: <b class="text-danger"> {{ med?.medication_name }} </b></span>
                                        <span class="col-12 px-0">Dosage: <b class="text-danger"> {{ med?.medication_dosage }} </b></span>
                                        <span class="col-12 px-0">Frequency: <b class="text-danger"> {{ med?.medication_frequency }} </b></span>
                                        <span class="col-12 px-0">Commenced date: <b class="text-danger"> {{ correctDateFormat(med?.medication_date_commenced) }} </b></span>
                                        <span class="col-12 px-0">Completion date: <b class="text-danger"> {{ correctDateFormat(med?.medication_date_of_completion) }} </b></span>
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="tableV2 mb-4" *ngIf="showProfileHealthAssessment &&inductionRequestData.additional_data.health_assessment_answers?.length">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th>Health Assessment</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="p-0">
                    <ng-container *ngFor="let data of inductionRequestData.health_assessment_answers; let idx = index">
                        <div class="pl-4">
                            <div class="row mx-0 py-3">
                                <span class="col-12 px-0 fw-500">{{ data.category }}</span>
                            </div>
                            <ng-container *ngFor="let q of data.questions; let i = index">
                                <div class="row mx-0 py-3">
                                    <span class="col-11 px-0">{{ q.question }}</span>
                                    <span class="col-1 fw-500" [ngClass]="{'text-danger': q.answer == 'Yes'}">
                                            {{ q.answer}}
                                        </span>
                                </div>
                                <hr *ngIf="i < data.questions?.length - 1" class="m-0">
                            </ng-container>
                        </div>
                        <hr *ngIf="idx < inductionRequestData.health_assessment_answers?.length - 1" class="m-0">
                    </ng-container>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="tableV2" *ngIf="showProfileMedicalAssessment && inductionRequestData.additional_data?.medical_assessments_answers?.length">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th>Medical Assessment</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="p-0">
                    <div class="pl-4">
                        <ng-container
                                *ngFor="let data of inductionRequestData.additional_data?.medical_assessments_answers; let i = index">
                            <div class="row mx-0 py-3">
                                <span class="col-11 px-0">{{ data.question }}</span>
                                <span class="col-1 fw-500" [ngClass]="{'text-danger': +data.answer === 1}">
                                        {{ +data.answer === 1 ? 'Yes' : 'No'}}
                                    </span>
                            </div>
                            <div class="row mx-0 pb-3" *ngIf="data?.ans_details">
                                <span class="col-12 px-0">please specify</span>
                                <span class="col-12 px-0 fw-500">{{ data.ans_details }}</span>
                            </div>
                            <hr *ngIf="i < inductionRequestData.additional_data.medical_assessments_answers?.length - 1"
                                class="m-0">
                        </ng-container>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</ng-template>

<ng-template #additionalQuestions>
    <ng-container *ngTemplateOutlet="commonInfo"></ng-container>
    <div class="tableV2 mb-4">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th>
                    {{ inductionRequestData.induction_question_answers.additional_qa_title ?
                    inductionRequestData.induction_question_answers.additional_qa_title : 'Additional Questions' }}
                </th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="p-0">
                    <div class="pl-4">
                        <ng-container *ngFor="let q of inductionRequestData.induction_question_answers.additional_qa; let i = index">
                            <div class="row mx-0 py-3">
                                <span class="col-12 px-0"> {{ q.question }} </span>
                                <ng-container *ngIf="(q.ans_field_type === 'date' && q.ans_epoch); else elseAnswer">
                                    <span class="col-12 px-0 fw-500">{{ displayDate(q.ans_epoch) }}</span>
                                </ng-container>
                                <ng-template #elseAnswer>
                                    <span class="col-12 px-0 fw-500">{{ q.answer }}</span>
                                </ng-template>
                            </div>
                            <ng-container *ngIf="inductionRequestData.induction_question_answers?.additional_qa[i].sub_questions?.length">
                                <div class="row mx-0 pb-3" *ngFor="let sub_q of inductionRequestData.induction_question_answers?.additional_qa[i].sub_questions || []; let j = index">
                                    <ng-container *ngIf="(inductionRequestData.induction_question_answers.additional_qa[i].sub_questions[j].que_condition).toString().trim().toLowerCase() === ('If ' + inductionRequestData.induction_question_answers.additional_qa[i].answer).toString().trim().toLowerCase()">
                                        <span class="col-12 px-0">{{ sub_q.question }}</span>
                                        <ng-container *ngIf="(sub_q.ans_field_type === 'date' && sub_q.ans_epoch); else elseSubAnswer">
                                            <span class="col-12 px-0 fw-500">{{ displayDate(sub_q.ans_epoch) }}</span>
                                        </ng-container>
                                        <ng-template #elseSubAnswer>
                                            <span class="col-12 px-0 fw-500">{{ sub_q.answer }}</span>
                                        </ng-template>
                                    </ng-container>
                                </div>
                            </ng-container>
                            <hr *ngIf="i < inductionRequestData.induction_question_answers?.additional_qa?.length - 1" class="m-0">
                        </ng-container>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</ng-template>

<ng-template #rightToWork>
    <ng-container *ngTemplateOutlet="commonInfo"></ng-container>
    <div class="tableV2 mb-4">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th>Right to Work</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="p-0">
                    <div class="pl-4">
                        <!-- <div class="row mx-0 py-3">
                            <span class="col-11 px-0">Valid PPAC ID provided</span>
                            <div class="col-1 fw-500"><span class="float-right">Yes</span></div>
                        </div>
                        <hr class="m-0"> -->
                        <ppac-status-card
                                [showInput]="false"
                                [fromApproval]="true"
                                [onReviewStep]="true"
                                [showVerifyBtn]="false"
                                [authUser$]="authUser$"
                                [induction_phrase]="projectInfo?.custom_field?.induction_phrase_singlr"
                                [parent_company]="projectInfo?.parent_company"
                                [contractor_rtw_config]="projectContractor.rtw_status"
                                [ppac_data]="{doc_id: inductionRequestData.rtw_doc_code, rtw_result: inductionRequestData.rtw_check_result}"
                        ></ppac-status-card>
                        <!-- <hr class="m-0">
                        <div class="row mx-0 py-3">
                            <div class="col-10 px-0">Both stages of PPAC ID verified, this induction may now be
                                approved</div>
                            <div class="col-2">
                                <div class="float-right">
                                    <span class="material-symbols-outlined text-success" [ngbTooltip]="Tooltip">
                                        check_circle </span>
                                    <span class="material-symbols-outlined text-success" [ngbTooltip]="Tooltip">
                                        check_circle </span>
                                    <span class="material-symbols-outlined text-success" [ngbTooltip]="Tooltip">
                                        check_circle </span>
                                    <ng-template #Tooltip>Tooltip here</ng-template>
                                </div>
                            </div>
                        </div> -->
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <!-- <pre>{{ projectContractor | json }}</pre> -->
</ng-template>

<ng-template #completeCompetence>
    <ccl-status-tile [config]="cclStatusConfig"></ccl-status-tile>
</ng-template>

<ng-template #competenciesCerts>
    <ng-container *ngTemplateOutlet="commonInfo"></ng-container>
    <ng-container *ngTemplateOutlet="completeCompetence"></ng-container>
    <div class="tableV2 mb-4">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th>Competencies</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let doc of inductionRequestData.additional_data.user_docs || []">
                <td class="pr-0">
                    <div class="row mx-0 py-3">
                        <div class="col-12 px-0">
                            <div class="d-flex flex-wrap justify-content-between w-100">
                                <div>
                                    <div class="d-flex align-items-center flex-wrap b-1">
                                        <span class="fw-500">{{ doc.name }}</span>
                                        <ng-container *ngIf="doc._citb_enabled_doc">
                                            <div class="d-flex align-items-center ml-2">
                                                <ng-container *ngIf="doc?.is_verified === 1; else notVerified">
                                                    <span class="text-status verified fw-500">Verified</span>
                                                    <span class="material-symbols-outlined fill icon-verified ml-1"
                                                          [ngbTooltip]="'Document has been verified'">verified</span>
                                                </ng-container>
                                                <ng-template #notVerified>
                                                    <span class="text-status text-danger fw-500">Not Verified</span>
                                                    <span class="material-symbols-outlined icon-warning text-danger ml-1"
                                                          [ngbTooltip]="'Document requires verification'">warning</span>
                                                </ng-template>
                                            </div>
                                        </ng-container>
                                    </div>
                                    <div class="mb-1">
                                        <span class="font-weight-light">Doc. Number: </span>
                                        <span>{{ doc.doc_number }}</span>
                                    </div>
                                    <div>
                                        <ng-container *ngIf="isDateExpired(doc.expiry_date); else notExpired">
                                                <span class="fw-500 text-danger font-italic">
                                                    Expired {{ displayDate(+doc.expiry_date) }}
                                                </span>
                                        </ng-container>
                                        <ng-template #notExpired>
                                            <span class="font-weight-light">Expiry: </span>
                                            <span>{{ displayDate(+doc.expiry_date) }}</span>
                                        </ng-template>
                                    </div>
                                </div>
                                <div *ngIf="doc.user_files?.length" class="rounded comp-img cursor-pointer position-relative bg-anti-flash-white mr-3" (click)="openImage(doc)" [title]="doc.user_files.length+' images'">
                                    <ng-container *ngIf="doc.user_files[0]?.file_mime === 'application/pdf'; else isImg">
                                        <span class="material-symbols-outlined center-absolute" style="font-size: 45px;"> picture_as_pdf </span>
                                    </ng-container>
                                    <ng-template #isImg>
                                        <div class="image-box" [ngStyle]="{ 'background-image': 'url(' + (doc.user_files[0].sm_url || doc.user_files[0].file_url) + ')'}">
                                                <span class="d-flex justify-content-center align-items-center image-text">
                                                    {{ doc.user_files.length || 0 }}<br />
                                                    {{ (doc.user_files.length || 0) > 1 ? 'images' : 'image' }}
                                                </span>
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            <tr *ngIf="!inductionRequestData.additional_data.user_docs?.length">
                <td colspan="5">
                    <div class="w-100 d-flex justify-content-center py-4"> No Records </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</ng-template>

<ng-template #conductCards>
    <ng-container *ngTemplateOutlet="commonInfo"></ng-container>
    <div class="tableV2 mb-4">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th colspan="5">Conduct Cards</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="fw-500">Card type</td>
                <td class="fw-500">Date issued</td>
                <td class="fw-500">Issued by</td>
                <td class="fw-500">Action</td>
                <td class="fw-500">Expiry</td>
            </tr>
            <tr *ngFor="let data of inductionRequestData?.conduct_cards">
                <td>{{ data.card_detail.card_type }}</td>
                <td>{{ displayDate(data.createdAt) }}</td>
                <td>
                    {{ data.assigned_by_ref.name }}
                </td>
                <td>{{ data.card_detail.card_action }}</td>
                <td>{{ data.card_detail.indefinite ? 'N/A' : displayDate(+data.expire_on) }}</td>
            </tr>
            <tr *ngIf="!inductionRequestData.conduct_cards?.length">
                <td colspan="5">
                    <div class="w-100 d-flex justify-content-center py-4"> No Records </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</ng-template>

<ng-template #briefings>
    <ng-container *ngTemplateOutlet="commonInfo"></ng-container>
    <nav>
        <div class="nav nav-tabs" id="nav-tab" role="tablist">
            <a class="nav-item nav-link" id="nav-home-tab" (click)="setActiveTab('rams')"
                [ngClass]="{'active': activeTab === 'rams'}" role="tab" aria-controls="nav-home" aria-selected="true">
                {{ projectInfo.custom_field.rams_phrase_singlr || 'RAMS' }} ({{ receivedBriefings?.ramsItems.length || 0 }})
            </a>
            <a class="nav-item nav-link" id="nav-profile-tab" (click)="setActiveTab('toolbox_talks')"
                [ngClass]="{'active': activeTab === 'toolbox_talks'}" role="tab" aria-controls="nav-profile" aria-selected="false">
                Toolbox Talks ({{ receivedBriefings?.toolboxTalksItems.length || 0 }})
            </a>
            <a class="nav-item nav-link" id="nav-contact-tab" (click)="setActiveTab('wpps')"
                [ngClass]="{'active': activeTab === 'wpps'}" role="tab" aria-controls="nav-contact" aria-selected="false">
                {{ projectInfo.custom_field.wpp_phrase_singlr || 'WPPs' }} ({{ receivedBriefings?.wppItems.length || 0 }})
            </a>
            <a class="nav-item nav-link" id="nav-contact-tab2" (click)="setActiveTab('task_briefings')"
                [ngClass]="{'active': activeTab === 'task_briefings'}" role="tab" aria-controls="nav-contact2" aria-selected="false">
                {{ projectInfo.custom_field.tb_phrase_singlr || 'Task Briefings' }} ({{ receivedBriefings?.taskBriefingsItems.length || 0 }})
            </a>
        </div>
    </nav>

    <div class="tab-content" id="nav-tabContent">
        <div class="tab-pane fade pt-3" [ngClass]="{'show active': activeTab === 'rams'}" id="nav-home" role="tabpanel"
             aria-labelledby="nav-home-tab">
            <div class="tableV2 mb-4">
                <table class="w-100 mb-0" style="table-layout: fixed;">
                    <thead>
                    <tr>
                        <th>Title</th>
                        <th>Briefed by</th>
                        <th>Date & Time</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let data of receivedBriefings?.ramsItems">
                        <td class="text-break">{{ data.briefing_title }}</td>
                        <td class="text-break">{{ data.briefed_by }}</td>
                        <td class="text-break">{{ displayDate(+data.briefed_at, AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}</td>
                    </tr>
                    <ng-container *ngIf="!receivedBriefings?.ramsItems?.length">
                        <ng-container *ngTemplateOutlet="noRecords"></ng-container>
                    </ng-container>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="tab-pane fade pt-3" [ngClass]="{'show active': activeTab === 'toolbox_talks'}" id="nav-profile"
             role="tabpanel" aria-labelledby="nav-profile-tab">
            <div class="tableV2 mb-4">
                <table class="w-100 mb-0" style="table-layout: fixed;">
                    <thead>
                    <tr>
                        <th>Title</th>
                        <th>Briefed by</th>
                        <th>Date & Time</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let data of receivedBriefings?.toolboxTalksItems">
                        <td class="text-break">{{ data.briefing_title }}</td>
                        <td class="text-break">{{ data.briefed_by }}</td>
                        <td class="text-break">{{ displayDate(+data.briefed_at, AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}</td>
                    </tr>
                    <ng-container *ngIf="!receivedBriefings?.toolboxTalksItems?.length">
                        <ng-container *ngTemplateOutlet="noRecords"></ng-container>
                    </ng-container>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="tab-pane fade pt-3" [ngClass]="{'show active': activeTab === 'wpps'}" id="nav-contact"
             role="tabpanel" aria-labelledby="nav-contact-tab">
            <div class="tableV2 mb-4">
                <table class="w-100 mb-0" style="table-layout: fixed;">
                    <thead>
                    <tr>
                        <th>Title</th>
                        <th>Briefed by</th>
                        <th>Date & Time</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let data of receivedBriefings?.wppItems">
                        <td class="text-break">{{ data.briefing_title }}</td>
                        <td class="text-break">{{ data.briefed_by }}</td>
                        <td class="text-break">{{ displayDate(+data.briefed_at, AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}</td>
                    </tr>
                    <ng-container *ngIf="!receivedBriefings?.wppItems?.length">
                        <ng-container *ngTemplateOutlet="noRecords"></ng-container>
                    </ng-container>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="tab-pane fade pt-3" [ngClass]="{'show active': activeTab === 'task_briefings'}" id="nav-contact2"
             role="tabpanel" aria-labelledby="nav-contact-tab2">
            <div class="tableV2 mb-4">
                <table class="w-100 mb-0" style="table-layout: fixed;">
                    <thead>
                    <tr>
                        <th>Title</th>
                        <th>Briefed by</th>
                        <th>Date & Time</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let data of receivedBriefings?.taskBriefingsItems">
                        <td class="text-break">{{ data.briefing_title }}</td>
                        <td class="text-break">{{ data.briefed_by }}</td>
                        <td class="text-break">{{ displayDate(+data.briefed_at, AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}</td>
                    </tr>
                    <ng-container *ngIf="!receivedBriefings?.taskBriefingsItems?.length">
                        <ng-container *ngTemplateOutlet="noRecords"></ng-container>
                    </ng-container>
                    </tbody>
                </table>
            </div>
        </div>

        <ng-template #noRecords>
            <tr>
                <td colspan="3">
                    <div class="w-100 d-flex justify-content-center py-4"> No Records </div>
                </td>
            </tr>
        </ng-template>
    </div>
</ng-template>

<ng-template #inductionSummary>
    <ng-container *ngTemplateOutlet="commonInfo"></ng-container>
    <div class="tableV2 mb-4" *ngIf="!isShadowUser && inductionRequestData.declarations?.supervisor?.length">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th>Supervisor Declaration</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="pr-0">
                    <div class="row mx-0 py-2" *ngFor="let declaration of inductionRequestData.declarations.supervisor">
                        <div class="col-10 px-0">{{ declaration.question }}</div>
                        <div class="col-2">
                            <div class="float-right">
                                <span class="material-symbols-outlined check_circle fill text-success"> check_circle </span>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="tableV2 mb-4" *ngIf="!isShadowUser && inductionRequestData.declarations?.operator?.length">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th>Plant/Machinery Operator Declaration</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="pr-0">
                    <ng-container *ngFor="let declaration of inductionRequestData.declarations.operator; let i = index">
                        <div class="row mx-0 py-2">
                            <div class="col-11 px-0">
                                <span>{{ declaration.question }}</span>
                            </div>
                            <div class="col-1" [ngClass]="{'col-12 px-0': declaration.type == 'text'}">
                                    <span class="fw-500" [ngClass]="{'text-danger': declaration.answer.toString().toLowerCase().trim() === 'no'}">
                                        {{ declaration.answer | titlecase }}
                                    </span>
                            </div>
                        </div>
                        <hr  class="my-0" *ngIf="i !== inductionRequestData.declarations.operator.length - 1">
                    </ng-container>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="tableV2 mb-4" *ngIf="!isShadowUser">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th>Declarations</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="pr-0">
                    <div class="row mx-0 py-2" *ngFor="let declaration of inductionRequestData.additional_data.project.declarations || []">
                        <div class="col-10 px-0">{{ declaration.content }}</div>
                        <div class="col-2">
                            <div class="float-right">
                                <span class="material-symbols-outlined check_circle fill text-success"> check_circle </span>
                            </div>
                        </div>
                    </div>
                    <div class="row mx-0 py-2" *ngIf="inductionRequestData.confirm_detail_valid">
                        <div class="col-10 px-0">I confirm that I have reviewed all information provided in my profile and on this form and agree it is correct and up to date.</div>
                        <div class="col-2">
                            <div class="float-right">
                                <span class="material-symbols-outlined check_circle fill text-success"> check_circle </span>
                            </div>
                        </div>
                    </div>
                    <div class="row mx-0 py-2" *ngIf="inductionRequestData.additional_data?.project?.has_media_content && inductionRequestData.accepting_media_declaration">
                        <div class="col-10 px-0">
                            {{ inductionRequestData.additional_data.project.media_declaration_content }}
                            <i *ngIf="inductionRequestData?.additional_data.project.media_file_ids[0]?.id" class="small">
                                ({{ inductionRequestData?.additional_data.project.media_file_ids[0]?.name }})
                            </i>
                        </div>
                        <div class="col-2">
                            <div class="float-right">
                                <span class="material-symbols-outlined check_circle fill text-success"> check_circle </span>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="tableV2 mb-4" *ngIf="inductionRequestData.induction_answers?.length;">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th>{{ inductionRequestData.additional_data.project.custom_field.induction_phrase_singlr || 'Induction' }} Quiz</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="pr-0">
                    <div class="row mx-0 py-2">
                        <ng-container *ngFor="let answer_group of inductionRequestData.induction_answers; let i = index; let last = index">
                            <div class="col-12 px-0" [ngClass]="{'mb-4': !last}">
                                <div class="question mb-2">{{ inductionRequestData.induction_question_answers?.questions[i]?.question }}</div>
                                <div class="answers">
                                    <ng-container *ngFor="let answer of answer_group; let j = index">
                                        <div class="fw-500"> {{ answer }} </div>
                                    </ng-container>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="tableV2 mb-4" *ngIf="inductionRequestData.additional_data.project.media_file_ids?.length;">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th>{{ inductionRequestData.additional_data.project.custom_field.induction_phrase_singlr || 'Induction' }} Media</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="pr-0">
                    <div class="row mx-0 py-2">
                        <div class="col-12 px-0">
                            <div class="media-files-container">
                                <div class="media-file-item mb-2" *ngFor="let file of inductionRequestData.additional_data.project.media_file_ids">
                                    <a [href]="file?.sm_url ? file.sm_url : file.file_url" target="_blank" class="link fw-500" [title]="file.name">{{ file.name }}</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="tableV2">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
            <tr>
                <th>{{ inductionRequestData.additional_data.project.custom_field.induction_phrase_singlr || 'Induction' }} Details</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngIf="inductionRequestData.user_sign">
                <td class="pr-0">
                    <div class="row mx-0 py-2">
                        <div class="col-12 px-0">
                            <span>Signature: </span><br>
                            <span class="fw-500">
                                    <img class="" [src]="inductionRequestData.user_sign" alt="user_sign">
                                </span>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td class="pr-0">
                    <div class="row mx-0 py-2">
                        <div class="col-12 px-0">
                            <span>Submitted: </span>
                            <br><span class="fw-500"> {{ displayDate(inductionRequestData.createdAt, AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) }}</span>
                        </div>
                    </div>
                </td>
            </tr>
            <tr *ngIf="inductionRequestData.inductor_ref">
                <td class="pr-0">
                    <div class="row mx-0 py-2">
                        <div class="col-12 px-0">
                            <span>Approved by:</span> <br/>
                            <span class="fw-500"> {{ inductionRequestData.inductor_ref.first_name }} {{ inductionRequestData.inductor_ref.last_name }} </span>
                        </div>
                    </div>
                </td>
            </tr>
            <tr *ngIf="inductionRequestData.inductor_ref && inductionRequestData.additional_data.accepted_at">
                <td class="pr-0">
                    <div class="row mx-0 py-2">
                        <div class="col-12 px-0">
                            <span>Approved:</span> <br/>
                            <span class="fw-500"> {{ displayDate(+inductionRequestData.additional_data.accepted_at, AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) }} </span>
                        </div>
                    </div>
                </td>
            </tr>
            <tr *ngIf="inductionRequestData.induction_booking">
                <td class="pr-0">
                    <div class="row mx-0 py-2">
                        <div class="col-12 px-0">
                            <span>Induction booking: </span>
                            <br><span class="fw-500">{{ inductionRequestData.induction_booking }}</span>
                        </div>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</ng-template>

<ng-template #changeLog>
    <ng-container *ngTemplateOutlet="commonInfo"></ng-container>
    <!-- <div class="tableV2 mb-4">
        <table class="w-100 mb-0" style="table-layout: fixed;">
            <thead>
                <tr>
                    <th colspan="6">Change Log</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="fw-500">Date</td>
                    <td class="fw-500">Category</td>
                    <td class="fw-500">Details</td>
                    <td class="fw-500">Changed from</td>
                    <td class="fw-500">Changed to</td>
                    <td class="fw-500">Changed by</td>
                </tr>
                <tr>
                    <td>DD/MM/YYYY</td>
                    <td>Competencies</td>
                    <td>CSCS</td>
                    <td>Pending</td>
                    <td>123</td>
                    <td>Himanshu</td>
                </tr>
                <tr>
                    <td>DD/MM/YYYY</td>
                    <td>Competencies</td>
                    <td>CSCS (Doc. number)</td>
                    <td>123456789</td>
                    <td>Expired</td>
                    <td>ABC123</td>
                </tr>
                <tr>
                    <td>DD/MM/YYYY</td>
                    <td>Induction status</td>
                    <td>lorem ipsum this text is an example of a users comments that show when they request a change to
                        an induction. </td>
                    <td>Verified</td>
                    <td>Change request</td>
                    <td>Aron Clare</td>
                </tr>
            </tbody>
        </table>
    </div> -->
    <project-comments
            [projectComments]="inductionComments"
            [currentUser]="authUser$"
            [tableViewCommentsTitle]="'Change Log'"
            [isFromChangeLog]="true"
            [isPanelOpen]="true"
            [isCollapsible]="false"
            [containerClass]="'large-font'">
    </project-comments>
</ng-template>

<i-modal #viewImg [title]="carouselState.showCarousel ? carouselState.currentImageName : userDocInfo?.title" size="lg" (onClickRightPB)="null"
         [showCancel]="false" [showFooter]="false" modalBodyClass="p-0" (onCancel)="onClickCrossBtn($event)">
    <div *ngIf="!carouselState.showCarousel" class="w-100 h-100 d-flex align-items-center justify-content-center">
        <img class="w-100 px-5" style="object-fit: contain; max-height: 80vh;" [src]="userDocInfo?.imgUrl" alt="img">
    </div>
    <div class="col-sm-12 text-center pl-0 pr-0 custom_carousel_style" *ngIf="carouselState.showCarousel && carouselState.userDocFiles?.length > 0">
        <ngb-carousel (slide)="slideChange($event)" [wrap]="true" [interval]="false">
            <ng-template ngbSlide *ngFor="let img of carouselState.userDocFiles; let i = index" [id]="'slide-' + i">
                <img [src]="img" class="img-cover" alt="Slide {{ i + 1 }}">
            </ng-template>
        </ngb-carousel>
    </div>
</i-modal>

import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import {ProjectService, Comment, User, AuthService, Project, ExtendedCompanyInfo, FeatureExclusionUtility, TimeUtility, InductionRequest} from "@app/core";
import { AssetsUrl, IModalComponent } from "@app/shared";
import { AppConstant } from "@env/environment";
import * as dayjs from "dayjs";
import { take } from "rxjs/operators";
import { CCLStatusTileConfig } from "@app/modules/common";
interface modalSidebarOptions {
	key: string;
	label: string;
	is_active: boolean;
	has_warning: boolean;
	sectionHide: boolean;
}
interface CarouselState {
    userDocFiles: string[];
    showCarousel: boolean;
    imageNameMap: string[];
    currentImageName: string;
}
@Component({
	selector: "view-induction",
	styleUrls: ["./view-induction.component.scss"],
	templateUrl: "./view-induction.component.html",
})
export class ViewInductionComponent implements OnInit {

	@Input()
	inductionDetailsRow: InductionRequest;

	@Input()
	inductionRequestData: any;

	@Input()
	verifiableCompetencies: string[] = [];

	@Input()
	projectId: number;

	modalSidebarOptions: modalSidebarOptions[];
	activeSidebarOption = { key: 'personalDetails', label: 'Personal Details', is_active: true, has_warning: false };
	readonly AppConstant = AppConstant;
	receivedBriefings: any;
	userDocInfo: any;
	readonly inductionSections = {
		personalDetails: "personalDetails",
		employmentDetails: "employmentDetails",
		safetyAssessment: "safetyAssessment",
		healthMedical: "healthMedical",
		additionalQuestions: "additionalQuestions",
		rightToWork: "rightToWork",
		competenciesCerts: "competenciesCerts",
		conductCards: "conductCards",
		briefings: "briefings",
		inductionSummary: "inductionSummary",
		changeLog: "changeLog"
	}
	authUser$: User;
	inductionComments: Array<Comment> = [];
	projectResolverResponse: any = {};
	projectInfo: Project = new Project();
	projectContractor: ExtendedCompanyInfo = {};
	isAdditionalQuestionsHide = false;
	activeTab = 'rams'; // Default active tab

	@ViewChild('viewImg') private viewImg: IModalComponent;

	showProfileMedicalAssessment: boolean = true;
	showProfileHealthAssessment: boolean = true;
	showSiteHealthAssessment: boolean = true;
	today: dayjs.Dayjs;
	isShadowUser: boolean = false;
	showEmploymentStartDate: boolean = true;
	showTypeOfEmployment: boolean = true;
	showMinWage: boolean = true;
	showEmpNbr: boolean = false;
	showNIN: boolean = false;
	hasHealthOrMedicalIssues: boolean = false;
	hasExpiredDocs: boolean = false;
	hasNegativeCards: boolean = false;
	hasRightToWork: boolean = false;
	alt_img_link: string = AssetsUrl.userSidebarIcons.profilePicPlaceholder;
	profileImageUrl: string | null = null;
	canShowUnder18Badge: boolean = false;

	carouselState: CarouselState = {
		userDocFiles: [],
		showCarousel: false,
		imageNameMap: [],
		currentImageName: ''
	};

	cclStatusConfig: CCLStatusTileConfig;

	private get timezone(): string {
		return this.projectInfo?.custom_field?.timezone;
	}

	constructor(
		private projectService: ProjectService,
		private authService: AuthService,
		private route: ActivatedRoute,
		private featureExclusionUtility: FeatureExclusionUtility,
		private timeUtility: TimeUtility,
	) {}

	ngOnInit(): void {
		this.initializeData();
		this.setupModalSidebarOptions();
		this.setupAuthUser();
	}

	private initializeData(): void {
		this.setProfileImageUrl();
		// Initialize project data from resolver
		this.projectResolverResponse = this.route.snapshot.data.projectResolverResponse;
		this.projectInfo = this.projectResolverResponse.project;
		this.projectContractor = this.projectResolverResponse.contractor;

		// Set current date for time calculations
		this.today = dayjs();

		// Early return if induction data is missing
		if (!this.inductionRequestData) {
			console.warn('Induction request data is missing');
			return;
		}

		// Destructure and process induction data
		const { additional_data, conduct_cards, induction_question_answers, induction_slot, rtw_check_result } = this.inductionRequestData;
		
		// Process user information
		this.isShadowUser = this.authService.isShadowUser(additional_data?.user_info);
		
		// Process questions and comments
		this.isAdditionalQuestionsHide = !induction_question_answers?.additional_qa?.length;
		this.inductionComments = this.inductionRequestData?.comments?.length ?
			[...this.inductionRequestData.comments].reverse() : [];

		const user_docs = (additional_data?.user_docs || []).map(d => {
			d._citb_enabled_doc = this.verifiableCompetencies.includes(d.name);
			return d;
		});
		this.inductionRequestData.additional_data = {
			...this.inductionRequestData.additional_data || {},
			user_docs,
		};

		// Format induction booking information
		this.formatInductionBooking(induction_slot);
		
		// Process health assessment data
		if (additional_data?.health_assessment_answers) {
			this.inductionRequestData.health_assessment_answers = this.transformData(
				additional_data.health_assessment_answers
			);
		}

		// Initialize feature flags and warning indicators
		this.initializeFeatureFlags();
		this.initializeWarningFlags(conduct_cards, additional_data, rtw_check_result);

        this.canShowUnder18Badge = (additional_data.user_info.dob) ? dayjs().diff(additional_data.user_info.dob, 'year') < 18 : false;
        // Set up the CCL status configuration
		this.setupCclStatusConfig();
	}

	private setupCclStatusConfig(): void {
		// Create the consolidated config object
		this.cclStatusConfig = {
			cclCheckData: this.inductionRequestData.ccl_check_data,
			parentCompany: this.projectInfo?.parent_company,
			contractorCclConfig: this.projectContractor.ccl_status || {
				enabled: false,
				is_excluded_project: false
			},
			inductionPhrase: this.projectInfo?.custom_field?.induction_phrase_singlr || '',
			authUser: this.authUser$,
			fromApproval: false
		};
	}

	private formatInductionBooking(inductionSlot: any): void {
		if (!inductionSlot) {
			this.inductionRequestData.induction_booking = '';
			return;
		}

		const timestamp = inductionSlot.seconds * 1000;
		const formattedDate = dayjs(timestamp).tz(this.timezone).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS);

		this.inductionRequestData.induction_booking = `${formattedDate} (${inductionSlot.location})`;
	}

	/**
	 * Initializes feature flags based on utility services and project configuration
	 * This determines which UI elements should be displayed to the user
	 */
	private initializeFeatureFlags(): void {
		// Initialize employment-related feature flags
		this.initializeEmploymentFlags();

		// Process site health assessment visibility based on user data
		this.processSiteHealthAssessmentVisibility();
		
		// Initialize health assessment feature flags
		this.initializeHealthFlags();
	}
	
	/**
	 * Initializes employment-related feature flags
	 */
	private initializeEmploymentFlags(): void {
		const countryCode = this.inductionRequestData?.additional_data?.user_info?.country_code;
		this.showEmploymentStartDate = this.featureExclusionUtility.showEmploymentStartDate();
		this.showTypeOfEmployment = this.featureExclusionUtility.showTypeOfEmployment();
		this.showMinWage = this.featureExclusionUtility.showMinWage();
		this.showEmpNbr = this.featureExclusionUtility.showEmpNbr(countryCode);
		this.showNIN = this.featureExclusionUtility.showNIN(countryCode);
	}
	
	/**
	 * Initializes health assessment feature flags
	 */
	private initializeHealthFlags(): void {
		this.showProfileMedicalAssessment = this.featureExclusionUtility.showProfileMedicalAssessment();
		this.showProfileHealthAssessment = this.featureExclusionUtility.showProfileHealthAssessment();
		this.showSiteHealthAssessment = this.projectContractor?.features_status?.induction_health_q || false;
	}
	
	/**
	 * Determines if site health assessment should be shown based on user's health data
	 */
	private processSiteHealthAssessmentVisibility(): void {
		// If no health issues are reported, ensure site health assessment is visible
		const hasNoReportedMedicalConditions = !this.inductionRequestData.reportable_medical_conditions;
		const isNotOnLongTermMedication = !this.inductionRequestData.on_long_medication;
		const hasNoSideEffects = !this.inductionRequestData.any_side_effects || 
			this.inductionRequestData.any_side_effects.toString().toLowerCase().trim() !== 'yes';
		
		if (hasNoReportedMedicalConditions && isNotOnLongTermMedication && hasNoSideEffects) {
			this.showSiteHealthAssessment = true;
		}
	}

	private initializeWarningFlags(conductCards: any[], additionalData: any, rtwCheckResult: any): void {
		// Check for negative conduct cards
		this.hasNegativeCards = Array.isArray(conductCards) && 
			conductCards.some(card => card?.card_detail?.card_type === 'Negative');

		// Check for expired documents
		this.hasExpiredDocs = Array.isArray(additionalData?.user_docs) && 
			additionalData.user_docs.some(doc => this.isDateExpired(doc?.expiry_date));

		// Check for health or medical issues
		this.hasHealthOrMedicalIssues = this.checkHealthAndMedicalIssues();

		// Check for right to work status
		this.hasRightToWork = rtwCheckResult?.status && rtwCheckResult?._data?.rtw_status === rtwCheckResult.status;
	}

	private checkHealthAndMedicalIssues(): boolean {
		const hasHealthIssues = this.inductionRequestData.health_assessment_answers?.some(category => 
			category.questions.some(q => q.answer === 'Yes')
		);

		const hasMedicalIssues = this.inductionRequestData.additional_data?.medical_assessments_answers?.some(q =>
			+q.answer === 1
		);

		return hasHealthIssues || hasMedicalIssues;
	}

	private setupModalSidebarOptions(): void {
		this.modalSidebarOptions = [
			{ key: 'personalDetails', label: 'Personal Details', is_active: true, has_warning: false, sectionHide: false },
			{ key: 'employmentDetails', label: 'Employment Details', is_active: false, has_warning: false, sectionHide: false },
			{ key: 'safetyAssessment', label: 'Safety Assessment', is_active: false, has_warning: false, sectionHide: false },
			{ key: 'healthMedical', label: 'Health & Medical', is_active: false, has_warning: this.hasHealthOrMedicalIssues, sectionHide: false },
			{ key: 'additionalQuestions', label: 'Additional Questions', is_active: false, has_warning: false, sectionHide: this.isAdditionalQuestionsHide },
			// { key: 'additionalQuestions', label: 'Additional Questions', is_active: false, has_warning: false, sectionHide: false },
			{ key: 'rightToWork', label: 'Right to Work', is_active: false, has_warning: false, sectionHide: !this.hasRightToWork },
			{ key: 'competenciesCerts', label: 'Competencies & Certs', is_active: false, has_warning: this.hasExpiredDocs, sectionHide: false },
			{ key: 'conductCards', label: 'Conduct Cards', is_active: false, has_warning: this.hasNegativeCards, sectionHide: false },
			{ key: 'briefings', label: 'Briefings', is_active: false, has_warning: false, sectionHide: false },
			{ key: 'inductionSummary', label: 'Induction Summary', is_active: false, has_warning: false, sectionHide: false },
			{ key: 'changeLog', label: 'Change Log', is_active: false, has_warning: false, sectionHide: false },
		];
	}

	private setupAuthUser(): void {
		this.authService.authUser.subscribe(data => {
			if (data?.id) {
				this.authUser$ = data;
			}
		});
	}

	onSelection(optionSelected) {
		this.activeSidebarOption = optionSelected;
		this.modalSidebarOptions = this.modalSidebarOptions.map(option => ({
			...option,
			is_active: option.key === optionSelected.key,
		}));
		if (optionSelected.key === this.inductionSections.briefings) {
			this.getProjectReceivedBriefing(this.inductionRequestData);
		}
	}

	setActiveTab(tab: string): void {
		this.activeTab = tab;
	}

	displayDate(d, format = AppConstant.defaultDateFormat) {	
		return dayjs(d).tz(this.timezone).format(format);
		// return dayjs(d).utc(true).tz(this.timezone).format(format);
	}

	travelTime(induction, returnType: string): string {
		let travelTimeOverride = this.timeUtility.getActiveTravelTime(induction, this.today);
		let travel_time = travelTimeOverride?.travel_time;
	
		// Return early if no travel time data is available
		if (!travel_time?.to_work && !travel_time?.to_home) {
			return '0 minutes';
		}
	
		const toWorkMinutes = this.parseDurationToMinutes(travel_time.to_work);
		const toHomeMinutes = this.parseDurationToMinutes(travel_time.to_home);
		const totalMinutes = this.timeUtility.getTotalTravelDuration(travel_time);
	
		let minutes: number;
	
		switch (returnType) {
			case 'to_work':
				minutes = toWorkMinutes;
				break;
			case 'to_home':
				minutes = toHomeMinutes;
				break;
			default:
				minutes = totalMinutes;
		}
		return `${minutes} minutes`;
	}	

	private parseDurationToMinutes(durationStr: string): number {
		if (!durationStr) return 0;
		
		try {
			// Handle duration format like "HH:MM" or "H:MM"
			if (typeof durationStr === 'string' && durationStr.includes(':')) {
				const [hours, minutes] = durationStr.split(':').map(part => parseInt(part, 10));
				return (hours * 60) + minutes;
			}
			
			// Try to parse as a duration using dayjs
			const minutes = dayjs.duration(durationStr).asMinutes();
			if (!isNaN(minutes)) {
				return minutes;
			}
			
			return 0;
		} catch {
			return 0;
		}
	}

	duration(time: number): string {
		const totalMonths = dayjs().tz(this.timezone).diff(dayjs(Number(time)).tz(this.timezone), "months");
		return `${Math.floor(totalMonths / 12)} years, ${totalMonths % 12} months`;
	}

	transformData(data: any[]) {
		const result = [];
		data.forEach(item => {
			const category = item.question_ref.category;
			const questionText = item.question_ref.question;
			const answerText = item.answer === '1' ? 'Yes' : 'No';

			// Find the category in result array
			let categoryData = result.find(cat => cat.category === category);

			// If category does not exist, create a new one
			if (!categoryData) {
				categoryData = { category, questions: [] };
				result.push(categoryData);
			}

			// Add question to the category
			categoryData.questions.push({
				question: questionText,
				answer: answerText
			});
		});
		return result;
	}

	isDateExpired(timestamp: string): boolean {
		if (!timestamp) return false;
		
		try {
			const parsedTimestamp = parseInt(timestamp, 10);
			if (isNaN(parsedTimestamp)) return false;
			
			return dayjs(parsedTimestamp).tz(this.timezone).isBefore(dayjs().tz(this.timezone));
		} catch {
			return false;
		}
	}

	async getProjectReceivedBriefing(row) {
		if (row.expanded) return;
		const userId = row?.user_ref;
		this.receivedBriefings = await this.projectService.getProjectReceivedBriefing(this.projectId, userId).pipe(take(1)).toPromise();
	}

	openImage(row: any) {
		const result = row?.user_files.flatMap(item => {
			const isPdf = item.file_mime === 'application/pdf';
			const urls = isPdf ? item.img_translation : [item.file_url];
			return urls.map(url => ({ url, name: item.name }));
		});
		
		this.carouselState.userDocFiles = result.map(r => r.url);
		this.carouselState.imageNameMap = result.map(r => r.name);
		this.carouselState.currentImageName = this.carouselState.imageNameMap[0];
		this.carouselState.showCarousel = true;
		this.viewImg.open();
	}

	openProfileImage() {
		this.carouselState.showCarousel = false;
		this.userDocInfo = {
			imgUrl: this.inductionRequestData?.additional_data?.user_info?.profile_pic_ref?.file_url,
			title: this.inductionRequestData?.creator_name,
		}
		if (this.userDocInfo.imgUrl) {
			this.viewImg.open();	
		}
	}

	/**
	 * Formats a date to DD-MM-YYYY format
	 * @param date The date to format
	 * @returns Formatted date or empty string
	 */
	correctDateFormat(date: string): string {
		if (!date) return '';
		
		const parsedDate = dayjs(date);
		if (parsedDate.isValid()) {
			return parsedDate.format(this.AppConstant.defaultDateFormat);
		}
		
		// Return original if parsing fails
		return date;
	}

	slideChange({ prev, current, direction }) {
		const newIndex = Number(current.split("-").pop());
  		this.carouselState.currentImageName = this.carouselState.imageNameMap[newIndex];
	}

	onClickCrossBtn(event) {
		this.carouselState.showCarousel = false;
	}

	private setProfileImageUrl(): void {
		const picRef = this.inductionRequestData?.additional_data?.user_info?.profile_pic_ref;
		this.profileImageUrl = picRef?.sm_url || picRef?.file_url || null;
	}
	
	onImageError(event: Event): void {
		const img = event.target as HTMLImageElement;
		img.onerror = null; // Prevent infinite loop
		img.src = '/assets/images/avatar-dummy-img.jpeg';
	}
}

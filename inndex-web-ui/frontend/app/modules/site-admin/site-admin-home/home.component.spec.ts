import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HomeComponent } from './home.component';

describe('HomeComponent', () => {
    let component: HomeComponent;
    let fixture: ComponentFixture<HomeComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [HomeComponent]
        }).compileComponents();

        fixture = TestBed.createComponent(HomeComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    it('should have has_add_project_access as false by default', () => {
        expect(component.has_add_project_access).toBeFalsy();
    });

    it('should set has_add_project_access to true when hasAddProjectAccess(true) is called', () => {
        component.hasAddProjectAccess(true);
        expect(component.has_add_project_access).toBeTruthy();
    });

    it('should set has_add_project_access to false when hasAddProjectAccess(false) is called', () => {
        component.hasAddProjectAccess(false);
        expect(component.has_add_project_access).toBeFalsy();
    });
});

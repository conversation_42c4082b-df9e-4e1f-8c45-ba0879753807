<div class="">
    <div class="col-12 mt-5">
        <ngx-skeleton-loader *ngIf="isTableDataLoading" count="8" [theme]="{ 'border-radius': '0', height: '50px' }">
        </ngx-skeleton-loader>
    </div>
    <div class="d-flex flex-column flex-sm-row flex-wrap justify-content-between">
        <div class="col-12 col-md-8 p-0 w-auto">
            <search-with-filters (searchEmitter)="searchFunction($event)"></search-with-filters>
        </div>
        <div class="float-md-right d-sm-block d-flex" style="z-index: 999">
            <action-button
                [newFeatureTitle]="'Add New'"
                (onOpenAddNew)="openConfigureNewCardsModal()"
                [showActionDropdown]="false"
                >
            </action-button>
        </div>
    </div>

    <ng-container *ngIf="!isTableDataLoading">
        <div class="col-12">
            <div class="table-responsive-sm ngx-datatable-custom">
                <ngx-datatable class="bootstrap table table-hover table-sm"
                    [rows]="filteredAssignedConductCards"
                    [columns]="[
                        {name:'Ref No.', prop: 'id', sortable: false, headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'py-1 table-cell-text'},
                        {name:'Card Name', prop: 'card_conducts.card_name', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: typeColumn},
                        {name:'Card Type', prop: 'card_type', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text'},
                        {name:'Action Taken', prop: 'card_conducts.card_action', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: cardAction},
                        {name:'Action', prop: 'id', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: actionColumn}
                    ]"
                    [columnMode]="'force'"
                    [footerHeight]="36"
                    [rowHeight]="'auto'"
                    [externalPaging]="true"
                    [count]="page.totalElements"
                    [offset]="page.pageNumber"
                    [limit]="page.size"
                    (page)="pageCallback($event)"
                >
                    <ng-template #typeColumn let-row="row" let-value="value">
                        <div class="flex-center">
                            <div class="color-circle" [style.background-color]="row?.card_color"></div>
                            {{ row?.card_name }}
                        </div>
                    </ng-template>
                    <ng-template #cardAction let-row="row" let-value="value">
                        {{ row.card_action }}
                    </ng-template>
                    <ng-template #actionColumn let-row="row" let-value="value">
                        <div style="line-height: 41px;">
                            <button title="Preview Card Info" type="button" class="btn btn-sm align-middle btn-action mr-1 mb-1 p-1" href="javascript:void(0)" (click)="openConfigureNewCardsModal(row)">
                                <svg class="img-icon px-1" style="width: 25px;"><use [attr.xlink:href]="assetsUrl.assetConfigIcons.editAssetConfig"></use></svg>
                            </button>
                            <button title="Delete" type="button" class="btn btn-sm align-middle btn-action mr-1 mb-1 p-1" (click)="deleteConductCard(row.id)">
                                <i class="d-flex align-items-center justify-content-center">
                                <span class="material-symbols-outlined large-font fw-500 cursor-pointer">delete</span>
                                </i>
                            </button>
                        </div>
                    </ng-template>
                </ngx-datatable>
            </div>
        </div>
    </ng-container>
    <block-loader [show]="isTableDataLoading || isApiDataLoading" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>

    <i-modal #configureNewCardsModal size="md" title="Configure New Card" [showCancel]="false" rightPrimaryBtnTxt="Save"
        (onCancel)="resetConfigureNewCardModal()" [rightPrimaryBtnDisabled]="!isFormValid()" (onClickRightPB)="onSubmit($event)">
        <div class="px-4 py-3">
            <configure-card *ngIf="showConfigureNewCardsModal"
                [showExpiry]="true"
                [isConfigureNewCard]="true"
                [cardDetails]="selectedConductCardRow"
                (onFormValueChange)="onFormValueChange($event)">
            </configure-card>
        </div>
    </i-modal>
</div>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
.search-input {
    margin-left: unset !important;
}

.heading {
    font-size: 14px;
    font-weight: 500;
}

.table-cell-text {
    font-size: 14px;
    font-weight: 400;
}

/* Color dot css */
.dot {
    height: 8px;
    width: 8px;
}

.color-circle {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 10px;
}

/* up-down arrow css */
.arrow-small {
    height: 10px;
    width: 10px;
}
/* Drop-down css */
.main-container {
    border: 0.5px solid #9D9D9D;
}

.main-drop {
    height: 40px;
}

/* accordion css */
.accordion {
    width: 100%;
}

.accordion button {
    box-shadow: none;
    display: flex;
    align-items: center;
    width: 100%;
    text-align: left;
    border: 0;
    border-radius: 0;
    overflow-anchor: none;
}

.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-action {
    width: 32px;
    height: 32px;
}

.cell-bottom-text{
    font-size: 10px;
    color: gray;
}

import { Component, OnInit, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { User, Common, CreateEmployer, ConductCardsService, ToastService, AuthService, AssignedConductCards } from '@app/core';
import { IModalComponent, GenericConfirmationModalComponent, AssetsUrl } from '@app/shared';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import * as dayjs from 'dayjs';

@Component({
  selector: 'app-configure-conduct-cards',
  templateUrl: './configure-conduct-cards.component.html',
  styleUrls: ['./configure-conduct-cards.component.scss']
})
export class ConfigureConductCardsComponent implements OnInit {
    @ViewChild('configureNewCardsModal') private configureNewCardsModalRef: IModalComponent;
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    authUser$: User;
    isTableDataLoading: boolean = false;
    isApiDataLoading: boolean = false;

    paginationData = new Common();
    page = this.paginationData.page;
    tableOffset: number = 0;
    conductCardsSearch: string;
    AssetsUrlSiteAdmin = AssetsUrl.siteAdmin;

    companyResolverResponse: any;
    employer: CreateEmployer = {};
    conductCardParams: any;
    assignedConductCards: AssignedConductCards[] = [];
    cardType: any;
    selectedConductCardRow : AssignedConductCards;
    showConfigureNewCardsModal: boolean = false;
    filteredAssignedConductCards: AssignedConductCards[] = [];
    assetsUrl = AssetsUrl;

    constructor(
        private modalService: NgbModal,
        private conductCardsService: ConductCardsService,
        private toastService: ToastService,
        private activatedRoute: ActivatedRoute,
        private authService: AuthService,
    ) { }

    ngOnInit() {
        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
            }
        });
        this.cardType = this.conductCardsService.CARDTYPE;
        this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        this.employer = this.companyResolverResponse.company;
        this.getConductCards();
    }

    pageCallback(pageInfo: {count?: number, pageSize?: number, limit?: number, offset?: number }) {
        this.page.pageNumber = pageInfo.offset;
        this.getConductCards();
    }

    getConductCards() {
        this.isTableDataLoading = true;
        this.conductCardsService.getConductCards(this.employer.id).subscribe({
            next: (res: any) => {
            this.assignedConductCards = res.card_conducts || [];
            this.applySearchFilter();
            this.page.totalElements = this.filteredAssignedConductCards.length;
            },
            error: (err: any) => {
            this.isTableDataLoading = false;
            const message = err.message || 'Failed to fetch conduct cards.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: err });
            },
            complete: () => {
            this.isTableDataLoading = false;
            }
        });
    }

    createConductCard(params: any) {
        this.isApiDataLoading = true;
        this.conductCardsService.createConductCard(this.employer.id, params).subscribe({
            next: (res: any) => {
                this.getConductCards();
            },
            error: (err: any) => {
                this.isApiDataLoading = false;
                const message = err.message || 'Failed to create conduct card.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: err });
            },
            complete: () => {
                this.isApiDataLoading = false;
            }
        });
    }

    updateConductCard(req: any) {
        this.isApiDataLoading = true;
        this.conductCardsService.updateConductCard(this.employer.id, req.id, req).subscribe({
            next: (res: any) => {
                this.getConductCards();
            },
            error: (err: any) => {
                this.isApiDataLoading = false;
                const message = err.message || 'Failed to create conduct card.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: err });
            },
            complete: () => {
                this.isApiDataLoading = false;
            }
        });
    }

    deleteConductCard(id: number) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Are you sure you want to delete this conduct card?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.isApiDataLoading = true;
                this.conductCardsService.deleteConductCards(this.employer.id, id).subscribe({
                    next: (res: any) => {
                        if (res && res.success) {
                            this.getConductCards();
                        }
                    },
                    error: (err) => {
                        this.isApiDataLoading = false;
                        const message = err.message || 'Failed to delete conduct card.';
                        this.toastService.show(this.toastService.types.ERROR, message);
                    },
                    complete: () => {
                        this.isApiDataLoading = false;
                    },
                });
            }
        });
    }

    openConfigureNewCardsModal(row?: any) {
        this.selectedConductCardRow = row
        this.modalService.dismissAll();
        this.showConfigureNewCardsModal = true;
        this.configureNewCardsModalRef.open();
    }

    closeViewCardDetailsModal(event) {
        event.closeFn();
    }

    onFormValueChange(ngForm: NgForm) {
        const formValue = ngForm.value;
        const { id: creator_ref } = this.authUser$;
        const { id: company_ref } = this.employer;
        const { card_action, card_color, card_name, card_type, indefinite } = formValue;
        const { months, days } = formValue;
        const expire_in = (!indefinite || formValue.card_type === this.cardType.NEGATIVE) ? { month: months, day: days } : {};
        this.conductCardParams = {
          creator_ref,
          company_ref,
          card_action,
          card_color,
          card_name,
          card_type,
          indefinite: indefinite ?? false,
          expire_in,
        };
    }

    isFormValid(): boolean {
        const params = this.conductCardParams;
        const requiredFieldsPresent = !!(params?.card_action && params?.card_color && params?.card_name && params?.card_type);
        const expirationFieldsPresent = !!(params?.indefinite || ((params?.expire_in?.month === 0 || params?.expire_in?.month) && (params?.expire_in?.day === 0 || params?.expire_in?.day)));
        if(params?.card_type === this.cardType.NEGATIVE) {
            return requiredFieldsPresent && expirationFieldsPresent;
        } else {
            return requiredFieldsPresent;
        }
    }

    onSubmit(event) {
        if(this.selectedConductCardRow){
            this.updateConductCard({...this.conductCardParams, id: this.selectedConductCardRow.id})
        } else {  
            this.createConductCard(this.conductCardParams);
        }
        this.resetConfigureNewCardModal();
        event.closeFn();
    }

    resetConfigureNewCardModal() {
        this.showConfigureNewCardsModal = false;
    }

    onSelectionSubmit(event) {
        event.closeFn();
    }

    dayjs(n: number, format?: any) {
        return dayjs(n, format);
    };

    searchFunction(data: { search: string }) {
        this.conductCardsSearch = data.search.toLowerCase().trim();
        this.page.pageNumber = 0;
        this.applySearchFilter();
    }
    applySearchFilter() {
        if (!this.conductCardsSearch) {
            this.filteredAssignedConductCards = [...this.assignedConductCards];
        } else {
            const term = this.conductCardsSearch;
            this.filteredAssignedConductCards = this.assignedConductCards.filter(card => {
            return (
                card?.card_name?.toLowerCase().includes(term) ||
                card?.card_action?.toLowerCase().includes(term) ||
                card?.card_type?.toLowerCase().includes(term) ||
                String(card?.id).includes(term)
            );
            });
        }

        this.page.totalElements = this.filteredAssignedConductCards.length;
    }

}

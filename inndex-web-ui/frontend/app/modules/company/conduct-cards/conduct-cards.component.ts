import {Component, ElementRef, OnInit, TemplateRef, ViewChild} from "@angular/core";
import { NgForm } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { AuthService, Common, ConductCardActionButtons, CreateEmployer, HttpService, isInheritedProjectOfCompany, Project, ToastService, User, ConductCard } from "@app/core";
import { NgbMomentjsAdapter } from "@app/core/ngb-moment-adapter";
import { ConductCardsService } from "@app/core/services/conduct-cards.service";
import { AssetsUrl, GenericConfirmationModalComponent, IModalComponent } from "@app/shared";
import { AppConstant } from "@env/environment";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import * as dayjs from "dayjs";
import {HttpParams} from "@angular/common/http";
import {fromEvent} from "rxjs";
import {debounceTime, distinctUntilChanged, filter, tap} from "rxjs/operators";
@Component({
    selector: "conduct-cards",
    templateUrl: "./conduct-cards.component.html",
    styleUrls: ["./conduct-cards.component.scss"]
})

export class ConductCardsComponent implements OnInit {

    @ViewChild('configureCardsModal') private configureCardsModalRef: IModalComponent;
    @ViewChild('configureNewCardsModal') private configureNewCardsModalRef: IModalComponent;
    @ViewChild('viewCardDetailsModal') private viewCardDetailsModalRef: IModalComponent;
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    authUser$: User;
    isTableDataLoading: boolean = false;
    isApiDataLoading: boolean = false;

    //table var
    paginationData = new Common();
    page = this.paginationData.page;
    tableOffset: number = 0;
    conductCardsSearch: string;
    AssetsUrlSiteAdmin = AssetsUrl.siteAdmin;
    negativeCardToggle: Array<any> = [];
    positiveCardToggle: Array<any> = [];

    conductCardsList: Array<any> = [];
    filteredConductCardsList: Array<any> = [];
    companyResolverResponse: any;
    employer: CreateEmployer = {};
    conductCardParams: any;
    positiveCards: Array<any> = [];
    negativeCards: Array<any> = [];
    displayDateFormat: string = AppConstant.dateFormat_D_MM_YYYY;
    displayTimeFormat: string = AppConstant.defaultTimeFormat;
    assignedConductCards: Array<any> = [];
    cardType: any;
    cardInfo: any = {};
    selectedCardRow: ConductCard;
    showConfigureNewCardsModal: boolean = false;
    is_inherited_project: boolean = false;
    projectInfo: Project = new Project;
    isProjectPortal: boolean = false;
    isMobileDevice: boolean = false;
    constructor(
        private modalService: NgbModal,
        private conductCardsService: ConductCardsService,
        private toastService: ToastService,
        private activatedRoute: ActivatedRoute,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private authService: AuthService,
        private httpService: HttpService,
    ) {
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
     }

    ngOnInit() {
        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
            }
        });
        this.cardType = this.conductCardsService.CARDTYPE;
        this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        this.employer = this.companyResolverResponse.company;
        this.getConductCards();
        this.getAssignedCardUsersList();
    }

    public onActionSelection() {
        this.downloadAssignedConductCards();
    }
    projectRetrieved(project: Project) {
        this.projectInfo = project;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
    }

    downloadAssignedConductCards() {
        this.isApiDataLoading = true;
        this.conductCardsService.downloadAssignedConductCard(this.employer.id, () => {
            this.isApiDataLoading = false;
        });
    }

    pageCallback(pageInfo: {count?: number, pageSize?: number, limit?: number, offset?: number }) {
        this.page.pageNumber = pageInfo.offset;
        this.getAssignedCardUsersList();
    }

    getAssignedCardUsersList() {
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`);

        if(this.conductCardsSearch) {
            params = params.append('search',`${this.conductCardsSearch}`);
        }
        this.isTableDataLoading = true;
        this.conductCardsService.getAssignedCardUsersList(this.employer.id, params).subscribe({
            next: (res: any) => {
                this.assignedConductCards = res.records;
                this.page.totalElements = res.totalCount;
            },
            error: (err: any) => {
                this.isTableDataLoading = false;
                const message = err.message || 'Failed to fetch conduct cards.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: err });
            },
            complete: () => {
                this.isTableDataLoading = false;
            }
        });
    }

    unassignCardUsersList(row: any, event) {
        const id = row.conduct_card_ref;
        const req = {
            user_ref : row.user_ref.id,
            assigned_id : row.id,
        }
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete Conduct Card',
            title: `Are you sure you want to delete this assigned conduct card?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.isApiDataLoading = true;
                this.conductCardsService.unassignCardUsersList(this.employer.id, id, req).subscribe({
                    next: (res) => {
                        if (res?.success) {
                            this.getAssignedCardUsersList();
                        }
                    },
                    error: (err) => {
                        this.isApiDataLoading = false;
                        const message = err.message || 'Failed to delete conduct card.';
                        this.toastService.show(this.toastService.types.ERROR, message);
                    },
                    complete: () => {
                        this.isApiDataLoading = false;
                        event.closeFn();
                    },
                });
            },
        });
    }

    getConductCards() {
        this.isTableDataLoading = true;
        this.conductCardsService.getConductCards(this.employer.id).subscribe({
            next: (res: any) => {
                this.conductCardsList = res.card_conducts || [];
                this.filteredConductCardsList = res.card_conducts || [];
                this.separateCardList();
                // console.log("conduct card list:", this.conductCardsList);
            },
            error: (err: any) => {
                this.isTableDataLoading = false;
                const message = err.message || 'Failed to fetch conduct cards.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: err });
            },
            complete: () => {
                this.isTableDataLoading = false;
            }
        });
    }

    separateCardList() {
        this.positiveCards = [];
        this.negativeCards = [];
        this.conductCardsList.forEach(element => {
            if(element.card_type === this.cardType.NEGATIVE) {
                this.negativeCards.push(element);
            } else {
                this.positiveCards.push(element);
            }
        });
        this.positiveCards.sort((a, b) => b.updatedAt - a.updatedAt);
        this.negativeCards.sort((a, b) => b.updatedAt - a.updatedAt);
    }

    createConductCard(params: any) {
        this.isApiDataLoading = true;
        this.conductCardsService.createConductCard(this.employer.id, params).subscribe({
            next: (res: any) => {
                this.getConductCards();
            },
            error: (err: any) => {
                this.isApiDataLoading = false;
                const message = err.message || 'Failed to create conduct card.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: err });
            },
            complete: () => {
                this.isApiDataLoading = false;
            }
        });
    }

    updateConductCard(req: any) {
        this.isApiDataLoading = true;
        this.conductCardsService.updateConductCard(this.employer.id, req.id, req.params).subscribe({
            next: (res: any) => {
                this.getConductCards();
            },
            error: (err: any) => {
                this.isApiDataLoading = false;
                const message = err.message || 'Failed to create conduct card.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: err });
            },
            complete: () => {
                this.isApiDataLoading = false;
            }
        });
    }

    deleteConductCard(id: number) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Are you sure you want to delete this conduct card?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.isApiDataLoading = true;
                this.conductCardsService.deleteConductCards(this.employer.id, id).subscribe({
                    next: (res: any) => {
                        if (res && res.success) {
                            this.getConductCards();
                        }
                    },
                    error: (err) => {
                        this.isApiDataLoading = false;
                        const message = err.message || 'Failed to delete conduct card.';
                        this.toastService.show(this.toastService.types.ERROR, message);
                    },
                    complete: () => {
                        this.isApiDataLoading = false;
                    },
                });
            }
        });
    }

    openConfigureCardsModal() {
        this.positiveCardToggle = [];
        this.negativeCardToggle = [];
        this.configureCardsModalRef.open();
    }

    openConfigureNewCardsModal() {
        this.modalService.dismissAll();
        this.showConfigureNewCardsModal = true;
        this.configureNewCardsModalRef.open();
    }

    openViewCardDetailsModal(row: ConductCard) {
        this.selectedCardRow = row;
        this.modalService.dismissAll();
        const expire:any = (+row.expire_on) ? this.calculateExpiryInfo(+row.expire_on) : {};
        this.cardInfo = {
            id: row.id,
            issuedTo: (row.issued_to_employer && row.issued_to_employer.user_employer) ? `${row.user_ref.name} (${row.issued_to_employer.user_employer})` : row.user_ref.name,
            cardType: row.card_detail.card_type,
            action: row.card_detail.card_action,
            issuedBy: row.assigned_by_ref.name,
            projectOfIssue: row.project_ref.name,
            dateOfIssue: dayjs(+row.createdAt).format(AppConstant.defaultDateFormat),
            expiryDate: (expire.expiryDate) ? expire.expiryDate : null,
            expiredIn: (expire.expiryCount) ? expire.expiryCount : null,
            comment: row.comment,
            cardName: row.card_detail.card_name,
        };
        this.viewCardDetailsModalRef.open();
    }

    closeViewCardDetailsModal(event) {
        event.closeFn();
    }

    private calculateExpiryInfo(timestamp: number) {
        const expiryDate = dayjs(timestamp);
        const now = dayjs();

        // Calculate the total duration between now and the expiry date
        const duration = expiryDate.diff(now, 'day', true);
        if (duration <= 0) {
            return {
                expiryCount: '0 days',
                expiryDate: expiryDate.format(AppConstant.defaultDateFormat)
            };
        }

        // Calculate the remaining years, months, and days
        const years = expiryDate.diff(now, 'year');
        const months = expiryDate.subtract(years, 'year').diff(now, 'month');
        const days = expiryDate.subtract(years, 'year').subtract(months, 'month').diff(now, 'day');

        // Create a formatted string for the remaining time
        const parts = [];
        if (years) parts.push(`${years} year${years > 1 ? 's' : ''}`);
        if (months) parts.push(`${months} month${months > 1 ? 's' : ''}`);
        if (days) parts.push(`${days} day${days > 1 ? 's' : ''}`);
        const expiryCount = parts.join(', ');

        const data = {
          expiryCount: expiryCount,
          expiryDate: expiryDate.format(AppConstant.defaultDateFormat),
        };
        return data;
    }

    positiveCardsToggle(i) {
        const index = this.positiveCardToggle.indexOf(i);
        if (index !== -1) {
            this.positiveCardToggle.splice(index, 1);
        } else {
            this.positiveCardToggle.push(i);
        }
        console.log(this.positiveCardToggle);
    }

    negativeCardsToggle(i) {
        const index = this.negativeCardToggle.indexOf(i);
        if (index !== -1) {
            this.negativeCardToggle.splice(index, 1);
        } else {
            this.negativeCardToggle.push(i);
        }
        console.log(this.negativeCardToggle);
    }


    onFormValueChange(ngForm: NgForm) {
        const formValue = ngForm.value;
        const { id: creator_ref } = this.authUser$;
        const { id: company_ref } = this.employer;
        const { card_action, card_color, card_name, card_type, indefinite } = formValue;
        const { months, days } = formValue;
        const expire_in = (!indefinite || formValue.card_type === this.cardType.NEGATIVE) ? { month: months, day: days } : {};
        this.conductCardParams = {
          creator_ref,
          company_ref,
          card_action,
          card_color,
          card_name,
          card_type,
          indefinite: indefinite ?? false,
          expire_in,
        };
    }

    isFormValid(): boolean {
        const params = this.conductCardParams;
        const requiredFieldsPresent = !!(params?.card_action && params?.card_color && params?.card_name && params?.card_type);
        const expirationFieldsPresent = !!(params?.indefinite || ((params?.expire_in?.month === 0 || params?.expire_in?.month) && (params?.expire_in?.day === 0 || params?.expire_in?.day)));
        if(params?.card_type === this.cardType.NEGATIVE) {
            return requiredFieldsPresent && expirationFieldsPresent;
        } else {
            return requiredFieldsPresent;
        }
    }

    onSubmit(event) {
        this.createConductCard(this.conductCardParams);
        this.resetConfigureNewCardModal();
        event.closeFn();
        this.openConfigureCardsModal();
    }

    resetConfigureNewCardModal() {
        this.showConfigureNewCardsModal = false;
    }

    onSelectionSubmit(event) {
        event.closeFn();
    }

    dayjs(n: number, format?: any) {
        return dayjs(n, format);
    };

    searchFunction(data){
        this.conductCardsSearch = data.search.toLowerCase();
        this.page.pageNumber = 0;
        this.getAssignedCardUsersList();

    }
}

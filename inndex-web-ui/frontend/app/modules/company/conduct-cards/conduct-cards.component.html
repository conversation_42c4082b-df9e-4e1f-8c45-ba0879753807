<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav [employer]="employer" [companyResolverResponse]="companyResolverResponse" *ngIf="!isProjectPortal" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div
    [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}"
    >
        <div class="row">
            <project-header
            [isCompanyHeader]="!isProjectPortal"
            [projectData]="projectInfo"
            [parentCompany]="employer"
            class="col-sm-12 mt-3 nav-tabs"
            >
        </project-header>

            <div class="col-sm-12 my-3 outer-border">
                <div class="outer-border-radius">
                <div
                    class="d-flex justify-content-between flex-column flex-sm-row px-3 mb-2"
                    >
                        <h5 class="float-md-left">Conduct Cards <small>({{page.totalElements}})</small></h5>
                    <div
                        class="float-md-right d-sm-block d-flex" style="z-index: 999">
                        <button class="btn btn-sm btn-brandeis-blue other-action-btn justify-content-center d-flex align-items-center pointer-cursor" (click)="onActionSelection()" id="dropdownDlReport1">
                        <div class="medium-font m-font-size">Export</div>
                        </button>
                    </div>
                </div>
                <div class="px-3">
                    <search-with-filters
                    (searchEmitter)="searchFunction($event)"
                    ></search-with-filters>
                </div>
                <ng-container *ngIf="!isTableDataLoading">
                    <div class="col-12 my-2">
                        <div class="table-responsive-sm ngx-datatable-custom">
                        <ngx-datatable class="bootstrap table table-hover table-sm"
                            [virtualization]="false"
                            [rows]="assignedConductCards"
                            [columns]="[
                                {name:'Ref No.', prop: 'company_record_id', sortable: false, headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'py-1 table-cell-text'},
                                {name:'Date of Issue', prop: 'createdAt', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: createdAt},
                                {name:'Issued to', prop: 'user_ref', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: issuedTo},
                                {name:'Card Name', prop: 'card_detail.card_name', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: typeColumn},
                                {name:'Action', prop: 'card_action', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: cardAction},
                                {name:'Issued by', prop: 'assigned_by_ref', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: issuedBy},
                                {name:'Project', prop: 'project_name', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: projectName},
                                {name:'Action', prop: 'id', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: actionColumn},
                            ]"
                            [columnMode]="'force'"
                            [footerHeight]="36"
                            [rowHeight]="'auto'"
                            [externalPaging]="true"
                            [count]="page.totalElements"
                            [offset]="page.pageNumber"
                            [limit]="page.size"
                            (page)="pageCallback($event)"
                        >
                        <ng-template #typeColumn let-row="row" let-value="value">
                        <div class="flex-column">
                            <span class="flex-center">
                                <div class="color-circle" [style.background-color]="row?.card_detail?.card_color"></div>
                                {{ row?.card_detail?.card_name }}
                            </span>
                            <span class="cell-bottom-text" style="margin-left: 18px;">{{ row?.card_detail?.card_type}}</span>
                        </div>
                        </ng-template>
                        <ng-template #projectName let-row="row" let-value="value">
                        <div class="flex-column">
                            <span class=" flex-center">{{ row?.project_ref?.name }}</span>
                            <span class="cell-bottom-text">(ID: {{ row?.project_ref?.id }})</span>
                        </div>
                        </ng-template>
                        <ng-template #issuedTo let-row="row" let-value="value">
                            <div style="display: inline-grid;  line-height: 1.625;">
                                <span>{{ row?.user_ref?.name }}</span>
                                <span class="cell-bottom-text">(ID: {{ row?.user_ref?.id }})</span>
                            </div>
                        </ng-template>
                        <ng-template #createdAt let-row="row" let-value="value">
                            <div style="display: inline-grid;  line-height: 1.625;">
                                <span>{{ dayjs(+value).format(displayDateFormat) }}</span>
                                <span class="cell-bottom-text">({{ dayjs(+value).format(displayTimeFormat) }})</span>
                            </div>
                        </ng-template>
                        <ng-template #issuedBy let-row="row" let-value="value">
                            <div style="display: inline-grid;  line-height: 1.625;">
                                <span>{{ row?.assigned_by_ref?.name }}</span>
                                <span *ngIf="row.assigned_by_user_employer" class="cell-bottom-text">{{ '{' + row.assigned_by_user_employer + '}' }}</span>
                            </div>
                        </ng-template>
                        <ng-template #cardAction let-row="row" let-value="value" class="flex-column">
                            <div class="flex-column">
                                <span class="flex-center">{{ row?.card_detail?.card_action }}</span>
                                <span class="cell-bottom-text">
                                    <ng-container *ngIf="!row.card_detail?.indefinite && row?.card_detail?.expire_in?.month">
                                        ({{ row.card_detail.expire_in.month+' Months' }})
                                    </ng-container>
                                    <ng-container *ngIf="row?.card_detail?.indefinite">
                                        (indefinite)
                                    </ng-container>
                                </span>
                            </div>
                        </ng-template>
                        <ng-template #actionColumn let-row="row" let-value="value">
                        <div style="line-height: 41px">
                            <button title="Preview Card Info" type="button" class="btn btn-sm align-middle btn-action mr-1 mb-1 p-1"
                            href="javascript:void(0)" (click)="openViewCardDetailsModal(row)">
                            <img class="img-icon px-1" [src]="AssetsUrlSiteAdmin.search_svg" alt="">
                            </button>
                        </div>
                        </ng-template>
                        </ngx-datatable>
                        </div>
                    </div>
                </ng-container>
                <block-loader [show]="isTableDataLoading || isApiDataLoading" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
            </div>
            </div>
        </div>
    </div>


    <!-- Configure Cards Modal -->
    <i-modal #configureCardsModal size="md" title="Configure Cards" [showCancel]="false" rightPrimaryBtnTxt="Done" (onClickRightPB)="onSelectionSubmit($event)">
            <div class="d-flex flex-column" *ngIf="positiveCards.length">
                <div class="heading">Positive Cards</div>
                <div *ngFor="let item of positiveCards; let i=index" class="d-flex my-2">
                    <ngb-accordion #acc="ngbAccordion">
                        <ngb-panel id="toggle-1">
                            <ng-template ngbPanelHeader>
                                <button ngbPanelToggle class="btn p-0" (click)="positiveCardsToggle(i)">
                                    <div class="d-flex align-items-center cursor-pointer main-drop w-100">
                                        <div class="">
                                            <span class="d-inline-block rounded-circle mr-2 dot" [ngStyle]="{'background-color': item.card_color}"></span>
                                            <span class="heading">{{ item.card_name }}</span>
                                        </div>
                                        <div class="ml-auto">
                                            <ng-container *ngIf="positiveCardToggle.includes(i); else ArrowDown">
                                                <img class="arrow-small" [src]="AssetsUrlSiteAdmin.arrowUpFilledTriangle" alt="">
                                            </ng-container>
                                            <ng-template #ArrowDown>
                                                <img class="arrow-small" [src]="AssetsUrlSiteAdmin.arrowDownFilledTriangle" alt="">
                                            </ng-template>
                                        </div>
                                    </div>
                                </button>
                            </ng-template>
                            <ng-template ngbPanelContent>
                                <configure-card
                                [cardDetails]="item"
                                (updateConductCard)="updateConductCard($event)">
                                </configure-card>
                            </ng-template>
                        </ngb-panel>
                    </ngb-accordion>
                    <div class="ml-3 mt-2">
                        <span class="material-symbols-outlined cursor-pointer text-danger" aria-hidden="true" (click)="deleteConductCard(item.id)">
                            delete
                        </span>
                    </div>
                </div>
            </div>
            <div class="d-flex flex-column" *ngIf="negativeCards.length">
                <div class="heading mb-2">Negative Cards</div>
                <div *ngFor="let item of negativeCards; let i=index;" class="d-flex mb-2">
                    <ngb-accordion #acc="ngbAccordion">
                        <ngb-panel id="toggle-1">
                            <ng-template ngbPanelHeader>
                                <button ngbPanelToggle class="btn p-0" (click)="negativeCardsToggle(i)">
                                    <div class="d-flex align-items-center cursor-pointer main-drop w-100">
                                        <div class="">
                                            <span class="d-inline-block rounded-circle dot mr-2" [ngStyle]="{'background-color': item.card_color}"></span>
                                            <span class="heading">{{ item.card_name }}</span>
                                        </div>
                                        <div class="ml-auto">
                                            <div class="ml-auto">
                                                <ng-container *ngIf="negativeCardToggle.includes(i); else ArrowDown">
                                                    <img class="arrow-small" [src]="AssetsUrlSiteAdmin.arrowUpFilledTriangle" alt="">
                                                </ng-container>
                                                <ng-template #ArrowDown>
                                                    <img class="arrow-small" [src]="AssetsUrlSiteAdmin.arrowDownFilledTriangle" alt="">
                                                </ng-template>
                                            </div>
                                        </div>
                                    </div>
                                </button>
                            </ng-template>
                            <ng-template ngbPanelContent>
                                <configure-card
                                    [showExpiry]="true"
                                    [cardDetails]="item"
                                    (updateConductCard)="updateConductCard($event)">
                                </configure-card>
                            </ng-template>
                        </ngb-panel>
                    </ngb-accordion>
                    <div class="ml-3 mt-2">
                        <span class="material-symbols-outlined text-danger cursor-pointer" aria-hidden="true" (click)="deleteConductCard(item.id)">
                            delete
                        </span>
                    </div>
                </div>
            </div>
            <div class="w-100">
                <button type="button" class="btn btn-sm btn-outline-primary float-right"
                    (click)="openConfigureNewCardsModal()">
                    Add New Card
                </button>
            </div>
    </i-modal>

    <i-modal #configureNewCardsModal size="md" title="Configure New Card" [showCancel]="false" rightPrimaryBtnTxt="Save"
        (onCancel)="resetConfigureNewCardModal()" [rightPrimaryBtnDisabled]="!isFormValid()" (onClickRightPB)="onSubmit($event)">
        <div class="px-4 py-3">
            <configure-card *ngIf="showConfigureNewCardsModal"
                [showExpiry]="true"
                [isConfigureNewCard]="true"
                (onFormValueChange)="onFormValueChange($event)">
            </configure-card>
        </div>
    </i-modal>

    <i-modal #viewCardDetailsModal size="md" [showCancel]="false" [title]="cardInfo.id + '. ' + cardInfo.issuedTo"
        rightPrimaryBtnTxt="Done" (onClickRightPB)="closeViewCardDetailsModal($event)" leftSecondaryBtnTxt="Delete" [leftSecondaryBtnIcon]="{icon: 'delete', class: ''}" (onClickLeftSB)="unassignCardUsersList(selectedCardRow, $event)">
        <div class="px-4 py-3">
            <div class="w-100 p-2" style="opacity: 0.5;">
                <div class="form-group mb-2">
                    <label class="heading mb-0">Issued to</label>
                    <div class="input-group">
                        <input type="text" class="form-control heading bg-white" [(ngModel)]="cardInfo.issuedTo" readonly>
                    </div>
                </div>
                <div class="form-group mb-2">
                    <label class="heading mb-0">Card Name</label>
                    <div class="input-group">
                        <input type="text" class="form-control heading bg-white" [(ngModel)]="cardInfo.cardName" readonly>
                    </div>
                </div>
                <div class="form-group mb-2">
                    <label class="heading mb-0">Card Type</label>
                    <div class="input-group">
                        <input type="text" class="form-control heading bg-white" [(ngModel)]="cardInfo.cardType" readonly>
                    </div>
                </div>
                <div class="form-group mb-2">
                    <label class="heading mb-0">Action</label>
                    <div class="input-group">
                        <input type="text" class="form-control heading bg-white" [(ngModel)]="cardInfo.action" readonly>
                    </div>
                </div>
                <div class="form-group mb-2">
                    <label class="heading mb-0">Issued by</label>
                    <div class="input-group">
                        <input type="text" class="form-control heading bg-white" [(ngModel)]="cardInfo.issuedBy" readonly>
                    </div>
                </div>
                <div class="form-group mb-2">
                    <label class="heading mb-0">Project of Issue</label>
                    <div class="input-group">
                        <input type="text" class="form-control heading bg-white" [(ngModel)]="cardInfo.projectOfIssue" readonly>
                    </div>
                </div>
                <div class="form-group mb-2">
                    <label class="heading mb-0">Date of Issue</label>
                    <div class="input-group">
                        <input type="text" class="form-control heading bg-white" [(ngModel)]="cardInfo.dateOfIssue" readonly>
                    </div>
                </div>
                <div class="form-group mb-2" *ngIf="cardInfo.cardType == cardType.NEGATIVE">
                    <div class="row mx-0 custom-drop" *ngIf="(cardInfo.expiryDate != null && cardInfo.expiredIn != null); else eIndefinite">
                        <div class="col-2 flex-center">
                            <label class="heading mb-0"> Expiry </label>
                        </div>
                        <div class="col-5 pr-0">
                            <input type="text" class="form-control heading" [(ngModel)]="cardInfo.expiryDate" readonly>
                        </div>
                        <div class="col-5 pr-0">
                            <input type="text" class="form-control heading" [(ngModel)]="cardInfo.expiredIn" readonly>
                        </div>
                    </div>

                    <ng-template #eIndefinite>
                        <label class="heading mb-0"> Expiry </label>: Indefinite
                    </ng-template>
                </div>

                <div class="form-group mb-2">
                    <label class="heading mb-0">Comments</label>
                    <div class="input-group">
                        <textarea type="text" class="form-control heading bg-white" [(ngModel)]="cardInfo.comment" readonly></textarea>
                    </div>
                </div>
            </div>
        </div>
    </i-modal>
</div>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
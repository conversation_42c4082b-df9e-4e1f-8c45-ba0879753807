<div class="w-100 p-2">
    <form novalidate #conductCardForm="ngForm" (ngSubmit)="saveCardDetails(conductCardForm)">
        <div class="form-group mb-2">
            <label class="heading mb-0">
                Card Colour <!-- <small class="required-asterisk ">*</small> -->
            </label>
            <ng-select class="w-100 heading" 
                name="card_color"
                bindLabel="name" 
                bindValue="color"
                ng-value="selectedCardColor"
                placeholder="Please select card colour" 
                [items]="cardColors" 
                [clearable]="false"
                [searchable]="false"
                [(ngModel)]="selectedCardColor"
                #selectedCardColorValue="ngModel"
                (change)="onChange()" required>
                <ng-template ng-label-tmp let-item="item">
                    <div class="heading flex-center">
                        <div class="color-circle" [style.background-color]="item.color"></div>
                        {{ item.name }}
                    </div>
                </ng-template>
            </ng-select>
            <div class="alert alert-danger" [hidden]="(selectedCardColorValue.valid)">Card colour is required.</div>
        </div>
        <div class="form-group mb-2">
            <label class="heading mb-0">
                Card Name <!-- <small class="required-asterisk ">*</small> -->
            </label>
            <div class="input-group">
                <input type="text" class="form-control heading" 
                    placeholder="Please enter card name" 
                    name="card_name"
                    required="true" 
                    [(ngModel)]="cardName" 
                    #cardNameValue="ngModel" 
                    ng-value="cardName" 
                    (keyup)="onChange()" required>
                <div class="alert alert-danger" [hidden]="(cardNameValue.valid)">Card name is required.</div>
            </div>
        </div>
        <div class="form-group mb-2">
            <label class="heading mb-0">
                Card Type <!-- <small class="required-asterisk ">*</small> -->
            </label>
            <ng-select class="w-100 heading" 
                [items]="cardType" 
                bindLabel="name" 
                name="card_type"
                bindValue="name"
                placeholder="Please select card type" 
                [clearable]="false" 
                [searchable]="false"
                [(ngModel)]="selectedCardType" 
                #cardTypeValue="ngModel" 
                ng-value="selectedCardType" 
                (change)="onTypeChange()" required>
                <ng-template ng-label-tmp let-item="item">
                    <div class="heading flex-center">
                        {{ item.name }}
                    </div>
                </ng-template>
            </ng-select>
            <div class="alert alert-danger" [hidden]="(cardTypeValue.valid)">Card type is required.</div>
        </div>
        <div class="form-group mb-3">
            <label class="heading mb-0">
                Action <!-- <small class="required-asterisk ">*</small> -->
            </label>
            <ng-select class="heading dropdown-list" 
                [items]="selectedCardType == card_type.POSITIVE ? positiveAction : negativeAction" 
                bindLabel="name" 
                name="card_action"
                bindValue="name"
                placeholder="Please select card action" 
                [clearable]="false" 
                [searchable]="false"
                [(ngModel)]="selectedCardAction" 
                #cardActionValue="ngModel" 
                ng-value="selectedCardAction" 
                (change)="onChange()" required
                appendTo="body">
                <ng-template ng-label-tmp let-item="item">
                    <div class="heading flex-center">
                        {{ item.name }}
                    </div>
                </ng-template>
            </ng-select>
            <div class="alert alert-danger" [hidden]="(cardActionValue.valid)">Card action is required.</div>
        </div>
        <ng-container *ngIf="(showExpiry && selectedCardType == card_type.NEGATIVE) || indefinite">
            <div class="form-group mt-2" [ngClass]="{'disabled': indefinite}">
                <div class="row mx-0 custom-drop">
                    <div class="col-2 flex-center">
                        <label class="heading mb-0"> Expiry </label>
                    </div>
                    <div class="col-5 pr-0">
                        <ng-select class="w-100 heading" 
                            [items]="months" 
                            name="months"
                            bindLabel="name" 
                            bindValue="value"
                            placeholder="Months" 
                            [clearable]="false" 
                            [searchable]="false"
                            [(ngModel)]="selectedMonths"
                            #monthsValue="ngModel"
                            (change)="onChange()"
                            ng-value="selectedMonths" [required]="!indefinite">
                            <ng-template ng-label-tmp let-item="item">
                                <div class="heading flex-center">
                                    {{ item.name }}
                                </div>
                            </ng-template>
                        </ng-select>
                        <div class="alert alert-danger" [hidden]="(monthsValue.valid)">Months required.</div>
                    </div>
                    <div class="col-5 pr-0">
                        <ng-select class="w-100 heading" 
                            [items]="days" 
                            name="days"
                            bindLabel="name" 
                            bindValue="value"
                            placeholder="Days" 
                            [clearable]="false" 
                            [searchable]="false"
                            [(ngModel)]="selectedDays"
                            #daysValue="ngModel"
                            (change)="onChange()"
                            ng-value="selectedDays" [required]="!indefinite">
                            <ng-template ng-label-tmp let-item="item">
                                <div class="heading flex-center">
                                    {{ item.name }}
                                </div>
                            </ng-template>
                        </ng-select>
                        <div class="alert alert-danger" [hidden]="(daysValue.valid)">Days required.</div>
                    </div>
                </div>
            </div>
            <div class="form-group mb-2">
                <div class="col-12 d-flex justify-content-md-end pr-0">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" 
                            id="enableIndefiniteChkBx" 
                            name="indefinite"
                            #indefiniteChkBx="ngModel" 
                            [(ngModel)]="indefinite" 
                            (change)="onChange()"/>
                        <label class="custom-control-label" for="enableIndefiniteChkBx">Indefinite</label>
                    </div>
                </div>
            </div>
        </ng-container>
        <div class="form-group mt-2 mb-0" *ngIf="!isConfigureNewCard">
            <div class="col-12 d-flex justify-content-md-end pr-0">
                <button type="submit" class="btn btn-sm btn-primary" [disabled]="!conductCardForm.valid">
                    Save
                </button>
            </div>
        </div>
    </form>
</div>
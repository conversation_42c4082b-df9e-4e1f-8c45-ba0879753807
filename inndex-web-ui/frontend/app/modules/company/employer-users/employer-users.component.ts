import {Component, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {
    User,
    UserService,
    ToastService,
    ProjectService,
    ResourceService,
    AuthService,
    Project,
    signDocument,
    SiteMessaging, CompanyTimeLogService, HttpService
} from "@app/core";
import { ActivatedRoute } from "@angular/router";
import * as dayjs from 'dayjs';
import { Borders, FillPattern, Font, Workbook, Worksheet } from 'exceljs';
import * as Excel from "exceljs/dist/exceljs.min.js";
import * as ExcelProper from "exceljs";
import {DatatableComponent} from "@swimlane/ngx-datatable";
import {NgbModal, NgbDate, NgbActiveModal, NgbDateStruct} from "@ng-bootstrap/ng-bootstrap";
import {Observable, from, forkJoin} from "rxjs";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import {AdditionalDocUploaderComponent} from "@app/modules/common";
import * as fs from 'file-saver';
import { NgbNavChangeEvent } from '@ng-bootstrap/ng-bootstrap';
import {AppConstant} from "@env/environment";
import { ActionBtnEntry, GenericConfirmationModalComponent, IModalComponent } from '@app/shared';

@Component({
    selector: 'employer-users',
    templateUrl: 'employer-users.component.html',
    styleUrls:['./employer-users.scss']
})
export class EmployerUsersComponent implements OnInit {

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    authUser$: User;
    downloadTimeSheetReportLoading: boolean = false;
    employees_details: Array<any> = [];
    tempList: Array<any> = [];
    jobRole: string = null;
    companyUser = null;
    employer_name: string = '';
    employerId: number = 0;
    employee_row:  any = {};
    competencies:any = [];
    contactDetails:any = {};
    healthAssessments:any = {};
    currentProjectInfo:any = {};
    signedDocuments: Array<signDocument> = [];
    signedDocument: signDocument = new signDocument;
    healthAssessmentsData: any = [];
    healthAssessmentsCategoryData: any = [];
    medicalAssessmentsData: any = [];
    timeDetails: any = [];
    timeDetailsFiltered: any = [];
    img_link: string = `/images/project-placeholder.png`;
    blobType: string = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    userProjects: Array<any> = [];
    userProjectId = null;
    userProjectId2 = null;
    maxDate: NgbDateStruct = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
    // minDate: NgbDateStruct = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
    dbDateFormat: string = 'YYYY-MM-DD';
    hoveredDate: NgbDate;
    fromDate: NgbDate;
    toDate: NgbDate;
    usersList: Array<any> = [];
    filteredUsers: Array<any> = [];
    selectedUsers: Array<any> = [];
    jobRoles: any = [];
    selectedRole: string = null;
    employeesLoading: boolean = false;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    blockloader: boolean = false;
    empDetailComment: string = '';
    isEmpCommentEnable: boolean = false;
    textAreaRows: number = 0;
    is_mobile_nav: boolean = false;
    showEmployeeInfoModal: boolean = false;
    showDownloadTimeSheetModal: boolean = false;
    showSignDocumentModal: boolean = false;
    showViewDocModal: boolean = false;
    isFilteredUsersReset: boolean = false;
    hasSelectedUsers: boolean = false;
    baseButtonConfigGroup: Record<string, ActionBtnEntry[]> = {};
    baseButtonConfig: Array<ActionBtnEntry> = [
        {
            key: 'view',
            label: '',
            title: 'View Employee Information',
            mat_icon: 'search',
        }, 
        {
            key: 'download',
            label: '',
            title: 'Download PDF',
            mat_icon: 'download',
        },
        {
            key: 'delete',
            label: '',
            title: 'Delete Employee',
            mat_icon: 'delete',
        },      
    ];

    constructor(
        private authService: AuthService,
        private toastService: ToastService,
        private companyAdminService: CompanyTimeLogService,
        private userService: UserService,
        private resourceService: ResourceService,
        private activatedRoute: ActivatedRoute,
        private modalService: NgbModal,
        public ngbMomentjsAdapter: NgbMomentjsAdapter,
        private projectService: ProjectService,
        private httpService: HttpService,
    ) {
        this.is_mobile_nav = this.httpService.isMobileDevice();
    }

    onNavChange(changeEvent: NgbNavChangeEvent) {
        if(changeEvent.nextId == 'nav-employment') {
            let commentRowsArr = (this.employee_row['comment'] || '').split(/<br[^>]*>/g);
            this.textAreaRows = (commentRowsArr || []).reduce((count, string) => {
                let strRowCount = (string.length || 0)/46;
                count += (strRowCount < 1) ? 0 : Math.ceil(strRowCount) - 1;
                return count;
            }, commentRowsArr.length);

            this.empDetailComment = (this.employee_row['comment'] || '').replace(/<br[^>]*>/g, "\n");
            this.isEmpCommentEnable = !(this.employee_row['comment']);
        }
    }

    trackById(index, record) {
        return record.id;
    }

    displayDate(d: number, format: string) {
        return dayjs(d).format(format);
    }

    AppConstant = AppConstant;
    dayjs(n: number, format?: any) {
        return dayjs(n, format);
    };

    unix(n: number) {
        return dayjs.unix(n);
    };

    getTimeFromSeconds(n: number) {
        return dayjs().startOf('day').add(dayjs.duration(+n, 'seconds').asSeconds(), 'second');
    };

    onDateSelection(date: NgbDate) {
        if (!this.fromDate && !this.toDate) {
            this.fromDate = date;
        } else if (this.fromDate && !this.toDate && date.after(this.fromDate)) {
            this.toDate = date;
        }else {
            this.toDate = null;
            this.fromDate = date;
        }
    }

    isHovered(date: NgbDate) {
        return this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate);
    }

    isInside(date: NgbDate) {
        return date.after(this.fromDate) && date.before(this.toDate);
    }

    isRange(date: NgbDate) {
        return date.equals(this.fromDate) || date.equals(this.toDate) || this.isInside(date) || this.isHovered(date);
    }

    isAboveMaxRange(date: NgbDate){
        let max = (this.maxDate);

        // @todo: add check of lower bound date too.
        // this to create local variable with already converted data, to speed up processing
        return (!date.before(max) && !date.equals(max));
    }

    employer: any;
    ngOnInit() {
        this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
        this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
        this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
        this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
            }
        });
        this.employeesLoading = true;
        this.activatedRoute.params.subscribe(params => {
            this.employerId = params['employerId'];
            this.initializeUsersByEmployer();
        });
    }

    @ViewChild(DatatableComponent) table: DatatableComponent;

    updateFilter(value) {
        const val = value.toLowerCase();

        // filter our data
        const temp = this.tempList.filter(function (d) {
            return (
                (d.user_ref.name && d.user_ref.name.toLowerCase().indexOf(val) !== -1) ||
                (d.job_role && d.job_role.toString().toLowerCase().indexOf(val) !== -1) ||
                !val
            );
        });

        // update the rows
        this.employees_details = temp;
        // Whenever the filter changes, always go back to the first page
        this.table.offset = 0;
    }

    initializeUsersByEmployer() {
        if(this.employerId) {
            this.userService.getUsersByEmployer(this.employerId).subscribe((data: any) => {
                this.employeesLoading = false;
                if(data && data.success) {
                    this.employer_name = data.employer_name;
                    if (data.project_logo_file) {
                        this.img_link = data.project_logo_file.file_url;
                    }
                    this.employees_details = data.employees_details;
                    this.tempList = JSON.parse(JSON.stringify(data.employees_details));
                    this.initializeDefaultUsersData();
                    this.jobRoles = this.availableJobRoles();
                } else {
                    const message = data.message || 'Failed to fetch users by employer.';
                    this.toastService.show(this.toastService.types.ERROR, message, { data: data });
                }
            });
        }
    }

    initializeDefaultUsersData() {
        this.usersList = (this.tempList || []).reduce((acc, item) => {
            acc.push({ ...item, _isSelected: false });
            return acc;
        }, []);
        this.filteredUsers = JSON.parse(JSON.stringify(this.usersList));
    }

    @ViewChild('viewEmployeeInformationHtml')
    private viewEmployeeInformationHtmlRef: IModalComponent;
    viewEmployeeInformation(row: any) {
        this.employee_row = row;
        this.empDetailComment = (row.comment || '').replace(/<br[^>]*>/g, "\n");
        this.isEmpCommentEnable = !(row.comment);
        let apiCalls = {
            contact_response: this.companyAdminService.getCompanyUserContactDetail(this.employerId, row.user_ref.id),
            project_response: this.userService.getUserCurrentProject(row.user_ref.id),
        };
        this.blockloader = true;
        this.contactDetails = {};
        this.currentProjectInfo = {};
        forkJoin(apiCalls).subscribe(({contact_response, project_response} : any) => {
            if (contact_response && contact_response.contact_detail) {
                this.contactDetails = contact_response.contact_detail;
            }

            if (project_response && project_response.project_info) {
                this.currentProjectInfo = project_response.project_info;
            }
            this.blockloader = false;
            this.showEmployeeInfoModal = true;
            this.viewEmployeeInformationHtmlRef.open();
        });
    }

    closeEmployeeInfoModal(event?) {
        this.showEmployeeInfoModal = false;
        if(event){
            event.closeFn();
        }
    }

    closeTimesheetModal(event?) {
        this.showDownloadTimeSheetModal = false;
        if(event){
            event.closeFn();
        }
    }

    createEmploymentTime(epoch_ms) {
        if(epoch_ms && !isNaN(+epoch_ms)){
            let m = dayjs(+epoch_ms);
            let d = dayjs.duration(dayjs().diff(m));
            return dayjs.isDuration(d) ? (
                (d.years() ? d.years() + ' year': '')
                + ' '
                + (d.months() ? d.months() + ' month': '')
                + ' '
                + (!d.years() ? (d.days() ? d.days() + ' day': ''): '')
            ) : null;
        }
        return "Not Available";
    }

    numberToYesNo(num) {
        if(+num === 1){
            return 'Yes';
        }else if(+num === 0){
            return 'No';
        }
        else if(num === "n/a"){
            return 'N/A';
        }
        else if(num === null){
            return '-';
        }
        return num;
    }

    checkHasSelectedUsers(noOfSelectedUsers: number) {
        this.hasSelectedUsers = (noOfSelectedUsers > 0) ? true : false;
    }

    downloadTrainingRecords(filtered, form) {
        this.downloadTimeSheetReportLoading = true;
        let users = form.value.selectedUsers.map((item) => item.id);
        if(this.employerId) {
            let reqData: any = {
                toBefilterd: false
            };
            if(filtered) {
                reqData.toBefilterd = true;
                reqData.employerUsersList = users;
            }
            this.userService.getUserDocumentsByEmployer(this.employerId, reqData).subscribe((response: any) => {
                if(response && response.success) {
                    var sortedRecords = response.data.sort(function (a, b) {
                        return a.name.localeCompare(b.name);

                    });
                    let params = {country_code: (this.employer?.country_code || undefined)};
                    this.resourceService.getCompetencies(params).subscribe((data: any) => {
                        let competencyList = data.competencieslist.map(function(r) {
                            return r.name
                        });
                        this.downloadReport(sortedRecords, competencyList)
                    });
                    this.filteredUsers = [ ...this.usersList ];
                    this.isFilteredUsersReset = true;
                    form.reset();
                }
            });
        }
    }

    @ViewChild('downloadTimeModalHtml')
    private downloadTimeModalHtmlRef: IModalComponent;
    downloadTimeRecordsPopup() {
        this.showDownloadTimeSheetModal = true;
        this.downloadTimeModalHtmlRef.open();
    }

    filterTimeLogs() {
        this.timeDetailsFiltered = this.timeDetails.filter(r=> r.project_id == this.userProjectId);
    }

    downloadTimeSheet(event) {
        let fromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.fromDate);
        let toDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.toDate);
        let filteredRecords = this.timeDetails;
        if(this.userProjectId2) {
            filteredRecords = this.timeDetails.filter(r=> r.project_id === this.userProjectId2);
        }
        if(fromDate) {
            if(!toDate) {
                toDate = fromDate;
            }
            filteredRecords = filteredRecords.filter((item: any) =>
                dayjs(item.day_of_yr, 'YYYY-MM-DD').isSameOrAfter(fromDate) && dayjs(item.day_of_yr, 'YYYY-MM-DD').isSameOrBefore(toDate)
            );
        }
        let workbook: ExcelProper.Workbook = new Excel.Workbook();
        let worksheet = workbook.addWorksheet('Timesheet');
        let columns = [
            { header: "Date", key: "Date", width:20},
            { header: "Project", key: "Project", width:20},
            { header: "Job Title", key: "Job Title", width:20},
            { header: "In Time", key: "In Time", width:20},
            { header: "Out Time", key: "Out Time", width:20},
            { header: "Total Time", key: "Total Time", width:20},
            { header: "Adjusted Minutes", key: "Adjusted Minutes", width:20},
            { header: "Adjusted Minutes Comments", key: "Adjusted Minutes Comments", width:30}
        ];
        worksheet.columns = columns;
        let records = [];
        filteredRecords.filter(row => {
            let result = {
                "Date": dayjs(row.day_of_yr, AppConstant.apiRequestDateFormat).format(AppConstant.displayDateFormat),
                "Project": this.getProjectName(row.project_id),
                "Job Title": this.jobRole,
                "In Time": row.clock_in ? this.unix(+row.clock_in).format('HH:mm:ss') : '-',
                "Out Time": row.clock_out ? this.unix(+row.clock_out).format('HH:mm:ss') : '-',
                "Total Time": row.effective_time ? this.getTimeFromSeconds(row.effective_time).format('HH:mm:ss') : '',
                "Adjusted Minutes": (row.adjustment > 0 ? '+' : '') + (row.adjustment || ''),
                'Adjusted Minutes Comments': (row.comments && row.comments.length) ? row.comments.filter(c => c.origin === 'admin' && c.module === 'members').map(c => c.note).join(' ') : ''
            };

            records.push(result);
        });
        worksheet.addRows(records);
        worksheet.eachRow(function(row, rowNumber) {
            // Iterate over all (including empty) cells in a row
            row.eachCell({ includeEmpty: true }, function(cell, colNumber) {
                if (rowNumber === 1) {
                    row.height = 20;
                    cell.font = {
                    bold: true,
                    };
                    cell.fill = {
                        type: 'pattern',
                        pattern:'solid',
                        fgColor: {argb: 'dedede'}
                    }
                    cell.border = {
                        top: {style:'thin'},
                        left: {style:'thin'},
                        bottom: {style:'thin'},
                        right: {style:'thin'}
                    };

                }
            });
        });

        workbook.xlsx.writeBuffer().then(data => {
            const blob = new Blob([data], { type: this.blobType });
            fs.saveAs(blob, `${this.companyUser.name} - Timesheet.xlsx`);
            this.closeTimesheetModal(event);
        });
    }

    getProjectName(projectId) {
        let project: any = this.userProjects.find(p=> p.id == projectId) || {};
        return project.name;
    }

    downloadImage(fileUrl, name) {
        fs.saveAs(fileUrl, name);
    }

    downloadReport(data, competencies) {
        let records = [];
        let workbook: ExcelProper.Workbook = new Excel.Workbook();
        let worksheet = workbook.addWorksheet('sheet1');
        let from = dayjs();
        //worksheet.views = [{zoomScale: 75}];
        let columns = [
            { header: "User Id", key: "user_id", width:10},
            { header: "Name", key: "Name", width:40},
            { header: "Job Role", key: "Job Role", width:40},
        ];

        competencies.forEach(function (competency) {
            columns.push({'header': competency, 'key':competency, width:15});
        });
        worksheet.columns = columns;

        data.forEach(function (r) {
            let userCompetency = {};
            var uDocuments = r.userDocuments.reduce(function(uDoc, doc, i) {
                uDoc[doc.name] = doc;
                return uDoc;
              }, {});

            competencies.forEach(function (competency) {
                userCompetency[competency] = uDocuments[competency] ? dayjs(+uDocuments[competency].expiry_date).format(AppConstant.defaultDateFormat) : null;
            });
            let result = {
                "user_id": r.user_id,
                "Name": r.name,
                "Job Role": r.job_role,
                ...userCompetency
            };
            records.push(result);

        });

        worksheet.addRows(records);
        worksheet.eachRow(function(row, rowNumber) {
            // Iterate over all (including empty) cells in a row
            row.eachCell({ includeEmpty: true }, function(cell, colNumber) {
                if (rowNumber == 1) {
                    row.height = 20;
                    cell.font = {
                      bold: true,
                    };
                    cell.fill = {
                        type: 'pattern',
                        pattern:'solid',
                        fgColor: {argb: 'dedede'}
                    }
                    cell.border = {
                        top: {style:'thin'},
                        left: {style:'thin'},
                        bottom: {style:'thin'},
                        right: {style:'thin'}
                    };

                }
                if(rowNumber > 1 && colNumber > 2) {
                    let v = cell.value;
                    if(v != null) {
                        let dateString:string = v.toString();
                        let mDate = dayjs(dateString, AppConstant.defaultDateFormat);
                        var diff = from.diff(mDate, 'day');
                        //expiry date less than a month should be in red
                        if(diff >= -30) {

                            cell.font = {
                                color: {argb: "d61e1e"}
                            }
                        }
                    }
                }
            });
        });

        let deleted= 0;
        let indexToDelete = 0;
        let isNull;
        //remove empty columns
        competencies.map((c, i) => {
            isNull = obj => obj[c] == null;
            if(records.every(isNull)) {
                indexToDelete = i-deleted+4;
                worksheet.spliceColumns(indexToDelete, 1);
                deleted++;
            }
        })

        workbook.xlsx.writeBuffer().then(data => {
            const blob = new Blob([data], { type: this.blobType });
            fs.saveAs(blob, `${this.employer_name}- Employee Training Records - ${dayjs().format(AppConstant.defaultDateFormat)}.xlsx`);
            this.downloadTimeSheetReportLoading = false;
        });

    }

    getMyHealthAssessmentAnswers(userId) {
         this.companyAdminService.getCompanyUserHealthAssessment(this.employerId, userId, 'true').subscribe((data: any) => {
            if (data && data.health_assessments && data.health_assessments.length) {
                this.healthAssessments = data.health_assessments;
                this.healthAssessmentsData = [];
                for (var item of data.health_assessments) {
                    if (item.question_ref) {
                        if (this.healthAssessmentsData[item.question_ref.category] == undefined) {
                            this.healthAssessmentsData[item.question_ref.category] = [];
                        }

                        let questionAndAnswer = {};
                        if (item.question_ref.question) {
                            questionAndAnswer = {
                                'question': item.question_ref.question,
                                'answer': this.numberToYesNo(item.answer)
                            }
                        }
                        this.healthAssessmentsData[item.question_ref.category].push(questionAndAnswer);
                    }
                }
                this.healthAssessmentsCategoryData = Object.keys(this.healthAssessmentsData);
            }
        });
    }

    getMyMedicalAssessmentAnswers(userId) {
        this.companyAdminService.getCompanyUserMedicalAssessment(this.employerId, userId).subscribe((data: any) => {
            if (data.medical_assessments && data.medical_assessments.length) {
                this.medicalAssessmentsData = data.medical_assessments.map(qa => {
                    return {
                        'question':qa.question,
                        'answer':this.numberToYesNo(qa.answer),
                        'ans_details':qa.ans_details
                    };
                });
            }
        });

    }

    getUserTimeDetails(user, jobRole) {
        this.jobRole = jobRole;
        this.companyUser = user;
        this.projectService.getCompanyProjectsForUser(this.employerId, user.id).subscribe((data:any) => {
            let userProjects = [...data.additional_projects, ...data.employer_projects];
            this.userService.getUserTimeDetails(this.employerId, user.id).subscribe((data: any) => {
                if(data.success) {
                    this.setActiveProjects(userProjects, data.timeLogs);
                    this.timeDetails = data.timeLogs;
                    this.timeDetailsFiltered = this.timeDetails;
                }
            });
        });
    }

    setActiveProjects(projects, timeLogs) {
        let activeProjects = timeLogs.map(log => log.project_id );
        let uniqueProjects = Array.from(new Set(activeProjects));
        this.userProjects = projects.filter(p => uniqueProjects.includes(p.id));
    }

    getMyCompetencies(userId) {
        this.companyAdminService.getCompanyUserDocuments(this.employerId, userId, {linear: 'true', doc_owner_id: userId}).subscribe((data: any) => {
            if (data && data.user_documents) {
                this.competencies = data.user_documents;
                this.getRowButtonGroup(this.competencies);
            }
        });
    }

    getRowButtonGroup(records: any[]): void {
        if (!records?.length) return;

        const BASE_BUTTON_CONFIG: Array<ActionBtnEntry> = [{
            key: 'download',
            label: '',
            title: 'Download',
            mat_icon: 'download'
        }];

        this.baseButtonConfigGroup = records.reduce((config, row) => {
            if (!row?.id) return config;

            const photos = this.photoDownloadOpts(row.user_files || []);
            const downloadOptions = [
                ...this.createPhotoDownloadOptions(photos)
            ];

            config[row.id] = [{
                ...BASE_BUTTON_CONFIG[0],
                children: downloadOptions
            }];

            return config;
        }, {});
    }

    competenciesBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        // Handle individual photo downloads
        const photos = this.photoDownloadOpts(row?.user_files || []);
        const downloadOptions = this.createPhotoDownloadOptions(photos);
        
        const matchingOption = downloadOptions.find(opt => opt.key === entry.key);
        if (matchingOption) {
            this.downloadImage(matchingOption.title, matchingOption.label);
        }
    }

    photoDownloadOpts(array){
        return array.sort((a, b) => b?.id - a?.id);
    }

    createPhotoDownloadOptions(photos: any[]): Array<ActionBtnEntry> {
        return photos.map((photo, index) => ({
            key: `download_${photo.id}`,
            label: `Download ${((index == 0) ? 'Front' : ((index == 1) ? 'Back' : 'Additional Image ' + (index - 1)))}`,
            title: photo.file_url || '',
        }));
    }

    getKeys(obj) {
        return Object.keys(obj);
    }

    trackByRowIndex(index: number, obj: any){
        return index;
    }

    update_row: any = {};

    availableJobRoles() {
        let userRoles = this.usersList.map(u => u.job_role).filter(role => role);
        return Array.from(new Set(userRoles));
    }

    @ViewChild('filterRecordsModal')
    private filterRecordsModalRef: IModalComponent;
    filterRecordsPopup() {
        this.initializeDefaultUsersData();
        this.filterRecordsModalRef.open();
    }

    closeTrainingRecords(form) {
        form.reset();
    }

    @ViewChild('additionalDocUploaderComponent', { static: true })
    private additionalDocUploaderComponentRef: AdditionalDocUploaderComponent;
    openMoreUploadPopup(row){
        this.update_row = row;
        this.additionalDocUploaderComponentRef.openMoreUploadPopup(row);
    }

    onDocumentUpload($event){
        console.log('Extra doc upload done');
        if(this.update_row.user_ref && this.update_row.user_ref.id){
            this.getMyCompetencies(this.update_row.user_ref.id);
        }
    }

    openModal(row: any){
        this.employee_row = row;
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete Employer',
            title: `Are you sure you would like to remove <span class="fw-500">${this.employee_row?.user_ref.name}</span> from your company portal? This operation cannot be undone.`,
            confirmLabel: 'Remove',
            onConfirm: () => {
                this.userService.deleteUserFromEmployer(this.employerId, row.user_ref.id).subscribe((deletingResp=>{
                    if(deletingResp && deletingResp['success']) {
                        this.initializeUsersByEmployer();
                    }
                }));
            }
        });

    }


    confirm(): void {

       // this.modalRef.hide();
        this.delete();
      }

      delete():void{
        console.log('deleted',' record');
      }

      decline(): void {
       // this.message = 'Declined!';
     //   this.modalRef.hide();

    }

    uploadDone($event) {
        if($event && $event.userFile && $event.userFile.id){
            this.signedDocument.doc_file_ref = $event.userFile.id;
        }
    }

    fileDeleteDone($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.signedDocument.doc_file_ref = null;
        }
    }

    downloadCompanyEmployeeInfo(userInfo) {
        this.blockloader = true;
        let body = {
            createdAt: userInfo.createdAt,
            companyId: this.employerId,
            type: 'pdf'
        };

        this.userService.downloadCompanyEmployeeInfo(body, userInfo.id, () => {
            this.blockloader = false;
        });
    }

    getSignDocuments(userId) {
        this.userService.getSignDocuments(userId).subscribe((data:any) => {
            if (data && data.success) {
                this.signedDocuments = data.document;
            } else {
                const message = 'Failed to fetch user documents.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    @ViewChild('signDocumentFormHtml')
    private signDocumentFormHtmlRef: IModalComponent;
    uploadDocumentPopup(employee) {
        this.employee_row = employee;
        this.signedDocument = new signDocument;
        this.showSignDocumentModal = true;
        this.signDocumentFormHtmlRef.open();
    }

    closeDocumentFormModal(event?) {
        this.showSignDocumentModal = false;
        if(event) {
            event.closeFn();
        }
    }

    sendSignDocument(event, formData) {
        if (!formData.valid) {
            const message = 'Invalid form data.';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Send Document',
            title: `Are you sure you want to send this document to <span class="fw-500">${this.employee_row?.user_ref?.name}</span> for signoff?`,
            confirmLabel: 'Send',
            onConfirm: () => {
                let req = { employee_ref: this.employee_row.user_ref.id, ...(formData.value) };
                this.blockloader = true;
                this.userService.addSignDocument(req).subscribe((data:any) => {
                    this.blockloader = false;
                    if(data && data.success) {
                        this.getSignDocuments(this.employee_row.user_ref.id);
                        this.closeDocumentFormModal(event);
                    } else {
                        const message = 'Something went wrong while sending the document.';
                        this.toastService.show(this.toastService.types.ERROR, message);
                    }
                });
            }
        });
    }

    getResendContent(resend_logs) {
        let titleArr = [];
        (resend_logs || []).map(log => {
            titleArr.push(`Resent: ${dayjs(log).format(AppConstant.fullDateTimeFormat)}`);
            return log;
        });

        let title = (titleArr.length) ? titleArr.join('&#10;') : 'Resend';
        return `<img style="width: 21px;" src="/images/resend.png" alt="Resend" title="${title}"> Unsigned`;
    }

    resendSignDocument(documentInfo) {
        this.blockloader = true;
        this.userService.resendSignDocument(documentInfo.id).subscribe((data:any) => {
            this.blockloader = false;
            if(data && data.success) {
                this.getSignDocuments(this.employee_row.user_ref.id);
            } else {
                const message = 'Something went wrong while resending the document.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    downloadFile(fileUrl, fileName) {
        fs.saveAs(fileUrl, fileName);
    }

    @ViewChild('viewDocumentHtml')
    private viewDocumentHtmlRef: IModalComponent;
    viewDocument(signDocument) {
        this.signedDocument = signDocument;
        this.showViewDocModal = true;
        this.viewDocumentHtmlRef.open();
    }

    closeViewDocModal(event?) {
        this.showViewDocModal = false;
        if(event){
            event.closeFn();
        }
    }

    textareaAutoGrow(element) {
        element.style.height = "45px";
        element.style.height = (element.scrollHeight + 0.2)+"px";
    }

    saveComment() {
        let req = {
            "comment": (this.empDetailComment || '').replace(/\r?\n/g,'<br/>')
        }
        this.employee_row['comment'] = req.comment;
        this.blockloader = true;
        this.userService.updateUserEmpDetail(this.employee_row['user_ref'].id, this.employee_row['id'], req).subscribe((data: any) => {
            if (data && data.success) {
                this.blockloader = false;
                this.isEmpCommentEnable = false;
                console.log("Record has been updated!!");
            }
        });
    }

    enableEmpCommentEdit() {
        this.isEmpCommentEnable = true;
    }
    searchFunction(data){
       this.updateFilter(data.search);
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any, qclReportContent, defectsHtml): void {
        const actionMap = {
            'view': () => this.viewEmployeeInformation(row),
            'download': () => this.downloadCompanyEmployeeInfo(row.user_ref),
            'delete': () => this.openModal(row),
        };
        const action = actionMap[entry.key];
        if (action) {
            action();
        }
    }
}

import { Component, OnInit, ViewChild } from "@angular/core";
import { HttpParams } from "@angular/common/http";
import { ActivatedRoute } from "@angular/router";
import * as dayjs from "dayjs";
import { DomSanitizer } from "@angular/platform-browser";
import {
  AuthService,
  Project,
  User,
  ToastService,
  SiteMessagingService,
  Common,
  HttpService,
  filterData,
  CompanySiteMessage,
  recipientData,
  CompanyService,
} from "@app/core";
import {
  NgbModal,
  NgbModalConfig,
  NgbDateStruct,
  NgbTimepickerConfig,
} from "@ng-bootstrap/ng-bootstrap";
import { AppConstant } from "@env/environment";
import { NgbMomentjsAdapter } from "@app/core/ngb-moment-adapter";
import {
    ActionBtnEntry,
  GenericConfirmationModalComponent,
  IModalComponent,
} from "@app/shared";
import { innDexConstant } from "@env/constants";
import { forkJoin } from "rxjs";


@Component({
    templateUrl: './company-messaging.component.html',
    providers: [NgbModalConfig, NgbModal, NgbTimepickerConfig],
    styleUrls: ['./company-messaging.component.scss'],
})

export class CompanyMessagingComponent implements OnInit {

    authUser$: User;
    employer: any = {};
    companyProjects: Array < Project > = [];
    archivedCompanyProjects: Array < Project > = [];
    companyResolverResponse: any;
    employerId: number = 0;
    loadingSiteMessaging: boolean = false;
    tableOffset: number = 0;
    requestLoader: boolean = false;
    company_messages: Array < any > = [];
    current_message: CompanySiteMessage = new CompanySiteMessage;
    allowedMime: Array < any > = ['image/jpeg', 'image/jpg', 'image/png',
        'application/pdf', 'video/mp4', 'video/quicktime', 'audio/mpeg'
    ];
    today_visited_user_checked: boolean = false;
    last30_visited_user_checked: boolean = false;
    approved_induction_user_checked: boolean = false;
    all_projects_checked: boolean = false;
    common = new Common();
    page = this.common.page;
    successResponse: {success:boolean,
    title:string, message:string} = {
        success: true,
        title:'',
        message: ''
    };
    messageTitle: string = 'Success';
    usersJobRole: Array < string > = [];
    selectedJobRoles: Array < string > = [];
    projectInfo: Project = new Project;
    isProjectPortal: boolean = false;
    isMobileDevice: boolean = false;
    messageFormState: {
        showSendMessageForm: boolean;
        showViewMessage: boolean;
        showEditMessage: boolean;
        showSentMessage: boolean;
      } = {
        showSendMessageForm: false,
        showViewMessage: false,
        showEditMessage: false,
        showSentMessage: false,
      };
      
    projects_list: any[] = []
    selectedProjects: any[] = []
    filter: {
        search: string,
        user: any[]
    } = {
        search: "",
        user: []
    }
    dummyList: recipientData[] = [];
    sorts: {
        dir:string, prop: string
    } = {
        dir:'desc', prop: 'id'
    };
    user_filter_data: any[] = []
    filterData: filterData[] = this.renderFilterData();
    time: {
        hour: any,
        minute: any
    } = {
        hour: "",
        minute: ""
    };
    selectedDay: NgbDateStruct =   this.ngbMomentjsAdapter.fromModel(dayjs());
    resent: boolean = false;
    showError: boolean = false;
    minDate: any = null;
    maxDate: any = null;
    tz : string  = 'Europe/London';
    maxFileSize: number = 10*1024*1024;
    loadingInlineCompanyMsg: boolean = false;
    rowButtonGroup: Array<ActionBtnEntry> = [
        {
            key: 'view_message',
            label: '',
            title: 'View message',
            mat_icon: 'search',
        },
        {
            key: 'edit_message',
            label: '',
            title: 'Edit message',
            mat_icon: 'edit_note',
        },
        {
            key: 'delete_message',
            label: '',
            title: 'Delete message',
            mat_icon: 'delete',
        },
    ];

    constructor(
        private activatedRoute: ActivatedRoute,
        private authService: AuthService,
        private toastService: ToastService,
        private siteMessagingService: SiteMessagingService,
        private modalService: NgbModal,
        private sanitizer: DomSanitizer,
        private httpService: HttpService,
        public ngbMomentjsAdapter: NgbMomentjsAdapter,
        private companyService:CompanyService,
    ) {
        this.isProjectPortal = this.activatedRoute.snapshot.data
            .is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
    }

    ngOnInit(): void {
        this.employer = this.activatedRoute.snapshot.data
          .companyResolverResponse.company;
        this.employerId = this.employer.id;
        this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
        this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        forkJoin({
            projects: this.companyService.getCompanyProjects(this.employerId),
            timezone: this.companyService.getCompanyTimezone(this.employerId)
          }).subscribe(({ projects, timezone }) => {
            if (projects && projects.success) {
              this.companyProjects = projects.projects;
            } else {
                const message = `Failed to fetch projects list on company, id: ${this.employerId}.`;
                this.toastService.show(this.toastService.types.ERROR, message, { data: projects });
            }
          
            if (timezone && timezone.success) {
              this.tz = timezone.timezone.value;
            } else {
              console.log('Failed to fetch company timezone ', timezone);
            }
          });
        this.initializeTable();

        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
            }
        });
        this.minDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.maxDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs().add(2,
            'M'));
    }

    fullDateTimeFormat_without_ss = innDexConstant.fullDateTimeFormat_without_ss;

    initializeTable(isPageChange?: boolean) {
        this.loadingSiteMessaging = true;
        this.pageCallback({
            offset: 0
        }, isPageChange);
    }

    /**
     * Populate the table with new data based on the page number
     * @param page The page to select
     */
    pageCallback(pageInfo: {
        count ? : number,
        pageSize ? : number,
        limit ? : number,
        offset ? : number
    }, isPageChange?: boolean) {
        this.page.pageNumber = pageInfo.offset;
        this.reloadTable(isPageChange);
    }


    reloadTable(isPageChange?: boolean) {
        if (isPageChange) {
          this.loadingInlineCompanyMsg = true;
        }
        const params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('search', this.filter.search)
            .set('sender', this.filter.user.length ? this.filter.user.join(
                ',') : '')
            .set('sortKey', this.sorts.prop)
            .set('sortDir', this.sorts.dir);

        this.siteMessagingService.getCompanyMessages(this.employerId, params)
            .subscribe((data: any) => {
                if (data && data.records) {
                    if (data.user_filter_data && data.user_filter_data
                        .length) {
                        this.user_filter_data = data.user_filter_data;
                        this.filterData = this.renderFilterData();
                    }
                    this.page.totalElements = data.records.length;
                    const updatedCompanyMessages = data.records.map((
                        message) => {
                        let read_by = this.getReadRate(message);
                        let totalrecipients = message
                            .recipients_array.filter(a => a.id !=
                                null).length;
                        let readRate = this.isValidNumber((+
                            read_by / +totalrecipients)) ? (+
                            read_by / +totalrecipients) : 0;
                        readRate = readRate > 0 ? +readRate.toFixed(
                            2) : +readRate;
                        this.page.totalElements = data.totalCount;
                        const readRatio =
                            `${read_by}/${totalrecipients}`
                        return {
                            ...message,
                            readRate,
                            readRatio
                        };
                    })
                    this.company_messages = updatedCompanyMessages;

                    if (this.current_message && this.current_message.id) {
                        this.current_message = (this.company_messages || [])
                            .find(msg => msg.id === this.current_message
                                .id);
                    }
                    this.loadingSiteMessaging = false;
                    this.loadingInlineCompanyMsg = false;
                    return;
                }
                const message = `Failed to fetch messages on company, id: ${this.employerId}.`;
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
                return;
            });
    }

    AppConstant = AppConstant;
    dayjs(n: number) {
        return dayjs(+n).tz(this.tz);
    };


    @ViewChild('sendMessageForm') sendMessageForm;
    

    @ViewChild(
        'sendMessageFormHtml'
    ) private sendMessageFormHtmlGenericModal: IModalComponent
    sendMessagePopup() {
        this.setSendModalConfig();
        this.messageFormState.showViewMessage = false;
        this.current_message = new CompanySiteMessage;
        this.today_visited_user_checked = false;
        this.last30_visited_user_checked = false;
        this.messageFormState.showSendMessageForm = true;
        this.messageFormState.showEditMessage = false;
        this.selectedProjects = [];
        this.all_projects_checked = false;

        this.showError = false;
        if (this.sendMessageForm) {
            this.sendMessageForm?.form.markAsUntouched();
            this.sendMessageForm.form.statusChanges.subscribe(status => {
                this.sendMessageFormHtmlGenericModal.rightPrimaryBtnDisabled =
                    status !== 'VALID';
            });
        }
        return this.sendMessageFormHtmlGenericModal.open()
    }

    @ViewChild(
        'confirmationModalRef'
    ) private confirmationModalRef: GenericConfirmationModalComponent;
    sendMessageRequest() {
        let request = { ...this.current_message };
        request.msg_files = (request.msg_files || []).reduce((arr, file) => {
            if (file && file.id) {
                arr.push(file.id);
            }
            return arr;
        }, []);
        request.send_to_all_projects = this.all_projects_checked;
        request.projects = this.selectedProjects;
        request.last30_visited_user = this.last30_visited_user_checked;
        request.today_visited_user = this.today_visited_user_checked;

        this.messageTitle = "Success"
        let pathParam : {
            employerId:number,
            messageId?:number
        } = {employerId:this.employerId}
        if(this.messageFormState.showEditMessage){
            pathParam.messageId =  request.id
        }
        this.requestLoader = true;
        this.siteMessagingService.sendCompanyMessage(request, pathParam)
            .subscribe(out => {
                if(out.users) {
                    this.modalService.dismissAll();
                    this.sendMessageFormHtmlGenericModal.close()
                    this.messageFormState.showSendMessageForm = false;
                }
                if (out.success) {
                    this.successResponse = out;
                    if(out.title){
                        this.messageTitle = out.title
                    }
                    this.initializeTable(true);

                    this.showSuccessWarningConfirmationModal()
                } else {
                    const message = out.message || 'Failed to send message.';
                    this.toastService.show(this.toastService.types.ERROR, message, { data: out });
                }

            }).add(() => {
                this.requestLoader = false;
                });
    }

    showSuccessWarningConfirmationModal() {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: this.messageTitle,
            title: this.successResponse.message,
            confirmLabel: (this.successResponse.title === 'Warning') ? 'Adjust' : 'OK',
            hasCancel: false,
            onConfirm: () => {
                if(this.successResponse.title === 'Warning'){
                    this.openAdjustModal();
                }
            }
        });
    }

    mediaUploadDone($event) {
        let { uploaded_items, userFile } = $event;
        userFile.map((item, index)=>{
            item.size = +(uploaded_items[index].file.size);
        })
        this.current_message.msg_files.splice(1, 0,...userFile);
        this.current_message.msg_files[0] = {};
        if(this.messageFormState.showEditMessage){
            this.changeValues()
        }
    }

    setEditModalConfig(){
        this.sendMessageFormHtmlGenericModal.title = 'Edit message';
        this.sendMessageFormHtmlGenericModal.rightPrimaryBtnTxt = 'Save';
        this.sendMessageFormHtmlGenericModal.rightSecondaryBtnTxt= '';
        this.sendMessageFormHtmlGenericModal.leftSecondaryBtnTxt = '';
        this.sendMessageFormHtmlGenericModal.showCancel = true;
        this.messageFormState = {
            ...this.messageFormState,
            showSendMessageForm: false,
            showViewMessage: false,
            showEditMessage: true
        } 
    }

    setViewModalConfig(){
        this.sendMessageFormHtmlGenericModal.title = 'View Message';    
        this.sendMessageFormHtmlGenericModal.rightPrimaryBtnTxt = '';
        this.sendMessageFormHtmlGenericModal.leftSecondaryBtnTxt = 'Edit Message';
        this.sendMessageFormHtmlGenericModal.rightPrimaryBtnTxt = 'OK';
        // this.sendMessageFormHtmlGenericModal.rightSecondaryBtnTxt= '';
        this.sendMessageFormHtmlGenericModal.showCancel = false;
        this.messageFormState = {
            ...this.messageFormState,
            showSendMessageForm: false,
            showViewMessage: true,
            showEditMessage: false
        } 
    }

    setSendModalConfig(){
        this.sendMessageFormHtmlGenericModal.rightPrimaryBtnTxt = 'Send';
        this.sendMessageFormHtmlGenericModal.rightSecondaryBtnTxt= '';
        this.sendMessageFormHtmlGenericModal.leftSecondaryBtnTxt = '';
        this.sendMessageFormHtmlGenericModal.title = 'Send Message';
        this.messageFormState = {
            ...this.messageFormState,
            showSendMessageForm: true,
            showViewMessage: false,
            showEditMessage: false
        } 
    }

    fileDeleteDone($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.current_message.msg_files = this.current_message.msg_files.filter(r => (r.id !== $event.userFile.id));
        }
        if(this.messageFormState.showEditMessage){
            this.changeValues()
        }
    }

    @ViewChild('messageDetailsHtml') private messageDetailsHtmlGenericModal: IModalComponent;

    viewMessageModel(row) {
        this.resent = false;
        this.current_message = {...row};
        this.selectedProjects = this.current_message.projects.map(a => ({id: a.id, name: a.name}));
        if(row.is_sent){
            this.requestLoader = true;
            this.siteMessagingService.getCompanyMessageRecipients(this.employerId,row.id).subscribe((data:{success:boolean, deliveredTo:Array<recipientData>,readBy:Array<recipientData>})=>{
                if(data.success){
                    this.current_message.deliveredTo = data.deliveredTo;
                    this.current_message.readBy = data.readBy;
                    this.current_message.recipients = [...data.readBy,...data.deliveredTo].sort((a, b) => (a.first_name > b.first_name ? 1 : -1))
                    this.dummyList = [...this.current_message.recipients]
                    this.messageFormState.showSentMessage = true;
                    this.requestLoader = false;
                    this.messageDetailsHtmlGenericModal.open();
                }
            })
            return;
        }
        this.messageFormState.showViewMessage = true;
        this.selectedProjects = row.projects.map(p=>p.id);
        this.current_message.schedule_delivery = row.scheduled_at;
        this.last30_visited_user_checked = this.current_message.last30_visited_user;
        this.today_visited_user_checked = this.current_message.today_visited_user;
        this.all_projects_checked = row.projects.length == this.companyProjects
            .length;
        let dateTime: any = dayjs(+this.current_message.scheduled_at).tz(this.tz)
        .format('D-M-YYYY HH:mm:ss');
        let [date, time] = dateTime.split(" ");
        let [day, month, year] = date.split("-");
        let [hour, minute, sec] = time.split(":");
        this.time = {
            hour: +hour,
            minute: +minute
        };
        this.selectedDay = {
            day: +day,
            month: +month,
            year: +year
        };

        if (row.scheduled_at) {
            this.setViewModalConfig();
        }

        this.sendMessageFormHtmlGenericModal.open()
    }
    editMessageModel(row, fromHtml = true) {
        this.setEditModalConfig()
        
        this.messageFormState.showEditMessage = true
        this.messageFormState.showViewMessage = false;
        this.current_message = { ...row};
        if(!this.current_message.msg_files.length){
            this.current_message.msg_files = [{}]
        }
        this.selectedProjects = row.projects.map(p=>p.id);
        this.current_message.schedule_delivery = row.scheduled_at ? true : false;
        this.last30_visited_user_checked = this.current_message.last30_visited_user;
        this.today_visited_user_checked = this.current_message.today_visited_user;
        this.all_projects_checked = row.projects.length == this.companyProjects
            .length;
        let dateTime: any = this.dayjs(+this.current_message.scheduled_at)
            .format('D-M-YYYY HH:mm:ss');
        let [date, time] = dateTime.split(" ");
        let [day, month, year] = date.split("-")
        let [hour, minute, sec] = time.split(":")
        this.time = {
            hour: +hour,
            minute: +minute
        }

        this.selectedDay = {
            day: +day,
            month: +month,
            year: +year
        }

        if (fromHtml) {

            this.sendMessageFormHtmlGenericModal.open()
        }
    }

    isValidNumber(value: any): boolean {
        return typeof value === 'number' && !isNaN(value);
    }

    getReadRate(row) {
        let readBy = 0;
        for (let recipient of row.recipients_array) {
            if (recipient.read_at) {
                readBy += 1
            }
        }
        return readBy;

    }

    getMarkMessageAt(item, type) {
        if (type === 'Accepted' && this.current_message.should_accepted) {
            return `${type}: ${this.dayjs(item.accepted_at).format(AppConstant.fullDateTimeFormat)}`;
        }
        let additionalStr = (!this.current_message.should_accepted) ?
            '&#10;No acceptance required' : '';
        return `${type}: ${this.dayjs(item.read_at).format(AppConstant.fullDateTimeFormat)}${additionalStr}`;
    }

    getUserEmployer(user) {
        return (user && user.parent_company && user.parent_company.name) ?
            `(${user.parent_company.name})` : '';
    }

    getMessageMarkTick(recipient, read_by_recipients, accepted_by_recipients) {
        let acceptedItem = accepted_by_recipients.find(accepted_by =>
            accepted_by.id == recipient.id);
        let readItem = read_by_recipients.find(read_by => read_by.id ==
            recipient.id);
        let htmlContent = `<span></span>`;
        if (acceptedItem && this.current_message.should_accepted) {
            let readTitle = (readItem) ?
                `${this.getMarkMessageAt(readItem, 'Read')}&#10;` : '';
            htmlContent =
                `<span><i class="fas fa-check-double" title="${readTitle + this.getMarkMessageAt(acceptedItem, 'Accepted')}"></i></span>`;
        } else if (readItem) {
            htmlContent =
                `<span><i class="fas fa-check" title="${this.getMarkMessageAt(readItem, 'Read')}"></i></span>`;
        }

        return this.sanitizer.bypassSecurityTrustHtml(htmlContent);
    }

    hasMarkedAsAction(recipient, read_by_recipients, accepted_by_recipients) {
        let acceptedItem = accepted_by_recipients.find(accepted_by =>
            accepted_by.id == recipient.id);
        let readItem = read_by_recipients.find(read_by => read_by.id ==
            recipient.id);
        return (acceptedItem || readItem) ? true : false;
    }

    getResendContent(recipient, resend_logs) {
        let titleArr = [];
        (resend_logs || []).map(log => {
            if (log.id == recipient.id) {
                titleArr.push(
                    `Resent: ${this.dayjs(log.resend_at).format(AppConstant.fullDateTimeFormat)}`
                );
            }
            return log;
        });

        let title = (titleArr.length) ? titleArr.join('&#10;') :
            'Resend Message';
        return `<img style="width: 21px;" src="/images/resend.png" alt="Resend Message" title="${title}">`;
    }

    selectUsers(event) {
        if (!event.target.checked) {
            this.selectedProjects = [];
        }else if (this.all_projects_checked) {
            this.selectedProjects = [...this.companyProjects.map(a => a.id)]
        }
        if(this.messageFormState.showEditMessage){
            this.changeValues()
        }
      
    }
    onFilterSelection(data) {
        this.filter.user = data['Author'].map(a => +a.id);
        this.page.pageNumber = 0;
        this.pageCallback({
            offset: 0
        }, true);
    }
    searchFunction(data) {
        this.filter.search = data.search;
        this.pageCallback({
            offset: 0
        }, true);
    }
    renderFilterData() {
        return [{
            name: 'Author',
            list: this.user_filter_data,
            enabled: true,
            state: false,
        }];
    }


    reorderUsersArray() {
        this.companyProjects = [...this.companyProjects.sort((a, b) => this
            .selectedProjects.includes(a.id) ? -1 : 1)];
        if(this.messageFormState.showEditMessage){
            this.changeValues()
        }
        if(this.selectedProjects.length == this.companyProjects.length){
            this.all_projects_checked = true;
        }else{
            this.all_projects_checked = false;
        }
    }

    confirmSendMessage(form, $modalLoader ? , cb ? ) {
        let totalFileSize = this.current_message.msg_files.reduce((total,file,index)=>{
            if(file && file?.id){
                total += +file.size;
            }
            return total;
        },0)    
        if(totalFileSize > this.maxFileSize){
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle:'Alert',
                title: 'The total size of the uploaded files exceeds 10 MB. Please review and consider optimizing the size or number of files accordingly.',
                confirmLabel:'ok',
                hasCancel: false,
            });
            return;
        }
        if (!form.valid || this.selectedProjects.length == 0 || (this
                .current_message.schedule_delivery && this.selectedDay == null
            )) {
            this.confirmationModalRef.openConfirmationPopup({
                    headerTitle:'Alert',
                    title: 'Please fill all the required fields.',
                    confirmLabel:'ok',
                    hasCancel: false,
            });
            this.showError = true;
            return;
        }
        if (this.current_message.schedule_delivery) {
            let dt =
                `${+this.selectedDay.day}-${+this.selectedDay.month}-${+this.selectedDay.year} ${+this.time.hour||'00'}:${+this.time.minute||'00'}:00`;
            this.current_message.scheduled_at = dayjs(dt, 'D-M-YYYY HH:mm:ss').tz(this.tz, true).valueOf();
            this.current_message.is_sent = false;
            if (dayjs().tz(this.tz, true).valueOf() > this.current_message
                .scheduled_at) {
                this.confirmationModalRef.openConfirmationPopup({
                    headerTitle:'Alert',
                    title: 'Choose a Future Date and Time.',
                    confirmLabel:'ok',
                    hasCancel: false,
                });
                return;
            }
        }
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Send Message',
            title: 'Are you sure you wish to send this message?',
            confirmLabel: 'Send',
            onConfirm: () => {
                this.sendMessageRequest();
            },
            onClose: () => {
                return;
            }
        });
    }
    inputChange(e) {
        let target = e.target;
        if (+target.value > +target.max) {
            this.time[target.name] = +target.max;
            target.value = +target.max;
            return;
        }
        if (+target.value < +target.min) {
            this.time[target.name] = +target.min;
            target.value = +target.min;
            return;
        }
        this.time[target.name] = +target.value;
        if(this.messageFormState.showEditMessage){
            this.changeValues()
        }
    }


    deleteCompanyMessage(row) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle:"Delete Message",
            title: `Are you sure you want to delete message ${row.record_id}?`,
            onConfirm: () => {
                this.requestLoader = true
                this.siteMessagingService.deleteCompanyMessage({
                    message_id:row.id,
                }, this.employerId).subscribe((data: any) => {
                    this.requestLoader = false;
                    this.reloadTable()
                    return;
                });
            },
            onClose: () => {
                console.log('user canceled confirmation modal');
            },
        });
    };
    changeValues() {
        const {msg_title, msg_text} = this.sendMessageForm.controls;
        if(!msg_title.value.trim().length || !msg_text.value.trim().length || !this.selectedProjects.length) {
            return;
        }
    }
    isNotExpired(scheduled_at) {
        return dayjs().tz(this.tz, true).valueOf() < +scheduled_at
    }
    changeRelatedValues() {
        if (!this.current_message.schedule_delivery) {
            this.current_message.is_sent = true;
            this.current_message.scheduled_at = null;
        }
    }
    

    onSort($event){
        let {sorts} = $event;
        let{ dir, prop } = sorts[0];
        this.sorts = { dir, prop };
        this.pageCallback({ offset: 0 }, true);
    }
    getOperatives(today_visited_user: boolean, last30_visited_user: boolean): string {
        if (today_visited_user && last30_visited_user) {
            return 'All operatives active today and in the last 30 days';
        } else if (today_visited_user) {
            return 'All operatives active today';
        } else if (last30_visited_user) {
            return 'All operatives active in the last 30 days';
        } else {
            return 'No operatives found';
        }
    }

    getOperativeReadStatus(recipientData: recipientData){
        return `${recipientData.accepted_at ? `Accepted ${dayjs.unix(+recipientData.accepted_at).tz(this.tz).format(this.fullDateTimeFormat_without_ss)}`: `Read ${dayjs.unix(+recipientData.read_at).tz(this.tz).format(this.fullDateTimeFormat_without_ss)}`}`;
    }
     
    resendMessage(current_message){
        let request = {userIds:[...current_message.deliveredTo.map(a=>a.user_ref)]}
        this.requestLoader  = true;
        this.siteMessagingService.resendCompanyMessage(current_message.id,this.employerId,request).subscribe((res:{success?:boolean, error?:boolean, message:any})=>{
            if(res.success){
                this.successResponse.message = res.message;
                this.messageTitle = 'Success';
                this.resent = true;
            }
            if(res.error){
                this.messageTitle = 'Error';
                this.successResponse = res.message;
                this.resent = false;
            }
            this.requestLoader  = false;
            this.modalService.dismissAll();
            this.messageFormState.showSentMessage = false;
            this.showSuccessWarningConfirmationModal();
        })
    }
    compareFn(a,b){
        return a.id === b.id;
    }
    closeModal(event) {
        event.closeFn();
    }
    openAdjustModal(){
        this.messageFormState.showSendMessageForm = true;
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        switch(entry.key) {
            case 'view_message':
                this.viewMessageModel(row);
                break;
            case 'edit_message':
                this.editMessageModel(row);
                break;
            case 'delete_message':
                this.deleteCompanyMessage(row);
                break;
            default:
                console.warn(`Unhandled action key: ${entry.key}`);
        }
    }
}

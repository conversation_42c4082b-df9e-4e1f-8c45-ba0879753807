<div [ngClass]="{'d-flex': !isProjectPortal}">
    <company-side-nav *ngIf="!isProjectPortal" [companyResolverResponse]="companyResolverResponse" [employer]="employer" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
        <project-header [isCompanyHeader]="true" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="col-sm-12 my-3 outer-border" *ngIf="!loadingSiteMessaging">
                <div class="col-sm-12 outer-border-radius">
                    <div class="d-flex flex-wrap justify-content-between mb-2 pb-2">
                        <h5>Total messages sent<small class="ml-1">({{ page.totalElements }})</small></h5>
                        <button class="btn btn-sm btn-im-send btn-brandeis-blue horizontal-center pointer-cursor" (click)="sendMessagePopup()">
                            <span class="material-symbols-outlined">
                                send
                            </span>
                            Send Message
                        </button>
                    </div>
                    <search-with-filters [filterData]="filterData" (filterEmitter)="onFilterSelection($event)" (searchEmitter)="searchFunction($event)"></search-with-filters>

                    <div class="company-message_table table-responsive-sm ngx-datatable-custom">
                        <ngx-datatable #table [columns]="[
                            {name:' Message #', prop: 'record_id', minWidth: '100', sortable: true, headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'A table-cell-text align-items-start'},
                            {name:'Title', prop: 'msg_title', minWidth: '100', sortable: true, headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'A table-cell-text align-items-start', cellTemplate: titleColumn},
                            {name:'Author', prop: 'sender.name', minWidth: '100', sortable: false, headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'A table-cell-text align-items-start', cellTemplate: authorColumn},
                            {name:'Date sent', prop: 'createdAt', minWidth: '100', sortable: false, headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'A table-cell-text align-items-start',cellTemplate: createdAt},
                            {name:'Read rate', prop: 'readRate', minWidth: '100', sortable: false, headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'A table-cell-text align-items-start',cellTemplate: readRate},
                            {name:'Action', prop: 'scheduled_at', minWidth: '100', sortable: false, headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'A table-cell-text align-items-start no-ellipsis',cellTemplate: actionColumn}
                            ]" 
                            class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h" [rows]="company_messages" [footerHeight]="40"
                            [scrollbarV]="true"
                            [virtualization]="false"
                            [loadingIndicator]="loadingInlineCompanyMsg"
                            [columnMode]="'force'" [rowHeight]="'60px'" [externalPaging]="true" [count]="page.totalElements"
                            [offset]="page.pageNumber" [limit]="page.size" (page)="pageCallback($event, true)"
                            [externalSorting]="true" (sort)="onSort($event)" [sorts] = "[sorts]"
                        >
                        <ng-template #titleColumn let-row="row" let-value="value">
                            <span appTooltip>{{ value }}</span>
                        </ng-template>
                        <ng-template #authorColumn let-row="row" let-value="value">
                            <span appTooltip>{{ value }}</span>
                        </ng-template>
                        <ng-template #createdAt let-row="row" ngx-datatable-cell-template>
                            <div class="d-flex flex-column" *ngIf='row?.is_sent '>
                                <span>
                                    {{ (dayjs(row.createdAt).format(AppConstant.defaultDateFormat))}}
                                </span>
                                <span
                                    class="d-block font-disabled">{{ (dayjs(row.createdAt).format(AppConstant.defaultTimeFormat))}}</span>
                            </div>
                            <div class="d-flex flex-column" *ngIf='!row?.is_sent'>
                                <span class="scheduled">Scheduled</span> <span
                                    class="d-block font-disabled">{{dayjs(row.scheduled_at).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS)}}</span>
                            </div>
                        </ng-template>
                        <ng-template #readRate let-row="row" ngx-datatable-cell-template>
                            <div *ngIf="row?.is_sent">
                                <span>
                                    {{row.readRatio != null ? row.readRatio : '' }}
                                </span>
                                <span
                                    [ngStyle]="{'color': row.readRate < 50 ? '#F20000' : row.readRate < 75  ? '#EDB531' : '#00992B', 'font-weight': 500}">
                                    ({{ row.readRate != null ? row.readRate + '%' : '' }}) </span>
                            </div>
                            <div *ngIf="!row?.is_sent">
                                <span>N/A</span>
                            </div>
                        </ng-template>
                        <ng-template #actionColumn let-row="row" ngx-datatable-cell-template>
                            <button-group
                                [buttons]="rowButtonGroup"
                                [btnConditions]="[true, row.scheduled_at && !row.is_sent, row.scheduled_at && !row.is_sent]"
                                (onActionClick)="rowBtnClicked($event, row)">
                            </button-group>
                        </ng-template>
                    </ngx-datatable>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<i-modal #sendMessageFormHtml  size="lg" (onClickRightPB)="messageFormState.showViewMessage ? closeModal($event) : confirmSendMessage(sendMessageForm)" (onClickLeftSB)="setEditModalConfig()">
    <form [ngStyle]="{color: messageFormState.showViewMessage ? 'grey':'' }" *ngIf="current_message" novalidate #sendMessageForm="ngForm">
        <ng-container *ngIf="messageFormState.showSendMessageForm || messageFormState.showViewMessage || messageFormState.showEditMessage">
            <div class="form-group">
                <label><strong>Message Title:</strong><small class="required-asterisk">*</small></label>
                <div class="input-group mb-3">
                    <input (ngModelChange)='messageFormState.showEditMessage &&  changeValues()' [disabled]="messageFormState.showViewMessage" type="text" class="form-control" #msgTitle="ngModel" ng-value="current_message.msg_title" placeholder="Message Title" name="msg_title" required [(ngModel)]="current_message.msg_title">
                </div>
            </div>

            <div class="form-group mb-0">
                <label><strong>Contents:</strong><small class="required-asterisk">*</small></label>
                <div class="input-group mb-0 d-flex flex-column">
                    <textarea class="form-control w-100" maxlength="1500" name="msg_text" [(ngModel)]="current_message.msg_text" ng-value="current_message.msg_text" style="min-height: 110px;" placeholder="Message Content" #msgText="ngModel" required [disabled]="messageFormState.showViewMessage" (ngModelChange)='messageFormState.showEditMessage &&  changeValues()'></textarea>
                    <div>
                        <span class="float-right small-font mt-1 text-muted">Maximum Characters - 1500</span>
                    </div>
                </div>
            </div>

            <div *ngIf="!messageFormState.showViewMessage" class="form-group">

                <label><strong>Add attachment:</strong></label>
                <div *ngIf="!messageFormState.showViewMessage" class="col-md-12 p-0 d-flex flex-column flex-wrap">
                    <div *ngFor="let c of current_message.msg_files" class="flex-grow-1 p-0 mt-2" [ngClass]='{"col-md-12": c.id, "col-md-12": !c.id}'>
                        <div>
                            <file-uploader-v2 [disabled]="false" [init]="c" [category]="'message-files'" (uploadDone)="mediaUploadDone($event)" [allowedMimeType]="allowedMime" [showHyperlink]="true" [showFileName]="true" (deleteFileDone)="fileDeleteDone($event)" [showDeleteBtn]="true" [showFileFullName]="true" [showThumbnail]="false" [maxFileSize]='maxFileSize' 
                            [multipleUpload]="true" [showSize] = "true">
                            </file-uploader-v2>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="messageFormState.showViewMessage && current_message.msg_files.length">
                <div>
                    <label class="font-weight-bold">Reference Documents</label>
                </div>
                <div class="form-group p-0 custom-input-div mt-0 form-control">
                    <div class="input-group p-2">
                        <ng-container *ngFor="let file of current_message.msg_files">
                            <a class="text-info" target="_blank" [href]="file.file_url">{{file.name}}</a>
                        </ng-container>
                    </div>
                </div>
            </div>


            <div class="form-group">
                <label class="justify-content-between mb-0"><span><strong>Acceptance:</strong></span>
                    <div class="custom-control custom-switch d-inline-block">
                        <input [disabled]="messageFormState.showViewMessage" type="checkbox" class="custom-control-input" id="acceptance" name="acceptance" [checked]="true" (ngModelChange)='messageFormState.showEditMessage &&  changeValues()' [(ngModel)]="current_message.should_accepted">
                        <label class="custom-control-label" for="acceptance"></label>
                    </div>
                </label>
                <div class="mb-3">
                    <div class="mb-1">
                        <label for="acceptance">Would you like add a tick box for user to confirm that they have read and understood the message?</label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="justify-content-between mb-0"><span><strong>Send message as email:</strong></span>
                    <div class="custom-control custom-switch d-inline-block">
                        <input (ngModelChange)='messageFormState.showEditMessage &&  changeValues()' type="checkbox" [disabled]="messageFormState.showViewMessage" class="custom-control-input" id="messageAsEmail" name="messageAsEmail" [checked]="true" [(ngModel)]="current_message.mail_alert">
                        <label class="custom-control-label" for="messageAsEmail">

                        </label>
                    </div>
                </label>
                <div class="mb-3">
                    <div class="mb-1">
                        <label for="message_as_email">Messages will be sent as push notification to the mobile app, would you like to send by email in addition?</label>
                    </div>
                </div>
            </div>

            <!-- <div class="form-group">
                <label class="justify-content-between mb-0"><span><strong>Schedule:</strong></span>
                    <div class="custom-control custom-switch d-inline-block">
                        <input (ngModelChange)='messageFormState.showEditMessage &&  changeValues()' type="checkbox" [disabled]="messageFormState.showViewMessage" class="custom-control-input" id="scheduleDelivery" name="scheduleDelivery" [checked]="true" [(ngModel)]="current_message.schedule_delivery" (ngModelChange)="changeRelatedValues()" (change)='changeValues()'>
                        <label class="custom-control-label" for="scheduleDelivery">

                        </label>
                    </div>
                </label>
                <div class="mb-3">
                    <div class="mb-1">
                        <label for="scheduleDelivery">Select a time and date to schedule the message.</label>
                    </div>
                </div>
            </div> -->
            <div class="form-group site-messaging-datepicker" *ngIf="current_message?.schedule_delivery">
                <div class="input-group d-inline-flex px-1">
                    <input [disabled]="messageFormState.showViewMessage" #sed="ngbDatepicker" [(ngModel)]="selectedDay" placement="bottom-right" class="form-control h-40" name="date" ng-value="selectedDay" (ngModelChange)='messageFormState.showEditMessage &&  changeValues()' ngbDatepicker placeholder="dd-mm-yyyy" [minDate]="minDate" [maxDate]="maxDate" readonly [attr.required]="scheduleDelivery ? true : null">
                    <div class="input-group-append">
                        <button [disabled]="messageFormState.showViewMessage" (click)="sed.toggle()" class="btn btn-outline-secondary calendar" type="button">
                            <span class="material-symbols-outlined">
                                calendar_month
                            </span>
                        </button>
                    </div>
                </div>

                <div class="justify-content-between horizontal-center my-3">
                    <label for="scheduleDelivery">Select Delivery Time:<small class="required-asterisk">*</small></label>
                    <div style="gap: 5px;" class="horizontal-center">
                        <input [disabled]="messageFormState.showViewMessage" type="number" name="hour" class="form-control w-70 time-input" placeholder="00"  (input)="inputChange($event)" [value]="time.hour" max="23" min="00" />
                        :
                        <input [disabled]="messageFormState.showViewMessage" type="number" name="minute" style="text-align: center;" class="form-control w-70 time-input" placeholder="00"  (input)="inputChange($event,'minute')" max="59" [value]="time.minute" min="00" />
                    </div>
                </div>
                <div class="alert alert-danger" [hidden]="!showError && current_message?.selectedDay !== null">Select date and time.</div>
            </div>

            <div class="form-group">
                <label><strong>Recipients:</strong><small class="required-asterisk">*</small></label>

                <div class="input-group">
                    <div class="custom-control custom-checkbox mb-1">
                        <input type="checkbox" class="custom-control-input" id="today_visited_user" name="today_visited_user" [(ngModel)]="today_visited_user_checked" [disabled]="messageFormState.showViewMessage" (ngModelChange)='messageFormState.showEditMessage &&  changeValues()'>
                        <label class="custom-control-label" for="today_visited_user">All on-site operatives</label>
                    </div>
                </div>

                <div class="input-group">
                    <div class="custom-control custom-checkbox mb-1">
                        <input type="checkbox" class="custom-control-input" id="last30_visited_user" name="last30_visited_user" [(ngModel)]="last30_visited_user_checked" [disabled]="messageFormState.showViewMessage" (ngModelChange)='messageFormState.showEditMessage &&  changeValues()'>
                        <label class="custom-control-label" for="last30_visited_user">All operatives active in the past 30 days</label>
                    </div>
                </div>

                <div class="input-group mb-3">
                    <div class="custom-control custom-checkbox mb-1">
                        <input type="checkbox" class="custom-control-input" id="all_projects_checked" name="all_projects_checked" [(ngModel)]="all_projects_checked" (change)="selectUsers($event)" [disabled]="messageFormState.showViewMessage">
                        <label class="custom-control-label" for="all_projects_checked">Send to all projects</label>
                    </div>
                </div>
            </div>
            <div>
                <ng-select #projects required [disabled]="messageFormState.showViewMessage" [searchable]="false" (change)="reorderUsersArray()" [closeOnSelect]="false" name="selectUserDropdown" placeholder="Select projects" class="dropdown-list sm-select filter-v2-select h-auto w-100" bindLabel="name" bindValue="id" [(ngModel)]="selectedProjects" [items]='companyProjects' [multiple]="true">
                    <ng-template ng-header-tmp>
                        <div style="width:100%; max-width:100%;" class="text-search horizontal-center px-2">
                            <span class="material-symbols-outlined font-small">search</span>
                            <input style="width: 100%; line-height: 24px" placeholder="Search" class="ngx-search " type="search" (input)="projects.filter($event.target.value)" />
                        </div>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" class="horizontal-center" let-item$="item$" let-index="index" let-search="searchTerm">
                        <label>
                            <input type="checkbox" [checked]="item$.selected" class="mr-1" />
                            <span>{{item.name}}</span>
                        </label>
                    </ng-template>
                </ng-select>
                <div [hidden]="!(showError && !selectedProjects.length)" class="input-group mb-3 overflow-auto">
                    <div class="alert alert-danger mb-0">Select atleast 1 project.</div>
                </div>
            </div>
            <input type="hidden" name="current_message" id="current_message" [(ngModel)]="current_message" />
        </ng-container>
    </form>
    <block-loader [show]="(false)" #modalLoader></block-loader>

</i-modal>

<i-modal #messageDetailsHtml title="Message Details" size="lg" rightSecondaryBtnDisabled="true" [showCancel]="false" rightPrimaryBtnTxt="OK" modalBodyClass="mb-3" (onClickRightPB)="closeModal($event)">
    <div *ngIf="current_message && messageFormState.showSentMessage">
        <div class="form-group">
            <label><strong>Message Title:</strong></label>
            <div class="input-group mb-3">
                <input type="text" class="form-control" #msgTitle="ngModel" ng-value="current_message.msg_title" placeholder="Message Title"
                       name="msg_title" disabled [(ngModel)]="current_message.msg_title">
            </div>
        </div>

        <div class="form-group">
            <label><strong>Contents:</strong></label>
            <div class="input-group mb-3">
                <div class="form-control" style="background-color: white;color:#9d9d9d; height: fit-content;">
                    <span style="white-space: pre-line;" [innerHTML] = "current_message.msg_text | urlify" class="d-block"></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label><strong>Date Sent:</strong></label>
            <div class="input-group mb-3">
                <input type="text" class="form-control"
                       name="msg_title" disabled [value]="dayjs(current_message.createdAt).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS)">
            </div>
        </div>
        <div *ngIf="current_message.msg_files.length">
            <div>
                <label class="font-weight-bold">Reference Documents</label>
            </div> 
            <ng-container *ngFor="let file of current_message.msg_files">
                <div class="form-group p-2 custom-input-div mt-0 form-control"> 
                    <a class="d-block text-info" target="_blank" [href]="file.file_url">{{file.name}}</a>
                </div>
            </ng-container>
        </div>
        <div class="form-group">
            <label class="font-weight-bold">Projects</label>
            <ng-select #projects required [readonly]="true" [searchable]="false" (change)="reorderUsersArray()" [closeOnSelect]="false" name="selectUserDropdown" placeholder="Select projects"
             class="dropdown-list sm-select filter-v2-select h-auto w-100" bindLabel="name" bindValue="id" [(ngModel)]="selectedProjects" [items]='companyProjects || []' [compareWith]="compareFn" [multiple]="true">
                <ng-template ng-header-tmp>
                    <div style="width:100%; max-width:100%;" class="text-search horizontal-center px-2">
                        <span class="material-symbols-outlined font-small">search</span>
                        <input style="width: 100%; line-height: 24px" placeholder="Search" class="ngx-search " type="search" (input)="projects.filter($event.target.value)" />
                    </div>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" class="horizontal-center" let-item$="item$" let-index="index" let-search="searchTerm">
                    <label>
                        <input type="checkbox" [checked]="item$.selected" class="mr-1" />
                        <span>{{item.name}}</span>
                    </label>
                </ng-template>
            </ng-select>
        </div>
        <div class="form-group">
            <label for="" class="font-weight-bold">Recipients <span>({{ getOperatives(current_message.today_visited_user, current_message.last30_visited_user) }})</span></label>
            <ng-select [virtualScroll]="true" class="dropdown-list sm-select filter-v2-select h-auto w-100  operative-dropdown" dropdownPosition="bottom" [clearable]="false" [(ngModel)]="dummyList" [items]="current_message.recipients" [closeOnSelect]="false" [searchable]="false" [compareWith]="compareFn">
               <ng-template  ng-multi-label-tmp>
                    <span style="display:block;" class="horizontal-center" class="ng-placeholder custom-placeholder text-truncate">
                        <span>{{current_message.recipients.length}} Operatives <span class="disabled">(Read:{{current_message?.readBy?.length}}/{{current_message?.recipients?.length}})</span></span>
                    </span>
                </ng-template>
                <ng-template ng-option-tmp disabled let-item="item" class="horizontal-center" let-item$="item$" let-index="index" let-search="searchTerm">
                    <label class="non-selectable-option">
                        <span>{{item.first_name}} {{item.last_name}}<span *ngIf="item.read_at || item.accepted_at" class="disabled ml-2">({{getOperativeReadStatus(item)}})</span></span>
                    </label>
                </ng-template>
            </ng-select>
        </div>
    </div>
</i-modal>

<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

<block-loader [show]="(requestLoader || loadingSiteMessaging)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
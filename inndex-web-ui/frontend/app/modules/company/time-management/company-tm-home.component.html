<div class="d-flex" id="wrapper">
	<company-side-nav [employer]="employer" [companyResolverResponse]="companyResolverResponse"></company-side-nav>
	<div class="w-100 px-3 detail-page-header-margin" [ngClass]="{'ml-fix': !is_mobile_nav}">
		<div class="row">
			<project-header [isCompanyHeader]="true" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
			<ul ngbNav #nav="ngbNav" [(activeId)]="active" (activeIdChange)="tabChange($event)" class="nav-tabs n-tab" style="display: none !important;">
				<li [ngbNavItem]="'time'" [domId]="'time'" class="p-0" [class.mr-n1]="(active !== 'analytics')">
					<a ngbNavLink class="default">Time Management</a>
					<ng-template ngbNavContent>
						<div class="col-sm-12 outer-border-radius my-3">
							<div class="col-12 px-0 pb-3"  *ngIf="!time_tab_loading">
								<search-with-filters [filterData]="filterData" (searchEmitter)="searchFunction($event, true)" (filterEmitter)="onFilterSelection($event, true)"></search-with-filters>
								<div class="clearfix"></div>
								<ngx-datatable
                      				#usersTable
                      				[scrollbarV]="true"
                      				[virtualization]="false"
									[loadingIndicator]="loadingInlineTmHome"
                      				[columnMode]="'force'"
                      				[footerHeight]="30"
                      				[headerHeight]="40"
                      				[rowHeight]="'auto'"
                      				[externalPaging]="true"
                      				[externalSorting]="true"
                      				[count]="timeLogPage.totalCount"
                      				[offset]="timeLogPage.pageNumber"
                      				[limit]="timeLogPage.pageSize"
                      				(page)="timePageCallback($event, true)"
                      				[sortType]="'single'"
                      				(sort)="timeSortCallback($event)"
                      				[rows]="timeLogPage.users"
                      				class="bootstrap table table-v2 ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                      				[columns]="[
                						{name:'User ID', prop: 'id', width: 20, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', minWidth:'70'},
                						{name:'First Name', prop: 'first_name', headerClass: 'py-2 font-weight-bold', cellTemplate: firstNameCell, cellClass: 'py-1', minWidth:'100'},
                						{name:'Last Name', prop: 'last_name', headerClass: 'py-2 font-weight-bold', cellTemplate: lastNameCell, cellClass: 'py-1', minWidth:'100'},
                						{name:'Most Recent Project', prop: 'recent_project', sortable: false, headerClass: 'py-2 font-weight-bold', cellTemplate: recentProjectCell, cellClass: 'py-1', minWidth:'100'},
                						{name:'No. of Breaches', prop: 'fatigue_total', cellTemplate: fatigueCell, sortable: false, width: 80, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', minWidth:'100'},
                						{name:'View Profile', cellTemplate: actionBtnCell, sortable: false, width: 20, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 no-ellipsis', minWidth:'100'}
            						]">
									<ng-template #firstNameCell let-value="value">
                                    	<span appTooltip>{{ value }}</span>
                                	</ng-template>
									<ng-template #lastNameCell let-value="value">
                                	    <span appTooltip>{{ value }}</span>
                                	</ng-template>
									<ng-template #recentProjectCell let-value="value">
                                	    <span appTooltip>{{ value }}</span>
                                	</ng-template>
                					<ng-template #fatigueCell let-row="row" let-value="value">
                					  {{value}} <span *ngIf="row.fatigue_pending" class="text-danger">(Open: {{row.fatigue_pending}})</span>
                					</ng-template>
                					<ng-template #actionBtnCell let-row="row" let-value="value">
									  	<button-group
                                		    [buttons]="baseButtonConfig"
                                		    (onActionClick)="rowBtnClicked($event, row)">
                                		</button-group>
                					</ng-template>
              					</ngx-datatable>
							</div>
						</div>
					</ng-template>
				</li>
				<li [ngbNavItem]="'fatigue'" [domId]="'fatigue'" class="p-0 " [class.mr-n1]="(active !== 'time')" [disabled]="true">
					<a ngbNavLink class="default">Fatigue</a>
					<ng-template ngbNavContent>
						<!-- Fatigue content here -->
					</ng-template>
				</li>
				<li [ngbNavItem]="'analytics'" [domId]="'analytics'" class="p-0" [class.mr-n1]="(active !== 'fatigue')" [disabled]="true">
					<a ngbNavLink class="default">Analytics</a>
					<ng-template ngbNavContent>
						<!-- Analytics content here -->
					</ng-template>
				</li>
			</ul>
			<!-- <div [ngbNavOutlet]="nav" class="col-sm-12 my-2 pt-2 nav-panel" style="margin-top: 20px !important;"></div> -->
			<div [ngbNavOutlet]="nav" class="col-sm-12 my-3 outer-border nav-panel" style="margin-top: 20px !important;"></div>
		</div>
	</div>
</div>

<i-modal #viewUserModalHtml title="User Information" size="xl" [showCancel]="false" rightPrimaryBtnTxt="Done" (onClickRightPB)="closeViewUserModal($event)">
	<div class="row" *ngIf="showViewUserModal">
		<div class="col-12 d-flex flex-row pt-3">
			<div class="round-profile-img" [style.background-image]="'url(' + selectedUser.profile_pic + ')'"></div>
			<div class="pl-4 p-2">
				<table class="table table-sm table-borderless info-list">
					<tbody>
						<tr>
							<th>Name:</th>
							<td>{{ selectedUser.name}}</td>
						</tr>
						<tr>
							<th>innDex ID:</th>
							<td>{{ selectedUser.id}}</td>
						</tr>
						<tr>
							<th>No. of breaches:</th>
							<td>{{ selectedUser.fatigue_total}}</td>
						</tr>
						<tr>
							<th>No. of open breaches:</th>
							<td>{{ selectedUser.fatigue_pending}}</td>
						</tr>
						<!--<tr>
              				<th>Avg. hours worked p/w:</th>
              				<td>{{ selectedUser.id}}</td>
            			</tr>-->
					</tbody>
				</table>
			</div>
		</div>

		<ul ngbNav #modalNav="ngbNav" (activeIdChange)="modalTabChange($event)" class="nav-tabs n-tab d-flex justify-content-center border-bottom-1" style="display: none !important;">
			<li [ngbNavItem]="'logs'" [domId]="'logs'" class="p-0">
				<a ngbNavLink class="default"><i class="fas fa-clock"></i> </a>
				<ng-template ngbNavContent>
					<company-user-time-logs [employerId]="employer?.id" [userId]="selectedUser.id" [userFullName]="selectedUser.name"></company-user-time-logs>
				</ng-template>
			</li>
			<li [ngbNavItem]="'fatigue-tab'" [domId]="'fatigue-tab'" class="p-0" [disabled]="true">
				<a ngbNavLink class="default"> <i class="fas fa-ticket-alt"></i> </a>
				<ng-template ngbNavContent>
					<!-- fatigue-tab content here -->
				</ng-template>
			</li>
			<li [ngbNavItem]="'analytics-tab'" [domId]="'analytics-tab'" class="p-0" [disabled]="true">
				<a ngbNavLink class="default"> <i class="fas fa-chart-line"></i> </a>
				<ng-template ngbNavContent>
					<!-- Analytics content here -->
				</ng-template>
			</li>
		</ul>
		<div [ngbNavOutlet]="modalNav" class="col-sm-12 mt-2 pt-2 nav-panel"></div>

	</div>
	<pre class="small d-none">{{ selectedUser | json }}</pre>
</i-modal>
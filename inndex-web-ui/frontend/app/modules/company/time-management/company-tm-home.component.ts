import {Component, OnInit, TemplateRef, ViewChild} from "@angular/core";
import {
    AuthService,
    CompanyTimeLogService,
    CompanyTimeManagementPage,
    CreateEmployer,
    ToastService,
    HttpService,
    Project,
    UserService
} from "@app/core";
import {ActivatedRoute} from "@angular/router";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import {ActionBtnEntry, AssetsUrl, IModalComponent} from "@app/shared";
import * as dayjs from "dayjs";
import {AppConstant} from "@env/environment";
import {DatatableComponent} from "@swimlane/ngx-datatable";
import {debounceTime, distinctUntilChanged} from "rxjs/operators";
import {Subject} from "rxjs";
import { filterData } from "@app/core";

@Component({
    selector: 'company-tm-home',
    templateUrl: 'company-tm-home.component.html',
    styleUrls:['./company-tm-home.component.scss']
})
export class CompanyTmHomeComponent implements OnInit {
    dbDateFormat: string = AppConstant.apiRequestDateFormat;
    timeFormat: string = AppConstant.defaultTimeFormat;
    time_tab_loading: boolean = false;

    timeLogPage: CompanyTimeManagementPage = new CompanyTimeManagementPage;
    selectedUser: any;

    searchInputChanged: Subject<string> = new Subject<string>();
    filter: {
        searchText?: string;
        project?: number[];
    } = {
        project:[],searchText:""
    };
    is_mobile_nav: boolean = false;
    filterData:filterData[] = [
        {
            name:'project',
            list:this.timeLogPage.projects,
            enabled:false,
            state:false
        }
    ];
    renderFilterData:boolean= false;
    showViewUserModal:boolean = false;
    loadingInlineTmHome:boolean = false;
    baseButtonConfig: Array<ActionBtnEntry> = [
        {
            key: 'view',
            label: '',
            title: 'View',
            mat_icon: 'search',
        },       
    ];

    constructor(
        private authService: AuthService,
        private userService: UserService,
        private toastService: ToastService,
        private companyTimeLogService: CompanyTimeLogService,
        private activatedRoute: ActivatedRoute,
        private modalService: NgbModal,
        private httpService: HttpService,
    ) {
        this.is_mobile_nav = this.httpService.isMobileDevice();
    }

    logo_img_link: string = '/images/project-placeholder.png';

    active: string = 'time';
    employer: CreateEmployer;
    companyResolverResponse: any;
    ngOnInit() {
        this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
        if(this.employer.logo_file_url){
            this.logo_img_link = this.employer.logo_file_url;
        }
        this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        this.tabChange(this.active);
        this.bindSearchInputEvent();
    }

    tabChange(tabId) {
        // this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { view: tabId }, queryParamsHandling: 'merge' });
        console.log('tabChange to', tabId)
        this.active = tabId;
        if(tabId === 'time'){
            this.initTimeTab()
        }
    }

    unix(n: number| string) {
        return dayjs.unix(+n).format(this.timeFormat);
    }

    @ViewChild('usersTable') usersTable: DatatableComponent;

    initTimeTab(pageNumber = 0, sortInfo = {}, isPageChange?: boolean){
        let q = this.filter.searchText || '';
        let projectRef = this.filter.project.join(",") || '';
        if (!isPageChange) {
          this.time_tab_loading = true;
        } else {
          this.loadingInlineTmHome = true;
        }
        this.companyTimeLogService.getCompanyInductedUsers(this.employer.id, {
            pageNumber,
            ...sortInfo,
            q,
            projectRef,
        }).subscribe((data: any) => {
            this.time_tab_loading = false;
            this.loadingInlineTmHome = false;
            if(data.success){
                this.timeLogPage = data;
                if(data.sortKey){
                    setTimeout(() => {
                        this.usersTable.sorts = [{prop: data.sortKey, dir: data.sortDir}];
                    }, 0)
                }
                if(!this.renderFilterData){
                    this.filterData = [
                        {
                            name:'project',
                            list:this.timeLogPage.projects,
                            enabled:true,
                            state:false
                        }
                    ];
                    this.renderFilterData = true;
                }
            }else{
                const message = data.message || 'Failed to fetch users.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
                this.timeLogPage = new CompanyTimeManagementPage;
            }
        });
    }

    timePageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean) {
        this.initTimeTab(pageInfo.offset, {sortKey: this.timeLogPage.sortKey, sortDir: this.timeLogPage.sortDir}, isPageChange);
    }

    timeSortCallback({sorts}) {
        let [firstSort] = sorts || [];
        let {dir, prop} = firstSort;
        this.initTimeTab(0, {sortKey: prop, sortDir: dir});
    }

    bindSearchInputEvent(){
        this.searchInputChanged.pipe(
            debounceTime(300),  // wait 300ms after the last event before emitting last event
            distinctUntilChanged()      // only emit if value is different from previous value
        ).subscribe(model => {
            this.filter.searchText = model;
            this.initTimeTab(0, {});
        });
    }

    searchTextChanged(text){
        // console.log('update filter for', text)
        this.searchInputChanged.next(text);
    }
    filterByProject(){
        // console.log('update project filter for', this.filter.project)
        this.initTimeTab(0, {});
    }


    @ViewChild('viewUserModalHtml') private viewUserModalHtmlRef: IModalComponent;
    openMemberDetail(row) {
        this.selectedUser = row;
        this.showViewUserModal = true;
        this.viewUserModalHtmlRef.open();
    }

    closeViewUserModal(event) {
        this.showViewUserModal = false;
        event.closeFn();
    }

    modalTabChange(tabId){
        console.log('modal tabChange to', tabId)
    }
    onFilterSelection(data, isPageChange: boolean){
        this.filter.project = data.project.map(a=>+a.id);
        this.initTimeTab(0, {}, isPageChange);
    }
    searchFunction(data, isPageChange: boolean){
        this.filter.searchText = data?.search;
        let stopReload = data?.stopReload;
        if(!stopReload){
            this.initTimeTab(0, {}, isPageChange);
        };
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any, qclReportContent, defectsHtml): void {
        const actionMap = {
            'view': () => this.openMemberDetail(row),
        };
        const action = actionMap[entry.key];
        if (action) {
            action();
        }
    }
   
}

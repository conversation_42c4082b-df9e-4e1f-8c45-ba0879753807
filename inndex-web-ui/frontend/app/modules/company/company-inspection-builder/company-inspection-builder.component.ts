import { ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { ControlContainer, NgForm } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { Common, CreateEmployer, InspectionBuilder, ToastService, InspectionBuilderService, scoringSystemForm, ChecklistFormModel, ScoringType, ScoringSystem, CustomScoringFields } from "@app/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import * as dayjs from 'dayjs';
import { DragulaService } from 'ng2-dragula';
import {AppConstant} from "@env/environment";
import { GenericConfirmationModalComponent, IModalComponent } from '@app/shared';
import { DuplicateValidatorDirective } from '@app/shared/pipe-and-directive';
import { take } from 'rxjs/operators';

@Component({
    viewProviders: [{provide: ControlContainer, useExisting: NgForm}],
    selector: 'company-inspection-builder',
    templateUrl: './company-inspection-builder.component.html',
})
export class CompanyInspectionBuilderComponent implements OnInit {

    AppConstant = AppConstant;
    dayjs = dayjs;
    @Input() employer: CreateEmployer;
    @Output() activatedDashboardIBs = new EventEmitter<any>();

    @ViewChild('buildInspectionHtml') public buildInspectionHtmlRef: IModalComponent;
    @ViewChild('inviteToInspectionHtml') private inviteToInspectionHtmlRef: IModalComponent;
    @ViewChild('viewInviteesHtml') private viewInviteesHtmlRef: IModalComponent;
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    @ViewChild('buildInspectionForm') buildInspectionForm: NgForm;
    
    scoringType = ScoringType;
    scoringSystem = ScoringSystem;
    customScoringFields = CustomScoringFields;

    durationPickerOptions : any = {
        showPreview: false,
        previewFormat: `{{D}} Days`,
        showNegative: false,
        showLetters: false,
        showYears: false,
        showMonths: false,
        showWeeks: false,
        showHours: false,
        showMinutes: false,
        showSeconds: false,
    };

    activateCustomScoring: boolean = false;
    ibChecklist: InspectionBuilder = new InspectionBuilder;
    copyIbChecklist: InspectionBuilder = new InspectionBuilder;
    ibChecklists: Array<InspectionBuilder> = [];
    detailFieldValidity: boolean = false;
    subheadingIds = [];
    processingLoader: boolean = false;
    companyAdditionalProjects: Array<any> = [];
    selectedProjects: Array<any> = [];
    current_inspection: InspectionBuilder = new InspectionBuilder;
    successMessage: string = '';
    isValidRootCause: boolean = false;
    inspectionInvitees: Array<any> = [];
    scoringSystemForm: scoringSystemForm = {
        validError: [],
        errorMessage: [],
        invalidRatings: false,
        invalidValues: false,
        validate: [],
        item_type: [],
        item_value: []
    }
    checklistForm: ChecklistFormModel = {
        duplicateHeadings: new Set<number>(),
        duplicateSubheadings: {},
        invalidChecklist: false,
        selectedIbChecklistIndex: null
    }
    isInspectionBtnDisable: boolean = false
    openDetailModal: boolean = false
    showInspectionModal: boolean = false
    
    constructor(
        private readonly modalService: NgbModal,
        private readonly activatedRoute: ActivatedRoute,
        private readonly toastService: ToastService,
        private readonly inspectionBuildService: InspectionBuilderService,
        private readonly dragulaService: DragulaService,
        private cdRef: ChangeDetectorRef,
        private ngZone: NgZone
    ) {
        this.dragulaCreateGroup('heading', 'drag-main');
        this.dragulaCreateGroup('subheading', 'drag-sub');
    }

    ngOnInit() {
        this.initTableRecords();
        this.companyAdditionalProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyAdditionalProjects;
    }

    hasAllProjectSelected(){
        if (this.selectedProjects.length === 0 || this.companyAdditionalProjects.length === 0) {
            return false;
        }

        return this.companyAdditionalProjects.every(project => this.selectedProjects.includes(project.id));
    }

    initTableRecords(isUpdated= false) {
        this.inspectionBuildService.getCompanyIBChecklists(this.employer.id).subscribe((data:any) => {
            if (data && data.success) {
                this.ibChecklists = data.company_ib_checklists;
                if (isUpdated) {
                    let companyInspectionBuilders = [];
                    for(let i in this.ibChecklists) {
                        let inspectionBuilder: any = this.ibChecklists[i];
                        if (inspectionBuilder.dashboard_enabled) {
                            companyInspectionBuilders.push({
                                'id': inspectionBuilder.id,
                                'ib_title': inspectionBuilder.ib_title,
                                'createdAt': inspectionBuilder.createdAt,
                            });
                        }
                    }
                    this.activatedDashboardIBs.emit(companyInspectionBuilders);
                }
                return data.company_ib_checklists;
            }
            const message = `Failed to fetch Inspection Builder Checklists, For company: ${this.employer.id}.`;
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            return [];
        });
    }

    trackByRowIndex(index, obj) {
        return index;
    }

    openModal(content, size, windowClass='', cb = () => {}) {
        this.ibChecklist.severity.options = (this.ibChecklist.severity.options && this.ibChecklist.severity.options.length) ? this.ibChecklist.severity.options : new Common().severity_options;
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size,
            windowClass: windowClass
        }).result.then((result) => {
            this.resetVars();
            cb();
        }, (reason) => {
            this.resetVars();
            cb();
        });
    }

    resetVars() {
        this.ibChecklist = new InspectionBuilder;
        this.ibChecklist.severity.options = (this.ibChecklist.severity.options && this.ibChecklist.severity.options.length) ? this.ibChecklist.severity.options : new Common().severity_options;
    }

    buildUpdateInspectionModal() {
        this.showInspectionModal = true;
        this.cdRef.detectChanges();
        this.closeAndResetForm();
        if(!this.ibChecklist.id) {
            //reset
            (this.scoringSystem || []).map(item => {
                item.has_rating_point = false;
                item.rating_point = [];
            });

            //reset
            (this.customScoringFields || []).map(field => {
                field.value = '';
            });
        }
        this.checklistForm = new ChecklistFormModel();
        this.buildInspectionHtmlRef.open();
        this.cdRef.detectChanges();
        this.openDetailModal = true;
    }

    toggleHasSubheading($event) {
        let hasSubheads = $event.target.checked;
        let tickTxt = (hasSubheads) ? 'ticking' : 'unticking';
        if(this.ibChecklist.checklist && this.ibChecklist.checklist.length) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Warning',
                title: `By ${tickTxt} this on, all pre-filled checklists will be cleared. Do you wish to continue?`,
                confirmLabel: 'Continue',
                onConfirm: () => {
                    this.checklistForm = new ChecklistFormModel();
                    this.toggleHasSubheadingConfirm(hasSubheads);
                },
                onClose: () => {
                    this.ibChecklist.has_subheadings = !hasSubheads;
                    this.callUpdateInspection();
                }
            });
        } else {
            this.toggleHasSubheadingConfirm(hasSubheads);
        }
    }

    callUpdateInspection(){
        setTimeout(() => {
            this.updateInspectionBtnVisibility();
        }, 50)
    }

    toggleHasSubheadingConfirm(hasSubheads: boolean) {
        let newChecklist = {};
        if(hasSubheads){
            newChecklist = {
                id : this.getUniqueId(),
                heading : '',
                subheadings : [{
                    item_id : 1,
                    item_que : ''
                }]
            };
        } else {
            newChecklist = {
                item_id : this.getUniqueId(),
                item_que : ''
            };
        }

        this.ibChecklist.checklist = [newChecklist];
        this.callUpdateInspection();
    }

    addCheckItem() {
        let newChecklist = {};
        if(this.ibChecklist.has_subheadings){
            newChecklist = {
                id : this.getUniqueId(),
                heading : '',
                subheadings : [{
                    item_id : this.getUniqueId(),
                    item_que : ''
                }]
            };
        } else {
            newChecklist = {
                item_id : this.getUniqueId(),
                item_que : ''
            };
        }

        this.ibChecklist.checklist.push(newChecklist);
        this.checkListValidationCheck()
    }

    getUniqueId() {
        return Date.now() + Math.round(performance.now());
    }

    removeCheckItem($event, questionIndex) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete Item',
            title: `Are you sure you want to delete this item?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.ibChecklist.checklist.splice(questionIndex, 1);
        
                DuplicateValidatorDirective.revalidateGroup('heading');
                this.callUpdateInspection();
            }
        });
    }

    checkListValidationCheck() {
        DuplicateValidatorDirective.revalidateGroup('heading');
        for (const heading of this.ibChecklist.checklist) {
            DuplicateValidatorDirective.revalidateGroup('checklist', heading.id);
        }
        this.callUpdateInspection();
        this.cdRef.detectChanges();
    }

    addSubItem(HeadIndex, queId) {
        if(!this.ibChecklist.checklist[HeadIndex].subheadings){
            this.ibChecklist.checklist[HeadIndex].subheadings = [];
        }
        this.ibChecklist.checklist[HeadIndex].subheadings.push({
            item_id : this.getUniqueId(),
            item_que : ''
        });
        
        this.checkListValidationCheck();
    }

    removeSubItem($event, HeadIndex, SubIndex) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete Item',
            title: `Are you sure you want to delete this item?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                const parentId = this.ibChecklist.checklist[HeadIndex]?.id;
        
                this.ibChecklist.checklist[HeadIndex].subheadings.splice(SubIndex, 1);
        
                DuplicateValidatorDirective.revalidateGroup('checklist', parentId);
                this.callUpdateInspection();
            }
        });
    }

    statusConfirmationPopup(form, event) {
        if (!form || !form.valid) {
            const message = 'Modal form is not valid.';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Confirm',
            title: `Would you like to activate this inspection for all ${this.employer.name} projects?`,
            confirmLabel: 'Yes',
            cancelLabel: 'No',
            onConfirm: () => {
                //save inspection with enabled status
                this.sendBuildReq(event, true);
            },
            onClose: () => {
                //save inspection with disabled status
                this.sendBuildReq(event, false);
            }
        });
    }

    sendBuildReq(event, isEnable=false) {
        let ibClReq = this.ibChecklist;
        ibClReq.enabled = isEnable;
        ibClReq.company_ref = this.employer.id;
        ibClReq.has_subheadings = (ibClReq.has_subheadings === null) ? false : ibClReq.has_subheadings;
        ibClReq.ib_type = 'company';
        ibClReq.root_cause.options = (ibClReq.root_cause.options || []).filter(item => item.name);
        ibClReq.scoring_system = (this.scoringSystem || []).find(item => (item.type == ibClReq.score_type));
        if (ibClReq.score_type == this.scoringType.TrafficLight) {
            ibClReq.scoring_system.values = (this.customScoringFields || []).reduce((arr, field) => {
                if (field.value) {
                    arr.push(field.value);
                }
                return arr;
            }, []);
        }
        this.processingLoader = true;
        this.inspectionBuildService.createCompanyIBChecklist(this.employer.id, ibClReq).subscribe(out => {
            this.initTableRecords();
            if(!out.success){
                const message = out.message || 'Failed to store data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
            }
            this.closeAndResetForm()
            event.closeFn();
            this.processingLoader = false;
        });
    }

    enableInspection($event, inspection) {
        inspection.enabled = $event.target.checked;
        inspection.validateProject = !inspection.enabled;
        this.callUpdateIBChecklist(inspection.id, inspection);
    }

    enableInspectionDashboard($event, inspection) {
        let req = {
            dashboard_enabled: $event.target.checked,
        };
        this.callUpdateIBChecklist(inspection.id, req);
    }

    callUpdateIBChecklist(ibClId, req, event?) {
        if (req.root_cause && req.root_cause.options) {
            req.root_cause.options = (req.root_cause.options || []).filter(item => item.name);
        }

        this.inspectionBuildService.updateCompanyIBChecklist(this.employer.id, ibClId, req).subscribe(out => {
            this.processingLoader = false;
            this.closeInspectionBuilderModal(event);
            this.initTableRecords(true);
        });
    }

    closeAndResetForm(){
        this.buildInspectionForm?.reset();
        this.ibChecklist = this.copyIbChecklist;
        this.ibChecklist.score_type = null;
        this.activateCustomScoring = false;
        this.checklistForm = new ChecklistFormModel();
        this.resetVars()
    }

    closeInspectionBuilderModal(event?){
        this.openDetailModal = false;
        if(event) {
            this.closeAndResetForm()
            event.closeFn();
            return;
        }
        this.ibChecklists[this.checklistForm.selectedIbChecklistIndex] = this.copyIbChecklist;
        this.resetScoringSystemForm();
        this.resetVars();
        this.showInspectionModal = false;
    }

    inviteToInspectionPopup(row){
        this.ibChecklist = row;
        this.selectedProjects = [];
        this.inviteToInspectionHtmlRef.open();
    }

    closeInviteToInspectionModal(event?) {
        if(event) {
            event.closeFn();
        }
    }

    changeSelectedProjects($event, user) {
        if ($event.target.checked) {
            if (!this.selectedProjects.includes(user.id)) {
                this.selectedProjects.push(user.id);
            }
        } else {
            let index = this.selectedProjects.indexOf(user.id);
            if (index > -1) {
                this.selectedProjects.splice(index, 1);
            }
        }

    }

    isSelectAtleastOneProject() {
        return (this.selectedProjects.length) ? true : null;
    }

    sendInviteToInspectionRequest(form, event) {
        if (!form.valid) {
            const message = 'Invalid Form.';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }

        let request = {
            ibChecklist: this.ibChecklist,
            selected_project: this.selectedProjects
        };

        this.processingLoader = true;
        this.inspectionBuildService.inviteToNMsOnInspection(this.employer.id, request).subscribe(out => {
            this.processingLoader = false;
            this.resetAllProjectSelectedVal();
            event.closeFn();
            if(out.success) {
                this.initTableRecords();
                this.successMessage = `Invite has been sent to ${request.selected_project.length} projects!`;
                this.confirmationModalRef.openConfirmationPopup({
                    headerTitle: 'Success',
                    title: this.successMessage,
                    confirmLabel: 'OK',
                    hasCancel: false,
                    onConfirm: () => {
                        return true;
                    }
                });
            } else {
                const message = out.message || 'Failed to send invitation on inspection.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
            }
        });
    }

    resetAllProjectSelectedVal() {
        this.selectedProjects = [];
    }

    toggleAllProjectSelection($event) {
        if ($event.target.checked) {
            this.selectedProjects = (this.companyAdditionalProjects || []).reduce((arr, project) => {
                arr.push(project.id);
                return arr;
            }, []);
        } else {
            this.selectedProjects = [];
        }
    }

    viewInvitesPopup(inspection) {
        this.ibChecklist = inspection;
        this.inspectionInvitees = inspection.invitees;
        this.viewInviteesHtmlRef.open();
    }

    closeViewInvitesModal(event?) {
        if(event){
            event.closeFn();
        }
    }

    editInspectionPopup(ibChecklist, selectedIndex) {
        this.showInspectionModal = true;
        this.cdRef.detectChanges();
        this.checklistForm.selectedIbChecklistIndex = selectedIndex
        this.ibChecklist = ibChecklist;
        this.ibChecklist.score_type = (this.ibChecklist && this.ibChecklist.scoring_system && this.ibChecklist.scoring_system.type) ? this.ibChecklist.scoring_system.type : '';
        this.ibChecklist.severity.options = (this.ibChecklist.severity.options && this.ibChecklist.severity.options.length) ? this.ibChecklist.severity.options : new Common().severity_options;
        this.activateCustomScoring = (this.ibChecklist.scoring_system.type == this.scoringType.TrafficLight);
        if (this.ibChecklist.scoring_system.type == this.scoringType.TrafficLight) {
            (this.ibChecklist.scoring_system.values || []).map((value, index) => {
                this.customScoringFields[index].value = value;
            });
        } else {
            this.customScoringFields = (this.customScoringFields).map(item => {
                item.value = '';
                return item;
            });
        }
        
        DuplicateValidatorDirective.revalidateGroup('heading');
        for (const heading of this.ibChecklist.checklist) {
            DuplicateValidatorDirective.revalidateGroup('checklist', heading.id);
        }
        
        (this.scoringSystem || []).map(item => {
           if (item.type == this.ibChecklist.scoring_system.type) {
               item.has_rating_point = (this.ibChecklist.scoring_system.has_rating_point || false);
               item.rating_point = (this.ibChecklist.scoring_system.rating_point || []);
           }
        });
        this.copyIbChecklist = JSON.parse(JSON.stringify(this.ibChecklist))
        this.buildInspectionHtmlRef.open();
        this.ngZone.onStable.pipe(take(1)).subscribe(() => {
            Object.values(this.buildInspectionForm.controls).forEach(control => {
                control.markAsTouched();
                control.updateValueAndValidity();
            });

            this.openDetailModal = true;
            this.updateInspectionBtnVisibility();
        });
    }

    scoreTypeSelector(selectedScore) {
        this.resetScoringSystemForm();
        this.customScoringFields = (this.customScoringFields).map(item => {
            item.value = '';
            return item;
        });
        this.activateCustomScoring = (selectedScore == this.scoringType.TrafficLight);
        (this.scoringSystem || []).forEach(item => {
            item.has_rating_point = false;
            item.rating_point = [];
            this.scoringSystemForm.errorMessage = this.scoringSystemForm.errorMessage.map((ss) => ({...ss, rating:""}));
        });
        this.callUpdateInspection();
    }

    resetScoringSystemForm(){
        this.scoringSystemForm.validError = [];
        this.scoringSystemForm.errorMessage = [];
        this.scoringSystemForm.invalidRatings = false;
        this.scoringSystemForm.invalidValues = false;
        this.scoringSystemForm.validate = [];
        this.scoringSystemForm.item_type = [];
        this.scoringSystemForm.item_value = [];
    }

    private dragulaCreateGroup(groupName: string, className: string): void {
        this.dragulaService.destroy(groupName);
        this.dragulaService.createGroup(groupName, {
            moves: (el, container, handle) => {
                console.log(handle.className);
                console.log(handle.className.includes(className));
                return handle.className.includes(className);
            }
        });
    }
        
    isInvalidValue(customField: any, index: number) {
        const customFieldValue = customField.value?.trim().toLowerCase();
    
        if (!customFieldValue) {
            this.scoringSystemForm.validate[index] = false;
            this.scoringSystemForm.validError[index] = { value: false };
            this.scoringSystemForm.errorMessage[index] = { value: "" };
            return;
        }
    
        this.scoringSystemForm.item_value = this.scoringSystemForm.item_value || [];
        this.scoringSystemForm.item_value[index] = customFieldValue;
    
        ["errorMessage", "validError", "validate"].forEach((field) => {
            if (!this.scoringSystemForm[field][index]) {
                this.scoringSystemForm[field][index] = { value: field === "errorMessage" ? "" : false };
            }
        });
    
        const duplicateGroups: Record<string, number[]> = {};
        this.scoringSystemForm.item_value.forEach((value, i) => {
            if (!value) return;
            if (!duplicateGroups[value]) {
                duplicateGroups[value] = [];
            }
            duplicateGroups[value].push(i);
        });
    
        this.scoringSystemForm.item_value.forEach((_, i) => {
            if (!duplicateGroups[this.scoringSystemForm.item_value[i]] || duplicateGroups[this.scoringSystemForm.item_value[i]].length <= 1) {
                this.scoringSystemForm.validate[i] = false;
                this.scoringSystemForm.validError[i].value = false;
                this.scoringSystemForm.errorMessage[i].value = "";
            }
        });
    
        Object.values(duplicateGroups).forEach(indices => {
            if (indices.length > 1) {
                indices.forEach(i => {
                    this.scoringSystemForm.validate[i] = true;
                    this.scoringSystemForm.validError[i].value = true;
                    this.scoringSystemForm.errorMessage[i].value = "This value is already used. Please enter a unique value.";
                });
            }
        });
    
        this.scoringSystemForm.invalidValues = Object.values(duplicateGroups).some(indices => indices.length > 1);
        this.callUpdateInspection();
    }    

    isInvalid(item: any) {
        
        item.rating_point.map((points, i) => {
             
            if (!this.scoringSystemForm.errorMessage[i]) {
                this.scoringSystemForm.errorMessage[i] = { rating: ''};
            }
            if (!this.scoringSystemForm.validError[i]) {
                this.scoringSystemForm.validError[i] = { rating: false};
            }
            if (points == null || (points == 0 && i == 0) || ( points > item.rating_point[i-1])) {
                this.scoringSystemForm.validate[i]=  true;
                this.scoringSystemForm.validError[i].rating = true;
                this.scoringSystemForm.item_type[i] = item.type;
                this.scoringSystemForm.errorMessage[i].rating =  i == 0 ? 'This rating point should be greater than 0.' : 'This rating point cannot be greater than previous one.';
                this.scoringSystemForm.errorMessage[i].rating =  points == null ? 'This rating point cannot be empty.' : this.scoringSystemForm.errorMessage[i].rating;
                return;
            }
            this.scoringSystemForm.validate[i]= false;
            this.scoringSystemForm.validError[i].rating = false;
            this.scoringSystemForm.errorMessage[i].rating = '';
        });
        if (item.rating_point.length == (item.type == this.scoringType.TrafficLight ? this.customScoringFields.length : item.values.length)) {
            this.scoringSystemForm.invalidRatings = (this.scoringSystemForm.validError.some((val) => val.rating == true) || item.rating_point.includes(null));
        }
        this.callUpdateInspection();
    }

    ratingPointChanged(item) {
        (this.scoringSystem || []).forEach(item => {
            item.rating_point = [];
            this.scoringSystemForm.errorMessage = this.scoringSystemForm.errorMessage.map((ss) => ({...ss, rating:""}));
            if(!this.scoringSystemForm.validError.some((value) => value))
            this.scoringSystemForm.validError = [];
        });
        let filteredRatingPoints = (item.rating_point || []).filter(p => p != null);
        this.scoringSystemForm.invalidRatings = (!item.has_rating_point && filteredRatingPoints.length !== item.values.length);
    }

    updateInspectionBtnVisibility() {
        this.isInspectionBtnDisable = (
            !this.ibChecklist.checklist.length ||
            this.buildInspectionForm?.invalid ||
            (!this.isValidRootCause && this.ibChecklist.root_cause.has_root_cause) ||
            this.scoringSystemForm.invalidRatings ||
            this.scoringSystemForm.invalidValues ||
            !this.detailFieldValidity
        );
        this.cdRef.detectChanges();
    }

    trackByChecklist(index: number, item: any): any {
        return item.id || index;
    }
}

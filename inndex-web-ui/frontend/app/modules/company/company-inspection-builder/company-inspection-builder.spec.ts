import { ComponentFixture, fakeAsync, TestBed } from "@angular/core/testing";
import { CompanyInspectionBuilderComponent } from "./company-inspection-builder.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ActivatedRoute } from "@angular/router";
import { DragulaService } from "ng2-dragula";
import { ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";
import { FormBuilder, FormsModule, NgForm, ReactiveFormsModule, Validators } from "@angular/forms";
import { of } from "rxjs";
import {
  ChecklistFormModel,
  InspectionBuilder,
  InspectionBuilderService,
  ToastService,
  ToastType,
} from "@app/core";
import { DuplicateValidatorDirective } from "@app/shared/pipe-and-directive";
import { IModalComponent } from "@app/shared";

const defaultScoringSystem = () => [
  {
    type: 1,
    title: "Traffic Light System",
    values: [],
    has_rating_point: false,
    rating_point: [],
  },
  {
    type: 2,
    title: "Rating: 3/2/1",
    values: ["3", "2", "1"],
    has_rating_point: false,
    rating_point: [],
    colors: { 3: "#2c961b", 2: "#EDB531", 1: "#D60707" },
  },
  {
    type: 3,
    title: "Satisfactory: Yes/No",
    values: ["Yes", "No"],
    has_rating_point: false,
    rating_point: [],
    colors: { Yes: "#2c961b", No: "#D60707" },
  },
];

const defaultCustomScoringField = () => [
  { color: "#2c961b", placeholder: "e.g. Good", value: "Good" },
  { color: "#EDB531", placeholder: "e.g. Fair", value: "fair" },
  { color: "#D60707", placeholder: "e.g. Poor", value: "Poor" },
];

describe("CompanyInspectionBuilderComponent", () => {
  let component: CompanyInspectionBuilderComponent;
  let fixture: ComponentFixture<CompanyInspectionBuilderComponent>;

  const mockModalService = jasmine.createSpyObj("NgbModal", ["open"]);
  const mockActivatedRoute = {
    snapshot: {
      params: { id: "123" },
      data: {
        companyResolverResponse: {
          companyAdditionalProjects: [],
        },
      },
    },
  };

  const mockToastService = jasmine.createSpyObj("ToastService", [
    "show",
    "showSuccess",
    "showError",
  ]);

  const mockInspectionBuilderService = jasmine.createSpyObj(
    "InspectionBuilderService",
    [
      "getById",
      "save",
      "getCompanyIBChecklists",
      "updateCompanyIBChecklist",
      "createCompanyIBChecklist",
    ]
  );

  const mockDragulaService = jasmine.createSpyObj("DragulaService", [
    "createGroup",
    "destroy",
  ]);
  const mockChangeDetectorRef = jasmine.createSpyObj("ChangeDetectorRef", [
    "detectChanges",
  ]);
  mockInspectionBuilderService.getById?.and.returnValue(
    of({
      id: 123,
      title: "Test Title",
    })
  );
  mockInspectionBuilderService.save.and.returnValue(of({}));
  mockInspectionBuilderService.updateCompanyIBChecklist.and.returnValue(of({}));

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [CompanyInspectionBuilderComponent, IModalComponent],
      imports: [FormsModule, ReactiveFormsModule],
      providers: [
        { provide: NgbModal, useValue: mockModalService },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: ToastService, useValue: mockToastService },
        {
          provide: InspectionBuilderService,
          useValue: mockInspectionBuilderService,
        },
        { provide: DragulaService, useValue: mockDragulaService },
        { provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();
  });

  beforeEach(() => {
    mockInspectionBuilderService.getCompanyIBChecklists.and.returnValue(
      of({
        success: true,
        company_ib_checklists: [],
      })
    );

    fixture = TestBed.createComponent(CompanyInspectionBuilderComponent);
    component = fixture.componentInstance;

    const injectedToastService = TestBed.inject(
      ToastService
    ) as jasmine.SpyObj<ToastService>;
    injectedToastService.types = ToastType;

    component.employer = { id: 101 };

    fixture.detectChanges();

    component.scoringSystem = defaultScoringSystem();
    component.customScoringFields = defaultCustomScoringField();
    (component as any).confirmationModalRef = jasmine.createSpyObj(
      "confirmationModalRef",
      ["openConfirmationPopup"]
    );
  });

  afterEach(() => {
    fixture.destroy();
  });

  it("should create the component", () => {
    expect(component).toBeTruthy();
  });

  it("should open the edit inspection modal and set component properties correctly", fakeAsync(() => {
    const ibChecklist = {
      scoring_system: { type: 1, values: [1, 2] },
      severity: { options: ["low", "medium", "high"] },
      root_cause: { has_root_cause: false },
      checklist: [{ id: 1, name: "Test Checklist" }],
    };
    const selectedIndex = 0;

    spyOn(DuplicateValidatorDirective, "revalidateGroup").and.callThrough();

    component.editInspectionPopup(ibChecklist, selectedIndex);

    expect(mockModalService.open).toHaveBeenCalled();

    expect(component.checklistForm.selectedIbChecklistIndex).toBe(
      selectedIndex
    );
    expect(component.ibChecklist).toEqual(ibChecklist);
    expect(component.ibChecklist.score_type).toBe(1);
    expect(component.ibChecklist.severity.options).toEqual([
      "low",
      "medium",
      "high",
    ]);
    expect(component.activateCustomScoring).toBeTruthy();

    expect(DuplicateValidatorDirective.revalidateGroup).toHaveBeenCalledWith(
      "heading"
    );
    expect(DuplicateValidatorDirective.revalidateGroup).toHaveBeenCalledWith(
      "checklist",
      ibChecklist.checklist[0].id
    );

    const controlSpies = Object.values(component.buildInspectionForm.controls).map(control => {
      const markSpy = spyOn(control, 'markAsTouched').and.callThrough();
      const updateSpy = spyOn(control, 'updateValueAndValidity').and.callThrough();
      return { markSpy, updateSpy };
    });

    Object.values(component.buildInspectionForm.controls).forEach(control => {
      control.markAsTouched();
      control.updateValueAndValidity();
    });

    controlSpies.forEach(({ markSpy, updateSpy }) => {
      expect(markSpy).toHaveBeenCalled();
      expect(updateSpy).toHaveBeenCalled();
    });
  }));

  it("should reset forms and fields when ibChecklist has no id and open modal", () => {
    component.ibChecklist = {
      severity: {
        has_severity: false,
        options: []
      },
      root_cause: {
          has_root_cause: false,
          options: []
      }
    };
    component.scoringSystemForm = {
      invalidRatings: false,
      invalidValues: false,
      validError: [],
      errorMessage: [],
      validate: [],
      item_type: [],
      item_value: [],
    };

    spyOn(component["cdRef"], "detectChanges");
    spyOn(component, "closeAndResetForm");
    component.buildInspectionHtmlRef = {
      open: jasmine.createSpy("open"),
    } as any;

    component.buildUpdateInspectionModal();

    expect(component.closeAndResetForm).toHaveBeenCalled();

    component.scoringSystem.forEach((item) => {
      expect(item.has_rating_point).toBeFalsy();
      expect(item.rating_point).toEqual([]);
    });

    component.customScoringFields.forEach((field) => {
      expect(field.value).toBe("");
    });

    expect(component.checklistForm).toEqual(jasmine.any(ChecklistFormModel));
    expect(component.buildInspectionHtmlRef.open).toHaveBeenCalled();
  });

  it("should show toast if form is invalid", () => {
    const formGroupSpy = jasmine.createSpyObj("FormGroup", ["get"]);
    Object.defineProperties(formGroupSpy, {
      controls: { value: {} },
      valid: { value: true },
      invalid: { value: false },
    });
    component.buildInspectionForm = formGroupSpy;
    const form = { valid: false };

    component.statusConfirmationPopup(form, {} as any);

    const toastService = TestBed.inject(
      ToastService
    ) as jasmine.SpyObj<ToastService>;

    expect(toastService.show).toHaveBeenCalledWith(
      ToastType.INFO,
      "Modal form is not valid."
    );
  });

  it("should open confirmation modal if form is valid", () => {
    const fb = new FormBuilder();
    const mockForm = fb.group({ dummy: ["value", Validators.required] });

    expect(mockForm.valid).toBeTruthy();

    const mockNgForm = {
      valid: true,
      invalid: false,
      value: { dummy: "value" },
      controls: {
        dummy: { valid: true, value: "value" },
      },
    } as unknown as NgForm;

    component.buildInspectionForm = mockNgForm;

    component.employer = { name: "ABC Corp" };

    const modalSpy = jasmine.createSpy("openConfirmationPopup");
    (component as any).confirmationModalRef = {
      openConfirmationPopup: modalSpy,
    };

    component.statusConfirmationPopup(mockForm, {} as any);

    expect(modalSpy).toHaveBeenCalled();

    const args = modalSpy.calls.mostRecent().args[0];
    expect(args.title).toContain("ABC Corp");
  });

  it("should call sendBuildReq(true) when onConfirm is called", () => {
    const mockForm = { valid: true };
    const mockEvent = { test: "event" };

    const formGroupSpy = jasmine.createSpyObj("FormGroup", ["get"]);
    Object.defineProperties(formGroupSpy, {
      controls: { value: {} },
      valid: { value: true },
      invalid: { value: false },
    });
    component.buildInspectionForm = formGroupSpy;

    spyOn(component as any, "sendBuildReq");

    component.statusConfirmationPopup(mockForm, mockEvent);

    const args = (
      component as any
    ).confirmationModalRef.openConfirmationPopup.calls.mostRecent().args[0];
    args.onConfirm();

    expect((component as any).sendBuildReq).toHaveBeenCalled();
  });

  it("should call sendBuildReq(false) when onClose is called", () => {
    const fb = new FormBuilder();
    const mockForm = fb.group({ dummy: ["value", Validators.required] })
    const mockEvent = { test: "event" };

    spyOn(component as any, "sendBuildReq").and.stub();

    component.statusConfirmationPopup(mockForm, mockEvent);

    const args = (
      component as any
    ).confirmationModalRef.openConfirmationPopup.calls.mostRecent().args[0];
    args.onClose();

    expect((component as any).sendBuildReq).toHaveBeenCalledWith(
      mockEvent,
      false
    );
  });

  it("should call createCompanyIBChecklist and handle response properly", () => {
    const expectedRequest = jasmine.objectContaining({
      enabled: true,
      company_ref: 101,
      has_subheadings: false,
      ib_type: "company",
      root_cause: jasmine.objectContaining({
        options: [{ name: "Root1" }],
      }),
      scoring_system: jasmine.objectContaining({
        type: 1,
        values: ["Good", "fair", "Poor"],
      }),
    });

    mockInspectionBuilderService.createCompanyIBChecklist.and.returnValue(
      of({ success: true })
    );

    component.ibChecklist = {
      score_type: 1,
      has_subheadings: null,
      root_cause: { options: [{ name: "Root1" }] },
      scoring_system: null,
      enabled: false,
    };

    component.scoringSystem = [
      {
        type: 1,
        title: "Traffic Light System",
        values: [],
        has_rating_point: false,
        rating_point: [],
      },
    ];

    component.customScoringFields = [
      { color: "#2c961b", placeholder: "e.g. Good", value: "Good" },
      { color: "#EDB531", placeholder: "e.g. Fair", value: "fair" },
      { color: "#D60707", placeholder: "e.g. Poor", value: "Poor" },
    ];

    // Mock the buildInspectionForm to prevent undefined controls error
    component.buildInspectionForm = {
      reset: jasmine.createSpy('reset'),
      controls: {}
    } as any;

    spyOn(component, 'closeAndResetForm').and.callThrough();
    spyOn(component, 'initTableRecords').and.stub();

    component.sendBuildReq({ closeFn: () => {} }, true);

    expect(
      mockInspectionBuilderService.createCompanyIBChecklist
    ).toHaveBeenCalledWith(101, expectedRequest);

    const [, actualReq] =
      mockInspectionBuilderService.createCompanyIBChecklist.calls.mostRecent()
        .args;

    expect(actualReq.enabled).toBe(true);
    expect(actualReq.company_ref).toBe(101);
    expect(actualReq.ib_type).toBe("company");
    expect(actualReq.root_cause.options).toEqual([{ name: "Root1" }]);
    expect(actualReq.scoring_system.type).toBe(1);
    expect(actualReq.scoring_system.values).toEqual(["Good", "fair", "Poor"]);
  });

  it("should show error toast if createCompanyIBChecklist returns failure", () => {
    component.ibChecklist = {
      score_type: 1,
      has_subheadings: null,
      root_cause: { options: [] },
    } as any;

    component.employer = { id: 101 };
    component.customScoringFields = [];

    const mockEvent = {
      closeFn: jasmine.createSpy("closeFn"),
    };

    const failureResponse = {
      success: false,
      message: "Something went wrong.",
    };
    mockInspectionBuilderService.createCompanyIBChecklist.and.returnValue(
      of(failureResponse)
    );

    spyOn(component, "closeAndResetForm");
    spyOn(component, "initTableRecords");

    component.sendBuildReq(mockEvent, false);

    expect(mockToastService.show).toHaveBeenCalledWith(
      ToastType.ERROR,
      "Something went wrong.",
      { data: failureResponse }
    );
  });

  it("should correctly update isInspectionBtnDisable based on form and checklist state", () => {
    // Initialize ibChecklist using the actual InspectionBuilder constructor to ensure all properties are properly set
    component.ibChecklist = new InspectionBuilder();
    component.ibChecklist.checklist = [{}];
    component.ibChecklist.root_cause.has_root_cause = true;

    component.buildInspectionForm = {
      get invalid() {
        return false;
      },
      controls: {}
    } as any;

    component.isValidRootCause = true;
    component.scoringSystemForm = {
      invalidRatings: false,
      invalidValues: false,
      validError: [],
      errorMessage: [],
      validate: [],
      item_type: [],
      item_value: []
    } as any;

    component.detailFieldValidity = true;

    spyOn(component["cdRef"], "detectChanges");

    component.updateInspectionBtnVisibility();
    expect(component.isInspectionBtnDisable).toBeFalsy();

    component.ibChecklist.checklist = [];
    component.updateInspectionBtnVisibility();
    expect(component.isInspectionBtnDisable).toBeTruthy();

    component.ibChecklist.checklist = [{}];
    component.buildInspectionForm = {
      get invalid() {
        return true;
      },
      controls: {}
    } as any;
    component.updateInspectionBtnVisibility();
    expect(component.isInspectionBtnDisable).toBeTruthy();

    component.buildInspectionForm = {
      get invalid() {
        return false;
      },
      controls: {}
    } as any;
    component.isValidRootCause = false;
    // Ensure root_cause exists before setting has_root_cause
    if (!component.ibChecklist.root_cause) {
      component.ibChecklist.root_cause = {};
    }
    component.ibChecklist.root_cause.has_root_cause = true;
    component.updateInspectionBtnVisibility();
    expect(component.isInspectionBtnDisable).toBeTruthy();

    component.isValidRootCause = true;
    component.scoringSystemForm.invalidRatings = true;
    component.updateInspectionBtnVisibility();
    expect(component.isInspectionBtnDisable).toBeTruthy();

    component.scoringSystemForm.invalidRatings = false;
    component.scoringSystemForm.invalidValues = true;
    component.updateInspectionBtnVisibility();
    expect(component.isInspectionBtnDisable).toBeTruthy();

    component.scoringSystemForm.invalidValues = false;
    component.detailFieldValidity = false;
    component.updateInspectionBtnVisibility();
    expect(component.isInspectionBtnDisable).toBeTruthy();
  });
});

<div class="d-flex" id="wrapper">
    <company-side-nav
        [employer]="employer"
        [companyResolverResponse]="companyResolverResponse"
        (reInitializeProjectsByEmployer)="initializeProjectsByEmployer()"
        [activatedDashboardIBs]="ibChecklists"
        [featuresStatus]="companyFeaturesStatus"
    ></company-side-nav>
    <div class="w-100 px-4 pt-3 company-setting-navs" [ngClass]="{'ml-fix': !isMobileDevice}">
        <div class="detail-page-header-margin">
            <ul ngbNav #nav="ngbNav" [(activeId)]="activeId" (activeIdChange)="tabChange($event)" class="nav-tabs n-tab nav-tabs-v2 w-100">
                <li [ngbNavItem]="'access'" [domId]="'access'">
                    <a ngbNavLink class="nav-a">Access</a>
                    <ng-template ngbNavContent>
                        <div class="col-12 pb-3">
                            <small class="form-text text-muted d-none">Manage admin access here.</small>
                            <form novalidate #addCompanyUserForm="ngForm">
                                <div class="form-group">
                                    <manage-company-access
                                        access_role="COMPANY_ADMIN"
                                        [admins]="employer.admins"
                                        [employer]="employer"
                                    ></manage-company-access>
                                </div>
                                <block-loader [show]="(modalRequestInProgress)"></block-loader>
                                <button type="button" class="btn btn-outline-primary" (click)="saveNewCompanyUsers()"
                                        [disabled]="addCompanyUserForm.invalid">Save
                                </button>
                            </form>
                        </div>
                    </ng-template>
                </li>
                <li [ngbNavItem]="'induction'" [domId]="'induction'">
                    <a ngbNavLink class="nav-a">{{induction_setting?.phrasing.singlr}}</a>
                    <ng-template ngbNavContent>
                        <company-induction-setting
                            [employer]="employer"
                            [setting]="induction_setting"
                            (onSave)="onSaveInductionSetting($event)"
                        ></company-induction-setting>
                    </ng-template>
                </li>
                <li [ngbNavItem]="'blacklisted'" [domId]="'blacklisted'"
                    *ngIf="employer?.features_status?.user_blacklisting">
                    <a ngbNavLink class="nav-a">Blocked</a>
                    <ng-template ngbNavContent>
                        <div class="col-sm-12" *ngIf="blacklisted_users_loading">
                            <ngx-skeleton-loader count="8"
                                                 [theme]="{ 'border-radius': '0', height: '30px', width: '100%' }"></ngx-skeleton-loader>
                        </div>
                        <div class="col-sm-12 table-responsive-sm" *ngIf="!blacklisted_users_loading">
                            <input
                                class="col-md-4 ngx-search d-none"
                                type="search"
                                [(ngModel)]="blockedUserSearch"
                                placeholder='Type to search User'
                                (input)='onSearchBlockedUsers($event)'
                            />
                            <small class="form-text text-muted">All users who are blocked
                                from {{employer.name}} Projects.</small>
                            <ngx-datatable
                                class="table table-hover table-sm bootstrap"
                                [rows]="blacklisted_users"
                                [columns]="[
                          {name:'innDex Id', prop: 'user_id', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', width: 10},
                          {name:'Name', prop: 'name', cellTemplate: nameTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1'},
                          {name:'Company', prop: 'employer', cellTemplate: companyTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', width: 60},
                          {name:'Blocked On', prop: 'blacklisted_on', cellTemplate: timestampTemplate, width: 60, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1'},
                          {name:'Block request by', prop: 'admin_name', cellTemplate: adminNameTemplate, width: 50,  headerClass: 'py-2 font-weight-bold', cellClass: 'py-1'},
                          {name:'Previous Project', prop: 'project_name', cellTemplate: projectNameTemplate, width: 70, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1'},
                          {name:'Action', cellTemplate: actionBtn, width: 10, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 no-ellipsis', sortable: false}
                      ]"
                                [columnMode]="'force'"
                                [limit]="50"
                                [footerHeight]="36"
                                [sorts]="[{prop: 'blacklisted_on', dir: 'desc'}]"
                                [rowHeight]="'auto'">

                                <ng-template #nameTemplate let-value="value">
                                    <span appTooltip>{{ value }}</span>
                                </ng-template>
                                <ng-template #companyTemplate let-value="value">
                                    <span appTooltip>{{ value }}</span>
                                </ng-template>
                                <ng-template #timestampTemplate let-value="value">
                                    <span appTooltip>{{ value ? dayjsFullDate(+value) : '-' }}</span>
                                </ng-template>
                                <ng-template #adminNameTemplate let-value="value" let-row="row">
                                    <span appTooltip>{{ value }}</span> <span *ngIf="row.note" [ngbTooltip]="row.note"><i
                                    class="fas fa-comment"></i></span>
                                </ng-template>
                                <ng-template #projectNameTemplate let-value="value" let-row="row">
                                    <!--<button class="btn btn-sm btn-link" [routerLink]="['/project-portal/project/'+ row.project_id +'/induction']">{{ value }}</button>-->
                                    <span appTooltip>{{ value }}</span>
                                </ng-template>
                                <ng-template #actionBtn let-value="value" let-row="row">
                                    <button class="btn btn-sm btn-outline-success" (click)="unblockUserRequest(row)"
                                            title="Reinstate Access">
                                        <i class="fas fa-user-plus"></i>
                                    </button>
                                </ng-template>
                            </ngx-datatable>


                            <manage-blacklisting
                                #manageBlacklistingModal
                                [projectId]="0"
                                [projectCompany]="employer"
                                [adminUser]="authUser$"
                                [hasOptimaEnabled]="false"
                                [companyArea]="true"
                                [optimaAccessGroup]="[]"
                                (onSave)="onBlackListingResponse($event)"
                            ></manage-blacklisting>
                        </div>
                    </ng-template>
                </li>
                <li [ngbNavItem]="'incident_report'" [domId]="'incident_report'">
                    <a ngbNavLink class="nav-a">Incident Report</a>
                    <ng-template ngbNavContent>
                        <div class="mb-3 mt-2">
                            <ngb-accordion #acc="ngbAccordion" [activeIds]="activeIncidentPanels" (panelChange)="panelChange($event, 'incident_report')">
                                <ngb-panel id="notifications">
                                    <ng-template ngbPanelHeader>
                                        <button ngbPanelToggle class="btn p-0">
                                            <i class="fas fa-angle-down"></i>
                                            <strong> Notifications</strong>
                                        </button>
                                    </ng-template>
                                    <ng-template ngbPanelContent>
                                        <incident-notification-preference
                                            [employer]="employer"
                                        ></incident-notification-preference>
                                    </ng-template>
                                </ngb-panel>
                                <ngb-panel id="action-category-config">
                                    <ng-template ngbPanelHeader>
                                        <button ngbPanelToggle class="btn p-0">
                                            <i class="fas fa-angle-down"></i>
                                            <strong> Action Category Configuration</strong>
                                        </button>
                                    </ng-template>
                                    <ng-template ngbPanelContent>
                                        <form novalidate #configForm="ngForm">
                                            <span>Configure action categories.</span>
                                            <button class="btn btn-primary mb-2 float-right" [disabled]="!configForm.valid" (click)="saveCategoriesRequest(configForm, modalLoader)">Save</button>
                                            <div class="form-group">
                                                <table class="table table-bordered">
                                                    <thead>
                                                        <tr>
                                                            <th>Category</th>
                                                            <th class="text-center">Active <i class="ml-1 fa fa-info-circle small" ngbTooltip="Deactivating a category will hide the option when submitting an incident action." [openDelay]="200" [closeDelay]="500"></i></th>
                                                            <th class="text-center">Action</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <ng-container *ngFor="let item of actionCategories?.value trackBy : trackByRowIndex; let i = index;">
                                                            <tr>
                                                                <td>
                                                                    <input type="text" class="form-control" [name]="'categoryName' + i"
                                                                        #categoryName="ngModel" [(ngModel)]="item.label" [required]="(i != 0) ? true : false"
                                                                        placeholder="{{(i != 0) ? 'Enter category name' : 'Enter category name then press add'}}" autocomplete="off" />
                                                                    <div class="alert alert-danger mb-0 mt-1 p-0 pl-2" [hidden]="categoryName.valid">Category name is required.</div>
                                                                </td>
                                                                <td class="text-center">
                                                                    <div class="custom-control custom-checkbox" *ngIf="i != 0">
                                                                        <input type="checkbox" class="custom-control-input" [id]="'active_' + i" [name]="'active_' + i"
                                                                            #active="ngModel" [(ngModel)]="item.active" />
                                                                        <label class="custom-control-label" [for]="'active_' + i"></label>
                                                                    </div>
                                                                </td>
                                                                <td class="text-center">
                                                                    <button *ngIf="i == 0 else remove" class="btn btn-sm btn-outline-primary" (click)="addCategory(i)">
                                                                        <i class="fa fa-plus"></i> Add
                                                                    </button>
                                                                    <ng-template #remove>
                <span class="material-symbols-outlined text-danger cursor-pointer" (click)="removeCategoryRow(i)">
                                                                            delete
                                                                        </span>
                                                                    </ng-template>
                                                                </td>
                                                            </tr>
                                                        </ng-container>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </form>
                                        <block-loader #modalLoader [showBackdrop]="true" alwaysInCenter="true"></block-loader>
                                    </ng-template>
                                </ngb-panel>
                            </ngb-accordion>
                        </div>
                    </ng-template>
                </li>
                <li [ngbNavItem]="'inspection_builder'" [domId]="'inspection_builder'">
                    <a ngbNavLink class="nav-a">Inspections</a>
                    <ng-template ngbNavContent>
                        <div class="col-md-12 p-0 mb-3">
                            <company-inspection-builder
                                [employer]="employer"
                                (activatedDashboardIBs)="getActivatedDashboardIBs($event)"
                            ></company-inspection-builder>
                        </div>
                    </ng-template>
                </li>
                <li [ngbNavItem]="'company-itp-config'" [domId]="'company-itp-config'">
                    <a ngbNavLink class="nav-a">ITP</a>
                    <ng-template ngbNavContent>
                        <company-itp-config [employer]="employer"></company-itp-config>
                    </ng-template>
                </li>
                <li [ngbNavItem]="'tools_tab'" [domId]="'tools_tab'">
                    <a ngbNavLink class="nav-a">Tools</a>
                    <ng-template ngbNavContent>
                        <div class="mb-3 mt-2">
                            <ngb-accordion #acc="ngbAccordion" [activeIds]="current_tools" (panelChange)="panelChange($event, 'tools_tab')">
                                <ngb-panel id="company_tools">
                                    <ng-template ngbPanelHeader>
                                        <button ngbPanelToggle class="btn p-0">
                                            <i class="fas fa-angle-down mr-1"></i>
                                            <strong>Company Portal Tools</strong>
                                        </button>
                                    </ng-template>
                                    <ng-template ngbPanelContent>
                                        <div class="col-12 pb-3">
                                            <p class="form-text mb-1 mt-0">Enable/Disable sections</p>
                                            <form novalidate #featuresStatusForm="ngForm">
                                                <table class="table table-bordered">
                                                    <thead>
                                                    <tr>
                                                        <th class="text-center">Section</th>
                                                        <th class="text-center">Action</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                        <ng-template ngFor let-item [ngForOf]="companyTools | keyvalue">
                                                            <tr>
                                                                <td class="text-center">{{ item.value }}</td>
                                                                <td class="text-center">
                                                                    <input type="checkbox" class="cursor-pointer" [name]="item+'_status'"
                                                                    [checked]="tempCompanyFeaturesStatus[item.key]"
                                                                    (change)="tempCompanyFeaturesStatus[item.key] = !tempCompanyFeaturesStatus[item.key]" />
                                                                </td>
                                                            </tr>
                                                        </ng-template>
                                                    </tbody>
                                                </table>
                                                <button type="button" class="btn btn-outline-primary" (click)="saveActiveCompanyTools(featuresStatusForm)"
                                                        [disabled]="featuresStatusForm.invalid">Save
                                                </button>
                                            </form>
                                        </div>
                                    </ng-template>
                                </ngb-panel>
                                <!--<ngb-panel id="project_tools">
                                    <ng-template ngbPanelHeader>
                                        <button ngbPanelToggle class="btn p-0">
                                            <i class="fas fa-angle-down mr-1"></i>
                                            <strong>Project Portal Tools</strong>
                                        </button>
                                    </ng-template>
                                    <ng-template ngbPanelContent>
                                        Project Portal Tools
                                    </ng-template>
                                </ngb-panel>-->
                            </ngb-accordion>
                        </div>
                    </ng-template>
                </li>
                <li [ngbNavItem]="'conduct_cards'" [domId]="'conduct_cards'">
                    <a ngbNavLink class="nav-a">Conduct Cards</a>
                    <ng-template ngbNavContent>
                        <div class="col-md-12 p-0 mb-3">
                            <app-configure-conduct-cards></app-configure-conduct-cards>
                        </div>
                    </ng-template>
                </li>
                <li [ngbNavItem]="'permits'" [domId]="'permits'" *ngIf="this.employer.features_status && this.employer.features_status.permit">
                    <a ngbNavLink class="nav-a">Permits</a>
                    <ng-template ngbNavContent>
                        <div class="col-md-12 p-0 mb-3">
                            <permit-template></permit-template>
                        </div>
                    </ng-template>
                </li>
                <li [ngbNavItem]="'company_supply_chain'" [domId]="'company_supply_chain'">
                    <a ngbNavLink class="nav-a">Supply Chain</a>
                    <ng-template ngbNavContent>
                        <company-supply-chain-configuration></company-supply-chain-configuration>
                    </ng-template>
                </li>
                <li [ngbNavItem]="'asset_management'" [domId]="'asset_management'">
                    <a ngbNavLink class="nav-a">Asset Management</a>
                    <ng-template ngbNavContent>
                        <asset-management-config></asset-management-config>
                    </ng-template>
                </li>
                <li [ngbNavItem]="'skill_matrix'" [domId]="'skill_matrix'" *ngIf="this.employer.features_status && this.employer.features_status.skills_matrix">
                    <a ngbNavLink class="nav-a">Competency Matrix</a>
                    <ng-template ngbNavContent>
                        <company-skills-matrix-setting
                            [skills_matrix]="companyFeaturesStatus.skills_matrix"
                            (featureStatusChanged)="onSkillsSettingChanged($event)"
                            [employer]="employer"></company-skills-matrix-setting>
                    </ng-template>
                </li>
            </ul>

            <div [ngbNavOutlet]="nav" class="col-sm-12 pt-2 mb-3 nav-panel nav-panel-v2 company-settings-panel"></div>

        </div>
    </div>
</div>
<block-loader [showBackdrop]="true" alwaysInCenter="true" [show]="processLoader"></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { ControlContainer, NgForm } from "@angular/forms";
import {  CreateEmployer, ITP_TYPE, QualityChecklist, QualityChecklistsService } from "@app/core";
import * as dayjs from 'dayjs';
import {AppConstant} from "@env/environment";
import { GenericConfirmationModalComponent, IModalComponent } from '@app/shared';
import { CreateorEditITPComponent } from '@app/modules/common';
import { innDexConstant } from '@env/constants';


@Component({
    viewProviders: [{provide: ControlContainer, useExisting: NgForm}],
    selector: 'company-itp-config',
    templateUrl: './company-itp-configuration.component.html',
})
export class CompanyItpConfigComponent implements OnInit {

    AppConstant = AppConstant;
    dayjs = dayjs;

    @Input() employer: CreateEmployer;
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    qualityChecklist: QualityChecklist[] = [];
    filteredQualityChecklist: QualityChecklist[] = [];
    qualityCL: QualityChecklist = new QualityChecklist;
    fieldsValid: boolean = true;
    loading: boolean = false;
    editModalOpened :boolean = false;

    constructor(
        private qualityCLService: QualityChecklistsService,
    ) {

    }

    ngOnInit() {
        this.getQCLdata();
    }

   

    getQCLdata($event?){
        this.loading = true;
        this.qualityCLService.getCompanyQChecklists(this.employer.id,{itpType: 'company'}).subscribe((data: any) => {
            if(data && data.success){
                this.qualityChecklist = data.company_checklists;
                this.filteredQualityChecklist = data.company_checklists;
                this.loading = false;
            }else{
                console.error('Failed to get data',data);
                alert(data.message ? data.message : 'Failed to get data');
            }
        })
    }

    @ViewChild('itpAccessModalHtml') private itpAccessGenricModal: IModalComponent;
    expandItpsModal() {
       return this.itpAccessGenricModal.open();
   }
   @ViewChild('createOrEditItp') createOrEditItp: CreateorEditITPComponent;
   openCreateOrOpenItpModal( qualityCL? ){
    this.createOrEditItp.openCreateOrOpenItpModal(qualityCL, ITP_TYPE.company)
   }
   closeItpAccessModal(){
       this.itpAccessGenricModal.close();
   }

   toggleQcClStatus($event,item:QualityChecklist){
       $event.preventDefault();
       $event.stopPropagation();
       let title = item.enabled ? 'disable': 'enable'
       this.confirmationModalRef.openConfirmationPopup({
        headerTitle: `${title} ITP`,
        title: `Are you sure you want to  ${title} this ITP?`,
        confirmLabel: title,
        onConfirm: () => {
            item.enabled = !item.enabled;
            item.qc_doc = item.qc_doc.map(({ id }) => id).filter(id => id != null);
            this.loading = true;
            this.updateQChecklist(item.id, item);
            }
        });
    }
    updateQChecklist(id, body, cb = () => {}){
        this.qualityCLService.updateCompanyQChecklist(this.employer.id,id, body).subscribe(out => {
            this.loading = false;
            if(!out.success){
                console.error('Failed to update data',out);
                alert(out.message ? out.message : 'Failed to update data');
                return;
            }
            this.getQCLdata();
            cb();
        });
    }
    searchFunction(data){
        let searchTerm = data.search.toLowerCase();
        if(!searchTerm){
            this.filteredQualityChecklist = this.qualityChecklist;
            return;
        }
        this.filteredQualityChecklist = this.qualityChecklist.filter(a=>a.qc_title.toLowerCase().includes(searchTerm));
    }
}

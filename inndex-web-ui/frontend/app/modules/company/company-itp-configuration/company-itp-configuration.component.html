<div class="justify-content-between horizontal-center" style="height: 70px;">
    <div>
        <search-with-filters customClasses = "d-inline" [loading]="loading" (searchEmitter)="searchFunction($event)"></search-with-filters>
    </div>
    <div>
        <button class="btn btn-sm btn-outline-brandeis-blue btn-hover mb-3 m-btn-size-lg " id="newBtn" (click)="openCreateOrOpenItpModal(qualityCL)">
            <div class="d-flex align-items-center justify-content-center">
                <span class="material-symbols-outlined x-large-font mr-1">
                    add
                </span>
                <span class="mr-1">New ITP</span>
            </div>
        </button>
    </div>
</div>
<create-or-edit-itp [company] = "employer" [qualityCL]= "qualityCL" (fetchQCLData)="getQCLdata($event)" #createOrEditItp></create-or-edit-itp>
<ng-container *ngIf="!loading" ngForm>
    <div class="company-itp col-12">
      <div class="table-responsive-sm ngx-datatable-custom">
        <ngx-datatable class="bootstrap table table-hover table-sm"
            [rows]="filteredQualityChecklist ? filteredQualityChecklist : []"
            [columns]="[
                {name:'ITP Ref No.', prop:'record_id', headerClass: 'py-2 font-weight-bold col-2', width: 100, cellClass: 'py-1 table-cell-text col-2', cellTemplate: itpRefNo},
                {name:'ITP Title', prop:'qc_title', headerClass: 'py-2 font-weight-bold w-100 col-5 px-2', cellClass: 'py-1 table-cell-text col-5', cellTemplate: itpTitle},
                {name:'Created By', prop:'user_ref', headerClass: 'py-2 font-weight-bold col-2' , cellClass: 'py-1 table-cell-text col-2', cellTemplate: createdBy},
                {name:'Date Created', prop:'createdAt', headerClass: 'py-2 font-weight-bold col-2', cellClass: 'py-1 table-cell-text col-2', cellTemplate: dateCreated},
                {name:'Active', prop:'enabled', headerClass: 'py-2 font-weight-bold col-2', cellClass: 'py-1 table-cell-text col-1', cellTemplate: active}
            ]"
            [columnMode]="'force'"
            [footerHeight]="36"
            [rowHeight]="'auto'">
            <ng-template #itpRefNo let-row="row">
                {{ employer.company_initial }}{{ row.record_id }}
            </ng-template>
            <ng-template #itpTitle let-row="row" >
                <a href="javascript:void(0)"  class="font-weight-bold d-block w-100 text-truncate text-info"  (click)="openCreateOrOpenItpModal(row)"><span appTooltip>{{ row.qc_title }}</span></a>
            </ng-template>
            <ng-template #createdBy let-row="row">
                <span appTooltip>{{ row.user_ref.first_name }} {{ row.user_ref.last_name }}</span>
            </ng-template>
            <ng-template #dateCreated let-row="row">
                {{ dayjs(row.createdAt).format(AppConstant.fullDateTimeFormat) }}
            </ng-template>
            <ng-template #active let-row="row">
                <div (click)="toggleQcClStatus($event, row)" class="custom-control custom-switch d-inline-block">
                    <input type="checkbox" class="custom-control-input" [id]="'enabled'+row.id" [name]="'enabled'+row.id"
                        [checked]="true" [(ngModel)]="row.enabled">
                    <label class="custom-control-label" [for]="'enabled'+row.id"></label>
                </div>
            </ng-template>
        </ngx-datatable>
      </div>
    </div>
</ng-container>
<block-loader [show]="loading" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

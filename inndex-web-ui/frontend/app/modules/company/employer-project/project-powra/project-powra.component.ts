import {Component, OnInit, TemplateRef, ViewChild, ViewEncapsulation} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import { HttpParams } from '@angular/common/http';
import {Observable} from "rxjs";
import * as dayjs from 'dayjs';
import {map, share} from "rxjs/operators";
import {NgbModalConfig, NgbModal, NgbDateStruct} from '@ng-bootstrap/ng-bootstrap';
import {ProjectPowraService, AuthService, UserService, User, Project, ProjectService, isInheritedProjectOfCompany, Common, HttpService, ToastService} from "@app/core";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import { ShareToolReportToEmailComponent } from '@app/modules/common';
import {AppConstant} from "@env/environment";
import { ActionBtnEntry, IModalComponent } from '@app/shared';

@Component({
    templateUrl: './project-powra.component.html',
    // add NgbModalConfig and NgbModal to the component providers
    providers: [NgbModalConfig, NgbModal],
    styles: [],
})
export class ProjectPowraComponent implements OnInit {
    employerId: number = 0;
    authUser$: User;
    projects: Array<Project> = [];
    projectsLoading: boolean = false;
    powraRecords: Array<any> = [];
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;
    AppConstant = AppConstant;
    employer: any = {};
    powra_row: any = {};
    powraReviewSignature;
    iframe_content_loading: boolean = false;
    powra_loading: boolean = false;
    is_inherited_project: boolean = false;
    loadingPowras: boolean = false;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    paginationData = new Common();
    page = this.paginationData.page;
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    isProjectPortal: boolean = false;
    isMobileDevice: boolean = false;
    showModal: boolean = false;
    isInitPowra = false;
    loadingInlinePowra: boolean = false;
    rowButtonGroup: Array<ActionBtnEntry> = [];

    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private modalService: NgbModal,
        private projectPowraService: ProjectPowraService,
        private authService: AuthService,
        private projectService: ProjectService,
        private userService: UserService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private httpService: HttpService,
        private toastService: ToastService,
    ) {
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
            this.buildButtonGroup();
        }
    }

    dayjs(n: number) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };

    ngOnInit() {
        if(!this.isProjectPortal) {
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
                this.employerId = params['employerId'];
            });
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        } else {
            this.initializeTable();
        }

        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });
    }

    buildButtonGroup() {
        this.rowButtonGroup = [
            {
                key: 'view',
                label: '',
                title: `View ${this.projectInfo?.custom_field?.powra_phrase_singlr} Details`,
                mat_icon: 'search',
            },
            {
                key: 'download_pdf',
                label: '',
                title: `Download ${this.projectInfo?.custom_field?.powra_phrase_singlr} PDF`,
                mat_icon: 'download',
            },
            {
                key: 'share_powra',
                label: '',
                title: `Share ${this.projectInfo?.custom_field?.powra_phrase_singlr} Details`,
                mat_icon: 'share',
            },
            {
                key: 'review_powra',
                label: '',
                title: `Review ${this.projectInfo?.custom_field?.powra_phrase_singlr}`,
                mat_icon: 'rate_review',
            },
        ];
    }

    // Will get called for Company route only.
    projectRetrieved(project: Project){
        this.projectInfo = project;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
        this.buildButtonGroup();
        this.initializeTable();
    }

    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean) {
        if (!this.isInitPowra) {
          this.isInitPowra = true;
          return;
        }
        this.page.pageNumber = pageInfo.offset;
        this.initializeTable(isPageChange);
    }

    private initializeTable(isPageChange?: boolean) {
        const params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`);
        if (isPageChange) {
          this.loadingInlinePowra = true;
        } else {
          this.loadingPowras = true;
        }
        this.projectPowraService.getAllPowra(this.projectId, params)
            .subscribe((data:any) => {
                this.loadingPowras = false;
                this.loadingInlinePowra = false;
                if (data && data.allPowra) {
                    this.powraRecords = data.allPowra;
                    this.page.totalElements = data.total_record_count;
                    return data.allPowra;
                }
                const message = `Failed to fetch project powra, id: ${this.projectId}.`;
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
                return [];
            });
    }

    
    @ViewChild('reviewPOWRARef')
    private reviewPOWRARef: IModalComponent;
    viewPowraReview(row, viewOnly:boolean = false) {
        this.powra_row = row;
        this.showModal = !viewOnly;
        this.reviewPOWRARef.open();
        this.iframe_content_loading = true;
        this.openPowraView(row, 'html', (html) => {
            let iframe = document.getElementById('powraPreviewFrame') as HTMLIFrameElement;
            let doc = iframe.contentDocument;
            doc.write(html);
            this.iframe_content_loading = false;
        }, false);
    }

    openPowraView(row, target = 'html', cb = (data) => {}, openWindow = true){
        let params: any = {
            nowMs: dayjs().valueOf(),
        };
        if (this.authUser$.timezone) {
            params.tz = this.authUser$.timezone;
        }
        if (!openWindow) {
            params.embed = 'frame';
        }
        this.powra_loading = true;
        this.projectPowraService.viewOrDownloadPowra(row.id, row.updatedAt, params, target, (data) => {
            this.powra_loading = false;
            cb(data);
        }, openWindow);
    }

    saveSignature(data, $modalLoader) {
        $modalLoader.show = true;
        this.powraReviewSignature = data;
        let request = {
            reviewer: {name: this.authUser$.name, user_ref: this.authUser$.id, sign: this.powraReviewSignature, timestamp: dayjs().valueOf()}
        };
        this.projectPowraService.updatePowra(this.projectId, request, this.powra_row.id).subscribe(out => {

            if(!out.success){
                const message = out.message || 'Failed to store data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
            }
            $modalLoader.show = false;
            this.reviewPOWRARef.close();
            this.showModal = false;
            this.initializeTable();
        });
    }

    clearSignature() {
        this.powraReviewSignature = null;
    }

    closeModal() {
        this.showModal = false;
    }

    @ViewChild('sharePowraReportModal') sharePowraReportModalRef: ShareToolReportToEmailComponent;
    openSharePowraDetailModal(row) {
        this.sharePowraReportModalRef.openEmailFormModal(row);
    }

    sharePowraReport(event) {
        this.projectPowraService.sharePowraReport(event.req, event.reportId).subscribe((res:any) => {
            event.cb(res);
        });
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
            const actionMap = {
                'view': () => this.viewPowraReview(row, true),
                'download_pdf': () => this.openPowraView(row, 'pdf'),
                'share_powra': () => this.openSharePowraDetailModal(row),
                'review_powra': () => this.viewPowraReview(row),
        };
        const action = actionMap[entry.key];
        if (action) {
            action();
        }
    }
}

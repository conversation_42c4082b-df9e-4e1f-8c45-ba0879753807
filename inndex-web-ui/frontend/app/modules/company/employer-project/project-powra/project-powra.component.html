<style>
    .text-truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>
<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav *ngIf="!isProjectPortal"  [employer]="employer" [companyResolverResponse]="companyResolverResponse" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="col-sm-12 my-3 outer-border" *ngIf="!loadingPowras">
                <div class="col-sm-12 my-3 outer-border-radius">
                <div class="d-flex flex-wrap justify-content-between mb-2 pb-2">
                    <h5>Total {{projectInfo?.custom_field?.powra_phrase}} <small>({{ page.totalElements }})</small></h5>
                </div>
                <div class="table-responsive-sm">
                    <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                   [scrollbarV]="true"
                                   [virtualization]="false"
                                   [loadingIndicator]="loadingInlinePowra"
                                   [rows]="powraRecords"
                                   [footerHeight]="40"
                                   [columnMode]="'force'"
                                   [rowHeight]="'auto'"
                                   [externalPaging]="true"
                                   [count]="page.totalElements"
                                   [offset]="page.pageNumber"
                                   [limit]="page.size"
                                   (page)="pageCallback($event, true)"
                    >
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                {{projectInfo?.custom_field?.powra_phrase_singlr}} #
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{row.record_id}}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Submitted At
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{ dayjs(row.createdAt).format(AppConstant.fullDateTimeFormat)}}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Submitted By
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{row.report_completed_by}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Work Activity
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip class="text-truncate" style="white-space: pre-line;">{{ row.work_activity }}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold action-column"
                                              cellClass="action-column no-ellipsis" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Action
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <button-group
                                    [buttons]="rowButtonGroup"
                                    (onActionClick)="rowBtnClicked($event, row)">
                                </button-group>
                            </ng-template>
                        </ngx-datatable-column>
                    </ngx-datatable>
                    <block-loader [show]="(powra_loading)" alwaysInCenter="true"></block-loader>
                </div>
                <div class="clearfix"></div>
            </div>
            </div>
        </div>
    </div>
</div>

<i-modal #reviewPOWRARef [title]="projectInfo?.custom_field?.powra_phrase_singlr + ' #' + powra_row?.record_id" (onCancel)="closeModal()" size="xl" [windowClass]="'xl-modal'" [showCancel]="false" [showFooter]="false">
    <div class="d-flex justify-content-center flex-wrap overflow-auto h-100">
            <div class="w-100" [ngStyle]="{'height': showModal ? '69.5%' : '95%'}">
                <iframe #powraPreviewFrame id='powraPreviewFrame' class="border-0" style="width: 100%;height: 100%;position: relative;"></iframe>
            </div>
                <div *ngIf="showModal" class="form-group row mt-1 mr-0">
                    <div class="col-md-12 form-inline justify-content-center px-0">
                        <signature-pad-selector
                            [height]="150"
                            [width]="1140"
                            (signChanged)="saveSignature($event, modalLoader)"
                            (clear)="clearSignature()"
                        ></signature-pad-selector>
                    </div>
                </div>
    </div>
    <block-loader alwaysInCenter="true" [show]="iframe_content_loading" #modalLoader></block-loader>
</i-modal>
<block-loader [show]="loadingPowras" showBackdrop="true" alwaysInCenter="true"></block-loader>
<share-tool-report-to-email
    [projectId]="projectId" [employerId]="employerId" #sharePowraReportModal
    [tool_name]="'Share '+projectInfo?.custom_field?.powra_phrase" (onSave)="sharePowraReport($event)">
</share-tool-report-to-email>

<div id="wrapper">
    <company-side-nav [employer]="employer" [companyResolverResponse]="companyResolverResponse" [projectFeaturePermission] = "projectFeaturePermission" (reInitializeProjectsByEmployer)="initializeProjectsByEmployer()" [companyProjectSectionAccess]="project.company_additional_project_section_access"></company-side-nav>
    <div class="p-0 mw-100" [ngClass]="is_mobile_nav ? 'm-0' : 'ml-fix'">
        <div class="row mx-0">
            <div class="col-sm-12 p-0 detail-page-header-margin">

                <!-- start Filter code -->
                <div id="company-map" class="mapFilterOverlay">
                    <div class="reset p-2" [ngClass]="{
                       'd-block': (isResetFilter===true),
                       'd-none': (isResetFilter===false)
                     }">
                        <a routerLink="/company-admin/employer-project/{{employerId}}">
                            Reset Filter <i class="far fa-times-circle float-right"></i></a>
                    </div>
                    <div class="mapFilterOverlaysearch p-2">
                        <input class="search-input outline w-100" type="search" [(ngModel)]="searchQuery" placeholder="Search Projects" (keyup.enter)="searchByQuery(type,value)"/>
                        <!-- <input [(ngModel)]="searchQuery" placeholder="search" (keyup.enter)="searchByQuery(type,value)"> -->
                    </div>
                    <div class="panel-group" id="accordion">
                        <ngb-accordion #acc="ngbAccordion" activeIds="p-main">
                            <ngb-panel id="p-type">
                                <ng-template ngbPanelTitle>
                                    Project Type <i class="text-uppercase" [ngClass]="{'text-success':(type!='')}">{{type}}</i>
                                </ng-template>
                                <ng-template ngbPanelContent>
                                    <ul class="ul-wrap">
                                        <li>
                                            <a routerLink="/company-admin/employer-project/{{employerId}}" [queryParams]="{value:value,p_risk:projectRisk,division:division,query:searchQuery}">
                                                <div class="w-100 d-flex align-items-center justify-content-between pl-3">
                                                    All Type <span class="text-brandeis-blue fw-500">{{all_type_count}}</span>
                                                </div>
                                            </a>
                                        </li>
                                        <li *ngFor="let t of PROJECT_TYPES" [className]="t.key===type ? 'selected' : ''">
                                            <a routerLink="/company-admin/employer-project/{{employerId}}" [queryParams]="{type:t.key,value:value,
                                            p_risk:projectRisk,division:division,query:searchQuery}">
                                                <div class="w-100 d-flex align-items-center justify-content-between pl-3">
                                                    <!-- <img class="projectIcons" [src]="t.icon"> --> {{t.value}}
                                                    <span class="text-brandeis-blue fw-500"> {{t.count_by_type}} </span>
                                                </div>
                                            </a>
                                        </li>
                                    </ul>
                                </ng-template>
                            </ngb-panel>

                            <ngb-panel id="p-value">
                                <ng-template ngbPanelTitle>
                                    Project Value <i class="text-uppercase" [ngClass]="{'text-success':(value!='')}">{{value}}</i>
                                </ng-template>
                                <ng-template ngbPanelContent>
                                    <ul class="ul-wrap">
                                        <li>
                                            <a routerLink="/company-admin/employer-project/{{employerId}}" [queryParams]="{type:type,p_risk:projectRisk,division:division,query:searchQuery}">
                                                <div class="w-100 d-flex align-items-center justify-content-between pl-3">
                                                    All Value <span class="text-lime-green fw-500">{{all_value_count}}</span>
                                                </div>
                                            </a>
                                        </li>
                                        <li *ngFor="let p of PROJECT_VALUE" [className]="p.key===value ? 'selected' : ''">
                                            <a routerLink="/company-admin/employer-project/{{employerId}}" [queryParams]="{type:type,value:p.key,
                                            p_risk:projectRisk,division:division,query:searchQuery}">
                                                <div class="w-100 d-flex align-items-center justify-content-between pl-3">
                                                    {{p.value}} <span class="text-lime-green fw-500"> {{p.count_by_value}} </span>
                                                </div>
                                            </a>
                                        </li>
                                    </ul>
                                </ng-template>
                            </ngb-panel>

                            <ngb-panel id="p-site-risks" *ngIf="all_project_risk_count > 0">
                                <ng-template ngbPanelTitle>
                                    Site Big Risks <i class="text-uppercase" [ngClass]="{'text-success':(projectRisk!='')}">{{projectRisk}}</i>
                                </ng-template>
                                <ng-template ngbPanelContent>
                                    <ul class="ul-wrap">
                                        <li>
                                            <a routerLink="/company-admin/employer-project/{{employerId}}" [queryParams]="{type:type,value:value,division:division,query:searchQuery}">
                                                <div class="w-100 d-flex align-items-center justify-content-between pl-3">
                                                    All Big Risks <span class="text-fuel-yellow fw-500"> {{ all_project_risk_count }} </span>
                                                </div>
                                            </a>
                                        </li>
                                        <ng-container *ngFor="let risk of SITE_MAIN_RISKS">
                                            <li *ngIf="risk?.count_by_project_risk > 0" [className]="risk.name === projectRisk ? 'selected' : ''">
                                                <a routerLink="/company-admin/employer-project/{{employerId}}" [queryParams]="{ type:type,value:value,p_risk:risk.name,division:division,query:searchQuery}" class="mr-2">
                                                    <div class="w-100 d-flex align-items-center justify-content-between pl-3">
                                                        {{ risk.name }} <span class="text-fuel-yellow fw-500"> {{ risk?.count_by_project_risk }} </span>
                                                    </div>
                                                </a>
                                            </li>
                                        </ng-container>
                                    </ul>
                                </ng-template>
                            </ngb-panel>
                            <ngb-panel id="p-divisions" *ngIf="DIVISION_DATA?.length > 0">
                                    <ng-template ngbPanelTitle>
                                        Divisions <i class="text-uppercase" [ngClass]="{'text-success':(division!='')}">{{selectedDivision}}</i>
                                    </ng-template>
                                    <ng-template ngbPanelContent>
                                        <ul class="ul-wrap">
                                            <li>
                                                <a routerLink="/company-admin/employer-project/{{employerId}}" [queryParams]="{type:type,value:value,p_risk:projectRisk,query:searchQuery}">
                                                    <div class="w-100 d-flex align-items-center justify-content-between pl-3">All Divisions <span class="text-fuel-yellow fw-500">{{ all_division_count }}</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <ng-container *ngFor="let divisionData of DIVISION_DATA">
                                                <li [className]="divisionData.id===division ? 'selected' : ''">
                                                    <a routerLink="/company-admin/employer-project/{{employerId}}" [queryParams]="{type: type,value:value, p_risk:projectRisk,division:divisionData.id,query: searchQuery }" class="mr-2">
                                                        <div class="w-100 d-flex align-items-center justify-content-between pl-3">{{ divisionData.name }} <span class="text-fuel-yellow fw-500"> {{ divisionData.disabledProjectCount }}</span>
                                                        </div>
                                                    </a>
                                                </li>
                                             </ng-container>
                                        </ul>
                                    </ng-template>
                            </ngb-panel>

                            <ngb-panel id="p-main">
                                <ng-template ngbPanelTitle>
                                    Projects
                                </ng-template>
                                <ng-template ngbPanelContent>
                                    <ul class="ul-wrap">
                                        <li *ngFor="let item of filteredProjects">
                                            <a class="mr-0" href="javascript:void(0)">
                                                <div class="w-100 d-flex align-items-center justify-content-between pl-3">
                                                    <p class="m-0 mr-2 w-100" (click)="openProject(employerId, item)"> {{ item.name || '' }} <span *ngIf="item.project_number">({{item.project_number}})</span></p>
                                                    <span class="dd-wo-caret cursor-pointer" container="body" ngbDropdown *ngIf="(item.project_category == 'company-project' || item.project_category == 'default')">
                                                        <i class="fas fa-ellipsis-v dropright" ngbDropdownToggle></i>
                                                        <div ngbDropdownMenu>
                                                          <button ngbDropdownItem *ngIf="employerInfo.features_status.add_project" class="cursor-pointer" (click)="editProject(item, blockLoader, projectFormPopup)" type="button">Edit</button>
                                                          <button ngbDropdownItem *ngIf="authUser$.roles.includes('COMPANY_ADMIN') && item.project_category=='company-project'" class="cursor-pointer" (click)="archiveUnarchiveProject(item, true)" type="button">Archive Project</button>
                                                          <button ngbDropdownItem class="cursor-pointer" (click)="openManageCPAModal(item)" type="button">Manage Access</button>
                                                        </div>
                                                    </span>
                                                </div>
                                            </a>
                                        </li>
                                    </ul>
                                </ng-template>
                            </ngb-panel>

                            <ngb-panel id="p-archived">
                                <ng-template ngbPanelTitle>
                                    Archived Projects
                                </ng-template>
                                <ng-template ngbPanelContent>
                                    <ul class="ul-wrap">
                                        <li *ngFor="let item of filteredDisabledProjects">
                                            <a class="mr-0">
                                                <div class="w-100 d-flex align-items-center justify-content-between pl-3">
                                                    <p (click)="openProject(employerId, item)" class="m-0 mr-2 w-100">{{item.name}} <span *ngIf="item.project_number">({{item.project_number}})</span></p>
                                                    <span class="dd-wo-caret cursor-pointer" container="body" ngbDropdown *ngIf="(item.project_category == 'company-project')">
                                                        <i class="fas fa-ellipsis-v dropright" ngbDropdownToggle></i>
                                                        <div ngbDropdownMenu>
                                                          <button ngbDropdownItem *ngIf="authUser$.roles.includes('COMPANY_ADMIN')" class="cursor-pointer" (click)="archiveUnarchiveProject(item, false)" type="button">Unarchive Project</button>
                                                        </div>
                                                    </span>
                                                </div>
                                            </a>
                                        </li>
                                    </ul>
                                </ng-template>
                            </ngb-panel>

                        </ngb-accordion>
                    </div>
                </div>
                <!-- end Filter code -->
                <agm-map
                    [latitude]="lat"
                    [longitude]="lng"
                    [zoom]="zoom"
                    [disableDefaultUI]="false"
                    [mapTypeId]="mapViewType"
                    [zoomControl]="true"
                    [streetViewControl]="false"
                    [fitBounds]="true"
                >
                    <agm-marker-cluster imagePath="https://raw.githubusercontent.com/googlemaps/v3-utility-library/master/markerclustererplus/images/m">
                        <agm-marker
                            *ngFor="let m of markers; let i = index"
                            [latitude]="m.lat"
                            [longitude]="m.lng"
                            [markerDraggable]="false"
                            [agmFitBounds]="true"
                            [iconUrl]="m.iconUrl"
                            [opacity]="m.info.disabled_on == null ? 1: 0.6"
                        >

                            <agm-info-window>
                                <div class="poi-info-window gm-style">
                                    <div  class="title full-width">
                                       {{ m.info.project_number != null ? m.info.project_number + ' - ' + m.name : m.name || ''}}
                                    </div>
                                    <div class="address">
                                        <div class="view-link" *ngIf="m.info.disabled_on == null">
                                            <a
                                                [routerLink]="['/company-admin/project-time-management/' + employerId + '/time-logs/' + m.id]"
                                            >Open Project</a>
                                        </div>
                                    </div>
                                </div>
                            </agm-info-window>

                        </agm-marker>
                    </agm-marker-cluster>


                </agm-map>
            </div>
        </div>
        <block-loader [show]="(projectsLoading)"></block-loader>
    </div>
</div>

<i-modal #projectFormPopup title="Project Details" size="lg" [showCancel]="false" rightPrimaryBtnTxt="Save" 
    [rightPrimaryBtnDisabled]="!projectForm.valid" (onClickRightPB)="updateCompanyProject(projectForm, $event)">
        <form [ngClass]="{'form-container setup-content companyProjectForm': true}" novalidate #projectForm="ngForm">
            <div *ngIf="(project.project_category == 'company-project')">
                <div class="form-group">
                    <label>
                        Project Name <small class="required-asterisk ">*</small>
                    </label>
                    <input type="text" class="form-control" required #name="ngModel" [(ngModel)]="project.name" name="name"
                           placeholder="Project Name"/>
                    <div class="alert alert-danger mt-1" [hidden]="!(name.errors && name.errors.required)">Project Name is required</div>
                    <div class="alert alert-danger mt-1" [hidden]="!(name.errors && name.errors.pattern)">Project Name should not include non-ascii characters.</div>
                </div>

                <div class="form-group">
                    <label>
                        Project/Contract Number <small class="required-asterisk ">*</small>
                    </label>
                    <input type="text" class="form-control" required #project_number="ngModel" [(ngModel)]="project.project_number" name="project_number"
                           placeholder="Project/Contract Number" />
                    <div class="alert alert-danger" [hidden]="(project_number.valid)">Project/Contract Number is required.</div>
                </div>

                <div class="form-group" *ngIf="postcodeInput?.type === 'postcode-lookup'">
                    <label>
                        Project post code <small class="required-asterisk ">*</small>
                    </label>
                    <div>
                        <input type="text" class="form-control inline-input" required #post_code="ngModel" [(ngModel)]="project.postcode" name="post_code"
                           placeholder="Project post code"/>
                           <project-geofence-modal-box [projectId]="project.id" class="geofence-icon"></project-geofence-modal-box>
                    </div>
                    <div class="alert alert-danger" [hidden]="(post_code.valid)">Project post code is required.</div>
                </div>

                <ng-container *ngIf="postcodeInput?.type === 'address-lookup'">
                    <div class="form-group">
                        <label>
                            Project location <small class="required-asterisk ">*</small>
                        </label>
                        <project-geofence-modal-box [projectId]="project.id" class="geofence-icon"></project-geofence-modal-box>
                        <div class="row">
                            <div class="col-6 form-group">
                                <label>
                                    Latitude
                                </label>
                                <input type="number" class="form-control inline-input" required #lat="ngModel" [(ngModel)]="project.custom_field.location.lat" name="lat"
                                       placeholder="Enter Latitude" pattern="^-?(?:90(?:\.0+)?|(?:[1-8]?[0-9])(?:\.\d+)?)$"/>
                            </div>
                            <div class="col-6 form-group">
                                <label>
                                    Longitude
                                </label>
                                <input type="number" class="form-control inline-input" required #long="ngModel" [(ngModel)]="project.custom_field.location.long" name="long"
                                       placeholder="Enter Longitude" pattern="^-?(?:180(?:\.0+)?|(?:1[0-7][0-9]|[1-9]?[0-9])(?:\.\d+)?)$"/>
                            </div>
                        </div>
                        <div class="alert alert-danger mt-1" *ngIf="(lat.invalid || long.invalid) && (lat.touched || lat.dirty || long.touched || long.dirty)">
                            Project location is required
                        </div>
                        <div class="alert alert-danger mt-1" [hidden]="!lat.errors?.pattern">Enter a valid latitude (-90 to 90).</div>
                        <div class="alert alert-danger mt-1" [hidden]="!long.errors?.pattern">Enter a valid longitude (-180 to 180).</div>
                    </div>
                    <div class="form-group">
                        <label>
                            Address <small class="required-asterisk ">*</small>
                        </label>
                        <input type="text" class="form-control inline-input" required #address="ngModel" [(ngModel)]="project.custom_field.location.region" name="address"
                               placeholder="Address"/>
                        <div class="alert alert-danger mt-1" [hidden]="!(address.errors && address.errors.required)">Address is required</div>
                    </div>
                </ng-container>

                <div class="form-group">
                    <label>Timezone <small class="required-asterisk">*</small></label>
                    <ng-select [items]="availableTimeZones"
                        bindLabel="name"
                        bindValue="name" required
                        placeholder="Choose Timezone"
                        name="timezone"
                        [(ngModel)]="project.custom_field.timezone"
                        #projectTimeZone="ngModel">
                    </ng-select>
                    <div class="alert alert-danger" [hidden]="!(projectTimeZone.errors && projectTimeZone.errors.required)">Project timezone is required</div>
                </div>

                <div class="form-group">
                    <label>
                        innTime login PIN <small class="required-asterisk ">*</small>
                    </label>
                    <input type="text" pattern="\d*" minlength="6" maxlength="8" class="form-control" required #pin="ngModel" [(ngModel)]="project.pin" name="pin"
                           placeholder="innDex TV Login PIN" />
                    <div class="alert alert-danger" [hidden]="!(pin.errors && pin.errors.required)">innTime login PIN is required.</div>
                    <div class="alert alert-danger" [hidden]="!(pin.errors && pin.errors.pattern)">innTime login PIN must be a number</div>
                    <div class="alert alert-danger" [hidden]="!(pin.errors && pin.errors.minlength)">innTime login PIN minimum length must be 6 digits</div>
                </div>

                <div class="form-group">
                    <label>Client</label>
                    <input type="text" class="form-control" [(ngModel)]="project.client" name="client"
                           placeholder="Enter Client" />
                </div>

                <div class="form-group">
                    <label>
                        Main Contact Name <small class="required-asterisk ">*</small>
                    </label>
                    <input type="text" class="form-control" required #pContactName="ngModel" [(ngModel)]="project.main_contact_name" name="pContactName"
                           placeholder="Enter Main Contact Name" />
                    <div class="alert alert-danger" [hidden]="(pContactName.valid)">Main Contact Name is required</div>
                </div>

                <div class="form-group">
                    <label>Main Contact Number <small class="required-asterisk ">*</small>
                    </label>
                    <app-country-code-contact-input [contactData]="project" 
                                [name]="'main_contact_number_obj'" [isRequired]="true"
                                [errorMessageTitle]="'Main Contact Number'" [defaultCountryCode]="this.employerInfo.country_code">
                    </app-country-code-contact-input>
                </div>

                <div class="form-group">
                    <label>Description</label>
                    <textarea name="description" [(ngModel)]="project.description" class="form-control" placeholder="Your project description (Optional)"></textarea>
                </div>

                <div class="form-group">
                    <label>Start Date <small class="required-asterisk ">*</small></label>
                    <div class="input-group col-sm-8">
                        <input class="form-control" placeholder="dd-mm-yyyy" readonly
                               name="start_date" [(ngModel)]="project.start_date" ngbDatepicker
                               #sd="ngbDatepicker" ng-value="project.start_date" required>
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary calendar" (click)="sd.toggle()" type="button">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>End Date <small class="required-asterisk ">*</small></label>
                    <div class="input-group col-sm-8">
                        <input class="form-control" placeholder="dd-mm-yyyy" readonly
                               name="end_date" [(ngModel)]="project.end_date" ngbDatepicker
                               #ed="ngbDatepicker" ng-value="project.end_date" required>
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary calendar" (click)="ed.toggle()" type="button">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Project Value <small class="required-asterisk ">*</small></label>
                    <ng-select [(ngModel)]="project.value" [items]="PROJECT_VALUE" bindLabel="value" bindValue="key" name="value" class="form-control" required ng-value="project.value">
                    </ng-select>
                </div>

                <div class="form-group">
                    <label>Project Type (list as per main project setup) <small class="required-asterisk ">*</small></label>
                    <ng-select [items]="PROJECT_TYPES" bindLabel="value" bindValue="key" [(ngModel)]="project.project_type" name="project_type" class="form-control" required ng-value="project.project_type">
                    </ng-select>
                </div>

                <div class="form-group">
                    <label>Type of Works</label>
                    <ng-select [items]="knownTypeOfWorks"
                               [multiple]="true"
                               bindLabel="name"
                               bindValue="name"
                               placeholder="Choose Type of Works"
                               name="type_of_works"
                               [(ngModel)]="project.type_of_works"
                               #competencyName="ngModel">
                    </ng-select>
                </div>

                <div class="form-group">
                    <label>Contract Type</label>
                    <input type="text" class="form-control" [(ngModel)]="project.main_contract_type" name="main_contract_type"
                           placeholder="Enter Main Contract Type" />
                </div>

                <div class="form-group">
                    <label>Designer</label>
                    <input type="text" class="form-control" [(ngModel)]="project.designer" name="designer"
                           placeholder="Enter Designer" />
                </div>

                <div class="form-group">
                    <label>Stakeholder</label>
                    <input type="text" class="form-control" [(ngModel)]="project.stakeholder" name="stakeholder"
                           placeholder="Enter Stakeholder" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-12 pl-0">Use the tick boxes to select the project features</label>
                <table>
                    <colgroup>
                        <col style="width: 33%" />
                        <col style="width: 33%" />
                        <col style="width: 33%" />
                    </colgroup>
                    <tr>
                        <td [ngbTooltip]="projectFeaturePermission.close_calls === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.close_calls)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.close_calls)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.close_calls"
                                       [checked]="project.company_additional_project_section_access.close_calls"
                                       [disabled]="disableToolStatus(projectFeaturePermission.close_calls)"
                                       name="ca-cc"
                                       id="ca-cc">
                                <label class="custom-control-label" for="ca-cc">
                                    <h6>{{ project.custom_field.cc_phrase }}</h6>
                                </label>
                                <i *ngIf="project.project_category=='company-project'" class="fas fa-cog ml-1 cowSetting cursor-pointer" (click)="altPhraseCcModal()"></i>
                            </div>
                        </td>

                        <td [ngbTooltip]="projectFeaturePermission.clerk_of_works === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.clerk_of_works)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.clerk_of_works)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.clerk_of_works"
                                       [checked]="project.company_additional_project_section_access.clerk_of_works"
                                       [disabled]="disableToolStatus(projectFeaturePermission.clerk_of_works)"
                                       name="ca-cow"
                                       id="ca-cow">
                                <label class="custom-control-label" for="ca-cow">
                                    <h6>{{ project.cow_setting.cow_phrase }}</h6>
                                </label>
                                <div *ngIf="project.project_category=='company-project'" ngbDropdown [autoClose]="'outside'" class="d-inline-block mr-1"
                                     placement="bottom-right" container="body">
                                    <i class="fas fa-cog ml-1 dropdown-toggle-no-caret cowSetting cursor-pointer" ngbDropdownToggle
                                       placement="bottom-left" data-toggle="dropdown" aria-haspopup="true"
                                       aria-expanded="false" id="dropdownMenu3"></i>
                                    <div ngbDropdownMenu class="dropdown-menu" aria-labelledby="dropdownMenu3">
                                        <button class="dropdown-item cursor-pointer"
                                                (click)="projectSiteConfigModal()" type="button">Configuration
                                        </button>
                                        <button class="dropdown-item cursor-pointer" (click)="tagOwnersModal()"
                                                type="button">Tag Owners
                                        </button>
                                        <button class="dropdown-item cursor-pointer"
                                                (click)="cowSiteDrawingsModal()" type="button">Add Site Drawings
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </td>

                        <td [ngbTooltip]="projectFeaturePermission.collection_notes === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.collection_notes)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.collection_notes)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.collection_notes"
                                       [checked]="project.company_additional_project_section_access.collection_notes"
                                       [disabled]="disableToolStatus(projectFeaturePermission.collection_notes)"
                                       name="ca-cn"
                                       id="ca-cn">
                                <label class="custom-control-label" for="ca-cn">
                                    <h6>{{project.custom_field.cn_phrase}}</h6>
                                </label>
                                <i *ngIf="project.project_category=='company-project'" class="fas fa-cog ml-1 cowSetting cursor-pointer" (click)="altPhraseCnModal()"></i>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td [ngbTooltip]="projectFeaturePermission.daily_activities === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.daily_activities)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.daily_activities)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.daily_activities"
                                       [checked]="project.company_additional_project_section_access.daily_activities"
                                       [disabled]="disableToolStatus(projectFeaturePermission.daily_activities)"
                                       name="ca-da"
                                       id="ca-da">
                                <label class="custom-control-label" for="ca-da">
                                    <h6>{{project.custom_field.da_phrase}}</h6>
                                </label>
                                <div *ngIf="project.project_category=='company-project'" ngbDropdown [autoClose]="'outside'" class="d-inline-block mr-1"
                                     placement="bottom-right" container="body">
                                    <i class="dropdown-toggle-no-caret fas fa-cog ml-1 cowSetting cursor-pointer" ngbDropdownToggle
                                       placement="bottom-left" data-toggle="dropdown" aria-haspopup="true"
                                       aria-expanded="false" id="dropdownMenu2"></i>
                                    <div ngbDropdownMenu class="dropdown-menu" aria-labelledby="dropdownMenu2">
                                        <button class="dropdown-item cursor-pointer"
                                                (click)="dailyActivitiesImportModal()" type="button">Import
                                            Activities
                                        </button>
                                        <button class="dropdown-item cursor-pointer" (click)="altPhraseDaModal()"
                                                type="button">Configuration
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.delivery_notes === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.delivery_notes)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.delivery_notes)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.delivery_notes"
                                       [checked]="project.company_additional_project_section_access.delivery_notes"
                                       [disabled]="disableToolStatus(projectFeaturePermission.delivery_notes)"
                                       name="ca-dn"
                                       id="ca-dn">
                                <label class="custom-control-label" for="ca-dn">
                                    <h6>{{project.custom_field.dn_phrase}}</h6>
                                </label>
                                <i *ngIf="project.project_category=='company-project'" class="fas fa-cog ml-1 cowSetting cursor-pointer" (click)="altPhraseDnModal()"></i>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.good_calls === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.good_calls)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.good_calls)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.good_calls"
                                       [checked]="project.company_additional_project_section_access.good_calls"
                                       [disabled]="disableToolStatus(projectFeaturePermission.good_calls)"
                                       name="ca-gc"
                                       id="ca-gc">
                                <label class="custom-control-label" for="ca-gc">
                                    <h6>{{project.custom_field.gc_phrase}}</h6>
                                </label>
                                <i *ngIf="project.project_category=='company-project'" class="fas fa-cog ml-1 cowSetting cursor-pointer" (click)="altPhraseGcModal()"></i>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td [ngbTooltip]="projectFeaturePermission.ib_checklist === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.ib_checklist)" container="body">
                            <div class="custom-checkbox d-inline-block c-config" [class.disable-tool]="disableToolStatus(projectFeaturePermission.ib_checklist)">
                                <inspections-modal-box
                                    [project]="project"
                                    [section_access_key]="'company_additional_project_section_access'"
                                    [settingIcon]="'fa-cog'"
                                    [inspections_admin]="projectFeaturePermission.ib_checklist"
                                ></inspections-modal-box>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.incident_report === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.incident_report)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.incident_report)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.incident_report"
                                       [checked]="project.company_additional_project_section_access.incident_report"
                                       [disabled]="disableToolStatus(projectFeaturePermission.incident_report)"
                                       name="ca-ir"
                                       id="ca-ir">
                                <label class="custom-control-label" for="ca-ir">
                                    <h6>Incident Report</h6>
                                </label>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.observations === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.observations)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.observations)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.observations"
                                       [checked]="project.company_additional_project_section_access.observations"
                                       [disabled]="disableToolStatus(projectFeaturePermission.observations)"
                                       name="ca-obrs"
                                       id="ca-obrs">
                                <label class="custom-control-label" for="ca-obrs">
                                    <h6>{{project.custom_field.obrs_phrase}}</h6>
                                </label>
                                <i *ngIf="project.project_category=='company-project'" class="fas fa-cog ml-1 cowSetting cursor-pointer" (click)="altPhraseObrsModal()"></i>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td [ngbTooltip]="projectFeaturePermission.powra === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.powra)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.powra)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.powra"
                                       [checked]="project.company_additional_project_section_access.powra"
                                       [disabled]="disableToolStatus(projectFeaturePermission.powra)"
                                       name="ca-powra"
                                       id="ca-powra">
                                <label class="custom-control-label" for="ca-powra">
                                    <h6>{{project.custom_field.powra_phrase}}</h6>
                                </label>
                                <i *ngIf="project.project_category=='company-project'" class="fas fa-cog ml-1 cowSetting cursor-pointer" (click)="altPhrasePowraModal()"></i>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.progress_photos === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.progress_photos)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.progress_photos)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.progress_photos"
                                       [checked]="project.company_additional_project_section_access.progress_photos"
                                       [disabled]="disableToolStatus(projectFeaturePermission.progress_photos)"
                                       name="ca-pps"
                                       id="ca-pps">
                                <label class="custom-control-label" for="ca-pps"><h6>Progress Photos</h6></label>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.quality_checklist === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.quality_checklist)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.quality_checklist)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.quality_checklist"
                                       [checked]="project.company_additional_project_section_access.quality_checklist"
                                       [disabled]="disableToolStatus(projectFeaturePermission.quality_checklist)"
                                       name="ca-qcl"
                                       id="ca-qcl">
                                <label class="custom-control-label" for="ca-qcl">
                                    <h6>{{project.custom_field.qcl_phrase}}</h6>
                                </label>
                                <i *ngIf="project.project_category=='company-project'" class="fas fa-cog ml-1 cowSetting cursor-pointer" (click)="altPhraseQclModal()"></i>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td [ngbTooltip]="projectFeaturePermission.rams === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.rams)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.rams)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.rams"
                                       [checked]="project.company_additional_project_section_access.rams"
                                       [disabled]="disableToolStatus(projectFeaturePermission.rams)"
                                       name="ca-rams"
                                       id="ca-rams">
                                <label class="custom-control-label" for="ca-rams">
                                    <h6>{{project.custom_field.rams_phrase}}</h6>
                                </label>
                                <i *ngIf="project.project_category=='company-project'" class="fas fa-cog ml-1 cowSetting cursor-pointer" (click)="altPhraseRamsModal()"></i>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.take_5s === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.take_5s)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.take_5s)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.take_5s"
                                       [checked]="project.company_additional_project_section_access.take_5s"
                                       [disabled]="disableToolStatus(projectFeaturePermission.take_5s)"
                                       name="ca-t5s"
                                       id="ca-t5s">
                                <label class="custom-control-label" for="ca-t5s">
                                    <h6>{{project.custom_field.take5_phrase}}</h6>
                                </label>
                                <i *ngIf="project.project_category=='company-project'" class="fas fa-cog ml-1 cowSetting cursor-pointer" (click)="altPhraseT5sModal()"></i>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.task_briefings === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.task_briefings)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.task_briefings)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.task_briefings"
                                       [checked]="project.company_additional_project_section_access.task_briefings"
                                       [disabled]="disableToolStatus(projectFeaturePermission.task_briefings)"
                                       name="ca-tbs"
                                       id="ca-tbs">
                                <label class="custom-control-label" for="ca-tbs">
                                    <h6>{{project.custom_field.tb_phrase}}</h6>
                                </label>
                                <i *ngIf="project.project_category=='company-project'" class="fas fa-cog ml-1 cowSetting cursor-pointer" (click)="altPhraseTbModal()"></i>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td [ngbTooltip]="projectFeaturePermission.toolbox_talks === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.toolbox_talks)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.toolbox_talks)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.toolbox_talks"
                                       [checked]="project.company_additional_project_section_access.toolbox_talks"
                                       [disabled]="disableToolStatus(projectFeaturePermission.toolbox_talks)"
                                       name="ca-tt"
                                       id="ca-tt">
                                <label class="custom-control-label" for="ca-tt"><h6>Toolbox Talks</h6></label>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.work_package_plan === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.work_package_plan)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.work_package_plan)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.work_package_plan"
                                       [checked]="project.company_additional_project_section_access.work_package_plan"
                                       [disabled]="disableToolStatus(projectFeaturePermission.work_package_plan)"
                                       name="ca-wpss"
                                       id="ca-wpss">
                                <label class="custom-control-label" for="ca-wpss">
                                    <h6>{{project.custom_field.wpp_phrase}}</h6>
                                </label>
                                <i *ngIf="project.project_category=='company-project'" class="fas fa-cog ml-1 cowSetting cursor-pointer" (click)="altPhraseWppModal()"></i>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.time_management === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.time_management)" container="body">
                            <div *ngIf="project.project_category == 'default'" class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.time_management)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.time_management"
                                       [checked]="project.company_additional_project_section_access.time_management"
                                       [disabled]="disableToolStatus(projectFeaturePermission.time_management)"
                                       name="ca-tm"
                                       id="ca-tm">
                                <label class="custom-control-label" for="ca-tm"><h6>Time Management</h6></label>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td [ngbTooltip]="projectFeaturePermission.site_messaging === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.site_messaging)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.site_messaging)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.site_messaging"
                                       [checked]="project.company_additional_project_section_access.site_messaging"
                                       [disabled]="disableToolStatus(projectFeaturePermission.site_messaging)"
                                       name="sm"
                                       id="sm">
                                <label class="custom-control-label" for="sm"><h6>Site Messaging</h6></label>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.permit === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.permit)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.permit)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.permit"
                                       [checked]="project.company_additional_project_section_access.permit"
                                       [disabled]="disableToolStatus(projectFeaturePermission.permit)"
                                       name="ca-permit"
                                       id="ca-permit">
                                <label class="custom-control-label" for="ca-permit"><h6>Permit</h6></label>
                                <i *ngIf="project.project_category=='company-project'" class="fas fa-cog ml-1 cowSetting cursor-pointer" (click)="altPhrasePermitModal()"></i>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        </form>
        <block-loader [show]="(modalRequestInProgress)"></block-loader>
</i-modal>

<i-modal #manageCompanyProjectAdminModal title="Manage Access" size="lg" [showCancel]="false" rightPrimaryBtnTxt="Done" [rightPrimaryBtnDisabled]="hasNonSavedAccessRow()" (onClickRightPB)="closeManageAdminModal($event)">
        <form novalidate #manageCPAForm="ngForm">
            <div class="form-group">
                <label>Allowed Users</label>
                <i class="fa fa-plus-circle text-primary float-right cursor-pointer" (click)="addCPAUser()"></i>

                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th class="text-center">Email</th>
                        <th class="text-center">Nominated Manager</th>
                        <th class="text-center">Action</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let item of company_project_admins; let i = index;">
                        <td>
                            <user-search-box
                                [userEmail]="item.email"
                                [disabled]="!!item.role_id"
                                [placeholder]="item.name"
                                [required]="true"
                                (validityChanged)="onIsValidCPAEmail($event, i)"
                                (inputChanged)="onValidCPAEmailInput($event, i)">
                            </user-search-box>
                        </td>

                        <td class="text-center">
                            <input type="checkbox" class="cursor-pointer" value="nominated"
                                   [name]="'designation'+i" [checked]="(item.designation || []).includes('nominated')"
                                   (change)="changeModel($event, item.designation, 'nominated', i)"/>
                        </td>

                        <td class="text-center">
                            <div class="d-flex justify-content-around align-items-center">
                                <i *ngIf="!item.role_id" [ngClass]="{'text-success cursor-pointer': item.isValid}" [style.opacity]="(item.isValid ? 1 : 0.4)" class="fas fa-save x-large-font" (click)="saveNewCPAUser(item, i)"></i>
                                <i *ngIf="item.role_id" [ngClass]="{'text-success cursor-pointer': true}" class="fas fa-save x-large-font" (click)="updateCPAUser(item, i)"></i>
                                <span class="material-symbols-outlined text-danger cursor-pointer" (click)="removeCPAUser(item, i)" *ngIf="item.role_id && (authUser$.id !== item.id)" >
                                    delete
                                </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </form>
        <block-loader [show]="(cpaModalRequestInProgress)"></block-loader>
</i-modal>
<company-site-config-modal
    #projectSiteConfigPopup
    [project]="project">
</company-site-config-modal>

<daily-activities-import-modal
    #dailyActivitiesImportModalHtml
    [project]="project" [companyId]="employerId">
</daily-activities-import-modal>

<cow-owener-tagging-modal
    #cowOwenerTaggingPopup
    [project]="project">
</cow-owener-tagging-modal>

<cow-site-drawings-modal
    #cowSiteDrawingsPopup
    [project]="project">
</cow-site-drawings-modal>

<alternative-phrase-setting
    #altPhraseTb [project]="project"
    [defaultPhrase]="'Task Briefings'" [feature]="'task_briefings'">.rams"
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseWpp [project]="project"
    [defaultPhrase]="'Work Package Plans'" [feature]="'work_package_plan'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhrasePermit [project]="project"
    [defaultPhrase]="'Permits'" [feature]="'permit'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseRams [project]="project"
    [defaultPhrase]="'RAMS'" [feature]="'rams'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseQcl [project]="project"
    [defaultPhrase]="'ITPs'" [feature]="'quality_checklist'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseT5s [project]="project"
    [defaultPhrase]="'Take 5s'" [feature]="'take_5s'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseDa [project]="project"
    [defaultPhrase]="'Daily Activities'" [feature]="'daily_activities'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseCn [project]="project"
    [defaultPhrase]="'Collection Notes'" [feature]="'collection_notes'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseDn [project]="project"
    [defaultPhrase]="'Delivery Notes'" [feature]="'delivery_notes'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseGc [project]="project"
    [defaultPhrase]="'Good Calls'" [feature]="'good_calls'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseObrs [project]="project"
    [defaultPhrase]="'Observations'" [feature]="'observations'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhrasePowra [project]="project"
    [defaultPhrase]="'POWRA'" [feature]="'powra'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseCc [project]="project"
    [defaultPhrase]="'Close Calls'" [feature]="'close_calls'">
</alternative-phrase-setting>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

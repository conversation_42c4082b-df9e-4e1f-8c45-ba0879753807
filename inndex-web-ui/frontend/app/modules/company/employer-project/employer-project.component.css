agm-map{
    /*height:83vh;*/
    height: calc(100vh - 64px);
    width:100%
}

.pl-55 {
  padding-left: 0px;
}

@media screen and (max-width:1024px){ 
  .pl-55 {
    padding-left:55px !important;
  }
}​

.project-list{
    border: 1px solid black;
    max-height:81vh;
}

.list-group-flush .list-group-item { border-bottom:0}
.gm-svpc{display:none}
.mapFilterOverlay{
    position: absolute;
    z-index: 1;
    background: #fff;
    top: 6px;
    bottom: 6px;
    left: 6px;
    min-width: 280px;
    max-width: 280px;
    /* overflow-y: auto; */
    border-radius: 10px;
}


/* .mapFilterOverlay  */
.panel-group{
  /* padding: 5px; */
  overflow-y: auto;
  height: 93%;
  border-radius: 10px;
}
:host ::ng-deep .card-header .btn.btn-link {
  color: var(--spanish-gray);
}
:host ::ng-deep .card-header .btn.btn-link.collapsed {
  color: var(--black);
}
:host ::ng-deep .card-header .btn {
    font-size: 16px;
    margin-bottom: 0;
    padding: 10px 0;
    display: block;
    width: 100%;
    text-align: inherit;
    text-decoration: none;
    font-weight: 400;
}
:host ::ng-deep .card-header .btn:focus{
    box-shadow: none;
}
.mapFilterOverlay .projectIcons{
  height: 14px;
  margin-right: 5px;
}
.mapFilterOverlay ul{
  list-style-type: none;
  padding-left: 0;
}
.mapFilterOverlay ul li{
  border-top: 1px solid var(--light-silver);
  display: inline-block;
  width: 100%;
  line-height: 30px;
}
.mapFilterOverlay ul li a{
  font-size: 14px;
  color: var(--black);
  display: inline-block;
  width: 94%;
  padding: 5px 0;
  text-decoration: none;
  cursor: pointer;
}
.mapFilterOverlay ul li a:hover{
  color: #333;
}
/* right tick for show selected */
/* .mapFilterOverlay ul li.selected a:after{ 
  content: "\2713";
  float: right;
  color: grey;
} */
/* .mapFilterOverlaysearch{
  text-align: center;
}
.mapFilterOverlaysearch input{
  border-width: 0 0 1px 0;
  border-color: #ccc;
  width: 90%;
  margin:10px auto 6px auto;
  font-size: 14px;
  background-size: 13px;
  background-repeat: no-repeat;
  background-position: 100%;
  height: 35px;
  background-image:url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMTkuMC4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogNi4wMCBCdWlsZCAwKSAgLS0+CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiBpZD0iQ2FwYV8xIiB4PSIwcHgiIHk9IjBweCIgdmlld0JveD0iMCAwIDUxMiA1MTIiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDUxMiA1MTI7IiB4bWw6c3BhY2U9InByZXNlcnZlIiB3aWR0aD0iNTEycHgiIGhlaWdodD0iNTEycHgiPgo8Zz4KCTxnPgoJCTxwYXRoIGQ9Ik0yMjUuNDc0LDBDMTAxLjE1MSwwLDAsMTAxLjE1MSwwLDIyNS40NzRjMCwxMjQuMzMsMTAxLjE1MSwyMjUuNDc0LDIyNS40NzQsMjI1LjQ3NCAgICBjMTI0LjMzLDAsMjI1LjQ3NC0xMDEuMTQ0LDIyNS40NzQtMjI1LjQ3NEM0NTAuOTQ4LDEwMS4xNTEsMzQ5LjgwNCwwLDIyNS40NzQsMHogTTIyNS40NzQsNDA5LjMyMyAgICBjLTEwMS4zNzMsMC0xODMuODQ4LTgyLjQ3NS0xODMuODQ4LTE4My44NDhTMTI0LjEwMSw0MS42MjYsMjI1LjQ3NCw0MS42MjZzMTgzLjg0OCw4Mi40NzUsMTgzLjg0OCwxODMuODQ4ICAgIFMzMjYuODQ3LDQwOS4zMjMsMjI1LjQ3NCw0MDkuMzIzeiIgZmlsbD0iIzAwMDAwMCIvPgoJPC9nPgo8L2c+CjxnPgoJPGc+CgkJPHBhdGggZD0iTTUwNS45MDIsNDc2LjQ3MkwzODYuNTc0LDM1Ny4xNDRjLTguMTMxLTguMTMxLTIxLjI5OS04LjEzMS0yOS40MywwYy04LjEzMSw4LjEyNC04LjEzMSwyMS4zMDYsMCwyOS40M2wxMTkuMzI4LDExOS4zMjggICAgYzQuMDY1LDQuMDY1LDkuMzg3LDYuMDk4LDE0LjcxNSw2LjA5OGM1LjMyMSwwLDEwLjY0OS0yLjAzMywxNC43MTUtNi4wOThDNTE0LjAzMyw0OTcuNzc4LDUxNC4wMzMsNDg0LjU5Niw1MDUuOTAyLDQ3Ni40NzJ6IiBmaWxsPSIjMDAwMDAwIi8+Cgk8L2c+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPC9zdmc+Cg==);
}
.mapFilterOverlaysearch input:focus{
  outline: none;
} */
/* .reset a{


  border-width: 0 0 1px 0;
  color: #858c91;
  border-color: #ccc;
  width: 90%;
  margin:10px auto 6px auto;
  font-size: 14px;
  background-size: 13px;
  background-repeat: no-repeat;
  background-position: 100%;
  height: 35px;
  background-image:url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMTkuMC4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogNi4wMCBCdWlsZCAwKSAgLS0+CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiBpZD0iQ2FwYV8xIiB4PSIwcHgiIHk9IjBweCIgdmlld0JveD0iMCAwIDUxMiA1MTIiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDUxMiA1MTI7IiB4bWw6c3BhY2U9InByZXNlcnZlIiB3aWR0aD0iNTEycHgiIGhlaWdodD0iNTEycHgiPgo8Zz4KCTxnPgoJCTxwYXRoIGQ9Ik0yMjUuNDc0LDBDMTAxLjE1MSwwLDAsMTAxLjE1MSwwLDIyNS40NzRjMCwxMjQuMzMsMTAxLjE1MSwyMjUuNDc0LDIyNS40NzQsMjI1LjQ3NCAgICBjMTI0LjMzLDAsMjI1LjQ3NC0xMDEuMTQ0LDIyNS40NzQtMjI1LjQ3NEM0NTAuOTQ4LDEwMS4xNTEsMzQ5LjgwNCwwLDIyNS40NzQsMHogTTIyNS40NzQsNDA5LjMyMyAgICBjLTEwMS4zNzMsMC0xODMuODQ4LTgyLjQ3NS0xODMuODQ4LTE4My44NDhTMTI0LjEwMSw0MS42MjYsMjI1LjQ3NCw0MS42MjZzMTgzLjg0OCw4Mi40NzUsMTgzLjg0OCwxODMuODQ4ICAgIFMzMjYuODQ3LDQwOS4zMjMsMjI1LjQ3NCw0MDkuMzIzeiIgZmlsbD0iIzAwMDAwMCIvPgoJPC9nPgo8L2c+CjxnPgoJPGc+CgkJPHBhdGggZD0iTTUwNS45MDIsNDc2LjQ3MkwzODYuNTc0LDM1Ny4xNDRjLTguMTMxLTguMTMxLTIxLjI5OS04LjEzMS0yOS40MywwYy04LjEzMSw4LjEyNC04LjEzMSwyMS4zMDYsMCwyOS40M2wxMTkuMzI4LDExOS4zMjggICAgYzQuMDY1LDQuMDY1LDkuMzg3LDYuMDk4LDE0LjcxNSw2LjA5OGM1LjMyMSwwLDEwLjY0OS0yLjAzMywxNC43MTUtNi4wOThDNTE0LjAzMyw0OTcuNzc4LDUxNC4wMzMsNDg0LjU5Niw1MDUuOTAyLDQ3Ni40NzJ6IiBmaWxsPSIjMDAwMDAwIi8+Cgk8L2c+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPC9zdmc+Cg==);
} */
 .reset {
  padding-left: 5px;
  padding-top: 5px;
  font-size: 14px;
 }
.reset a {
  position: relative;
  display: block;
  border-bottom: 1px solid #D6A537;
  color: #D6A537;
  padding-top: 0.23077em;
  padding-bottom: 5px;
  text-decoration: none;
}


.panel-group#accordion::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: var(--anti-flash-white);
}

.panel-group#accordion::-webkit-scrollbar {
  width: 5px;
  background-color: var(--anti-flash-white);
}

.panel-group#accordion::-webkit-scrollbar-thumb {
  background-color: var(--spanish-gray);
}


/* text-align: left;
padding: 10px 0;
color: #333;
padding: 0 10px;
border-bottom: 1px solid #999;
margin: 0px; */

.cowSetting {position: relative;bottom: 2px;}
.inline-input {
  display: inline-block;
  width: 95%;
}

.geofence-icon{
  float: right;
  margin-top:8px;
}

.search-input {
  margin: 0px !important;
}

/* accordion css */
:host ::ng-deep .accordion .card {
  padding: 0 2px;
  border-bottom: 1px solid #999;
}
:host ::ng-deep .accordion .card-header .btn:after {
    content: "❮";
    float: right;
    color: var(--spanish-gray);
    transform: rotate(90deg);
}
:host ::ng-deep .accordion .card-header .btn.collapsed:after {
    content: "❯";
    color: var(--black);
}
:host ::ng-deep .accordion .card:last-child{
  border-bottom: 0;
}
:host ::ng-deep .accordion .card-header{
  padding: 0 14px;
  margin: 0;
  background-color: transparent;
  border-bottom: 0;
}
:host ::ng-deep .accordion .card {
  border: none;
}
:host ::ng-deep .accordion .card-body{
  padding: 0 0 0 14px;
}

.text-lime-green {
  color: var(--success);
}
.text-fuel-yellow {
  color: var(--fuel-yellow);
}
.ul-wrap {
  border-bottom: 1px solid var(--light-silver);
  margin-bottom: 0.5rem;
}
:host ::ng-deep .accordion .card-header .btn:hover {
  background-color: transparent !important;
}
td {
  vertical-align: middle;
}
.c-config .custom-control{
  padding-left: 0;
}
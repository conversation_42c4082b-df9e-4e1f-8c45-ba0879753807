.modal-scroll { max-height: 940px; overflow-y: auto; overflow-x: hidden; }
.customLabel.custom-control-label::before, .customLabel.custom-control-label::after { left: -1rem; }

.sectionHead {
    color: #212529;
    background-color: #edb61d;
    border-color: #edb61d;
    border-radius: 0.2em;
}

.pdfFieldSelector {
    float: right;
    position: relative;
    .dropbtn {
        font-size: 12px;
        outline: none;
        background-color: #F8FBFF;
        border-style: dashed !important;
        border: solid 1px #67A4FF;
        width: 400px;
    }
    /*&:hover {
        .pdfFieldOptions {
            display: block;
        }
    }*/
}
.pdfFieldOptions {
    /*display: none;*/
    position: absolute;
    background-color: var(--lotion);
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    width: 100%;
    z-index: 99999;
    white-space: nowrap;
    .ddFieldOption {
        padding: 0.5rem 1.25rem; display: flex;
    }
}

.btn-im-helmet:hover, .btn-im-helmet:active { background-color: #edb61d; }
.pdfFieldsNav { background-color: var(--anti-flash-white); border-radius: 0.2em; }
.pdfFieldsNav .nav-link {  font-weight: 400;  }
.pdfFieldsNav .nav-link.active { background-color: #FFFFFF; color: #000000 !important; font-weight: 600 !important; }
.fa.fa-chevron-up, .fa.fa-chevron-down, .fa.fa-bars { color: #000000; }
.fieldsTable { font-size: 16px; }
.fieldsTable .colOne { width: 5%; }
.fieldsTable .colTwo { width: 21%; background-color: #f8f8f8; padding: 0.26em 0.75rem !important }
.col-one-sign-off-config { width: 8% !important; }
.col-two-sign-off-config { width: 35% !important; }
table.table.table-bordered.fieldsTable tr td {
    padding: 0px;
}

table.table.table-bordered.fieldsTable tr td input, table.table.table-bordered.fieldsTable tr td select, table.table.table-bordered.fieldsTable tr td .form-control {
    border: none;
    border-radius: unset;
}

table.table.table-bordered.fieldsTable tr td input:focus, table.table.table-bordered.fieldsTable tr td select:focus, table.table.table-bordered.fieldsTable tr td .form-control:focus {
    box-shadow: none;
}

.sectionFields-list .drag-box, .fillablepdffields-list .drag-box, .signaturefields-list .drag-box { background-color: #fff; }
.requireCloseout .custom-control-input[disabled] ~ .custom-control-label::before, .requireCloseout .custom-control-input:disabled ~ .custom-control-label::before { background-color: var(--light-silver); border: 1px solid var(--anti-flash-white); }
.fillableSelect > .ng-select-container { border: none !important; }

.fieldsTable .form-control:disabled, .fieldsTable .form-control[readonly] {
    background-color: #fff !important;
    color: var(--spanish-gray);
}
::ng-deep ng-select.no-border {
    .ng-select-container {
        border: none;
    }
    .ng-select-container:hover {
        box-shadow: none;
    }
}

::ng-deep .fillableSelect span.ng-value-label {
    max-width: 480px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}

::ng-deep .dummySelector .ng-arrow-wrapper {
    display: none;
}

::ng-deep .unlinkedSectionsDD.ng-select .ng-placeholder { color: var(--black) !important; }

.heightInherit { height: inherit}
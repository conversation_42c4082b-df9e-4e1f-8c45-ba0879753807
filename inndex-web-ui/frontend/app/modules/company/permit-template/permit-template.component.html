<div class="row">
    <div class="d-flex w-100 p-3 flex-column flex-md-row flex-wrap justify-content-between">
        <div class="col-md-6 p-0">
            <search-with-filters (searchEmitter)="searchFilter($event)"></search-with-filters>
        </div>
        <div class="d-sm-block d-flex">
            <action-button
                [newFeatureTitle]="'New Permit Template'"
                (onOpenAddNew)="createNewPermitModal()"
                [showActionDropdown]="false">
            </action-button>
        </div>
    </div>

    <div class="col-12">
        <ngx-skeleton-loader *ngIf="isDataLoading" count="8" [theme]="{ 'border-radius': '0', height: '50px' }">
        </ngx-skeleton-loader>
    </div>
    <ng-container *ngIf="!isDataLoading">
        <div class="col-12">
            <div class="table-responsive-sm ngx-datatable-custom">
                <ngx-datatable class="bootstrap table table-hover table-sm"
                               [rows]="permitTemplates"
                               [columns]="[
                                    {name:'Ref No.', prop: 'ref_number', sortable: false, headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'py-1 A table-cell-text', cellTemplate: refNoColumn},
                                    {name:'Permit Type', prop: 'permit_type', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 B table-cell-text', cellTemplate: pTypeColumn},
                                    {name:'Created By', prop: 'created_by.name', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: createdByColumn},
                                    {name:'Date Created', prop: 'createdAt', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 C table-cell-text', cellTemplate: createdAt},
                                    {name:'Active', prop: 'id', sortable: false, headerClass: 'py-2 font-weight-bold', width:200, cellClass: 'py-1 D table-cell-text action-column', cellTemplate: actionColumn}
                                ]"
                               [columnMode]="'force'"
                               [footerHeight]="36"
                               [rowHeight]="'auto'"
                               [externalPaging]="true"
                               [count]="page.totalElements"
                               [offset]="page.pageNumber"
                               [limit]="page.size"
                               (page)="pageCallback($event)"
                >
                    <ng-template #refNoColumn let-row="row" let-value="value">
                        <span appTooltip>{{ value }}</span>
                    </ng-template>
                    <ng-template #pTypeColumn let-row="row" let-value="value">
                        <a href="javascript:void(0)" class="text-info font-weight-bold" (click)="editPermitTemplateModal(row)"><span appTooltip>{{ row.permit_type }}</span></a>
                    </ng-template>
                    <ng-template #createdByColumn let-row="row" let-value="value">
                        <span appTooltip>{{ value }}</span>
                    </ng-template>
                    <ng-template #createdAt let-row="row" let-value="value">
                        {{ dayjs(+value).format(displayDateTimeFormat) }}
                    </ng-template>
                    <ng-template #actionColumn let-row="row" let-value="value">
                        <div style="line-height: 36px;" class="px-3">
                            <label class="checkbox-switch-v2 mb-0">
                                <input type="checkbox" [checked]="row.is_active"
                                [name]="'is_active_switch_'+row.id"
                                [id]="'is_active_switch_'+row.id"
                                (change)="updatePermitTemplateStatus(row, $event)">
                                <span class="slider-v2 round"></span>
                            </label>
                        </div>
                    </ng-template>
                </ngx-datatable>
            </div>
        </div>
    </ng-container>
</div>

<!-- Create Permit Template Modal -->
<i-modal #permitTemplatesModal [title]="(permitTemplate.id) ? 'Update Permit Template' : 'Create Permit Template'" size="lg"  [windowClass]="'xl-modal'" cancelBtnText="Cancel" [showCancel]="false" [confirmBeforeClose]="true"  (onClickRightPB)="savePermitTemplate($event)" rightPrimaryBtnTxt="Save" [rightPrimaryBtnDisabled]="!permitTemplateForm.valid">
    <form novalidate #permitTemplateForm="ngForm" style="height: 100%">
        <div class="col-12 d-flex p-0 heightInherit">
            <div class="py-4 px-5 modal-scroll col-6" style="max-height: 82vh;">
                <div class="form-group">
                    <label>Permit Reference No. <small class="required-asterisk">*</small></label>
                    <input type="text" class="form-control" [(ngModel)]="permitTemplate.ref_number" name="ref_number" required/>
                </div>

                <div class="form-group">
                    <label>Permit Type <small class="required-asterisk">*</small></label>
                    <input type="text" class="form-control" [(ngModel)]="permitTemplate.permit_type" name="permit_type" required/>
                </div>

                <div class="form-group">
                    <label>Reference Documents</label>
                    <div *ngFor="let c of permitTemplate.ref_docs; let i = index">
                        <file-uploader-v2
                                class="mt-1 mb-0 d-block refDocUploader"
                                [init]="c"
                                [multipleUpload]="true"
                                [category]="'ref-doc-uploader'"
                                (uploadDone)="refDocUploadDone($event)"
                                (deleteFileDone)="deleteRefDoc($event, i)"
                                [allowedMimeType]="ALLOWED_MIME_TYPE"
                                #attachmentUploader
                                [showDeleteBtn]="true"
                                [showFileName]="false"
                                [disabled]="false"
                                [output_v2]="true"
                        >
                        </file-uploader-v2>
                    </div>
                </div>

                <div class="form-group d-flex align-items-center">
                    <label class="mr-2 mb-0" style="width: 90px;">Valid For <small class="required-asterisk">*</small></label>
                    <div class="d-flex w-100">
                        <div class="mr-2 w-50">
                            <ng-select name="day" #day="ngModel" [(ngModel)]="expireDay" class="dropdown-list" placeholder="Days" appendTo="body" required (change)="selectValidFor()">
                                <ng-option *ngFor="let day of META_DAYS" [value]="day.value">{{ day.name }}</ng-option>
                            </ng-select>
                        </div>
                        <div class="ml-2 w-50">
                            <ng-select name="month" #month="ngModel" [(ngModel)]="expireHour" class="dropdown-list" placeholder="Hours" appendTo="body" required (change)="selectValidFor()">
                                <ng-option *ngFor="let month of META_HOURS" [value]="month.value">{{ month.name }}</ng-option>
                            </ng-select>
                        </div>
                    </div>
                </div>

                <div class="form-group col-12 p-0 d-inline-block m-0">
                    <label class="mr-4 float-left cursor-pointer" for="include_mandatory_attachments">Include Mandatory Attachments</label>
                    <div class="custom-control custom-checkbox float-right">
                        <input type="checkbox" class="custom-control-input" name="include_mandatory_attachments"
                               id="include_mandatory_attachments" [checked]="permitTemplate.include_mandatory_attachments" (click)="toggleIncludeAttachments($event)"/>
                        <label class="custom-control-label customLabel font-weight-bold" for="include_mandatory_attachments"></label>
                    </div>
                </div>

                <ng-container *ngIf="permitTemplate.include_mandatory_attachments">
                    <div class="form-group">
                        <label class="col-12 p-0 m-0">Attachment Title<small class="required-asterisk">*</small></label>
                        <div *ngFor="let attachmentTitle of (mandatory_attachments_title || []); trackBy : trackByRowIndex; let i = index;" class="col-12 p-0 d-flex align-items-center">
                            <div class="col-11 px-0 py-2 d-flex align-items-center">
                                <div class="col-md-12 p-0 d-inline-block text-truncate">
                                    <input type="text" class="form-control" [name]="'attachment_title_'+i"
                                           [(ngModel)]="mandatory_attachments_title[i]" [required]="(i === 0 && mandatory_attachments_title.length === 1) || i > 0"
                                           placeholder="Enter attachment title" autocomplete="off"/>
                                </div>
                            </div>
                            <button [disabled]="!mandatory_attachments_title[i].length" class="btn col-md-1 float-right pr-0 align-items-center justify-content-center" *ngIf="i == 0" (click)="addAttachmentTitle($event)">
                                <i class="fa fa-plus-circle cursor-pointer float-right" style="color: #235fc7;"></i>
                            </button>
                            <div class="col-md-1 pr-0 float-right align-items-center justify-content-center" *ngIf="i > 0">
                                       <span class="material-symbols-outlined text-danger cursor-pointer float-right"
                                             (click)="removeAttachmentTitle(i)">
                                        delete
                                    </span>
                            </div>
                        </div>
                    </div>
                </ng-container>

                <div class="form-group col-12 p-0 d-inline-block mb-1" *ngIf="permitTemplate.fillable_pdf_ref && fillablePdfFormFields.length">
                    <label [ngClass]="{'mr-4 float-left cursor-pointer': true, 'spanish-gray-font': permitTemplate.id}" for="require_closeout">Require Closeout</label>
                    <div class="custom-control custom-checkbox float-right requireCloseout">
                        <input type="checkbox" class="custom-control-input" name="require_closeout"
                               id="require_closeout" [(ngModel)]="permitTemplate.require_closeout" (change)="toggleRequireCloseout($event)"
                               [disabled]="!!(permitTemplate.id)"/>
                        <label class="custom-control-label customLabel font-weight-bold" for="require_closeout"></label>
                    </div>
                </div>

                <div class="form-group col-12 p-0 d-inline-block mb-1">
                    <label class="mr-4 float-left cursor-pointer" for="include_register">Include <span i18n="@@operative">Operative</span> Register</label>
                    <div class="custom-control custom-checkbox float-right requireCloseout">
                        <input type="checkbox" class="custom-control-input" name="include_register"
                               id="include_register" [(ngModel)]="permitTemplate.include_register" (change)="toggleIncludeRegister($event)"/>
                        <label class="custom-control-label customLabel font-weight-bold" for="include_register"></label>
                    </div>
                </div>

                <div class="form-group" *ngIf="permitTemplate.include_register">
                    <label>Take <span i18n="@@operative">Operative</span> Register When <small class="required-asterisk">*</small></label>
                    <ng-select [items]="take_register_options"
                               bindLabel="label"
                               bindValue="value"
                               placeholder="Choose a option"
                               name="take_register_when"
                               [(ngModel)]="permitTemplate.take_register_when"
                               required
                    >
                    </ng-select>
                </div>

                <div class="form-group" *ngIf="permitTemplate.include_register">
                    <label>Register Signature <small class="required-asterisk">*</small></label>
                    <ng-select [items]="register_signature_options"
                               bindLabel="label"
                               bindValue="value"
                               placeholder="Choose a option"
                               name="register_config_signature"
                               [(ngModel)]="permitTemplate.register_config.signature"
                               required
                    >
                    </ng-select>
                </div>

                <div class="form-group col-12 p-0 d-inline-block mb-1" *ngIf="permitTemplate.include_register">
                    <label class="mr-4 float-left cursor-pointer" for="register_config_user_list">To take a register, use a list of inducted users rather than on-site users?</label>
                    <div class="custom-control custom-checkbox float-right requireCloseout">
                        <input type="checkbox" class="custom-control-input" name="register_config_user_list"
                               id="register_config_user_list" [checked]="permitTemplate.register_config.user_list === 'inducted_users'" (change)="toggleUserListSelection($event)"/>
                        <label class="custom-control-label customLabel font-weight-bold" for="register_config_user_list"></label>
                    </div>
                </div>
            </div>
            <div class="col-6 bl-light-silver p-0 modal-scroll heightInherit" style="max-height: 82vh;">
                <div class="pt-4 px-5 col-12">
                    <div class="form-group">
                        <label>Fillable PDF<small class="required-asterisk ">*</small></label>
                        <file-uploader-v2
                            #fillablePdfUploader
                            class="mt-1 mb-0"
                            [init]="fillable_pdf_file"
                            (uploadDone)="uploadFillablePdfDone($event)"
                            (deleteFileDone)="deleteFillablePdf($event)"
                            [allowedMimeType]="['application/pdf', 'application/x-pdf']"
                            [category]="'permit-fillable-pdf'"
                            [showDeleteBtn]="(permitTemplate.id) ? false: true"
                            [showFileName]="false"
                            [disabled]="false"
                            [clearable]="true"
                            [output_v2]="true"
                        >
                        </file-uploader-v2>
                    </div>

                    <div *ngIf="permitTemplate.fillable_pdf_ref && fillablePdfFormFields.length" class="form-group">
                        <div class="form-group">
                            <label>Font size of fillable fields<small class="required-asterisk">*</small></label>
                            <ng-select name="font_size" [(ngModel)]="permitTemplate.font_size" required>
                                <ng-option *ngFor="let num of fontSizeOptions" [value]="num">{{ num }}</ng-option>
                            </ng-select>
                        </div>
                        <label class="font-weight-bold col-11 p-0">The fields below were detected in the fillable PDF. You will need to cross-check the field labels and types and update as necessary. For any signature field, you will need to set them as E-Signature field component in your fillable PDF:</label>
                        <ul class="nav nav-pills nav-justified my-3 pdfFieldsNav align-items-center d-flex">
                            <li class="nav-item pr-0 cursor-pointer my-1 mr-1 pl-1">
                                <strong [ngClass]="{'nav-link': true, 'active': (contentToVisible == 'unassigned') }" (click)="showFields('unassigned')">Template Configuration</strong>
                            </li>
                            <li class="nav-item cursor-pointer my-1 ml-1 pr-1">
                                <strong [ngClass]="{'nav-link': true, 'active': (contentToVisible == 'signatures') }" (click)="showFields('signatures')">Sign-off/Closeout Configuration</strong>
                            </li>
                        </ul>
                        <div [ngClass]="{'form-group': true, 'd-none': contentToVisible == 'signatures'}">
                            <label class="mr-4 mb-0">Add New Section</label>
                            <ng-container *ngFor="let section of (field_sections || []); trackBy : trackByRowIndex; let i = index;">
                                <div class="p-0 mx-0">
                                    <div [ngClass]="{'col-12 px-0 d-flex align-items-center': true, 'py-0 mb-2': i > 0, 'py-2': i == 0}">
                                        <div [ngClass]="{'p-0 col-11 align-items-center d-flex': true, 'sectionHead': i > 0}">
                                                <span *ngIf="i > 0" class="col-1 cursor-pointer moveSection" (click)="moveSectionOneDown(i)">
                                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.33348 9.67459C3.33348 10.7141 3.68337 11.6014 4.38316 12.3365C5.08295 13.0715 5.94994 13.4647 6.98412 13.5159H7.45691L6.08512 12.1361L6.96331 11.2579L9.86391 14.1666L6.96331 17.0832L6.08512 16.205L7.48256 14.7915H7.00977C5.62623 14.7403 4.4593 14.2218 3.50898 13.2363C2.55866 12.2507 2.0835 11.0677 2.0835 9.6874C2.0835 8.26433 2.57575 7.05787 3.56027 6.06802C4.54477 5.07818 5.74588 4.58325 7.1636 4.58325H9.87193V5.83323H7.1636C6.10057 5.83323 5.19646 6.20503 4.45127 6.94863C3.70607 7.69221 3.33348 8.60086 3.33348 9.67459ZM11.6989 14.7915V13.5416H17.9168V14.7915H11.6989ZM11.6989 10.3124V9.06242H17.9168V10.3124H11.6989ZM11.6989 5.83323V4.58325H17.9168V5.83323H11.6989Z" fill="black"/></svg>
                                                </span>
                                            <div [ngClass]="{'p-0 d-inline-block text-truncate': true, 'col-md-10': i > 0, 'col-md-12': i == 0}">
                                                <input type="text" class="form-control" [ngClass]="{'form-control': true, 'btn-im-helmet': (i > 0 && section.is_disabled)}" [name]="'title_'+i"
                                                       [(ngModel)]="section.title" [disabled]="section.is_disabled" pattern="[!@#$%^&*()_+={}\[\]:;'<>,.?/\\|~\w\s]+"
                                                       placeholder="Enter new section title" autocomplete="off" [required]="i > 0"/>
                                            </div>
                                            <span *ngIf="i > 0" class="col-1 cursor-pointer editSection" (click)="enableInputField(i)">
                                                    <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="24px" viewBox="0 0 24 24" width="24px" fill="#212529"><rect fill="none" height="24" width="24"/><path d="M14,11c0,0.55-0.45,1-1,1H4c-0.55,0-1-0.45-1-1s0.45-1,1-1h9C13.55,10,14,10.45,14,11z M3,7c0,0.55,0.45,1,1,1h9 c0.55,0,1-0.45,1-1s-0.45-1-1-1H4C3.45,6,3,6.45,3,7z M10,15c0-0.55-0.45-1-1-1H4c-0.55,0-1,0.45-1,1s0.45,1,1,1h5 C9.55,16,10,15.55,10,15z M18.01,12.87l0.71-0.71c0.39-0.39,1.02-0.39,1.41,0l0.71,0.71c0.39,0.39,0.39,1.02,0,1.41l-0.71,0.71 L18.01,12.87z M17.3,13.58l-5.16,5.16C12.05,18.83,12,18.95,12,19.09v1.41c0,0.28,0.22,0.5,0.5,0.5h1.41c0.13,0,0.26-0.05,0.35-0.15 l5.16-5.16L17.3,13.58z"/></svg>
                                                </span>
                                        </div>
                                        <button [disabled]="!this.titleRegex.test(field_sections[0]?.title) || !field_sections[0]?.title.length" class="btn col-md-1 p-0 float-right align-items-center justify-content-center" *ngIf="i == 0" (click)="addFieldSection(section.section_id)">
                                            <i class="fa fa-plus-circle float-right" style="color: #235fc7;"></i>
                                        </button>
                                        <div class="col-md-1 p-0 float-right align-items-center justify-content-center" *ngIf="i > 0">
                                                   <span class="material-symbols-outlined text-danger cursor-pointer float-right"
                                                         (click)="removeFieldSection(i)">
                                                    delete
                                                </span>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="i > 0" class="row p-0 mx-0">
                                    <div class="col-11 p-0 mb-2">
                                        <div class="pdfFieldSelector w-100">
                                            <button class="btn dropbtn text-brandeis-blue w-100" (click)="toggleFieldSelector(i, section.section_id)">
                                                Click here to add fields
                                            </button>
                                            <div [ngClass]="{'pdfFieldOptions': true, 'd-none': !section.show_options }">
                                                <div style="height: 300px; overflow: auto;">
                                                    <ng-container *ngFor="let field of fillablePdfFormFields; let j = index;">
                                                        <div class="ddFieldOption" [ngClass]="{'d-none': signatureTypes.includes(field.field_type) || field.linked_with}">
                                                            <div class="custom-control custom-checkbox float-right w-100 requireCloseout"  style="font-size: 14px; z-index: auto">
                                                                <input type="checkbox"
                                                                       class="custom-control-input"
                                                                       [id]="'s_field_'+j+i"
                                                                       (click)="toggleSectionField(field.field_name, section.section_id)"
                                                                       [checked]="selectedSectionFields.includes(field.field_name)"
                                                                       [disabled]="!!(+field.section_id && (+field.section_id != +section.section_id))"
                                                                />
                                                                <label *ngIf="!+field.section_id else elseLabel" class="custom-control-label" [for]="'s_field_'+j+i" style="padding-top: 2px; max-width: 90%">
                                                                    <span class='cursor-pointer d-block text-truncate'>{{ field.field_label }}</span>
                                                                </label>

                                                                <ng-template #elseLabel>
                                                                    <label class="custom-control-label" [ngbTooltip]="'Assigned: '+getSectionTitle(field.section_id)" [placement]="(j == 0) ? 'bottom-left' : 'top-left'" [openDelay]="200" [closeDelay]="500"  [for]="'s_field_'+j+i" style="padding-top: 2px; max-width: 90%">
                                                                        <span [ngClass]="{'cursor-pointer d-block text-truncate': true, 'spanish-gray-font': !!(+field.section_id && (+field.section_id != +section.section_id))}">{{ field.field_label }}</span>
                                                                    </label>
                                                                </ng-template>
                                                            </div>
                                                        </div>
                                                    </ng-container>
                                                </div>

                                                <div class="col-md-12 d-flex align-items-center justify-content-end px-2 py-2 border-top">
                                                    <!-- <div role="button" class="px-4" style="line-height: 38px; border-radius: 0.25rem; color: #0066FF;"> <span class="fw-600" (click)="toggleFieldSelector(i)">Cancel</span></div> -->
                                                    <button type="submit" class="btn btn-brandeis-blue px-4 ml-1" (click)="assignSectionToFields(i, section.section_id)"> Done </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div *ngIf="i > 0" class="d-block">
                                    <div class="p-0 mb-2 sectionFields-list" [id]="section.section_id" [dragula]="'fillablepdffields_'+section.section_id">
                                        <ng-template ngFor let-item [ngForOf]="(sectionFillablePdfFormFields[section.section_id] || [])" let-j="index">
                                            <div class="drag-box" [attr.section_id]="item.section_id" *ngIf="item.section_id && (item.section_id == section.section_id) &&  !item.linked_with" [id]="item.field_id">
                                                <table class="table table-bordered fieldsTable rounded-corners">
                                                    <tbody>
                                                    <tr>
                                                        <td class="text-center colOne align-middle">
                                                            <i class="fa fa-chevron-down cursor-pointer mt-1" *ngIf="item.collapse" (click)="toggleFieldInfo(j, false, section.section_id)"></i>
                                                            <i class="fa fa-chevron-up cursor-pointer mt-1" *ngIf="!item.collapse" (click)="toggleFieldInfo(j, false, section.section_id)"></i>
                                                        </td>
                                                        <td class="colTwo align-middle">Field Name</td>
                                                        <td>
                                                            <input type="text" class="form-control"
                                                                   [name]="'fieldName_'+item.field_id+'_sl_'+j"
                                                                   [ngModel]="item.field_name"
                                                                   placeholder="Enter Name" autocomplete="off"
                                                                   readonly/>
                                                        </td>
                                                    </tr>

                                                    <tr [ngClass]="{'d-none': item.collapse}">
                                                        <td class="text-center colOne align-middle">
                                                            <i class="fa fa-bars  mt-1"></i>
                                                        </td>
                                                        <td class="colTwo align-middle">Field Label<small class="required-asterisk">*</small></td>
                                                        <td>
                                                            <input type="text" class="form-control"
                                                                   required
                                                                   [name]="'fieldLabel_'+item.field_id+'_sl_'+j"
                                                                   [(ngModel)]="item.field_label"
                                                                   placeholder="Enter Label" autocomplete="off"/>
                                                        </td>
                                                    </tr>

                                                    <tr [ngClass]="{'d-none': item.collapse}">
                                                        <td class="colOne" [rowSpan]="(['data_field', 'selectbox', 'multi_select'].includes(item.field_type)) ? 5 : 4"></td>
                                                        <td class="colTwo align-middle" [rowSpan]="(['data_field', 'selectbox', 'multi_select'].includes(item.field_type)) ? 2 : 1">Field Type<small class="required-asterisk">*</small></td>
                                                        <td>
                                                            <ng-select [name]="item.field_id+'_sl_'+j" [(ngModel)]="item.field_type" class="fillableSelect rounded-0 no-border" required (change)="changeFieldType(j)">
                                                                <ng-option *ngFor="let t of fillablePdfFormFieldTypes" [value]="t.key">{{ t.label }}</ng-option>
                                                            </ng-select>
                                                        </td>
                                                    </tr>

                                                    <tr [ngClass]="{'d-none': item.collapse}" *ngIf="['data_field', 'selectbox', 'multi_select'].includes(item.field_type)">
                                                        <td colspan="2">
                                                            <ng-select  *ngIf="item.field_type == 'data_field'" [(ngModel)]="item.data_field"
                                                                        [name]="item.data_field+'_sl_'+j" required [clearable]="false" class="fillableSelect no-border">
                                                                <ng-option *ngFor="let t of predefinedDataFields" [value]="t.key">{{ t.label }}</ng-option>
                                                            </ng-select>

                                                            <label *ngIf="item.field_type == 'selectbox' || item.field_type == 'multi_select'"
                                                                   style="font-size: 14px;padding: 0.375rem 0.75rem;line-height: 1.9;" class="cursor-pointer pt-1 m-0"
                                                                   (click)="manageCustomSelectOption(j,section.section_id)">
                                                                <i class="fas fa-edit text-primary mr-1"></i>Edit dropdown options
                                                            </label>
                                                        </td>
                                                    </tr>

                                                    <tr [ngClass]="{'d-none': item.collapse || item.field_type === 'data_field'}">
                                                        <td colspan="2">
                                                            <div class="form-control">
                                                                <label class="float-left cursor-pointer" [for]="'is_mandatory_'+item.field_id+'_sl_'+j">Mandatory Field</label>
                                                                <label class="checkbox-switch-v2 mb-0 float-right">
                                                                    <input type="checkbox"
                                                                           [name]="'is_mandatory_'+item.field_id+'_sl_'+j"
                                                                           [id]="'is_mandatory_'+item.field_id+'_sl_'+j"
                                                                           [(ngModel)]="item.is_mandatory">
                                                                    <span class="slider-v2 round"></span>
                                                                </label>
                                                            </div>
                                                        </td>
                                                    </tr>

                                                    <tr [ngClass]="{'d-none': item.collapse}">
                                                        <td colspan="2">
                                                            <div class="form-control">
                                                                <label class="float-left cursor-pointer" [for]="'hide_info_'+item.field_id+'_sl_'+j">Hide field from permit</label>
                                                                <label class="checkbox-switch-v2 mb-0 float-right">
                                                                    <input type="checkbox"
                                                                           [name]="'hide_info_'+item.field_id+'_sl_'+j"
                                                                           [id]="'hide_info_'+item.field_id+'_sl_'+j"
                                                                           [(ngModel)]="item.hide_info">
                                                                    <span class="slider-v2 round"></span>
                                                                </label>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </ng-template>
                                    </div>
                                </div>
                            </ng-container>
                        </div>
                    </div>
                </div>
                <hr class="my-4" *ngIf="field_sections.length > 1 && !hasAssignedSectionToFields && this.contentToVisible == 'unassigned'">
                <div [ngClass]="{'px-5 pb-2 col-12': true, 'd-none': contentToVisible === 'signatures'}" *ngIf="fillablePdfFormFields.length && permitTemplate.fillable_pdf_ref">
                    <div class="form-group">
                        <div class="d-block">
                            <span class="font-weight-bold" *ngIf="field_sections.length > 1 && !hasAssignedSectionToFields">Unassigned</span>
                            <div class="p-0 mb-2 fillablepdffields-list" dragula="fillablepdffieldsdefault" [(dragulaModel)]="fillablePdfFormFields">
                                <ng-template ngFor let-item [ngForOf]="(fillablePdfFormFields || [])" let-i="index">
                                    <div [ngClass]="{'drag-box': true, 'd-none': signatureTypes.includes(item.field_type) || item.linked_with}" *ngIf="!item.section_id">
                                        <table class="table table-bordered fieldsTable rounded-corners">
                                            <tbody>
                                            <tr>
                                                <td class="text-center colOne align-middle">
                                                    <i class="fa fa-chevron-down cursor-pointer mt-1" *ngIf="item.collapse" (click)="toggleFieldInfo(i, false)"></i>
                                                    <i class="fa fa-chevron-up cursor-pointer mt-1" *ngIf="!item.collapse" (click)="toggleFieldInfo(i, false)"></i>
                                                </td>
                                                <td class="colTwo align-middle">Field Name</td>
                                                <td>
                                                    <input type="text" class="form-control"
                                                           [name]="'fieldName_'+item.field_id+'_fl_'+i"
                                                           [ngModel]="item.field_name"
                                                           placeholder="Enter Name" autocomplete="off"
                                                           readonly/>
                                                </td>
                                            </tr>

                                            <tr [ngClass]="{'d-none': item.collapse}">
                                                <td class="text-center colOne align-middle">
                                                    <i class="fa fa-bars"></i>
                                                </td>
                                                <td class="colTwo align-middle">Field Label<small class="required-asterisk">*</small></td>
                                                <td>
                                                    <input type="text" class="form-control"
                                                           required
                                                           [name]="'fieldLabel_'+item.field_id+'_fl_'+i"
                                                           [(ngModel)]="item.field_label"
                                                           placeholder="Enter Label" autocomplete="off"/>
                                                </td>
                                            </tr>

                                            <tr [ngClass]="{'d-none': item.collapse}">
                                                <td class="colOne" [rowSpan]="(['data_field', 'selectbox', 'multi_select'].includes(item.field_type)) ? 5 : 4"></td>
                                                <td class="colTwo align-middle" [rowSpan]="(['data_field', 'selectbox', 'multi_select'].includes(item.field_type)) ? 2 : 1">Field Type<small class="required-asterisk">*</small></td>
                                                <td>
                                                    <ng-select [name]="item.field_id+'_fl_'+i" [(ngModel)]="item.field_type" class="fillableSelect no-border" required (change)="changeFieldType(i)">
                                                        <ng-option *ngFor="let t of fillablePdfFormFieldTypes" [value]="t.key">{{ t.label }}</ng-option>
                                                    </ng-select>
                                                </td>
                                            </tr>

                                            <tr [ngClass]="{'d-none': item.collapse}" *ngIf="['data_field', 'selectbox', 'multi_select'].includes(item.field_type)">
                                                <td colspan="2">
                                                    <ng-select  *ngIf="item.field_type == 'data_field'" [(ngModel)]="item.data_field"
                                                                [name]="item.data_field+'_fl_'+i" required [clearable]="false" class="fillableSelect no-border">
                                                        <ng-option *ngFor="let t of predefinedDataFields" [value]="t.key">{{ t.label }}</ng-option>
                                                    </ng-select>

                                                    <label *ngIf="item.field_type == 'selectbox' || item.field_type == 'multi_select'"
                                                           style="font-size: 14px; padding: 0.375rem 0.75rem;" class="cursor-pointer pt-1 m-0"
                                                           (click)="manageCustomSelectOption(i)">
                                                        <i class="fas fa-edit text-primary mr-1"></i>Edit dropdown options
                                                    </label>
                                                </td>
                                            </tr>

                                            <tr [ngClass]="{'d-none': item.collapse || item.field_type === 'data_field'}">
                                                <td colspan="2">
                                                    <div class="form-control">
                                                        <label class="float-left cursor-pointer" [for]="'is_mandatory_'+item.field_id+'_fl_'+i">Mandatory Field</label>
                                                        <label class="checkbox-switch-v2 mb-0 float-right">
                                                            <input type="checkbox"
                                                                   [name]="'is_mandatory_'+item.field_id+'_fl_'+i"
                                                                   [id]="'is_mandatory_'+item.field_id+'_fl_'+i"
                                                                   [(ngModel)]="item.is_mandatory">
                                                            <span class="slider-v2 round"></span>
                                                        </label>
                                                    </div>
                                                </td>
                                            </tr>

                                            <tr [ngClass]="{'d-none': item.collapse}">
                                                <td colspan="2">
                                                    <div class="form-control">
                                                        <label class="float-left cursor-pointer" [for]="'hide_info_'+item.field_id+'_fl_'+i">Hide field from permit</label>
                                                        <label class="checkbox-switch-v2 mb-0 float-right">
                                                            <input type="checkbox"
                                                                   [name]="'hide_info_'+item.field_id+'_fl_'+i"
                                                                   [id]="'hide_info_'+item.field_id+'_fl_'+i"
                                                                   [(ngModel)]="item.hide_info">
                                                            <span class="slider-v2 round"></span>
                                                        </label>
                                                    </div>
                                                </td>
                                            </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </ng-template>
                            </div>
                        </div>
                    </div>
                </div>

                <hr class="my-4" *ngIf="this.selectedLinkFields.length && this.contentToVisible == 'unassigned' && permitTemplate.fillable_pdf_ref">
                <div [ngClass]="{'px-5 pb-2 col-12': true, 'd-none': contentToVisible === 'signatures'}" *ngIf="this.selectedLinkFields.length && permitTemplate.fillable_pdf_ref">
                    <div class="form-group">
                        <div class="d-block">
                            <span class="font-weight-bold">Linked Fields</span>
                            <div class="p-0 mb-2 fillablepdffields-list">
                                <ng-template ngFor let-item [ngForOf]="(fillablePdfFormFields || [])" let-k="index">
                                    <div [ngClass]="{'drag-box': true}" *ngIf="item.linked_with">
                                        <table class="table table-bordered fieldsTable rounded-corners">
                                            <tbody>
                                            <tr>
                                                <td class="text-center colOne align-middle">
                                                    <i class="fa fa-chevron-down cursor-pointer mt-1" *ngIf="item.collapse" (click)="toggleFieldInfo(k, false)"></i>
                                                    <i class="fa fa-chevron-up cursor-pointer mt-1" *ngIf="!item.collapse" (click)="toggleFieldInfo(k, false)"></i>
                                                </td>
                                                <td class="colTwo align-middle">Field Name</td>
                                                <td>
                                                    <input type="text" class="form-control"
                                                           [name]="'fieldName_'+item.field_id+'_lf_'+k"
                                                           [id]="'fieldName_'+item.field_id+'_lf_'+k"
                                                           [ngModel]="item.field_name"
                                                           placeholder="Enter Name" autocomplete="off"
                                                           readonly/>
                                                </td>
                                            </tr>

                                            <tr [ngClass]="{'d-none': item.collapse}">
                                                <td class="text-center colOne align-middle">
                                                    <i class="fa fa-bars"></i>
                                                </td>
                                                <td class="colTwo align-middle">Field Label<small class="required-asterisk">*</small></td>
                                                <td>
                                                    <input type="text" class="form-control"
                                                           required
                                                           [name]="'fieldLabel_'+item.field_id+'_lf_'+k"
                                                           [id]="'fieldLabel_'+item.field_id+'_lf_'+k"
                                                           [(ngModel)]="item.field_label"
                                                           placeholder="Enter Label" autocomplete="off"/>
                                                </td>
                                            </tr>

                                            <tr [ngClass]="{'d-none': item.collapse}">
                                                <td class="colOne" [rowSpan]="(['data_field', 'selectbox', 'multi_select'].includes(item.field_type)) ? 4 : 3"></td>
                                                <td class="colTwo align-middle" [rowSpan]="(['data_field', 'selectbox', 'multi_select'].includes(item.field_type)) ? 2 : 1">Field Type</td>
                                                <td>
                                                    <ng-select [name]="item.field_id+'_lf_'+k" [id]="item.field_id+'_lf_'+k" [(ngModel)]="item.field_type" class="dummySelector fillableSelect no-border" required disabled  (change)="changeFieldType(k)">
                                                        <ng-option *ngFor="let t of fillablePdfFormFieldTypes" [value]="t.key">{{ t.label }}</ng-option>
                                                    </ng-select>
                                                </td>
                                            </tr>


                                            <tr [ngClass]="{'d-none': item.collapse}" *ngIf="['data_field', 'selectbox', 'multi_select'].includes(item.field_type)">
                                                <td colspan="2">
                                                    <ng-select  *ngIf="item.field_type == 'data_field'" [(ngModel)]="item.data_field"
                                                                [name]="item.data_field+'_lf_'+k" [id]="item.data_field+'_lf_'+k" required [clearable]="false" class="fillableSelect no-border">
                                                        <ng-option *ngFor="let t of predefinedDataFields" [value]="t.key">{{ t.label }}</ng-option>
                                                    </ng-select>

                                                    <label *ngIf="item.field_type == 'selectbox' || item.field_type == 'multi_select'"
                                                           style="font-size: 14px; padding: 0.375rem 0.75rem;" class="cursor-pointer pt-1 m-0"
                                                           (click)="manageCustomSelectOption(k)">
                                                        <i class="fas fa-edit text-primary mr-1"></i>Edit dropdown options
                                                    </label>
                                                </td>
                                            </tr>

                                            <tr [ngClass]="{'d-none': item.collapse}">
                                                <td class="colTwo align-middle">Linked Field</td>
                                                <td>
                                                    <ng-select *ngIf="item.field_type == 'data_field'" [ngModel]="item.linked_with" [name]="item.linked_with+'_lf_'+k" [id]="item.linked_with+'_lf_'+k" [clearable]="false" class="dummySelector fillableSelect no-border" disabled>
                                                        <ng-option *ngFor="let field of signatures" [value]="field.field_name">
                                                            <span>{{ field.field_label }}</span>
                                                        </ng-option>
                                                    </ng-select>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </ng-template>
                            </div>
                        </div>
                    </div>
                </div>

                <div [ngClass]="{'px-5 pb-2 col-12': true, 'd-none': contentToVisible === 'unassigned'}" *ngIf="permitTemplate.fillable_pdf_ref">
                    <div *ngIf="permitTemplate.fillable_pdf_ref && fillablePdfFormFields.length && signatures.length" class="form-group mb-0">
                        <div class="form-group" *ngIf="this.selectedLinkSections.length != this.field_sections.length-1 && this.field_sections.length > 1">
                            <label class="d-flex">Unlinked Sections<span class="material-symbols-outlined medium-font my-1 mx-1 cursor-pointer" [ngbTooltip]="'Any unlinked sections will be automatically assigned to be filled in by the permit requestor'">info</span></label>
                            <ng-select class="unlinkedSectionsDD" placeholder="Sections" name="field_sections" [searchable]="false" [clearable]="false">
                                <ng-container *ngFor="let section of field_sections">
                                    <ng-option  *ngIf="section.title && !selectedLinkSections.includes(section.section_id)" disabled>{{ section.title }}</ng-option>
                                </ng-container>
                            </ng-select>
                        </div>

                        <label><strong>Sign-off Configuration</strong></label>
                        <div class="d-block">
                            <div class="col-12 p-0 mb-2 signaturefields-list" dragula="signaturefields" [(dragulaModel)]="signatures">
                                <ng-container *ngFor="let item of (signatures || []); let i = index;">
                                    <div class="drag-box" *ngIf="!item.is_closeout" [ngClass]="{'drag-box': true, 'donot-drag': !!(permitTemplate.id)}">
                                        <table class="table table-bordered fieldsTable rounded-corners">
                                            <tbody>
                                            <tr>
                                                <td class="text-center colOne col-one-sign-off-config align-middle">
                                                    <i class="fa fa-chevron-down cursor-pointer mt-1" *ngIf="item.collapse" (click)="toggleFieldInfo(i, true)"></i>
                                                    <i class="fa fa-chevron-up cursor-pointer mt-1" *ngIf="!item.collapse" (click)="toggleFieldInfo(i, true)"></i>
                                                </td>
                                                <td class="colTwo col-two-sign-off-config align-middle">Order</td>
                                                <td>
                                                    <span class="form-control d-inline-block text-muted">{{item.sign_number}}</span>
                                                </td>
                                            </tr>

                                            <tr>
                                                <td class="text-center colOne col-one-sign-off-config align-middle" [rowSpan]="(permitTemplate.id) ? 7 : -1">
                                                    <i class="fa fa-bars mt-1" *ngIf="!permitTemplate.id"></i>
                                                </td>
                                                <td class="colTwo col-two-sign-off-config align-middle">Field Name</td>
                                                <td>
                                                    <span class="form-control d-inline-block">{{item.field_name}}</span>
                                                </td>
                                            </tr>

                                            <tr [ngClass]="{'d-none': item.collapse}">
                                                <td class="text-center colOne col-one-sign-off-config align-middle" *ngIf="!permitTemplate.id" [rowSpan]="(!permitTemplate.id) ? 6 : -1"></td>
                                                <td class="colTwo col-two-sign-off-config align-middle">Field Label</td>
                                                <td>
                                                    <input type="text" class="form-control"
                                                           required
                                                           [id]="'field_label'+i+getUniqueId(item.field_name)"
                                                           [name]="'field_label'+i+getUniqueId(item.field_name)"
                                                           [(ngModel)]="item.field_label"
                                                           placeholder="Enter Label" autocomplete="off"/>
                                                </td>
                                            </tr>

                                            <tr [ngClass]="{'d-none': item.collapse}">
                                                <td class="colTwo col-two-sign-off-config align-middle">Sign-off Declaration<span class="material-symbols-outlined medium-font my-1 mx-1 cursor-pointer align-middle" [ngbTooltip]="'The text entered will be displayed to the user before they confirm and sign off this step'">info</span></td>
                                                <td>
                                                    <textarea class="form-control" [id]="'declaration'+i+getUniqueId(item.field_name)" [name]="'declaration'+i+getUniqueId(item.field_name)" [(ngModel)]="item.declaration"></textarea>
                                                </td>
                                            </tr>

                                            <tr [ngClass]="{'d-none': item.collapse}">
                                                <td class="colTwo col-two-sign-off-config align-middle">Link Fields<span class="material-symbols-outlined medium-font my-1 mx-1 cursor-pointer align-middle" ngbTooltip="Any linked fields will be autofilled when the respective sign-off step is completed (e.g. a user's name or date/time of sign-off)">info</span></td>
                                                <td>
                                                    <ng-select [id]="'link_fields_'+i+getUniqueId(item.field_name)" [name]="'link_fields_'+i+getUniqueId(item.field_name)" #linkField="ngModel" [(ngModel)]="item.link_fields"
                                                               style="width: 100%;" [multiple]="true" class="fillableSelect no-border" (change)="linkFieldsSelected(false)">
                                                        <ng-container *ngFor="let field of fillablePdfFormFields">
                                                            <ng-option  *ngIf="!signatureTypes.includes(field.field_type)" [value]="field.field_name" [disabled]="selectedLinkFields.includes(field.field_name) && !(item.link_fields).includes(field.field_name)">
                                                                <span>{{ field.field_label }}</span>
                                                            </ng-option>
                                                        </ng-container>
                                                    </ng-select>
                                                </td>
                                            </tr>

                                            <tr *ngIf="field_sections.length > 1 && !item.is_closeout" [ngClass]="{'d-none': item.collapse}">
                                                <td class="colTwo col-two-sign-off-config align-middle">Link Sections<small class="required-asterisk" *ngIf="item.is_requestor && field_sections.length > 1">*</small><span class="material-symbols-outlined medium-font my-1 mx-1 cursor-pointer" [ngbTooltip]="'When a section is linked into a sign-off step, the user responsible for the respective sign-off will be asked to fill in the associated fields'">info</span></td>
                                                <td>
                                                    <ng-select [id]="'link_sections_'+i+getUniqueId(item.field_name)" [name]="'link_sections_'+i+getUniqueId(item.field_name)" #linkSections="ngModel" [(ngModel)]="item.link_sections" [required]="item.is_requestor && field_sections.length > 1"
                                                               style="width: 100%;" [multiple]="true" class="fillableSelect no-border" (change)="linkSectionsSelected(i, 'signOff')">
                                                        <ng-container *ngFor="let section of field_sections">
                                                            <ng-option *ngIf="section.title" [value]="section.section_id" [disabled]="selectedLinkSections.includes(section.section_id) && !(item.link_sections && (item.link_sections).includes(section.section_id))">
                                                                <span>{{ section.title }}</span>
                                                            </ng-option>
                                                        </ng-container>
                                                    </ng-select>
                                                </td>
                                            </tr>

                                            <tr *ngIf="i === 0" [ngClass]="{'d-none': item.collapse}">
                                                <td colspan="2">
                                                    <div class="form-control">
                                                        <label class="float-left cursor-pointer" [for]="'requestor_'+i">Requestor</label>
                                                        <label class="checkbox-switch-v2 mb-0 float-right">
                                                            <input type="checkbox"
                                                                   [name]="'requestor_'+i+getUniqueId(item.field_name)"
                                                                   [id]="'requestor_'+i+getUniqueId(item.field_name)"
                                                                   (click)="onOptionChange($event, i, 'is_requestor')"
                                                                   [checked]="item.is_requestor"
                                                                   [disabled]="permitTemplate.id"
                                                            />
                                                            <span class="slider-v2 round"></span>
                                                        </label>
                                                    </div>
                                                </td>
                                            </tr>

                                            <tr *ngIf="permitTemplate.require_closeout && (signatures.length + closeoutSignatures.length) > 2 && i > 0 && (closeoutRequestorIndex === undefined)"  [ngClass]="{'d-none': item.collapse}">
                                                <td colspan="2">
                                                    <div class="form-control">
                                                        <label class="float-left cursor-pointer" [for]="'closeout_requestor_'+i">Closeout Requestor</label>
                                                        <label class="checkbox-switch-v2 mb-0 float-right">
                                                            <input type="checkbox"
                                                                   [name]="'closeout_requestor_'+i+getUniqueId(item.field_name)"
                                                                   [id]="'closeout_requestor_'+i+getUniqueId(item.field_name)"
                                                                   (click)="onOptionChange($event, i, 'is_closeout_requestor')"
                                                                   [checked]="item.is_closeout_requestor"
                                                                   [disabled]="permitTemplate.id"
                                                            />
                                                            <span class="slider-v2 round"></span>
                                                        </label>
                                                    </div>
                                                </td>
                                            </tr>

                                            <tr *ngIf="permitTemplate.require_closeout && (signatures.length + closeoutSignatures.length) > 2 && i > 0"  [ngClass]="{'d-none': item.collapse}">
                                                <td colspan="2">
                                                    <div class="form-control">
                                                        <label class="float-left cursor-pointer" [for]="'closeout_person_'+i">Is this part of the closeout sign-off process?</label>
                                                        <label class="checkbox-switch-v2 mb-0 float-right">
                                                            <input type="checkbox"
                                                                   [name]="'closeout_person_'+i+getUniqueId(item.field_name)"
                                                                   [id]="'closeout_person_'+i+getUniqueId(item.field_name)"
                                                                   (click)="onOptionChange($event, i, 'is_closeout')"
                                                                   [checked]="item.is_closeout"
                                                                   [disabled]="permitTemplate.id"
                                                            />
                                                            <span class="slider-v2 round"></span>
                                                        </label>
                                                    </div>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                        <div class="d-block" *ngIf="permitTemplate.require_closeout && closeoutSignatures.length">
                            <hr class="my-4">
                            <label class="font-weight-bold">Closeout Configuration</label>
                            <div class="col-12 p-0 mb-2 signaturefields-list" dragula="closeoutSignatures" [(dragulaModel)]="closeoutSignatures">
                                <ng-container *ngFor="let item of (closeoutSignatures || []); let j = index;">
                                    <div class="drag-box" *ngIf="item.is_closeout || item.is_closeout_requestor" [ngClass]="{'drag-box': true, 'donot-drag': !!(permitTemplate.id)}">
                                        <table class="table table-bordered fieldsTable rounded-corners">
                                            <tbody>
                                            <tr>
                                                <td class="text-center colOne col-one-sign-off-config align-middle">
                                                    <i class="fa fa-chevron-down cursor-pointer mt-1" *ngIf="item.collapse" (click)="toggleFieldInfo(j, true)"></i>
                                                    <i class="fa fa-chevron-up cursor-pointer mt-1" *ngIf="!item.collapse" (click)="toggleFieldInfo(j, true)"></i>
                                                </td>
                                                <td class="colTwo col-two-sign-off-config align-middle">Order</td>
                                                <td>
                                                    <span class="form-control d-inline-block text-muted">{{item.sign_number}}</span>
                                                </td>
                                            </tr>

                                            <tr>
                                                <td class="text-center colOne col-one-sign-off-config align-middle" [rowSpan]="(permitTemplate.id) ? 7 : -1">
                                                    <i class="fa fa-bars mt-1" *ngIf="!permitTemplate.id"></i>
                                                </td>
                                                <td class="colTwo col-two-sign-off-config align-middle">Field Name</td>
                                                <td>
                                                    <span class="form-control d-inline-block">{{item.field_name}}</span>
                                                </td>
                                            </tr>

                                            <tr [ngClass]="{'d-none': item.collapse}">
                                                <td class="text-center colOne col-one-sign-off-config align-middle" *ngIf="!permitTemplate.id" [rowSpan]="(!permitTemplate.id) ? 6 : -1"></td>
                                                <td class="colTwo col-two-sign-off-config align-middle">Field Label</td>
                                                <td>
                                                    <input type="text" class="form-control"
                                                           required
                                                           [id]="'field_label_cl_'+j+getUniqueId(item.field_name)"
                                                           [name]="'field_label_cl_'+j+getUniqueId(item.field_name)"
                                                           [(ngModel)]="item.field_label"
                                                           placeholder="Enter Label" autocomplete="off"/>
                                                </td>
                                            </tr>

                                            <tr [ngClass]="{'d-none': item.collapse}">
                                                <td class="colTwo col-two-sign-off-config align-middle">Sign-off Declaration<span class="material-symbols-outlined medium-font my-1 mx-1 cursor-pointer align-middle" [ngbTooltip]="'The text entered will be displayed to the user before they confirm and sign off this step'">info</span></td>
                                                <td>
                                                    <textarea class="form-control" [id]="'declaration_cl_'+j+getUniqueId(item.field_name)" [name]="'declaration_cl_'+j+getUniqueId(item.field_name)" [(ngModel)]="item.declaration"></textarea>
                                                </td>
                                            </tr>

                                            <tr [ngClass]="{'d-none': item.collapse}">
                                                <td class="colTwo col-two-sign-off-config align-middle">Link Fields<span class="material-symbols-outlined medium-font my-1 mx-1 cursor-pointer align-middle" ngbTooltip="Any linked fields will be autofilled when the respective sign-off step is completed (e.g. a user's name or date/time of sign-off)">info</span></td>
                                                <td>
                                                    <ng-select [id]="'link_fields_cl_'+j+getUniqueId(item.field_name)" [name]="'link_fields_cl_'+j+getUniqueId(item.field_name)" #linkField="ngModel" [(ngModel)]="item.link_fields"
                                                               style="width: 100%;" [multiple]="true" class="fillableSelect no-border" (change)="linkFieldsSelected(false)">
                                                        <ng-container *ngFor="let field of fillablePdfFormFields">
                                                            <ng-option  *ngIf="!signatureTypes.includes(field.field_type)" [value]="field.field_name" [disabled]="selectedLinkFields.includes(field.field_name) && !(item.link_fields).includes(field.field_name)">
                                                                <span>{{ field.field_label }}</span>
                                                            </ng-option>
                                                        </ng-container>
                                                    </ng-select>
                                                </td>
                                            </tr>

                                            <tr *ngIf="field_sections.length > 1" [ngClass]="{'d-none': item.collapse}">
                                                <td class="colTwo col-two-sign-off-config align-middle">Link Sections<small class="required-asterisk" *ngIf="item.is_requestor && field_sections.length > 1">*</small><span class="material-symbols-outlined medium-font my-1 mx-1 cursor-pointer" [ngbTooltip]="'When a section is linked into a sign-off step, the user responsible for the respective sign-off will be asked to fill in the associated fields'">info</span></td>
                                                <td>
                                                    <ng-select [id]="'link_sections_cl_'+j+getUniqueId(item.field_name)" [name]="'link_sections_cl_'+j+getUniqueId(item.field_name)" #linkSections="ngModel" [(ngModel)]="item.link_sections" [required]="item.is_requestor && field_sections.length > 1"
                                                               style="width: 100%;" [multiple]="true" class="fillableSelect no-border" (change)="linkSectionsSelected(j, 'closeoutSignOff')">
                                                        <ng-container *ngFor="let section of field_sections">
                                                            <ng-option *ngIf="section.title" [value]="section.section_id" [disabled]="selectedLinkSections.includes(section.section_id) && !(item.link_sections && (item.link_sections).includes(section.section_id))">
                                                                <span>{{ section.title }}</span>
                                                            </ng-option>
                                                        </ng-container>
                                                    </ng-select>
                                                </td>
                                            </tr>

                                            <tr *ngIf="item.is_closeout_requestor"  [ngClass]="{'d-none': item.collapse}">
                                                <td colspan="2">
                                                    <div class="form-control">
                                                        <label class="float-left cursor-pointer" [for]="'closeout_requestor_cl_'+j">Closeout Requestor</label>
                                                        <label class="checkbox-switch-v2 mb-0 float-right">
                                                            <input type="checkbox"
                                                                   [name]="'closeout_requestor_cl_'+j+getUniqueId(item.field_name)"
                                                                   [id]="'closeout_requestor_cl_'+j+getUniqueId(item.field_name)"
                                                                   (click)="onCloseoutOptionChange($event, j, 'is_closeout_requestor')"
                                                                   [checked]="item.is_closeout_requestor"
                                                                   [disabled]="permitTemplate.id"
                                                            />
                                                            <span class="slider-v2 round"></span>
                                                        </label>
                                                    </div>
                                                </td>
                                            </tr>

                                            <tr *ngIf="item.is_closeout"  [ngClass]="{'d-none': item.collapse}">
                                                <td colspan="2">
                                                    <div class="form-control">
                                                        <label class="float-left cursor-pointer" [for]="'closeout_person_cl_'+j">Closeout Sign-off</label>
                                                        <label class="checkbox-switch-v2 mb-0 float-right">
                                                            <input type="checkbox"
                                                                   [name]="'closeout_person_cl_'+j+getUniqueId(item.field_name)"
                                                                   [id]="'closeout_person_cl_'+j+getUniqueId(item.field_name)"
                                                                   (click)="onCloseoutOptionChange($event, j, 'is_closeout')"
                                                                   [checked]="item.is_closeout"
                                                                   [disabled]="permitTemplate.id"
                                                            />
                                                            <span class="slider-v2 round"></span>
                                                        </label>
                                                    </div>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <input type="hidden" name="validateRequireCloseout" required [ngModel]="(permitTemplate.require_closeout && !hasCloseoutSignatures(closeoutSignatures)) ? '' : 'yes'">
        <input type="hidden" name="validateFillableFields" required [ngModel]="!fillablePdfFormFields.length ? '' : 'yes'">
        <input type="hidden" name="validateIsValidFor" required [ngModel]="!this.isValidForSelected ? '' : 'yes'">
    </form>
</i-modal>


<ng-template #customSelectOptionsModal let-c="close" let-d="dismiss">
    <div class="modal-header">
        <span class="modal-title">Dropdown Options</span>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span type="button" class="close-icon small">
                <span aria-hidden="true" style="padding: 1px 7px;">×</span>
            </span>
        </button>
    </div>
    <div class="modal-body" style="height: 300px; overflow: auto;">
        <form novalidate #optionForm="ngForm">
            <div>
                <span>Add & Remove Options</span>
                <div class="form-group">
                    <div *ngFor="let option of fillablePdfSelectField.options trackBy : trackByRowIndex; let i = index;" class="row p-0 mx-0">
                        <div class="col-11 px-0 py-2 d-flex align-items-center">
                            <div class="col-md-12 p-0 d-inline-block text-truncate">
                                <input type="text" class="form-control" [name]="'optionLabel' + i"
                                       [(ngModel)]="option.label"
                                       placeholder="Enter Option Label" autocomplete="off" />
                            </div>
                        </div>
                        <button [disabled]="!option.label.length" class="btn col-md-1 p-2 float-right d-flex align-items-center justify-content-center" *ngIf="i == 0" (click)="addCustomSelectOption(i)">
                            <i class="fa fa-plus-circle cursor-pointer" style="color: #235fc7;"></i>
                        </button>
                        <div class="col-md-1 p-2 float-right d-flex align-items-center justify-content-center" *ngIf="i > 0">
                               <span class="material-symbols-outlined text-danger cursor-pointer"
                               (click)="removeCustomSelectOption(i)">
                                delete
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer py-1">
        <button class="btn btn-brandeis-blue" [disabled]="!optionForm.valid" (click)="c('Close Click')">Save</button>
    </div>
</ng-template>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
<block-loader [show]="(requestProcessing)"  [showBackdrop]="true" alwaysInCenter="true"></block-loader>

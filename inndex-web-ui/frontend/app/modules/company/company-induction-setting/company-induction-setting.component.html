<style>
    .form-group.disabled {
        pointer-events: none;
        opacity: .3;
    }
</style>
<div class="row">
    <div class="col-12 mb-3">
        <small class="form-text text-muted mb-3"> When Company Induction setting is activated,
            users will be unable to carry out a site specific induction without firstly carrying out the company induction
        </small>
        <form novalidate #companyInductionForm="ngForm">
            <div class="form-group">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" name="company_induction_status"
                           id="company_induction_status"
                           [(ngModel)]="setting.status"
                           [checked]="setting.status"
                           class="custom-control-input">
                    <label for="company_induction_status" class="custom-control-label pr-2">Activate</label>
                </div>
            </div>
            <div class="form-group" [ngClass]="{'disabled': !setting.status}">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" name="has_ci_alternate_phrase"
                           [(ngModel)]="setting.has_phrase"
                           [checked]="setting.has_phrase" [name]="'has_company_induction_phrase'"
                           id="has_ci_alternate_phrase" (click)="togglePhrasing($event)"
                           class="custom-control-input">
                    <label for="has_ci_alternate_phrase" class="custom-control-label pr-2">Would you like to use alternative phrasing for the company
                        induction tool?</label>
                </div>
            </div>
            <div class="form-group row mb-2" [ngClass]="{'disabled': !setting.status || !setting.has_phrase}">
                <label class="col-md-4 col-form-label form-control-label">Singular form of tool name:</label>
                <div class="col-md-7 form-inline pl-0">
                    <input type="text" [name]="'company_induction_phrase_singlr'" #altSglrPhrase="ngModel" [maxlength]="maxlength"
                           [(ngModel)]="setting.phrasing.singlr" [readonly]="!setting.has_phrase"
                           [required]="setting.has_phrase" class="form-control col-sm-10"/>
                    <span *ngIf="setting.has_phrase" class="small text-danger mx-1">
                    Characters remaining: {{(maxlength - (setting.phrasing.singlr || '').length)}}/{{maxlength}}
                </span>
                    <div class="alert alert-danger col-10 mb-0" [hidden]="(!setting.has_phrase || altSglrPhrase.valid)">Alternative singular phrase is
                        required
                    </div>
                </div>
            </div>
            <div class="form-group row mb-0" [ngClass]="{'disabled': !setting.status || !setting.has_phrase}">
                <label class="col-md-4 col-form-label form-control-label">Plural form of tool name:</label>
                <div class="col-md-7 form-inline pl-0">
                    <input type="text" [name]="'company_induction_phrase'" #altPhrase="ngModel" [maxlength]="maxlength"
                           [(ngModel)]="setting.phrasing.normal" [readonly]="!setting.has_phrase"
                           [required]="setting.has_phrase" class="form-control col-sm-10"/>
                    <span *ngIf="setting.has_phrase" class="small text-danger mx-1">
                    Characters remaining: {{(maxlength - (setting.phrasing.normal || '').length)}}/{{maxlength}}
                </span>
                    <div class="alert alert-danger col-10 mb-0" [hidden]="(!setting.has_phrase || altPhrase.valid)">Plural phrase is required</div>
                </div>
            </div>

            <div class="form-group mt-2" [ngClass]="{'disabled': !setting.status}">
                <div class="row mx-0 custom-drop">
                    <div class="col-4 flex-center px-0">
                        <label class="heading pr-2"> Valid for </label>
                        <i class="fa fa-info-circle small"
                           container="body"
                           [ngbTooltip]="'Use this to select the number of weeks until expiry. After expiry the user will need to resubmit the company induction to allow further site specific inductions to be carried out'"
                        ></i>
                    </div>
                    <div class="col-4 px-0">
                        <ng-select class="w-100 heading"
                                   [items]="weeks"
                                   name="weeks"
                                   [virtualScroll]="true"
                                   bindLabel="name"
                                   bindValue="value"
                                   placeholder="Weeks"
                                   [clearable]="false"
                                   [searchable]="false"
                                   [(ngModel)]="setting.valid_for_weeks"
                                   #weeksValue="ngModel"
                                   ng-value="setting.valid_for_weeks" [required]="setting.status">
                            <ng-template ng-label-tmp let-item="item">
                                <div class="heading flex-center">
                                    {{ item.name }}
                                </div>
                            </ng-template>
                        </ng-select>
                        <div class="alert alert-danger" [hidden]="(weeksValue.valid)">Weeks required.</div>
                    </div>

                </div>
            </div>

            <hr>
            <div class="form-group mt-2" [ngClass]="{'disabled': !setting.status}">
                <h5>Media</h5>
                <div class="custom-control custom-checkbox p-right">
                    <input type="checkbox" class="custom-control-input"
                           [(ngModel)]="setting.has_media_content"
                           [checked]="setting.has_media_content"
                           name="has_media_content" (click)="toggleMediaTab($event)"
                           id="has_media_content">
                    <label class="custom-control-label small pr-5" for="has_media_content">Would you like to include a video/presentations as part of {{setting.phrasing?.singlr}}? </label>
                </div>
                <ul class="list-group">
                    <ng-template ngFor let-item [ngForOf]="(setting.media_resources || [])" let-i="index">
                        <li class="list-group-item border-left-0 border-right-0 pl-0" style="background: transparent;" [class.border-top-0]="(i === 0)" [class.border-bottom-0]="(i === (setting.media_resources || []).length - 1)">
                            <div class="form-group row">
                                <label class="col-md-3">Target language <small class="required-asterisk">*</small></label>
                                <div class="col-md-3 pl-0">
                                    <ng-select required
                                               placeholder="Choose a language"
                                               [name]="'media_locale_' + i"
                                               [(ngModel)]="item.lang"
                                               #projectMediaLocale="ngModel">
                                            <ng-option *ngFor="let lang of locales" [value]="lang" [disabled]="isAlreadySelectedMLang(i, lang, item.type)">{{lang}}</ng-option>
                                    </ng-select>
                                    <div class="alert alert-danger" [hidden]="!(projectMediaLocale.errors && projectMediaLocale.errors.required)">Media language is required</div>
                                </div>
                                <div class="custom-control custom-switch col-md-3">
                                    <input type="checkbox" [checked]="item.type === 'url'"
                                           (click)="toggleMediaUrlMode($event, i)"
                                           class="custom-control-input" [id]="'switch' + i">
                                    <label class="custom-control-label" [for]="'switch' + i">Add Luma1 Media URL instead?</label>
                                </div>
                                <div class="custom-control custom-radio col-md-2">
                                    <input type="radio"  [name]="'d-radio'+ item.type"
                                           [checked]="item.is_default"
                                           (click)="toggleMediaDefaultState($event, item.type, i)"
                                           class="custom-control-input" [id]="'d-radio' + i">
                                    <label class="custom-control-label" [for]="'d-radio' + i">Default {{item.type}}</label>
                                </div>
                                <span class="material-symbols-outlined text-danger float-right cursor-pointer" (click)="removeMediaResource(i)" *ngIf="i || (setting.media_resources || []).length > 1">
                                    delete
                                </span>
                            </div>
                            <div class="form-group mb-0 row small" *ngIf="item.type === 'file'">
                                <label class="col-sm-3 mb-0">Upload Video/PDF:</label>
                                <div class="col-sm-9 pl-0">
                                    <file-uploader-v2
                                        class="pl-0 mt-n2 d-block"
                                        [disabled]="!setting.has_media_content"
                                        [init]="(item.file_ref && item.file_ref.id) ? item.file_ref : {id: item.file_ref}"
                                        [category]="'project-media'"
                                        [dragnDropTxt]="'Drag and drop video or pdf here'"
                                        [showHyperlink]="true"
                                        (uploadDone)="mediaResourceUploadDone($event, i)"
                                        [allowedMimeType]="['video/mp4', 'application/pdf', 'application/x-pdf']"
                                        (deleteFileDone)="mediaResourceRemoved($event, i)"
                                        [showDeleteBtn]="true"
                                        #videoUploader></file-uploader-v2>
                                    <input type="text" class="d-none"
                                           [name]="'file_ref' + i"
                                           required
                                           #mediaFileRef="ngModel"
                                           [ngModel]="(item.file_ref && item.file_ref.id) ? true : undefined" />
                                </div>
                            </div>
                            <div class="form-group mb-0 small" *ngIf="item.type === 'url'">
                                <input type="text" class="form-control" [(ngModel)]="item.content" [name]="'media_url'+i"
                                       placeholder="Enter Media url" required #htmlMediaUrl="ngModel"
                                       pattern="^(https?):\/\/[^\s$.?#]+\.[^\s\/]+\/.*" />
                                <div class="alert alert-danger" [hidden]="!(htmlMediaUrl.errors && htmlMediaUrl.errors.required)">Media URL is required</div>
                                <div class="alert alert-danger" [hidden]="!(htmlMediaUrl.errors && htmlMediaUrl.errors.pattern)">Media URL is not valid</div>
                            </div>
                        </li>
                    </ng-template>

                    <input type="text" class="d-none"
                           name="media_resource"
                           [required]="setting.has_media_content"
                           #mediaResources="ngModel"
                           [ngModel]="hasValidMediaResources() ? true : undefined" />
                    <input type="text" class="d-none"
                           name="has_media_default"
                           [required]="setting.has_media_content"
                           #mediaDefault="ngModel"
                           [ngModel]="hasDefaultPerMedia() ? true : undefined" />

                </ul>
                <div class="form-group" *ngIf="setting.has_media_content && (allMediaNotDefined('file') || allMediaNotDefined('url'))">
                    <label>Add further media</label>
                    <i class="fa fa-plus-circle text-primary float-right cursor-pointer" (click)="addMediaResource()"></i>
                </div>

            </div>

            <hr>
            <div class="form-group" [ngClass]="{'disabled': !setting.status}">
                <label>
                    <h5><span i18n="@@Uciq">{{setting.phrasing?.singlr}} Quiz</span></h5>
                </label>
                <div class="custom-control custom-checkbox p-right">
                    <input type="checkbox" class="custom-control-input"
                           [(ngModel)]="setting.has_quiz"
                           [checked]="setting.has_quiz"
                           name="quiz_status" (click)="toggleQStatus($event, 'quiz_sets', 'Quiz', 'quiz')"
                           id="quiz_status">
                    <label class="custom-control-label small pr-5" for="quiz_status"> Would you like to add a multiple choice quiz to the {{setting.phrasing?.singlr}}? </label>
                </div>
                <div *ngIf="setting.has_quiz">
                    <ul class="list-group">
                        <li class="list-group-item" *ngFor="let quiz of setting.quiz_sets; let j = index;">
                            <span class="material-symbols-outlined float-right text-danger cursor-pointer" (click)="removeElement(j, 'quiz')" *ngIf="j || setting.quiz_sets.length > 1">
                                delete
                            </span>
                            <induction-quiz-row
                                [mediaForm]="companyInductionForm"
                                [quiz]="quiz" [index]="j"
                                [alreadySelectedLang]="getAlreadySelectedQLang(j)"
                                (markedDefault)="toggleQDefault($event, 'quiz_sets')"
                            ></induction-quiz-row>
                        </li>
                        <li class="list-group-item text-center">
                            <button *ngIf="(locales.length > setting.quiz_sets.length)" class="btn" (click)="addQuizRow('quiz', setting.quiz_sets.length)"><i class="fa fa-plus-circle text-primary cursor-pointer"> Add new Questions Set</i></button>
                        </li>
                    </ul>
                </div>
            </div>

            <hr>
            <div class="form-group" [ngClass]="{'disabled': !setting.status}">
                <label>
                    <h5><span i18n="@@aiq">Additional {{setting.phrasing?.singlr}} Questions</span></h5>
                </label>
                <div class="custom-control custom-checkbox p-right mb-3">
                    <input type="checkbox" class="custom-control-input"
                           [(ngModel)]="setting.has_iq"
                           [checked]="setting.has_iq"
                           name="additional_iq_status" (click)="toggleQStatus($event, 'iq_sets', 'Additional Questions', 'question')"
                           id="additional_iq_status">
                    <label class="custom-control-label small pr-5" for="additional_iq_status"> This will add another
                        step to the {{setting.phrasing?.singlr}} to enable the collection of any additional information during the
                        {{setting.phrasing?.singlr}} process. </label>
                </div>

                <div *ngIf="setting.has_iq">
                    <ul class="list-group">
                        <li class="list-group-item" *ngFor="let aq of setting.iq_sets; let j = index;">
                            <span class="material-symbols-outlined float-right text-danger cursor-pointer" (click)="removeElement(j, 'question')" *ngIf="j || setting.iq_sets.length > 1">
                                delete
                            </span>
                            <induction-question-row
                                [mediaForm]="companyInductionForm" [aq]="aq" [index]="j"
                                [alreadySelectedLang]="getAlreadySelectedIQLang(j)"
                                (markedDefault)="toggleQDefault($event, 'iq_sets')"
                            ></induction-question-row>
                        </li>
                        <li class="list-group-item text-center">
                            <button *ngIf="(locales.length > setting.iq_sets.length)" class="btn" (click)="addQuizRow('question', setting.iq_sets.length)"><i class="fa fa-plus-circle text-primary cursor-pointer"> Add new Questions Set</i></button>
                        </li>
                    </ul>
                </div>
            </div>

            <pre class="small d-none">{{ setting | json }}</pre>
            <block-loader [show]="(requestInProgress)" alwaysInCenter="alwaysInCenter"></block-loader>
            <button type="button" class="btn btn-outline-primary" (click)="saveCompanyInductionSetting()"
                    [disabled]="companyInductionForm.invalid || (setting.status && (!setting.has_media_content && !setting.has_iq && !setting.has_quiz))">Save
            </button>
        </form>
    </div>
</div>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
import {Component, OnInit, TemplateRef, Input, Output, EventEmitter, ViewChild, ChangeDetectorRef} from '@angular/core';
import {ActivatedRoute, Router, Scroll, NavigationEnd, } from "@angular/router";
import {NgbDate, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {
    CreateEmployer,
    UserService,
    Project,
    ProjectService,
    ResourceService,
    AuthService,
    User,
    ToastService,
    SidebarUtilsService,
    OptimaSetting,
    OptimaService,
    isInheritedProjectOfCompany,
    ProjectInspectionTourService,
    CloseCallService,
    InspectionBuilderService,
    HttpService,
    ProjectFeaturePermission,
    ProjectLocation, FeatureExclusionUtility,
} from "@app/core";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import {CompanySiteConfigComponent} from "../company-site-configuration/company-site-config.component";
import {CowOwenerTaggingComponent} from "../cow-owener-tagging/cow-owener-tagging.component";
import * as dayjs from 'dayjs';
let advancedFormat = require('dayjs/plugin/advancedFormat');
dayjs.extend(advancedFormat);
import {DomSanitizer} from "@angular/platform-browser";
import {AlternativePhraseSettingComponent} from "@app/modules/common";
import {AppConstant} from "@env/environment";
import { AssetsUrl } from '@app/shared/assets-urls/assets-urls';
import { GenericConfirmationModalComponent, IModalComponent } from '@app/shared';

@Component({
    selector: 'company-side-nav',
    templateUrl: './company-side-nav.component.html',
    styleUrls: ['./company-side-nav.component.scss']
})
export class CompanySideNavComponent implements OnInit {

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    authUser$: any;

    @Input()
    companyResolverResponse: any;

    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    hasCompanyInspectionTours: boolean = false;
    hasIBuilder: boolean = false;
    hasCompanyCloseCalls: boolean = false;
    hasCompanyGoodCalls: boolean = false;
    hasCompanyObservations: boolean = false;
    hasCompanyIncidentReport: boolean = false;
    hasCompanyUsageReport: boolean = false;
    liveProFirstInspectionCreatedAt: number;
    disabledProFirstInspectionCreatedAt: number;
    companyInspectionBuilders: Array<any> = [];

    @Input()
    employer: any = {};

    @Output()
    reInitializeProjectsByEmployer = new EventEmitter<any>();

    @Output()
    projectRetrieved = new EventEmitter<any>();

    @Input()
    activatedDashboardIBs: any = [];

    @Input()
    featuresStatus: object = {};

    @Input()
    companyProjectSectionAccess: object = {};

    AppConstant = AppConstant;
    dayjs = dayjs;
    is_mobile_nav: boolean = false;
    is_drawer_open: boolean = false;
    is_inherited_project: boolean = false;
    employerId: number = 0;
    employerInfo: CreateEmployer = {};
    modalRequestInProgress: boolean = false;
    statusResponseMessage: string;
    addProjectRequestInProgress: boolean = false;
    companyUsers: Array<any> = [];
    project: Project = new Project;
    selectedCompanyProject: Project = new Project;
    public knownTypeOfWorks: Array<any>;
    public availableTimeZones: Array<any>;
    PROJECT_VALUE: Array<any> = [];
    PROJECT_TYPES: Array<any> = [];
    query:string="";
    value:string;
    type:string;
    projectId:number=null;
    hasTimeManagementAccess: boolean = false;
    hasProgressPhotosAccess: boolean = false;
    hasCollectionNotesAccess: boolean = false;
    hasDeliveryNotesAccess: boolean = false;
    hasDailyActivities: boolean = false;
    hasCloseCallAccess: boolean = false;
    hasToolboxTalksAccess: boolean = false;
    hasPermitAccess: boolean = false;
    hasClerkOfWorksAccess: boolean = false;
    hasPowraAccess: boolean = false;
    hasIncidentReportAccess: boolean = false;
    hasInspectionTour: boolean = false;
    disableFeaturesCount: number = 0;
    heightAdjustment: string = `100vh - 410px`;
    hasGoodCalls: boolean = false;
    hasObservations: boolean = false;
    hasSiteMessaging: boolean = false;
    hasTake5s: boolean = false;
    hasTaskBriefings: boolean = false;
    hasInspections: boolean = false;
    geofenceData: OptimaSetting = new OptimaSetting;
    hasWorkPackagePlan: boolean = false;
    hasRams: boolean = false;
    hasAssetManagement: boolean = false;
    hasQualityChecklist: boolean = false;
    dashboardLoader: boolean = false;
    dashboardHtmlContent: any;
    dashboardStartDate: NgbDate;
    @Input()
    dashboardReportFrom: any;
    dashboardReportTo: any;
    includeArchivedProject?: boolean = false;
    projectsToSelect: Array<any> = [];
    selectedDashboardProjects: Array<any> = [];
    projectSelectorModalRef: any;
    isDashboardMenuCollapsed: boolean = true;
    currentDashboardFeature: string = '';
    currentDashboardFeatureName: string = '';
    closeCallAlternetPhrase: string = 'Close Call';
    goodCallAlternetPhrase: string = 'Good Call';
    observationAlternetPhrase: string = 'Observation';
    dashboardTypes: Array<any> = [];
    selectedDashboardType: string = 'inspection-tour';
    responsibleCompanies: Array<any> = [];
    selectedResponsibleCompanies: Array<any> = [];
    loadingDashboard: boolean = false;
    inspectionBuilderInfo: any;
    selectedTaggedOwner: any = {};
    normalProjectsInspectionTourRating: any = {yes: true, no: true};
    industrialProjectsInspectionTourRating: any = {good: true, fair: true, poor: true};
    ib_scoring_system: any = {};
    rating_color: Array<any> = ['greenText', 'yellowText', 'redText'];
    isNormalProjectDashboard: boolean = true;
    isIndustrialProjectDashboard: boolean = true;
    sort_items_by: string = 'date';
    companyFeaturesStatus: object = {};
    company_induction_phrasing_singlr: string = "Company Induction";
    tool_access: any = {
        dashboard: false,
        time_management: false,
        progress_photos:false,
        delivery_notes:false,
        daily_activities:false,
        close_calls: false,
        toolbox_talks: false,
        clerk_of_works: false,
        powra: false,
        take_5s:false,
        incident_report:false,
        inspection_tour: false,
        good_calls: false,
        site_messaging: false,
        task_briefings:false,
        work_package_plan: false,
        rams: false,
        quality_checklist: false,
        collection_notes: false,
        observations: false,
        asset_vehicles: false,
        asset_equipment: false
    };
    loadPowerBiComponent: boolean = false;
    biToolName: string = '';
    biToolLabel: string = '';
    biModalTitle: string = 'Dashboard';
    @Input() projectFeaturePermission: ProjectFeaturePermission = {};
    featureSettingToolTipForLockOn:string = 'This tool is currently mandated for all company projects. To disable it, please reach out to your teams innDex champion';
    featureSettingToolTipForLockOff:string = 'This tool is currently unavailable for all company projects. To enable it, please reach out to your teams innDex champion';
    sectionAccess: any = {};
    countries: Array<any> = [];
    showProjectForm: boolean = false
    postcodeInput: any = {};
    constructor(
        private activatedRoute: ActivatedRoute,
        private authService: AuthService,
        private modalService: NgbModal,
        private toastService: ToastService,
        private projectService: ProjectService,
        private router: Router,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private resourceService: ResourceService,
        private sidebarUtils: SidebarUtilsService,
        private optimaService: OptimaService,
        private projectInspectionTourService: ProjectInspectionTourService,
        private closeCallService: CloseCallService,
        private domSanitizer: DomSanitizer,
        private inspectionBuilderService: InspectionBuilderService,
        private httpService: HttpService,
        private userService: UserService,
        private featureExclusionUtility: FeatureExclusionUtility,
    ) {
        this.is_mobile_nav = this.httpService.isMobileDevice();
        this.PROJECT_VALUE = this.projectService.PROJECT_VALUE;
        this.PROJECT_TYPES = this.projectService.PROJECT_TYPES;
    }

    routeRelation: any = {
        subProjectList: [
            'project-time-management',
            'dashboard',
            'inductions',
            'calendar-view',
            'project-warnings',
            'project-progress-photos',
            'project-collection-notes',
            'project-delivery-notes',
            'project-daily-activities',
            'project-toolbox-talks',
            'close-call',
            'clerk-of-works',
            'project-powra',
            'incident-reports',
            'project-incident-reports',
            'project-inspection-tour',
            'project-good-calls',
            'project-take5s',
            'project-task-briefings',
            'project-inspections',
            'project-work-package-plan',
            'project-rams',
            'quality-checklists',
            'project-site-messaging',
            'project-assets',
            'project-observations'
        ]
    };
    isDashboard:boolean=(this.activatedRoute.snapshot.url[1].path==='employer-project') ? true :false;
    sidebarIcons = AssetsUrl.sidebarIcons;
    ngOnInit() {
        this.project['main_contact_number_obj'] = {code: null, number: ''};
        if(!this.project.custom_field.location){
            this.project.custom_field.location = {
                lat:null,
                long:null,
                region:'',
                country:'',
                admin_district:'',
            }
        }
        this.authService.authUser.subscribe( (user:User) => {
            if(user && user.id) {
                this.authUser$ = user;
            }
        });
        this.employerInfo = this.employer;
        this.postcodeInput = this.featureExclusionUtility.showProjectPostalCode(this.employerInfo.country_code);
        if(this.postcodeInput?.type === 'address-lookup'){
            this.getCountries();
        }
        if(this.employerInfo.name){
            this.featureSettingToolTipForLockOn = `This tool is currently mandated for all ${this.employerInfo.name} projects. To disable it, please reach out to your teams innDex champion`;
            this.featureSettingToolTipForLockOff = `This tool is currently unavailable for all ${this.employerInfo.name} projects. To enable it, please reach out to your teams innDex champion`;
        }


        this.dashboardTypes = [
            {
                name: this.employerInfo.name, value: 'inspection-tour'
            },
            {
                name:'Subcontractors', value: 'subcontractor'
            }];

        this.activatedRoute.params.subscribe(params => {
            this.employerId = params['employerId'];
            //this.getCompanyProjects();
            this.projectId = params['projectId'];
            if(this.projectId) {
                this.sidebarUtils.getProjectDetails(this.projectId).subscribe(prj => {
                    const project = prj;
                    this.selectedCompanyProject = project;
                    this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
                    this.refreshMenuAccess();
                    this.projectRetrieved.emit(project || {});

                    //calculate submanu's height based on feature enable/disable
                    this.disableFeatureCount();
                    this.heightAdjustmentCalculate();
                });
            }
        });

        if(!this.knownTypeOfWorks){
            this.initializeData();
        }

        if(!this.availableTimeZones){
            this.initializeTimezoneData();
        }

        this.hasCompanyInspectionTours = (this.companyResolverResponse.hasCompanyInspectionTours && this.authUser$.roles.includes('COMPANY_ADMIN'));
        this.hasIBuilder = (this.companyResolverResponse.companyInspectionBuilders && this.companyResolverResponse.companyInspectionBuilders.length && this.authUser$.roles.includes('COMPANY_ADMIN'));
        this.hasCompanyCloseCalls = (this.companyResolverResponse.hasCompanyCloseCalls && this.authUser$.roles.includes('COMPANY_ADMIN'));
        this.hasCompanyGoodCalls = (this.companyResolverResponse.hasCompanyGoodCalls && this.authUser$.roles.includes('COMPANY_ADMIN'));
        this.hasCompanyObservations = (this.companyResolverResponse.hasCompanyObservations && this.authUser$.roles.includes('COMPANY_ADMIN'));
        this.hasCompanyIncidentReport = this.authUser$.roles.includes('COMPANY_ADMIN');
        this.hasCompanyUsageReport = this.authUser$.roles.includes('COMPANY_ADMIN');
        //this.dashboardReportFrom = (this.companyResolverResponse.liveProFirstInspectionCreatedAt || this.companyResolverResponse.disabledProFirstInspectionCreatedAt);
        this.liveProFirstInspectionCreatedAt = (this.companyResolverResponse.liveProFirstInspectionCreatedAt || this.companyResolverResponse.disabledProFirstInspectionCreatedAt);
        this.disabledProFirstInspectionCreatedAt = this.companyResolverResponse.disabledProFirstInspectionCreatedAt;
        this.company_induction_phrasing_singlr = this.companyResolverResponse.company._company_induction_phrasing ? this.companyResolverResponse.company._company_induction_phrasing.singlr : 'Company Induction';
        this.companyProjects = (this.companyResolverResponse.companyProjects || []).sort((a,b) => (a.createdAt < b.createdAt) ? 1 : ((b.createdAt < a.createdAt) ? -1 : 0));

        //get close call phrase from most recent project
        this.closeCallAlternetPhrase = (this.companyProjects.length && this.companyProjects[0].custom_field.cc_phrase) ? this.companyProjects[0].custom_field.cc_phrase : 'Close Call';
        this.goodCallAlternetPhrase = (this.companyProjects.length && this.companyProjects[0].custom_field.gc_phrase) ? this.companyProjects[0].custom_field.gc_phrase : 'Good Call';
        this.observationAlternetPhrase = (this.companyProjects.length && this.companyProjects[0].custom_field.obrs_phrase) ? this.companyProjects[0].custom_field.obrs_phrase : 'Observation';

        this.archivedCompanyProjects = this.companyResolverResponse.archivedCompanyProjects;
        this.companyInspectionBuilders = this.companyResolverResponse.companyInspectionBuilders;
        this.companyFeaturesStatus = this.companyResolverResponse.companyFeaturesStatus;
    }

    ngOnChanges() {
        //update company inspection builders link in sidebar based on activate/deactivate dashboard
        this.companyInspectionBuilders = this.activatedDashboardIBs;
        this.companyFeaturesStatus = this.featuresStatus;
        this.company_induction_phrasing_singlr = this.companyResolverResponse.company._company_induction_phrasing ? this.companyResolverResponse.company._company_induction_phrasing.singlr : 'Company Induction';
        this.project.company_additional_project_section_access = this.companyProjectSectionAccess;
    }

    @ViewChild('projectFormPopup') projectFormPopupRef: IModalComponent;
    openProjectForm() {
        this.modalRequestInProgress = true;
        this.geofenceData = new OptimaSetting;
        this.projectService.checkCompanyProjectStatus(this.employerId).subscribe(data => {
            this.modalRequestInProgress = false;
            if(data.success){
                this.projectFormPopupRef.open();
                this.showProjectForm = true
            } else if(data.max_reached){
                this.statusResponseMessage = data.message ? data.message : '';
                this.confirmationModalRef.openConfirmationPopup({
                    headerTitle: 'Maximum number of projects reached',
                    title: this.statusResponseMessage,
                    confirmLabel: 'Continue',
                    hasCancel: false,
                    onConfirm: () => {
                        return true;
                    }
                });
            }
            else {
                const message = data.message || 'Failed to store data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });

    }

    /**
     * Get availableTimeZones
     */
     initializeTimezoneData() {
        this.resourceService.getTimezones().subscribe((data: any) => {
            if(data && data.success) {
                this.availableTimeZones = data.timezones;
            } else {
                const message = data.message || 'Failed to fetch timezones.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
        return;
    }

    getCountries() {
        this.userService.getCountries('countries').subscribe((res: any) => {
            this.countries = res;
        });
    }

    openCompanyAccess(companyAccessFormPopup) {
        const modalRef = this.modalService.open(companyAccessFormPopup, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: "lg"
        });
    }

    @ViewChild('projectSiteConfigPopup', { static: true }) projectSiteConfigPopupRef: CompanySiteConfigComponent;
    projectSiteConfigModal() {
        if(this.projectSiteConfigPopupRef && this.projectSiteConfigPopupRef.projectSiteConfig){
            this.projectSiteConfigPopupRef.projectSiteConfig();
        }
    }

    @ViewChild('cowOwenerTaggingPopup') cowOwenerTaggingPopupRef: CowOwenerTaggingComponent;
    tagOwnersModal() {
        if(this.cowOwenerTaggingPopupRef && this.cowOwenerTaggingPopupRef.openOwenerTagging){
            this.cowOwenerTaggingPopupRef.openOwenerTagging();
        }
    }

    @ViewChild('altPhraseTb') altPhraseTbRef: AlternativePhraseSettingComponent;
    altPhraseTbModal() {
        if(this.altPhraseTbRef && this.altPhraseTbRef.openModal){
            this.altPhraseTbRef.openModal();
        }
    }

    @ViewChild('altPhraseWpp') altPhraseWppRef: AlternativePhraseSettingComponent;
    altPhraseWppModal() {
        if(this.altPhraseWppRef && this.altPhraseWppRef.openModal){
            this.altPhraseWppRef.openModal();
        }
    }

    @ViewChild('altPhrasePermit') altPhrasePermitRef: AlternativePhraseSettingComponent;
    altPhrasePermitModal() {
        if(this.altPhrasePermitRef && this.altPhrasePermitRef.openModal){
            this.altPhrasePermitRef.openModal();
        }
    }

    @ViewChild('altPhraseRams') altPhraseRamsRef: AlternativePhraseSettingComponent;
    altPhraseRamsModal() {
        if(this.altPhraseRamsRef && this.altPhraseRamsRef.openModal){
            this.altPhraseRamsRef.openModal();
        }
    }

    @ViewChild('altPhraseQcl') altPhraseQclRef: AlternativePhraseSettingComponent;
    altPhraseQclModal() {
        if(this.altPhraseQclRef && this.altPhraseQclRef.openModal){
            this.altPhraseQclRef.openModal();
        }
    }

    @ViewChild('altPhraseT5s') altPhraseT5sRef: AlternativePhraseSettingComponent;
    altPhraseT5sModal() {
        if(this.altPhraseT5sRef && this.altPhraseT5sRef.openModal){
            this.altPhraseT5sRef.openModal();
        }
    }

    @ViewChild('altPhraseDa') altPhraseDaRef: AlternativePhraseSettingComponent;
    altPhraseDaModal() {
        if(this.altPhraseDaRef && this.altPhraseDaRef.openModal){
            this.altPhraseDaRef.openModal();
        }
    }

    @ViewChild('altPhraseCn') altPhraseCnRef: AlternativePhraseSettingComponent;
    altPhraseCnModal() {
        if(this.altPhraseCnRef && this.altPhraseCnRef.openModal){
            this.altPhraseCnRef.openModal();
        }
    }

    @ViewChild('altPhraseDn') altPhraseDnRef: AlternativePhraseSettingComponent;
    altPhraseDnModal() {
        if(this.altPhraseDnRef && this.altPhraseDnRef.openModal){
            this.altPhraseDnRef.openModal();
        }
    }

    @ViewChild('altPhraseGc') altPhraseGcRef: AlternativePhraseSettingComponent;
    altPhraseGcModal() {
        if(this.altPhraseGcRef && this.altPhraseGcRef.openModal){
            this.altPhraseGcRef.openModal();
        }
    }

    @ViewChild('altPhraseObrs') altPhraseObrsRef: AlternativePhraseSettingComponent;
    altPhraseObrsModal() {
        if(this.altPhraseObrsRef && this.altPhraseObrsRef.openModal){
            this.altPhraseObrsRef.openModal();
        }
    }

    @ViewChild('altPhrasePowra') altPhrasePowraRef: AlternativePhraseSettingComponent;
    altPhrasePowraModal() {
        if(this.altPhrasePowraRef && this.altPhrasePowraRef.openModal){
            this.altPhrasePowraRef.openModal();
        }
    }

    @ViewChild('altPhraseCc') altPhraseCcRef: AlternativePhraseSettingComponent;
    altPhraseCcModal() {
        if(this.altPhraseCcRef && this.altPhraseCcRef.openModal){
            this.altPhraseCcRef.openModal();
        }
    }

    search(type?,value?) {
        this.router.navigate(['/company-admin/employer-project/'+this.employerId], { queryParams: { type: type,value:value,query:this.query } });
    }

    saveCompanyProject(projectForm: any, event) {
        if (!projectForm.valid || !this.isEndDateValid) {
            const message = 'Something went wrong, form data is not valid.';
            this.toastService.show(this.toastService.types.INFO, message, { data: projectForm.errors });
            return false;
        }
        this.project.parent_company = this.employerId;
        if(!this.project.custom_field){
            this.project.custom_field = {};
        }
        this.project.custom_field.country_code = this.employerInfo?.country_code;
        if(this.postcodeInput?.type === 'address-lookup'){
            const countryObJ = (this.countries || []).find(country => country.code.toLowerCase() === this.project.custom_field.country_code.toLowerCase());
            this.project.postcode = "00000";
            this.project.custom_field.location.country = countryObJ.name;
        }
        this.project.project_category = 'company-project';
        this.project.start_date = this.project.start_date ? this.ngbMomentjsAdapter.toModel(this.project.start_date).valueOf() : null;
        this.project.end_date = this.project.end_date ? this.ngbMomentjsAdapter.toModel(this.project.end_date).valueOf() : null;
        // create project
        this.project.is_active = 1;
        this.project.disabled_on = null;
        this.addProjectRequestInProgress = true;
        this.projectService.createProject(this.project).subscribe(data => this.handleResponseFromModelSubmit(event, projectForm, data));
        this.closeModal();
    }

    closeModal() {
        this.projectFormPopupRef.close()
        this.showProjectForm = false
    }

    private handleResponseFromModelSubmit(event, $projectForm, data:any) {
        this.addProjectRequestInProgress = false;
        if (data.success) {
            this.geofenceData.project_ref = data.project.id;
            this.saveGeofenceData();
            $projectForm.reset && $projectForm.reset();
            this.emitReInitializeProjectsByEmployer();
            event.closeFn();
        } else {
            const message = data.message || 'Failed to store data.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
        }
    }

    saveGeofenceData() {

        this.optimaService.addUpdateOptimaSetting(this.geofenceData).subscribe(res=> {
            console.log("Saved the geofence data");
        });

    }

    isChildRoute(parentRouteName, projectId = 0) {
        let routeName = this.activatedRoute.snapshot.url[1].path;
        let projectIdParam = this.activatedRoute.snapshot.params.projectId;
        if(routeName != undefined) {
            return this.routeRelation[parentRouteName].includes(routeName) && (projectIdParam == projectId);
        }
        return false;
    }

    /**
     * Get typeOfWorklist
     */
    initializeData() {
        // should be called only when needed
        this.resourceService.getTypeOfWorks().subscribe((data: any) => {
            if(data && data.success) {
                this.knownTypeOfWorks = data.typeOfWorklist;
            } else {
                const message = data.message || 'Failed to fetch type of works.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
        return;
    }

    emitReInitializeProjectsByEmployer(){
        this.reInitializeProjectsByEmployer.emit();
    }

    refreshMenuAccess() {
        let projectSectionAccess = {
            ...this.tool_access,
            ...(this.selectedCompanyProject.project_section_access || {})
        };
        this.sectionAccess = projectSectionAccess;
        //Consider company level access if project add/edit enabled for employer
        if (this.employerInfo.features_status.add_project) {
            projectSectionAccess = {
                ...projectSectionAccess,
                ...(this.selectedCompanyProject.company_additional_project_section_access || {})
            };
            this.sectionAccess = projectSectionAccess
        }

        this.hasTimeManagementAccess = projectSectionAccess.time_management;
        this.hasDailyActivities = projectSectionAccess.daily_activities;
        this.hasClerkOfWorksAccess = projectSectionAccess.clerk_of_works;
        this.hasPowraAccess = projectSectionAccess.powra;
        this.hasCloseCallAccess = projectSectionAccess.close_calls;
        this.hasToolboxTalksAccess = projectSectionAccess.toolbox_talks;
        this.hasPermitAccess = projectSectionAccess.permit;
        this.hasProgressPhotosAccess = projectSectionAccess.progress_photos;
        this.hasCollectionNotesAccess = projectSectionAccess.collection_notes;
        this.hasDeliveryNotesAccess = projectSectionAccess.delivery_notes;
        this.hasIncidentReportAccess = projectSectionAccess.incident_report;
        this.hasInspectionTour = projectSectionAccess.inspection_tour;
        this.hasGoodCalls = projectSectionAccess.good_calls;
        this.hasObservations = projectSectionAccess.observations;
        this.hasSiteMessaging = projectSectionAccess.site_messaging;
        this.hasTake5s = projectSectionAccess.take_5s;
        this.hasTaskBriefings = projectSectionAccess.task_briefings;
        this.hasQualityChecklist = projectSectionAccess.quality_checklist;
        this.hasWorkPackagePlan = projectSectionAccess.work_package_plan;
        this.hasRams = projectSectionAccess.rams;
        this.hasAssetManagement = (projectSectionAccess.asset_vehicles || projectSectionAccess.asset_equipment);
        this.hasInspections = (projectSectionAccess.inspection_tour || projectSectionAccess.ib_checklist) ? true : false;
    }

    isFeatureEnable(status_key: string): boolean {
        // Check if authUser$ and employerInfo are defined
        if (this.authUser$?.roles && this.employerInfo?.features_status) {
            const flag = this.authUser$?.roles.includes('COMPANY_ADMIN') && this.employerInfo?.features_status?.[status_key];
            if (flag || status_key === 'dashboards') {
                // Check if companyFeaturesStatus[status_key] is a boolean
                return (typeof this.companyFeaturesStatus[status_key] === 'boolean') ? this.companyFeaturesStatus[status_key] : true;
            }
            return flag;
        } else {
            // the case where one of the required properties is undefined
            return false;
        }
    }

    disableFeatureCount() {
        let non_visible_features = ['user_blacklisting'];
        for (let feature_status in this.employerInfo.features_status) {
            if (!non_visible_features.includes(feature_status) && this.employerInfo.features_status.hasOwnProperty(feature_status)) {
                if (!this.isFeatureEnable(feature_status)) {
                    this.disableFeaturesCount = this.disableFeaturesCount + 1;
                }
            }
        }
        return this.disableFeaturesCount;
    }

    heightAdjustmentCalculate(extraHeight = 0) {
        //add extra height based on project name
        extraHeight += (this.selectedCompanyProject && this.selectedCompanyProject.name && (this.selectedCompanyProject.name).length >= 25) ? 30 : 0;
        extraHeight += (this.hasCompanyInspectionTours || this.hasIBuilder || this.hasCompanyCloseCalls || this.hasCompanyGoodCalls || this.hasCompanyObservations || this.hasCompanyIncidentReport || this.hasCompanyUsageReport) ? 50 : 0;
        if (this.disableFeaturesCount) {
            let height = extraHeight + 600 - (50 * this.disableFeaturesCount);
            this.heightAdjustment = `100vh - ${height}px`;
        } else {
            let height = extraHeight + 596;
            this.heightAdjustment = `100vh - ${height}px`;
        }
    }

    onSaveGeofenceData(data) {
        this.geofenceData = data;
    }

    openDashboardGenericModal(dashboardType) {
        this.loadPowerBiComponent = true;
        this.biToolName = dashboardType;
        if (this.biToolName === 'close_call_company') {
            this.biModalTitle = `${this.closeCallAlternetPhrase} Dashboard`;
        } else if (this.biToolName === 'good_call_company') {
            this.biModalTitle = `${this.goodCallAlternetPhrase} Dashboard`;
        } else if (this.biToolName === 'observation_company') {
            this.biModalTitle = `${this.observationAlternetPhrase} Dashboard`;
        } else if (this.biToolName === 'incident_reports_company') {
            this.biModalTitle = `Incident Report Dashboard`;
        } else if (this.biToolName === 'usage_reports - Copy') {
            this.biModalTitle = `Usage Report Dashboard`;
        } else if (this.biToolName === 'inspections_company') {
            this.biModalTitle = `Inspections Dashboard`;
        }
    }

    powerBiDashboardClose() {
        this.loadPowerBiComponent = false;
    }

    openModal(content, size='') {
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size
        });
    }

    @ViewChild('dashboardHtml', { static: true })
    private dashboardHtmlRef: TemplateRef<any>;
    openDashboardModal(featureValue, isAlreadyOpen, record:any = {}) {
        this.currentDashboardFeature = featureValue;
        if (this.currentDashboardFeature == 'inspection-tour') {
            this.dashboardTypes = [
                {
                    name: this.employerInfo.name, value: 'inspection-tour'
                },
                {
                    name:'Subcontractors', value: 'subcontractor'
                }];
        } else if (this.currentDashboardFeature == 'inspection-builder') {
            this.dashboardTypes = [
                {
                    name: this.employerInfo.name, value: 'inspection-builder'
                },
                {
                    name:'Subcontractors', value: 'ib-subcontractor'
                }];
        }
        this.currentDashboardFeatureName = (this.dashboardTypes).reduce((name, item) => {
            if(item.value == featureValue) {
                name = item.name
            }
            return name;
        }, '');
        this.loadingDashboard = true;

        //reset
        this.selectedDashboardProjects = [];
        this.includeArchivedProject = false;
        this.projectsToSelect = this.sortByName(this.companyProjects);

        this.dashboardReportFrom  = dayjs().startOf('day').subtract(1, 'year').valueOf();
        this.dashboardReportTo = dayjs().endOf('day').valueOf();
        this.dashboardStartDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+this.dashboardReportFrom)) as NgbDate;

        let body: any = {
            from_date: this.dashboardReportFrom,
            to_date: this.dashboardReportTo,
            include_archived_project: this.includeArchivedProject,
            selected_projects: (this.projectsToSelect || []).map(project => project.id),
        };

        if(this.currentDashboardFeature === 'inspection-tour') {
            body.file_name = 'Inspection-Tour-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            this.projectInspectionTourService.companyDashboardOfInspectionTour(body, this.employerId, 'html', body.file_name).subscribe((data:any) => {
                this.loadingDashboard = false;
                this.dashboardHtmlContent = this.domSanitizer.bypassSecurityTrustHtml(data);
            });
        } else if(this.currentDashboardFeature === 'subcontractor') {
            this.isNormalProjectDashboard = false;
            this.isIndustrialProjectDashboard = false;
            this.responsibleCompanies = [];
            body.file_name = 'Subcontractors-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            body.selected_responsible_companies = this.selectedResponsibleCompanies;

            for (let project of this.projectsToSelect) {
                if ((body.selected_projects || []).includes(project.id)) {
                    if (project.project_type == 'industrial') {
                        this.isIndustrialProjectDashboard = true;
                    } else {
                        this.isNormalProjectDashboard = true;
                    }
                }
            }

            this.projectInspectionTourService.subContractorDashboard(body, this.employerId, 'html', body.file_name).subscribe((data:any) => {
                this.loadingDashboard = false;
                data = JSON.parse(data);
                this.responsibleCompanies = data.tagged_companies;
                this.dashboardHtmlContent = this.domSanitizer.bypassSecurityTrustHtml(data.html);
            });
        } else if(this.currentDashboardFeature === 'inspection-builder') {
            this.inspectionBuilderInfo = (record.ib_title) ? record : this.inspectionBuilderInfo;
            for (let item of this.inspectionBuilderInfo.scoring_system.values) {
                this.ib_scoring_system[item] = true;
            }

            body.file_name = this.replaceAll(this.inspectionBuilderInfo.ib_title, ' ', '-')+'-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            //Remove not required properties
            delete body.cc_phrase;

            this.inspectionBuilderService.companyDashboardOfInspectionBuilder(body, this.employerId, this.inspectionBuilderInfo.id,'html', body.file_name).subscribe((data:any) => {
                this.loadingDashboard = false;
                this.dashboardHtmlContent = this.domSanitizer.bypassSecurityTrustHtml(data);
            });
        } else if(this.currentDashboardFeature === 'ib-subcontractor') {
            this.responsibleCompanies = [];
            body.file_name = 'Subcontractors-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            body.selected_responsible_companies = this.selectedResponsibleCompanies;
            //Remove not required properties
            delete body.cc_phrase;

            this.inspectionBuilderService.ibSubContractorDashboard(body, this.employerId, this.inspectionBuilderInfo.id, 'html', body.file_name).subscribe((data:any) => {
                this.loadingDashboard = false;
                data = JSON.parse(data);
                this.responsibleCompanies = data.tagged_companies;
                this.dashboardHtmlContent = this.domSanitizer.bypassSecurityTrustHtml(data.html);
            });
        }
        if (!isAlreadyOpen) {
            this.openModal(this.dashboardHtmlRef, 'xl');
        }
    }

    downloadDashboard() {
        this.dashboardLoader = true;

        let body:any = {
            from_date: this.dashboardReportFrom,
            to_date: this.dashboardReportTo,
            include_archived_project: this.includeArchivedProject,
            selected_projects: (this.selectedDashboardProjects.length) ? this.selectedDashboardProjects : (this.projectsToSelect || []).map(project => project.id),
            cc_phrase: this.closeCallAlternetPhrase,
        };

        if(this.currentDashboardFeature === 'inspection-tour') {
            body.file_name = 'Inspection-Tour-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            this.projectInspectionTourService.companyDashboardOfInspectionTour(body, this.employerId, 'pdf', body.file_name, () => {
                this.dashboardLoader = false;
            });
        } else if(this.currentDashboardFeature === 'subcontractor') {
            body.file_name = 'Subcontractors-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            body.selected_responsible_companies = this.selectedResponsibleCompanies;
            this.projectInspectionTourService.subContractorDashboard(body, this.employerId, 'pdf', body.file_name, () => {
                this.dashboardLoader = false;
            });
        } else if (this.currentDashboardFeature === 'inspection-builder') {
            body.file_name = this.replaceAll(this.inspectionBuilderInfo.ib_title, ' ', '-')+'-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            //Remove not required properties
            delete body.cc_phrase;

            this.inspectionBuilderService.companyDashboardOfInspectionBuilder(body, this.employerId, this.inspectionBuilderInfo.id,'pdf', body.file_name, () => {
                this.dashboardLoader = false;
            });
        } else if (this.currentDashboardFeature === 'ib-subcontractor') {
            body.file_name = 'Subcontractors-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            body.selected_responsible_companies = this.selectedResponsibleCompanies;
            //Remove not required properties
            delete body.cc_phrase;

            this.inspectionBuilderService.ibSubContractorDashboard(body, this.employerId, this.inspectionBuilderInfo.id, 'pdf', body.file_name, () => {
                this.dashboardLoader = false;
            });
        }
    }

    downloadTaggedOwnerDashboard($event) {
        this.dashboardLoader = true;
        let body:any = {
            from_date: this.ngbMomentjsAdapter.ngbDateToDayJs($event.fromDate).startOf('day').valueOf(),
            to_date: this.ngbMomentjsAdapter.ngbDateToDayJs(($event.toDate || $event.fromDate)).endOf('day').valueOf(),
            include_archived_project: this.includeArchivedProject,
            selected_projects: (this.selectedDashboardProjects.length) ? this.selectedDashboardProjects : (this.projectsToSelect || []).map(project => project.id),
            selected_tagged_owner: this.selectedTaggedOwner,
            normal_projects_rating: this.normalProjectsInspectionTourRating,
            industrial_projects_rating: this.industrialProjectsInspectionTourRating,
            sort_items_by: this.sort_items_by
        };

        body.file_name = `${this.selectedTaggedOwner.name} Inspection Report`;
        if(this.currentDashboardFeature === 'ib-subcontractor') {
            delete body.normal_projects_rating;
            delete body.industrial_projects_rating;
            body.ib_scoring_system = this.ib_scoring_system;

            this.inspectionBuilderService.ibTaggedOwnerDashboard(body, this.employerId, this.selectedTaggedOwner.id, this.inspectionBuilderInfo.id, body.file_name, () => {
                this.dashboardLoader = false;
            });
        } else {
            this.projectInspectionTourService.taggedOwnerDashboard(body, this.employerId, this.selectedTaggedOwner.id, 'pdf', body.file_name, () => {
                this.dashboardLoader = false;
            });
        }
    }

    updateCompanyDashboard($event, includeArchived, unSelectProjects, unSelectCompanies) {
        this.dashboardLoader = true;
        let fromDateMs = ($event.fromDate) ? this.ngbMomentjsAdapter.ngbDateToDayJs($event.fromDate).valueOf() : this.dashboardReportFrom;
        let toDateMs = ($event.toDate) ? this.ngbMomentjsAdapter.ngbDateToDayJs($event.toDate).valueOf() : this.dashboardReportTo;

        this.dashboardReportFrom  = dayjs(+fromDateMs).startOf('day').valueOf();
        this.dashboardReportTo = dayjs(+toDateMs).endOf('day').valueOf();

        if(unSelectProjects) {
            this.unSelectProjects();
        }

        if(unSelectCompanies) {
            this.unSelectAllResponsibleCompanies();
        }

        if (this.includeArchivedProject) {
            this.projectsToSelect = this.sortByName([...this.companyProjects, ...this.archivedCompanyProjects]);
        } else {
            this.projectsToSelect = this.sortByName(this.companyProjects);
        }

        let body:any = {
            from_date: this.dashboardReportFrom,
            to_date: this.dashboardReportTo,
            include_archived_project: this.includeArchivedProject,
            selected_projects:  (this.selectedDashboardProjects.length) ? this.selectedDashboardProjects : (this.projectsToSelect || []).map(project => project.id),
            cc_phrase: this.closeCallAlternetPhrase,
        };

        if(this.projectSelectorModalRef) {
            this.projectSelectorModalRef.close();
            this.projectSelectorModalRef = null;
        }

        if(this.currentDashboardFeature === 'inspection-tour') {
            body.file_name = 'Inspection-Tour-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            this.projectInspectionTourService.companyDashboardOfInspectionTour(body, this.employerId, 'html', body.file_name).subscribe((data: any) => {
                this.dashboardLoader = false;
                this.dashboardHtmlContent = this.domSanitizer.bypassSecurityTrustHtml(data);
            });
        } else if(this.currentDashboardFeature === 'subcontractor') {
            this.isNormalProjectDashboard = false;
            this.isIndustrialProjectDashboard = false;
            this.responsibleCompanies = [];
            body.file_name = 'Subcontractors-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            body.selected_responsible_companies = this.selectedResponsibleCompanies;

            for (let project of this.projectsToSelect) {
                if ((body.selected_projects || []).includes(project.id)) {
                    if (project.project_type == 'industrial') {
                        this.isIndustrialProjectDashboard = true;
                    } else {
                        this.isNormalProjectDashboard = true;
                    }
                }
            }

            this.projectInspectionTourService.subContractorDashboard(body, this.employerId, 'html', body.file_name).subscribe((data:any) => {
                this.dashboardLoader = false;
                data = JSON.parse(data);
                this.responsibleCompanies = data.tagged_companies;
                this.dashboardHtmlContent = this.domSanitizer.bypassSecurityTrustHtml(data.html);
            });
        } else if (this.currentDashboardFeature === 'inspection-builder') {
            body.file_name = this.replaceAll(this.inspectionBuilderInfo.ib_title, ' ', '-')+'-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            //Remove not required properties
            delete body.cc_phrase;

            this.inspectionBuilderService.companyDashboardOfInspectionBuilder(body, this.employerId, this.inspectionBuilderInfo.id,'html', body.file_name).subscribe((data:any) => {
                this.dashboardLoader = false;
                this.dashboardHtmlContent = this.domSanitizer.bypassSecurityTrustHtml(data);
            });
        } else if(this.currentDashboardFeature === 'ib-subcontractor') {
            this.responsibleCompanies = [];
            body.file_name = 'Subcontractors-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            body.selected_responsible_companies = this.selectedResponsibleCompanies;
            //Remove not required properties
            delete body.cc_phrase;

            this.inspectionBuilderService.ibSubContractorDashboard(body, this.employerId, this.inspectionBuilderInfo.id, 'html', body.file_name).subscribe((data:any) => {
                this.dashboardLoader = false;
                data = JSON.parse(data);
                this.responsibleCompanies = data.tagged_companies;
                this.dashboardHtmlContent = this.domSanitizer.bypassSecurityTrustHtml(data.html);
            });
        }
    }

    selectAllProject() {
        this.selectedDashboardProjects = (this.projectsToSelect || []).map(project => project.id);
    }

    unSelectProjects() {
        this.selectedDashboardProjects = [];
    }

    sortByName(arrOfObjs) {
        return (arrOfObjs || []).sort((a,b) => (a.name > b.name) ? 1 : ((b.name > a.name) ? -1 : 0));
    }

    toggleDashboardMenu($event) {
        $event.stopPropagation();
        let extraHeight = 0;
        if(this.isDashboardMenuCollapsed) {
            extraHeight += this.hasCompanyInspectionTours ? 35 : 0;
            extraHeight += this.hasIBuilder ? 35 : 0;
            extraHeight += this.hasCompanyCloseCalls ? 35 : 0;
            extraHeight += this.hasCompanyGoodCalls ? 35 : 0;
            extraHeight += this.hasCompanyObservations ? 35 : 0;
            extraHeight += this.hasCompanyIncidentReport ? 35 : 0;
            extraHeight += this.hasCompanyUsageReport ? 35 : 0;
        }

        this.heightAdjustmentCalculate(extraHeight);
    }

    downloadParticipantsList() {
        this.dashboardLoader = true;

        let body:any = {
            from_date: this.dashboardReportFrom,
            to_date: this.dashboardReportTo,
            include_archived_project: this.includeArchivedProject,
            selected_projects: (this.selectedDashboardProjects.length) ? this.selectedDashboardProjects : (this.projectsToSelect || []).map(project => project.id),
        };

        if (this.currentDashboardFeature === 'inspection-tour' || this.currentDashboardFeature === 'subcontractor') {
            this.projectInspectionTourService.downloadParticipantsList(body, this.employerId, () => {
                this.dashboardLoader = false;
            });
        } else {
            this.inspectionBuilderService.downloadParticipantsList(body, this.employerId, this.inspectionBuilderInfo.id, () => {
                this.dashboardLoader = false;
            });
        }
    }

    selectAllResponsibleCompanies() {
        this.selectedResponsibleCompanies = (this.responsibleCompanies || []).map(company => company.id);
    }

    unSelectAllResponsibleCompanies() {
        this.selectedResponsibleCompanies = [];
    }

    replaceAll(str='', find, replace) {
        const escapedFind = find.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, '\\$1');
        return str.replace(new RegExp(escapedFind, 'g'), replace);
    }

    disableToolTipStatus(status: string):boolean {
        return (status === 'unlocked' || status === 'auto-on');
    }


    disableToolStatus(status: string):boolean {
        return (status === 'lock-on' || status === 'lock-off');
    }

    changeTaggedOwner(event, $taggedOwnerElem: any = {}) {
        let [taggedOwner] = $taggedOwnerElem.selectedValues;
        this.selectedTaggedOwner = taggedOwner;
    }

    get isEndDateValid() {
        if (this.project.start_date && this.project.end_date) {
            const endDate = this.ngbMomentjsAdapter.toModel(this.project.end_date);
            const startDate = this.ngbMomentjsAdapter.toModel(this.project.start_date);
            return endDate ? endDate.isAfter(startDate) : false;
        }
        return true;
    }
    validatePostcode(pPostcode?){
        this.addProjectRequestInProgress = true;
        this.projectService.validatePostcode(this.project.postcode, this.employerInfo?.country_code).subscribe((response: {success:boolean, latLongData: ProjectLocation }) => {
            if(response.success && response.latLongData){
                pPostcode.control.setErrors(null);
                this.project.custom_field.location = response.latLongData;
            } else {
                pPostcode.control.setErrors({valid: true});
            }
            this.addProjectRequestInProgress = false;
        })
    }
}

<svg xmlns="http://www.w3.org/2000/svg" class="d-none">
    <symbol viewBox="0 0 497.59998 339.9165" id="dashboard">
        <path d="m483.9,331.1c2.7,0,5-1.8,5.7-4.4,5.2-20.7,8-42.4,8-64.8,0-140.5-109.7-254.4-245-254.4S7.5,121.4,7.5,261.9s2.8,44.1,8,64.8,3,4.4,5.7,4.4h462.7Z" style="fill: none; stroke: rgb(255, 255, 255); stroke-miterlimit: 10; stroke-width: 15px;"/>
        <line x1="8.7" y1="253.3" x2="48" y2="253.3" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 15px;"/>
        <line x1="129.9" y1="41.6" x2="150.1" y2="75.6" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 15px;"/>
        <line x1="497" y1="253.3" x2="457.8" y2="253.3" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 15px;"/>
        <line x1="374.8" y1="41.6" x2="354.6" y2="75.6" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 15px;"/>
        <line x1="43.6" y1="133.6" x2="69.5" y2="149.5" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 15px;"/>
        <line x1="455" y1="133.6" x2="429.1" y2="149.5" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 15px;"/>
        <line x1="252.5" y1="237.1" x2="252.5" y2="152.3" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 15px;"/>
        <circle cx="252" cy="265.6" r="27" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 15px;"/>
        <line x1="252.5" y1="56.1" x2="252.5" y2="8.3" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 15px;"/>
    </symbol>
    <symbol viewBox="0 0 454.2 506.4" id="inductions">
        <rect x="49.1" y="7.5" width="397.5" height="491.4" rx="10" ry="10" style="fill: none; stroke: rgb(255, 255, 255); stroke-miterlimit: 10; stroke-width: 15px;"/>
        <circle cx="249.5" cy="188.8" r="50.9" style="fill: none; stroke: rgb(255, 255, 255); stroke-miterlimit: 10; stroke-width: 15px;"/>
        <path d="m149.4,366.4h196.2c.7,0,1.3-.6,1.3-1.3v-38.2c0-7.2-3-14.1-8.2-19-12.5-11.7-40.2-30.5-89.1-30.5s-80.8,22.8-93.8,35.7c-4.9,4.9-7.6,11.5-7.6,18.4v33.6c0,.7.6,1.3,1.3,1.3Z" style="fill: none; stroke: rgb(255, 255, 255); stroke-miterlimit: 10; stroke-width: 15px;"/>
        <line x1="48.5" y1="253.2" x2="7.5" y2="253.2" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 15px;"/>
        <line x1="48.8" y1="399.7" x2="7.8" y2="399.7" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 15px;"/>
        <line x1="48.8" y1="110.3" x2="7.8" y2="110.3" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 15px;"/>
    </symbol>
    <symbol viewBox="0 0 505.7 505.7" id="timeManagement">
        <circle cx="252.9" cy="252.9" r="245.4" style="fill: none; stroke: rgb(255, 255, 255); stroke-miterlimit: 10; stroke-width: 15px;"/>
        <path d="m117.3,252.4l133.5.3c1.4,0,2.6-1.2,2.6-2.6V62.7" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 15px;"/>
    </symbol>
    <symbol viewBox="0 0 509.3 408.1" id="toolboxTalks">
        <path d="m44.2,356.7c-2.9-.9-4.4-4.2-3.1-7,5.8-12.3,19.6-43.5,19.6-63s-28.6-50.2-46.7-104.8C1.1,142.8,7.3,99.7,31.4,66.2c16-22.1,41.2-44,82.1-53,130.4-29,190.1,58.8,196,96.3,5.4,33.7-5.1,43.5-.9,57.7,4.5,15.1,17.1,45.1,31.1,61.6,5.4,6.4,4,16.1-3.1,20.5l-13.3,8.3c-4.3,2.7-6.6,7.6-5.8,12.6l1.2,7.5c.6,3.9-1.2,7.7-4.6,9.7h0c-2.6,1.5-3.6,4.8-2.2,7.5h0c2.1,4.2,1.1,9.3-2.5,12.3l-3.8,3.2c-1.3,1.1-1.9,2.8-1.7,4.5,1,7.5,2.6,28.7-9.5,35.7s-55.7-5.1-70.2,2.6-26.4,33.5-31.9,44.2c-1.3,2.5-4.2,3.7-6.9,2.9l-141.4-43.7Z" style="fill: none; stroke: rgb(255, 255, 255); stroke-miterlimit: 10; stroke-width: 14.8px;"/>
        <path d="m394.3,223.6c9.5,13.3,14.5,30,12.9,47.6s-8,30.1-17.9,41.1" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 14.8px;"/>
        <path d="m433.3,195c16.3,22.9,24.9,51.5,22.2,81.7-2.4,27.3-13.7,51.7-30.7,70.6" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 14.8px;"/>
        <path d="m462.8,136.7c28.2,39.5,42.9,88.8,38.3,140.9-4.2,47.1-23.6,89.1-52.9,121.7" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 14.8px;"/>
    </symbol>
    <symbol viewBox="0 0 447.3 512" id="employees">
        <circle cx="228" cy="118.2" r="110.7" style="fill:none; stroke:#fff; stroke-miterlimit:10; stroke-width:15px;"/>
        <path d="m10.3,504.5h426.6c1.6,0,2.8-1.3,2.8-2.8v-83c0-15.7-6.5-30.7-17.9-41.4-27.2-25.5-87.4-66.4-193.9-66.4s-175.8,49.5-204,77.7c-10.6,10.6-16.5,25-16.5,40.1v73c0,1.6,1.3,2.8,2.8,2.8Z" style="fill:none; stroke:#fff; stroke-miterlimit:10; stroke-width:15px;"/>
    </symbol>
    <symbol viewBox="0 0 504.4 443.4" id="incidentReport">
        <path d="m267,18.1c-8.1-14.1-21.4-14.1-29.6,0L11,410.3c-8.1,14.1-1.5,25.6,14.8,25.6h452.9c16.3,0,22.9-11.5,14.8-25.6L267,18.1Z" style="fill: none; stroke: rgb(255, 255, 255); stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 15px;"/>
        <path d="m231.8,349c0-11.2,7.8-19.3,18.4-19.3s18.4,8.1,18.4,19.3-7.2,19.3-18.4,19.3-18.4-8.4-18.4-19.3Zm15.2-44.6c-4.1,0-7.5-3.3-7.6-7.4l-3.9-135c-.1-4.1,3.1-7.4,7.2-7.4h15.2c4.1,0,7.3,3.3,7.2,7.4l-3.9,135c-.1,4.1-3.5,7.4-7.6,7.4h-6.4Z" style="fill: rgb(255, 255, 255);"/>
    </symbol>
    <symbol viewBox="0 0 507 359.5" id="eLearning">
        <path d="m93.6,160.5v182c0,5.3,4.3,9.5,9.5,9.5h264.8c5.3,0,9.5-4.3,9.5-9.5v-182.8" style="fill:none; stroke:#fff; stroke-miterlimit:10; stroke-width:15px;"/>
        <path d="m457.2,121l-218.7,106.2c-1.3.6-2.8.6-4.1,0L10.2,121c-3.6-1.7-3.6-6.9,0-8.6L234.4,7.9c1.3-.6,2.8-.6,4.1,0l218.7,104.4c3.6,1.7,3.6,6.8,0,8.6Z" style="fill:none; stroke:#fff; stroke-miterlimit:10; stroke-width:15px;"/>
        <line x1="460.2" y1="118" x2="460.2" y2="196.8" style="fill:none; stroke:#fff; stroke-linecap:round; stroke-miterlimit:10; stroke-width:15px;"/>
        <circle cx="458.5" cy="238.6" r="41" style="fill:none; stroke:#fff; stroke-miterlimit:10; stroke-width:15px;"/>
    </symbol>
    <symbol viewBox="0 0 482.8 509.7" id="projects">
        <path d="m24.8,150.7v-48.2c0-16.7,13.5-30.2,30.2-30.2h147.8l39.6,47.3,178.7,1.6c16.1.1,29,13.2,29,29.3v50.8" style="fill:none; stroke:#fff; stroke-miterlimit:10; stroke-width:15px;"/>
        <path d="m443.4,200.3c17.7.2,31.9,14.5,31.9,32.2v236.4c0,18.4-14.9,33.2-33.2,33.2H40.7c-18.4,0-33.2-14.9-33.2-33.2V179.8c0-18.4,14.9-33.2,33.2-33.2h162.6l43.5,52,196.5,1.8" style="fill:none; stroke:#fff; stroke-miterlimit:10; stroke-width:15px;"/>
        <path d="m52.7,72.3v-38.8c0-14.4,11.7-26,26-26h127.4l34.1,40.7,154,1.4c13.8.1,25,11.4,25,25.2v46.2" style="fill:none; stroke:#fff; stroke-miterlimit:10; stroke-width:15px;"/>
    </symbol>
</svg>
<div *ngIf="is_mobile_nav && is_drawer_open" class="mask position-absolute" (click)="is_drawer_open = !is_drawer_open;"></div>
<div class="company-side-nav pt-0" [ngClass]="{'mobile-nav': is_mobile_nav, 'side-nav-drawer-closed': !is_drawer_open, 'side-nav-drawer-open': is_drawer_open}">
    <aside *ngIf="employerInfo">
        <div class="sidebar left ">
            <div class="side-nav-drawer text-right" *ngIf="is_mobile_nav">
                <span (click)="is_drawer_open = !is_drawer_open;" class="fa fa-bars drawer-icon"></span>
            </div>
            <ul *ngIf="!is_mobile_nav || is_drawer_open" [ngClass]="{'list-sidebar bg-default': true, 'mobile-sidebar': is_mobile_nav}">
                <li *ngIf="(hasCompanyInspectionTours || hasIBuilder || hasCompanyCloseCalls || hasCompanyGoodCalls || hasCompanyObservations || hasCompanyIncidentReport || hasCompanyUsageReport) && isFeatureEnable('dashboards')">
                    <a href="javascript:void(0)" (click)="toggleDashboardMenu($event); isDashboardMenuCollapsed = !isDashboardMenuCollapsed" class="collapsed">
                        <div class="sidebar-icon"><svg><use xlink:href="#dashboard"></use></svg></div>
                        <span class="nav-label">Dashboards</span>
                        <span [ngClass]="{'fa pull-right': true, 'fa-chevron-down': !isDashboardMenuCollapsed, 'fa-chevron-up': isDashboardMenuCollapsed}"></span>
                    </a>
                    <ul [ngbCollapse]="isDashboardMenuCollapsed" class="sub-menu collapse" id="dashboardMenu">
                        <li *ngIf="hasCompanyInspectionTours">
                            <a href="javascript:void(0)" (click)="openDashboardModal('inspection-tour', false)">Inspection Tour</a>
                        </li>
                        <!-- <ng-container *ngIf="hasIBuilder">
                        <ng-template ngFor let-item [ngForOf]="companyInspectionBuilders" let-i="index">
                            <li>
                                <a href="javascript:void(0)" (click)="openDashboardModal('inspection-builder', false, item)">{{ item.ib_title }}</a>
                            </li>
                        </ng-template>
                        </ng-container>-->
                        <li *ngIf="hasIBuilder">
                            <a href="javascript:void(0)" (click)="openDashboardGenericModal('inspections_company')">Inspections</a>
                        </li>
                        <li *ngIf="hasCompanyCloseCalls">
                            <a href="javascript:void(0)" (click)="openDashboardGenericModal('close_call_company')">{{this.closeCallAlternetPhrase}}</a>
                        </li>
                        <li *ngIf="hasCompanyGoodCalls">
                            <a href="javascript:void(0)" (click)="openDashboardGenericModal('good_call_company')">{{this.goodCallAlternetPhrase}}</a>
                        </li>
                        <li *ngIf="hasCompanyObservations">
                            <a href="javascript:void(0)" (click)="openDashboardGenericModal('observation_company')">{{this.observationAlternetPhrase}}</a>
                        </li>
                        <li *ngIf="hasCompanyIncidentReport">
                            <a href="javascript:void(0)" (click)="openDashboardGenericModal('incident_reports_company')">Incident Report</a>
                        </li>
                        <li *ngIf="hasCompanyUsageReport">
                            <a href="javascript:void(0)" (click)="openDashboardGenericModal('usage_reports')">Usage Report</a>
                        </li>
                    </ul>
                </li>
                <li *ngIf="isFeatureEnable('employees')">
                    <a href="javascript:void(0)" [routerLinkActive]="'active'" [routerLink]="['/company-admin/employer-users/'+employerId]" class="last-menu">
                        <div class="sidebar-icon"> <svg><use xlink:href="#employees"></use></svg> </div>
                        <span class="nav-label">Employees</span>
                    </a>
                </li>

                <li *ngIf="employerInfo?._ci_enabled">
                    <a href="javascript:void(0)" [routerLinkActive]="'active'" [routerLink]="['/company-admin/company-inductions/'+employerId]" class="last-menu">
                        <div class="sidebar-icon"> <svg><use xlink:href="#inductions"></use></svg> </div>
                        <span class="nav-label">{{company_induction_phrasing_singlr}}</span>
                    </a>
                </li>
                <li *ngIf="isFeatureEnable('time_management')">
                    <a href="javascript:void(0)" [routerLinkActive]="'active'" [routerLink]="['/company-admin/time-management/'+employerId]" class="last-menu">
                        <div class="sidebar-icon"> <svg><use xlink:href="#timeManagement"></use></svg> </div>
                        <span class="nav-label">Time Management</span>
                    </a>
                </li>
                <li *ngIf="isFeatureEnable('toolboxtalks')">
                    <a href="javascript:void(0)" [routerLinkActive]="'active'" [routerLink]="['/company-admin/toolbox-talks/'+employerId]" class="last-menu">
                        <div class="sidebar-icon"> <svg><use xlink:href="#toolboxTalks"></use></svg> </div>
                        <span class="nav-label">Toolbox Talks</span>
                    </a>
                </li>
                <li *ngIf="isFeatureEnable('incident_reports')">
                    <a href="javascript:void(0)" [routerLinkActive]="'active'" [routerLink]="['/company-admin/incident-reports/'+employerId]" class="last-menu">
                        <div class="sidebar-icon"> <svg><use xlink:href="#incidentReport"></use></svg> </div>
                        <span class="nav-label">Incidents</span>
                    </a>
                </li>
                <li>
                    <a href="javascript:void(0)" [routerLinkActive]="'active'" [routerLink]="['/company-admin/conduct-cards/'+employerId]" class="last-menu">
                        <div class="sidebar-icon"> <img [src]="sidebarIcons.conductCards" /></div>
                        <span class="nav-label">Conduct Cards</span></a>
                </li>
                <li *ngIf="isFeatureEnable('e_learning')">
                    <a href="javascript:void(0)" [routerLinkActive]="'active'" [routerLink]="['/company-admin/e-learning/'+employerId]" class="last-menu">
                        <div class="sidebar-icon"> <svg><use xlink:href="#eLearning"></use></svg> </div>
                        <span class="nav-label">E-Learning</span>
                    </a>
                </li>
                <li *ngIf="isFeatureEnable('company_messaging')">
                    <a href="javascript:void(0)" [routerLinkActive]="'active'" [routerLink]="['/company-admin/company-messaging/'+employerId]" class="last-menu">
                        <div class="sidebar-icon"> <svg><use [attr.xlink:href]="sidebarIcons.messages"></use></svg> </div>
                        <span class="nav-label">Company Messaging</span>
                    </a>
                </li>
                <li>
                    <a href="javascript:void(0)" [routerLinkActive]="'active'" [routerLink]="['/company-admin/employer-project/'+employerId]" class="last-menu collapsed"
                       [attr.data-toggle]="(companyProjects && companyProjects.length) ? 'collapse': undefined"
                       [attr.data-target]="(companyProjects && companyProjects.length) ? '#projectList': undefined" >
                        <div class="sidebar-icon"> <svg><use xlink:href="#projects"></use></svg> </div>
                        <span class="nav-label">Projects</span>
                    </a>
                    <ul class="collapse show" [hidden]="isDashboard"  *ngIf="selectedCompanyProject && selectedCompanyProject.id">
                        <li>
                            <a class="collapsed cursor-pointer hover-none" style="padding-left: 15px;" [attr.data-target]="'#subProjectList' + selectedCompanyProject.id" >{{ selectedCompanyProject.name }}</a>
                            <ul [ngClass]="{'sub-menu small collapse': true, 'show': isChildRoute('subProjectList', selectedCompanyProject.id)}" [id]="'subProjectList' + selectedCompanyProject.id ">
                                <li class="companySubMenu pb-3 mr-1" [ngStyle]="{'max-height': 'calc('+ heightAdjustment +')'}">
                                    <a *ngIf="is_inherited_project" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/dashboard/'+ employerId + '/live/'+ selectedCompanyProject.id]">Live Dashboard</a>
                                    <a *ngIf="is_inherited_project" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/dashboard/'+ employerId + '/view/'+ selectedCompanyProject.id]">Project Dashboard</a>
                                    <a *ngIf="is_inherited_project" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/inductions/'+ employerId + '/view/'+ selectedCompanyProject.id]">{{ selectedCompanyProject.custom_field.induction_phrase }}</a>
                                    <a *ngIf="hasTimeManagementAccess" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-time-management/'+ employerId + '/time-logs/'+ selectedCompanyProject.id]">Time Management</a>
                                    <a *ngIf="hasSiteMessaging" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-site-messaging/' + selectedCompanyProject.id + '/' + employerId]">Site Messaging</a>
                                    <a *ngIf="hasProgressPhotosAccess" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-progress-photos/' + selectedCompanyProject.id + '/' + employerId]">Progress Photos</a>
                                    <a *ngIf="hasDeliveryNotesAccess" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-delivery-notes/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.custom_field.dn_phrase }}</a>
                                    <a *ngIf="hasCollectionNotesAccess" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-collection-notes/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.custom_field.cn_phrase }}</a>
                                    <a *ngIf="hasDailyActivities" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-daily-activities/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.custom_field.da_phrase }}</a>
                                    <a *ngIf="hasToolboxTalksAccess" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-toolbox-talks/' + selectedCompanyProject.id + '/' + employerId]">Toolbox Talks</a>
                                    <a *ngIf="hasPermitAccess" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/permit/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.custom_field.permit_phrase }}</a>
                                    <a *ngIf="hasGoodCalls" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-good-calls/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.custom_field.gc_phrase }}</a>
                                    <a *ngIf="hasObservations" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-observations/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.custom_field.obrs_phrase }}</a>
                                    <a *ngIf="hasCloseCallAccess" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/close-call/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.custom_field.cc_phrase }}</a>
                                    <a *ngIf="hasClerkOfWorksAccess" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/clerk-of-works/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.cow_setting.cow_phrase }}</a>
                                    <a *ngIf="hasPowraAccess" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-powra/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.custom_field.powra_phrase }}</a>
                                    <a *ngIf="hasIncidentReportAccess" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-incident-reports/' + selectedCompanyProject.id + '/' + employerId]">Incident Reports</a>
                                    <a *ngIf="hasInspections && hasInspectionTour" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-inspections/' + selectedCompanyProject.id + '/' + employerId]">Inspections</a>
                                    <a *ngIf="!hasInspectionTour && sectionAccess.ib_checklist" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-inspections/' + selectedCompanyProject.id + '/' + employerId]" [queryParams]="{selected_ibcl: selectedCompanyProject.first_ib_id}">Inspections</a>
                                    <a *ngIf="hasTake5s" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-take5s/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.custom_field.take5_phrase }}</a>
                                    <a *ngIf="hasTaskBriefings" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-task-briefings/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.custom_field.tb_phrase }}</a>
                                    <a *ngIf="hasWorkPackagePlan" id="work_package_plan" fragment="work_package_plan" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-work-package-plan/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.custom_field.wpp_phrase }}</a>
                                    <a *ngIf="hasRams" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-rams/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.custom_field.rams_phrase }}</a>
                                    <a *ngIf="hasAssetManagement" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/project-assets/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.custom_field.assets_phrase }}</a>
                                    <a *ngIf="hasQualityChecklist" [routerLinkActive]="['selected']" [routerLink]="['/company-admin/quality-checklists/' + selectedCompanyProject.id + '/' + employerId]">{{ selectedCompanyProject.custom_field.qcl_phrase || "ITPs" }}</a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </li>
            </ul>
            <div *ngIf="(!is_mobile_nav || is_drawer_open) && authUser$.roles.includes('COMPANY_ADMIN')" class="d-flex flex-column companyPortalBtnGroup" role="group">
                <button *ngIf="employerInfo.features_status.add_project" type="button" class="btn btn-primary badge-pill button-bordered mb-2" (click)="openProjectForm()">
                    Add Project
                </button>
                <!--<button type="button" class="btn btn-secondary" (click)="openCompanyAccess(addCompanyModelContent)">Give Access</button>-->
                <button type="button" class="btn btn-primary badge-pill button-bordered" [routerLink]="['/company-admin/' + employerId + '/settings']">Settings</button>
            </div>
            <block-loader [show]="(modalRequestInProgress)"></block-loader>
        </div>
    </aside>
</div>

<i-modal #projectFormPopup title="Project Details" size="lg" rightPrimaryBtnTxt="Save" [showCancel]="false" [rightPrimaryBtnDisabled]="!projectForm.valid" (onClickRightPB)="saveCompanyProject(projectForm, $event)" (onClickRightPB)="closeModal()">
        <form [ngClass]="{'form-container setup-content companyProjectForm': true}" novalidate #projectForm="ngForm">
            <div class="form-group">
                <label>
                    Project Name <small class="required-asterisk ">*</small>
                </label>
                <input type="text" class="form-control" required #name="ngModel" [(ngModel)]="project.name" name="name"
                       placeholder="Project Name"/>
                <div class="alert alert-danger mt-1" [hidden]="!(name.errors && name.errors.required)">Project Name is required</div>
                <div class="alert alert-danger mt-1" [hidden]="!(name.errors && name.errors.pattern)">Project Name should not include non-ascii characters.</div>
            </div>

            <div class="form-group">
                <label>
                    Project/Contract Number <small class="required-asterisk ">*</small>
                </label>
                <input type="text" class="form-control" required #project_number="ngModel" [(ngModel)]="project.project_number" name="project_number"
                       placeholder="Project/Contract Number" />
                <div class="alert alert-danger" [hidden]="(project_number.valid)">Project/Contract Number is required.</div>
            </div>

            <div class="form-group" *ngIf="postcodeInput?.type === 'postcode-lookup'">
                <label>
                    Project post code <small class="required-asterisk ">*</small>
                </label>
                <div>
                    <input type="text" class="form-control inline-input" required minlength="4" #post_code="ngModel" [(ngModel)]="project.postcode" name="post_code"
                           placeholder="Project post code" (focusout)="project.postcode.trim().length > 3 && validatePostcode(post_code)" />
                    <project-geofence-modal-box (geofence)="onSaveGeofenceData($event)" class="geofence-icon"></project-geofence-modal-box>
                </div>
                <div class="alert alert-danger" *ngIf="post_code.errors?.required && (post_code.touched || post_code.dirty)">{{(project?.custom_field?.country_code === 'US') ? 'Zip code' : 'Postcode'}} is required</div>
                <div class="alert alert-danger" [hidden]="!(post_code.errors && post_code.errors.valid)">Invalid {{(project?.custom_field?.country_code === 'US') ? 'Zip code' : 'Postcode'}}.</div>
            </div>

            <ng-container *ngIf="postcodeInput?.type === 'address-lookup'">
                <div class="form-group">
                    <label>
                        Project location <small class="required-asterisk ">*</small>
                    </label>
                    <project-geofence-modal-box (geofence)="onSaveGeofenceData($event)" class="geofence-icon"></project-geofence-modal-box>
                    <div class="row">
                        <div class="col-6 form-group">
                            <label>
                                Latitude
                            </label>
                            <input type="number" class="form-control inline-input" required #lat="ngModel" [(ngModel)]="project.custom_field.location.lat" name="lat"
                                   placeholder="Enter Latitude" pattern="^-?(?:90(?:\.0+)?|(?:[1-8]?[0-9])(?:\.\d+)?)$"/>
                        </div>
                        <div class="col-6 form-group">
                            <label>
                                Longitude
                            </label>
                            <input type="number" class="form-control inline-input" required #long="ngModel" [(ngModel)]="project.custom_field.location.long" name="long"
                                   placeholder="Enter Longitude" pattern="^-?(?:180(?:\.0+)?|(?:1[0-7][0-9]|[1-9]?[0-9])(?:\.\d+)?)$"/>
                        </div>
                    </div>
                    <div class="alert alert-danger mt-1" *ngIf="(lat.invalid || long.invalid) && (lat.touched || lat.dirty || long.touched || long.dirty)">
                        Project location is required
                    </div>
                    <div class="alert alert-danger mt-1" [hidden]="!lat.errors?.pattern">Enter a valid latitude (-90 to 90).</div>
                    <div class="alert alert-danger mt-1" [hidden]="!long.errors?.pattern">Enter a valid longitude (-180 to 180).</div>
                </div>
                <div class="form-group">
                    <label>
                        Address <small class="required-asterisk ">*</small>
                    </label>
                    <input type="text" class="form-control inline-input" required #address="ngModel" [(ngModel)]="project.custom_field.location.region" name="address"
                           placeholder="Address"/>
                    <div class="alert alert-danger mt-1" [hidden]="!(address.errors && address.errors.required)">Address is required</div>
                </div>
            </ng-container>

            <div class="form-group">
                <label>Timezone <small class="required-asterisk">*</small></label>
                <ng-select [items]="availableTimeZones"
                    bindLabel="name"
                    bindValue="name" required
                    placeholder="Choose Timezone"
                    name="timezone"
                    [(ngModel)]="project.custom_field.timezone"
                    #projectTimeZone="ngModel">
                </ng-select>
                <div class="alert alert-danger" [hidden]="!(projectTimeZone.errors && projectTimeZone.errors.required)">Project timezone is required</div>
            </div>

            <div class="form-group">
                <label>
                    innTime login PIN <small class="required-asterisk ">*</small>
                </label>
                <input type="text" pattern="\d*" minlength="6" maxlength="8" class="form-control" required #pin="ngModel" [(ngModel)]="project.pin" name="pin"
                       placeholder="Project pin" />
                <div class="alert alert-danger" [hidden]="!(pin.errors && pin.errors.required)">Project PIN is required.</div>
                <div class="alert alert-danger" [hidden]="!(pin.errors && pin.errors.pattern)">Project PIN must be a number</div>
                <div class="alert alert-danger" [hidden]="!(pin.errors && pin.errors.minlength)">Project PIN minimum length must be 6 digits</div>
            </div>

            <div class="form-group">
                <label>Client</label>
                <input type="text" class="form-control" [(ngModel)]="project.client" name="client"
                       placeholder="Enter Client" />
            </div>

            <div class="form-group">
                <label>
                    Main Contact Name <small class="required-asterisk ">*</small>
                </label>
                <input type="text" class="form-control" required #pContactName="ngModel" [(ngModel)]="project.main_contact_name" name="pContactName"
                       placeholder="Enter Main Contact Name" />
                <div class="alert alert-danger" [hidden]="(pContactName.valid)">Main Contact Name is required</div>
            </div>

            <div class="form-group">
                <label>
                    Main Contact Number <small class="required-asterisk ">*</small>
                </label>
                <app-country-code-contact-input [contactData]="project"
                            [name]="'main_contact_number_obj'" [isRequired]="true"
                            [errorMessageTitle]="'Main Contact Number'" [defaultCountryCode]="this.employerInfo.country_code">
                </app-country-code-contact-input>
            </div>

            <div class="form-group">
                <label>Description</label>
                <textarea name="description" [(ngModel)]="project.description" class="form-control" placeholder="Your project description (Optional)"></textarea>
            </div>

            <div class="form-group">
                <label>Start Date <small class="required-asterisk ">*</small></label>
                <div class="input-group col-sm-8">
                    <input class="form-control" placeholder="dd-mm-yyyy" readonly
                           name="start_date" [(ngModel)]="project.start_date" ngbDatepicker
                           #sd="ngbDatepicker" ng-value="project.start_date" required>
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary calendar" (click)="sd.toggle()" type="button">
                            <i class="fa fa-calendar"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>End Date <small class="required-asterisk ">*</small></label>
                <div class="input-group col-sm-8">
                    <input class="form-control" placeholder="dd-mm-yyyy" readonly
                           name="end_date" [(ngModel)]="project.end_date" ngbDatepicker
                           #ed="ngbDatepicker" ng-value="project.end_date" required>
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary calendar" (click)="ed.toggle()" type="button">
                            <i class="fa fa-calendar"></i>
                        </button>
                    </div>
                </div>
                <div *ngIf="!isEndDateValid" class="alert alert-danger">
                    End date must be later than start date.
                </div>
            </div>

            <div class="form-group">
                <label>Project Value <small class="required-asterisk ">*</small></label>
                <ng-select [items]="PROJECT_VALUE" bindLabel="value" bindValue="key" placeholder="Project Value" [(ngModel)]="project.value" name="value" class="form-control" required ng-value="project.value">
                </ng-select>
            </div>

            <div class="form-group">
                <label>Project Type (list as per main project setup) <small class="required-asterisk ">*</small></label>
                <ng-select [items]="PROJECT_TYPES" bindLabel="value" bindValue="key" placeholder="Project Type" [(ngModel)]="project.project_type" name="project_type" class="form-control" required ng-value="project.project_type">
                </ng-select>
            </div>

            <div class="form-group">
                <label>Type of Works</label>
                <ng-select [items]="knownTypeOfWorks"
                           [multiple]="true"
                           bindLabel="name"
                           bindValue="name"
                           placeholder="Type of Works"
                           name="type_of_works"
                           [(ngModel)]="project.type_of_works"
                           #competencyName="ngModel">
                </ng-select>
            </div>

            <div class="form-group">
                <label>Contract Type</label>
                <input type="text" class="form-control" truncate [(ngModel)]="project.main_contract_type" name="main_contract_type"
                       placeholder="Enter Main Contract Type" />
            </div>

            <div class="form-group">
                <label>Designer</label>
                <input type="text" class="form-control" truncate [(ngModel)]="project.designer" name="designer"
                       placeholder="Enter Designer" />
            </div>

            <div class="form-group">
                <label>Stakeholder</label>
                <input type="text" class="form-control" truncate [(ngModel)]="project.stakeholder" name="stakeholder"
                       placeholder="Enter Stakeholder" />
            </div>

            <div class="form-group">
                <label class="col-md-12 pl-0">Use the tick boxes to select the project features</label>
                <table>
                    <colgroup>
                        <col style="width: 33%" />
                        <col style="width: 33%" />
                        <col style="width: 33%" />
                    </colgroup>
                    <tr>
                        <td [ngbTooltip]="projectFeaturePermission.close_calls === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.close_calls)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.close_calls"
                                       [checked]="project.company_additional_project_section_access.close_calls"
                                       [disabled]="disableToolStatus(projectFeaturePermission.close_calls)"
                                       name="cc"
                                       id="cc">
                                <label class="custom-control-label" for="cc"><h6>{{project.custom_field.cc_phrase}}</h6>
                                </label>
                                <i class="fas fa-cog ml-1 cowSetting" (click)="altPhraseCcModal()"></i>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.clerk_of_works === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.clerk_of_works)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block position-static">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.clerk_of_works"
                                       [checked]="project.company_additional_project_section_access.clerk_of_works"
                                       [disabled]="disableToolStatus(projectFeaturePermission.clerk_of_works)"
                                       name="cow"
                                       id="cow">
                                <label class="custom-control-label" for="cow">
                                    <h6>{{ project.cow_setting.cow_phrase }}</h6>
                                </label>
                                <div ngbDropdown [autoClose]="'outside'" class="d-inline-block mr-1"
                                     placement="bottom-right" #dropDown2="ngbDropdown">
                                    <i class="fas fa-cog ml-1 dropdown-toggle-no-caret cowSetting" ngbDropdownToggle
                                       placement="bottom-left" data-toggle="dropdown" aria-haspopup="true"
                                       aria-expanded="false" id="dropdownMenu2"></i>
                                    <div ngbDropdownMenu class="dropdown-menu" aria-labelledby="dropdownMenu2">
                                        <button class="dropdown-item cursor-pointer" (click)="projectSiteConfigModal()"
                                                type="button">Configuration
                                        </button>
                                        <button class="dropdown-item cursor-pointer" (click)="tagOwnersModal()"
                                                type="button">Tag Owners
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.collection_notes === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.collection_notes)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.collection_notes"
                                       [checked]="project.company_additional_project_section_access.collection_notes"
                                       [disabled]="disableToolStatus(projectFeaturePermission.collection_notes)"
                                       name="cn"
                                       id="cn">
                                <label class="custom-control-label" for="cn"><h6>{{project.custom_field.cn_phrase}}</h6>
                                </label>
                                <i class="fas fa-cog ml-1 cowSetting" (click)="altPhraseCnModal()"></i>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td [ngbTooltip]="projectFeaturePermission.daily_activities === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.daily_activities)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.daily_activities"
                                       [checked]="project.company_additional_project_section_access.daily_activities"
                                       [disabled]="disableToolStatus(projectFeaturePermission.daily_activities)"
                                       name="da"
                                       id="da">
                                <label class="custom-control-label" for="da"><h6>{{project.custom_field.da_phrase}}</h6>
                                </label>
                                <i class="fas fa-cog ml-1 cowSetting" (click)="altPhraseDaModal()"></i>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.delivery_notes === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.delivery_notes)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.delivery_notes"
                                       [checked]="project.company_additional_project_section_access.delivery_notes"
                                       [disabled]="disableToolStatus(projectFeaturePermission.delivery_notes)"
                                       name="dn"
                                       id="dn">
                                <label class="custom-control-label" for="dn"><h6>{{project.custom_field.dn_phrase}}</h6>
                                </label>
                                <i class="fas fa-cog ml-1 cowSetting" (click)="altPhraseDnModal()"></i>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.good_calls === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.good_calls)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.good_calls"
                                       [checked]="project.company_additional_project_section_access.good_calls"
                                       [disabled]="disableToolStatus(projectFeaturePermission.good_calls)"
                                       name="gc"
                                       id="gc">
                                <label class="custom-control-label" for="gc"><h6>{{project.custom_field.gc_phrase}}</h6>
                                </label>
                                <i class="fas fa-cog ml-1 cowSetting" (click)="altPhraseGcModal()"></i>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td [ngbTooltip]="projectFeaturePermission.incident_report === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.incident_report)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.incident_report)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.incident_report"
                                       [checked]="project.company_additional_project_section_access.incident_report"
                                       [disabled]="disableToolStatus(projectFeaturePermission.incident_report)"
                                       name="ir"
                                       id="ir">
                                <label class="custom-control-label" for="ir"><h6>Incident Report</h6></label>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.ib_checklist === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.ib_checklist)" container="body">
                            <div class="custom-checkbox d-inline-block c-config" [class.disable-tool]="disableToolStatus(projectFeaturePermission.ib_checklist)">
                                <inspections-modal-box
                                    [project]="project"
                                    [section_access_key]="'company_additional_project_section_access'"
                                    [settingIcon]="'fa-cog'"
                                    [inspections_admin]="projectFeaturePermission.ib_checklist"
                                ></inspections-modal-box>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.observations === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.observations)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.observations"
                                       [checked]="project.company_additional_project_section_access.observations"
                                       [disabled]="disableToolStatus(projectFeaturePermission.observations)"
                                       name="obrs"
                                       id="obrs">
                                <label class="custom-control-label" for="obrs">
                                    <h6>{{project.custom_field.obrs_phrase}}</h6></label>
                                <i class="fas fa-cog ml-1 cowSetting" (click)="altPhraseObrsModal()"></i>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td [ngbTooltip]="projectFeaturePermission.progress_photos === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.progress_photos)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.progress_photos)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.progress_photos"
                                       [checked]="project.company_additional_project_section_access.progress_photos"
                                       [disabled]="disableToolStatus(projectFeaturePermission.progress_photos)"
                                       name="pps"
                                       id="pps">
                                <label class="custom-control-label" for="pps"><h6>Progress Photos</h6></label>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.powra === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.powra)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.powra"
                                       [checked]="project.company_additional_project_section_access.powra"
                                       [disabled]="disableToolStatus(projectFeaturePermission.powra)"
                                       name="powra"
                                       id="powra">
                                <label class="custom-control-label" for="powra">
                                    <h6>{{project.custom_field.powra_phrase}}</h6></label>
                                <i class="fas fa-cog ml-1 cowSetting" (click)="altPhrasePowraModal()"></i>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.quality_checklist === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.quality_checklist)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.quality_checklist"
                                       [checked]="project.company_additional_project_section_access.quality_checklist"
                                       [disabled]="disableToolStatus(projectFeaturePermission.quality_checklist)"
                                       name="qcl"
                                       id="qcl">
                                <label class="custom-control-label" for="qcl">
                                    <h6>{{project.custom_field.qcl_phrase}}</h6></label>
                                <i class="fas fa-cog ml-1 cowSetting" (click)="altPhraseQclModal()"></i>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td [ngbTooltip]="projectFeaturePermission.rams === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.rams)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.rams"
                                       [checked]="project.company_additional_project_section_access.rams"
                                       [disabled]="disableToolStatus(projectFeaturePermission.rams)"
                                       name="rams"
                                       id="rams">
                                <label class="custom-control-label" for="rams">
                                    <h6>{{project.custom_field.rams_phrase}}</h6></label>
                                <i class="fas fa-cog ml-1 cowSetting" (click)="altPhraseRamsModal()"></i>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.take_5s === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.take_5s)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.take_5s"
                                       [checked]="project.company_additional_project_section_access.take_5s"
                                       [disabled]="disableToolStatus(projectFeaturePermission.take_5s)"
                                       name="t5s"
                                       id="t5s">
                                <label class="custom-control-label" for="t5s">
                                    <h6>{{project.custom_field.take5_phrase}}</h6></label>
                                <i class="fas fa-cog ml-1 cowSetting" (click)="altPhraseT5sModal()"></i>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.task_briefings === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.task_briefings)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.task_briefings"
                                       [checked]="project.company_additional_project_section_access.task_briefings"
                                       [disabled]="disableToolStatus(projectFeaturePermission.task_briefings)"
                                       name="tbs"
                                       id="tbs">
                                <label class="custom-control-label" for="tbs">
                                    <h6>{{project.custom_field.tb_phrase}}</h6></label>
                                <i class="fas fa-cog ml-1 cowSetting" (click)="altPhraseTbModal()"></i>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td [ngbTooltip]="projectFeaturePermission.toolbox_talks === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.toolbox_talks)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block" [class.disable-tool]="disableToolStatus(projectFeaturePermission.toolbox_talks)">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.toolbox_talks"
                                       [checked]="project.company_additional_project_section_access.toolbox_talks"
                                       [disabled]="disableToolStatus(projectFeaturePermission.toolbox_talks)"
                                       name="tt"
                                       id="tt">
                                <label class="custom-control-label" for="tt"><h6>Toolbox Talks</h6></label>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.work_package_plan === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.work_package_plan)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.work_package_plan"
                                       [checked]="project.company_additional_project_section_access.work_package_plan"
                                       [disabled]="disableToolStatus(projectFeaturePermission.work_package_plan)"
                                       name="wpss"
                                       id="wpss">
                                <label class="custom-control-label" for="wpss">
                                    <h6>{{project.custom_field.wpp_phrase}}</h6></label>
                                <i class="fas fa-cog ml-1 cowSetting" (click)="altPhraseWppModal()"></i>
                            </div>
                        </td>
                        <td [ngbTooltip]="projectFeaturePermission.permit === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" placement="left-top" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.permit)" container="body">
                            <div class="custom-control custom-checkbox d-inline-block">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.company_additional_project_section_access.permit"
                                       [checked]="project.company_additional_project_section_access.permit"
                                       [disabled]="disableToolStatus(projectFeaturePermission.permit)"
                                       name="permit"
                                       id="permit">
                                <label class="custom-control-label" for="permit">
                                    <h6>{{project.custom_field.permit_phrase}}</h6></label>
                                <i class="fas fa-cog ml-1 cowSetting" (click)="altPhrasePermitModal()"></i>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        </form>
    <block-loader [show]="(addProjectRequestInProgress)"></block-loader>
</i-modal>

<ng-template #dashboardHtml let-c="close" let-d="dismiss">
    <div class="modal-header border-bottom-0 d-block pt-2 pb-0 pl-1 px-0">
        <div class="col-md-12 p-0 d-inline-block" style="margin-top: 4px;">
                <div ngbDropdown class="mr-2 float-left pl-1" *ngIf="currentDashboardFeature === 'inspection-tour' || currentDashboardFeature === 'subcontractor'">
                    <button style="width: 270px;" class="btn btn-sm dropdownBtnMakeup" id="dashboardTypeDD" ngbDropdownToggle>
                        <i class="fas fa-chart-line mt-1 ml-1 float-left"></i>
                        <span>{{ currentDashboardFeatureName }}</span>
                        <i class="fa fa-caret-down mt-1 ml-1 float-right" aria-hidden="true"></i>
                    </button>
                    <div ngbDropdownMenu aria-labelledby="dashboardTypeDD" class="dashboardTypeDD">
                        <ng-template ngFor let-item [ngForOf]="dashboardTypes" let-i="index">
                            <button ngbDropdownItem (click)="openDashboardModal(item.value, true)">
                                {{item.name}}
                            </button>
                        </ng-template>
                    </div>
                </div>

                <div class="col-md-9 pr-1 float-right">
                    <button type="button" class="close mr-2 float-right" aria-label="Close" (click)="d('Cross click')">
                        <span aria-hidden="true">&times;</span>
                    </button>

                    <div ngbDropdown class="mr-2 float-right" >
                        <button style="width: 250px;" class="btn btn-sm dropdownBtnMakeup" id="dropdownDlReport1" ngbDropdownToggle>
                            <i class="far fa-calendar"></i>
                            <span>{{ dayjs(this.dashboardReportFrom).format(AppConstant.dateFormat_Do_MMM_YYYY) }} - {{ dayjs(this.dashboardReportTo).format(AppConstant.dateFormat_Do_MMM_YYYY) }}</span>
                            <i class="fa fa-caret-down" aria-hidden="true"></i>
                        </button>
                        <div ngbDropdownMenu aria-labelledby="dropdownDlReport1">
                            <download-report-modal-box [actionBtnText]="'Update Report'" [xlsxOnly]="true" [fromDate]="dashboardStartDate"  (downloadModal)="updateCompanyDashboard($event, false, false, false)"></download-report-modal-box>
                        </div>
                    </div>

                    <div ngbDropdown class="mr-2 float-right" *ngIf="currentDashboardFeature != 'inspection-builder'">
                        <button style="width: 170px;" class="btn btn-sm dropdownBtnMakeup" id="selectProjectDropdown" ngbDropdownToggle>
                            <i *ngIf="!selectedDashboardProjects.length" class="far fa-check-square"></i>
                            <span *ngIf="selectedDashboardProjects.length" >{{ selectedDashboardProjects.length }} Projects Selected</span>
                            <span *ngIf="!selectedDashboardProjects.length" >Select Projects</span>
                            <i class="fa fa-caret-down" aria-hidden="true"></i>
                        </button>
                        <div ngbDropdownMenu aria-labelledby="selectProjectDropdown" class="selectProjectDropdown">
                            <div class="download-report-container">
                                <form novalidate #selectProjectsForm="ngForm">
                                    <div class="form-group px-4">
                                        <label> <b> Select Projects </b> </label>
                                        <ng-select
                                            name="dashboard_projects"
                                            style="width: 100%;"
                                            [multiple]="true"
                                            placeholder="Select Projects"
                                            [items]="projectsToSelect"
                                            [(ngModel)]="selectedDashboardProjects"
                                            [closeOnSelect]="false"
                                            bindLabel="name" bindValue="id">
                                            <ng-template ng-header-tmp>
                                                <button (click)="selectAllProject()" class="btn btn-sm btn-secondary mr-1">Select all</button>
                                                <button (click)="unSelectProjects()" class="btn btn-sm btn-secondary">Unselect all</button>
                                            </ng-template>
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index" let-search="searchTerm">
                                                <input name="item-project-{{index}}" id="item-project-{{index}}" type="checkbox" [ngModel]="item$.selected" class="mr-1"/>
                                                <b>{{item?.name }}</b> <br/>
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                    <div class="form-group px-4 float-right">
                                        <button type="button" class="btn btn-outline-primary pl-2 pr-2 mr-2" (click)="updateCompanyDashboard($event, false, true, false)">Clear</button>
                                        <button type="button" class="btn btn-outline-primary pl-3 pr-3" (click)="updateCompanyDashboard($event, false, false, false)">Update Report</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="customTick custom-control custom-checkbox p-right ml-2 mr-3 float-right mt-2">
                        <input type="checkbox" class="custom-control-input"
                               [(ngModel)]="includeArchivedProject"
                               [checked]="includeArchivedProject"
                               name="include_archived_project" (change)="updateCompanyDashboard($event, true, false, false)"
                               id="include_archived_project">
                        <label class="custom-control-label" for="include_archived_project">Include archived projects?</label>
                    </div>
                </div>
        </div>
        <div class="col-md-12 p-0 d-inline-block" style="margin-top: 4px;" *ngIf="(currentDashboardFeature === 'subcontractor' || currentDashboardFeature === 'ib-subcontractor') && responsibleCompanies.length">
                <div ngbDropdown class="col-md-7 pl-1 float-left dropdown">
                    <button style="width: 270px;" class="btn btn-sm dropdownBtnMakeup mb-1" id="selectResponsibleCompanies" ngbDropdownToggle>
                        <i *ngIf="!selectedResponsibleCompanies.length" class="far fa-check-square"></i>
                        <span *ngIf="selectedResponsibleCompanies.length" >{{ selectedResponsibleCompanies.length }} Companies Selected</span>
                        <span *ngIf="!selectedResponsibleCompanies.length" >Filter by Company</span>
                        <i class="fa fa-caret-down" aria-hidden="true"></i>
                    </button>
                    <div ngbDropdownMenu aria-labelledby="selectResponsibleCompanies" class="selectResponsibleCompanies">
                        <div class="download-report-container">
                            <form novalidate #selectCompaniesForm="ngForm">
                                <div class="form-group px-4">
                                    <label> <b> Select Companies </b> </label>
                                    <ng-select
                                        name="responsibleCompanies"
                                        style="width: 100%;"
                                        [multiple]="true"
                                        placeholder="Filter by Company"
                                        [items]="responsibleCompanies"
                                        [(ngModel)]="selectedResponsibleCompanies"
                                        [closeOnSelect]="false"
                                        bindLabel="name" bindValue="id">
                                        <ng-template ng-header-tmp>
                                            <button (click)="selectAllResponsibleCompanies()" class="btn btn-sm btn-secondary mr-1">Select all</button>
                                            <button (click)="unSelectAllResponsibleCompanies()" class="btn btn-sm btn-secondary">Unselect all</button>
                                        </ng-template>
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index" let-search="searchTerm">
                                            <input name="item-project-{{index}}" id="item-company-{{index}}" type="checkbox" [ngModel]="item$.selected" class="mr-1"/>
                                            <b>{{item?.name }}</b> <br/>
                                        </ng-template>
                                    </ng-select>
                                </div>
                                <div class="form-group px-4 float-right">
                                    <button type="button" class="btn btn-outline-primary pl-2 pr-2 mr-2" (click)="updateCompanyDashboard($event, false, false, true)">Clear</button>
                                    <button type="button" class="btn btn-outline-primary pl-3 pr-3" (click)="updateCompanyDashboard($event, false, false, false)">Update Report</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div ngbDropdown class="d-inline-block ml-3" style="z-index: 999;" placement="bottom-right">
                        <button style="width: 270px;" class="btn btn-sm dropdownBtnMakeup mb-1" id="taggedOwnerDashboard" ngbDropdownToggle> <i class="far fa-calendar-alt"></i>
                            Subcontractor Report <i class="fas fa-caret-down"></i></button>
                        <div ngbDropdownMenu aria-labelledby="dropdownDlReport1">
                            <div class="p-0 m-3"><b>Apply filters to the download</b></div>
                            <div class="col-6 p-0 m-3">
                                <span> <b> Choose Company: </b> </span>
                                <ng-select
                                    class="d-inline-block rounded-select filter-select w-100"
                                    #taggedOwner
                                    (change)="changeTaggedOwner($event, taggedOwner)"
                                    placeholder="Choose Company">
                                    <ng-option *ngFor="let company of responsibleCompanies" [value]="company" >{{company.name}}</ng-option>
                                </ng-select>
                            </div>

                            <span class="col-md-12 p-0 ml-3"> <b> Sort Report by: </b> </span>
                            <div class="col-12 pb-2 form-inline">
                                <div class="custom-control custom-radio mr-3">
                                    <input type="radio" class="custom-control-input" name="rmc" id="rmc-date"
                                           required [(ngModel)]="sort_items_by"  [value]="'date'" />
                                    <label class="custom-control-label" for="rmc-date"> Date</label>
                                </div>
                                <div class="custom-control custom-radio">
                                    <input type="radio" class="custom-control-input" name="rmc" id="rmc-rating"
                                           required [(ngModel)]="sort_items_by"  [value]="'rating'" />
                                    <label class="custom-control-label" for="rmc-rating"> Rating </label>
                                </div>
                            </div>

                            <span class="col-md-12 p-0 ml-3"> <b> Choose Rating/s: </b> </span>
                            <div class="col-12 pb-2 form-inline" *ngIf="isNormalProjectDashboard && currentDashboardFeature === 'subcontractor'">
                                <div class="custom-control custom-checkbox mr-3">
                                    <input class="custom-control-input" [(ngModel)]="normalProjectsInspectionTourRating.yes" type="checkbox" name="it_yes" id="it_yes">
                                    <label class="custom-control-label greenText" for="it_yes"><span class="font-weight-bold">Yes</span></label>
                                </div>
                                <div class="custom-control custom-checkbox mr-3">
                                    <input class="custom-control-input" [(ngModel)]="normalProjectsInspectionTourRating.no" type="checkbox" name="it_no" id="it_no">
                                    <label class="custom-control-label redText" for="it_no"><span class="font-weight-bold">No</span></label>
                                </div>
                            </div>

                            <div class="col-12 pb-2 form-inline" *ngIf="isIndustrialProjectDashboard && currentDashboardFeature === 'subcontractor'">
                                <div class="custom-control custom-checkbox mr-3">
                                    <input class="custom-control-input" [(ngModel)]="industrialProjectsInspectionTourRating.good" type="checkbox" name="it_good" id="it_good">
                                    <label class="custom-control-label greenText" for="it_good"><span class="font-weight-bold">Good</span></label>
                                </div>
                                <div class="custom-control custom-checkbox mr-3">
                                    <input class="custom-control-input" [(ngModel)]="industrialProjectsInspectionTourRating.fair" type="checkbox" name="it_fair" id="it_fair">
                                    <label class="custom-control-label yellowText" for="it_fair"><span class="font-weight-bold">Fair</span></label>
                                </div>
                                <div class="custom-control custom-checkbox mr-3">
                                    <input class="custom-control-input" [(ngModel)]="industrialProjectsInspectionTourRating.poor" type="checkbox" name="it_poor" id="it_poor">
                                    <label class="custom-control-label redText" for="it_poor"><span class="font-weight-bold">Poor</span></label>
                                </div>
                            </div>

                            <div class="col-12 pb-2 form-inline" *ngIf="currentDashboardFeature === 'ib-subcontractor'">
                                <ng-template ngFor let-value [ngForOf]="inspectionBuilderInfo.scoring_system.values" let-i="index">
                                     <div class="custom-control custom-checkbox mr-3">
                                        <input class="custom-control-input" [(ngModel)]="ib_scoring_system[value]" type="checkbox" [name]="value" [id]="value">
                                        <label  [class]="'custom-control-label '+rating_color[i]" [for]="value"><span class="font-weight-bold">{{value}}</span></label>
                                    </div>
                                </ng-template>
                            </div>

                            <download-report-modal-box [isValid]="!!(selectedTaggedOwner && selectedTaggedOwner.id)" xlsxOnly="true" (downloadModal)="downloadTaggedOwnerDashboard($event)"></download-report-modal-box>
                        </div>
                    </div>
                </div>
        </div>
    </div>
    <div class="modal-body p-1">

        <ngx-skeleton-loader *ngIf="loadingDashboard" count="1" [theme]="{'border-radius': '0', height: '550px'}"></ngx-skeleton-loader>
        <div [innerHtml]="dashboardHtmlContent" *ngIf="!loadingDashboard"></div>
    </div>
    <div class="modal-footer d-inline-block" style="border: none;">
        <button title="Download Participants List" class="btn btn-outline-primary left"
                (click)="downloadParticipantsList()" *ngIf="currentDashboardFeature === 'inspection-tour' || currentDashboardFeature === 'subcontractor' || currentDashboardFeature === 'inspection-builder'">Download Participants List</button>
        <button type="button" class="btn btn-outline-primary float-right" (click)="d('Cross click')">OK</button>
        <button title="Download Dashboard" class="btn btn-outline-success float-right"
                (click)="downloadDashboard()">Download</button>
    </div>
</ng-template>

<power-bi-dashboard *ngIf="loadPowerBiComponent" (powerBiDashboardClose)="powerBiDashboardClose()" [modalTitle]="biModalTitle" [toolLabel]="biToolLabel" [toolName]="biToolName" [portalType]="'company'"></power-bi-dashboard>

<block-loader [show]="(dashboardLoader)" [showBackdrop]="true" alwaysInCenter="true"></block-loader>

<company-site-config-modal
    #projectSiteConfigPopup
    [project]="project">
</company-site-config-modal>

<cow-owener-tagging-modal
    #cowOwenerTaggingPopup
    [project]="project">
</cow-owener-tagging-modal>

<alternative-phrase-setting
    #altPhraseTb [project]="project"
    [defaultPhrase]="'Task Briefings'" [feature]="'task_briefings'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseWpp [project]="project"
    [defaultPhrase]="'Work Package Plans'" [feature]="'work_package_plan'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhrasePermit [project]="project"
    [defaultPhrase]="'Permits'" [feature]="'permit'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseRams [project]="project"
    [defaultPhrase]="'RAMS'" [feature]="'rams'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseQcl [project]="project"
    [defaultPhrase]="'ITPs'" [feature]="'quality_checklist'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseT5s [project]="project"
    [defaultPhrase]="'Take 5s'" [feature]="'take_5s'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseDa [project]="project"
    [defaultPhrase]="'Daily Activities'" [feature]="'daily_activities'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseCn [project]="project"
    [defaultPhrase]="'Collection Notes'" [feature]="'collection_notes'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseDn [project]="project"
    [defaultPhrase]="'Delivery Notes'" [feature]="'delivery_notes'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseGc [project]="project"
    [defaultPhrase]="'Good Calls'" [feature]="'good_calls'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseObrs [project]="project"
    [defaultPhrase]="'Observations'" [feature]="'observations'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhrasePowra [project]="project"
    [defaultPhrase]="'POWRA'" [feature]="'powra'">
</alternative-phrase-setting>

<alternative-phrase-setting
    #altPhraseCc [project]="project"
    [defaultPhrase]="'Close Calls'" [feature]="'close_calls'">
</alternative-phrase-setting>

<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

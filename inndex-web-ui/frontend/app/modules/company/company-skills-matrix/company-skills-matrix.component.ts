import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from "@angular/core";
import {
    ResourceService,
    SKILLS_MATRIX_RULE_TYPE,
    SkillsMatrixRule,
    SkillMatrixService,
    UserService,
    ToastService,
    SkillsMatrixPage,
    SkillRuleEntry,
    SkillsMatrixRow,
    ProjectService
} from "@app/core";
import {ActionBtnEntry, AssetsUrl, GenericConfirmationModalComponent, IModalComponent} from "@app/shared";
import * as dayjs from "dayjs";
import {concat, forkJoin, Observable, of, Subject} from "rxjs";
import {AppConstant} from "@env/environment";
import {DatatableComponent} from "@swimlane/ngx-datatable";
import {catchError, debounceTime, distinctUntilChanged, filter, map, switchMap, tap} from 'rxjs/operators';
import {innDexConstant} from '@env/constants';
import {NgSelectComponent} from '@ng-select/ng-select';

interface ExemptedUser {
    id: number;
    name: string;
    email?: string;
    disabled?: boolean;
}

@Component({
    selector: 'company-skills-matrix-setting',
    templateUrl: './company-skills-matrix.component.html',
    styleUrls: ['./company-skills-matrix.component.scss']
})

export class CompanySkillsMatrixComponent implements OnInit {

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    @ViewChild('addModalHtml') private addModal: IModalComponent;
    @ViewChild('vModalHtml') private vModalHtml: IModalComponent;
    @ViewChild('manageExemptionModalRef') private manageExemptionModalRef: IModalComponent;

    @Input()
    skills_matrix: boolean = false;

    @Input()
    employer: any;

    @Output()
    featureStatusChanged: EventEmitter<boolean> = new EventEmitter<boolean>();

    rulesPage: SkillsMatrixPage = new SkillsMatrixPage();
    filter: {
        searchText?: string;
    } = {};

    metaInfoFetched: boolean = false;
    allowedJobRoles: Array<any> = [];
    allCompetencies: Array<any> = [];
    tempCompetencies: Array<any> = [];
    rowButtonGroup: Array<ActionBtnEntry> = [
        {
            key: 'view',
            label: '',
            title: 'View',
            mat_icon: 'info',
            // btn_classes: 'btn-action',
        },
        {
            key: 'edit',
            label: '',
            title: 'Edit',
            mat_icon: 'edit_note',
            // btn_classes: 'btn-action',
            // icon_placement: 'right',
        },
        {
            key: 'delete',
            label: '',
            title: 'Delete',
            mat_icon: 'delete',
            btn_classes: 'btn-action1 text-danger1',
        }
    ];
    hasMatrixJobRoles: boolean = false;
    totalCount: number = 0;
    topButtonGroup: Array<any> = [
        {
            code: 'add',
            name: `Add new job role`,
            iconClass: 'material-symbols-outlined',
            iconClassLabel: 'add',
            enabled: true,
        },
        {
            code: 'exempt',
            name: `Exemption List`,
            iconClass: 'material-symbols-outlined',
            iconClassLabel: 'person_remove',
            enabled: true,
        },
    ];
    manageExemptionModal: {
        ready: boolean;
        exempted_users: ExemptedUser[],
        filtered_list: ExemptedUser[],
        selection?: number;
    } = {
        ready: false,
        exempted_users: [],
        filtered_list: [],
    };

    constructor(
        private toastService: ToastService,
        private skillMatrixService: SkillMatrixService,
        private projectService: ProjectService,
        private resourceService: ResourceService,
    ) {
    }

    SKILL_MATRIX_EXEMPTION_CONFIG: string = `skill_matrix_exemption_config`;
    viewProcessing: boolean = false;
    pagePlaceholderIcon: string = AssetsUrl.pagePlaceholder.companySkills;
    pageIntroLines: string[] = [`A competency matrix maps the skills and certifications needed for each job role. If switched on, only job roles listed here can be selected by workers.`, `You can build out your competency matrix fully before switching on this feature.`];
    modalProcessing: boolean = false;
    modalInfo: any = {
        ready: false,
        usedDocIds: [],
    };

    create_request: SkillsMatrixRule;

    viewModalInfo: { ready: boolean; title: string; record?: SkillsMatrixRule; } = {
        ready: false,
        title: '',
    };
    ngOnInit() {
        this.loadPage(0, { sortKey: 'job_role_name' }, true);
    }

    @ViewChild('ruleSetTable') ruleSetTable: DatatableComponent;

    loadPage(pageNumber: number = 0, sortInfo: any = {sortKey: 'job_role_name'}, fromInit: boolean = false) {
        this.viewProcessing = true;
        this.skillMatrixService.getCompanySkillMatrixList(this.employer.id, {
            ...sortInfo,
            pageNumber,
            pageSize: this.rulesPage.pageSize,
            q: (this.filter.searchText ? encodeURIComponent(this.filter.searchText) : ''),
        }).subscribe((data) => {
            this.viewProcessing = false;
            if (data.success) {
                if (fromInit) {
                  this.hasMatrixJobRoles = !!data.records.length;
                  this.totalCount = data.totalCount || 0;
                }
                this.rulesPage = data;
                if (data.sortKey) {
                    setTimeout(() => {
                        if (this.ruleSetTable) {
                            this.ruleSetTable.sorts = [{prop: data.sortKey, dir: data.sortDir}];
                        }
                    }, 0);
                }
            } else {
                const message = data.message || 'Failed to fetch rules.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    pageCallback($event) {
        let pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number } = $event;
        this.loadPage(pageInfo.offset, {
            sortKey: this.rulesPage.sortKey,
            sortDir: this.rulesPage.sortDir
        });
    }

    sortCallback({sorts}) {
        let [firstSort] = sorts || [];
        let {dir, prop} = firstSort;
        this.loadPage(0, {sortKey: prop, sortDir: dir});
    }

    searchFilter($event) {
        this.filter.searchText = $event.search.toLowerCase();
        this.loadPage(0);
    }

    onFeatureToggle($event) {
        const status = !this.skills_matrix;
        $event.preventDefault();
        if(status && this.rulesPage.totalCount === 0){
            // enabling. No rule records?
            return this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Warning',
                title: `Please add at least one job role before enabling the Competency Matrix`,
                confirmLabel: 'OK',
                hasCancel: false,
            });
        }
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Confirm',
            title: `Are you sure you want to ${status ? 'enable' : 'disable'} Competency Matrix?`,
            confirmLabel: 'Yes',
            onConfirm: () => {
                // console.log('toggle feature', status);
                this.skills_matrix = status;
                this.featureStatusChanged.emit(status);
            },
            onClose: () => {
                this.skills_matrix = !status;
            }
        });
    }

    private fetchMetaInfo(job_role_ref, onReady = () => {}) {
        this.modalProcessing = true;
        let apiCalls = {
            job_role_response: this.fetchSkillsToAdd(job_role_ref),
            ...(this.metaInfoFetched ? {} : {
                competencies_response: this.getCompetencies(),
            })
        };
        forkJoin(apiCalls).subscribe((responseList: any) => {
            this.modalProcessing = false;
            let errorKey = Object.keys(responseList).find(key => !responseList[key].success);
            if (responseList[errorKey]) {
                const message = responseList[errorKey].message || 'Failed to get data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: responseList[errorKey] });
                return;
            }
            this.allowedJobRoles = responseList.job_role_response.records || [];
            if(!this.metaInfoFetched){
                this.allCompetencies = (responseList.competencies_response.competencieslist || []).filter(c => c && (c.name.toString().toLowerCase() !== 'other') && !c.is_master);
                this.tempCompetencies = [...this.allCompetencies];
            }
            this.metaInfoFetched = true;
            onReady();
        })
    }

    extractExistingEntries(record){
        // console.log('record is', record);
        let list = [];
        if(record.entries && record.entries.length){
            list.push(...record.entries);
        }
        if (record.child && record.child.length) {
            list.push(...this.skillMatrixService.deserializeChildEntries(record.child));
        }
        // console.log('record doc', list);
        this.modalInfo.usedDocIds = list.map(e => (e && e.id) ? e.id : e)
        // console.log('record usedDocIds', this.modalInfo.usedDocIds);

    }

    topBtnClicked({code}) {
        if (code === 'add') {
            return this.openAddModal();
        } else if (code === 'exempt') {
            return this.openExemptionListModal();
        }
    }

    openExemptionListModal() {
        this.manageExemptionModal.exempted_users = [];
        this.modalProcessing = true;
        this.resourceService.getCompanySettingByName(this.SKILL_MATRIX_EXEMPTION_CONFIG, this.employer.id).subscribe((exemption_config: any) => {
            this.modalProcessing = false;
            if(!exemption_config.success){
                const message = exemption_config.message || 'Failed to load exemption list.';
                return this.toastService.show(this.toastService.types.ERROR, message, { data: exemption_config });
            }
            let exempted_users = ((exemption_config.record || {}).value || {}).exempted_users || [];
            this.manageExemptionModal.exempted_users = this.sortItems(exempted_users);
            this.manageExemptionModal.filtered_list = [...this.manageExemptionModal.exempted_users];
            this.manageExemptionModal.ready = true;
            this.manageExemptionModal.selection = null;
            this.manageExemptionModalRef.open();
            this.bindUsersTypeAhead();
        });
    }

    userInput$ = new Subject<string>();
    recordsLoading: boolean = false;
    records$: Observable<ExemptedUser[]>;
    private bindUsersTypeAhead(prefill = []){
        this.records$ = concat(
            of(prefill), // default items
            this.userInput$.pipe(
                filter(Boolean),
                filter(term => innDexConstant.emailRegex.test(term.toString())),
                distinctUntilChanged(),
                debounceTime(400),
                tap(() => this.recordsLoading = true),
                switchMap((term: string) => this.fetchUserInfo(term)),
                map(data => {
                    return (data?.records) || [{id: 0, name: 'No records found', disabled: true}];
                })
            )
        );
    }

    private fetchUserInfo(term: string): Observable<any> {
        return this.projectService.getUserInfo(term, true, {extra: 'email'}).pipe(
            map((data: any) => {
                if(data.success && data.userInfo && data.userInfo.id){
                    if(this.manageExemptionModal.exempted_users.findIndex(u => u.id === data.userInfo.id) > -1){
                        this.toastService.show(this.toastService.types.INFO, `User "${data.userInfo.name}" already exists in exemption list`);
                        data.userInfo.disabled = true;
                    }
                    return {records:[data.userInfo]};
                }
                return data;//(data?.records) || [{name: 'No records found', disabled: true}];
            }),
            catchError(() => of({})), // empty list on error
            tap(() => this.recordsLoading = false)
        );
    }
    trackByFn(item: any) {
        return item.id;
    }
    sortItems(items: ExemptedUser[]): ExemptedUser[] {
        return items.sort((a, b) => a?.name?.localeCompare(b?.name));
    }

    filterUsersList($event){
        const val = $event.target.value.toLowerCase();
        if(!val){
            this.manageExemptionModal.filtered_list = [...this.manageExemptionModal.exempted_users];
            return;
        }

        // filter our data
        this.manageExemptionModal.filtered_list = this.manageExemptionModal.exempted_users.filter((d) => {
            return (
                (d.name && d.name.toLowerCase().indexOf(val) !== -1) ||
                (d.email && d.email.toLowerCase().indexOf(val) !== -1) ||
                (d.id && d.id.toString().toLowerCase().indexOf(val) !== -1) ||
                !val
            );
        });
    }

    onSelection(item: any, selectDropdown$: NgSelectComponent) {
        if(!item){
            return;
        }
        this.manageExemptionModal.exempted_users.push(item);
        this.manageExemptionModal.exempted_users = this.sortItems(this.manageExemptionModal.exempted_users);
        this.manageExemptionModal.filtered_list = [...this.manageExemptionModal.exempted_users];
        selectDropdown$.clearModel();
        this.bindUsersTypeAhead();
        this.toastService.show(this.toastService.types.INFO, `The user has been added to the exemption list below. Click 'Save' to apply the changes.`)
    }

    onSelectionClose(){
        this.bindUsersTypeAhead();
    }

    onRemoveItem(row: any) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Confirm',
            title: `Are you sure you want remove "${row.name}" from Competency Matrix Exemption List?`,
            confirmLabel: 'Yes',
            onConfirm: () => {
                this.manageExemptionModal.exempted_users = this.manageExemptionModal.exempted_users.filter(u => u.id !== row.id);
                this.manageExemptionModal.filtered_list = [...this.manageExemptionModal.exempted_users];
            }
        });
    }

    saveExemptionList({closeFn}){
        this.modalProcessing = true;
        this.resourceService.saveCompanySetting({
            "setting_name": this.SKILL_MATRIX_EXEMPTION_CONFIG,
            "setting_value": {
                exempted_users: this.manageExemptionModal.exempted_users.map(u => u.id || u),
            }
        }, this.employer.id).subscribe(data => {
            this.modalProcessing = false;
            if(!data.success){
                const message = data.message || 'Failed to save Competency Matrix Exemption List.';
                return this.toastService.show(this.toastService.types.ERROR, message, { data });
            }
            closeFn();
            this.toastService.show(this.toastService.types.SUCCESS, `The exemption list has been updated.`)
            this.manageExemptionModal.ready = false;
        });
    }

    openAddModal(rule: any = {}) {
        this.modalInfo.usedDocIds = [];
        this.tempCompetencies = [];
        if (rule.id) {
            // deep copy nested object & array
            this.create_request = JSON.parse(JSON.stringify(rule));
            if(this.create_request.child && this.create_request.child.length === 0){
                this.create_request._competency_optional = true;
            }
            // traverse child to get list of all existing selected Competencies
            this.extractExistingEntries(this.create_request);
        } else {
            this.create_request = new SkillsMatrixRule();
            // this.create_request._id = (new Date()).getTime();
            this.create_request.child = [this.getNewChild()];
        }
        return this.fetchMetaInfo(this.create_request.job_role_ref, () => {
            this.modalInfo.ready = true;
            if(this.modalInfo.usedDocIds && this.modalInfo.usedDocIds.length){
                this.tempCompetencies = this.allCompetencies.map((value: any) => {
                    let x = {...value};
                    if (this.modalInfo.usedDocIds.includes(value.id)) {
                        x.disabled = true;
                    }
                    return x;
                });
                // this.tempCompetencies = [...a];
            }else{
                this.tempCompetencies = [...this.allCompetencies];
            }

            this.addModal.open();
        });
    }

    translateRequirementChild(child: SkillsMatrixRule) {
        return child.entries.map((e: SkillRuleEntry) => e.name).join(` ${child.rule} `);
    }

    viewDate(ms: any) {
        return dayjs(ms).format(AppConstant.fullDateTimeFormat);
    }

    saveInfo({closeFn}) {
        console.log(JSON.stringify(this.create_request, null, 2));
        const payload = {...this.create_request};
        if(payload._competency_optional){
            payload.child = [];
        }
        this.modalProcessing = true;
        this.skillMatrixService.saveSkillMatrixRecord(this.employer.id, payload).subscribe((data: any) => {
            this.modalProcessing = false;
            if (data.success) {
                closeFn();
                this.loadPage(0, { sortKey: 'job_role_name' }, true);
            } else if (data.already_exists) {
                this.confirmationModalRef.openConfirmationPopup({
                    headerTitle: 'Warning',
                    title: data.message,
                    confirmLabel: 'Ok',
                    hasCancel: false,
                    onConfirm: () => {
                    }
                });
            } else {
                const message = data.message || 'Failed to save info.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    private fetchSkillsToAdd(selectedJobRoleRef){
        return this.skillMatrixService.companySkillsToAdd(this.employer.id, {
            country_code: this.employer.country_code,
            ...(selectedJobRoleRef ? {
                selectedJobRoleId: (selectedJobRoleRef && selectedJobRoleRef.id) ? selectedJobRoleRef.id : selectedJobRoleRef ,
            } : {}),
        });
    }

    private getCompetencies() {
        return this.resourceService.getCompetencies({country_code: this.employer.country_code || undefined});
    }

    toggleRequirements($event){
        this.create_request._competency_optional = $event.target?.checked;
    }

    trackByRowIndex(index: number, obj: any) {
        return obj ? (obj._id || obj.id) : index;
    }

    private getNewChild() {
        return {
            _id: (new Date()).getTime(),
            rule: SKILLS_MATRIX_RULE_TYPE.AND,
            entries: [null],
        };
    }

    addRequirementRow() {
        this.create_request.child.push(this.getNewChild());
    }

    refreshMetaCompetencies($event, index) {
        let {selected_id, last} = $event || {};
        console.log('refresh meta', $event);
        this.tempCompetencies = this.tempCompetencies.map((value: any) => {
            let x = {...value};
            if (last && ((last.id && value.id === last.id) || (value.id === last))) {
                x.disabled = false;
            }
            if (selected_id && value.id === selected_id) {
                x.disabled = true;
            }
            return x;
        });
    }

    ruleSetChanged($event, index) {
        console.log('ruleset changed', index, $event);
        if ($event.empty) {
            this.create_request.child.splice(index, 1); //  = [...this.create_request.child.filter((v,i) => i !== index)];
        } else {
            this.create_request.child[index] = $event.rule;
        }
    }

    removeRuleById(ruleId, name) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Warning',
            title: `Are you sure you want to the delete the competency/certification requirements for the ${name} job role?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.modalProcessing = false;
                this.skillMatrixService.deleteSkillMatrixRecordById(this.employer.id, ruleId).subscribe((data: any) => {
                    this.modalProcessing = false;
                    this.loadPage(0, { sortKey: 'job_role_name' }, true);
                });
            }
        });

    }

    viewRule(row){
        this.viewModalInfo.title = row.job_role_ref?.name;
        this.viewModalInfo.record = row;
        this.viewModalInfo.ready = true;
        this.vModalHtml.open();
    }

    rowBtnClicked({entry}, row){
        if(entry.key === 'view'){
            return this.viewRule(row);
        }
        else if(entry.key === 'edit'){
            return this.openAddModal(row);
        }
        else if(entry.key === 'delete'){
            return this.removeRuleById(row.id, row.job_role_name);
        }
    }
}

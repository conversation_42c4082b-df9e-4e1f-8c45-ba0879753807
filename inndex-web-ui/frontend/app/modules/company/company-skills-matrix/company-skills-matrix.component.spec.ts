import {
    ComponentFixture,
    TestBed,
    fakeAsync,
    tick,
} from '@angular/core/testing';
import {CompanySkillsMatrixComponent} from './company-skills-matrix.component';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {ActivatedRoute} from '@angular/router';
import {
    ToastService,
    SkillMatrixService,
    ResourceService,
    SkillsMatrixRule,
    SkillsMatrixPage,
    SKILLS_MATRIX_RULE_TYPE,
    DoNotAskAgainService,
    ProjectService,
    ToastType,
} from '@app/core';
import {
    BlockLoaderComponent,
    GenericConfirmationModalComponent,
    IModalComponent,
    PlaceholderPageComponent,
} from '@app/shared';
import {of, Subject} from 'rxjs';
import {DatatableComponent} from '@swimlane/ngx-datatable';
import {NgSelectComponent} from '@ng-select/ng-select';
import {FormsModule} from '@angular/forms';
import {TitleCasePipe, UpperCasePipe} from '@angular/common';
import * as dayjs from 'dayjs';
import {activeSetting} from '@env/settings';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';

describe('CompanySkillsMatrixComponent', () => {
    let component: CompanySkillsMatrixComponent;
    let fixture: ComponentFixture<CompanySkillsMatrixComponent>;
    let mockSkillMatrixService: jasmine.SpyObj<SkillMatrixService>;
    let mockProjectService: jasmine.SpyObj<ProjectService>;
    let mockToastService: jasmine.SpyObj<ToastService>;
    let mockResourceService: jasmine.SpyObj<ResourceService>;
    let mockModalService: jasmine.SpyObj<NgbModal>;
    let mockActivatedRoute: any;
    let mockConfirmationModalRef: any;
    const ruleId = 123;
    const jobRoleName = 'Test Role';

    beforeEach(async () => {
        mockSkillMatrixService = jasmine.createSpyObj('SkillMatrixService', [
            'getCompanySkillMatrixList',
            'saveSkillMatrixRecord',
            'deleteSkillMatrixRecordById',
            'companySkillsToAdd',
            'deserializeChildEntries',
        ]);

        mockProjectService = jasmine.createSpyObj('projectService', ['getUserInfo']);
        mockToastService = jasmine.createSpyObj('ToastService', ['show']);
        mockToastService.types = ToastType;
        mockResourceService = jasmine.createSpyObj('ResourceService', [
            'getCompetencies',
            'getCompanySettingByName',
            'saveCompanySetting',
        ]);
        mockResourceService.getCompanySettingByName.and.returnValue(of({ success: true, record: { value: { exempted_users: [], filtered_list: [] } } }));
        mockResourceService.saveCompanySetting.and.returnValue(of({ success: true }));
        mockModalService = jasmine.createSpyObj('NgbModal', ['open']);

        mockActivatedRoute = {
            snapshot: {
                params: {},
            },
        };

        await TestBed.configureTestingModule({
            imports: [FormsModule],
            declarations: [
                CompanySkillsMatrixComponent,
                DatatableComponent,
                GenericConfirmationModalComponent,
                IModalComponent,
                BlockLoaderComponent,
                PlaceholderPageComponent,
            ],
            providers: [
                {provide: SkillMatrixService, useValue: mockSkillMatrixService},
                {provide: ProjectService, useValue: mockProjectService},
                {provide: ToastService, useValue: mockToastService},
                {provide: ResourceService, useValue: mockResourceService},
                {provide: NgbModal, useValue: mockModalService},
                {provide: ActivatedRoute, useValue: mockActivatedRoute},
                TitleCasePipe,
                UpperCasePipe,
                DoNotAskAgainService,
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
        }).compileComponents();

        fixture = TestBed.createComponent(CompanySkillsMatrixComponent);
        component = fixture.componentInstance;
        component.employer = {id: 1, country_code: 'US'};

        mockConfirmationModalRef = {
            openConfirmationPopup: jasmine.createSpy('openConfirmationPopup'),
        };
        component['confirmationModalRef'] = mockConfirmationModalRef;

        mockSkillMatrixService.deleteSkillMatrixRecordById.and.returnValue(
            of({success: true})
        );

        mockSkillMatrixService.deserializeChildEntries.and.returnValue([
            {id: 3},
            {id: 4},
        ]);
    });

    it('should create component', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
        expect(component.rulesPage instanceof SkillsMatrixPage).toBe(true);
        expect(component.filter).toEqual({});
        expect(component.rowButtonGroup.length).toBe(3);
        expect(component.pagePlaceholderIcon).toBeDefined();
        expect(component.pageIntroLines.length).toBe(2);
    });

    it('should call loadPage on ngOnInit', () => {
        spyOn(component, 'loadPage');
        fixture.detectChanges();

        expect(component.loadPage).toHaveBeenCalledWith(
            0,
            {sortKey: 'job_role_name'},
            true
        );
    });

    it('should fetch company skill matrix list', fakeAsync(() => {
        const mockResponse = {
            success: true,
            records: [],
            totalCount: 0,
            pageSize: 50,
            sortKey: 'job_role_name',
            sortDir: 'asc',
        };
        mockSkillMatrixService.getCompanySkillMatrixList.and.returnValue(
            of(mockResponse)
        );

        component.loadPage(0);
        tick();

        expect(
            mockSkillMatrixService.getCompanySkillMatrixList
        ).toHaveBeenCalledWith(1, {
            sortKey: 'job_role_name',
            pageNumber: 0,
            pageSize: 50,
            q: '',
        });
        expect(component.viewProcessing).toBeFalsy();
    }));

    it('should show error toast if loadPage fails', fakeAsync(() => {
        const errorMsg = 'Failed to fetch rules.';
        mockSkillMatrixService.getCompanySkillMatrixList.and.returnValue(
            of({success: false, message: errorMsg})
        );
        component.loadPage(0);
        tick();
        expect(mockToastService.show).toHaveBeenCalledWith(
            mockToastService.types.ERROR,
            errorMsg,
            { data: {success: false, message: errorMsg} }
        );
    }));

    it('should handle page callback', () => {
        spyOn(component, 'loadPage');
        const event = {offset: 2};

        component.pageCallback(event);

        expect(component.loadPage).toHaveBeenCalled();
    });

    it('should handle sort callback', () => {
        spyOn(component, 'loadPage');
        const event = {sorts: [{prop: 'name', dir: 'desc'}]};
        component.sortCallback(event);
        expect(component.loadPage).toHaveBeenCalledWith(0, {
            sortKey: 'name',
            sortDir: 'desc',
        });
    });

    it('should handle search filter', () => {
        spyOn(component, 'loadPage');
        const event = {search: 'test'};
        component.searchFilter(event);
        expect(component.filter.searchText).toBe('test');
        expect(component.loadPage).toHaveBeenCalledWith(0);
    });

    it('should save info successfully', fakeAsync(() => {
        const mockResponse = {success: true};
        mockSkillMatrixService.saveSkillMatrixRecord.and.returnValue(
            of(mockResponse)
        );
        spyOn(component, 'loadPage');

        component.create_request = new SkillsMatrixRule();
        component.saveInfo({closeFn: jasmine.createSpy()});
        tick();

        expect(mockSkillMatrixService.saveSkillMatrixRecord).toHaveBeenCalled();
        expect(component.loadPage).toHaveBeenCalledWith(0, {sortKey: 'job_role_name'}, true);
    }));

    it('should add requirement row', () => {
        component.create_request = new SkillsMatrixRule();
        component.create_request.child = [];

        component.addRequirementRow();

        expect(component.create_request.child.length).toBe(1);
    });

    it('should handle rule set change', () => {
        component.create_request = new SkillsMatrixRule();
        component.create_request.child = [
            {
                _id: 1,
                rule: SKILLS_MATRIX_RULE_TYPE.AND,
                entries: [null],
            },
        ];
        const event = {
            empty: false,
            rule: {
                _id: 1,
                rule: 'OR',
                entries: [null],
            },
        };

        component.ruleSetChanged(event, 0);

        expect(component.create_request.child[0].rule).toBe('OR');
    });

    it('should remove empty rule set', () => {
        component.create_request = new SkillsMatrixRule();
        component.create_request.child = [
            {
                _id: 1,
                rule: SKILLS_MATRIX_RULE_TYPE.AND,
                entries: [null],
            },
        ];

        const event = {empty: true};
        component.ruleSetChanged(event, 0);
        expect(component.create_request.child.length).toBe(0);
    });

    it('should view rule details', () => {
        const mockRow = {
            id: 1,
            job_role_name: 'Test Role',
            job_role_ref: {name: 'Test Role'},
        };

        const modalRef = jasmine.createSpyObj('NgbModal', ['open']);
        component['vModalHtml'] = modalRef;
        component.viewRule(mockRow);

        expect(component.viewModalInfo.title).toBe('Test Role');
        expect(modalRef.open).toHaveBeenCalled();
    });

    it('should handle view button click', () => {
        const mockRow = {id: 1, job_role_name: 'Test Role'};
        spyOn(component, 'viewRule');

        component.rowBtnClicked({entry: {key: 'view'}}, mockRow);

        expect(component.viewRule).toHaveBeenCalledWith(mockRow);
    });

    it('should handle edit button click', () => {
        const mockRow = {id: 1, job_role_name: 'Test Role'};
        spyOn(component, 'openAddModal');

        component.rowBtnClicked({entry: {key: 'edit'}}, mockRow);

        expect(component.openAddModal).toHaveBeenCalledWith(mockRow);
    });

    it('should handle delete button click', () => {
        const mockRow = {id: 1, job_role_name: 'Test Role'};
        spyOn(component, 'removeRuleById');

        component.rowBtnClicked({entry: {key: 'delete'}}, mockRow);

        expect(component.removeRuleById).toHaveBeenCalledWith(1, 'Test Role');
    });

    it('should open confirmation modal with correct parameters', () => {
        component.removeRuleById(ruleId, jobRoleName);

        expect(mockConfirmationModalRef.openConfirmationPopup).toHaveBeenCalled();

        const callArgs =
            mockConfirmationModalRef.openConfirmationPopup.calls.first().args[0];
        expect(callArgs.headerTitle).toBe('Warning');
        expect(callArgs.title).toContain(jobRoleName);
        expect(callArgs.confirmLabel).toBe('Delete');
        expect(typeof callArgs.onConfirm).toBe('function');
    });

    it('should call deleteSkillMatrixRecordById with correct parameters when confirmed', fakeAsync(() => {
        spyOn(component, 'loadPage');

        mockConfirmationModalRef.openConfirmationPopup.and.callFake(
            (options: any) => {
                options.onConfirm();
            }
        );

        component.removeRuleById(ruleId, jobRoleName);
        tick();

        expect(
            mockSkillMatrixService.deleteSkillMatrixRecordById
        ).toHaveBeenCalledWith(component.employer.id, ruleId);
        expect(component.loadPage).toHaveBeenCalledWith(0, {sortKey: 'job_role_name'}, true);
        expect(component.modalProcessing).toBeFalsy();
    }));

    it('should not proceed with delete if modal is cancelled', () => {
        component.removeRuleById(ruleId, jobRoleName);

        expect(
            mockSkillMatrixService.deleteSkillMatrixRecordById
        ).not.toHaveBeenCalled();
    });

    it('should extract existing entries correctly', () => {
        const mockRecord = {
            entries: [{id: 1}, {id: 2}],
            child: [{id: 3}, {id: 4}],
        };
        const expectedUsedDocIds = [1, 2, 3, 4];

        component.extractExistingEntries(mockRecord);

        expect(component.modalInfo.usedDocIds).toEqual(expectedUsedDocIds);
    });

    it('should translate requirement child correctly', () => {
        const mockChild = {
            entries: [
                {id: 1, name: 'Skill A'},
                {id: 2, name: 'Skill B'},
            ],
            rule: SKILLS_MATRIX_RULE_TYPE.AND,
        };
        const result = component.translateRequirementChild(mockChild);
        expect(result).toBe('Skill A AND Skill B');
    });

    it('should handle empty entries in translateRequirementChild', () => {
        const mockChild = {
            entries: [],
            rule: SKILLS_MATRIX_RULE_TYPE.AND,
        };
        const result = component.translateRequirementChild(mockChild);
        expect(result).toBe('');
    });

    it('should format date correctly using viewDate', () => {
        const mockTimestamp = 1633072800000;
        const expectedFormat = dayjs(mockTimestamp).format(
            activeSetting.fullDateTimeFormat
        );
        const result = component.viewDate(mockTimestamp);
        expect(result).toBe(expectedFormat);
    });

    it('should handle invalid date in viewDate', () => {
        const result = component.viewDate(null);
        expect(result).toBe('Invalid Date');
    });

    it('should call getCompetencies with correct parameters', () => {
        component.employer = {country_code: 'US'};
        component['getCompetencies']();
        expect(mockResourceService.getCompetencies).toHaveBeenCalledWith({
            country_code: 'US',
        });
    });

    it('should toggle _competency_optional based on event', () => {
        component.create_request = new SkillsMatrixRule();
        component.create_request._competency_optional = false;
        const mockEvent = {target: {checked: true}};
        component.toggleRequirements(mockEvent);
        expect(component.create_request._competency_optional).toBe(true);
    });

    it('should return correct trackByRowIndex value', () => {
        const objWithId = {_id: 5};
        const objWithIdResult = component.trackByRowIndex(0, objWithId);
        expect(objWithIdResult).toBe(5);

        const objWithIdFallback = {id: 10};
        const objWithIdFallbackResult = component.trackByRowIndex(
            0,
            objWithIdFallback
        );
        expect(objWithIdFallbackResult).toBe(10);

        const objNull = null;
        const objNullResult = component.trackByRowIndex(0, objNull);
        expect(objNullResult).toBe(0);
    });

    it('should refresh meta competencies correctly', () => {
        component.tempCompetencies = [
            {id: 1, disabled: true},
            {id: 2, disabled: true},
            {id: 3, disabled: true},
        ];

        const event = {selected_id: 2, last: {id: 1}};
        component.refreshMetaCompetencies(event, 0);

        expect(component.tempCompetencies).toEqual([
            {id: 1, disabled: false},
            {id: 2, disabled: true},
            {id: 3, disabled: true},
        ]);

        const event2 = {selected_id: 3, last: 1};
        component.refreshMetaCompetencies(event2, 0);

        expect(component.tempCompetencies).toEqual([
            {id: 1, disabled: false},
            {id: 2, disabled: true},
            {id: 3, disabled: true},
        ]);

        const event3 = {selected_id: 1, last: {id: 3}};
        component.refreshMetaCompetencies(event3, 0);

        expect(component.tempCompetencies).toEqual([
            {id: 1, disabled: true},
            {id: 2, disabled: true},
            {id: 3, disabled: false},
        ]);
    });

    it('should call companySkillsToAdd with correct parameters', () => {
        const mockResponse = of({success: true, records: []});
        mockSkillMatrixService.companySkillsToAdd.and.returnValue(mockResponse);

        const selectedJobRoleRef = {id: 5};
        const result = component['fetchSkillsToAdd'](selectedJobRoleRef);

        expect(mockSkillMatrixService.companySkillsToAdd).toHaveBeenCalledWith(
            component.employer.id,
            {
                country_code: component.employer.country_code,
                selectedJobRoleId: selectedJobRoleRef.id,
            }
        );

        result.subscribe((response) => {
            expect(response).toEqual({success: true, records: []});
        });
    });

    it('should call companySkillsToAdd without selectedJobRoleId when selectedJobRoleRef is null', () => {
        const mockResponse = of({success: true, records: []});
        mockSkillMatrixService.companySkillsToAdd.and.returnValue(mockResponse);
        const result = component['fetchSkillsToAdd'](null);

        expect(mockSkillMatrixService.companySkillsToAdd).toHaveBeenCalledWith(
            component.employer.id,
            {
                country_code: component.employer.country_code,
            }
        );

        result.subscribe((response) => {
            expect(response).toEqual({success: true, records: []});
        });
    });

    it('should call openConfirmationPopup when toggling feature', () => {
        component.skills_matrix = false;
        component.rulesPage.totalCount = 1;
        const event = {preventDefault: jasmine.createSpy()};

        component.onFeatureToggle(event);
        expect(event.preventDefault).toHaveBeenCalled();

        expect(
            component['confirmationModalRef'].openConfirmationPopup
        ).toHaveBeenCalled();
    });

    it('should call addModal.open when openAddModal is executed', fakeAsync(() => {
        component['addModal'] = jasmine.createSpyObj('addModal', ['open']);
        mockSkillMatrixService.companySkillsToAdd.and.returnValue(
            of({success: true, records: []})
        );
        mockResourceService.getCompetencies.and.returnValue(
            of({success: true, competencieslist: []})
        );

        component.openAddModal();
        tick();

        expect(component['addModal'].open).toHaveBeenCalled();
    }));

    it('should open exemption list modal when openExemptionListModal is called', () => {
        component.manageExemptionModal = {ready: false, exempted_users: [], filtered_list: []};
        component['manageExemptionModalRef'] = jasmine.createSpyObj('manageExemptionModalRef', ['open']);

        component.openExemptionListModal();

        expect(component.manageExemptionModal.ready).toEqual(true);
        expect(component['manageExemptionModalRef'].open).toHaveBeenCalled();
    });

    it('should show error toast if openExemptionListModal fails', () => {
        mockResourceService.getCompanySettingByName.and.returnValue(
            of({success: false, message: 'fail'})
        );
        component['manageExemptionModalRef'] = jasmine.createSpyObj('manageExemptionModalRef', ['open']);
        component.openExemptionListModal();
        expect(mockToastService.show).toHaveBeenCalledWith(
            mockToastService.types.ERROR,
            'fail',
            { data: {success: false, message: 'fail'} }
        );
    });

    it('should call openAddModal when topBtnClicked is called with add', () => {
        spyOn(component, 'openAddModal');
        component.topBtnClicked({code: 'add'});
        expect(component.openAddModal).toHaveBeenCalled();
    });

    it('should call openExemptionListModal when topBtnClicked is called with exempt', () => {
        spyOn(component, 'openExemptionListModal');
        component.topBtnClicked({code: 'exempt'});
        expect(component.openExemptionListModal).toHaveBeenCalled();
    });

    it('should handle onConfirm in onFeatureToggle', () => {
        component.skills_matrix = false;
        component.rulesPage.totalCount = 1;
        spyOn(component.featureStatusChanged, 'emit');

        const event = {preventDefault: jasmine.createSpy()};
        component.onFeatureToggle(event);
        const args = (component as any).confirmationModalRef.openConfirmationPopup.calls.mostRecent().args[0];
        args.onConfirm(); // simulate confirm


        expect(component.skills_matrix).toBe(true);
        expect(component.featureStatusChanged.emit).toHaveBeenCalledWith(true);
    });

    it('should show Warning when onFeatureToggle is called with 0 records', () => {
        component.skills_matrix = false;
        component.rulesPage.totalCount = 0;
        spyOn(component.featureStatusChanged, 'emit');

        const event = {preventDefault: jasmine.createSpy()};
        component.onFeatureToggle(event);
        expect(mockConfirmationModalRef.openConfirmationPopup).toHaveBeenCalled();
        const callArgs = mockConfirmationModalRef.openConfirmationPopup.calls.first().args[0];
        expect(callArgs.headerTitle).toBe('Warning');
        expect(component.skills_matrix).toBe(false);
    });

    it('should handle onClose in onFeatureToggle', () => {
        component.skills_matrix = true;
        component.rulesPage.totalCount = 1;

        const event = {preventDefault: jasmine.createSpy()};
        component.onFeatureToggle(event);
        const args = (component as any).confirmationModalRef.openConfirmationPopup.calls.mostRecent().args[0];
        args.onClose(); // simulate close

        expect(component.skills_matrix).toBe(true);
    });

    it('should call sortItems and update manageExemptionModal.exempted_users in onSelection', () => {
        const unsorted = {id: 1, name: 'A'};
        const selectDropdown$ = {clearModel: jasmine.createSpy()};
        spyOn(component, 'sortItems').and.callThrough();
        component.manageExemptionModal = {ready: false, exempted_users: [{id: 2, name: 'B'}], filtered_list: []};
        component.onSelection(unsorted, (selectDropdown$ as unknown) as NgSelectComponent);
        expect(component.sortItems).toHaveBeenCalledWith([unsorted, {id: 2, name: 'B'}]);
        expect(component.manageExemptionModal.exempted_users[0].name).toBe('A');
    });

    it('should remove user from exemption list in onRemoveItem', () => {
        const user = {id: 1, name: 'User1'};
        component.manageExemptionModal = {ready: false, exempted_users: [user], filtered_list: []};
        // Assign a spy to openConfirmationPopup
        component['confirmationModalRef'] = {
            openConfirmationPopup: jasmine.createSpy('openConfirmationPopup').and.callFake((opts: any) => opts.onConfirm())
        } as any;
        component.onRemoveItem(user);
        expect(component.manageExemptionModal.exempted_users.length).toBe(0);
    });


    it('should show error toast if saveExemptionList fails', fakeAsync(() => {
        mockResourceService.saveCompanySetting.and.returnValue(of({success: false, message: 'fail'}));
        const closeFn = jasmine.createSpy();
        component.manageExemptionModal = {ready: true, exempted_users: [{id: 1, name: 'A'}], filtered_list: []};
        component.saveExemptionList({closeFn});
        tick();
        expect(mockToastService.show).toHaveBeenCalledWith(
            mockToastService.types.ERROR,
            'fail',
            { data: {success: false, message: 'fail'} }
        );
        expect(closeFn).not.toHaveBeenCalled();
    }));

    it('should call closeFn and reset modal on successful saveExemptionList', fakeAsync(() => {
        const closeFn = jasmine.createSpy();
        component.manageExemptionModal = {ready: true, exempted_users: [{id: 1, name: 'A'}], filtered_list: []};
        component.saveExemptionList({closeFn});
        tick();
        expect(closeFn).toHaveBeenCalled();
        expect(component.manageExemptionModal.ready).toBe(false);
    }));

    it('should handle bindUsersTypeAhead with valid and invalid email', fakeAsync(() => {
        const validEmail = '<EMAIL>';
        const invalidEmail = 'notanemail';
        component.userInput$ = new Subject<string>();
        component.records$ = null as any;
        component['projectService'] = mockProjectService;
        mockProjectService.getUserInfo.and.returnValue(of({success: true, userInfo: {id: 1, name: 'User'}}));
        (component as any).bindUsersTypeAhead();
        component.userInput$.next(validEmail);
        tick(500);
        component.userInput$.next(invalidEmail);
        tick(500);
        expect(component.records$).toBeDefined();
    }));

    it('should return item.id in trackByFn', () => {
        const item = { id: 42, name: 'Test' };
        expect(component.trackByFn(item)).toBe(42);
    });

    it('should return undefined if item has no id in trackByFn', () => {
        const item = { name: 'NoId' };
        expect(component.trackByFn(item)).toBeUndefined();
    });

// Tests for private fetchUserInfo
    it('should return userInfo in records if user found', (done) => {
        const term = '<EMAIL>';
        const userInfo = { id: 1, name: 'User' };
        mockProjectService.getUserInfo.and.returnValue(of({ success: true, userInfo }));

        (component as any).manageExemptionModal = { exempted_users: [], filtered_list: [] };
        (component as any).fetchUserInfo(term).subscribe((result) => {
            expect(result.records[0]).toEqual(userInfo);
            done();
        });
    });

    it('should show info toast if user already in exempted_users', (done) => {
        const term = '<EMAIL>';
        const userInfo = { id: 1, name: 'User' };
        (component as any).manageExemptionModal = { exempted_users: [{ id: 1, name: 'User' }], filtered_list: [] };
        mockProjectService.getUserInfo.and.returnValue(of({ success: true, userInfo }));

        (component as any).fetchUserInfo(term).subscribe((result) => {
            expect(mockToastService.show).toHaveBeenCalledWith(
                mockToastService.types.INFO,
                'User "User" already exists in exemption list'
            );
            expect(result.records[0]).toEqual(userInfo);
            done();
        });
    });

    it('should return data as is if userInfo not found', (done) => {
        const term = '<EMAIL>';
        const data = { success: false, message: 'Not found' };
        mockProjectService.getUserInfo.and.returnValue(of(data));

        (component as any).manageExemptionModal = { exempted_users: [], filtered_list: [] };
        (component as any).fetchUserInfo(term).subscribe((result) => {
            expect(result).toEqual(data);
            done();
        });
    });

});

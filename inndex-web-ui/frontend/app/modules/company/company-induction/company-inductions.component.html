<div class="d-flex" id="wrapper">
    <company-side-nav [employer]="employer" [companyResolverResponse]="companyResolverResponse"></company-side-nav>
    <div class="w-100 px-3 detail-page-header-margin" [ngClass]="{'ml-fix': !is_mobile_nav}">
        <div class="row">
            <project-header [isCompanyHeader]="true" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="col-sm-12 my-3 outer-border" *ngIf="!page_loading">
                <div class="outer-border-radius">
                    <div class="col-sm-12 pb-3">
                        <div class="d-flex flex-column flex-sm-row justify-content-between">
                        <search-with-filters class="w-100" [filterData]="filterData" (filterEmitter)="onFilterSelection($event)" (searchEmitter)="searchFunction($event)"></search-with-filters>
                        <div class="position-relative">
                            <action-button 
                                [actionList]="actionButtonMetaData.actionList"
                                (selectedActionEmmiter)="onActionSelection($event)"
                                [hideNewFeatureBtn]="true">
                            </action-button>
                        </div>
                    </div>
                    <div class="clearfix"></div>

                    <ngx-datatable
                        [scrollbarV]="true"
                        [virtualization]="false"
                        [loadingIndicator]="loadingInlineCompanyInductions"
                        #cInductionsTable
                        [columnMode]="'force'"
                        [footerHeight]="30"
                        [headerHeight]="40"
                        [rowHeight]="'auto'"
                        [externalPaging]="true"
                        [externalSorting]="true"
                        [count]="companyInductionsPage.totalCount"
                        [offset]="companyInductionsPage.pageNumber"
                        [limit]="companyInductionsPage.pageSize"
                        (page)="timePageCallback($event, true)"
                        [sortType]="'single'"
                        (sort)="timeSortCallback($event)"
                        [rows]="companyInductionsPage.records"
                        class="bootstrap table table-v2 table-v3 ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                        [columns]="[
                {name:'User ID', prop: 'user_ref', maxWidth: 100, headerClass: 'py-2 font-weight-bold', minWidth: '100', cellClass: 'py-1'},
                {name:'First Name', prop: 'first_name', headerClass: 'py-2 font-weight-bold', cellTemplate: firstNameColumn, minWidth: '100', cellClass: 'py-1'},
                {name:'Last Name', prop: 'last_name', headerClass: 'py-2 font-weight-bold', cellTemplate: lastNameColumn, minWidth: '100', cellClass: 'py-1'},
                {name:'Email', prop: 'email', sortable: false, headerClass: 'py-2 font-weight-bold', cellTemplate: emailColumn, minWidth: '100', cellClass: 'py-1'},
                {name:'Date Completed', prop: 'created_at', cellTemplate: epochCell, maxWidth: 140, headerClass: 'py-2 font-weight-bold', minWidth: '120', cellClass: 'py-1'},
                {name:'Expiry Date', prop: 'expire_on', cellTemplate: epochCell, maxWidth: 125, headerClass: 'py-2 font-weight-bold', minWidth: '100', cellClass: 'py-1'},
                {name:'Status', prop: 'status_message', cellTemplate: statusCell, sortable: false, maxWidth: 100, headerClass: 'py-2 font-weight-bold text-center', minWidth: '100', cellClass: 'text-center py-1 d-flex1 h-auto align-self-center'},
                {name:'Action', cellTemplate: actionBtnCell, sortable: false, maxWidth: 100, headerClass: 'd-flex justify-content-start py-2 font-weight-bold', minWidth: '100', cellClass: 'd-flex justify-content-start py-1 no-ellipsis'}
            ]">
                        <ng-template #firstNameColumn let-row="row" let-value="value">
                            <span appTooltip>{{value}}</span>
                        </ng-template>
                        <ng-template #lastNameColumn let-row="row" let-value="value">
                            <span appTooltip>{{value}}</span>
                        </ng-template>
                        <ng-template #emailColumn let-row="row" let-value="value">
                            <span appTooltip>{{value}}</span>
                        </ng-template>
                        <ng-template #epochCell let-row="row" let-value="value">
                            {{value ? epoch(value) : ''}}
                        </ng-template>
                        <ng-template #statusCell let-row="row" let-value="value">
                            <span [ngClass]="getStatusClasses(row.status_code)"> {{value}} </span>
                        </ng-template>
                        <ng-template #actionBtnCell let-row="row" let-value="value">
                            <button
                                title="View" type="button" class="btn btn-sm align-middle btn-action mr-1 mb-1 px-2 py-1"
                                (click)="openPreview(row)">
                                <img class="img-icon" [src]="AssetsUrlSiteAdmin.search_svg" alt="p">
                            </button>
                            <button
                                title="Reject" type="button" class="btn btn-sm align-middle btn-action mb-1 px-2 py-1"
                                *ngIf="row.status_code === 1" (click)="markRejected($event, row)">
                                <img class="img-icon" [src]="AssetsUrlSiteAdmin.closeXIcon" alt="p">
                            </button>
                        </ng-template>
                    </ngx-datatable>
                </div>
            </div>
        </div>
    </div>
    <i-modal #previewCIModal size="lg" [title]="previewingRow.full_name" [showCancel]="false" rightPrimaryBtnTxt="Done" (onClickRightPB)="closePreviewCIModal($event)" (onCancel)="closePreviewCIModal()">
        <div class="px-2" *ngIf="showPreviewCIModal">
            <div class="card-body" *ngIf="previewingRow.ci.quiz && previewingRow.ci.quiz.induction_questions">
                <div class="font-weight-bold">Quiz</div>
                <ul class="list-group small">
                    <li
                        *ngFor="let answer of (previewingRow.ci.quiz.quiz_answers || []); let i = index;"
                        class="list-group-item px-0 border-top-0 border-left-0 border-right-0 d-flex justify-content-between align-items-center"
                        [class.border-bottom-0]="(i+1) === (previewingRow.ci.quiz.quiz_answers || []).length"
                    >
                        <span>{{ previewingRow.ci.quiz.induction_questions[i].question }}</span>
                        <div class="d-flex align-items-center">{{ answer}}
                        </div>
                    </li>
                </ul>
            </div>

            <div class="card-body" *ngIf="previewingRow.ci.additional_iq && previewingRow.ci.additional_iq.induction_questions">
                <div class="font-weight-bold">{{ previewingRow.ci.additional_iq.section_title }}</div>
                <ul class="list-group small border-0">
                    <ng-container *ngFor="let answer of previewingRow.ci.additional_iq.induction_questions; let i = index;">
                        <li
                            class="list-group-item px-0 border-top-0 border-left-0 border-right-0 d-flex justify-content-between align-items-center"
                            [class.border-bottom-0]="(i+1) === (previewingRow.ci.additional_iq.induction_questions || []).length"
                        >
                            <span>{{ previewingRow.ci.additional_iq.induction_questions[i].question }}</span>
                            <div class="d-flex align-items-center" style="white-space: pre-line;">
                                <span *ngIf="!['date'].includes(previewingRow.ci.additional_iq.induction_questions[i].ans_field_type)">
                                    {{ previewingRow.ci.additional_iq.induction_questions[i].answer }}
                                </span>
                                <span *ngIf="previewingRow.ci.additional_iq.induction_questions[i].ans_field_type === 'date'">
                                    {{ previewingRow.ci.additional_iq.induction_questions[i].answer ? dateToEpoch(i) : ''}}
                                </span>
                            </div>
                        </li>
                        <ng-container *ngFor="let item of previewingRow.ci.additional_iq.induction_questions[i].sub_questions; let j = index;">
                            <li
                                *ngIf="previewingRow.ci.additional_iq.induction_questions[i].sub_questions[j].que_condition == 'If '+previewingRow.ci.additional_iq.induction_questions[i].answer"
                                class="list-group-item px-0 border-top-0 border-left-0 border-right-0 d-flex justify-content-between align-items-center"
                                [class.border-bottom-0]="(j+1) === (previewingRow.ci.additional_iq.induction_questions[i].sub_questions || []).length"
                            >
                                <span>{{ previewingRow.ci.additional_iq.induction_questions[i].sub_questions[j].question }}</span>
                                <div class="d-flex align-items-center" style="white-space: pre-line;">
                                <span
                                    *ngIf="!['date'].includes(previewingRow.ci.additional_iq.induction_questions[i].sub_questions[j].ans_field_type)">
                                    {{ previewingRow.ci.additional_iq.induction_questions[i].sub_questions[j].answer }}
                                </span>
                                    <span *ngIf="previewingRow.ci.additional_iq.induction_questions[i].sub_questions[j].ans_field_type === 'date'">
                                    {{ previewingRow.ci.additional_iq.induction_questions[i].sub_questions[j].answer ? dateToEpoch(i, j) : ''}}
                                </span>
                                </div>
                            </li>
                        </ng-container>
                    </ng-container>
                </ul>
            </div>

            <div class="card-body" *ngIf="previewingRow.ci.media_resources && previewingRow.ci.media_resources.length">
                <div class="font-weight-bold">Induction Links</div>
                <ul class="list-group small">
                    <li
                        *ngFor="let media of (previewingRow.ci.media_resources || []); let i = index;"
                        class="list-group-item p-0 border-0">
                        <span>Presentation:</span>
                        <a *ngIf="(media.type === 'file')" class="btn btn-link btn-sm" target="_blank" [href]="media.file_ref?.file_url">
                            {{ media.file_ref?.name }}
                        </a>
                        <a *ngIf="(media.type === 'url')" class="btn btn-link btn-sm" target="_blank" [href]="media.content">
                            Luma1 Media URL
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </i-modal>
</div>
<block-loader [show]="page_loading" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
<induction-report-downloader-v2 #downloadReportActionCompRef 
    [employerId]="employer.id"
    [isCompanyInduction]="true"
></induction-report-downloader-v2>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

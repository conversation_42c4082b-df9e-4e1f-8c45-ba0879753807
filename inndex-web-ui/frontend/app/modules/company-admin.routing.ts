import {
    CompanyAdminComponent,
    CompanyInductionsComponent,
    EmployerUsersComponent,
    EmployerProjectComponent,
    ViewInductionsComponent,
    ProjectPowraComponent,
    ProjectTimeManagementHomeComponent,
    CompanySettingComponent,
    CpLiveDashboardComponent,
    CpProjectDashboardComponent, CompanyTmHomeComponent,
    ConductCardsComponent,
} from "@app/modules/company";
import {
    ProgressPhotosComponent,
    DeliveryNotesComponent,
    DailyActivitiesComponent,
    ToolboxTalksComponent,
    ClerkofWorksComponent,
    IncidentReportComponent,
    InspectionTourComponent,
    ELearningModuleComponent,
    CloseCallComponent,
    GoodCallComponent,
    SiteMessagingComponent,
    TaskBriefingsComponent,
    InspectionsComponent,
    WorkPackagePlanComponent,
    QualityChecklistsComponent, AssetsComponent,
    ProjectRamsComponent, PermitRequestComponent,
} from "@app/modules/common";
import {
    Take5sComponent,
} from "@app/modules/site-admin";
import {AuthGuard, CompanyAuthGuard, SiteUserGuard, CompanyResolver} from "@app/core";
import {Routes} from "@angular/router";
import { CompanyMessagingComponent } from "./company/company-messaging/company-messaging.component";
import { UserFormValidationGuard } from "@app/core/guards/user-form-validation.guard";

export const company_admin_routes: Routes = [
    {path: 'company-admin', component: CompanyAdminComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard, UserFormValidationGuard],},
    {path: 'company-admin/:employerId/settings', component: CompanySettingComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse : CompanyResolver }},
    {path: 'company-admin/employer-users/:employerId', component: EmployerUsersComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/employer-project/:employerId', component: EmployerProjectComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/time-management/:employerId', component: CompanyTmHomeComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/company-inductions/:employerId', component: CompanyInductionsComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/dashboard/:employerId/live/:projectId', component: CpLiveDashboardComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/dashboard/:employerId/view/:projectId', component: CpProjectDashboardComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/inductions/:employerId/view/:projectId', component: ViewInductionsComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-time-management/:employerId/time-logs/:projectId', component: ProjectTimeManagementHomeComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }, data: {attach_optima: true}},
    {path: 'company-admin/project-progress-photos/:projectId/:employerId', component: ProgressPhotosComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-delivery-notes/:projectId/:employerId', component: DeliveryNotesComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-collection-notes/:projectId/:employerId', component: DeliveryNotesComponent, data: {toolkey: 'collection_note'}, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-daily-activities/:projectId/:employerId', component: DailyActivitiesComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/close-call/:projectId/:employerId', component: CloseCallComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/toolbox-talks/:employerId', component: ToolboxTalksComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/incident-reports/:employerId', component: IncidentReportComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/clerk-of-works/:projectId/:employerId', component: ClerkofWorksComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-toolbox-talks/:projectId/:employerId', component: ToolboxTalksComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-powra/:projectId/:employerId', component: ProjectPowraComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-incident-reports/:projectId/:employerId', component: IncidentReportComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-inspection-tour/:projectId/:employerId', component: InspectionTourComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/e-learning/:employerId', component: ELearningModuleComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/company-messaging/:employerId', component: CompanyMessagingComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-good-calls/:projectId/:employerId', component: GoodCallComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-observations/:projectId/:employerId', data: {toolkey: 'observations'}, component: GoodCallComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-site-messaging/:projectId/:employerId', component: SiteMessagingComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-assets/:projectId/:employerId', component: AssetsComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-take5s/:projectId/:employerId', component: Take5sComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-task-briefings/:projectId/:employerId', component: TaskBriefingsComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},

    {path: 'company-admin/project-work-package-plan/:projectId/:employerId', component: WorkPackagePlanComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/project-rams/:projectId/:employerId', component: ProjectRamsComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/quality-checklists/:projectId/:employerId', component: QualityChecklistsComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},

    {path: 'company-admin/project-inspections/:projectId/:employerId', component: InspectionsComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/permit/:projectId/:employerId', component: PermitRequestComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
    {path: 'company-admin/conduct-cards/:employerId', component: ConductCardsComponent, canActivate: [AuthGuard, CompanyAuthGuard, SiteUserGuard], resolve:{ companyResolverResponse: CompanyResolver }},
];

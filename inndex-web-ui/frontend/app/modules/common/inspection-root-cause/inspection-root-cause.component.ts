import {Component, OnInit, Input, Output, EventEmitter, ViewChild, ChangeDetectionStrategy, QueryList, ChangeDetectorRef, ViewChildren, NgZone} from '@angular/core';
import { NgModel } from '@angular/forms';
import {InspectionBuilder} from "@app/core";
import { take } from 'rxjs/operators';

@Component({
    selector: 'inspection-root-cause',
    templateUrl: './inspection-root-cause.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InspectionRootCauseComponent {

    @Input()
    ibChecklist: InspectionBuilder = new InspectionBuilder;

    @Output()
    isValidRootCause: any = new EventEmitter<any>();
    @ViewChild('categoryName') categoryNameInput: NgModel;
    @ViewChildren('categorysNames') categorysNameInputs: QueryList<NgModel>;

    isValid: boolean = false
    isRequired: boolean = true

    constructor(private cdRef: ChangeDetectorRef,
    private ngZone: NgZone) {

    }

    ngAfterViewInit(): void {
        this.onInputChange();
    }
    
    ngOnChanges() {
        this.isRequired = this.ibChecklist.root_cause?.options?.every(({name}, i) => name == '')
        this.initialComponent();
    }

    onInputChange() {
        this.ngZone.onStable.pipe(take(1)).subscribe(() => {
            this.categorysNameInputs.forEach(input => {
                input.control?.markAsTouched();
            });

            const allValid = this.isAllValid();
            this.isValid = this.ibChecklist.root_cause.options.every(({ name }) => name == '') ||
                !this.ibChecklist.root_cause.options.some(({ name, is_active }) => name.length > 0 && is_active == true);

            this.isValidRootCause.emit(allValid && !this.isValid);
        });
    }


    isAllValid(): boolean {
        return this.categorysNameInputs?.toArray().every(input => input.valid);
    }


    onItemNameChange() {
        this.ngZone.onStable.pipe(take(1)).subscribe(() => {
            this.categorysNameInputs.forEach((input, i) => {
                if(i !== 0) input.control?.markAsTouched();
                this.cdRef.detectChanges();
            });

            const allValid = this.isAllValid();
            this.isValid = this.ibChecklist.root_cause.options[this.ibChecklist.root_cause.options.length - 1].name.length > 0 && 
            this.ibChecklist.root_cause.options.some(({ name, is_active }) => name.length > 0 && is_active == true);
            this.cdRef.detectChanges();
            this.isValidRootCause.emit(allValid && this.isValid);
        });
    }


    initialComponent() {
        const inputRootCause = this.ibChecklist.root_cause
        if (inputRootCause && inputRootCause.options && inputRootCause.options.length && inputRootCause.options[0].name.trim() !== '') {

            this.ibChecklist.root_cause.options = [
                {
                    "name": '',
                    "is_active": true
                },
                ...(this.ibChecklist.root_cause.options)
            ];
        } else {
            this.ibChecklist.root_cause.options = [
                {
                    "name": '',
                    "is_active": true
                },
                ...(inputRootCause.options?.slice(1) || []),
            ];
        }
    }

    trackByRowIndex(index: number, obj: any) {
        return index;
    }

    removeCategoryRow(i: number):void {
        this.ibChecklist.root_cause.options.splice(i, 1);    
        this.categorysNameInputs?.toArray().splice(i, 1);    
        this.isRequired = i != 0 && this.ibChecklist.root_cause.options[this.ibChecklist.root_cause.options.length - 1].name == ''
        this.cdRef.detectChanges();
        this.onItemNameChange()
    }

    addCategory(i) {
        if(this.ibChecklist.root_cause.options[i] && this.ibChecklist.root_cause.options[i].name) {
            this.ibChecklist.root_cause.options = [
                {
                    "name": '',
                    "is_active": true
                },
                ...(this.ibChecklist.root_cause.options)
            ];
        }
        this.isRequired = i == 0 && this.ibChecklist.root_cause.options[this.ibChecklist.root_cause.options.length - 1].name == ''
        this.cdRef.detectChanges();
        this.onItemNameChange()
    }
}

<style>
.ibClTabContent {margin-top: 2px !important;border: 1px solid #dee2e6;}
.dropdown-menu li a {padding: 10px 15px;font-weight: 300;cursor: pointer;}
.multi-column-dropdown {list-style: none;margin: 0px;padding: 0px;text-align: center;}
.multi-column-dropdown li a {display: block;clear: both;line-height: 1.428571429;color: #000000;}
.multi-column-dropdown li a:hover {text-decoration: underline;color: #edb61d;background-color: #fff;}
.dropdown-menu .searchHead {position: absolute;top: -38px;left: 0;width: 170px;height: 41px;padding: 4px 1px;background-color: #fff;display: block;border: 1px solid rgba(0, 0, 0, 0.15);border-bottom: 0;border-radius: 0.25rem;border-bottom-right-radius: unset;border-bottom-left-radius: unset;}
.dropdown-menu .searchHead input.form-control {padding-left: 28px;width: 97%;margin: auto;height: 36px;background-color: #F5F4F4;border: none;}
.dropdown-menu .searchHead .miglass {position: absolute;top: 36%;pointer-events: none;vertical-align: bottom;box-sizing: border-box;right: auto;left: 0.6rem;color: #868686;}
.dropdown-menu .inspection-list {max-height: 210px;overflow: auto;}
#select-inspection-button.btn-primary:focus {box-shadow: none;}
#inspections-drop .form-control::-webkit-input-placeholder { /* WebKit, Blink, Edge */color: #8D8B8A;}
#inspections-drop .form-control::-moz-placeholder { /* Mozilla Firefox 19+ */color: #8D8B8A;opacity:  1;}
#inspections-drop .form-control::-ms-input-placeholder { /* Microsoft Edge */color: #8D8B8A;}
#inspections-drop ::placeholder {color: #8D8B8A;}
.ng-select.ng-select-single .ng-select-container {
    height: 30px;
}
</style>
<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav *ngIf="!isProjectPortal" [employer]="employer" [companyResolverResponse]="companyResolverResponse" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="col-sm-12 my-3 outer-border">
                <div class="outer-border-radius">
                <!-- <div *ngIf="dataLoading">
                    <ngx-skeleton-loader count="1" appearance="circle" [theme]="{width: '165px', height: '38px', 'border-radius': '4px'}"></ngx-skeleton-loader>
                    <ngx-skeleton-loader count="8" [theme]="{'border-radius': '0', height: '50px'}"></ngx-skeleton-loader>
                </div> -->
                <div class="position-relative mb-1 px-3">
                    <div class="col-md-12 p-0 d-inline-block">
                    <div class="d-flex flex-wrap justify-content-between mb-2 gap-8">
                        <h5 class="float-md-left">{{currentItem.ib_title}} <small>(Inspections: {{ page.totalElements }})</small></h5>
                        <div class="d-flex gap-8 justify-content-end flex-column flex-grow-1 flex-sm-row">
                            <action-button
                                [showActionDropdown]="false"
                                [hideNewFeatureBtn]="currentItem.ib_title == 'Inspection Tour'"
                                [newFeatureTitle]="'New Report'"
                                (onOpenAddNew)="openNewInspectionBuilderModal()">
                            </action-button>
                            <div *ngIf="inspectionReports.length">
                                <ng-container>
                                    <button *ngIf="currentItem.ib_title == 'Inspection Tour'" class="btn btn-sm justify-content-center other-action-btn btn-brandeis-blue d-flex align-items-center btn-invite pointer-cursor" (click)="openReportDownloadModal()" >
                                        <span class="medium-font m-font-size material-symbols-outlined mr-2">dashboard</span>
                                        <div class="medium-font m-font-size">Dashboard</div>
                                    </button>
                                    <button *ngIf="isDashboardActivated" class="btn btn-sm justify-content-center other-action-btn btn-brandeis-blue d-flex align-items-center btn-invite pointer-cursor" (click)="openDashboardModal()" >
                                        <span class="medium-font m-font-size material-symbols-outlined mr-2">dashboard</span>
                                        <div class="medium-font m-font-size">Dashboard</div>
                                    </button>
                                </ng-container>

                               <!-- <ng-template #allOtherProject>
                                    <button *ngIf="currentItem.ib_title == 'Inspection Tour' ? true : isDashboardActivated" class="btn btn-sm justify-content-center other-action-btn btn-brandeis-blue d-flex align-items-center btn-invite pointer-cursor" (click)="openReportDownloadModal()" >
                                        <span class="medium-font m-font-size material-symbols-outlined mr-2">dashboard</span>
                                        <div class="medium-font m-font-size">Dashboard</div>
                                    </button>
                                </ng-template>-->
                            </div>
                    </div>
                    </div>
                    <div [ngClass] = "{'p-0 d-flex': true,'flex-column': isMobileDevice}">
                        <search-with-filters (searchEmitter)="searchFunction($event)" customClasses = "d-inline" >
                        </search-with-filters>
                        <ng-select
                            #ngSelectRef
                            [ngClass]="{
                                'w-25 filter-v2-select it-dropdown rounded-select filter-select mb-2': true,
                                'd-block': ibChecklists,
                                'ml-3': !isMobileDevice,
                                'X': ibChecklists?.length <= 7
                            }"
                            [searchable]="false"
                            [clearable]="false"
                            [multiple]="false"
                            [items]="ibChecklists"
                            bindLabel="ib_title"
                            (ngModelChange)="changeContent($event)"
                            [(ngModel)] = "currentItem"
                            name="ib_title"
                        >
                            <ng-template  ng-multi-label-tmp>
                                <span style="display:block;" class="horizontal-center" class="ng-placeholder custom-placeholder text-truncate">
                                    <span>{{currentItem.ib_title}}</span>
                                </span>
                            </ng-template>
                            <ng-template *ngIf="ibChecklists?.length > 7" ng-header-tmp>
                                <div style="width:100%; max-width:100%;" class="text-search horizontal-center px-2">
                                    <span class="material-symbols-outlined large-font">search</span>
                                    <input placeholder="Search" style="width: 100%; line-height: 24px" class="ngx-search" type="search" (input)="ngSelectRef.filter($event.target.value)" />
                                </div>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" class="horizontal-center" let-item$="item$" let-index="index" let-search="searchTerm">
                                <span style="margin-left: 5px;">
                                    {{item.ib_title}}
                                </span>
                            </ng-template>
                        </ng-select>
                    </div>
                    <ul ngbNav #nav="ngbNav" [(activeId)]="activeId" (activeIdChange)="tabChange($event)">
                        <li [ngbNavItem]="'inspection_tour'" [domId]="'inspection_tour'" *ngIf="loadInspectionTours">
                            <ng-template ngbNavContent>
                                <ng-container *ngIf="!loading">
                                    <inspection-tour
                                        [projectId]="projectId"
                                        [employerId]="employerId"
                                        [project]="projectInfo"
                                        [isProjectPortal]="isProjectPortal"
                                        [filterParam]="{is_inherited_project: is_inherited_project}"
                                        (isChildComponentLoaded)="isChildComponentLoaded($event)"
                                        (totalInspection)="handleDataFromChild($event)"
                                        [search]="search"
                                    ></inspection-tour>
                                </ng-container>
                            </ng-template>
                        </li>
                        <li [ngbNavItem]="'inspection_builder'" [domId]="'inspection_builder'" *ngIf="loadInspectionBuilder">
                            <ng-template ngbNavContent>
                                <div *ngIf="!loading">
                                    <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                                   [scrollbarV]="true"
                                                   [virtualization]="false"
                                                   [loadingIndicator]="loadingInlineInspectionBuilder"
                                                   [rows]="filteredinspectionReports"
                                                   [footerHeight]="40"
                                                   [columnMode]="'force'"
                                                   [rowHeight]="'auto'"
                                                   [externalPaging]="true"
                                                   [count]="page.totalElements"
                                                   [offset]="page.pageNumber"
                                                   [limit]="page.size"
                                                   (page)="pageCallback($event, true)">
                                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="70">
                                            <ng-template let-column="column" ngx-datatable-header-template>
                                                Report #
                                            </ng-template>
                                            <ng-template let-row="row" ngx-datatable-cell-template>
                                                {{ row.record_ref }}
                                            </ng-template>
                                        </ngx-datatable-column>
                                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="130">
                                            <ng-template let-column="column" ngx-datatable-header-template>
                                                Time & Date Prepared
                                            </ng-template>
                                            <ng-template let-row="row" ngx-datatable-cell-template>
                                                {{ dayjs(+row.createdAt).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}
                                            </ng-template>
                                        </ngx-datatable-column>
                                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                                            <ng-template let-column="column" ngx-datatable-header-template>
                                                Prepared By
                                            </ng-template>
                                            <ng-template let-row="row" ngx-datatable-cell-template>
                                                <span appTooltip>{{row?.user_ref?.first_name}} {{row?.user_ref?.middle_name || ''}} {{row?.user_ref?.last_name}}</span>
                                            </ng-template>
                                        </ngx-datatable-column>
                                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                                            <ng-template let-column="column" ngx-datatable-header-template>
                                                Location
                                            </ng-template>
                                            <ng-template let-row="row" ngx-datatable-cell-template>
                                                <span appTooltip>{{(row?.location) ? row.location : getLocation(row)}}</span>
                                            </ng-template>
                                        </ngx-datatable-column>
                                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="140">
                                            <ng-template let-column="column" ngx-datatable-header-template>
                                                Unsatisfactory Items
                                            </ng-template>
                                            <ng-template let-row="row" ngx-datatable-cell-template>
                                                <div style="margin-left: 10px;">
                                                    {{ getOpenText(row) }}
                                                    <button *ngIf="!hasUnsatisfactory"
                                                            [ngClass]="{'unsatisfactoryItemsStatus bgSuccess cursor-default': true}">
                                                        {{ unsatisfactoryColumnTxt }}
                                                    </button>
                                                    <button *ngIf="hasUnsatisfactory"
                                                            [ngClass]="{'unsatisfactoryItemsStatus bgDanger': true}"
                                                            (click)="managementReviewModal(row, '')">
                                                        {{ unsatisfactoryColumnTxt }}
                                                    </button>
                                                </div>
                                            </ng-template>
                                        </ngx-datatable-column>
                                        <ngx-datatable-column headerClass="font-weight-bold action-column"
                                                              cellClass="action-column no-ellipsis" [sortable]="false" minWidth="120">
                                            <ng-template let-column="column" ngx-datatable-header-template>
                                                Action
                                            </ng-template>
                                            <ng-template let-row="row" ngx-datatable-cell-template>
                                                <button-group
                                                    [buttons]="rowButtonGroup"
                                                    [btnConditions]="[
                                                        true,
                                                        true,
                                                        true,
                                                        (hasUnsatisfactoryItems(row) > 0)
                                                    ]"
                                                    (onActionClick)="rowBtnClicked($event, row)">
                                                </button-group>

                                            </ng-template>
                                        </ngx-datatable-column>
                                    </ngx-datatable>
                                </div>
                            </ng-template>
                        </li>
                    </ul>
                </div>
                <div [ngbNavOutlet]="nav" class="col-sm-12 pt-2 outer-border"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<i-modal #ibClReportViewContentRef [title]="selectedIbClReport?.ib_ref?.ib_title + ' - #' + selectedIbClReport?.record_ref" size="lg" [windowClass]="'xl-modal'" 
    [showCancel]="false" rightPrimaryBtnTxt="Done" (onClickRightPB)="closeModal($event)">
    <div class="scroll-wrapper">
        <iframe #ibClReportFrame id='ibClReportFrame' class="border-0" style="width: 100%;height: 100%;position: absolute;"></iframe>
    </div>
    <block-loader alwaysInCenter="true" [show]="iframe_content_loading" #ibClFrameLoader></block-loader>
</i-modal>

<i-modal #ibReviewCloseoutRef title="Review and Close Out" size="md" [showCancel]="false" rightPrimaryBtnTxt="Close" (onClickRightPB)="closeModal($event)">
        <div class="tb-attendees">
            <h6><strong> {{ (currentItem && currentItem.ib_title) ? currentItem.ib_title : 'Inspection Tour' }} #{{ selectedIbClReport?.record_ref }} - Unsatisfactory Items</strong></h6>
            <div>
                <table class="table small table-sm table-striped">
                    <thead>
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Action</th>
                    </tr>
                    </thead>
                    <tbody>
                    <ng-container *ngFor="let item of allIbClItems; trackBy : trackByRowIndex; let i = index;">
                        <tr *ngIf="negativeRating.includes((item?.answer).toLowerCase())">
                            <td style="padding-right: 0px; width:8%">{{ item?.number }}</td>
                            <td style="padding-right: 0px; padding-left: 0px; width:80%">
                                <p style="margin: 0px;font-size: 10px;"><span *ngIf="item?.category">{{item.category+ ": "}}</span>{{item.question}}</p>
                                <p style="margin: 0px;font-size: 8px;">(<i>{{([1, 2].includes(selectedIbClReport?.ib_ref?.scoring_system.type)) ? "Rating: " : "Satisfactory: "}}<span [ngStyle]="{color: getColor(item?.answer)}">{{ item?.answer | titlecase }}</span></i>)</p>
                            </td>
                            <td *ngIf="!item?.close_out?.close_out_at" style="width:12%; text-align: right;">
                                <button title="View Item Details" style="font-size: 10px;"
                                        class="btn btn-sm btn-outline-primary mr-1 p-1"
                                        (click)="viewItemDetailModal(selectedIbClReport, item, item.idx, item.checklist_name, item?.subIdx)">
                                    <i class="fa fa-search"></i></button>

                                <button title="Close Out" class="btn btn-sm btn-outline-primary p-1" style="font-size: 10px;"
                                        (click)="closeoutItemModal(selectedIbClReport, item, item.idx, item.checklist_name, item?.subIdx)">
                                    <i class="fa fa-times" aria-hidden="true"></i></button>
                            </td>
                            <td *ngIf="item?.close_out?.close_out_at" style="width:10%; text-align: center;">
                                <i class="fa fa-check-square text-success"></i>
                            </td>
                        </tr>
                    </ng-container>
                    </tbody>
                </table>
            </div>
        </div>
    <block-loader [show]="(false)" #modalLoader></block-loader>
</i-modal>

<i-modal #checklistItemDetailRef title="Item Details" size="md" cancelBtnText="OK" [rightPrimaryBtnTxt]="(!ibChecklistItem?.close_out?.close_out_at) ? 'Close Out' : ''" 
    (onClickRightPB)="closeoutItemModal(selectedIbClReport, ibChecklistItem, itemQueId, itemType, itemSubQueId)">
        <div class="text-left pb-2">
            <table class="table table-sm table-bordered mb-0">
                <tbody>
                <tr *ngIf="ibChecklistItem?.category || ibChecklistItem?.question">
                    <td class="tr-bg-dark-color w-25"> <strong>Category: </strong> </td>
                    <td> {{ ibChecklistItem.category }} <span *ngIf="ibChecklistItem?.category && ibChecklistItem?.question"> - </span> {{ ibChecklistItem?.question }} </td>
                </tr>
                <tr>
                    <td class="tr-bg-dark-color w-25"> <strong>{{([1, 2].includes(selectedIbClReport?.ib_ref?.scoring_system.type)) ? "Rating:" : "Satisfactory:"}}</strong> </td>
                    <td> <span [ngStyle]="{color: getColor(ibChecklistItem?.answer)}"> {{ ibChecklistItem?.answer | titlecase }}</span> </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem.summary">
                    <td class="tr-bg-dark-color w-25"> <strong>Summary:</strong> </td>
                    <td> {{ ibChecklistItem?.summary }} </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem.corrective_action_required">
                    <td class="tr-bg-dark-color w-25">
                        <strong *ngIf="ibChecklistItem && (!['fair', '2'].includes(ibChecklistItem?.answer))">Corrective Action Required:</strong>
                        <strong *ngIf="ibChecklistItem && (['fair', '2'].includes(ibChecklistItem?.answer))">Action Required:</strong>
                    </td>
                    <td> {{ ibChecklistItem?.corrective_action_required }} </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem.location_tag && ibChecklistItem.location_tag.lat">
                    <td class="tr-bg-dark-color w-25"> <strong>Location Tag (Lat, Long): </strong> </td>
                    <td>
                        <span style="color: blue; font-size: 14px;" (click)="openMapWithPin()" class="cursor-pointer">
                            ({{ibChecklistItem?.location_tag.lat | number : decimalConfig}}, {{ibChecklistItem?.location_tag.long | number : decimalConfig}})
                        </span>
                    </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem.tagged_user_ref && ibChecklistItem.tagged_user_ref.id">
                    <td class="tr-bg-dark-color w-25"> <strong>Assigned To:</strong> </td>
                    <td> {{ ibChecklistItem?.tagged_user_ref.name }} </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem.tagged_company_ref && ibChecklistItem.tagged_company_ref.length">
                    <td class="tr-bg-dark-color w-25"> <strong>Responsible Company:</strong> </td>
                    <td> {{ getTaggedCompanyNames(ibChecklistItem?.tagged_company_ref) }} </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem?.closeout_due_on && !time_to_closeout">
                    <td class="tr-bg-dark-color w-25"> <strong>Closeout Due Date:</strong> </td>
                    <td> {{ dayjs(ibChecklistItem?.closeout_due_on).format(AppConstant.defaultDateFormat) }} </td>
                </tr>
                <tr *ngIf="time_to_closeout">
                    <td class="tr-bg-dark-color w-25"> <strong>Severity:</strong> </td>
                    <td> {{ time_to_closeout }} </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem?.root_cause">
                    <td class="tr-bg-dark-color w-25"> <strong>Root Cause:</strong> </td>
                    <td> {{ ibChecklistItem?.root_cause }} </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem.item_closeout_images?.length">
                    <td colspan="2">
                        <div class="row">
                            <div class="col-md-4 my-1" *ngFor="let imgSrc of ibChecklistItem?.item_closeout_images">
                                <img class="img-thumbnail" [src]="imgSrc" alt="Item - Image" style="width: 100%; cursor: pointer;" (click)="viewImage(imgSrc, viewItemImgHtml)">
                            </div>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
            <!-- <p class="mb-1"><span *ngIf="ibChecklistItem?.category">{{ibChecklistItem.category+ ": "}}</span>{{ibChecklistItem?.question}}</p>
            <p class="mb-1"><strong>{{([1, 2].includes(selectedIbClReport?.ib_ref?.scoring_system.type)) ? "Rating:" : "Satisfactory:"}}</strong><span [ngStyle]="{color: getColor(ibChecklistItem?.answer)}"> {{ ibChecklistItem?.answer }}</span></p>
            <p class="mb-1" *ngIf="ibChecklistItem && ibChecklistItem.summary"><strong>Summary:</strong> {{ ibChecklistItem?.summary }}</p>
            <p class="mb-1" *ngIf="ibChecklistItem && ibChecklistItem.corrective_action_required">
                <strong *ngIf="ibChecklistItem && (!['fair', '2'].includes(ibChecklistItem?.answer))">Corrective Action Required:</strong>
                <strong *ngIf="ibChecklistItem && (['fair', '2'].includes(ibChecklistItem?.answer))">Action Required:</strong>
                {{ ibChecklistItem?.corrective_action_required }}
            </p>
            <p class="mb-1" *ngIf="ibChecklistItem && ibChecklistItem.location_tag && ibChecklistItem.location_tag.lat">
                <strong>Location Tag (Lat, Long): </strong>
                <span style="color: blue; font-size: 14px;" (click)="openMapWithPin()" class="cursor-pointer">
                    ({{ibChecklistItem?.location_tag.lat}}, {{ibChecklistItem?.location_tag.long}})
                </span>
            </p>
            <p class="mb-1" *ngIf="ibChecklistItem && ibChecklistItem.tagged_user_ref && ibChecklistItem.tagged_user_ref.id">
                <strong>Assigned To:</strong> {{ ibChecklistItem?.tagged_user_ref.name }}
            </p>
            <p class="mb-1" *ngIf="ibChecklistItem && ibChecklistItem.tagged_company_ref && ibChecklistItem.tagged_company_ref.length">
                <strong>Responsible Company:</strong> {{ getTaggedCompanyNames(ibChecklistItem?.tagged_company_ref) }}
            </p>
            <p class="mb-1" *ngIf="ibChecklistItem && ibChecklistItem?.closeout_due_on && !time_to_closeout">
                <strong>Closeout Due Date:</strong> {{ dayjs(ibChecklistItem?.closeout_due_on).format('DD-MM-YYYY') }}
            </p>
            <p class="mb-1" *ngIf="time_to_closeout">
                <strong>Severity:</strong> {{ time_to_closeout }}
            </p>
            <p class="mb-1" *ngIf="ibChecklistItem && ibChecklistItem?.root_cause">
                <strong>Root Cause:</strong> {{ ibChecklistItem?.root_cause }}
            </p>
            <div class="row" *ngIf="ibChecklistItem && ibChecklistItem.images.length">
                <div class="col-md-4" *ngFor="let t of ibChecklistItem?.images">
                    <img class="img-thumbnail" [src]="t?.sm_url || t?.file_url" alt="Item - Image" style="width: 100%; cursor: pointer;" (click)="viewImage(t?.file_url, viewItemImgHtml)">
                </div>
            </div> -->
        </div>
</i-modal>

<i-modal #ibClItemCloseOutRef [title]="'Closeout - ' + ibChecklistItem?.question" size="md" cancelBtnText="Cancel" rightPrimaryBtnTxt="Close Out" 
    (onClickRightPB)="closeOutRequest($event)" [rightPrimaryBtnDisabled]="!closeOutDetail.valid">
        <div *ngIf="showModal" class="text-left">
            <table class="table table-sm table-bordered mb-0">
                <tbody>
                <tr *ngIf="ibChecklistItem?.category || ibChecklistItem?.question ">
                    <td class="tr-bg-dark-color w-25"> <strong>Category:</strong> </td>
                    <td> {{ ibChecklistItem?.category }} <span *ngIf="ibChecklistItem?.category && ibChecklistItem?.question"> - </span> {{ibChecklistItem?.question}}</td>
                </tr>
                <tr>
                    <td class="tr-bg-dark-color w-25"> <strong>{{([1, 2].includes(selectedIbClReport?.ib_ref?.scoring_system.type)) ? "Rating:" : "Satisfactory:"}}</strong> </td>
                    <td> <span [ngStyle]="{color: getColor(ibChecklistItem.answer)}"> {{ ibChecklistItem.answer | titlecase }}</span> </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem.summary">
                    <td class="tr-bg-dark-color w-25"> <strong>Summary:</strong> </td>
                    <td class="long-text"> {{ ibChecklistItem?.summary }} </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem.corrective_action_required">
                    <td class="tr-bg-dark-color w-25">
                        <strong *ngIf="ibChecklistItem && (!['fair', '2'].includes(ibChecklistItem?.answer))">Corrective Action Required:</strong>
                        <strong *ngIf="ibChecklistItem && (['fair', '2'].includes(ibChecklistItem?.answer))">Action Required:</strong>
                    </td>
                    <td> {{ ibChecklistItem?.corrective_action_required }} </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem.location_tag && ibChecklistItem.location_tag.lat">
                    <td class="tr-bg-dark-color w-25"> <strong>Location Tag (Lat, Long): </strong> </td>
                    <td>
                        <span style="color: blue; font-size: 14px;" (click)="openMapWithPin()" class="cursor-pointer">
                            ({{ibChecklistItem?.location_tag.lat | number : decimalConfig}}, {{ibChecklistItem?.location_tag.long | number : decimalConfig}})
                        </span>
                    </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem.tagged_user_ref && ibChecklistItem.tagged_user_ref.id">
                    <td class="tr-bg-dark-color w-25"> <strong>Assigned To:</strong> </td>
                    <td> {{ ibChecklistItem?.tagged_user_ref.name }} </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem.tagged_company_ref && ibChecklistItem.tagged_company_ref.length">
                    <td class="tr-bg-dark-color w-25"> <strong>Responsible Company:</strong> </td>
                    <td> {{ getTaggedCompanyNames(ibChecklistItem?.tagged_company_ref) }} </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem.closeout_due_on && !time_to_closeout">
                    <td class="tr-bg-dark-color w-25"> <strong>Closeout Due Date:</strong> </td>
                    <td> {{ dayjs(ibChecklistItem.closeout_due_on).format(AppConstant.defaultDateFormat) }} </td>
                </tr>
                <tr *ngIf="time_to_closeout">
                    <td class="tr-bg-dark-color w-25"> <strong>Severity:</strong> </td>
                    <td> {{ time_to_closeout }} </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem?.root_cause">
                    <td class="tr-bg-dark-color w-25"> <strong>Root Cause:</strong> </td>
                    <td> {{ ibChecklistItem?.root_cause }} </td>
                </tr>
                <tr *ngIf="ibChecklistItem && ibChecklistItem.item_closeout_images?.length">
                    <td colspan="2">
                        <div class="row">
                            <div class="col-md-4" *ngFor="let imgSrc of ibChecklistItem.item_closeout_images">
                                <img class="img-thumbnail" [src]="imgSrc" alt="Item - Image" style="width: 100%; cursor: pointer;" (click)="viewImage(imgSrc, viewItemImgHtml)">
                            </div>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
            <!-- <p class="mb-1" *ngIf="ibChecklistItem?.category"><strong>Category:</strong> {{ ibChecklistItem?.category }}</p>
            <p class="mb-1"><strong>{{([1, 2].includes(selectedIbClReport?.ib_ref?.scoring_system.type)) ? "Rating:" : "Satisfactory:"}}</strong><span [ngStyle]="{color: getColor(ibChecklistItem.answer)}"> {{ ibChecklistItem.answer }}</span></p>
            <p class="mb-1" *ngIf="ibChecklistItem && ibChecklistItem.summary"><strong>Summary:</strong> {{ ibChecklistItem?.summary }}</p>
            <p class="mb-1" *ngIf="ibChecklistItem && ibChecklistItem.corrective_action_required">
                <strong *ngIf="ibChecklistItem && (!['fair', '2'].includes(ibChecklistItem?.answer))">Corrective Action Required:</strong>
                <strong *ngIf="ibChecklistItem && (['fair', '2'].includes(ibChecklistItem?.answer))">Action Required:</strong>
                {{ ibChecklistItem?.corrective_action_required }}
            </p>
            <p class="mb-1" *ngIf="ibChecklistItem && ibChecklistItem.location_tag && ibChecklistItem.location_tag.lat">
                <strong>Location Tag (Lat, Long): </strong>
                <span style="color: blue; font-size: 14px;" (click)="openMapWithPin()" class="cursor-pointer">
                    ({{ibChecklistItem?.location_tag.lat}}, {{ibChecklistItem?.location_tag.long}})
                </span>
            </p>
            <p class="mb-1" *ngIf="ibChecklistItem && ibChecklistItem.tagged_user_ref && ibChecklistItem.tagged_user_ref.id">
                <strong>Assigned To:</strong> {{ ibChecklistItem?.tagged_user_ref.name }}
            </p>
            <p class="mb-1" *ngIf="ibChecklistItem && ibChecklistItem.tagged_company_ref && ibChecklistItem.tagged_company_ref.length">
                <strong>Responsible Company:</strong> {{ getTaggedCompanyNames(ibChecklistItem?.tagged_company_ref) }}
            </p>
            <p class="mb-1" *ngIf="ibChecklistItem && ibChecklistItem.closeout_due_on && !time_to_closeout">
                <strong>Closeout Due Date:</strong> {{ dayjs(ibChecklistItem.closeout_due_on).format('DD-MM-YYYY') }}
            </p>
            <p class="mb-1" *ngIf="time_to_closeout">
                <strong>Severity:</strong> {{ time_to_closeout }}
            </p>
            <p class="mb-1" *ngIf="ibChecklistItem && ibChecklistItem?.root_cause">
                <strong>Root Cause:</strong> {{ ibChecklistItem?.root_cause }}
            </p>
            <div class="row" *ngIf="ibChecklistItem && ibChecklistItem.images.length">
                <div class="col-md-4" *ngFor="let t of ibChecklistItem?.images">
                    <img class="img-thumbnail" [src]="t?.sm_url || t?.file_url" alt="Item - Image" style="width: 100%; cursor: pointer;" (click)="viewImage(t?.file_url, viewItemImgHtml)">
                </div>
            </div> -->
        </div>
        <div class="mb-2 mt-1">
            <p class="mb-1"><strong>Details</strong> <small class="required-asterisk">*</small></p>
            <textarea class="form-control" name="closeout_detail"
                      [(ngModel)]="closeoutDetail"
                      ng-value="closeout_detail"
                      placeholder="Closeout detail"
                      #closeOutDetail="ngModel" required></textarea>
            <div class="alert alert-danger" [hidden]="closeOutDetail.valid">Closeout detail is required</div>
        </div>
        <div class="col-md-12 p-0 mt-3">
            <p class="mb-1"><strong>Upload Files</strong></p>
        </div>
        <div class="col-md-12 p-0 mb-0">
            <div *ngFor="let c of closeoutImgs" class="flex-grow-1 p-0">
                <file-uploader-v2
                    [disabled]="false"
                    [init]="c"
                    [category]="'close-out'"
                    (uploadDone)="mediaUploadDone($event)"
                    [allowedMimeType]="allowedMime"
                    [dragnDropTxt]="'Drag and drop image or pdf here'"
                    [showFileName]="false"
                    [hasImgAndDoc]="true"
                    #attachmentUploader
                    (deleteFileDone)="fileDeleteDone($event)"
                    [showDeleteBtn]="true" [multipleUpload]="true"
                >
                </file-uploader-v2>
            </div>
        </div>
</i-modal>
<block-loader [show]="(closeoutLoading)" [showBackdrop]="true" alwaysInCenter="true"></block-loader>

<i-modal #viewItemImgHtml size="lg" [showCancel]="false" [showFooter]="false">
        <div class="form-group row">
            <div class="col-sm-12 mt-2 text-center">
                <img [src]="itemImgSrc" (error)="onLogoError(img, itemImgSrc)" (load)="onLogoLoad(img)" style="width: 100%; height: auto;" #img />
            </div>
        </div>
</i-modal>

<app-generic-modal #inspectionPinMapHtml [genericModalConfig]="inspectionPinMapConfig">
    <ng-container *ngTemplateOutlet="cc"></ng-container>
    <ng-template #cc>
        <progress-photo-location-map
        [showMapWithMultiPin]="false"
        [row_data]="itemMapData"
        [project_data]=""
        feature="inspections"
    >
    </progress-photo-location-map>
    </ng-template>
</app-generic-modal>

<ng-template #dashboardHtml let-c="close" let-d="dismiss">
    <div class="modal-body p-1">
        <div ngbDropdown class="d-inline-block col-md-12 p-0" placement="bottom-right" [autoClose]="'outside'" #myDrop="ngbDropdown" style="margin-top: 4px;">
            <div class="float-right d-flex align-items-sm-center">
                <button style="width: 250px;" class="btn btn-sm dropdownBtnMakeup" id="dropdownDlReport1" ngbDropdownToggle>
                    <i class="far fa-calendar"></i>
                    <span>{{ dayjs(this.dashboardReportFrom).format(AppConstant.dateFormat_Do_MMM_YYYY) }} - {{ dayjs(this.dashboardReportTo).format(AppConstant.dateFormat_Do_MMM_YYYY) }}</span>
                    <i class="fa fa-caret-down" aria-hidden="true"></i>
                </button>
                <div ngbDropdownMenu aria-labelledby="dropdownDlReport1">
                    <download-report-modal-box [actionBtnText]="'Update Report'" [xlsxOnly]="true" [fromDate]="dashboardStartDate" (downloadModal)="updateDashboard($event, myDrop)"></download-report-modal-box>
                </div>
                <button type="button" class="close mb-2 ml-3 mr-1" aria-label="Close" (click)="d('Cross click')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        </div>

        <div [innerHtml]="dashboardHtmlContent" style="min-height: 450px;"></div>
    </div>
    <div class="modal-footer" style="border: none;">
        <button title="Download Dashboard" class="btn btn-outline-success"
                (click)="downloadDashboardPdf()">Download</button>
        <button type="button" class="btn btn-outline-primary" (click)="d('Cross click')">OK</button>
    </div>
</ng-template>

<app-generic-modal #inspectBuildModalRef [genericModalConfig]="insPectBuildMapConfig">
    <div *ngIf="showInspectionForm">
        <inspection-builder
            [currentInspectionItem]="builderObj.currentInspectionItem"
            [selectedHeading]="builderObj.selectedHeading"
            [headings]="builderObj.headings"
            [selectedTabIdInput]="selectedTabId"
            [inductedUsersEmployerData]="inductedUsersEmployer"
            [severitiesData]="builderObj.severities"
            [severityMandatory]="builderObj.severityMandatory"
            [rootCauseMandatory]="builderObj.rootCauseMandatory"
            [rootCausesData]="builderObj.rootCauses"
            [draftData]="draftData"
            [isDraftAvailable]="draftData.length > 0"
            [inspectionLoader]="draftInspectionLoading"
            (isCloseEvent)="closeInspectionModal($event)"
            (updateModalSize)="updateNewReportModalSize($event)"
            [headerClose]="headerClose"
            (headerCloseChanged)="headerClose = $event"
            [projectId]="projectId"
            [employerId]="employerId"
            [isProjectPortal]="isProjectPortal"
            (getAllDraft)="getDraftInspections(); updateDraft = true"
            [updateDraft]="updateDraft"
            [projectCountryCode]="projectInfo.custom_field?.country_code"
            [projectTimezone]="projectInfo.custom_field?.timezone"
        >
        </inspection-builder>
    </div>
</app-generic-modal>

<block-loader [show]="(blockLoader || draftInspectionLoading || loading)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>

<share-tool-report-to-email
    [projectId]="projectId" [employerId]="employerId" #shareIBReportModal
    [tool_name]="'Share '+(currentItem && currentItem.ib_title) ? currentItem.ib_title : 'Inspection'" (onSave)="shareInspectionBuilderReport($event)">
</share-tool-report-to-email>

<power-bi-dashboard *ngIf="loadPowerBiComponent" (powerBiDashboardClose)="powerBiDashboardClose()"  [toolLabel]="biToolLabel" [toolName]="biToolName" [toolId]="selectedTabId"></power-bi-dashboard>

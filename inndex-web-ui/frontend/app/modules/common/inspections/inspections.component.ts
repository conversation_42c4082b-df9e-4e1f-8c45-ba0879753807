import {Component, ElementRef, OnInit, TemplateRef, ViewChild, AfterViewInit, ChangeDetectorRef } from "@angular/core";
import {ActivatedRoute, Router} from "@angular/router";
import {NgbModalConfig, NgbModal, NgbDate } from '@ng-bootstrap/ng-bootstrap';
import {
    AuthService,
    isInheritedProjectOfCompany,
    Project,
    ProjectService,
    User,
    InspectionBuilder,
    InspectionBuilderService, Common, HttpService, UserService, filterData, ProjectInspectionTourService,
    ToastService
} from "@app/core";
import {fromEvent} from "rxjs";
import {debounceTime, distinctUntilChanged, filter, tap} from "rxjs/operators";
import * as dayjs from 'dayjs';
import {HttpParams} from "@angular/common/http";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import {DomSanitizer} from "@angular/platform-browser";
import { ShareToolReportToEmailComponent } from "../share-tool-report-to-email/share-tool-report-to-email.component";
import {AppConstant} from "@env/environment";
import { InspectionBuilderComponent } from "../inspection-builder/inspection-builder.component";
import { GenericModalComponent } from "../generic-modal/generic-modal.component";
import { GenericModalConfig } from "../generic-modal/generic-modal.config";
import { ActionBtnEntry, IModalComponent } from "@app/shared";
import {innDexConstant} from "@env/constants";
import { AnalyticsService } from "@app/core/services/analytics.service";

@Component({
    selector: 'inspections',
    templateUrl: './inspections.component.html',
    providers: [NgbModalConfig, NgbModal],
})

export class InspectionsComponent implements OnInit, AfterViewInit {

    builderObj: any = {
        inspectionAssessmentMainModalRef: '',
        filteredInductedUsersEmployer: [],
        filteredInductedUsers: [],
        tempSelectedCompanyNames: [],
        severities: [],
        severityMandatory: false,
        rootCauseMandatory: false,
        rootCauses: [],
        headings: [],
        currentInspectionItem: {},
    };
    employer: any = {};
    projects: Array<Project> = [];
    inductedUsersEmployer: any = [];
    projectsLoading: boolean = false;
    employerId: number = 0;
    authUser$: User;
    loadInspectionTours: boolean = false;
    loadingChildComponent: boolean = true;
    loadInspectionBuilder: boolean = false;
    is_inherited_project: boolean = false;
    activeId = 'inspection_tour';
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    temp: Array<InspectionBuilder> = [];
    tableOffset: number = 0;
    selectedTabId: number;
    pageNumber = 0;
    pageSize = 50;
    finalised = false;
    selectedIbCl;
    inspectionReports: Array<any> = [];
    iframe_content_loading: boolean = false;
    loading: boolean = false;
    selectedIbClReport;
    ibChecklistItem;
    ibChecklist: any = {};
    time_to_closeout: string = '';
    allIbClItems;
    hasUnsatisfactory: boolean = false;
    unsatisfactoryColumnTxt: string = '';
    defaultNegativeRating: Array<any> = ['no','poor', 'fair', '1', '2'];
    negativeRating: Array<any> = [];
    viewChecklistItemModalRef: any = null;
    managementReviewModalRef: any = null;
    itemType: string = '';
    itemQueId: any = null;
    itemSubQueId: any = null;
    closeoutImgs:  Array<any> = [{}];
    closeoutDetail: string = '';
    allowedMime : Array<any> = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    closeoutLoading: boolean = false;
    itemImgSrc: string = '';
    currentItem: InspectionBuilder = {ib_title: 'Inspection Tour'};
    colCount: number = 1;
    inspectionTitlesChunk: Array<any> = [];
    pagination = new Common();
    page = this.pagination.page;
    private _ib_checklists: Array<InspectionBuilder> = [];
    search: string;
    public get ibChecklists(): Array<InspectionBuilder> { return this._ib_checklists; }
    public set ibChecklists(ib_checklists: Array<InspectionBuilder>) {
        this._ib_checklists = ib_checklists;
    }
    itemMapData: any = {};
    filteredinspectionReports: Array<any> = [];
    AppConstant = AppConstant;
    dashboardStartDate: NgbDate;
    blockLoader: boolean = false;
    dashboardHtmlContent: any;
    dashboardReportFrom: any;
    dashboardReportTo: any;
    isDashboardActivated: boolean = false;
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    isProjectPortal: boolean = false;
    decimalConfig = AppConstant.decimelConfig;
    isMobileDevice: boolean = false;
    draftInspectionLoading: boolean = false;
    draftData: any[] = [];
    showInspectionForm = false;
    headerClose = false
    updateDraft = false
    loadPowerBiComponent: boolean = false;
    biToolName: string = '';
    biToolLabel: string = '';
    showModal: boolean = false;
    isOpen: boolean = false;
    isInitInspectionBuilder: boolean = false;
    loadingInlineInspectionBuilder: boolean = false;
    isStaging: boolean = innDexConstant.isStaging;
    rowButtonGroup: Array<ActionBtnEntry> = [
        {
            key: 'view_inspection',
            label: '',
            title: 'View Incident Details',
            mat_icon: 'search',
        },
        {
            key: 'download_inspection',
            label: '',
            title: 'Download Inspection Report',
            mat_icon: 'download',
        },
        {
            key: 'share_inspection',
            label: '',
            title: 'Share Inspection Details',
            mat_icon: 'share',
        },
        {
            key: 'close_inspection',
            label: '',
            title: 'Close Out',
            mat_icon: 'close',
        },
    ];
    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private authService: AuthService,
        private userService: UserService,
        private projectService: ProjectService,
        private inspecBuildService: InspectionBuilderService,
        private modalService: NgbModal,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private domSanitizer: DomSanitizer,
        private httpService: HttpService,
        private projectInspectionTourService: ProjectInspectionTourService,
        private cdRef: ChangeDetectorRef,
        private toastService: ToastService,
        private analytics: AnalyticsService,
    ) {
        router.events.subscribe((val) => {
            let viewId = this.activatedRoute.snapshot.queryParams['view'];
            let selectedIbCl = this.activatedRoute.snapshot.queryParams['selected_ibcl'];
            if(viewId) { this.activeId = viewId; }
            if(selectedIbCl) { this.selectedTabId = selectedIbCl; }
        });

        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
        }
    }

    dayjs(n: number) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    }

    ngOnInit() {
        if(!this.isProjectPortal) {
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
                this.employerId = params['employerId'];
            });
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        } else {
            this.initClassicInspections();
        }
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });

        //List of company to tag in close call
        this.userService.getProjectInductionsUsersEmployer(this.projectId, 2).subscribe((data: any) => {
            if(data.success && data.users_employer){
                this.inductedUsersEmployer = data.users_employer;
            } else {
                const message = data.message || 'Failed to get companies of the users who have had an approved induction on the project.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });

        this.biToolName = 'inspections';
        this.biToolLabel = this.currentItem.ib_title;
    }

    getDraftInspections(): Promise<void> {
        const params = new HttpParams()
            .set('pageNumber', `${this.pageNumber}`)
            .set('pageSize', `${this.pageSize}`)
            .set('finalised', `${this.finalised}`);

        return new Promise<void>((resolve, reject) => {
            this.inspecBuildService.getIbReportsDrafts(this.projectId, this.selectedTabId, params).subscribe({
                next: (data: any) => {
                    this.draftInspectionLoading = false;
                    if (data.success && data.project_ib_reports) {
                        this.draftData = data.project_ib_reports;
                        this.updateDraftData();
                        resolve();
                    } else {
                        const message = data.message || 'Failed to get list of inducted users.';
                        this.toastService.show(this.toastService.types.ERROR, message);
                        reject();
                    }
                },
                error: (error) => {
                    this.draftInspectionLoading = false;
                    const message = 'An error occurred while fetching draft inspections.';
                    this.toastService.show(this.toastService.types.ERROR, message, { data: error });
                    reject(error);
                }
            });
        });
    }

    updateDraftData() {
        this.draftData = this.draftData.map(({ checklist, ...rest }) => {
            if(rest.participants?.length){
                rest.participants = rest.participants.map((ref)=> typeof ref !== "number" ? ref.user_ref : ref)
            }
            if (rest.additional_checklist?.length) {
                rest["additional_checklist"] = rest.additional_checklist.map((addList) => {
                    let { subheadings } = addList;
                    if (subheadings?.length) {
                        subheadings = subheadings.map((subHead) => 
                            typeof subHead.item_id !== 'string' ? {...subHead, item_id: `OTH-${subHead.item_id}`} : subHead
                        );
                        addList.subheadings = subheadings;
                    } else {
                        addList.item_id = typeof addList.item_id !== 'string' ? `OTH-${addList.item_id}` : addList.item_id;
                    }
                    return addList;
                });
            }            
            if (rest.has_subheadings) {
                let updatedChecklist = []
                if(!checklist.length) {
                    checklist = rest.additional_checklist
                    updatedChecklist = checklist
                } else {
                    updatedChecklist = checklist.map((list) => {
                        const additionalListSubheadings = rest.additional_checklist.find(({ heading }) => list.heading == heading)?.subheadings;
                        if (additionalListSubheadings && additionalListSubheadings.length) {
                            list.subheadings = [...list.subheadings, ...additionalListSubheadings,];
                        }
                        return list;
                    });
                }
                const changePropertiesValueChecklist = updatedChecklist?.map(({ subheadings, ...checkListRest }) => {
                    const updatedSubheadings = subheadings?.map(({ closeout_due_on, images, ...subRest }) => {
                        const dateObj = new Date(closeout_due_on);
                        const closeoutDueOn = {
                            year: dateObj.getFullYear(),
                            month: dateObj.getMonth() + 1,
                            day: dateObj.getDate(),
                        };
                        if(subRest.severity){
                            subRest = ({...subRest, severity: subRest.severity})
                        }
                        if(!subRest.tagged_user_ref){
                            subRest = ({...subRest, tagged_user_ref: null})
                        }
                        const updateSubHeading = {
                            ...subRest, 
                            closeout_due_on: closeout_due_on ? closeoutDueOn : null,
                            images: images.map((image) => {
                                const fileExtension = image.file_url.split('.').pop();
                                return { ...image, file_mime: `image/${fileExtension}`, };
                            }),
                        }
                        return updateSubHeading;
                    })
                    return {
                        ...checkListRest, subheadings: updatedSubheadings 
                    };
                })
                return {
                    ...rest, checklist: changePropertiesValueChecklist,
                };
            } else {
                checklist = [...checklist, ...rest.additional_checklist]
                return {
                    ...rest,
                    checklist: checklist?.map(({  closeout_due_on, ...checkListRest }) => {
                        const dateObj = new Date(closeout_due_on);
                        const closeoutDueOn = {
                            year: dateObj.getFullYear(),
                            month: dateObj.getMonth() + 1,
                            day: dateObj.getDate(),
                        };
                        if(checkListRest.severity){
                            checkListRest = ({...checkListRest, severity: checkListRest.severity})
                        }
                        if(!checkListRest.tagged_user_ref){
                            checkListRest = ({...checkListRest, tagged_user_ref: null})
                        }
                        return {
                            ...checkListRest,
                            closeout_due_on: closeout_due_on ? closeoutDueOn : null,
                        };
                    }),
                };
            }

        });
    }

    ngAfterViewInit() {
        this.cdRef.detectChanges();
    }

    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean) {
        if (!this.isInitInspectionBuilder) {
          this.isInitInspectionBuilder = true;
          return;
        }
        this.page.pageNumber = pageInfo.offset;
        this.loadInspectionReports(this.selectedTabId, isPageChange);
    }

    getTaggedCompanyNames(taggedCompanies){
        return (taggedCompanies || []).map(company => company.name).join(',');
    }

    tabChange(id) {
        this.loading = false;
        this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { view: id, selected_ibcl: null }, queryParamsHandling: 'merge' });
    }

    // Will get called for Company route only.
    projectRetrieved(project: Project){
        this.projectInfo = project;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
        this.initClassicInspections();
    }

    initClassicInspections() {
        //initializing default child component
        if (this.isFeatureEnable('inspection_tour')) {
            this.loadInspectionTours = (this.isFeatureEnable('inspection_tour') && !this.selectedTabId);
            this.ibChecklists = [...[{ib_title: 'Inspection Tour'}], ...this.ibChecklists];
        }
        this.temp = this.ibChecklists;

        this.loadInspectionBuilder = (!this.loadInspectionTours);
        this.activeId = (!this.loadInspectionTours) ? 'inspection_builder' : this.activeId;
        this.initIbChecklists();
    }

    isChildComponentLoaded($event) {
        this.loading = false;
        this.loadingChildComponent = $event;
    }

    loadInspectionToursComponent() {
        if (!this.loadInspectionTours) {
            this.resetOtherInspectionsFlag();
            this.loadInspectionTours = true;
        }
    }

    resetOtherInspectionsFlag() {
        this.loadingChildComponent = true;
        this.loadInspectionTours = false;
    }

    isFeatureEnable(feature) {
        return this.projectInfo.project_section_access[feature];
    }

    private initIbChecklists(isPageChange?: boolean) {
        if (isPageChange) {
          this.loadingInlineInspectionBuilder = true;
        } else {
          this.loading = true;
        }
        this.inspecBuildService.getProjectIbCl(this.projectId, this.employerId, {'all': 'false'}).subscribe((data:any) => {
            this.loading = false;
            this.loadingInlineInspectionBuilder = false;
            if (data && data.success) {
                this.ibChecklists = (data.project_ib_checklists || []).filter(r => r.enabled);
                if (!this.loadInspectionTours && !this.selectedTabId && this.ibChecklists.length > 0) {
                    let firstIb = this.ibChecklists[0];
                    this.selectedTabId = firstIb.id;
                }

                if(this.selectedTabId) {
                    const ibChecklistObj = (this.ibChecklists || []).find(cl => cl.id == this.selectedTabId);
                    if(ibChecklistObj){
                        this.builderObj.currentInspectionItem = ibChecklistObj ? JSON.parse(JSON.stringify(ibChecklistObj)) : {};
                        this.currentItem = ibChecklistObj;
                        this.loadInspectionReports(this.selectedTabId);
                    } else {
                        this.changeContent({ib_title: 'Inspection Tour'});
                    }
                }

                if(this.isFeatureEnable('inspection_tour')) { this.ibChecklists = [...[{ib_title: 'Inspection Tour'}], ...this.ibChecklists]; }
                this.temp = this.ibChecklists;
                this.ibChecklists = this.ibChecklists.sort((a, b) => a.ib_title.localeCompare(b.ib_title));
                return data.project_ib_checklists;
            }
            const message = `Failed to fetch Inspection Builder Checklists, For project: ${this.projectId}.`;
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            return [];
        });
    }

    filterTabs(textElem: any = '') {
        let searchText = (textElem || '').toLowerCase();
        console.log(`filter by text: ${searchText}`);
        this.ibChecklists = this.temp.filter(function (d) {
            return (!searchText || (d.ib_title && d.ib_title.toLowerCase().indexOf(searchText) !== -1));
        });
        return this.ibChecklists;
    }

    changeContent(item) {
        this.builderObj.currentInspectionItem = item;
        this.inspectionReports = [];
        this.page.pageNumber = 0;
        if(!['Inspection Tour'].includes(item.ib_title) && +item.id){
            this.activeId = 'inspection_builder';
            this.loadInspectionBuilder = true;
            this.loading = true;
            this.createHeadings();
            this.loadInspectionReports(+item.id);
        } else if (item.ib_title && item.ib_title == 'Inspection Tour') {
            this.loading = true;
            this.isDashboardActivated = false;
            this.activeId = 'inspection_tour';
            this.loadInspectionBuilder = false;
            this.loadInspectionTours = true;
            this.tabChange(this.activeId);
            this.loadInspectionToursComponent();
        }
    }

    @ViewChild('inspectBuildModalRef') private inspectBuildModalRef: GenericModalComponent;
    openNewInspectionBuilderModal() {
        this.builderObj.currentInspectionItem.checklist = this.builderObj.currentInspectionItem.checklist.sort((a, b) => a.item_id - b.item_id)
        this.draftInspectionLoading = true;
        this.showInspectionForm = true;
        this.headerClose = false;
        this.createHeadings();
        this.analytics.start_session_recording();
        this.getDraftInspections().then(() => {
            this.insPectBuildMapConfig.modalTitle = `New Report - ${this.builderObj.currentInspectionItem.ib_title}`;
            this.inspectBuildModalRef.open();
        });
        return true;
    }

    public insPectBuildMapConfig: GenericModalConfig = {
        modalTitle: '',
        hideFooter: true,
        bodyClass: {
            overflow: 'unset',
            padding: '0px',
        },
        closeTrailingBtnAction: () => {
            this.headerClose = true;
            return !this.showInspectionForm;
        },
        modalOptions: {
            size: 'lg',
            windowClass: 'checklist-class'
        },
    };

    /**
     * create heading for custom-dropdown
     */
    private createHeadings(): void {
        this.builderObj.selectedHeading = (this.builderObj.currentInspectionItem.has_subheadings) ? 'Content' : 'Details';
        this.builderObj.currentInspectionItem['report_datetime'] = null;
        if(this.builderObj.currentInspectionItem.has_subheadings){
            this.builderObj.headings = (this.builderObj.currentInspectionItem.checklist || []).map(({id, heading}) => ({id, heading, isChecked: true}));
        } else {
            this.builderObj.headings = [{id: 1, heading: 'Checklist', isChecked: true}];
        }
        if( this.builderObj.currentInspectionItem.severity && this.builderObj.currentInspectionItem.severity.has_severity){
            this.builderObj.severities = (this.builderObj.currentInspectionItem.severity?.options || []).map(element => ({...element, rating: element.option}));
            this.builderObj.severityMandatory = this.builderObj.currentInspectionItem.severity?.is_mandatory ;
        }
        if( this.builderObj.currentInspectionItem.root_cause && this.builderObj.currentInspectionItem.root_cause.has_root_cause){
            const rootCauseData = this.builderObj.currentInspectionItem.root_cause.options;
            this.builderObj.rootCauses = (rootCauseData || []).filter(root => root.is_active);
            this.builderObj.rootCauseMandatory = this.builderObj.currentInspectionItem.root_cause?.is_mandatory ;
        }
    }

    public closeInspectionModal(event: string | boolean) {
        this.showInspectionForm = false;
        this.updateDraft = false
        this.inspectBuildModalRef.close();
        this.analytics.stop_session_recording();
        if (event == 'isFromSuccess'){
            this.loadInspectionReports(+this.selectedTabId, true);
            this.initIbChecklists(true);
        }
    }

    loadInspectionReports(ibClId: number, isPageChange?: boolean) {
        if (isPageChange) {
          this.loadingInlineInspectionBuilder = true;
        }
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`);

        if(this.search){
            params = params.append('q', this.search);
        }
        this.inspecBuildService.getIbClReportsById(this.projectId, ibClId, this.employerId, params).subscribe((data:any) => {
            this.loadingInlineInspectionBuilder = false;
            if (data && data.project_ib_reports) {

                this.isDashboardActivated = false;
                if (data.project_ib_reports && data.project_ib_reports.length && data.project_ib_reports[0].ib_ref) {
                    this.processInspectionBuilder(data.project_ib_reports[0].ib_ref);
                }

                this.inspectionReports = data.project_ib_reports;
                this.filteredinspectionReports = data.project_ib_reports;
                this.prepareNegativeRating(this.inspectionReports);
                this.page.totalElements = data.total_record_count;
                this.loading = false;
                return data.project_ib_reports;
            }
            const message = `Failed to fetch Checklist Inspections, id: ${ibClId}.`;
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            return [];
        });
        this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { selected_ibcl: ibClId, view: 'inspection_builder' }, queryParamsHandling: 'merge' });
    }

    @ViewChild('ibClReportViewContentRef')
    private ibClReportViewContentRef: IModalComponent;
    viewIbClReport(row) {
        this.selectedIbClReport = row;
        this.ibClReportViewContentRef.open();
        this.iframe_content_loading = true;
        this.getIbClReport(row, 'html');
    }

    getIbClReport(row, target = 'pdf', cb = (data) => {}){
        let body = {
            createdAt: +row.createdAt,
            companyId: this.employerId ? this.employerId : null,
            type: target
        };

        if(target === 'pdf') {
            this.closeoutLoading = true;
            this.inspecBuildService.downloadIbClReport(this.projectId, body, row.id, () => {
                this.closeoutLoading = false;
            });
        } else {
            this.inspecBuildService.downloadIbClReport(this.projectId, body, row.id).subscribe((html:any) => {
                let iframe = document.getElementById('ibClReportFrame') as HTMLIFrameElement;
                let doc = iframe.contentDocument;
                doc.write(html);
                this.iframe_content_loading = false;
            });
        }
    }

    openModal(content, size='', windowClass='') {
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size,
            windowClass: windowClass
        });
    }

    @ViewChild('viewItemImgHtml') private viewItemImgHtml: IModalComponent;
    viewImage(imgSrc) {
        this.itemImgSrc = imgSrc;
        this.viewItemImgHtml.open();
    }

    getOpenText(row) {
        let totalUnsatisfactory = 0;
        let totalOpenUnsatisfactory = 0;

        if(row.has_subheadings){
            let self = this;
            row.checklist.forEach(function (items) {
                totalUnsatisfactory += ((items.subheadings || []).filter(item => ((self.negativeRating.includes((item.answer)?.toLowerCase()))))).length;
                totalOpenUnsatisfactory += ((items.subheadings || []).filter(item => ((self.negativeRating.includes((item.answer)?.toLowerCase())) && !(item.close_out && item.close_out.close_out_at)))).length;
            });
            row.additional_checklist.forEach(function (items) {
                totalUnsatisfactory += ((items.subheadings || []).filter(item => ((self.negativeRating.includes((item.answer)?.toLowerCase()))))).length;
                totalOpenUnsatisfactory += ((items.subheadings || []).filter(item => ((self.negativeRating.includes((item.answer)?.toLowerCase())) && !(item.close_out && item.close_out.close_out_at)))).length;
            });
        } else {
            totalUnsatisfactory += ((row.checklist || []).filter(item => ((this.negativeRating.includes((item.answer)?.toLowerCase()))))).length;
            totalOpenUnsatisfactory += ((row.checklist || []).filter(item => ((this.negativeRating.includes((item.answer)?.toLowerCase())) && !(item.close_out && item.close_out.close_out_at)))).length;

            totalUnsatisfactory += ((row.additional_checklist || []).filter(item => ((this.negativeRating.includes((item.answer)?.toLowerCase()))))).length;
            totalOpenUnsatisfactory += ((row.additional_checklist || []).filter(item => ((this.negativeRating.includes((item.answer)?.toLowerCase())) && !(item.close_out && item.close_out.close_out_at)))).length;
        }

        if (!totalOpenUnsatisfactory && !totalUnsatisfactory) {
            this.hasUnsatisfactory = false;
            this.unsatisfactoryColumnTxt = `None`;
            return;
        } else if (!totalOpenUnsatisfactory) {
            this.hasUnsatisfactory = false;
            this.unsatisfactoryColumnTxt = `All Closed`;
            return;
        }
        this.hasUnsatisfactory = true;
        this.unsatisfactoryColumnTxt = `Open: ${totalOpenUnsatisfactory}/${totalUnsatisfactory}`;
        return;
    }

    prepareAllIbClItems() {
        let allIbClItems = [];
        let number = 1;
        if(!this.selectedIbClReport.has_subheadings) {
            this.selectedIbClReport.checklist.forEach(function (element, i) {
                element.checklist_name = "checklist";
                element.number = number;
                element.idx = i;
                allIbClItems.push(element);
                number++;
            });
            this.selectedIbClReport.additional_checklist.forEach(function (element, i) {
                element.checklist_name = "additional_checklist";
                element.number = number;
                element.idx = i;
                allIbClItems.push(element);
                number++;
            });
        } else {
            this.selectedIbClReport.checklist.forEach(function (headItem, i) {
                headItem.subheadings.forEach(function (element, j) {
                    element.checklist_name = "checklist";
                    element.category = headItem.heading;
                    element.idx = i;
                    element.subIdx = j;
                    element.number = `${number}.${(j+1)}`;
                    allIbClItems.push(element);
                });
                number++;
            });
            this.selectedIbClReport.additional_checklist.forEach(function (headItem, i) {
                headItem.subheadings.forEach(function (element, j) {
                    element.checklist_name = "additional_checklist";
                    element.category = headItem.heading;
                    element.idx = i;
                    element.subIdx = j;
                    element.number = `${number}.${(j+1)}`;
                    allIbClItems.push(element);
                });
                number++;
            });
        }
        this.allIbClItems = [...allIbClItems];
    }

    hasUnsatisfactoryItems(row) {
        let unsatisfactoryItems = [];
        if(row.has_subheadings){
            let self = this;
            [...row.checklist, ...row.additional_checklist].forEach(function (items) {
                unsatisfactoryItems.push(...(items.subheadings || []).filter(item => ((self.negativeRating.includes((item.answer)?.toLowerCase())) && !(item.close_out && item.close_out.close_out_at))));
            });
        } else {
            unsatisfactoryItems = ([...row.checklist, ...row.additional_checklist].filter(item => ((this.negativeRating.includes((item.answer)?.toLowerCase())) && !(item.close_out && item.close_out.close_out_at))));
        }

        return unsatisfactoryItems.length;
    }

    getColor(rating) {
        if (['no', 'poor', '1'].includes(rating)) {
            return "red";
        } else if (['fair', '2'].includes(rating)) {
            return "orange";
        }
    }

    trackByRowIndex(index: number, obj: any){
        return index;
    }

    addMoreImageRow() {
        this.closeoutImgs.push({
            identifier: (new Date()).getTime()
        });
    }

    logoImgRetries:number = 5;

    onLogoError($img:any, targetSrc:any) {
        $img.src = '/images/project-placeholder.png';
        if(targetSrc && targetSrc.length && this.logoImgRetries) {
            setTimeout(() => {
                this.logoImgRetries--;
                $img.src = targetSrc;
            }, 2000);
        }
    }

    onLogoLoad($img) {
        this.logoImgRetries = 5;
    }

    mediaUploadDone($event) {
        this.closeoutImgs.splice(1, 0,...$event.userFile);
        this.closeoutImgs[0] = {};
    }

    fileDeleteDone($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.closeoutImgs = this.closeoutImgs.filter(r => (r.id !== $event.userFile.id));
        }
    }

    @ViewChild('ibReviewCloseoutRef')
    private ibReviewCloseoutRef: IModalComponent;
    managementReviewModal(row, c){
        //close view inspection tour popup
        if(c) {
            c('Clock Click');
        }
        this.selectedIbClReport = row;
        this.prepareAllIbClItems();

        this.ibReviewCloseoutRef.open();
    }

    @ViewChild('checklistItemDetailRef')
    private checklistItemDetailRef: IModalComponent;
    viewItemDetailModal(selectedIbClReport, clItem, itemQueId, type, itemSubQueId = null) {
        this.ibChecklistItem = clItem;
        this.ibChecklistItemImages();
        this.itemQueId = itemQueId;
        this.itemSubQueId = itemSubQueId;
        this.itemType = type;
        this.ibChecklist = selectedIbClReport.ib_ref;
        this.time_to_closeout = (this.ibChecklist.severity.has_severity && clItem.severity && clItem.severity.rating && clItem.severity.days) ? `${clItem.severity.rating} (Closeout Due: ${dayjs(selectedIbClReport.createdAt).add(+(this.createTime(clItem.severity.days)), 'days').format(AppConstant.defaultDateFormat)})` : null;
        this.checklistItemDetailRef.open();
        this.isOpen = true;
    }

    @ViewChild('ibClItemCloseOutRef')
    private ibClItemCloseOutRef: IModalComponent;
    closeoutItemModal (selectedIbClReport, item, itemQueId, type, itemSubQueId = null) {
        this.ibChecklistItem = item;
        this.ibChecklistItemImages();
        this.closeoutDetail = '';
        this.closeoutImgs = [{}];
        this.itemQueId = itemQueId;
        this.itemSubQueId = itemSubQueId;
        this.itemType = type;
        this.ibChecklist = selectedIbClReport.ib_ref;
        this.time_to_closeout = (this.ibChecklist.severity.has_severity && item.severity && item.severity.rating && item.severity.days) ? `${item.severity.rating} (Closeout Due: ${dayjs(selectedIbClReport.createdAt).add(+(this.createTime(item.severity.days)), 'days').format(AppConstant.defaultDateFormat)})` : null;
        this.showModal = true;
        this.ibClItemCloseOutRef.open();
    }

    ibChecklistItemImages(){
        this.ibChecklistItem["item_closeout_images"] = this.ibChecklistItem?.images.map((img) => img?.img_translation.length ? img.img_translation : img?.sm_url || img.file_url ).flat(Infinity) || []
    }
    closeOutRequest(event) {
        this.closeoutLoading = true;
        let images = (this.closeoutImgs || []).reduce((result, elm) => {
            if (elm.id) {
                result.push(elm.id);
            }
            return result;
        },[]);

        console.log(`Closeout item with Idx ${this.itemQueId} subIdx ${this.itemQueId} in checklist ${this.itemType}`);

        let closeOutItemInfo = {};
        (this.selectedIbClReport[this.itemType] || []).map((item, index) => {
            if (index == this.itemQueId) {
                if (!this.selectedIbClReport.has_subheadings) {
                    item.close_out = {
                        "reviewed_by": this.authUser$.first_name + ' ' + this.authUser$.last_name,
                        "close_out_at": dayjs().valueOf(),
                        "details": this.closeoutDetail,
                        "images": images
                    }
                    closeOutItemInfo = item;
                } else {
                    (item.subheadings || []).map((subItem, idx) => {
                        if (idx == this.itemSubQueId) {
                            subItem.close_out = {
                                "reviewed_by": this.authUser$.first_name + ' ' + this.authUser$.last_name,
                                "close_out_at": dayjs().valueOf(),
                                "details": this.closeoutDetail,
                                "images": images
                            }
                            closeOutItemInfo = subItem;
                        }
                    });
                }
            }
            return item;
        });

        let closeout_status = 1;
        if (!this.hasUnsatisfactoryItems(this.selectedIbClReport)) {
            closeout_status = 2;
        }

        let body = {
            "checklist_type": this.itemType,
            "item_index": this.itemQueId,
            "item_sub_index": this.itemSubQueId,
            "closeout_status": closeout_status,
            "item_info": closeOutItemInfo
        }

        this.inspecBuildService.closeOutIbClReportItem(this.projectId, body, this.selectedIbClReport.id).subscribe((data: any) => {
            this.closeoutLoading = false;
            if(data && data.success) {
                event.closeFn();
                if(this.selectedTabId) { this.loadInspectionReports(this.selectedTabId); }
                if(this.isOpen) {
                    this.checklistItemDetailRef.close();
                    this.isOpen = false;
                }
                if(closeout_status == 2 && this.ibReviewCloseoutRef) {
                    this.ibReviewCloseoutRef.close();
                }
                const message = `Item closed out successfully.`;
                this.toastService.show(this.toastService.types.SUCCESS, message);
                return;
            }
            const message = `Failed to closed out the item.`;
            this.toastService.show(this.toastService.types.ERROR, message);
            this.showModal = false;
            event.closeFn();
        });
    }

    closeModal(event) {
        event.closeFn();
    }

    prepareNegativeRating(inspectionReports) {
        this.negativeRating = this.defaultNegativeRating;

        (inspectionReports || []).map(report => {
            if(
                report &&
                report.ib_ref &&
                report.ib_ref.id&&
                Object.keys(report.ib_ref.scoring_system).length &&
                report.ib_ref.scoring_system.values&&
                report.ib_ref.scoring_system.values.length
            ) {
                //Value on first index will not negative so excluding it
                let allRatings = report.ib_ref.scoring_system.values
                allRatings.shift();
                allRatings.map(v => {
                    if (
                        ![...this.defaultNegativeRating, '3'].includes(v.toLowerCase())
                    ) {
                        this.negativeRating.push(v.toLowerCase());
                    }
                });
            }
            return report;
        });
    }

    createTime(iso_string?: any) {
        if(iso_string){
            let d = dayjs.duration(iso_string);
            return dayjs.isDuration(d) ? d.asDays() : null;
        }
        return iso_string;
    }

    public openMapWithPin(){
        this.itemMapData = {
            location: this.ibChecklistItem.location_tag,
            user_ref: this.selectedIbClReport.user_ref,
            createdAt: this.selectedIbClReport.createdAt,
            record_id: this.selectedIbClReport.record_ref,
            status_message: 'Open'
        };
        this.showInspectionPinMapModal();
    }

    @ViewChild('inspectionPinMapHtml')  private inspectionPinMapHtmlModal: GenericModalComponent
    private inspectionPinMapHtmlRef: TemplateRef<any>;
    showInspectionPinMapModal(){
        this.inspectionPinMapConfig.modalTitle = this.ibChecklistItem?.category ? this.ibChecklistItem.category + ': ' : '' + this.ibChecklistItem?.question;
        return this.inspectionPinMapHtmlModal.open()
    }

    public inspectionPinMapConfig: GenericModalConfig = {
        modalTitle: '',
        hideFooter: true,
        modalOptions: {
            size: 'lg',
        }
    }

    @ViewChild('dashboardHtml', { static: true })
    private dashboardHtmlRef: TemplateRef<any>;
    openReportDownloadModal() {
        this.dashboardReportFrom = dayjs().startOf('day').subtract(1, 'y').valueOf();
        this.dashboardReportTo = dayjs().endOf('day').valueOf();
        this.processReportData();
    }

    updateDashboard($event, myDrop) {
        this.blockLoader = true;
        let fileName = 'Inspections-Builder-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
        this.dashboardReportFrom = this.ngbMomentjsAdapter.ngbDateToDayJs($event.fromDate).startOf('day').valueOf();
        this.dashboardReportTo = this.ngbMomentjsAdapter.ngbDateToDayJs($event.toDate).endOf('day').valueOf();
        this.processReportData(true, myDrop);
    }

    downloadDashboardPdf() {
        this.blockLoader = true;
        let fileName = 'Inspections-Builder-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);

        let dashboardServiceCall;
        let body:any = {
            companyId: this.employerId,
            from_date: this.dashboardReportFrom,
            to_date: this.dashboardReportTo
        };
        let args;
        if (this.currentItem.ib_title === 'Inspection Tour') {
            fileName = 'Inspections-Tour-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            dashboardServiceCall = this.projectInspectionTourService.dashboardOfInspectionTour.bind(this.projectInspectionTourService);
            body.file_name = fileName;
            args = [body, this.projectId, 'pdf', fileName,]
        } else {
            fileName = 'Inspections-Builder-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            dashboardServiceCall = this.inspecBuildService.dashboardOfInspectionBuilder.bind(this.inspecBuildService);
            body={...body,
                ib_type: this.currentItem.ib_type,
                company_ref: this.currentItem.company_ref,
                project_ref: this.projectId,
                file_name:fileName
            }
            args = [body, this.projectId, this.selectedTabId, 'pdf', fileName]
        }

        this.inspecBuildService.dashboardOfInspectionBuilder(body, this.projectId, this.selectedTabId, 'pdf', fileName, () => { this.blockLoader = false; }
        );
        dashboardServiceCall(...args, () => {
            this.blockLoader = false;
        });
    }

    processInspectionBuilder(ibInfo) {
        if((ibInfo.project_ref || ibInfo.company_ref) && ibInfo.dashboard_enabled) {
            this.isDashboardActivated = ibInfo.dashboard_enabled;
            return;
        }

        (ibInfo.activate_on_projects || []).map(item => {
            if (item.project_id == this.projectId && item.enabled) {
                this.isDashboardActivated = item.enabled;
            }
            return item;
        })
    }

    getLocation(row) {
        let location = '';
        if (row.fields_data && row.fields_data.length) {
            let locationField = (row.fields_data).find(field => (field.field_name).toLowerCase() == 'location');
            location = (locationField) ? locationField.field_value : '';
        }
        return location;
    }

    @ViewChild('shareIBReportModal') shareIBReportModalRef: ShareToolReportToEmailComponent;
    openShareIBDetailModal(row) {
        this.shareIBReportModalRef.openEmailFormModal(row);
    }

    shareInspectionBuilderReport(event) {
        console.log(event);
        this.inspecBuildService.shareInspectionBuilderReport(event.req, event.reportId).subscribe((res: any) => {
            event.cb(res);
        });
    }
    searchFunction(data) {
        this.search = data.search;
        if(this.currentItem.ib_title !== 'Inspection Tour'){
            this.loadInspectionReports(this.selectedTabId);
        }

    }

    handleDataFromChild(data: string) {
        this.page.totalElements = data;
    }
    processReportData(updating = false, myDrop = null){
        this.blockLoader = true;
        let dashboardReportFrom;
        let dashboardServiceCall;
        let args;
        let fileName;

        const body:{
            companyId: number,
            file_name: string,
            from_date: any,
            to_date: any,
            company_ref: number,
            project_ref?: number,
            ib_type?:any,
        } = {
            companyId: this.employerId,
            file_name: fileName,
            from_date: this.dashboardReportFrom,
            to_date: this.dashboardReportTo,
            company_ref: this.currentItem.company_ref,
            project_ref:0,
        };

        if (this.currentItem.ib_title === 'Inspection Tour') {
            fileName = 'Inspections-Tour-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            dashboardServiceCall = this.projectInspectionTourService.dashboardOfInspectionTour.bind(this.projectInspectionTourService);
            body.file_name = fileName;
            args = [body, this.projectId, 'html', fileName];

        } else {
            fileName = 'Inspections-Builder-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
            dashboardServiceCall = this.inspecBuildService.dashboardOfInspectionBuilder.bind(this.inspecBuildService);
            body.file_name = fileName;
            body.project_ref = this.projectId;
            body.ib_type =  this.currentItem.ib_type;
            args = [body, this.projectId,this.selectedTabId, 'html', fileName];
        }

        this.dashboardStartDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+dashboardReportFrom)) as NgbDate;
        dashboardServiceCall(...args).subscribe((data: any) => {
            if(myDrop){
                myDrop.close();
            }
            this.blockLoader = false;
            this.dashboardHtmlContent = this.domSanitizer.bypassSecurityTrustHtml(data);
            if(!updating){
                this.openModal(this.dashboardHtmlRef, 'xl', "inspectionTourDashboard");
            }
        });
    }

    openDashboardModal() {
        this.loadPowerBiComponent = true;
    }

    powerBiDashboardClose() {
        this.loadPowerBiComponent = false;
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actionMap = {
            'view_inspection': () => this.viewIbClReport(row),
            'download_inspection': () => this.getIbClReport(row),
            'share_inspection': () => this.openShareIBDetailModal(row),
            'close_inspection': () => this.managementReviewModal(row, ''),
        };

        const action = actionMap[entry.key];
        if (action) {
            action();
        } else {
            console.warn(`Unhandled action key: ${entry.key}`);
        }
    }
}

<style>
    .text-danger{
        font-size: 21px;
    }
</style>
<div class="d-block">
    <div class="col-12 p-0 mb-2 details-fields" dragula="details-fields" [(dragulaModel)]="container_obj[fields_key]">
        <ng-template ngFor let-item [ngForOf]="(container_obj[fields_key] || [])" let-i="index"  [ngForTrackBy]="trackByIndex">
            <div class="drag-box py-2">
                <div class="row mx-0">
                    <div class="col-1 text-center p-0 pt-1">
                        <i class="fa fa-bars" aria-hidden="true"></i>
                    </div>
                    <div class="col-5 px-1">
                        <input type="text" class="form-control" placeholder="Field name" #fieldItem="ngModel"
                            [(ngModel)]="container_obj[fields_key][i].field" name="{{'field' + i}}" (ngModelChange)="inputChanged()"
                            required autocomplete="off"
                            [inputDuplicateValidator]="'fieldGroup'"
                            >
                        <div *ngIf="fieldItem.errors?.required && fieldItem.touched" class="alert alert-danger mb-0 mt-1 py-1">
                            Field name is required.
                        </div>
                        <div *ngIf="fieldItem.errors?.duplicate && fieldItem.touched" class="alert alert-danger mb-0 mt-1 py-1">
                            Field name must be unique.
                        </div>
                        <div class="mandatoryCheckbox custom-control custom-checkbox pt-1" style="font-size: 14px; font-family: 'Font Awesome 5 Free';">
                            <input type="checkbox" class="custom-control-input" [id]="'is_mandatory_' + i" [name]="'is_mandatory_' + i"
                                    #isMandatory="ngModel" [(ngModel)]="item.is_mandatory" />
                            <label class="custom-control-label" [for]="'is_mandatory_' + i" style="padding-top: 2px;">Make this a mandatory field</label>
                        </div>
                    </div>
                    <div class="col-5 px-1">
                        <ng-select (change)="fieldTypChanged(i, fieldType.value)" [items]="fieldTypes" bindLabel="label" bindValue="key" #fieldType="ngModel" [(ngModel)]="container_obj[fields_key][i].field_type" [id]="'field_type' + i" [name]="'field_type' + i" class="form-control" required>
                        </ng-select>
                        <label *ngIf="container_obj[fields_key][i].field_type == 'dropdown'" style="font-size: 14px;" class="cursor-pointer pt-1 m-0" (click)="manageOptions(i)">
                            <i class="fa text-primary mr-1" [ngClass]="item.options.length == 0 ? 'fa-plus' : 'fa-edit'"></i>{{item.options.length == 0 ? 'Add' : 'Edit'}} dropdown options
                        </label>
                    </div>
                    <div class="col-1 d-flex justify-content-center p-0 pt-2">
                        <span class="material-symbols-outlined text-danger cursor-pointer" (click)="removeFieldBlock(i)">
                            delete
                        </span>
                    </div>
                </div>
            </div>
        </ng-template>
    </div>
    <div class="input-group mb-3">
        <button class="btn btn-sm btn-outline-brandeis-blue btn-hover m-btn-size-lg" id="newBtn" type="button" (click)="addFieldBlock()">
            <div class="d-flex align-items-center justify-content-center">
                <span class="material-symbols-outlined x-large-font mr-1">
                    add
                </span>
                <span class="mr-1">Add Fields</span>
            </div>
        </button>
    </div>
</div>

<i-modal #dropdownOptionsModal [title]="'Field Options'" size="md" (onCancel)="closeOptionsPopup($event)" [rightPrimaryBtnTxt]="'Save'" (onClickRightPB)="updateOptions()" [rightPrimaryBtnDisabled]="!optionForm.valid || !hasValidOptions()" [isCentered]="true" [showCancel]="false">
        <form novalidate #optionForm="ngForm">
            <div>
                <span>Add, remove options</span>
                <div class="form-group row">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">Options</th>
                            <th class="text-center">Active <i class="ml-1 fa fa-info-circle small" ngbTooltip="Deactivating a option will hide it from dropdown." [openDelay]="200" [closeDelay]="500"></i></th>
                            <th class="text-center">Action</th>
                        </tr>
                        </thead>
                        <tbody *ngIf="container_obj[fields_key]">
                            <ng-container *ngFor="let option of container_obj[fields_key][fieldIndex]?.options trackBy : trackByRowIndex; let j = index;">
                                <tr>
                                    <td>
                                        <ng-container *ngIf="j == 0">
                                            <input type="text" class="form-control" [name]="'optionLabel' + j"
                                                    #optionLabel="ngModel" [(ngModel)]="option.label"
                                                    placeholder="Enter option label then press add" autocomplete="off" 
                                                    [inputDuplicateValidator]="'optionLabel'" />
                                            <div *ngIf="optionLabel?.errors?.required && optionLabel?.touched" class="alert alert-danger mb-0 mt-1 py-1">
                                                Field name is required.
                                            </div>
                                            <div *ngIf="optionLabel?.errors?.duplicate && optionLabel?.touched" class="alert alert-danger mb-0 mt-1 py-1">
                                                Field name must be unique.
                                            </div>
                                        </ng-container>

                                        <ng-container *ngIf="j != 0">
                                            <input type="text" class="form-control" [name]="'optionLabel' + j"
                                                    #optionLabel="ngModel" [(ngModel)]="option.label"
                                                    placeholder="Enter option" autocomplete="off" required [inputDuplicateValidator]="'optionLabel'" />

                                            <div *ngIf="optionLabel?.errors?.required && optionLabel?.touched" class="alert alert-danger mb-0 mt-1 py-1">
                                                Field name is required.
                                            </div>
                                            <div *ngIf="optionLabel?.errors?.duplicate && optionLabel?.touched" class="alert alert-danger mb-0 mt-1 py-1">
                                                Field name must be unique.
                                            </div>
                                        </ng-container>

                                    </td>
                                    <td class="text-center">
                                        <div class="custom-control custom-checkbox" *ngIf="j != 0">
                                            <input type="checkbox" class="custom-control-input" [id]="'is_opt_active_' + j" [name]="'is_opt_active_' + j"
                                                #isActive="ngModel" [(ngModel)]="option.is_active" />
                                            <label class="custom-control-label" [for]="'is_opt_active_' + j"></label>
                                        </div>
                                    </td>
                                    <td *ngIf="j == 0" class="text-center">
                                        <button class="btn btn-sm btn-outline-primary" (click)="addOption(j, optionForm)" [disabled]="!option?.label || !optionLabel?.toArray()[0]?.valid">
                                            <i class="fa fa-plus"></i> Add
                                        </button>
                                    </td>
                                    <td *ngIf="j > 0" class="text-center">
                                        <span class="material-symbols-outlined text-danger cursor-pointer" (click)="removeOptionRow(j)" *ngIf="!(j == 0)">
                                            delete
                                        </span>
                                    </td>
                                </tr>
                            </ng-container>
                        </tbody>
                    </table>
                </div>
            </div>
        </form>
</i-modal>
import {Component, OnInit, ViewChild, TemplateRef, ElementRef} from '@angular/core';
import {ActivatedRoute} from "@angular/router";
import { HttpParams } from '@angular/common/http';
import {Observable, fromEvent} from "rxjs";
import {debounceTime, distinctUntilChanged, filter, tap} from "rxjs/operators";
import * as dayjs from 'dayjs';
import {
    ToolboxTalksService,
    AuthService,
    User,
    ProjectService,
    UserService,
    Project,
    isInheritedProjectOfCompany,
    Common,
    HttpService,
    ToolboxTalksActionButtons,
    ActionButtonVal,
    ToolboxTalks,
    ToastService,
} from "@app/core";
import {NgbModalConfig, NgbModal, NgbDateStruct} from '@ng-bootstrap/ng-bootstrap';
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import { Borders, FillPattern, Font, Workbook, Worksheet } from 'exceljs';

import * as Excel from "exceljs/dist/exceljs.min.js";
import * as ExcelProper from "exceljs";
import * as fs from 'file-saver';
import {AppConstant} from "@env/environment";
import { SearchWithFiltersComponent } from '../search-with-filters/search-with-filters.component';
import { filterData } from '@app/core';
import { ReportDownloaderComponent } from '../report-downloader/report-downloader.component';
import { ActionBtnEntry, IModalComponent } from '@app/shared';
import { innDexConstant } from '@env/constants';

@Component({
    templateUrl: './toolbox-talks.component.html',
    providers: [NgbModalConfig, NgbModal],
    styleUrls:['./toolbox-talks.component.scss']
})
export class ToolboxTalksComponent implements OnInit {
    @ViewChild('reportDownloader') private reportDownloader: ReportDownloaderComponent;
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;
    // workbook: ExcelProper.Workbook = new Excel.Workbook();
    blobType: string = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    employerId: number = 0;
    project: Observable<{}>;
    records: Array<any> = [];
    selectedToolboxTalks: Array<any> = [];
    projectUsers: Array<any> = [];
    filteredUsers: Array<any> = [];
    hasSelectedUsers: boolean = false;
    isFilteredUsersReset:boolean = false;
    authUser$: User;
    toolKey: string = 'toolbox_talks';
    talk_row: ToolboxTalks = new ToolboxTalks();
    talk_attendees:  Array<any> = [];
    allowedMime : Array<any> = ['application/pdf', 'application/x-pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    toolbox_talk_file = null;
    convert_doc_to_pdf: boolean = false;
    talk_file_ref = null;
    talkFile: any;
    img_link: string = `/images/project-placeholder.png`;

    processingToolboxTalk = false;
    addingToolboxTalkInProgress = false;
    employer: any = {};
    processingToolboxInvites: boolean = false;
    inviteModalRef = null;
    jobRoles: any = [];
    selectedRole: string = null;
    search_term;
    projects: any = [];
    selectedProjects: any = [];
    is_inherited_project: boolean = false;
    loadingToolboxTalks: boolean = false;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    pagination = new Common();
    blockLoader: boolean = false;
    page = this.pagination.page;
    isDownload:boolean = false;
    recordsDownload: Array<any> = [];
    tagged_owner: number;
    selectedOwner: any[]=[];
    temp_records: Array<any> = [];
    ownerList: Array<any> = [];
    search:any = null;
    titleEditDisable: boolean = true;
    companyEditDisable: boolean = true;
    talk_title: string = '';
    modalRef = null;
    availableTalks: Array<any> = [];
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    isProjectPortal: boolean = false;
    selectedEmployer: string = null;
    availableEmployers: Array<any> = [];
    isMobileDevice: boolean = false;
    searchWithFilter:any={search:"",filterOne:[]};
    filterData:filterData[] = [];
    reloadFilterData:boolean = false;
    actionButtonMetaData = {
        actionList: [],
        class: 'material-symbols-outlined',
    };
    sorts: {
        dir:string, prop: string
    } = {
        dir:'desc', prop: 'id'
    }
    isModalOpen: boolean = false;
    showModal: boolean = false;
    isInitToolboxTalk: boolean = false;
    loadingInlineToolboxTalk: boolean = false;
    loadPowerBiComponent: boolean = false;
    biToolName: string = '';
    biToolLabel: string = '';
    biModalTitle: string = 'Dashboard';
    dashboardLevel: string = 'project';
    talkRegisterButtonGroup: Array<ActionBtnEntry> = [
        {
            key: 'download_register',
            label: '',
            title: 'Download Register',
            mat_icon: 'download',
        }
    ];

    rowButtonGroup: Array<ActionBtnEntry> = [];

    constructor(
        private activatedRoute: ActivatedRoute,
        private toolboxTalksService: ToolboxTalksService,
        private authService: AuthService,
        private projectService: ProjectService,
        private modalService: NgbModal,
        private userService: UserService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private httpService: HttpService,
        private toastService: ToastService,
    ) {
        this.from_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.to_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
        }
        this.isMobileDevice = this.httpService.isMobileDevice();
        this.filterData = this.renderFilterData();
    }

    AppConstant = AppConstant;
    dayjs(n: number) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };

    ngOnInit() {
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });

        const isProjectView = this.isProjectPortal || this.isCompanyProjectRoute();
        this.initializeButtonGroup(isProjectView);
        
        if(!this.isProjectPortal) {
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
                this.employerId = params['employerId'];
            });
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
            this.img_link =  this.employer.logo_file_url;

            //Load company level toolbox talks
            if (!this.isCompanyProjectRoute()) {
                this.initializeTable();
            }
            this.toolBoxTalksActionButtons();
        } else {
            this.initializeTable();
            this.toolBoxTalksActionButtons();
        }
    }

    private initializeButtonGroup(isProjectView: boolean): void {
        this.rowButtonGroup = isProjectView 
            ? this.getProjectViewButtons()
            : this.getDefaultViewButtons();
    }

    private getProjectViewButtons(): ActionBtnEntry[] {
        return [
            {
                key: 'view_1',
                label: '',
                title: 'View Toolbox Talk',
                mat_icon: 'search',
            },
            {
                key: 'download',
                label: '',
                title: 'Download Toolbox Talk',
                mat_icon: 'download',
                children: [
                    {
                        key: 'download_pdf',
                        label: 'Download PDF',
                        title: 'Download PDF',
                    },
                    {
                        key: 'download_xlsx',
                        label: 'Download XLSX',
                        title: 'Download XLSX',
                    }
                ],
            },
        ];
    }

    private getDefaultViewButtons(): ActionBtnEntry[] {
        return [
            {
                key: 'view_2',
                label: '',
                title: 'View Toolbox Talk',
                mat_icon: 'search',
            }
        ];
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actionMap = {
            'view_1': () => this.toolboxTalkDetailModal(row),
            'view_2': () => row.talk_file_ref ? this.showToolboxTalkPdf(row) : this.toolboxTalkDetailModal(row),
            'download_pdf': () => this.downloadToolboxTalk(row),
            'download_xlsx': () => this.downloadToolboxTalkXLSX(row.id, this.authUser$.timezone),
            'download_register': () => this.toolboxTalkDownloadAttendees(row, this.talk_row?.talk_title),
        };
        const action = actionMap[entry.key];
        if (action) {
            action();
        }
    }
    maxTitleLength = innDexConstant.maxTitleLength

    toolBoxTalksActionButtons() {
        this.actionButtonMetaData.actionList = [
            {
                code: ToolboxTalksActionButtons.INVITE_TO_TOOLBOX,
                name: `Invite to Brief`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'send',
                enabled: true,
            },
            {
                code: ToolboxTalksActionButtons.DASHBOARD,
                name: `View Dashboard`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'dashboard',
                enabled: (this.isProjectPortal || this.isCompanyProjectRoute()),
            },
            {
                code: ToolboxTalksActionButtons.DOWNLOAD_REPORT,
                name: `Download Report`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: (this.isProjectPortal || this.isCompanyProjectRoute()),
            },
            {
                code: ToolboxTalksActionButtons.DOWNLOAD_REGISTER,
                name: `Download Register`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: this.isProjectPortal,
            },
        ];
    }

    // Will get called for Company route only.
    projectRetrieved(project: Project){
        this.projectInfo = project;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
        this.initializeTable();
    }

    @ViewChild('searchFilters') searchFilters: SearchWithFiltersComponent;
    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean) {
        if (!this.isInitToolboxTalk) {
          this.isInitToolboxTalk = true;
          return;
        }
        this.page.pageNumber = pageInfo.offset;
        this.initializeTable(isPageChange);
    }

    private initializeTable(isPageChange?: boolean) {
        if (isPageChange) {
          this.loadingInlineToolboxTalk = true;
        } else {
          this.loadingToolboxTalks = true;
        }
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('search', `${this.search ? encodeURIComponent(this.search) : ''}`)
            .set('sortKey', this.sorts.prop)
            .set('sortDir', this.sorts.dir);

        if(this.selectedOwner.length){
            params = params.append('tagged_owner',this.selectedOwner.join(','));
        }
        if(this.isProjectPortal || this.isCompanyProjectRoute()) {
            let extra = ['briefing-count'];
            if(!this.page.pageNumber && !this.search && !this.selectedOwner.length){
                extra.push('tagged-owners');
            }
            params = params.append('extra', extra.join(','));
            this.toolboxTalksService.getProjectBriefingToolRecordsList(this.projectId, this.toolKey, params).subscribe((data: any) => {
                // console.log('data', data);
                this.loadingToolboxTalks = false;
                this.loadingInlineToolboxTalk = false;
                if (data && data.tool_records) {
                    this.records = data.tool_records;
                    this.page.totalElements = data.totalCount;
                    this.temp_records = data.tool_records;
                    if(!this.reloadFilterData){
                        // this.checkTagOwner();
                        this.ownerList = data.taggedOwnersList;
                        this.filterData = this.renderFilterData()
                        this.reloadFilterData = true;
                    }
                    if(this.selectedOwner.length !=0){
                        this.filterByOwner();
                    }
                }else{
                    const message = `Failed to fetch toolbox talks, projectId: ${this.projectId}.`;
                    this.toastService.show(this.toastService.types.ERROR, message);
                }
            });
        } else {
            let _self = this;
            this.toolboxTalksService.getCompanyToolboxTalks(this.employerId, params).subscribe((data:any) => {
                this.loadingToolboxTalks = false;
                this.loadingInlineToolboxTalk = false;
                if (data && data.project_toolbox_talks) {
                    this.page.totalElements = data.total_record_count;
                    this.records = data.project_toolbox_talks;
                    return data.project_toolbox_talks;
                }
                const message = `Failed to fetch toolbox talks, projectId: ${this.projectId}.`;
                this.toastService.show(this.toastService.types.ERROR, message);
                return [];
            });
        }
        return "";
    }

    @ViewChild('addToolboxTalkRef')
    private addToolboxTalkRef: IModalComponent;
    showAddToolboxTalkPop(form) {
        this.isModalOpen = true;
        this.tagged_owner = null;
        this.talk_title = '';
        form.reset();
        return this.addToolboxTalkRef.open();
    }

    @ViewChild('toolboxInviteModalCompanyRef')
    private toolboxInviteModalCompanyRef: IModalComponent;
    @ViewChild('toolboxInviteModalRef')
    private toolboxInviteModalRef: IModalComponent;
    inviteToToolboxTalkPopup() {
        this.processingToolboxTalk = true;
        this.getAvailableTalks();
        if(this.isProjectPortal) {
            this.userService.getProjectAdmins(this.projectId).subscribe((res: any) => {
                if(res.success) {
                    this.projectUsers = res.admins;
                    this.projectUsers = (res.admins || []).reduce((acc, item) => {
                        if (item.inductionStatus !== null) {
                          acc.push({ ...item, _isSelected: false });
                        }
                        return acc;
                    }, []);
                    this.jobRoles = this.availableJobRoles();
                    this.filteredUsers = this.projectUsers;
                    this.getAvailableEmployers();
                    this.processingToolboxTalk = false;
                    this.toolboxInviteModalRef.open();
                }

            });
        }
        if(this.isCompanyRoute()) {
            if(this.isCompanyProjectRoute()) {
                this.userService.getCompanyProjectAdmins(this.employerId, this.projectId).subscribe((res: any) => {
                    if(res.success) {
                        this.projectUsers = res.admins;
                        this.jobRoles = this.availableJobRoles();
                        this.filteredUsers = this.projectUsers;
                        this.processingToolboxTalk = false;
                        this.toolboxInviteModalRef.open();
                    } else {
                        const message = res.message || 'Failed to get data.';
                        this.toastService.show(this.toastService.types.ERROR, message, { data: res });
                    }
                 });
            } else {
                this.projectService.getCompanyProjects(this.employerId).subscribe((data: any) => {
                    this.processingToolboxTalk = false;
                    if (data.success) {
                        this.projects = [ ...data['projects'].employer_projects, ...data['projects'].additional_projects];
                        this.toolboxInviteModalCompanyRef.open();
                    } else {
                        const message = data.message || 'Failed to get data.';
                        this.toastService.show(this.toastService.types.ERROR, message, { data: data });
                    }
                });

            }
        }
    }

    availableJobRoles() {
        let userRoles: any = [];
        this.projectUsers.map(u => {
            return userRoles.push(u.job_role);
        })
        let uniqueroles = Array.from(new Set(userRoles));
        return uniqueroles;
    }

    getAvailableEmployers() {
        let result = this.projectUsers.map(u => {
            return u.employer;
        });
        this.availableEmployers = [...new Set(result)];
    }

    changeProjectsModel(event, item) {
        if(event.target.checked) {
            this.selectedProjects.push(item);
        }
        else {
            this.selectedProjects = this.selectedProjects.filter(u=> u.id != item.id);
        }
    }

    selectAllProjects(event) {
        if(event.target.checked) {
            this.selectedProjects = this.projects;
        }
        else {
            this.selectedProjects = [];
        }
    }

    isAllProjectsSelected() {
        const selectedprojectsSet = new Set(this.selectedProjects.map(project => project.id));
        return this.projects.every(project => selectedprojectsSet.has(project.id));
    }

    onSort($event){
        let {sorts} = $event;
        let{ dir, prop } = sorts[0];
        this.sorts = { dir, prop };
        this.pageCallback({ offset: 0 }, true);
    }

    checkHasSelectedUsers(noOfSelectedUsers: number) {
        this.hasSelectedUsers = (noOfSelectedUsers > 0) ? true : false;
    }

    inviteUsers(form) {
        if (!form.valid) {
            return;
        }
        this.processingToolboxInvites = true;
        let toolboxTalks = form.value.selectedTools.map(tbt=> tbt.id);
        let users = form.value.selectedUsers.map(({ _isSelected, ...rest }) => rest);
        let req = {
            toolboxTalks: toolboxTalks,
            usersToInvite: users
        }
        this.toolboxTalksService.inviteToToolboxTalk(req, this.projectId).subscribe(res => {
            this.processingToolboxInvites = false;
            if(res.success) {
                this.filteredUsers = [ ...this.projectUsers ];
                this.isFilteredUsersReset = true;
                form.reset();
                this.invitesSuccessMsg(res);
                this.toolboxInviteModalRef.close();
            }
            else {
                this.invitesErrorMsg();
            }
        });
    }

    getAvailableTalks() {
        if(this.isCompanyRoute()) {
            this.toolboxTalksService.getCompanyTBTForInvite(this.employerId).subscribe((data:any) => {
                if (data.success && data.availableToolboxTalks) {
                    this.availableTalks = data.availableToolboxTalks;
                    return;
                }
                const message = `Failed to fetch toolbox talks, employerId: ${this.employerId}.`;
                this.toastService.show(this.toastService.types.ERROR, message);
            });
        } else {
            this.toolboxTalksService.getBriefingToolRecordForInvite(this.toolKey, this.projectId).subscribe((data:any) => {
                if (data.success && data.availableBriefingTools) {
                    this.availableTalks = data.availableBriefingTools;
                    return;
                }
                const message = `Failed to fetch ${this.toolKey}, projectId: ${this.projectId}.`;
                this.toastService.show(this.toastService.types.ERROR, message);
            });
        }
    }

    inviteUsersForCompanyTBTs(form) {
        if (!form.valid || this.selectedProjects.length ===0) {
            return;
        }
        this.processingToolboxInvites = true;
        let toolboxTalks = this.selectedToolboxTalks.map(tbt=> tbt.id);
        let req = {
            toolboxTalks: toolboxTalks,
            projectsToInvite: this.selectedProjects
        }
        this.toolboxTalksService.inviteToToolboxTalkForCompanyTBTs(req, this.employerId).subscribe(res => {
            this.processingToolboxInvites = false;
            if(res.success) {
                form.reset();
                this.invitesSuccessMsg(res);
                this.toolboxInviteModalCompanyRef.close();
            }
            else {
                this.invitesErrorMsg();
            }
        });
    }

    invitesSuccessMsg(res) {
        const message = res.status || 'Invites sent successfully.';
        this.toastService.show(this.toastService.types.SUCCESS, message);
    }

    invitesErrorMsg() {
        const message = 'Failed to send invites.';
        this.toastService.show(this.toastService.types.ERROR, message);
    }

    closeInviteModal() {
        this.selectedRole = null;
        this.filteredUsers = [];
        this.selectedToolboxTalks = null;
        this.inviteModalRef.close();
    }

    @ViewChild('toolboxTalkDetailsRef')
    private toolboxTalkDetailsRef: IModalComponent;
    toolboxTalkDetailModal(row) {
        this.processingToolboxTalk = true;
        this.toolboxTalksService.getProjectBriefingToolRecord(this.projectId, this.toolKey, row.id, {
            allBriefings: true,
            expand_attendees: true
        }).subscribe((data: any) => {
            this.processingToolboxTalk = false;
            console.log('data', data);
            if(!data.briefing){
                const message = 'Failed to fetch toolbox talks record.';
                this.toastService.show(this.toastService.types.ERROR, message);
                return false;
            }
            let row = data.briefing;
            this.titleEditDisable = true;
            this.companyEditDisable = true;
            this.tagged_owner = (row.tagged_owner && row.tagged_owner.id) ? row.tagged_owner.id : row.tagged_owner;
            this.talk_title = row.talk_title;
            this.talk_row = data.briefing;
            this.showModal = true
            this.toolboxTalkDetailsRef.open();
        });

    }

    openToolboxTalkModal(content){
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
        });
    }

    async initiateDownload(resp) {
        this.from_date = resp.fromDate;
        this.to_date = resp.toDate;
        const fromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.from_date).startOf('day').valueOf();
        const toDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.to_date).endOf('day').valueOf();
        this.isDownload = true;
        this.blockLoader = true;
        const params = new HttpParams()
            .set('download', `${this.isDownload}`)
            .set('from_date', `${fromDate}`)
            .set('to_date', `${toDate}`);
        this.toolboxTalksService.getProjectToolboxTalks(this.projectId, params).subscribe((data:any) => {
            this.loadingToolboxTalks = false;
            if (data && data.project_toolbox_talks) {
                this.recordsDownload = data.project_toolbox_talks;
                this.isDownload = false;
                this.downloadToolboxTalkReport();
                return;
            }
            const message = `Failed to fetch toolbox talks, projectId: ${this.projectId}.`;
            this.toastService.show(this.toastService.types.ERROR, message);
            return [];
        });
    }

    showToolboxTalkPdf(row){
        window.open(row.talk_file_ref.file_url, "_blank");
    }

    addToolboxTalkRequest(form, event) {
        if (!form.valid) {
            console.log('Modal form is not valid');
            return;
        }
        let addTalkReq = form.value;
        if(this.isProjectPortal || this.isCompanyProjectRoute()){
            addTalkReq.project_ref = this.projectId;
        } else {
            addTalkReq.company_ref = this.employerId;
        }
        addTalkReq.tagged_owner = this.tagged_owner;
        addTalkReq.user_ref = this.authUser$.id;
        addTalkReq.is_available = 1;
        addTalkReq.convert_doc_to_pdf = this.convert_doc_to_pdf;
        this.addingToolboxTalkInProgress = true;

        this.toolboxTalksService.createToolboxTalk(addTalkReq).subscribe(out => {
            if(!out.success){
                const message = out.message || 'Failed to store data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
            } else {
                const message = 'Toolbox talk created successfully.';
                this.toastService.show(this.toastService.types.SUCCESS, message);
            }
            this.addingToolboxTalkInProgress = false;
            form.reset();
            event.closeFn();
            this.isModalOpen = false;
            this.initializeTable(true);
        });
    }

    toolboxTalkDownloadAttendees(briefed, title='Toolbox Talk') {
        title = `Title: ${title}`;
        let workbook: ExcelProper.Workbook = new Excel.Workbook();
        let worksheet = workbook.addWorksheet('Toolbox Talk', {
            properties: {
                defaultRowHeight: 50
            }
        });

        let rowIndex = 1;
        worksheet.addRow([`${title}`, ' ', ' ']).commit();
        worksheet.getRow(rowIndex).eachCell(cell => {
            cell.font = {bold: true};
            /*cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: {argb: '82878c'}
            };*/
            cell.border = {
                top: {style: 'medium'},
                left: {style: 'thin'},
                bottom: {style: 'thin'},
                right: {style: 'thin'}
            }
            cell.alignment = { vertical: 'middle' };
        });
        worksheet.mergeCells('A1:C1');
        worksheet.getRow(rowIndex).height = 20;

        worksheet.getColumn('A').width = 32;
        worksheet.getColumn('A').style = {alignment: {wrapText: true}};
        worksheet.getColumn('B').width = 35;
        worksheet.getColumn('B').style = {alignment: {wrapText: true}};
        worksheet.getColumn('C').width = 27;
        rowIndex += 1;
        let briefedAt = briefed.briefed_at ? this.dayjs(+briefed.briefed_at).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) : null;
        worksheet.addRow([`Briefed By: ${briefed.briefed_by_name}`, `Briefing: ${briefedAt}`]).eachCell(cell => {
            cell.font = {bold: true};
        });
        worksheet.getRow(rowIndex).height = 20;

        rowIndex += 1;
        worksheet.addRow(["Name", "Employer", "Signature"]).eachCell(cell => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: {argb: 'D3D3D3'}
            };
            cell.border = {
                top: {style: 'thin'},
                bottom: {style: 'thin'},
                right: {style: 'thin'}
            }
        });
        worksheet.getRow(rowIndex).height = 20;

        //sort by name
        briefed.allattendees.sort((a, b) => (a.name > b.name) ? 1 : -1);
        let signatureArr = [];
        briefed.allattendees.map(row => {
            rowIndex += 1;
            if (row.sign) {
                signatureArr[rowIndex] = row.sign;
            }
            worksheet.addRow([row.name, row.employer, null]).height = 50;
            return row;
        });

        signatureArr.map((signature, index) => {
            let imageId = workbook.addImage({
                base64: signature,
                extension: 'png',
            });
            worksheet.addImage(imageId, {
                tl: { col: 2.1, row: (index-1)+0.1 },
                ext: { width: 130, height: 30 }
            });
        });

        workbook.xlsx.writeBuffer().then(data => {
            const blob = new Blob([data], { type: this.blobType });
            fs.saveAs(blob, 'Toolbox-Talk-Attendees.xlsx');
        });
    }

    uploadDone($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.talkFile = $event.userFile;
            this.toolbox_talk_file = $event.userFile.file_url;
            let urlParts = (this.toolbox_talk_file).split(".");
            this.convert_doc_to_pdf = (urlParts.length && ['doc', 'docx'].includes((urlParts[urlParts.length-1]).toLowerCase()));
            this.talk_file_ref = $event.userFile.id;
        }
    }

    fileDeleteDone($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.talkFile = undefined;
            this.toolbox_talk_file = null;
            this.convert_doc_to_pdf = false;
            this.talk_file_ref = null;
        }
    }

    makeTalkAvailable($event, row) {
        let makeAvailable = $event.target.checked;
        let req: any = {};
        if(row.company_ref && this.projectId) {
            req.project_available_status = row.project_available_status;
            req.project_available_status[this.projectId] = makeAvailable;
        }
        else {
            req = {
                is_available: makeAvailable,
                user_ref: row.user_ref.id
            }
        }
        this.loadingInlineToolboxTalk = true;
        this.toolboxTalksService.updateToolboxTalk(req, row.id).subscribe(out => {
            this.loadingInlineToolboxTalk = false;
        });
    }

    checkAvailability(record) {
        if(record.company_ref && this.projectId) {
            let status = record.project_available_status[this.projectId];
            if(status != undefined) {
                return status;
            }
        }
        return record.is_available;
    }

    isCompanyRoute() {
        let routeName = this.activatedRoute.snapshot.url[0].path;
        return routeName === 'company-admin';
    }

    isCompanyProjectRoute() {
        let routeName = this.activatedRoute.snapshot.url[1].path;
        return routeName === 'project-toolbox-talks';
    }

    downloadToolboxTalkReport() {
        const fromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.from_date).startOf('day').valueOf();
        const toDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.to_date).endOf('day').valueOf();
        console.log('fromDate', fromDate);
        console.log('toDate', toDate);

        let _self = this;
        let workbook: ExcelProper.Workbook = new Excel.Workbook();
        let worksheet = workbook.addWorksheet('Toolbox Talk', {
            properties: {
                defaultRowHeight: 30
            }
        });
        worksheet.columns = [
            { header: 'Project', key: 'project', width: 27 },
            { header: 'Toolbox Talk #', key: 'toolbox_talk_no', width: 27 },
            { header: 'Title', key: 'title', width: 27 },
            { header: 'Uploaded By', key: 'uploaded_by', width: 27 },
            { header: 'Uploaded At', key: 'uploaded_at', width: 20 },
            { header: 'No. Times Briefed', key: 'briefed_count', width: 22 },
            { header: 'No. Operatives Briefed', key: 'total_operatives_briefed', width: 22 },
            { header: 'Last briefed', key: 'last_briefed', width: 22 },
            { header: 'Last Briefed by', key: 'last_briefed_by', width: 35 },
        ];
        let rows = this.recordsDownload.filter(r =>  (r.register.length)).map(r => {
            let timesBriefedCount = 0;
            let operativesBriefedCount = 0;
            r.register = r.register.sort((a, b) => a.briefed_at - b.briefed_at);
            for (const reg of r.register) {
                timesBriefedCount = timesBriefedCount + 1;
                operativesBriefedCount += reg.allattendees.length;
            }

            const lastReg = r.register[r.register.length - 1];

            return {
                "project": (this.projectInfo && this.projectInfo.project_number !=null ? this.projectInfo.project_number +' - ' + this.projectInfo.name: this.projectInfo.name || ''),
                "toolbox_talk_no": r.talk_number,
                "title": r.talk_title,
                "uploaded_by": r.user_ref.name,
                "uploaded_at": this.dayjs(+r.createdAt).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS),
                "briefed_count": timesBriefedCount,
                "total_operatives_briefed": operativesBriefedCount,
                "last_briefed": lastReg ? this.dayjs(lastReg.briefed_at).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) : '',
                "last_briefed_by": lastReg?.briefed_by_name || '',
            };
        });
        worksheet.addRows(rows);
        worksheet.eachRow(function(row, rowNumber) {
            if(rowNumber == 1) {
                row.height = 20;
                row.eachCell(function(cell) {
                    cell.fill = {
                        type: 'pattern',
                        pattern:'solid',
                        fgColor: {argb: 'dedede'}
                    }
                    cell.border = {
                        top: { style: "thin" },
                        left: { style: "thin" },
                        bottom: { style: "thin" },
                        right: { style: "thin" }
                    };
                });
            }
        });

        workbook.xlsx.writeBuffer().then(data => {
            const blob = new Blob([data], { type: this.blobType });
            fs.saveAs(blob, `Toolbox Talk Report-${dayjs().format(AppConstant.apiRequestDateFormat)}.xlsx`);
            this.blockLoader = false;
        });
    }

    downloadToolboxTalkXLSX(id, timezone) {
        let request = {
            projectId: this.projectId,
            timezone: timezone
        };
        this.processingToolboxTalk = true;
        this.toolboxTalksService.downloadToolboxTalkXLSX(request, id, this.projectId, `Toolbox Talk Report.xlsx`, () => {
            this.processingToolboxTalk = false;
            this.blockLoader = false;
        });
    }

    downloadToolboxTalk(row) {
        this.processingToolboxTalk = true;
        let body = {
            projectId: this.projectId,
            createdAt: row.createdAt,
            companyId: this.employer.id,
            type: 'pdf'
        };

        this.toolboxTalksService.downloadToolboxTalk(body, row.id, () => {
            this.processingToolboxTalk = false;
        });
    }

    ngAfterViewInit() {

    }

    filterByOwner() {
        let filteredRecords = [];
        this.temp_records.forEach(r=> {
            if(r.tagged_owner && this.selectedOwner?.length > 0 && this.selectedOwner.includes(r.tagged_owner.id)) {
                filteredRecords.push(r);
            }
        });
        this.records = filteredRecords;
    }

    checkTagOwner() {
        this.ownerList = [];
        this.records.forEach(r=> {
            if(r.tagged_owner) {
                this.ownerList.push(r.tagged_owner);
            }
        });
        this.ownerList = this.ownerList.filter((e, i) => this.ownerList.findIndex(a => a.id === e.id) === i).sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
        this.filterData = this.renderFilterData();
    }

    onClearTagOwner() {
        this.records = this.temp_records;
    }

    getPlaceholder() {
        let placeholder = 'Type in Title, Name or Company to search...';
        if(!(this.isProjectPortal || this.isCompanyProjectRoute())) {
            placeholder = 'Type in Title, Name to search...';
        }
        return placeholder;
    }

    downloadRegister() {
        this.blockLoader = true;
        this.toolboxTalksService.downloadToolboxTalkRegister(this.projectId, () => {
            this.blockLoader = false;
        });
    }

    enableEdit(type) {
        if (type == 'title') {
            this.titleEditDisable = !(this.titleEditDisable);
        } else if(type == 'company') {
            this.companyEditDisable = !(this.companyEditDisable);
        }
    }

    updateTBT(row) {
        if (!this.talk_title && !this.tagged_owner) return false;
        this.blockLoader = true;
        let request: any = {};
        if (this.talk_title) {
            request.talk_title = this.talk_title;
        }

        if (this.tagged_owner) {
            request.tagged_owner = this.tagged_owner;
        }

        this.toolboxTalksService.updateToolboxTalk(request, row.id).subscribe(this.responseHandler.bind(this));
    }

    responseHandler(out: any) {
        if(out.success) {
            this.modalService.dismissAll();
            this.initializeTable(true);
        } else {
            const message = out.message || 'Failed to store data.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: out });
        }
        this.blockLoader = false;
    }

    onFilterSelection(data){
        this.selectedOwner = data.company.map(a=>a.id);
        this.pageCallback({ offset: 0 }, true);
    }

    searchFunction(data){
        this.search = data.search;
        if(!data.stopReload){
            this.initializeTable(true);
            this.page.pageNumber = 0;
        }
    }
    renderFilterData(){
        return [
            {
                name:'company',
                list:this.ownerList,
                enabled:(this.isProjectPortal || this.isCompanyProjectRoute()),
                state:false
            }
        ];
    }
    private getFilteredBriefingsForProject(regArray) {
        return regArray.filter(({ project_ref }) => {
            const projectId = project_ref?.id || (typeof project_ref === "number" && project_ref);
            return projectId === this.projectId;
        });
    }

    public onActionSelection(actionVal: ActionButtonVal) {
        const code = actionVal.code;
        if(code === ToolboxTalksActionButtons.INVITE_TO_TOOLBOX) {
            this.inviteToToolboxTalkPopup();
        } else if(code === ToolboxTalksActionButtons.DOWNLOAD_REPORT) {
            this.openToolBoxTalksReportModal();
        } else if(code === ToolboxTalksActionButtons.DOWNLOAD_REGISTER) {
            this.downloadRegister();
        } else if(code === ToolboxTalksActionButtons.DASHBOARD) {
            this.openDashboardModal();
        }
    }

    openToolBoxTalksReportModal() {
        this.reportDownloader.openModal();
    }

    async toolBoxTalksReportDownload(event) {
        await this.initiateDownload(event.selection);
        event.closeFn();
    }

    onCancel() {
        this.isModalOpen = false;
    }

    closeDetailReport(event) {
        event.closeFn();
        this.showModal = false;
    }

    closeInviteReport(form) {
        form.reset();
    }

    trackByRowIndex(index: number) {
        return index;
    }

    openDashboardModal() {
        this.loadPowerBiComponent = true;
        this.biModalTitle = 'Toolbox Talks Dashboard';
        if (this.isProjectPortal || this.isCompanyProjectRoute()) {
            this.biToolName = 'toolbox_talks'
            this.dashboardLevel = 'project';
        } else {
            this.biToolName = 'toolbox_talks_company';
            this.dashboardLevel = 'company';
        }
    }

    powerBiDashboardClose() {
        this.loadPowerBiComponent = false;
    }
}
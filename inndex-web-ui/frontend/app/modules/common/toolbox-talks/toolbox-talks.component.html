<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav [employer]="employer" [companyResolverResponse]="companyResolverResponse" *ngIf="!isProjectPortal" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [isCompanyHeader]="!isProjectPortal && !isCompanyProjectRoute()" [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="col-sm-12 my-3 outer-border" *ngIf="!loadingToolboxTalks">
                <div class="col-sm-12 outer-border-radius">
            <div class="d-flex justify-content-between mb-2 flex-wrap gap-8">
                <div class="col-md-5 d-inline-block p-0">
                    <h5 class="float-md-left col-md-12 p-0">Total Toolbox Talks <small>({{ page.totalElements }})</small></h5>
                </div>
                <action-button
                    [actionList]="actionButtonMetaData.actionList"
                    (selectedActionEmmiter)="onActionSelection($event)"
                    [newFeatureTitle]="'New Toolbox Talk'"
                    (onOpenAddNew)="showAddToolboxTalkPop(addToolboxTalkForm)">
                </action-button>
            </div>

            <div>
                <div >
                    <div>
                        <search-with-filters #searchFilters (searchEmitter)="searchFunction($event)" [filterData]="filterData" (filterEmitter)="onFilterSelection($event)"></search-with-filters>
                    </div>
                </div>
                <div class="clearfix"></div>
                <div class="table-responsive-sm">
                    <!-- Toolbox Talk table for Site Admin -->
                    <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                        [scrollbarV]="true"
                        [virtualization]="false"
                        [loadingIndicator]="loadingInlineToolboxTalk"
                        [rows]="records"
                        [footerHeight]="40"
                        [columnMode]="'force'"
                        [rowHeight]="'auto'"
                        [externalPaging]="true"
                        [count]="page.totalElements"
                        [offset]="page.pageNumber"
                        [limit]="page.size"
                        (page)="pageCallback($event, true)"
                        [externalSorting]="true"
                        (sort)="onSort($event)"
                        [sorts] = "[sorts]"
                    >
                        <ngx-datatable-column prop="id" headerClass="font-weight-bold" [sortable]="true" minWidth="110">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Toolbox Talk #
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{row.talk_number}}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column [prop]="(isProjectPortal || isCompanyProjectRoute()) ? 'title' : 'talk_title'" cellClass="text-break" headerClass="font-weight-bold" [sortable]="true" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Title
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{row.talk_title}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="110">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Uploaded By
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{row.user_ref.first_name}} {{row.user_ref?.middle_name || ''}} {{row.user_ref.last_name}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [width]="200" [sortable]="false" minWidth="130">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Date & Time Uploaded
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{ dayjs(+row.createdAt).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS)}}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column *ngIf="(isProjectPortal || isCompanyProjectRoute())" headerClass="font-weight-bold" [sortable]="false" minWidth="110">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Company
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{ row?.tagged_owner?.name}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column *ngIf="(isProjectPortal || isCompanyProjectRoute())" headerClass="font-weight-bold" [sortable]="false" minWidth="120">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                No. Times Briefed
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{row.briefed_count}}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column *ngIf="(isProjectPortal || isCompanyProjectRoute())" headerClass="font-weight-bold" [sortable]="false" minWidth="120">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Last Briefed
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <div class="d-flex flex-column">
                                    <span *ngIf="row?.last_briefed_at else notBriefed">
                                        {{ dayjs(+row?.last_briefed_at).format(AppConstant.defaultDateFormat)}} 
                                        <div class="small">({{ dayjs(+row?.last_briefed_at).format(AppConstant.defaultTimeFormat) }})</div>
                                    </span>
                                    <ng-template #notBriefed>-</ng-template>
                                </div>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="120">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Available
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <div class="col-6">
                                    <input type="checkbox" class="form-control checkbox-small cursor-pointer"
                                        [checked]="checkAvailability(row)"
                                        (click)="makeTalkAvailable($event, row)" [disabled]="isProjectPortal && row.company_ref && row.is_available == 0"/>
                                </div>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold action-column" cellClass="action-column no-ellipsis" [sortable]="false" minWidth="110">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Action
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <button-group
                                    [buttons]="rowButtonGroup"
                                    (onActionClick)="rowBtnClicked($event, row)">
                                </button-group>
                            </ng-template>
                        </ngx-datatable-column>
                    </ngx-datatable>
                    <!-- END Toolbox Talk table for Site Admin -->
                    <block-loader [show]="(processingToolboxTalk)" [showBackdrop]="true" alwaysInCenter="true"></block-loader>
                </div>
                <i-modal #addToolboxTalkRef title="New Toolbox Talk" (onCancel)="onCancel()" (onClickRightPB)="addToolboxTalkRequest(addToolboxTalkForm, $event)" rightPrimaryBtnTxt="Save"
                    [rightPrimaryBtnDisabled]="(!addToolboxTalkForm.valid || (!talkFile))">
                        <form novalidate #addToolboxTalkForm="ngForm">
                            <div class="form-group">
                                <label>Title <small class="required-asterisk">*</small></label>
                                <div class="input-group">
                                    <input type="text" class="form-control"
                                           placeholder="Toolbox talk title..."
                                           name="talk_title"
                                           required="true"
                                           [(ngModel)]="talk_title"
                                           [maxlength]="maxTitleLength">
                                </div>
                                <div class="mb-3 mt-1">
                                    <small class="float-right small-font text-muted">Maximum Characters - {{maxTitleLength}}</small>
                                </div>
                                
                            </div>
                            <div class="form-group" *ngIf="isProjectPortal || isCompanyProjectRoute()">
                                    <label>Company</label>
                                    <company-selector-v2
                                        [required]="false"
                                        [country_code]="projectInfo?.custom_field?.country_code"
                                        name="tagged_owner"
                                        [selectId]="tagged_owner"
                                        placeholder="Select Company"
                                        [classes]="'w-100'"
                                        (selectionChanged)="tagged_owner = $event.selected"
                                        [projectId]="projectId"
                                    ></company-selector-v2>
                            </div>
                            <div class="form-group" *ngIf="isModalOpen">
                                <label>Upload File (PDF or Word Doc.) <small class="required-asterisk">*</small></label>
                                <input type="hidden" name="talk_file_ref" id="talk_file_ref"
                                       [(ngModel)]="talk_file_ref" required="true"/>
                                <file-uploader-v2
                                    [init]='{}'
                                    (uploadDone)="uploadDone($event)"
                                    [allowedMimeType]="allowedMime"
                                    [dragnDropTxt]="'Drag and drop pdf or doc here'"
                                    (deleteFileDone)="fileDeleteDone($event)"
                                    [showDeleteBtn]="true"
                                    [showDragnDrop]="true"
                                    [disabled]="false"
                                    [showHyperlink]="true"
                                    [category]="'toolbox-talks'"
                                ></file-uploader-v2>
                            </div>
                        </form>
                    <block-loader [show]="(addingToolboxTalkInProgress)"></block-loader>
                </i-modal>

                <i-modal #toolboxTalkDetailsRef [showCancel]="false" [title]="talk_row?.talk_title + ' Details'" (onClickRightPB)="closeDetailReport($event)" rightPrimaryBtnTxt="OK">
                        <div *ngIf="(isProjectPortal || isCompanyProjectRoute()) && showModal">
                            <form novalidate #detailToolboxTalkForm="ngForm">
                            <table class="table table-sm table-bordered" style="font-size: 14px;">
                                <tbody>
                                    <tr *ngIf="isProjectPortal">
                                        <td class="tr-bg-dark-color" style="width: 25%;">
                                            <strong>Uploaded By:</strong>
                                        </td>
                                        <td>
                                            {{ talk_row?.user_ref.name }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 25%;">
                                            <strong>Title:</strong>
                                        </td>
                                        <td *ngIf="talk_row.project_ref && !talk_row.company_ref">
                                            <div class="input-group">
                                                <input type="text" class="form-control"
                                                       placeholder="Toolbox talk title..."
                                                       name="talk_title"
                                                       required="true"
                                                       [disabled]="titleEditDisable"
                                                       [(ngModel)]="talk_title">
                                                <div class="input-group-append">
                                                    <button *ngIf="titleEditDisable"class="btn btn-dark" (click)="enableEdit('title')" type="button">Edit</button>
                                                    <button *ngIf="!titleEditDisable" [disabled]="!talk_title" class="btn btn-dark" (click)="updateTBT(talk_row)"  type="button">Save</button>
                                                </div>
                                            </div>
                                        </td>
                                        <td *ngIf="!talk_row.project_ref && talk_row.company_ref" class="text-break">
                                            {{ talk_row?.talk_title }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 25%;">
                                            <strong>Company:</strong>
                                        </td>
                                        <td *ngIf="talk_row.project_ref && !talk_row.company_ref">
                                            <div class="input-group">
                                                <company-selector-v2
                                                    [class.select-with-button]="true"
                                                    [class]="companyEditDisable ? 'taggedOwnerDD' : 'taggedOwnerSaveDD'"
                                                    [required]="true"
                                                    errorClasses="d-none"
                                                    [country_code]="projectInfo?.custom_field?.country_code"
                                                    name="tagged_owner"
                                                    [selectId]="tagged_owner"
                                                    placeholder="Select Company"
                                                    (selectionChanged)="tagged_owner = $event.selected"
                                                    [disabled]="companyEditDisable"
                                                    [projectId]="projectId"
                                                ></company-selector-v2>
                                                <div class="input-group-append">
                                                    <button *ngIf="companyEditDisable" class="btn btn-dark" (click)="enableEdit('company')" type="button">Edit</button>
                                                    <button *ngIf="!companyEditDisable" [disabled]="!tagged_owner" class="btn btn-dark" (click)="updateTBT(talk_row)"  type="button">Save</button>
                                                </div>
                                            </div>
                                        </td>
                                        <td *ngIf="!talk_row.project_ref && talk_row.company_ref">
                                            {{ talk_row?.tagged_owner?.name }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 25%;">
                                            <strong>Document:</strong>
                                        </td>
                                        <td>
                                            <a class="text-info" [title]="talk_row?.talk_file_ref?.name" [href]="talk_row?.talk_file_ref?.file_url" target="_blank"
                                            >
                                                {{ talk_row?.talk_file_ref?.name }}
                                            </a>
                                        </td>
                                    </tr>
                                    <ng-container *ngIf="talk_row.register.length">
                                        <tr>
                                            <td class="tr-bg-dark-color" style="width: 25%;">
                                                <strong>Last Briefed:</strong>
                                            </td>
                                            <td>
                                                {{dayjs(+talk_row.register[0]?.briefed_at).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS)}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="tr-bg-dark-color" style="width: 25%;">
                                                <strong>Last Briefed by:</strong>
                                            </td>
                                            <td>
                                                {{talk_row.register[0]?.briefed_by_name}}
                                                <span *ngIf="talk_row.register[0]?.induction_company">
                                                    ({{talk_row.register[0]?.induction_company}})
                                                </span>
                                            </td>
                                        </tr>
                                    </ng-container>
                                    <tr *ngIf="talk_row.register.length" class="tr-bg-dark-color">
                                        <td colspan="2">
                                            <strong>Register:</strong>
                                        </td>
                                    </tr>
                                    <tr *ngIf="talk_row.register.length">
                                        <td colspan="2">
                                            <table class="table table-sm table-striped table-borderless m-0">
                                                <thead>
                                                <tr>
                                                    <th>Briefing</th>
                                                    <th>Briefed By</th>
                                                    <th>Attendees</th>
                                                    <th>Register</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr *ngFor="let briefed of talk_row.register">
                                                    <td>{{briefed.briefed_at ? dayjs(+briefed.briefed_at).format(AppConstant.defaultDateFormat) : null}} <br> <small>({{briefed.briefed_at ? dayjs(+briefed.briefed_at).format('HH:mm:ss') : null}})</small></td>
                                                    <td>{{briefed.briefed_by_name ? briefed.briefed_by_name : null}}</td>
                                                    <td>{{briefed.allattendees.length}}</td>
                                                    <td>
                                                        <button-group
                                                            [buttons]="talkRegisterButtonGroup"
                                                            (onActionClick)="rowBtnClicked($event, briefed)">
                                                        </button-group>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            </form>
                        </div>
                </i-modal>
                <div class="clearfix"></div>
            </div>
        </div>
        </div>
    </div>
</div>


<i-modal #toolboxInviteModalRef title="Invite to Brief" (onCancel)="closeInviteReport(inviteToolboxForm)" (onClickRightPB)="inviteUsers(inviteToolboxForm)" rightPrimaryBtnTxt="Send Invite" [rightPrimaryBtnDisabled]="!inviteToolboxForm.valid || !hasSelectedUsers">
        <form novalidate #inviteToolboxForm="ngForm">
            <tool-invite-modal
                [hasEmployerFilter]="true"
                [hasSearchByName]="true"
                [toolLabel]="'talk_title'"
                [isFilteredUsersReset]="isFilteredUsersReset"
                [toolSingularPhrase]="'Toolbox Talks'"
                [availableRecords]="availableTalks"
                [jobRoles]="jobRoles"
                [availableEmployers]="availableEmployers"
                [projectUsersData]="projectUsers"
                [filteredUsersData]="filteredUsers"
                (onSelectDeselectUser)="checkHasSelectedUsers($event)"
            ></tool-invite-modal>
        </form>
        <block-loader [show]="processingToolboxInvites"></block-loader>
</i-modal>

<i-modal #toolboxInviteModalCompanyRef title="Invite to Brief" (onCancel)="closeInviteReport(inviteCompanyToolboxForm)" (onClickRightPB)="inviteUsersForCompanyTBTs(inviteCompanyToolboxForm)" rightPrimaryBtnTxt="Send Invite" [rightPrimaryBtnDisabled]="!inviteCompanyToolboxForm.valid || selectedProjects.length < 1">
        <form novalidate #inviteCompanyToolboxForm="ngForm">
            <div class="form-group">
                <label><b>Select Toolbox Talks</b></label>
                <ng-select
                    class="filter-select"
                    appendTo="body"
                    style="width: 100%;"
                    [multiple]="true"
                    placeholder="Select Toolbox Talks"
                    [items]="availableTalks"
                    name="selectedToolboxTalks"
                    [(ngModel)]="selectedToolboxTalks"
                    bindLabel="talk_title" required>
                </ng-select>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-12">
                        <i>Inviting a project to complete a toolbox talk will notify all project admins to brief the workforce.</i>
                    </div>
                    <div class="col-8">
                        <label><b>Select Project</b></label>
                    </div>
                    <div class="col-4">
                        <label>Select All &nbsp;&nbsp;</label>
                        <input type="checkbox" class="cursor-pointer"
                        [checked]="isAllProjectsSelected()"
                        (change)="selectAllProjects($event)"/>
                    </div>
                </div>

                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">Name</th>
                            <th class="text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of (projects || []); trackBy : trackByRowIndex; let i = index;">
                            <td class="text-center">{{ item.name }}</td>
                            <td class="text-center">
                                <input type="checkbox" [checked]="selectedProjects.includes(item)"
                                (change)="changeProjectsModel($event, item)"/>
                            </td>
                        </tr>
                    </tbody>
                </table>

            </div>
        </form>
        <block-loader [show]="processingToolboxInvites"></block-loader>
</i-modal>

<block-loader [show]="(blockLoader || loadingToolboxTalks)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
<report-downloader #reportDownloader
    [xlsxOnly]="true"
    (onFilterSelection)="toolBoxTalksReportDownload($event)"
    >
</report-downloader>

<power-bi-dashboard *ngIf="loadPowerBiComponent" (powerBiDashboardClose)="powerBiDashboardClose()" [portalType]="dashboardLevel" [modalTitle]="biModalTitle" [toolLabel]="biToolLabel" [toolName]="biToolName"></power-bi-dashboard>
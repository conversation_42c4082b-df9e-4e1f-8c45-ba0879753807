<div class="d-flex" id="wrapper">
    <company-side-nav [employer]="employer" [companyResolverResponse]="companyResolverResponse" *ngIf="isCompanyRoute()" ></company-side-nav>
    <div class="w-100 px-3 detail-page-header-margin" [ngClass]="{'ml-fix': !is_mobile_nav}">
        <div class="row">
            <project-header [isCompanyHeader]="isCompanyRoute()" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="col-sm-12 my-3 outer-border">
                <div class="my-3 outer-border-radius">
            <div class="col-sm-12 my-2">
                <h5 class="float-md-left">Total E-Learning Modules <small>({{ allELearningModule.length }})</small></h5>
                <div class="col-md-3 float-md-right mb-3 pt-2 pb-2">
                    <button class="btn btn-sm btn-im-helmet float-md-left mr-2" (click)="addModulePopup()">Add Module</button>
                    <button class="btn btn-sm btn-im-helmet float-md-right" (click)="inviteToModulePopup()">Invite to Module</button>
                </div>
                <div class="clearfix"></div>
                <ngx-skeleton-loader *ngIf="eLearningsLoading" count="8" [theme]="{ 'border-radius': '0', height: '30px', width: '100%' }"></ngx-skeleton-loader>
                <div class="table-responsive-sm" *ngIf="!eLearningsLoading">
                    <ngx-datatable #table class="bootstrap table table-hover table-sm"
                                   [rows]="allELearningModule"
                                   [limit]="50"
                                   [footerHeight]="40"
                                   [columnMode]="'force'"
                                   [rowHeight]="'auto'"
                    >
                        <ngx-datatable-column headerClass="font-weight-bold">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Module #
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{ row.id }}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Title
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{ row.title }}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [width]="300">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Uploaded
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{ dayjs(row.createdAt)}}
                            </ng-template>
                        </ngx-datatable-column>

                        <ngx-datatable-column headerClass="font-weight-bold action-column" cellClass="action-column myclass" style="backface-visibility:visible">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Action
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <button-group
                                    [buttons]="baseButtonConfig"
                                    (onActionClick)="rowBtnClicked($event, row)">
                                </button-group>
                            </ng-template>
                        </ngx-datatable-column>
                    </ngx-datatable>
                </div>


                <block-loader [show]="(processingELearningModule)"></block-loader>
                <ng-template #addModuleHtml let-c="close" let-d="dismiss">
                    <div class="modal-header">
                        <h4 class="modal-title">E-Learning Module</h4>
                        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form novalidate #addModuleForm="ngForm">
                            <div class="form-group">
                                <label>Title <small class="required-asterisk ">*</small></label>
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control"
                                           placeholder="Please enter module title"
                                           name="talk_title"
                                           required="true"
                                           [(ngModel)]="eLearningModule.title">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Add Video / PDF <small class="required-asterisk ">*</small></label>
                                <ng-template ngFor let-item [ngForOf]="(eLearningModule.media_file_ids || [])" let-i="index">
                                    <li class="list-group-item border-0" style="background: transparent;">
                                        <div class="form-group row">
                                            <div class="col-sm-8">
                                                <file-uploader-v2
                                                    class="pl-0"
                                                    [init]="item" [dragnDropTxt]="'Drag and drop video or pdf here'"
                                                    [category]="'module-media'"
                                                    [showHyperlink]="true"
                                                    (uploadDone)="mediaUploadDone($event, i)"
                                                    [allowedMimeType]="['video/mp4', 'video/quicktime', 'application/pdf', 'application/x-pdf']"
                                                    (deleteFileDone)="fileDeleteDone($event)"
                                                    [showDeleteBtn]="true"
                                                    [disabled]="false"
                                                    #mediaUploader
                                                >
                                                </file-uploader-v2>
                                            </div>
                                        </div>
                                    </li>
                                </ng-template>
                            </div>
                            <div class="form-group">
                                <label>Questions <small class="required-asterisk ">*</small></label>
                                <table class="table table-bordered">
                                    <tbody>
                                        <ng-template ngFor let-item [ngForOf]="(eLearningModule.questions || [])" let-i="index">
                                            <tr>
                                                <td>
                                                    <input type="text" class="form-control"
                                                           placeholder="Please enter question" #question="ngModel"
                                                           [(ngModel)]="eLearningModule.questions[i].question"
                                                           ng-value="eLearningModule.questions[i].question"
                                                           name="{{'question' + i}}"
                                                           required autocomplete="off">
                                                    <div class="alert alert-danger mb-0 p-0 pl-2" [hidden]="!(question.errors && question.errors.required)">Question is required.</div>
                                                </td>
                                                <td class="text-center">
                                                    <button type="button" class="btn btn-primary m-auto" (click)="openOptionsModal(i)" [disabled]="!eLearningModule.questions[i].question">Add Options</button>
                                                </td>
                                                <td class="text-center">
                                                    <span class="material-symbols-outlined text-danger cursor-pointer" (click)="removeQuestion($event, i)" *ngIf="!(i == 0)">
                                                        delete
                                                    </span>
                                                </td>
                                            </tr>
                                        </ng-template>
                                        <tr>
                                            <td colspan="3">
                                                <i class="fa fa-plus-circle text-primary cursor-pointer" (click)="addQuestion()">Add Question</i>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-primary"
                                [disabled]="!addModuleForm.valid"
                                (click)="saveELearningModule(addModuleForm, c)">
                            <span *ngIf="!eLearningModule.id">Add Module</span>
                            <span *ngIf="eLearningModule.id">Update Module</span>
                        </button>
                    </div>
                </ng-template>

                <ng-template #questionOptionsHtml let-c="close" let-d="dismiss">
                    <div class="modal-header">
                        <h4 class="modal-title">Add Options</h4>
                        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form novalidate #addOptionsForm="ngForm">
                            <div class="form-group">
                                <label>Options <small class="required-asterisk ">*</small></label>
                                <table class="table table-bordered">
                                    <tbody>
                                        <ng-template ngFor let-item [ngForOf]="(eLearningModule.questions[questionIndex].options || [])" let-i="index">
                                            <tr>
                                                <td>
                                                    <options-row-component
                                                        [initialValue]="item"
                                                        (store)="updateOptionRow($event, i)"
                                                    ></options-row-component>
                                                </td>
                                                <td class="text-center">
                                                    <input type="checkbox" (change)="correctOption($event, i)"
                                                           [checked]="this.eLearningModule.questions[this.questionIndex].options[i] && isAmongCorrectOption(eLearningModule.questions[questionIndex].options[i], i)"
                                                           [disabled]="!this.eLearningModule.questions[this.questionIndex].options[i]">
                                                </td>
                                                <td class="text-center">
                                                    <span class="material-symbols-outlined text-danger cursor-pointer" (click)="removeOption($event, i)" *ngIf="!(i == 0)">
                                                        delete
                                                    </span>
                                                </td>
                                            </tr>
                                        </ng-template>
                                        <tr>
                                            <td colspan="3">
                                                <i class="fa fa-plus-circle text-primary cursor-pointer" (click)="addOption()">Add Option</i>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-primary"
                                [disabled]="!addOptionsForm.valid"
                                (click)="associateOptionsWithQuestion(addOptionsForm, c)">Save
                        </button>
                    </div>
                </ng-template>

                <ng-template #viewModuleHtml let-c="close" let-d="dismiss">
                    <div class="modal-header">
                        <h4 class="modal-title">{{ eLearningModule.title }}</h4>
                        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-12 text-left" *ngIf="eLearningModule.media_file_ids.length">
                                <p class="mb-1"><strong class="mr-1">Video / PDF:</strong><a [href]="getFileLink(eLearningModule.media_file_ids)" target="_blank" class="ml-2">Click to View</a></p>
                            </div>

                            <div class="col-md-12">
                                <p class="mb-1"><strong class="mr-1">Questions / Answers List:</strong></p>
                                <ul class="ml-2 list-unstyled">
                                    <ng-template ngFor let-item [ngForOf]="(eLearningModule.questions || [])" let-i="index">
                                        <li>
                                            <span class="mr-1">{{ i+1 }}.</span><span class="mr-1">Question:</span>{{ eLearningModule.questions[i].question }}
                                            <ul>
                                                <ng-template ngFor let-item [ngForOf]="(eLearningModule.questions[i].options || [])" let-j="index">
                                                    <li>
                                                        {{ eLearningModule.questions[i].options[j] }}
                                                        <i *ngIf="eLearningModule.answers[i].answers.includes(eLearningModule.questions[i].options[j])" class="ml-1 fa fa-check"></i>
                                                    </li>
                                                </ng-template>
                                            </ul>
                                        </li>
                                    </ng-template>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-primary" aria-label="Close" (click)="d('Cross click')">
                            Close
                        </button>
                    </div>
                </ng-template>

                <ng-template #inviteToModuleHtml let-c="close" let-d="dismiss">
                    <div class="modal-header">
                        <h4 class="modal-title">Invite to E-Learning Module</h4>
                        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form novalidate #inviteToModuleForm="ngForm">
                            <div class="form-group">
                                <label>Select Module <small class="required-asterisk ">*</small></label>
                                <ng-select
                                    bindLabel="title"
                                    placeholder="Select Module"
                                    name="selectedModule"
                                    required
                                    style="width: 100%;"
                                    (change)="onSelectModule($event)"
                                >
                                    <ng-option [value]="item.id" *ngFor="let item of allELearningModule">
                                        {{item.title}}
                                    </ng-option>
                                </ng-select>
                            </div>

                            <div class="form-group">
                                <label>Filter Employees by Job Role</label>
                                <ng-select
                                    style="width: 100%;"
                                    placeholder="Filter Employees by Job Role"
                                    [items]="employees_job_roles"
                                    name="selectedRole"
                                    (change)="onSelectJobRole($event)"
                                    bindLabel="job_role"
                                    bindValue="job_role"
                                >
                                </ng-select>
                            </div>

                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-8">
                                        <label>Select Employee <small class="required-asterisk ">*</small></label>
                                    </div>

                                    <div class="custom-control custom-checkbox col-md-4">
                                        <input type="checkbox" class="custom-control-input" [checked]="(filtered_employees.length == selectedEmployees.length)"
                                               (change)="selectAllEmployees($event)" id="selectAllEmployee"/>
                                        <label class="custom-control-label" for="selectAllEmployee">Select All</label>
                                    </div>
                                </div>

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th class="text-center">Name</th>
                                            <th class="text-center">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let item of (filtered_employees || []); trackBy : trackByRowIndex; let i = index;">
                                            <td class="text-center">{{ item.user_ref.first_name }} {{ item.user_ref.last_name }}</td>
                                            <td class="text-center">
                                                <div class="custom-control custom-checkbox">
                                                    <input type="checkbox" class="custom-control-input" [checked]="selectedEmployees.includes(item.user_ref.id)"
                                                           (change)="toggleEmployeeSelection($event, item)" id="selectEmployee{{i}}"/>
                                                    <label class="custom-control-label" for="selectEmployee{{i}}"></label>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <block-loader [show]="processingInviteToModule"></block-loader>
                        <button type="button" class="btn btn-outline-primary" aria-label="Close" (click)="d('Cross click')">Close</button>
                        <button type="button" class="btn btn-outline-primary" [disabled]="!inviteToModuleForm.valid || !selectedEmployees.length || !selectedModule" (click)="sendInvitation(inviteToModuleForm, c)">Send Invitation</button>
                    </div>
                </ng-template>
            </div>
        </div>
        </div>
        </div>
    </div>
</div>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

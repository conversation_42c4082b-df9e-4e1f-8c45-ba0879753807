import {Component, OnInit, TemplateRef, ViewChild} from "@angular/core";
import {NgbModal, NgbModalConfig} from "@ng-bootstrap/ng-bootstrap";
import * as dayjs from 'dayjs';
import {ActivatedRoute} from "@angular/router";
import {AuthService, UserService, ELearningModuleService, ELearningModule, Project, ToastService, HttpService, ELearningConfModalType} from "@app/core";
import {AppConstant} from "@env/environment";
import { ActionBtnEntry, GenericConfirmationModalComponent } from "@app/shared";

@Component({
    templateUrl: './e-learning-module.component.html',
    providers: [NgbModalConfig, NgbModal]
})
export class ELearningModuleComponent implements OnInit {

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    img_link: string = `/images/project-placeholder.png`;
    employerId: number = 0;
    allELearningModule: Array<any> = [];
    eLearningModule: ELearningModule = new ELearningModule;
    processingELearningModule: boolean = false;
    allowedMime : Array<any> = ['application/pdf', 'application/x-pdf'];
    questionIndex: number;
    question_id: number = 1;
    correctOptionsIndex: Array<any> = [];
    employer: any = {};
    processingInviteToModule: boolean = false;
    eLearningsLoading: boolean = false;
    company_employees: Array<any> = [];
    employees_job_roles: Array<any> = [];
    filtered_employees: Array<any> = [];
    selectedEmployees: Array<any> = [];
    selectedModule: number = 0;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    is_mobile_nav: boolean = false;
    baseButtonConfig: Array<ActionBtnEntry> = [
        {
            key: 'view',
            label: '',
            title: 'View',
            mat_icon: 'search',
        },
        {
            key: 'edit',
            label: '',
            title: 'Edit',
            mat_icon: 'edit_note',
        },        
    ];

    constructor(
        private activatedRoute: ActivatedRoute,
        private userService: UserService,
        private toastService: ToastService,
        private eLearningModuleService: ELearningModuleService,
        private modalService: NgbModal,
        private httpService: HttpService,
    ) {
        this.is_mobile_nav = this.httpService.isMobileDevice();
    }

    ngOnInit(): void {
        this.eLearningsLoading = true;
        this.activatedRoute.params.subscribe(params => {
            if(this.isCompanyRoute()) {
                this.employerId = params['employerId'];
                this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
                this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
                this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
                this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
                this.img_link =  this.employer.logo_file_url;
                this.getAllELearningModule();
                this.fetchUsersByEmployer();
            }
        });

        this.addMediaBlock();
    }

    dayjs(n: number, format?: any) {
        return dayjs(n, format).format(AppConstant.defaultDateFormat);
    };

    isCompanyRoute() {
        let routeName = this.activatedRoute.snapshot.url[0].path;
        return routeName === 'company-admin';
    }

    getAllELearningModule() {
        this.eLearningModuleService.getAllELearningModule(this.employerId).subscribe((data: any) => {
            this.eLearningsLoading = false;
            if(data.success && data.eLearningModules) {
                this.allELearningModule = data.eLearningModules;
            } else {
                const message = `Failed to fetch e-learning modules.`;
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    fetchUsersByEmployer() {
        this.userService.getUsersByEmployer(this.employerId).subscribe((data: any) => {
            if(data && data.success) {
                this.company_employees = data.employees_details;
                this.employees_job_roles = this.company_employees.map(employee => {
                    return { job_role: employee.job_role };
                });
            } else {
                const message = data.message || 'Failed to fetch employees.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    @ViewChild('addModuleHtml', { static: true })
    private addModuleHtmlRef: TemplateRef<any>;
    addModulePopup() {
        this.resetModulePopup();
        return this.openModal(this.addModuleHtmlRef, 'lg');
    }

    resetModulePopup() {
        this.question_id = 1;
        this.eLearningModule.title = '';
        this.eLearningModule.media_file_ids = [{}];
        this.eLearningModule.questions = [{
            id : this.question_id,
            question : '',
            multichoice : false,
            options : []
        }];
        this.eLearningModule.answers = [];
        this.question_id += 1;
    }

    mediaUploadDone(data:any, index:number){
        if(data && data.userFile){
            this.eLearningModule.media_file_ids[index] = data.userFile;
        }
    }

    addMediaBlock() {
        if(!this.eLearningModule.media_file_ids){
            this.eLearningModule.media_file_ids = [];
        }
        this.eLearningModule.media_file_ids.push({});
    }

    addQuestion() {
        if(!this.eLearningModule.questions) {
            this.eLearningModule.questions = [];
        }
        this.eLearningModule.questions.push({
            id : this.question_id,
            question : '',
            multichoice : false,
            options : []
        });

        this.question_id += 1;
    }

    removeQuestion($event, questionIndex) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Remove',
            title: `Are you sure you want to remove this question?`,
            confirmLabel: 'Remove',
            onConfirm: () => {
                this.eLearningModule.questions.splice(questionIndex, 1);
                 this.eLearningModule.answers.splice(questionIndex, 1);
            }
        });
    }

    @ViewChild('questionOptionsHtml', { static: true })
    private questionOptionsHtmlRef: TemplateRef<any>;
    openOptionsModal(index) {
        this.questionIndex = index;
        this.correctOptionsIndex = [];
        if (this.eLearningModule.questions[this.questionIndex] && this.eLearningModule.questions[this.questionIndex].options && !this.eLearningModule.questions[this.questionIndex].options.length) {
            this.addOption();
        } else if (this.eLearningModule.questions[this.questionIndex].options.length) {
            let result = this.eLearningModule.answers.find(ans => ans.question_id == this.eLearningModule.questions[this.questionIndex].id);
            ((result && result.answers) || []).map(answer => {
                let index = this.eLearningModule.questions[this.questionIndex].options.indexOf(answer);
                this.correctOptionsIndex.push(index);
            });
        }
        return this.openModal(this.questionOptionsHtmlRef, 'md');
    }

    addOption() {
        if(!this.eLearningModule.questions[this.questionIndex].options){
            this.eLearningModule.questions[this.questionIndex].options = [];
        }
        this.eLearningModule.questions[this.questionIndex].options.push('');
    }

    updateOptionRow(optionText, optionIndex) {
        if (optionText) {
            this.eLearningModule.questions[this.questionIndex].options[optionIndex] = optionText;
        }
    }

    removeOption($event, optionIndex) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Remove',
            title: `Are you sure you want to remove this option?`,
            confirmLabel: 'Remove',
            onConfirm: () => {
                this.eLearningModule.questions[this.questionIndex].options.splice(optionIndex, 1);
                let i = this.correctOptionsIndex.indexOf(optionIndex);
                this.correctOptionsIndex.splice(i, 1);
            }
        });
    }

    correctOption($event, optionIndex) {
        if ($event.target.checked) {
            this.correctOptionsIndex.push(optionIndex);
        } else {
            let i = this.correctOptionsIndex.indexOf(optionIndex);
            this.correctOptionsIndex.splice(i, 1);
        }
    }

    isAmongCorrectOption(optionText, optionIndex) {
        let selectedOption = this.eLearningModule.questions[this.questionIndex].options[optionIndex];
        if (this.eLearningModule.answers && this.eLearningModule.answers.length && this.eLearningModule.answers[this.questionIndex]) {
            return this.eLearningModule.answers[this.questionIndex].answers.includes(selectedOption);
        }
        return false;
    }

    editModulePopup(row) {
        this.eLearningModule = row;
        return this.openModal(this.addModuleHtmlRef, 'lg');
    }

    @ViewChild('viewModuleHtml', { static: true })
    private viewModuleHtmlRef: TemplateRef<any>;
    viewModulePopup(row) {
        this.eLearningModule = row;
        return this.openModal(this.viewModuleHtmlRef, 'lg');
    }

    openModal(content, size){
        this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size
        });
        return false;
    }

    associateOptionsWithQuestion(form, cb) {
        let emptyOptionFields = this.eLearningModule.questions[this.questionIndex].options
            .filter((option, index) => !option);
        if (!form.valid || emptyOptionFields.length) {
            const message = 'Something went wrong, form data is not valid.';
            this.toastService.show(this.toastService.types.INFO, message, { data: form.errors });
            return false;
        }
        if (!this.correctOptionsIndex.length) {
            const message = 'Please tick at least one option as correct option.';
            this.toastService.show(this.toastService.types.INFO, message);
            return false;
        }

        let correctOptions = this.eLearningModule.questions[this.questionIndex].options
            .filter((questionOption, index) => this.correctOptionsIndex.includes(index));
        this.eLearningModule.answers[this.questionIndex] = {
            question_id: this.eLearningModule.questions[this.questionIndex].id,
            answers: correctOptions
        };

        if (correctOptions.length > 1) {
            this.eLearningModule.questions[this.questionIndex].multichoice = true;
        }
        console.log(this.eLearningModule);
        cb();
    }

    saveELearningModule(form, cb) {
        if (!form.valid) {
            const message = 'Something went wrong, form data is not valid.';
            this.toastService.show(this.toastService.types.INFO, message, { data: form.errors });
            return false;
        }

        console.log('Request', this.eLearningModule);
        this.processingELearningModule = true;
        if(this.eLearningModule.id) {
            console.log('Updating e-learning module.');
            // update e-Learning Module
            this.eLearningModuleService.updateELearningModule(this.employerId, this.eLearningModule.id, this.eLearningModule).subscribe((data:any) => {
                if (data && data.eLearningModule) {
                    //update module list
                    this.getAllELearningModule();
                    return data.eLearningModule;
                }
                const message = `Failed to fetch update e-learning module, id: ${this.eLearningModule.id}.`;
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            });
        } else {
            console.log('Creating e-learning module.');
            this.eLearningModuleService.createELearningModule(this.employerId, this.eLearningModule).subscribe((data:any) => {
                if (data && data.eLearningModule) {
                    //update module list
                    this.getAllELearningModule();
                    return data.eLearningModule;
                }
                const message = 'Failed to create e-learning module.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            });
        }
        cb();
        this.processingELearningModule = false;
    }

    fileDeleteDone($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.eLearningModule.media_file_ids = [{}];
        }
    }

    getFileLink(media_files) {
        //Till, only media file available in a module
        let link = media_files.reduce((link, media_file) => media_file.file_url, '');
        return link;
    }

    @ViewChild('inviteToModuleHtml', { static: true })
    private inviteToModuleHtmlRef: TemplateRef<any>;
    inviteToModulePopup() {
        this.selectedEmployees = [];
        this.selectedModule = 0;
        this.filtered_employees = this.company_employees;
        return this.openModal(this.inviteToModuleHtmlRef, 'lg');
    }

    onSelectModule($event) {
        if ($event) {
            this.selectedModule = $event
        } else {
            this.selectedModule = 0;
        }
    }

    onSelectJobRole($event) {
        this.selectedEmployees = [];
        if ($event) {
            this.filtered_employees = this.company_employees.filter(employee => employee.job_role == $event.job_role);
        } else {
            this.filtered_employees = this.company_employees
        }
    }

    trackByRowIndex(index: number, obj: any) {
        return index;
    }

    toggleEmployeeSelection($event, item) {
        if ($event.target.checked) {
            this.selectedEmployees.push(item.user_ref.id);
        } else {
            let i = this.selectedEmployees.indexOf(item.user_ref.id);
            this.selectedEmployees.splice(i, 1);
        }
    }

    selectAllEmployees($event) {
        if ($event.target.checked) {
            this.selectedEmployees = this.company_employees.map(employee => employee.user_ref.id);
        } else {
            this.selectedEmployees = [];
        }
    }

    sendInvitation(form, cb) {
        if (!form.valid) {
            const message = 'Something went wrong, form data is not valid.';
            this.toastService.show(this.toastService.types.INFO, message, { data: form.errors });
            return false;
        }

        let moduleInvitation = {
            selected_module: this.selectedModule,
            selected_employees: this.selectedEmployees
        };
        this.eLearningModuleService.inviteToELearningModule(this.employerId, moduleInvitation).subscribe(data => {
            cb();
            if(data && data.success && data.status) {
                this.toastService.show(this.toastService.types.SUCCESS, data.status);
            } else {
                const message = 'Failed to send invitation to employees.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any, qclReportContent, defectsHtml): void {
        const actionMap = {
            'view': () => this.viewModulePopup(row),
            'edit': () => this.editModulePopup(row),
        };
        const action = actionMap[entry.key];
        if (action) {
            action();
        }
    }
}

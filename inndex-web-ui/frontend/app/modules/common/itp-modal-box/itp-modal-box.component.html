<style>
    .score-type-select.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {
        background-color: unset;
        border-color: #adb5bd;
    }
    .file-ref{
        border: 0.5px solid #d9d9d9;
        border-radius: 3px;
        padding: 12px 9px 12px 9px;
    }
    .sign-off table td{
        border-top: none;
    }
</style>
<input type="checkbox" class="custom-control-input" [(ngModel)]="project.project_section_access.quality_checklist"
[checked]="project.project_section_access.quality_checklist" [disabled]="disableToolStatus(itp_admin)" name="qcl" id="qcl">
<label class="custom-control-label" for="qcl">{{ project.custom_field.qcl_phrase }}</label>
<i class="fas ml-1 cursor-pointer fa-plus-square" (click) = "expandItpsModal()"></i>

<create-or-edit-itp #createOrEditItp [qualityCL]= "qualityCL" [project]="project" (fetchQCLData)="getQCLdata($event)" ></create-or-edit-itp>
<i-modal #itpAccessModalHtml title="ITP's" [showCancel]='false'  rightPrimaryBtnTxt = 'Done' (onClickRightPB)="closeItpAccessModal()">
    <form novalidate #itpForm="ngForm">
        <ng-template ngFor let-item [ngForOf]="(qualityChecklist || [])" let-i="index">
            <div class="form-group">
                <div class="custom-control custom-checkbox horizontal-center">
                    <div class="horizontal-center" >
                        <input type="checkbox" class="custom-control-input"
                        [checked]="isItpChecked(item)" name="itp" id="itp_enabled_{{item.id}}" (click)="toggleQcClStatus($event, item)">
                        <label class="mb-0 custom-control-label" for="itp_enabled_{{item.id}}"><span>{{item.qc_title}}</span></label>
                        <i *ngIf = "item.itp_type === 'project'" (click)="openCreateOrOpenItpModal(item)" class="ml-2 fas fa-edit cursor-pointer align-top"></i>
                    </div>
                </div>
            </div>
        </ng-template>
        <label class="cursor-pointer" (click)="openCreateOrOpenItpModal(qualityCL)"><i class="fa fa-plus text-primary mr-1"></i> Build {{ project.custom_field.qcl_phrase_singlr }}</label>
    </form>
    <block-loader [show]="loading" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
</i-modal>

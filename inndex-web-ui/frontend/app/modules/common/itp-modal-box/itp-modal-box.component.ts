import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { ITP_TYPE, Project, QualityChecklist, QualityChecklistsService, ToastService } from "@app/core";
import { GenericConfirmationModalComponent } from '@app/shared';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { IModalComponent } from "@app/shared";
import { CreateorEditITPComponent } from '../create-or-edit-itp/create-or-edit-itp.component';

@Component({
    selector: 'itp-modal-box',
    templateUrl:'itp-modal-box.component.html',
    providers: [ NgbModal ],
})
export class ItpModalBoxComponent implements OnInit {
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    @Input() project: Project = new Project;
    @Input() itp_admin: string;
    qualityChecklist: any = [];
    qualityCL: QualityChecklist = new QualityChecklist;
    fieldsValid: boolean = true;
    loading: boolean = false;
    editModalOpened = false;
    projectId: number;
    constructor(
        private qualityCLService: QualityChecklistsService,
        private toastService: ToastService,
        ) {
    }
    ngOnInit() {
    }

    ngOnChanges(changes: any) {
        this.projectId = changes?.project?.currentValue?.id;
        if(this.projectId) {
            this.getQCLdata();
        }
    }

    @ViewChild('itpForm') itpForm;
    @ViewChild('itpAccessModalHtml') private itpAccessGenricModal: IModalComponent;
     expandItpsModal() {
        return this.itpAccessGenricModal.open();
    }
    toggleQcClStatus( $event, item:QualityChecklist ) {
        item.qc_doc = item.qc_doc.map(({ id }) => id).filter(id => id != null);
        let enabled = $event.target.checked;
        if (item.itp_type == 'company') {
            let existingRecord = (item.activate_on_projects || []).find(item => +item.project_id === +this.project.id);
            if (existingRecord) {
                let existingRecordIndex = (item.activate_on_projects || []).findIndex(item => +item.project_id === +this.project.id);
                existingRecord["enabled"] = enabled;
                item.activate_on_projects[existingRecordIndex] = existingRecord;
            } else {
                item.activate_on_projects.push({
                    "project_id": +this.project.id,
                    "enabled": enabled
                })
            }
            let activate_on_projects = item.activate_on_projects.filter((obj, index) => {
                return index === item.activate_on_projects.findIndex(o => +obj.project_id === +o.project_id);
            });

            item.activate_on_projects =  activate_on_projects;
        }else{
            item.project_ref = item.project_ref.id;
            item.enabled = enabled;
        }
        this.loading = true;
        this.qualityCLService.updateQChecklist(this.project.id, item.id, item).subscribe(data => {
            if(data && data.success){
                this.getQCLdata();
            } else {
                this.loading = false;
                this.toastService.show(this.toastService.types.ERROR, 'Something went wrong while updating the ITP status.');
            }
        })
    }
    getQCLdata($event?){
        this.qualityCLService.getProjectQChecklists(this.project.id, 0).subscribe((data: any) => {
            if(data){
                this.qualityChecklist = data.project_checklists;
                this.loading = false;
            }
        })
    }
    @ViewChild('createOrEditItp') createOrEditItp: CreateorEditITPComponent;
    openCreateOrOpenItpModal( qualityCL? ){
        this.createOrEditItp.openCreateOrOpenItpModal(JSON.parse(JSON.stringify(qualityCL)), ITP_TYPE.project)
    }
    closeItpAccessModal(){
        this.itpAccessGenricModal.close();
    }

    disableToolStatus(status:string):boolean{
        return (status === 'lock-on' || status === 'lock-off');
    }
   
    isItpChecked(item:QualityChecklist){
        if(item.itp_type === 'project'){
            return item.enabled;
        }else{
            let existsOnProjects = item.activate_on_projects.find(a=> a.project_id === this.project.id);
            return existsOnProjects ? existsOnProjects.enabled : false;
        }
    }
}

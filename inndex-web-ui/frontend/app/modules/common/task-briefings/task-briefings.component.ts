import {Component, OnInit, TemplateRef, ViewChild, ElementRef} from "@angular/core";
import {ActivatedRoute, Router} from "@angular/router";
import { HttpParams } from '@angular/common/http';
import {
    AuthService,
    ProjectService,
   ToastService, ProjectFeaturesService,
    User,
    UserService,
    Project,
    isInheritedProjectOfCompany,
    Common,
    HttpService,
    TaskBriefingsActionButtons,
    ActionButtonVal,
    ToolboxTalksService
} from "@app/core";
import {NgbDateStruct, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import * as dayjs from 'dayjs';
import {forkJoin, Observable, fromEvent} from "rxjs";
import {debounceTime, distinctUntilChanged, filter, tap} from "rxjs/operators";
import * as ExcelProper from "exceljs";
import * as fs from "file-saver";
import * as Excel from "exceljs/dist/exceljs.min.js";
import {AppConstant} from "@env/environment";
import { SearchWithFiltersComponent } from "../search-with-filters/search-with-filters.component";
import { filterData } from "@app/core";
import { ReportDownloaderComponent } from "../report-downloader/report-downloader.component";
import { ActionBtnEntry, IModalComponent } from "@app/shared";
import { TaskBriefing } from "@app/core/models/task-briefing.model";


@Component({
    templateUrl: './task-briefings.component.html',
    styleUrls: ['./task-briefings.component.scss'],
    styles: [`
        :host ::ng-deep .datatable-body-cell {
            overflow-x: visible !important;
        }

        .tableDownloadItem { left: -22px !important; }
    `]
})

export class TaskBriefingsComponent implements OnInit {
    @ViewChild('reportDownloader') private reportDownloader: ReportDownloaderComponent;
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;
    blobType: string = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    employerId: number = 0;
    records: Array<any> = [];
    projectUsers: Array<any> = [];
    filteredUsers: Array<any> = [];
    hasSelectedUsers: boolean = false;
    isFilteredUsersReset:boolean = false;
    authUser$: User;
    briefing_row:  TaskBriefing = new TaskBriefing();
    briefing_attendees:  Array<any> = [];
    allowedMime : Array<any> = ['application/pdf', 'application/x-pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    task_briefing_file = null;
    briefing_file_ref = null;
    convert_doc_to_pdf: boolean = false;
    briefingFile: any;
    img_link: string = `/images/project-placeholder.png`;
    processingTaskBriefing = false;
    addingTaskBriefingInProgress = false;
    employer: any = {};
    processingBriefingInvites: boolean = false;
    inviteApiError = null;
    inviteApiSuccess = null;
    inviteModalRef = null;
    jobRoles: any = [];
    is_inherited_project: boolean = false;
    loadingBriefings: boolean = false;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    tbPhrase: string = ``;
    tbSglrPhrase: string = ``;
    pagination = new Common();
    page = this.pagination.page;
    blockLoader : boolean = false;
    temp_records: Array<any> = [];
    ownerList: Array<any> = [];
    search:any = null;
    tagged_owner: number;
    selectedOwner:number[]=[];
    titleEditDisable: boolean = true;
    companyEditDisable: boolean = true;
    briefing_title: string = '';
    modalRef = null;
    availableRecords: Array<any> = [];
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    isProjectPortal: boolean = false;
    search_term: string = null;
    selectedEmployer: string = null;
    availableEmployers: Array<any> = [];
    isMobileDevice: boolean = false;
    searchWithFilter:any;
    filterData:filterData[] = this.renderFilterData();
    reloadFilterData:boolean = false;
    showAddTaskBriefingModal:boolean = false;
    showTaskBriefingDetailModal:boolean = false;
    actionButtonMetaData = {
        actionList: [],
        class: 'material-symbols-outlined',
    };
    sorts: {
        dir:string, prop: string
    } = {
        dir:'desc', prop: 'id'
    };
    isInitTaskBriefing = false;
    loadingInlineTaskBriefing: boolean = false;
    toolKey: string = 'task_briefings';
    rowButtonGroup: Array<ActionBtnEntry> = [];
    taskBriefingRegisterButtonGroup: Array<ActionBtnEntry> = [
        {
            key: 'download_register',
            label: '',
            title: 'Download Register',
            mat_icon: 'download',
        }
    ];

    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private toastService: ToastService,
        private projectFeaturesService: ProjectFeaturesService,
        private authService: AuthService,
        private projectService: ProjectService,
        private modalService: NgbModal,
        private userService: UserService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private httpService: HttpService,
        private toolboxTalksService: ToolboxTalksService
    ) {
        this.from_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.to_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
        }
    }

    AppConstant = AppConstant;
    dayjs(n: number) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };

    ngOnInit() {
        if(!this.isProjectPortal) {
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
                this.employerId = params['employerId'];
            });
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
            this.img_link =  this.employer.logo_file_url;
        } else {
            this.initializeTable();
        }

        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });
    }

    @ViewChild('searchFilters') searchFilters: SearchWithFiltersComponent;
    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean) {
        if (!this.isInitTaskBriefing) {
          this.isInitTaskBriefing = true;
          return;
        }
        this.page.pageNumber = pageInfo.offset;
        this.initializeTable(isPageChange);
    }

    async initiateDownload(resp) {
        this.blockLoader = true;
        this.from_date = resp.fromDate;
        this.to_date = resp.toDate;
        this.downloadTaskBriefingReport();
    }

    // Will get called for Company route only.
    projectRetrieved(project: Project){
        this.projectInfo = project;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
        this.initializeTable();
    }

    private initializeTable(isPageChange?: boolean) {
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('is_inherited_project', `${this.is_inherited_project}`)
            .set('sortKey', 'status')
            .set('sortKey', this.sorts.prop)
            .set('sortDir', this.sorts.dir)
            .set('search',`${this.search ? encodeURIComponent(this.search): ''}`);

        if(this.selectedOwner.length){
            params = params.append('tagged_owner', this.selectedOwner.join(','));
        }
        let extra = ['briefing-count'];
        if(!this.page.pageNumber && !this.search && !this.selectedOwner.length){
            extra.push('tagged-owners');
        }
        params = params.append('extra', extra.join(','));

        this.tbPhrase = this.projectInfo ? this.projectInfo.custom_field.tb_phrase : '';
        this.tbSglrPhrase = this.projectInfo ? this.projectInfo.custom_field.tb_phrase_singlr : '';
        this.buildButtonGroup();
        this.actionButtonMetaData.actionList = [
            {
                code: TaskBriefingsActionButtons.INVITE_TO_TASK_BRIEFINGS,
                name: `Invite to Brief`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'send',
                enabled: (this.isProjectPortal || this.isCompanyRoute()),
            },
            {
                code: TaskBriefingsActionButtons.DOWNLOAD_REPORT,
                name: `Download Report`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: (this.isProjectPortal || this.isCompanyRoute()),
            },
            {
                code: TaskBriefingsActionButtons.DOWNLOAD_REGISTER,
                name: `Download Register`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: true,
            },
        ];
        if (isPageChange) {
          this.loadingInlineTaskBriefing = true;
        } else {
          this.loadingBriefings = true;
        }
        this.toolboxTalksService.getProjectBriefingToolRecordsList(this.projectId, this.toolKey, params).subscribe((data: any) => {
            this.loadingBriefings = false;
            this.loadingInlineTaskBriefing = false;
            if (data && data.tool_records) {
                this.records = data.tool_records;
                this.page.totalElements = data.totalCount;
                this.temp_records = data.tool_records;
                if(!this.reloadFilterData){
                    this.ownerList = data.taggedOwnersList;
                    this.filterData = this.renderFilterData();
                    this.reloadFilterData = true;
                }
            }else{
                const message = `Failed to fetch task briefings, projectId: ${this.projectId}.`;
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    buildButtonGroup() {
        this.rowButtonGroup = [
            {
                key: 'view',
                label: '',
                title: `View ${this.tbSglrPhrase}`,
                mat_icon: 'search',
            },
            {
                key: 'download',
                label: '',
                title: 'Download',
                mat_icon: 'download',
                children: [
                    {
                        key: 'download_pdf',
                        label: 'Download PDF',
                        title: 'Download PDF',
                    },
                    {
                        key: 'download_xlsx',
                        label: 'Download XLSX',
                        title: 'Download XLSX',
                    },
                ]
            },
        ];
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actionMap = {
            'view': () => this.taskBriefingDetailModal(row),
            'download_pdf': () => this.downloadTaskBriefing(row),
            'download_xlsx': () => this.downloadTaskBriefingXLSX(row.id, this.authUser$.timezone),
            'download_register': () => this.taskBriefingDownloadAttendees(row, this.briefing_row?.briefing_title),
        };
        const action = actionMap[entry.key];
        if (action) {
            action();
        }
    }

    @ViewChild('addTaskBriefingHtml')
    private addTaskBriefingHtmlRef: IModalComponent;
    showAddTaskBriefingPop() {
        this.tagged_owner = null;
        this.briefingFile = {};
        this.briefing_title = '';
        this.showAddTaskBriefingModal = true;
        this.addTaskBriefingHtmlRef.open();
    }

    @ViewChild('briefingInviteModal')
    private briefingInviteModalRef: IModalComponent;
    inviteToTaskBriefingPopup() {
        this.processingTaskBriefing = true;
        this.getAvailableBriefings();
        if(this.isProjectPortal) {
            this.userService.getProjectAdmins(this.projectId).subscribe((data:any) =>{
                if(data && data.admins) {
                    this.setDataInviteToTaskBriefingPopup(data.admins);
                }
            });
        }
        if(this.isCompanyRoute()) {
            let companyAdminsRequest = this.userService.getCompanyAdmins(this.employerId);
            let companyProjectAdminsRequest = this.userService.getCompanyProjectAdmins(this.employerId, this.projectId);
            forkJoin([
                companyProjectAdminsRequest,
                companyAdminsRequest
            ]).subscribe(responseList => {
                let res: any = responseList;
                let error = res.filter(r => !r.success);
                if(error.length == 0) {
                    this.setDataInviteToTaskBriefingPopup([...res[0].admins, ...res[1].admins]);
                }
            });
        }
    }

    getAvailableBriefings() {
        this.toolboxTalksService.getBriefingToolRecordForInvite(this.toolKey, this.projectId).subscribe((data:any) => {
            if (data.success && data.availableBriefingTools) {
                this.availableRecords = data.availableBriefingTools;
                return;
            }
            const message = `Failed to fetch ${this.toolKey}, projectId: ${this.projectId}.`;
            this.toastService.show(this.toastService.types.ERROR, message);
        });
    }

    setDataInviteToTaskBriefingPopup(projectUsers) {
        this.projectUsers = (projectUsers || []).reduce((acc, item) => {
          if (item.inductionStatus !== null) {
            acc.push({ ...item, _isSelected: false });
          }
          return acc;
        }, []);
        this.jobRoles = this.availableJobRoles();
        this.filteredUsers = [...this.projectUsers];
        this.processingTaskBriefing = false;
        this.getAvailableEmployers();
        this.briefingInviteModalRef.open();
    }

    availableJobRoles() {
        return (this.projectUsers || []).reduce((acc, user) => {
            if (!acc.some(role => role.name === user.job_role)) {
                acc.push({ name: user.job_role });
            }
            return acc;
        }, []);
    }

    getAvailableEmployers() {
        let result = this.projectUsers.map(u => {
            return u.employer;
        });
        this.availableEmployers = [...new Set(result)];
    }

    checkHasSelectedUsers(noOfSelectedUsers: number) {
        this.hasSelectedUsers = (noOfSelectedUsers > 0) ? true : false;
    }

    inviteUsers(form) {
        if (!form.valid) {
            return;
        }
        this.processingBriefingInvites = true;
        let taskBriefings = form.value.selectedTools.map(tb=> tb.id);
        let users = form.value.selectedUsers.map(({ _isSelected, ...rest }) => rest);
        let req = {
            taskBriefings: taskBriefings,
            usersToInvite: users
        }
        this.projectFeaturesService.inviteToTaskBriefing(req, this.projectId).subscribe(res => {
            this.processingBriefingInvites = false;
            if(res.success) {
                this.inviteApiSuccess = res.status;
                this.filteredUsers = [ ...this.projectUsers ];
                this.isFilteredUsersReset = true;
                form.reset();
            }
            else {
                this.inviteApiError = "Failed to send Invites";
            }
        })

    }

    closeInviteModal(form) {
        form.reset();
        this.inviteApiError = null;
        this.inviteApiSuccess = null;
        this.filteredUsers = [];
    }

    @ViewChild('taskBriefingDetailsHtml')
    private taskBriefingDetailsHtmlRef: IModalComponent;
    taskBriefingDetailModal(row) {
        this.blockLoader = true;
        this.titleEditDisable = true;
        this.companyEditDisable = true;

        this.toolboxTalksService.getProjectBriefingToolRecord(this.projectId, this.toolKey, row.id, {
            allBriefings: true,
            expand_attendees: true
        }).subscribe((data: any) => {
            this.blockLoader = false;
            if(!data.briefing){
                const message = data.message || 'Failed to fetch record.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
                return false;
            }
            this.showTaskBriefingDetailModal = true;
            let row = data.briefing;
            this.briefing_row = row;
            this.briefing_title = row.briefing_title;
            this.tagged_owner = (row.tagged_owner && row.tagged_owner.id) ? row.tagged_owner.id : row.tagged_owner;
            this.taskBriefingDetailsHtmlRef.open();
        });
    }

    closeTaskBriefingDetails(event?) {
        this.showTaskBriefingDetailModal = false;
        if(event) {
            event.closeFn();
        }
    }

    openTaskBriefingModal(content) {
        return this.openModal(content, 'md');
    }

    openModal(content, size) {
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size
        });
    }

    addTaskBriefingRequest(form, event) {
        if (!form.valid) {
            console.log('Modal form is not valid');
            return;
        }
        let addBriefingReq = form.value;
        addBriefingReq.project_ref = this.projectId;
        addBriefingReq.user_ref = this.authUser$.id;
        addBriefingReq.tagged_owner = this.tagged_owner;
        addBriefingReq.is_available = 1;
        addBriefingReq.convert_doc_to_pdf = this.convert_doc_to_pdf;
        this.addingTaskBriefingInProgress = true;

        this.projectFeaturesService.createTaskBriefing(addBriefingReq, this.projectId).subscribe(out => {
            if(!out.success){
                const message = out.message || 'Failed to store data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
            }
            this.addingTaskBriefingInProgress = false;
            form.reset();
            this.briefingFile = {};
            this.closeAddTaskBriefingModal(event);
            this.initializeTable(true);
        });
    }

    closeAddTaskBriefingModal(event?) {
        this.showAddTaskBriefingModal = false;
        if(event) {
            event.closeFn();
        }
    }

    uploadDone($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.briefingFile = $event.userFile;
            this.task_briefing_file = $event.userFile.file_url;
            let urlParts = (this.task_briefing_file).split(".");
            this.convert_doc_to_pdf = (urlParts.length && ['doc', 'docx'].includes((urlParts[urlParts.length-1]).toLowerCase()));
            this.briefing_file_ref = $event.userFile.id;
        }
    }

    fileDeleteDone($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.briefingFile = undefined;
            this.task_briefing_file = null;
            this.convert_doc_to_pdf = false;
            this.briefing_file_ref = null;
        }
    }

    makeBriefingAvailable($event, row) {
        let makeAvailable = $event.target.checked;
        let req = {
            is_available: makeAvailable,
            user_ref: row.user_ref.id
        };

        this.processingTaskBriefing = true;
        this.projectFeaturesService.updateTaskBriefing(req, this.projectId, row.id).subscribe(out => {
            if(!out.success) {
                const message = 'Failed to update record.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
            this.processingTaskBriefing = false;
        });
    }

    checkAvailability(record) {
        return record.is_available;
    }

    isCompanyRoute() {
        let routeName = this.activatedRoute.snapshot.url[0].path;
        return routeName === 'company-admin';
    }

    downloadTaskBriefingReport() {
        let fromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.from_date);
        let toDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.to_date);
        if(!fromDate || !toDate) {
            const message = 'Please enter from date and to date.';
            this.toastService.show(this.toastService.types.ERROR, message);
            return false;
        }

        if(toDate.isBefore(fromDate)) {
            const message = 'Incorrect {from,to} date.';
            this.toastService.show(this.toastService.types.ERROR, message);
            return false;
        }
        let formattedFromDate = fromDate.format(AppConstant.apiRequestDateFormat);
        let formattedToDate = toDate.format(AppConstant.apiRequestDateFormat);
        let companyId = (this.employerId ? this.employerId : null);
        let request = {
            projectId: this.projectId,
            fromDate: fromDate.startOf('day').valueOf(),
            toDate: toDate.endOf('day').valueOf(),
            companyId: companyId,
            is_inherited_project: this.is_inherited_project,
            download: 'true',
            format: 'xls'
        };
        this.addingTaskBriefingInProgress = true;
        this.projectFeaturesService.downloadTaskBriefingReport(request, this.projectId, `${this.tbPhrase} Report-${this.projectId}-[${fromDate.format(AppConstant.defaultDateFormat)}-${toDate.format(AppConstant.defaultDateFormat)}].xlsx`, () => {
            this.addingTaskBriefingInProgress = false;
            this.blockLoader = false;
        });
    }

    taskBriefingDownloadAttendees(briefed, tbTitle='Task Briefing') {
        let title = `${this.tbSglrPhrase}: ${tbTitle}`;
        let workbook: ExcelProper.Workbook = new Excel.Workbook();
        let worksheet = workbook.addWorksheet(this.tbSglrPhrase, {
            properties: {
                defaultRowHeight: 50
            }
        });

        let rowIndex = 1;
        worksheet.addRow([`${title}`, ' ', ' ']).commit();
        worksheet.getRow(rowIndex).eachCell(cell => {
            cell.font = {bold: true};
            /*cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: {argb: '82878c'}
            };*/
            cell.border = {
                top: {style: 'medium'},
                left: {style: 'thin'},
                bottom: {style: 'thin'},
                right: {style: 'thin'}
            }
            cell.alignment = { vertical: 'middle' };
        });
        worksheet.mergeCells('A1:C1');
        worksheet.getRow(rowIndex).height = 20;

        worksheet.getColumn('A').width = 32;
        worksheet.getColumn('A').style = {alignment: {wrapText: true}};
        worksheet.getColumn('B').width = 35;
        worksheet.getColumn('B').style = {alignment: {wrapText: true}};
        worksheet.getColumn('C').width = 27;
        rowIndex += 1;
        let briefedAt = briefed.briefed_at ? this.dayjs(+briefed.briefed_at).format(AppConstant.defaultDateFormat) : null;
        worksheet.addRow([`Briefed By: ${briefed.briefed_by_name}`, `Date Briefed: ${briefedAt}`]).eachCell(cell => {
            cell.font = {bold: true};
        });
        worksheet.getRow(rowIndex).height = 20;

        rowIndex += 1;
        worksheet.addRow(["Name", "Employer", "Signature"]).eachCell(cell => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: {argb: 'D3D3D3'}
            };
            cell.border = {
                top: {style: 'thin'},
                bottom: {style: 'thin'},
                right: {style: 'thin'}
            }
        });
        worksheet.getRow(rowIndex).height = 20;

        //sort by name
        briefed.allattendees.sort((a, b) => (a.name > b.name) ? 1 : -1);
        let signatureArr = [];
        briefed.allattendees.map(row => {
            rowIndex += 1;
            if (row.sign) {
                signatureArr[rowIndex] = row.sign;
            }
            worksheet.addRow([row.name, row.emp, null]).height = 50;
            return row;
        });

        signatureArr.map((signature, index) => {
            let imageId = workbook.addImage({
                base64: signature,
                extension: 'png',
            });
            worksheet.addImage(imageId, {
                tl: { col: 2.1, row: (index-1)+0.1 },
                ext: { width: 130, height: 30 }
            });
        });

        workbook.xlsx.writeBuffer().then(data => {
            const blob = new Blob([data], { type: this.blobType });
            fs.saveAs(blob, `${ this.tbSglrPhrase } report-${tbTitle}-${briefedAt}'.xlsx`);
        });
    }

    downloadTaskBriefingXLSX(id, timezone) {
        let request = {
            projectId: this.projectId,
            timezone: timezone
        };
        this.processingTaskBriefing = true;
        this.projectFeaturesService.downloadTaskBriefingXLSX(request, id, this.projectId, `${this.tbSglrPhrase} Report.xlsx`, () => {
            this.processingTaskBriefing = false;
        });
    }

    downloadTaskBriefing(row) {
        this.processingTaskBriefing = true;
        let body = {
            createdAt: row.createdAt,
            companyId: this.employerId,
            type: 'pdf'
        };

        this.projectFeaturesService.downloadTaskBriefing(body, this.projectId, row.id, () => {
            this.processingTaskBriefing = false;
        });
    }

    ngAfterViewInit() {
    }

    filterByOwner() {
        let filteredRecords = [];
        this.temp_records.forEach(r=> {
            if(r.tagged_owner &&this.selectedOwner.includes(r.tagged_owner.id)) {
                filteredRecords.push(r);
            }
        });
        this.records = filteredRecords;
    }


    onClearTagOwner() {
        this.records = this.temp_records;
    }

    downloadRegister() {
        this.blockLoader = true;
        this.projectFeaturesService.downloadTaskBriefingRegister(this.projectId, () => {
            this.blockLoader = false;
        });
    }

    enableEdit(type) {
        if (type == 'title') {
            this.titleEditDisable = !(this.titleEditDisable);
        } else if(type == 'company') {
            this.companyEditDisable = !(this.companyEditDisable);
        }
    }

    updateTB(row) {
        if (!this.briefing_title && !this.tagged_owner) return false;
        this.blockLoader = true;

        let request: any = {}
        if (this.briefing_title) {
            request.briefing_title = this.briefing_title;
        }

        if (this.tagged_owner) {
            request.tagged_owner = this.tagged_owner;
        }

        this.projectFeaturesService.updateTaskBriefing(request, this.projectId, row.id).subscribe(this.responseHandler.bind(this));
    }

    responseHandler(out: any) {
        this.modalService.dismissAll();
        this.closeTaskBriefingDetails();
        if(out.success) {
            this.initializeTable(true);
        } else {
            const message = out.message || 'Failed to store data.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: out });
        }
        this.blockLoader = false;
    }

    onFilterSelection(data){
        this.selectedOwner = data.owner.map(a=>a.id);
        this.pageCallback({ offset:0 }, true);
    }
    searchFunction(data){
        this.search = data.search ? data.search.toLowerCase() : '';
        let stopReload = data?.stopReload;
        if(!stopReload){
            this.initializeTable(true);
        }
        this.page.pageNumber = 0;
    }
    renderFilterData(){
        return [
            {
                name:'owner',
                list:this.ownerList,
                enabled:true,
                state:false
            }
        ];
    }

    onSort($event){
        let {sorts} = $event;
        let{ dir, prop } = sorts[0];
        this.sorts = { dir, prop };
        this.pageCallback({ offset: 0 }, true);
    }

    public onActionSelection(actionVal: ActionButtonVal) {
        const code = actionVal.code;
        if(code === TaskBriefingsActionButtons.INVITE_TO_TASK_BRIEFINGS) {
            this.inviteToTaskBriefingPopup();
        } else if(code === TaskBriefingsActionButtons.DOWNLOAD_REPORT) {
            this.openTaskBriefingsReportModal();
        } else if(code === TaskBriefingsActionButtons.DOWNLOAD_REGISTER) {
            this.downloadRegister();
        }
    }

    openTaskBriefingsReportModal() {
        this.reportDownloader.openModal();
    }

    async taskBriefingsReportDownload(event) {
        await this.initiateDownload(event.selection);
        event.closeFn();
    }
}

.dropdown-menu li a {
    padding: 10px 15px;
    font-weight: 300;
    cursor: pointer;
}
.multi-column-dropdown {
    list-style: none;
    margin: 0px;
    padding: 0px;
    text-align: center;
    width: 100%;
}
.multi-column-dropdown li a {
    display: block;
    clear: both;
    line-height: 1.428571429;
    color: var(--black);
}
.multi-column-dropdown li a:hover {
    text-decoration: underline;
    color: var(--orange-yellow);
    background-color: var(--white);
}
#select-incident-type-button.btn-primary:focus {
    box-shadow: none;
}
#incident-type-drop ::placeholder {
    color: var(--philippine-gray);
}
#incident-type-drop {
    z-index: 1045;
    top: -42px !important;
    min-width: 190px;
}
.border-none {
    border-collapse: collapse;
    border: none;
}
.border-none td {
    border: 1px solid var(--platinum);
}
.border-none tr:first-child td {
    border-top: none;
}
.border-none tr:last-child td {
    border-bottom: none;
}
.border-none tr td:first-child {
    border-left: none;
}
.border-none tr td:last-child {
    border-right: none;
}
.text-color-red {
    color: var(--danger);
}
.text-color-yellow {
    color: var(--fuel-yellow);
}
.text-color-green {
    color: var(--success);
}
.ir_cause_opts ::ng-deep .ng-dropdown-panel-items.scroll-host {
    max-height: 160px;
}
.ir_personnel_opts ::ng-deep .ng-dropdown-panel-items.scroll-host {
    max-height: 113px;
}
.font-heading {
    font-size: 18px;
}
/* custome dropdown */
#incident-type-drop1 ::placeholder {
    color: var(--philippine-gray);
}
#incident-type-drop1 {
    z-index: 1045;
    min-width: 370px;
}
.z-index {
    z-index: 1045 !important;
}
#select-incident-type-button1 .btn-primary:focus {
    box-shadow: none;
    max-width: 185px;
}
.outer-width {
    min-width: 150px;
    height: 40px;
}
.inner-width {
    min-width: 300px;
    transform: translate(-1px, 32px) !important;
    border-top: none;
}
/* for remove dropdown icon */
.dropdown-toggle::after {
    display: none;
}
.btn-refine {
    background-color: var(--white) !important;
    border-bottom: 1px solid var(--lavender-gray);
}
.text-color-grey {
    color: var(--philippine-silver) !important;
}
.btn-refine:focus {
    outline: none;
    box-shadow: none;
}
/* custome checkBox */
input[type="checkbox"] {
    position: relative;
    cursor: pointer;
}
input[type="checkbox"]:before {
    content: "";
    display: block;
    position: absolute;
    width: 16px;
    height: 16px;
    top: -1px;
    left: -2px;
    border-radius: 3px;
    background-color: var(--bright-gray);
}
input[type="checkbox"]:checked:after {
    content: "";
    display: block;
    width: 5px;
    height: 10px;
    border: solid var(--black);
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(50deg);
    -ms-transform: rotate(50deg);
    transform: rotate(50deg);
    position: absolute;
    top: 1px;
    left: 4px;
}

.custom-dropdown-menu {
    transform: translate3d(0px, 24px, 0px);
    top: 0px;
    left: 0px;
    will-change: transform;
    z-index: 1050;
    width: calc(31em + 2px);
}
.drop-header {
    font-size: 16px;
    font-weight: 500;
    // border-bottom: solid 1px var(--light-silver);
}

.report-form {
    overflow: auto;
}

.short-form {
    max-height: calc(100vh - 15.5rem) !important;
}

.long-form {
    max-height: calc(100vh - 31.8rem) !important;
}

.action-column button.fa-edit {
    box-shadow: none;
}

<div class="px-5 pt-0 pb-4">
    <form novalidate #incidentDetails="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 0 && incidentReport.incident_type)}">
            <div class="form-group">
                <input type="hidden" name="incident_date" [(ngModel)]="incidentReport._incident_date" required>
            </div>

            <div class="form-group">
                <input type="hidden" name="incident_time" [(ngModel)]="incidentReport._incident_time" required>
            </div>

            <div class="form-group">
                <label>Location of Incident: <small class="required-asterisk">*</small></label>
                <input type="text" class="form-control" [(ngModel)]="incidentReport.location" name="location" required/>
            </div>

            <div class="form-group">
                <label>Lighting Conditions: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="lightingCondition" #lightingCondition="ngModel" [(ngModel)]="incidentReport.lighting_condition"
                           required style="width: 100%;">
                    <ng-option *ngFor="let lightingCondition of incidentTypeMetaData.lighting_conditions" [value]="lightingCondition">{{ lightingCondition }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group">
                <label>Weather Conditions: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="weatherCondition" #weatherCondition="ngModel" [(ngModel)]="incidentReport.weather_conditions"
                           required style="width: 100%;"
                           [multiple]="true">
                    <ng-option *ngFor="let weatherCondition of incidentTypeMetaData.weather_conditions" [value]="weatherCondition">{{ weatherCondition }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group">
                <label>Incident Category: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="incidentCategory" #incidentCategory="ngModel" [(ngModel)]="incidentReport.incident_category"
                           required style="width: 100%;">
                    <ng-option *ngFor="let incidentCategory of incidentTypeMetaData.type_of_health_incident" [value]="incidentCategory">{{ incidentCategory }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group">
                <label>Incident Details: <small class="required-asterisk">*</small></label>
                <textarea class="form-control" style="min-height: 120px;" [(ngModel)]="incidentReport.incident_details" name="incident_details" [maxlength]="textAreaMaxCharLimit"></textarea>
                <span class="float-right small-font mt-1 text-muted">Maximum Characters - {{textAreaMaxCharLimit}}</span>
            </div>

            <div class="form-group">
                <label>Potential Severity: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="pSeverity" #pSeverity="ngModel" [(ngModel)]="incidentReport.potential_severity"
                           required style="width: 100%;">
                    <ng-option *ngFor="let severity of incidentTypeMetaData.severity" [value]="severity">{{ severity }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group mb-0">
                <label>Actual Severity: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="aSeverity" #aSeverity="ngModel" [(ngModel)]="incidentReport.actual_severity"
                           required style="width: 100%;">
                    <ng-option *ngFor="let severity of incidentTypeMetaData.severity" [value]="severity">{{ severity }}</ng-option>
                </ng-select>
            </div>
        </fieldset>
    </form>

    <form novalidate #injuredPersonDetail="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 1)}">
            <div class="form-group">
                <label>Person Type: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="pType" #pType="ngModel" [(ngModel)]="incidentReport.person_affected[0].person_type"
                           required style="width: 100%;">
                    <ng-option *ngFor="let pType of incidentTypeMetaData.person_type" [value]="pType">{{ pType }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group" *ngIf="PersonType.Employee == incidentReport.person_affected[0].person_type">
                <label>Choose User: <small class="required-asterisk">*</small></label>

                <inducted-user-selector
                    [required]="true"
                    placeholder="Select a user"
                    [name]="'iUser'"
                    extraFields="employment,names,gender,contact"
                    [projectId]="projectId"
                    (selectionChanged)="autoFillPerson($event)"
                    [selection]="incidentReport.person_affected[0].user_ref"
                ></inducted-user-selector>
            </div>

            <div class="form-group" *ngIf="'Contractor' == incidentReport.person_affected[0].person_type">
                <label>Job Role: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="jRole" #jRole="ngModel" [(ngModel)]="incidentReport.person_affected[0].job_title"
                           required  style="width: 100%;">
                    <ng-option *ngFor="let jRole of jobRoleList" [value]="jRole.name">{{ jRole.name }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group">
                <label>First Name: <small class="required-asterisk">*</small></label>
                <input type="text" class="form-control" [(ngModel)]="incidentReport.person_affected[0].f_name" name="f_name" required/>
            </div>

            <div class="form-group">
                <label>Last Name: <small class="required-asterisk">*</small></label>
                <input type="text" class="form-control" [(ngModel)]="incidentReport.person_affected[0].l_name" name="l_name" required/>
            </div>

            <div class="form-group">
                <label>Contact Number: <small class="required-asterisk">*</small></label>
                <app-country-code-contact-input [contactData]="incidentReport.person_affected[0]" 
                                    [name]="'contact_number'" [isRequired]="true"
                                    (phoneModelChange)="subscribeToActiveFormStatus()"
                                    [errorMessageTitle]="'Contact No.'"
                                    [defaultCountryCode]="projectInfo?.custom_field?.country_code">
                </app-country-code-contact-input>
            </div>

            <div class="form-group" *ngIf="'Contractor' != incidentReport.person_affected[0].person_type">
                <label>Job Title: <small class="required-asterisk">*</small></label>
                <input type="text" class="form-control" [(ngModel)]="incidentReport.person_affected[0].job_title" name="job_title" required/>
            </div>

            <div class="form-group">
                <label>Gender: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="pGender" #pGender="ngModel" [(ngModel)]="incidentReport.person_affected[0].gender"
                           required style="width: 100%;">
                    <ng-option *ngFor="let pGender of incidentTypeMetaData.gender" [value]="pGender">{{ pGender }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group mb-0">
                <label>Address: <small class="required-asterisk">*</small></label>
                <textarea class="form-control" [(ngModel)]="incidentReport.person_affected[0].address" name="address" required [maxlength]="textAreaMaxCharLimit"></textarea>
                <span class="float-right small-font mt-1 text-muted">Maximum Characters - {{textAreaMaxCharLimit}}</span>
            </div>
        </fieldset>
    </form>

    <form novalidate #injuryTreatment="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 2)}">
            <div class="form-group row mx-0 p-0 mt-3" [ngClass]="{'mb-0': !incidentReport?.is_onsite_treatment}">
                <div class="col-md-8 px-0">Was any on site treatment given?</div>
                <div class="col-md-4 px-0 d-flex justify-content-end">
                    <div class="custom-control custom-radio ml-2">
                        <input type="radio" class="custom-control-input" name="is_onsite_treatment" id="true"
                               required [value]="true" [(ngModel)]="incidentReport.is_onsite_treatment"/>
                        <label class="custom-control-label d-inline-block" for="true">Yes</label>
                    </div>
                    <div class="custom-control custom-radio ml-2">
                        <input type="radio" class="custom-control-input" name="is_onsite_treatment" id="false"
                               required [value]="false" [(ngModel)]="incidentReport.is_onsite_treatment"/>
                        <label class="custom-control-label d-inline-block" for="false">No</label>
                    </div>
                </div>
            </div>

            <div *ngIf="incidentReport.is_onsite_treatment" class="form-group">
                <label>Details of treatment: <small class="required-asterisk">*</small></label>
                <textarea class="form-control" [(ngModel)]="incidentReport.site_treatment.details" name="details" required [maxlength]="textAreaMaxCharLimit"></textarea>
                <span class="float-right small-font mt-1 text-muted">Maximum Characters - {{textAreaMaxCharLimit}}</span>
            </div>

            <div *ngIf="incidentReport.is_onsite_treatment" class="form-group mb-0">
                <label>Treatment administered by: <small class="required-asterisk">*</small></label>
                <input type="text" class="form-control" [(ngModel)]="incidentReport.site_treatment.administered_by" name="administered_by" required/>
            </div>
        </fieldset>
    </form>

    <form novalidate #incidentWitnesses="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 3)}">
            <div class="form-group row mx-0 p-0 mt-3" [ngClass]="{'mb-0': !incidentReport?.any_witnesses}">
                <div class="col-md-8 px-0">Were there any Witnesses or Third Parties? <small class="required-asterisk">*</small></div>
                <div class="col-md-4 px-0 d-flex justify-content-end">
                    <div class="custom-control custom-radio ml-2">
                        <input type="radio" class="custom-control-input" name="any_witnesses" id="anyWitnessesT"
                               required [value]="true" [(ngModel)]="incidentReport.any_witnesses"/>
                        <label class="custom-control-label d-inline-block" for="anyWitnessesT">Yes</label>
                    </div>
                    <div class="custom-control custom-radio ml-2">
                        <input type="radio" class="custom-control-input" name="any_witnesses" id="anyWitnessesF"
                               required [value]="false" [(ngModel)]="incidentReport.any_witnesses" (change)="changeAnyWitnessValue()"/>
                        <label class="custom-control-label d-inline-block" for="anyWitnessesF">No</label>
                    </div>
                </div>
                <input 
                    type="text" 
                    class="d-none" 
                    name="invalid_ruleset" 
                    [required]="incidentReport.any_witnesses" 
                    [ngModel]="incidentReport.any_witnesses && incidentReport.witnesses.length > 0 ? 'valid' : null" 
                />
            </div>

            <ng-container *ngIf="incidentReport.witnesses.length && incidentReport.any_witnesses">
                <ng-template ngFor let-witness [ngForOf]="(incidentReport.witnesses || [])" let-i="index">

                    <div class="row mx-0 mb-2">
                        <div class="col-md-11 px-0">
                            <div class="row mx-0 p-2 border rounded-lg">
                                <div class="col-md-11 p-0 d-inline-block">
                                    <div class="col-md-12 font-weight-bold p-0">{{witness.f_name}} {{witness.l_name}}</div>
                                    <small>({{witness.person_type}})</small>
                                </div>
                                <div class="col-md-1 px-0 d-flex justify-content-center align-items-center">
                                    <i class="fa fa-edit cursor-pointer py-2" aria-hidden="true" (click)="editWitness(i)"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 px-0 d-flex justify-content-center align-items-center">
                            <span class="material-symbols-outlined text-danger cursor-pointer py-2" (click)="removeWitness(i)">
                                delete
                            </span>
                        </div>
                    </div>
                </ng-template>
            </ng-container>

            <!-- Add Witness -->
            <form *ngIf="showAddWitness" novalidate #addWitnessForm="ngForm">
                <div class="form-group">
                    <label>Person Type: <small class="required-asterisk">*</small></label>
                    <ng-select class="dropdown-list" appendTo="body" name="pType" #pType="ngModel" [(ngModel)]="witness_detail.person_type"
                               required style="width: 100%;">
                        <ng-option *ngFor="let pType of incidentTypeMetaData.person_type" [value]="pType">{{ pType }}</ng-option>
                    </ng-select>
                </div>

                <div class="form-group">
                    <label>First Name: <small class="required-asterisk">*</small></label>
                    <input type="text" class="form-control" [(ngModel)]="witness_detail.f_name" name="f_name" required/>
                </div>

                <div class="form-group">
                    <label>Last Name: <small class="required-asterisk">*</small></label>
                    <input type="text" class="form-control" [(ngModel)]="witness_detail.l_name" name="l_name" required/>
                </div>

                <div class="form-group">
                    <label>Contact Number: <small class="required-asterisk">*</small></label>
                    <app-country-code-contact-input [contactData]="witness_detail" 
                                        [name]="'contact_number'" [isRequired]="true"
                                        (phoneModelChange)="subscribeToActiveFormStatus()"
                                        [errorMessageTitle]="'Contact No.'"
                                        [defaultCountryCode]="projectInfo?.custom_field?.country_code">
                    </app-country-code-contact-input>
                </div>

                <div class="form-group">
                    <label>Comments/Statement: <small class="required-asterisk">*</small></label>
                    <textarea class="form-control" [(ngModel)]="witness_detail.comments" name="comments" required [maxlength]="textAreaMaxCharLimit"></textarea>
                    <span class="float-right small-font mt-1 text-muted">Maximum Characters - {{textAreaMaxCharLimit}}</span>
                </div>

                <div class="form-group d-inline-block col-md-12 p-0 mb-0">
                    <button type="button" class="btn btn-outline-brandeis-blue float-left" (click)="addWitnessFormToggle(false)">Cancel</button>
                    <button type="button" class="btn btn-brandeis-blue btn-pill float-right" [disabled]="!addWitnessForm.valid || numberValidationService.checkNumberValidation(witness_detail?.contact_number?.number)" (click)="addWitness()">Add</button>
                </div>
            </form>
            <button *ngIf="incidentReport.any_witnesses && !showAddWitness" class="col-md-12 btn btn-brandeis-blue btn-pill" (click)="addWitnessFormToggle(true)">Add Witness</button>
        </fieldset>
    </form>

    <form novalidate #incidentAttachment="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 4)}">

            <file-uploader-v2
                class="mt-1 pt-1 mb-1"
                [init]="attachments"
                [multipleUpload]="true"
                [category]="'attachment-uploader'"
                (uploadDone)="attachmentUploadDone($event)"
                [dragnDropTxt]="'Drag and drop image or pdf here'"
                [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']"
                #attachmentUploader
                [showDeleteBtn]="false"
                [showFileName]="false"
                [disabled]="false"
            >
            </file-uploader-v2>

            <ng-container *ngIf="attachments.length">
                <incident-file-link-with-desc
                    [attachments]="attachments"
                    [incidentReport]="incidentReport"
                    [selectedAttachIndex]="selectedAttachIndex"
                    (onRemoveAttachment)="removeAttachment($event)"
                    (onToggleDescription)="modifyDescription($event)">
                </incident-file-link-with-desc>
            </ng-container>
        </fieldset>
    </form>

    <form novalidate #incidentActions="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 5)}">

            <div class="form-group mb-0">
                <label>Initial Action Taken</label>
                <textarea class="form-control" style="min-height: 120px;" [(ngModel)]="incidentReport.action_details" name="action_details" [maxlength]="textAreaMaxCharLimit"></textarea>
                <span class="float-right small-font mt-1 text-muted">Maximum Characters - {{textAreaMaxCharLimit}}</span>
            </div>
        </fieldset>
    </form>
    <form novalidate #incidentReview="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 6)}">
            <ng-template ngFor let-step [ngForOf]="(meta_incident_type_steps || [])" let-i="index">
                <div class="col-md-12 py-2 border rounded-lg mt-3" *ngIf="step != 'Review'">
                    <div class="col-md-12 p-0 mb-2">
                        <span class="col-md-8 p-0 fw-500">{{step}}</span>
                        <small class="float-right font-weight-bold cursor-pointer" (click)="editForm(i)"><i class="fa fa-edit"></i> Edit</small>
                    </div>

                    <div class="col-md-12 small">
                        <!--Incident Details-->
                        <dl class="dl-horizontal" *ngIf="i == 0">
                            <dt>Date & Time of Incident: </dt>
                            <dd>{{dateFormat(this.incidentReport.incident_date).format('DD/MM/YYYY HH:mm')}}</dd>

                            <dt>Location of Incident: </dt>
                            <dd>{{incidentReport.location}}</dd>

                            <dt>Lighting Conditions: </dt>
                            <dd>{{incidentReport.location}}</dd>

                            <dt>Weather Conditions: </dt>
                            <dd>{{incidentReport.weather_conditions}}</dd>

                            <dt>Incident Category: </dt>
                            <dd>{{incidentReport.incident_category}}</dd>

                            <dt>Incident Details: </dt>
                            <dd class="text-break">{{incidentReport.incident_details}}</dd>

                            <dt>Potential Severity: </dt>
                            <dd>{{incidentReport.potential_severity}}</dd>

                            <dt>Actual Severity: </dt>
                            <dd>{{incidentReport.actual_severity}}</dd>
                        </dl>

                        <!--Injured Person Details-->
                        <dl class="dl-horizontal" *ngIf="i == 1">
                            <dt>Person Type: </dt>
                            <dd>{{incidentReport.person_affected[0].person_type}}</dd>

                            <dt>First Name: </dt>
                            <dd>{{incidentReport.person_affected[0].f_name}}</dd>

                            <dt>Last Name: </dt>
                            <dd>{{incidentReport.person_affected[0].l_name}}</dd>

                            <dt>Contact Number: </dt>
                            <dd>{{ incidentReport.person_affected[0]?.contact_number?.code ? '(+' + incidentReport.person_affected[0]?.contact_number?.code + ')' : '' }} {{ incidentReport.person_affected[0]?.contact_number?.number }}</dd>

                            <dt>Job Title: </dt>
                            <dd>{{incidentReport.person_affected[0].job_title}}</dd>

                            <dt>Gender: </dt>
                            <dd>{{incidentReport.person_affected[0].gender}}</dd>

                            <dt>Address: </dt>
                            <dd class="text-break">{{incidentReport.person_affected[0].address}}</dd>
                        </dl>

                        <!--Injury & Treatment-->
                        <dl class="dl-horizontal" *ngIf="i == 2">
                            <dt>Was any on site treatment given? </dt>
                            <dd>{{(incidentReport.is_onsite_treatment) ? "Yes" : "No" }}</dd>

                            <ng-container *ngIf="incidentReport.is_onsite_treatment">
                                <dt>Details of treatment:</dt>
                                <dd class="text-break">{{incidentReport.site_treatment.details}}</dd>

                                <dt>Treatment administered by:</dt>
                                <dd>{{incidentReport.site_treatment.administered_by}}</dd>
                            </ng-container>
                        </dl>


                        <!--Witnesses & Third Parties-->
                        <dl class="dl-horizontal" *ngIf="i == 3">
                            <dt>Were there any Witnesses or Third Parties?</dt>
                            <dd>{{(incidentReport.any_witnesses) ? "Yes" : "No" }}</dd>

                            <ng-container *ngIf="incidentReport.any_witnesses && incidentReport.witnesses.length">
                                <dt>Witnesses Detail: </dt>
                                <ul *ngFor="let witness of incidentReport.witnesses; let i = index;" class="list-unstyled">
                                    <li>{{i+1}}. {{witness.f_name}} {{witness.l_name}} ({{witness.person_type}})</li>
                                </ul>
                            </ng-container>
                        </dl>

                        <!--Attachments-->
                        <dl class="dl-horizontal" *ngIf="i == 4">
                            <dd *ngIf="attachments.length else noAttachment">
                                <ul *ngFor="let attachment of attachments; let i = index;" class="list-unstyled">
                                    <li>
                                        <a class="d-block w-100 text-info text-underline" target="_blank" [href]="attachment.file_url">{{attachment.name}}</a>
                                        <span class="text-break">{{incidentReport.attachment_file_ids[i].description}}</span>
                                    </li>
                                </ul>
                            </dd>
                            <ng-template  #noAttachment>
                                <dd class="text-center">None Added</dd>
                            </ng-template>
                        </dl>

                        <!--Actions-->
                        <dl class="dl-horizontal" *ngIf="i == 5">
                            <ng-container  *ngIf="incidentReport.action_details else noAction">
                                <dt>Initial Action Taken:</dt>
                                <dd class="text-break">{{incidentReport.action_details}}</dd>
                            </ng-container>
                            <ng-template  #noAction>
                                <dd class="text-center">None Added</dd>
                            </ng-template>
                        </dl>
                    </div>
                </div>
            </ng-template>
        </fieldset>
    </form>
</div>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

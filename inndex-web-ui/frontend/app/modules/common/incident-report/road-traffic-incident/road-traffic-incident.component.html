<div class="px-5 pt-0 pb-4">
    <form novalidate #incidentDetails="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 0 && incidentReport.incident_type)}">
            <div class="form-group">
                <input type="hidden" name="incident_date" [(ngModel)]="incidentReport._incident_date" required>
            </div>

            <div class="form-group">
                <input type="hidden" name="incident_time" [(ngModel)]="incidentReport._incident_time" required>
            </div>

            <div class="form-group">
                <label>Lighting Conditions: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="lightingCondition" #lightingCondition="ngModel" [(ngModel)]="incidentReport.lighting_condition"
                           required style="width: 100%;">
                    <ng-option *ngFor="let lightingCondition of incidentTypeMetaData.lighting_conditions" [value]="lightingCondition">{{ lightingCondition }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group">
                <label>Weather Conditions: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="weatherCondition" #weatherCondition="ngModel" [(ngModel)]="incidentReport.weather_conditions"
                           required style="width: 100%;"
                           [multiple]="true">
                    <ng-option *ngFor="let weatherCondition of incidentTypeMetaData.weather_conditions" [value]="weatherCondition">{{ weatherCondition }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group">
                <label>Incident Details: <small class="required-asterisk">*</small></label>
                <textarea class="form-control" style="min-height: 120px;" [(ngModel)]="incidentReport.incident_details" name="incident_details" required [maxlength]="textAreaMaxCharLimit"></textarea>
                <span class="float-right small-font mt-1 text-muted">Maximum Characters - {{textAreaMaxCharLimit}}</span>
            </div>

            <div class="form-group">
                <label>Potential Severity: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="pSeverity" #pSeverity="ngModel" [(ngModel)]="incidentReport.potential_severity"
                           required style="width: 100%;">
                    <ng-option *ngFor="let severity of incidentTypeMetaData.severity" [value]="severity">{{ severity }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group mb-0">
                <label>Actual Severity: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="aSeverity" #aSeverity="ngModel" [(ngModel)]="incidentReport.actual_severity"
                           required style="width: 100%;">
                    <ng-option *ngFor="let severity of incidentTypeMetaData.severity" [value]="severity">{{ severity }}</ng-option>
                </ng-select>
            </div>
        </fieldset>
    </form>

    <form novalidate #locationEnvironmentDetail="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 1)}">
            <div class="form-group">
                <label>Location Type: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="lType" #lType="ngModel" (change)="onLocationTypeChange(incidentReport.loc_env_details.location_type)" [(ngModel)]="incidentReport.loc_env_details.location_type"
                           required style="width: 100%;">
                    <ng-option *ngFor="let lType of incidentTypeMetaData.location_type" [value]="lType">{{ lType }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group" *ngIf="incidentReport.loc_env_details.location_type == 'Road'">
                <label>Type of Road: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="rType" #rType="ngModel" [(ngModel)]="incidentReport.loc_env_details.road_type"
                           required style="width: 100%;">
                    <ng-option *ngFor="let rType of incidentTypeMetaData.road_type" [value]="rType">{{ rType }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group" *ngIf="incidentReport.loc_env_details.location_type == 'Road'">
                <label>Road Speed Limit (MPH): <small class="required-asterisk">*</small></label>
                <input type="text" class="form-control" [(ngModel)]="incidentReport.loc_env_details.speed_limit" name="speed_limit" required/>
            </div>

            <div class="form-group" *ngIf="incidentReport.loc_env_details.location_type == 'Road'">
                <label>Full Width of Road (approx.): <small class="required-asterisk">*</small></label>
                <input type="text" class="form-control" [(ngModel)]="incidentReport.loc_env_details.road_width" name="road_width" required/>
            </div>

            <div class="form-group">
                <label>Location: <small class="required-asterisk">*</small></label>
                <input type="text" class="form-control" [(ngModel)]="incidentReport.loc_env_details.location" name="le_location" required/>
            </div>

            <div class="form-group">
                <label>State of Surface: <small class="required-asterisk">*</small></label>
                <input type="text" class="form-control" [(ngModel)]="incidentReport.loc_env_details.surface_state" name="surface_state" required/>
            </div>

            <div class="form-group mb-0">
                <label>Visibility: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="visibility" #visibility="ngModel" [(ngModel)]="incidentReport.loc_env_details.visibility"
                           required style="width: 100%;">
                    <ng-option *ngFor="let visibility of incidentTypeMetaData.weather_visibility" [value]="visibility">{{ visibility }}</ng-option>
                </ng-select>
            </div>
        </fieldset>
    </form>

    <form novalidate #vehicleDetail="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 2)}">
            <div class="form-group">
                <label><span i18n="@@vrn">Vehicle Registration Number</span>: <small class="required-asterisk">*</small></label>
                <input type="text" class="form-control" [(ngModel)]="incidentReport.vehicle_details.reg_number" name="reg_number" required/>
            </div>

            <div class="form-group">
                <label>Vehicle Type: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="vType" #vType="ngModel" [(ngModel)]="incidentReport.vehicle_details.type"
                           required style="width: 100%;">
                    <ng-option *ngFor="let vType of incidentTypeMetaData.vehicle_type" [value]="vType">{{ vType }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group">
                <label>Vehicle Status: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="vStatus" #vStatus="ngModel" [(ngModel)]="incidentReport.vehicle_details.status"
                           required style="width: 100%;">
                    <ng-option *ngFor="let vStatus of incidentTypeMetaData.vehicle_status" [value]="vStatus">{{ vStatus }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group row mx-0 p-0 mt-3">
                <div class="col-md-8 px-0">Was The Vehicle Stationary? <small class="required-asterisk">*</small></div>
                <div class="col-md-4 px-0 d-flex justify-content-end">
                    <div class="custom-control custom-radio ml-2">
                        <input type="radio" class="custom-control-input" name="was_stationary" id="wasStationaryT"
                               required [value]="true" [(ngModel)]="incidentReport.vehicle_details.was_stationary" (change)="onChangeVehicleStationaryOrNot(incidentReport.vehicle_details.was_stationary)"/>
                        <label class="custom-control-label d-inline-block" for="wasStationaryT">Yes</label>
                    </div>
                    <div class="custom-control custom-radio ml-2">
                        <input type="radio" class="custom-control-input" name="was_stationary" id="wasStationaryF"
                               required [value]="false" [(ngModel)]="incidentReport.vehicle_details.was_stationary"/>
                        <label class="custom-control-label d-inline-block" for="wasStationaryF">No</label>
                    </div>
                </div>
            </div>

            <div class="form-group" *ngIf="!incidentReport.vehicle_details.was_stationary">
                <label>Speed of Vehicle: <small class="required-asterisk">*</small></label>
                <input type="text" class="form-control" [(ngModel)]="incidentReport.vehicle_details.speed" name="speed" required/>
            </div>

            <div class="form-group row mx-0 p-0 mt-3">
                <div class="col-md-8 px-0">Was The Vehicle Unattanded? <small class="required-asterisk">*</small></div>
                <div class="col-md-4 px-0 d-flex justify-content-end">
                    <div class="custom-control custom-radio ml-2">
                        <input type="radio" class="custom-control-input" name="was_unattanded" id="wasUnattandedT"
                               required [value]="true" [(ngModel)]="incidentReport.vehicle_details.was_unattanded" (change)="onChangeVehicleAttendOrNot(incidentReport.vehicle_details.was_unattanded)"/>
                        <label class="custom-control-label d-inline-block" for="wasUnattandedT">Yes</label>
                    </div>
                    <div class="custom-control custom-radio ml-2" style="line-height: 1.75;">
                        <input type="radio" class="custom-control-input" name="was_unattanded" id="wasUnattandedF"
                               required [value]="false" [(ngModel)]="incidentReport.vehicle_details.was_unattanded" (change)="onChangeVehicleAttendOrNot(incidentReport.vehicle_details.was_unattanded)"/>
                        <label class="custom-control-label d-inline-block" for="wasUnattandedF">No</label>
                    </div>
                </div>
            </div>

            <div class="form-group" *ngIf="incidentReport.vehicle_details.was_unattanded === false">
                <label>Number of Passengers: <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" appendTo="body" name="pCount" #pCount="ngModel" [(ngModel)]="incidentReport.vehicle_details.passengers_count"
                           required style="width: 100%;">
                    <ng-option *ngFor="let number of createRange(20)" [value]="number">{{ number }}</ng-option>
                </ng-select>
            </div>

            <div class="form-group mb-0">
                <label>Full Details of Damage: <small class="required-asterisk">*</small></label>
                <textarea class="form-control" [(ngModel)]="incidentReport.vehicle_details.damage_details" name="damage_details" required [maxlength]="textAreaMaxCharLimit"></textarea>
                <span class="float-right small-font mt-1 text-muted">Maximum Characters - {{textAreaMaxCharLimit}}</span>
            </div>
        </fieldset>
    </form>

    <form novalidate #driverDetail="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 3)}">
            <div class="form-group">
                <label>First Name: <small class="required-asterisk">*</small></label>
                <input type="text" class="form-control" [(ngModel)]="incidentReport.driver_details.f_name" name="d_f_name" required/>
            </div>

            <div class="form-group">
                <label>Last Name: <small class="required-asterisk">*</small></label>
                <input type="text" class="form-control" [(ngModel)]="incidentReport.driver_details.l_name" name="d_l_name" required/>
            </div>

            <div class="form-group">
                <label>Contact Number: <small class="required-asterisk">*</small></label>
                <app-country-code-contact-input [contactData]="incidentReport.driver_details" 
                                    [name]="'contact_number'" [isRequired]="true"
                                    (phoneModelChange)="subscribeToActiveFormStatus()"
                                    [errorMessageTitle]="'Contact No.'"
                                    [defaultCountryCode]="projectInfo?.custom_field?.country_code">
                </app-country-code-contact-input>
            </div>

            <div class="form-group">
                <label>Job Title: <small class="required-asterisk">*</small></label>
                <input type="text" class="form-control" [(ngModel)]="incidentReport.driver_details.job_title" name="d_job_title" required/>
            </div>

            <div class="form-group mb-0">
                <label>Address: <small class="required-asterisk">*</small></label>
                <textarea class="form-control" [(ngModel)]="incidentReport.driver_details.address" name="d_address" required [maxlength]="textAreaMaxCharLimit"></textarea>
                <span class="float-right small-font mt-1 text-muted">Maximum Characters - {{textAreaMaxCharLimit}}</span>
            </div>
        </fieldset>
    </form>

    <form novalidate #thirdPartyInvolved="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 4)}">
            <div class="form-group row mx-0 p-0 mt-3" [ngClass]="{'mb-0': !incidentReport?.is_thirdparty_vehicle}">
                <div class="col-md-8 px-0">Was There a Third Party Involved?</div>
                <div class="col-md-4 px-0 d-flex justify-content-end">
                    <div class="custom-control custom-radio ml-2">
                        <input type="radio" class="custom-control-input" name="is_thirdparty_vehicle" id="thirdPartyT"
                               required [value]="true" [(ngModel)]="incidentReport.is_thirdparty_vehicle"/>
                        <label class="custom-control-label d-inline-block" for="thirdPartyT">Yes</label>
                    </div>
                    <div class="custom-control custom-radio ml-2">
                        <input type="radio" class="custom-control-input" name="is_thirdparty_vehicle" id="thirdPartyF"
                               required [value]="false" [(ngModel)]="incidentReport.is_thirdparty_vehicle" (change)="changeThirdPartyValue()"/>
                        <label class="custom-control-label d-inline-block" for="thirdPartyF">No</label>
                    </div>
                </div>
                <input 
                    type="text" 
                    class="d-none" 
                    name="invalid_ruleset" 
                    [required]="incidentReport.is_thirdparty_vehicle" 
                    [ngModel]="incidentReport.is_thirdparty_vehicle && incidentReport.thirdparty_vehicle.length > 0 ? 'valid' : null" 
                />
            </div>

            <ng-container *ngIf="incidentReport.thirdparty_vehicle.length && incidentReport.is_thirdparty_vehicle">
                <ng-template ngFor let-thirdParty [ngForOf]="(incidentReport.thirdparty_vehicle || [])" let-i="index">

                    <div class="row mx-0 mb-2">
                        <div class="col-md-11 px-0">
                            <div class="row mx-0 p-2 border rounded-lg">
                                <div class="col-md-11 p-0 d-inline-block">
                                    <div class="col-md-12 font-weight-bold p-0">{{thirdParty.name}}</div>
                                    <small>({{thirdParty.category}})</small>
                                </div>
                                <div class="col-md-1 px-0 d-flex justify-content-center align-items-center">
                                    <i class="fa fa-edit cursor-pointer py-2" aria-hidden="true" (click)="editThirdParty(i)"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 px-0 d-flex justify-content-center align-items-center">
                            <span class="material-symbols-outlined text-danger cursor-pointer py-2" (click)="removeThirdParty(i)">
                                delete
                            </span>
                        </div>
                    </div>
                </ng-template>
            </ng-container>

            <!-- Add Third Party -->
            <form *ngIf="showAddThirdParty" novalidate #addThirdPartyForm="ngForm">
                <div class="form-group">
                    <label>Third Party Category: <small class="required-asterisk">*</small></label>
                    <ng-select class="dropdown-list" appendTo="body" name="tCategory" #tCategory="ngModel" [(ngModel)]="third_party.category"
                               required style="width: 100%;" (change)="onThirdPartycategoryChange($event)">
                        <ng-option *ngFor="let tCategory of incidentTypeMetaData.third_party_category" [value]="tCategory">{{ tCategory }}</ng-option>
                    </ng-select>
                </div>

                <div class="form-group">
                    <label>Third Party Name: <small class="required-asterisk">*</small></label>
                    <input type="text" class="form-control" [(ngModel)]="third_party.name" name="t_name" required/>
                </div>

                <div class="form-group">
                    <label>Third Party Address: <small class="required-asterisk">*</small></label>
                    <textarea class="form-control" [(ngModel)]="third_party.address" name="t_address" required [maxlength]="textAreaMaxCharLimit"></textarea>
                    <span class="float-right small-font mt-1 text-muted">Maximum Characters - {{textAreaMaxCharLimit}}</span>
                </div>

                <div class="form-group">
                    <label>Third Party Contact Number: <small class="required-asterisk">*</small></label>
                    <app-country-code-contact-input [contactData]="third_party" 
                                        [name]="'contact_number'" [isRequired]="true"
                                        (phoneModelChange)="subscribeToActiveFormStatus()"
                                        [errorMessageTitle]="'Contact No.'"
                                        [defaultCountryCode]="projectInfo.custom_field.country_code">
                    </app-country-code-contact-input>
                </div>

                <div class="form-group">
                    <label>Third Party Insurer: <small class="required-asterisk">*</small></label>
                    <input type="text" class="form-control" [(ngModel)]="third_party.insurer" name="t_insurer" required/>
                </div>


                <div  *ngIf="third_party.category == 'Person'" class="form-group row mx-0 p-0 mt-3">
                    <div class="col-md-8 px-0">Damage to the Person? <small class="required-asterisk">*</small></div>
                    <div class="col-md-4 px-0 d-flex justify-content-end">
                        <div class="custom-control custom-radio ml-2">
                            <input type="radio" class="custom-control-input" name="injury" id="injuryT"
                                   required [value]="true" [(ngModel)]="third_party.injury"/>
                            <label class="custom-control-label d-inline-block" for="injuryT">Yes</label>
                        </div>
                        <div class="custom-control custom-radio ml-2">
                            <input type="radio" class="custom-control-input" name="person_damage" id="injuryF"
                                   required [value]="false" [(ngModel)]="third_party.injury"/>
                            <label class="custom-control-label d-inline-block" for="injuryF">No</label>
                        </div>
                    </div>
                </div>


                <div  *ngIf="third_party.category == 'Property'" class="form-group row mx-0 p-0 mt-3">
                    <div class="col-md-8 px-0">Damage to the Property? <small class="required-asterisk">*</small></div>
                    <div class="col-md-4 px-0 d-flex justify-content-end">
                        <div class="custom-control custom-radio ml-2">
                            <input type="radio" class="custom-control-input" name="property_damage" id="propertyDamageT"
                                   required [value]="true" [(ngModel)]="third_party.property_damage"/>
                            <label class="custom-control-label d-inline-block" for="propertyDamageT">Yes</label>
                        </div>
                        <div class="custom-control custom-radio ml-2">
                            <input type="radio" class="custom-control-input" name="property_damage" id="propertyDamageF"
                                   required [value]="false" [(ngModel)]="third_party.property_damage"/>
                            <label class="custom-control-label d-inline-block" for="propertyDamageF">No</label>
                        </div>
                    </div>
                </div>

                <div  *ngIf="third_party.category == 'Vehicle'" class="form-group row mx-0 p-0 mt-3">
                    <div class="col-md-8 px-0">Damage to the Vehicle? <small class="required-asterisk">*</small></div>
                    <div class="col-md-4 px-0 d-flex justify-content-end">
                        <div class="custom-control custom-radio ml-2">
                            <input type="radio" class="custom-control-input" name="vehicle_damage" id="vehicleDamageT"
                                   required [value]="true" [(ngModel)]="third_party.vehicle_damage"/>
                            <label class="custom-control-label d-inline-block" for="vehicleDamageT">Yes</label>
                        </div>
                        <div class="custom-control custom-radio ml-2">
                            <input type="radio" class="custom-control-input" name="vehicle_damage" id="vehicleDamageF"
                                   required [value]="false" [(ngModel)]="third_party.vehicle_damage"/>
                            <label class="custom-control-label d-inline-block" for="vehicleDamageF">No</label>
                        </div>
                    </div>
                </div>

                <div class="form-group" *ngIf="third_party.category && (third_party.injury || third_party.property_damage || third_party.vehicle_damage)">
                    <label>Details of Damage: <small class="required-asterisk">*</small></label>
                    <textarea class="form-control" [(ngModel)]="third_party.details" name="t_details" required [maxlength]="textAreaMaxCharLimit"></textarea>
                    <span class="float-right small-font mt-1 text-muted">Maximum Characters - {{textAreaMaxCharLimit}}</span>
                </div>

                <div class="form-group d-inline-block col-md-12 p-0 mb-0">
                    <button type="button" class="btn btn-outline-brandeis-blue btn-pill float-left" (click)="addThirdPartyFormToggle(false)">Cancel</button>
                    <button type="button" class="btn btn-brandeis-blue btn-pill float-right" [disabled]="!addThirdPartyForm.valid || numberValidationService.checkNumberValidation(third_party?.contact_number?.number)" (click)="addThirdParty()">Add</button>
                </div>
            </form>
            <button *ngIf="incidentReport.is_thirdparty_vehicle && !showAddThirdParty" class="col-md-12 btn btn-brandeis-blue btn-pill" (click)="addThirdPartyFormToggle(true)">Add</button>
        </fieldset>
    </form>

    <form novalidate #incidentWitnesses="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 5)}">
            <div class="form-group row mx-0 p-0 mt-3" [ngClass]="{'mb-0': !incidentReport?.any_witnesses}">
                <div class="col-md-8 px-0">Were there any Witnesses?</div>
                <div class="col-md-4 px-0 d-flex justify-content-end">
                    <div class="custom-control custom-radio ml-2">
                        <input type="radio" class="custom-control-input" name="any_witnesses" id="anyWitnessesT"
                               required [value]="true" [(ngModel)]="incidentReport.any_witnesses"/>
                        <label class="custom-control-label d-inline-block" for="anyWitnessesT">Yes</label>
                    </div>
                    <div class="custom-control custom-radio ml-2">
                        <input type="radio" class="custom-control-input" name="any_witnesses" id="anyWitnessesF"
                               required [value]="false" [(ngModel)]="incidentReport.any_witnesses" (change)="changeAnyWitnessValue()"/>
                        <label class="custom-control-label d-inline-block" for="anyWitnessesF">No</label>
                    </div>
                </div>
                <input 
                    type="text" 
                    class="d-none" 
                    name="invalid_ruleset" 
                    [required]="incidentReport.any_witnesses" 
                    [ngModel]="incidentReport.any_witnesses && incidentReport.witnesses.length > 0 ? 'valid' : null" 
                />
            </div>

            <ng-container *ngIf="incidentReport.witnesses.length && incidentReport.any_witnesses">
                <ng-template ngFor let-witness [ngForOf]="(incidentReport.witnesses || [])" let-i="index">

                    <div class="row mx-0 mb-2">
                        <div class="col-md-11 px-0">
                            <div class="row mx-0 p-2 border rounded-lg">
                                <div class="col-md-11 p-0 d-inline-block">
                                    <div class="col-md-12 font-weight-bold p-0">{{witness.f_name}} {{witness.l_name}}</div>
                                    <small>({{witness.person_type}})</small>
                                </div>
                                <div class="col-md-1 px-0 d-flex justify-content-center align-items-center">
                                    <i class="fa fa-edit cursor-pointer py-2" aria-hidden="true" (click)="editWitness(i)"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 px-0 d-flex justify-content-center align-items-center">
                            <span class="material-symbols-outlined text-danger cursor-pointer py-2" (click)="removeWitness(i)">
                                delete
                            </span>
                        </div>
                    </div>
                </ng-template>
            </ng-container>

            <!-- Add Witness -->
            <form *ngIf="showAddWitness" novalidate #addWitnessForm="ngForm">
                <div class="form-group">
                    <label>Person Type: <small class="required-asterisk">*</small></label>
                    <ng-select class="dropdown-list" appendTo="body" name="pType" #pType="ngModel" [(ngModel)]="witness_detail.person_type"
                               required style="width: 100%;">
                        <ng-option *ngFor="let pType of incidentTypeMetaData.person_type" [value]="pType">{{ pType }}</ng-option>
                    </ng-select>
                </div>

                <div class="form-group">
                    <label>First Name: <small class="required-asterisk">*</small></label>
                    <input type="text" class="form-control" [(ngModel)]="witness_detail.f_name" name="f_name" required/>
                </div>

                <div class="form-group">
                    <label>Last Name: <small class="required-asterisk">*</small></label>
                    <input type="text" class="form-control" [(ngModel)]="witness_detail.l_name" name="l_name" required/>
                </div>

                <div class="form-group">
                    <label>Contact Number: <small class="required-asterisk">*</small></label>
                    <app-country-code-contact-input [contactData]="witness_detail" 
                                        [name]="'contact_number'" [isRequired]="true"
                                        (phoneModelChange)="subscribeToActiveFormStatus()"
                                        [errorMessageTitle]="'Contact No.'"
                                        [defaultCountryCode]="projectInfo?.custom_field.country_code">
                    </app-country-code-contact-input>
                </div>

                <div class="form-group">
                    <label>Comments/Statement: <small class="required-asterisk">*</small></label>
                    <textarea class="form-control" [(ngModel)]="witness_detail.comments" name="comments" required [maxlength]="textAreaMaxCharLimit"></textarea>
                    <span class="float-right small-font mt-1 text-muted">Maximum Characters - {{textAreaMaxCharLimit}}</span>
                </div>

                <div class="form-group d-inline-block col-md-12 p-0 mb-0">
                    <button type="button" class="btn btn-outline-brandeis-blue float-left" (click)="addWitnessFormToggle(false)">Cancel</button>
                    <button type="button" class="btn btn-brandeis-blue btn-pill float-right" [disabled]="!addWitnessForm.valid || numberValidationService.checkNumberValidation(witness_detail?.contact_number?.number)" (click)="addWitness()">Add</button>
                </div>
            </form>
            <button *ngIf="incidentReport.any_witnesses && !showAddWitness" class="col-md-12 btn btn-brandeis-blue btn-pill" (click)="addWitnessFormToggle(true)">Add Witness</button>
        </fieldset>
    </form>

    <form novalidate #incidentAttachment="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 6)}">
            <file-uploader-v2
                class="mt-1 pt-1 mb-1"
                [init]="attachments"
                [multipleUpload]="true"
                [category]="'attachment-uploader'"
                [dragnDropTxt]="'Drag and drop image or pdf here'"
                (uploadDone)="attachmentUploadDone($event)"
                [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']"
                #attachmentUploader
                [showDeleteBtn]="false"
                [showFileName]="false"
                [disabled]="false"
            >
            </file-uploader-v2>

            <ng-container *ngIf="attachments.length">
                <incident-file-link-with-desc
                    [attachments]="attachments"
                    [incidentReport]="incidentReport"
                    [selectedAttachIndex]="selectedAttachIndex"
                    (onRemoveAttachment)="removeAttachment($event)"
                    (onToggleDescription)="modifyDescription($event)">
                </incident-file-link-with-desc>
            </ng-container>
        </fieldset>
    </form>

    <form novalidate #incidentActions="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 7)}">
            <div class="form-group mb-0">
                <label>Initial Action Taken</label>
                <textarea class="form-control" style="min-height: 120px;" [(ngModel)]="incidentReport.action_details" name="action_details" [maxlength]="textAreaMaxCharLimit"></textarea>
                <span class="float-right small-font mt-1 text-muted">Maximum Characters - {{textAreaMaxCharLimit}}</span>
            </div>
        </fieldset>
    </form>

    <form novalidate #incidentReview="ngForm">
        <fieldset [ngClass]="{'d-none': !(activeStep == 8)}">
            <ng-template ngFor let-step [ngForOf]="(meta_incident_type_steps || [])" let-i="index">
                <div class="col-md-12 py-2 border rounded-lg mt-3" *ngIf="step != 'Review'">
                    <div class="col-md-12 p-0 mb-2">
                        <span class="col-md-8 p-0 fw-500">{{step}}</span>
                        <small class="float-right font-weight-bold cursor-pointer" (click)="editForm(i)"><i class="fa fa-edit"></i> Edit</small>
                    </div>

                    <div class="col-md-12 small">
                        <!--Incident Details-->
                        <dl class="dl-horizontal" *ngIf="i == 0">
                            <dt>Date & Time of Incident: </dt>
                            <dd>{{dateFormat(this.incidentReport.incident_date).format('DD/MM/YYYY HH:mm')}}</dd>

                            <dt>Lighting Conditions: </dt>
                            <dd>{{incidentReport.lighting_condition}}</dd>

                            <dt>Weather Conditions: </dt>
                            <dd>{{incidentReport.weather_conditions}}</dd>

                            <dt>Incident Details: </dt>
                            <dd class="text-break">{{incidentReport.incident_details}}</dd>

                            <dt>Potential Severity: </dt>
                            <dd>{{incidentReport.potential_severity}}</dd>

                            <dt>Actual Severity: </dt>
                            <dd>{{incidentReport.actual_severity}}</dd>
                        </dl>

                        <!--Location Environment Detail-->
                        <dl class="dl-horizontal" *ngIf="i == 1">
                            <dt>Location Type: </dt>
                            <dd>{{incidentReport.loc_env_details.location_type}}</dd>

                            <ng-container *ngIf="incidentReport.loc_env_details.location_type == 'Road'">
                                <dt>Type of Road: </dt>
                                <dd>{{incidentReport.loc_env_details.road_type}}</dd>

                                <dt>Road Speed Limit (MPH): </dt>
                                <dd>{{incidentReport.loc_env_details.speed_limit}}</dd>

                                <dt>Full Width of Road (approx.): </dt>
                                <dd>{{incidentReport.loc_env_details.road_width}}</dd>
                            </ng-container>

                            <dt>Location: </dt>
                            <dd>{{incidentReport.loc_env_details.location}}</dd>

                            <dt>State of Surface: </dt>
                            <dd>{{incidentReport.loc_env_details.surface_state}}</dd>

                            <dt>Visibility: </dt>
                            <dd>{{incidentReport.loc_env_details.visibility}}</dd>
                        </dl>

                        <!--Vehicle Detail-->
                        <dl class="dl-horizontal" *ngIf="i == 2">
                            <dt><span i18n="@@vrn">Vehicle Registration Number</span>: </dt>
                            <dd>{{incidentReport.vehicle_details.reg_number}}</dd>

                            <dt>Vehicle Type: </dt>
                            <dd>{{incidentReport.vehicle_details.type}}</dd>

                            <dt>Vehicle Status: </dt>
                            <dd>{{incidentReport.vehicle_details.status}}</dd>

                            <dt>Was The Vehicle Stationary? </dt>
                            <dd>{{(incidentReport.vehicle_details.was_stationary) ? "Yes" : "No" }}</dd>

                            <ng-container *ngIf="incidentReport.vehicle_details.was_stationary">
                                <dt>Speed of Vehicle: </dt>
                                <dd>{{incidentReport.vehicle_details.speed}}</dd>
                            </ng-container>

                            <dt>Was The Vehicle Unattanded? </dt>
                            <dd>{{(incidentReport.vehicle_details.was_unattanded) ? "Yes" : "No" }}</dd>

                            <ng-container *ngIf="incidentReport.vehicle_details.was_unattanded === false">
                                <dt>Number of Passengers: </dt>
                                <dd>{{incidentReport.vehicle_details.passengers_count}}</dd>
                            </ng-container>


                            <dt>Full Details of Damage: </dt>
                            <dd class="text-break">{{incidentReport.vehicle_details.damage_details}}</dd>
                        </dl>

                        <!--Driver Detail-->
                        <dl class="dl-horizontal" *ngIf="i == 3">
                            <dt>First Name: </dt>
                            <dd>{{incidentReport.driver_details.f_name}}</dd>

                            <dt>Last Name: </dt>
                            <dd>{{incidentReport.driver_details.l_name}}</dd>

                            <dt>Contact Number: </dt>
                            <dd>{{ incidentReport.driver_details?.contact_number?.code ? '(+' + incidentReport.driver_details?.contact_number.code + ')' : '' }} {{ incidentReport.driver_details?.contact_number?.number }}</dd>

                            <dt>Job Title: </dt>
                            <dd>{{incidentReport.driver_details.job_title}}</dd>

                            <dt>Address: </dt>
                            <dd class="text-break">{{incidentReport.driver_details.address}}</dd>
                        </dl>

                        <!--Third Parties-->
                        <dl class="dl-horizontal" *ngIf="i == 4">
                            <dt>Were there any Third Parties?</dt>
                            <dd>{{(incidentReport.is_thirdparty_vehicle) ? "Yes" : "No" }}</dd>

                            <ng-container *ngIf="incidentReport.is_thirdparty_vehicle && incidentReport.thirdparty_vehicle.length">
                                <dt>Third Party Detail: </dt>
                                <ul *ngFor="let thirdParty of incidentReport.thirdparty_vehicle; let i = index;" class="list-unstyled">
                                    <li>{{i+1}}. {{thirdParty.name}} ({{thirdParty.category}})</li>
                                </ul>
                            </ng-container>
                        </dl>

                        <!--Witnesses-->
                        <dl class="dl-horizontal" *ngIf="i == 5">
                            <dt>Were there any Witnesses?</dt>
                            <dd>{{(incidentReport.any_witnesses) ? "Yes" : "No" }}</dd>

                            <ng-container *ngIf="incidentReport.any_witnesses && incidentReport.witnesses.length">
                                <dt>Witnesses Detail: </dt>
                                <ul *ngFor="let witness of incidentReport.witnesses; let i = index;" class="list-unstyled">
                                    <li>{{i+1}}. {{witness.f_name}} {{witness.l_name}} ({{witness.person_type}})</li>
                                </ul>
                            </ng-container>
                        </dl>

                        <!--Attachments-->
                        <dl class="dl-horizontal" *ngIf="i == 6">
                            <dd *ngIf="attachments.length else noAttachment">
                                <ul *ngFor="let attachment of attachments; let i = index;" class="list-unstyled">
                                    <li>
                                        <a class="d-block w-100 text-info text-underline" target="_blank" [href]="attachment.file_url">{{attachment.name}}</a>
                                        <span class="text-break">{{incidentReport.attachment_file_ids[i].description}}</span>
                                    </li>
                                </ul>
                            </dd>
                            <ng-template  #noAttachment>
                                <dd class="text-center">None Added</dd>
                            </ng-template>
                        </dl>

                        <!--Actions-->
                        <dl class="dl-horizontal" *ngIf="i == 7">
                            <ng-container  *ngIf="incidentReport.action_details else noAction">
                                <dt>Initial Action Taken:</dt>
                                <dd class="text-break">{{incidentReport.action_details}}</dd>
                            </ng-container>
                            <ng-template  #noAction>
                                <dd class="text-center">None Added</dd>
                            </ng-template>
                        </dl>
                    </div>
                </div>
            </ng-template>
        </fieldset>
    </form>
</div>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

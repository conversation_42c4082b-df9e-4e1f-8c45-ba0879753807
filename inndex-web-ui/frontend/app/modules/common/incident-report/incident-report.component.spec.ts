import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IncidentReportComponent } from './incident-report.component';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import {CookieService, HttpService, ResourceService, ToastService, UserService} from '@app/core';
import {NgbModal, NgbModule} from '@ng-bootstrap/ng-bootstrap';
import {of, Subject} from 'rxjs';
import { AuthService, IncidentReportService } from '@app/core';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {NgbMomentjsAdapter} from '@app/core/ngb-moment-adapter';
import {
    FormsModule, NgModel,
    ReactiveFormsModule
} from '@angular/forms';
import {NgSelectModule} from '@ng-select/ng-select';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";

// Extend the plugins (needed to use `tz`)
dayjs.extend(utc);
dayjs.extend(timezone);


describe('IncidentReportComponent', () => {
    let component: IncidentReportComponent;
    let fixture: ComponentFixture<IncidentReportComponent>;
    let activatedRouteMock: any;
    let routerMock: any;
    let authServiceMock: any;
    let incidentReportServiceMock: any;
    let httpServiceMock: any;
    let resourceServiceMock: any;
    let toastServiceMock: any;
    let mockNgbMomentJsAdapter: any;

    beforeEach(async () => {
        // Mocking the dependencies
        activatedRouteMock = {
            snapshot: {
                queryParams: { 'incident_type': 'injury' },
                url: [
                    { path: 'company' },
                    { path: 'project-incident-reports' }
                ],
                data: {
                    is_project_portal: false,
                    companyResolverResponse: {
                        company: {
                            id: 1,
                            logo_file_url: 'logo.png',
                            features_status: {
                                incident_reports: true
                            }
                        },
                        companyProjects: [],
                        archivedCompanyProjects: []
                    }
                },
                params: { employerId: 123, projectId: 456 }
            },
            params: of({ employerId: 123, projectId: 456 }),
        };



        routerMock = { navigate: jasmine.createSpy('navigate') };
        authServiceMock = {
            get authUser() {
                return of({ id: 1, name: 'Test User' });
            }
        };

    incidentReportServiceMock = {
      updateIncidentReport: jasmine.createSpy("updateIncidentReport").and.returnValue(of({ success: true })),
      updateIncidentReportCA: jasmine.createSpy("updateIncidentReportCA").and.returnValue(of({ success: true })),
      getProjectIncidentReports: jasmine.createSpy().and.returnValue(of([])),
    };

        httpServiceMock = {
            isMobileDevice: jasmine.createSpy('isMobileDevice').and.returnValue(false),
            isProjectPortal: jasmine.createSpy('isProjectPortal').and.returnValue(true),
        };

        resourceServiceMock = {
            getCompanySettingByName: jasmine.createSpy().and.returnValue(
                of({
                    success: true,
                    record: {
                        value: [
                            { name: 'Safety', active: true },
                            { name: 'Compliance', active: false }
                        ]
                    }
                })
            ),
            getInnDexSettingByName: jasmine.createSpy().and.returnValue(
                of({
                    success: true,
                    record: {
                        value: {
                            immediate_causes: [{ id: 1, name: 'Human Error' }],
                            underlying_causes: [{ id: 2, name: 'Process Failure' }],
                            root_causes: [{ id: 3, name: 'Systemic Issues' }]
                        }
                    }
                })
            )
        };

        toastServiceMock = {
            types: { INFO: 'info', ERROR: 'error' },
            show: jasmine.createSpy('show')
        };

        mockNgbMomentJsAdapter = {
            dayJsToNgbDate: (date: dayjs.Dayjs) => ({ year: date.year(), month: date.month() + 1, day: date.date() })
        };

        // Setting up TestBed
        await TestBed.configureTestingModule({
            declarations: [
                IncidentReportComponent
            ],
            imports:[
                HttpClientTestingModule,
                FormsModule,
                ReactiveFormsModule,
                NgbModule,
                NgSelectModule,
            ],
            providers: [
                { provide: Router, useValue: routerMock },
                { provide: ActivatedRoute, useValue: activatedRouteMock },
                { provide: AuthService, useValue: authServiceMock },
                { provide: IncidentReportService, useValue: incidentReportServiceMock },
                { provide: HttpService, useValue: httpServiceMock },
                { provide: NgbModal, useValue: {} },
                {provide: ResourceService, useValue: resourceServiceMock },
                {provide: ToastService, useValue: toastServiceMock },
                {provide: NgbMomentjsAdapter, useValue: mockNgbMomentJsAdapter},
                CookieService,
                UserService
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
        }).compileComponents();
    });

  beforeEach(() => {
    fixture = TestBed.createComponent(IncidentReportComponent);
    component = fixture.componentInstance;
    // Set up authUser$ property
    component.authUser$ = { id: 1, name: "Test User" };
    fixture.detectChanges();
  });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize constructor values properly', () => {
        const today = mockNgbMomentJsAdapter.dayJsToNgbDate(dayjs());

        expect(component.from_date).toEqual(today);
        expect(component.to_date).toEqual(today);
        expect(component.selectedIncidentType).toBe('injury');
        expect(component.isProjectPortal).toBeFalsy();
        expect(component.isMobileDevice).toBeFalsy();
        expect(component.projectId).toBe(456);
    });

    it('should format date with timezone', () => {
        const testDate = 1710486400000; // Sample timestamp
        // Mock tz() method in dayjs
        spyOn(dayjs.prototype, 'tz').and.callFake(function () {
            return this; // Return the same instance
        });
        const result = component.dayjs(testDate);
        expect(result).toBeDefined();
    });

  describe("saveReviewField", () => {
    beforeEach(() => {
      spyOn(component, "populateSelectedPersonnel");
      spyOn<any>(component, "initializeTable");
      // Set up the component state properly
      component.isProjectPortal = true;
      component.projectId = 456;
    });

        function createMockForm(value: any) {
            return {
                value,
                controls: {
                    test_field: {
                        reset: jasmine.createSpy('reset')
                    }
                }
            };
        }

        it('should show info toast if no comment and no cause options selected', () => {
            component.causeOptSelection = { test_field: [] };
            const form = createMockForm({ test_field: '' });

            component.saveReviewField(form, null, 'test_field', { test_field: [] });

            expect(toastServiceMock.show).toHaveBeenCalledWith(
                toastServiceMock.types.INFO,
                'You must select a user to proceed.'
            );
            expect(incidentReportServiceMock.updateIncidentReport).not.toHaveBeenCalled();
        });

        it('should send request if only comment provided', () => {
            component.causeOptSelection = { test_field: [] };
            const form = createMockForm({ test_field: 'Test comment' });

            const ir = { id: 99, test_field: [] };

            component.saveReviewField(form, null, 'test_field', ir);

      expect(incidentReportServiceMock.updateIncidentReport).toHaveBeenCalled();
      const callArgs = incidentReportServiceMock.updateIncidentReport.calls.mostRecent().args;
      expect(callArgs[0].action).toBe("saveReviewField");
      expect(callArgs[0].test_field.length).toBe(1);
      expect(callArgs[0].test_field[0].comment).toBe("Test comment");
    });

        it('should send request if only cause options provided', () => {
            component.causeOptSelection = {
                test_field: [{ head: 'Head A', subhead: 'Sub A' }]
            };
            const form = createMockForm({ test_field: '' });
            const ir = { id: 99, test_field: [] };

            component.saveReviewField(form, null, 'test_field', ir);

      expect(incidentReportServiceMock.updateIncidentReport).toHaveBeenCalled();
      const callArgs = incidentReportServiceMock.updateIncidentReport.calls.mostRecent().args;
      expect(callArgs[0].action).toBe("saveReviewField");
      expect(callArgs[0].test_field.length).toBe(1);
      expect(callArgs[0].test_field[0].head).toBe("Head A");
    });

        it('should send combined comment and cause options request', () => {
            component.causeOptSelection = {
                test_field: [{ head: 'Head A', subhead: 'Sub A' }]
            };
            const form = createMockForm({ test_field: 'With comment' });
            const ir = { id: 99, test_field: [] };

            component.saveReviewField(form, null, 'test_field', ir);

      expect(incidentReportServiceMock.updateIncidentReport).toHaveBeenCalled();
      const callArgs = incidentReportServiceMock.updateIncidentReport.calls.mostRecent().args;
      expect(callArgs[0].action).toBe("saveReviewField");
      expect(callArgs[0].test_field.length).toBe(2);
    });

        it('should show error toast on failed update', () => {
            incidentReportServiceMock.updateIncidentReport.and.returnValue(of({ success: false, message: 'Fail!' }));
            component.causeOptSelection = {
                test_field: [{ head: 'Head A', subhead: 'Sub A' }]
            };
            const form = createMockForm({ test_field: '' });
            const ir = { id: 99, test_field: [] };

            component.saveReviewField(form, null, 'test_field', ir);

            expect(toastServiceMock.show).toHaveBeenCalledWith(
                toastServiceMock.types.ERROR,
                'Fail!',
                { data: { success: false, message: 'Fail!' } }
            );
        });

        it('should reset form and initialize table on success', () => {
            component.causeOptSelection = {
                test_field: [{ head: 'Head A', subhead: 'Sub A' }]
            };
            const form = createMockForm({ test_field: 'Test' });
            const ir = { id: 99, test_field: [] };

            component.saveReviewField(form, null, 'test_field', ir);

            expect(form.controls.test_field.reset).toHaveBeenCalled();
            expect(component.causeOptSelection.test_field).toEqual([{ head: null, subhead: null }]);
            expect((component as any).initializeTable).toHaveBeenCalled();
        });

        it('should reset to special object if field is relevant_personnel_user_refs', () => {
            component.causeOptSelection = {
                relevant_personnel_user_refs: [{ head: 'Head A', subhead: 'Sub A' }]
            };
            const form = createMockForm({ relevant_personnel_user_refs: 'Test' });
            const ir = { id: 99, relevant_personnel_user_refs: [] };

            component.saveReviewField(form, null, 'relevant_personnel_user_refs', ir);

            expect(component.causeOptSelection.relevant_personnel_user_refs).toEqual([
                { head: null, subhead: null, id: 0 }
            ]);
            expect(component.populateSelectedPersonnel).toHaveBeenCalled();
        });
    });

  describe("saveReviewField - Company Admin", () => {
    beforeEach(() => {
      spyOn(component, "populateSelectedPersonnel");
      spyOn<any>(component, "initializeTable");
      // Set up the component state for company admin (non-project portal)
      component.isProjectPortal = false;
      component.employerId = 123;
    });

    function createMockForm(value: any) {
      return {
        value,
        controls: {
          test_field: {
            reset: jasmine.createSpy("reset"),
          },
        },
      };
    }

    it("should call updateIncidentReportCA when isProjectPortal is false", () => {
      component.causeOptSelection = { test_field: [] };
      const form = createMockForm({ test_field: "Test comment" });
      const ir = { id: 99, test_field: [] };

      component.saveReviewField(form, null, "test_field", ir);

      expect(incidentReportServiceMock.updateIncidentReportCA).toHaveBeenCalled();
      expect(incidentReportServiceMock.updateIncidentReport).not.toHaveBeenCalled();

      const callArgs = incidentReportServiceMock.updateIncidentReportCA.calls.mostRecent().args;
      expect(callArgs[0].action).toBe("saveReviewField");
      expect(callArgs[0].test_field.length).toBe(1);
      expect(callArgs[0].test_field[0].comment).toBe("Test comment");
      expect(callArgs[1]).toBe(123); // employerId
      expect(callArgs[2]).toBe(99); // ir.id
    });

    it("should send request with cause options using updateIncidentReportCA", () => {
      component.causeOptSelection = {
        test_field: [{ head: "Head A", subhead: "Sub A" }],
      };
      const form = createMockForm({ test_field: "" });
      const ir = { id: 99, test_field: [] };

      component.saveReviewField(form, null, "test_field", ir);

      expect(incidentReportServiceMock.updateIncidentReportCA).toHaveBeenCalled();
      const callArgs = incidentReportServiceMock.updateIncidentReportCA.calls.mostRecent().args;
      expect(callArgs[0].action).toBe("saveReviewField");
      expect(callArgs[0].test_field.length).toBe(1);
      expect(callArgs[0].test_field[0].head).toBe("Head A");
      expect(callArgs[1]).toBe(123); // employerId
      expect(callArgs[2]).toBe(99); // ir.id
    });

    it("should show error toast on failed updateIncidentReportCA", () => {
      incidentReportServiceMock.updateIncidentReportCA.and.returnValue(of({ success: false, message: "Fail!" }));
      component.causeOptSelection = {
        test_field: [{ head: "Head A", subhead: "Sub A" }],
      };
      const form = createMockForm({ test_field: "" });
      const ir = { id: 99, test_field: [] };

      component.saveReviewField(form, null, "test_field", ir);

      expect(toastServiceMock.show).toHaveBeenCalledWith(toastServiceMock.types.ERROR, "Fail!", { data: { success: false, message: "Fail!" } });
    });

    it("should reset form and initialize table on successful updateIncidentReportCA", () => {
      component.causeOptSelection = {
        test_field: [{ head: "Head A", subhead: "Sub A" }],
      };
      const form = createMockForm({ test_field: "Test" });
      const ir = { id: 99, test_field: [] };

      component.saveReviewField(form, null, "test_field", ir);

      expect(form.controls.test_field.reset).toHaveBeenCalled();
      expect(component.causeOptSelection.test_field).toEqual([{ head: null, subhead: null }]);
      expect((component as any).initializeTable).toHaveBeenCalled();
    });

    it("should handle relevant_personnel_user_refs field with updateIncidentReportCA", () => {
      component.causeOptSelection = {
        relevant_personnel_user_refs: [{ head: "Head A", subhead: "Sub A" }],
      };
      const form = createMockForm({ relevant_personnel_user_refs: "Test" });
      const ir = { id: 99, relevant_personnel_user_refs: [] };

      component.saveReviewField(form, null, "relevant_personnel_user_refs", ir);

      expect(incidentReportServiceMock.updateIncidentReportCA).toHaveBeenCalled();
      expect(component.causeOptSelection.relevant_personnel_user_refs).toEqual([{ head: null, subhead: null, id: 0 }]);
      expect(component.populateSelectedPersonnel).toHaveBeenCalled();
    });
  });

    it('should toggle primaryBtnDisabled based on closeOutDetailModel validity', () => {
        const statusChangesMock = new Subject<void>();

        // Fake NgModel with a dynamic valid getter
        let isValid = true;
        const fakeNgModel: Partial<NgModel> = {
            get valid() {
                return isValid;
            },
            statusChanges: statusChangesMock.asObservable()
        };

        // Required mock config object
        component.closeOutActionModalRefConfig = {
            modalTitle: '',
            modalOptions: {},
            primaryBtnDisabled: true
        };

        // Manually assign mocked ViewChild
        component.closeOutDetailModel = fakeNgModel as NgModel;

        component.ngAfterViewInit();

        // Emit with valid = true
        isValid = true;
        statusChangesMock.next();
        expect(component.closeOutActionModalRefConfig.primaryBtnDisabled).toBeFalsy();

        // Emit with valid = false
        isValid = false;
        statusChangesMock.next();
        expect(component.closeOutActionModalRefConfig.primaryBtnDisabled).toBeTruthy();
    });
});

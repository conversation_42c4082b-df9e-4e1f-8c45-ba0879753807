<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav [employer]="employer" [companyResolverResponse]="companyResolverResponse" *ngIf="!isProjectPortal" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [isCompanyHeader]="!isProjectPortal && !isCompanyProjectRoute()" [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="col-sm-12 my-3 outer-border" *ngIf="!loadingIncidents">
                <div class="outer-border-radius">
                    <div class="d-flex flex-wrap justify-content-between flex-column flex-sm-row px-3">
                        <h5 class="float-md-left">Total Incidents <small>(Open {{ openRecords }} : Raised {{ (page && page.totalElements) ? page.totalElements : records.length }})</small></h5>
                        <action-button
                                *ngIf="isProjectPortal else actionSelector"
                                [actionList]="actionButtonMetaData.actionList"
                                (selectedActionEmmiter)="onActionSelection($event)"
                                [newFeatureTitle]="'New Report'"
                                (onOpenAddNew)="newReportModal()">
                        </action-button>

                        <ng-template #actionSelector>
                            <action-button
                                    [hideNewFeatureBtn]="true"
                                    [actionList]="actionButtonMetaData.actionList"
                                    (selectedActionEmmiter)="onActionSelection($event)">
                            </action-button>
                        </ng-template>
                    </div>
                    <div class="col-sm-12 my-2">
                        <div>
                            <search-with-filters (searchEmitter)="searchFunction($event)" (filterEmitter)="onFilterSelection($event)" [filterData]='filterData'></search-with-filters>
                        </div>
                        <div class="table-responsive-sm">
                            <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                        [scrollbarV]="true"
                                        [virtualization]="false"
                                        [loadingIndicator]="loadingInlineIncidentReport"
                                        [rows]="records"
                                        [footerHeight]="40"
                                        [columnMode]="'force'"
                                        [rowHeight]="'auto'"
                                        [externalPaging]="true"
                                        [count]="page.totalElements"
                                        [offset]="page.pageNumber"
                                        [limit]="page.size"
                                        (page)="pageCallback($event, true)"
                            >
                                <ngx-datatable-column headerClass="font-weight-bold" [maxWidth]="100" [sortable]="false" minWidth="70">
                                    <ng-template let-column="column" ngx-datatable-header-template>
                                        Incident #
                                    </ng-template>
                                    <ng-template let-row="row" ngx-datatable-cell-template>
                                        {{row.record_ref}}
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column headerClass="font-weight-bold" *ngIf="!isProjectPortal && !isCompanyProjectRoute()" [sortable]="false" minWidth="100">
                                    <ng-template let-column="column" ngx-datatable-header-template>
                                        Project
                                    </ng-template>
                                    <ng-template let-row="row" ngx-datatable-cell-template>
                                        <span appTooltip>{{ row.project_ref?.name }}</span>
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                                    <ng-template let-column="column" ngx-datatable-header-template>
                                        Prepared By
                                    </ng-template>
                                    <ng-template let-row="row" ngx-datatable-cell-template>
                                        <span appTooltip>{{row?.user_ref?.first_name}} {{row?.user_ref?.last_name}}</span>
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                                    <ng-template let-column="column" ngx-datatable-header-template>
                                        Incident Date
                                    </ng-template>
                                    <ng-template let-row="row" ngx-datatable-cell-template>
                                        {{ dayjs(+row.incident_date).format(AppConstant.defaultDateFormat)}}
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                                    <ng-template let-column="column" ngx-datatable-header-template>
                                        Incident Type
                                    </ng-template>
                                    <ng-template let-row="row" ngx-datatable-cell-template>
                                        <span appTooltip>{{row?.incident_type}}</span>
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column headerClass="font-weight-bold action-column text-center"
                                                      cellClass="action-column no-ellipsis" [sortable]="false" minWidth="110">
                                    <ng-template let-column="column" ngx-datatable-header-template>
                                        Actions
                                    </ng-template>
                                    <ng-template let-row="row" ngx-datatable-cell-template>
                                        <div class="text-center">
                                            {{ getActionsStatusText(row.incident_actions) }}
                                            <span *ngIf="!hasAllActionClosed" class="btn-sm badge-pill badge-success">
                                                {{ actionsColumnTxt }}
                                            </span>
                                            <span *ngIf="hasAllActionClosed" (click)="openReviewModal(row,'', 'toggle-9')" class="btn-sm badge-pill badge-danger cursor-pointer">
                                                {{ actionsColumnTxt }}
                                            </span>
                                        </div>
                                    </ng-template>
                                </ngx-datatable-column>

                                <ngx-datatable-column headerClass="font-weight-bold text-center text-center" cellClass="no-ellipsis" [sortable]="false" minWidth="100">
                                    <ng-template let-column="column" ngx-datatable-header-template>
                                        Status
                                    </ng-template>
                                    <ng-template let-row="row" ngx-datatable-cell-template>
                                        <div class="text-center">
                                            <span class="btn-sm badge-pill {{(row.status_message == 'Open') ? 'badge-danger' : ((row.status_message == 'Reviewed') ? 'ir-reviewed' : 'badge-success')}}">
                                                {{ row?.status_message }}
                                            </span>
                                        </div>
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                                    <ng-template let-column="column" ngx-datatable-header-template>
                                        Closed Out
                                    </ng-template>
                                    <ng-template let-row="row" ngx-datatable-cell-template>
                                        {{ getCloseOutDate(row.closed_out_date) }}
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column headerClass="font-weight-bold action-column" cellClass="action-column" [width]="230" [sortable]="false" minWidth="100">
                                    <ng-template let-column="column" ngx-datatable-header-template>
                                        Action
                                    </ng-template>
                                    <ng-template let-row="row" ngx-datatable-cell-template>
                                        <button-group
                                            [buttons]="rowButtonGroup"
                                            [btnConditions]="[
                                                true,
                                                (row.status != 3) && (isProjectPortal),
                                                true,
                                                true,
                                                row.status != 3,
                                                row.status != 3 && ((companyIncidentsEnabled && !isProjectPortal) || (isProjectPortal && !companyIncidentsEnabled))
                                            ]"
                                            (onActionClick)="rowBtnClicked($event, row)">
                                        </button-group>
                                    </ng-template>
                                </ngx-datatable-column>
                            </ngx-datatable>
                            <block-loader [show]="(downloadIRLoading)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<app-generic-modal #reviewPopupHtml [genericModalConfig]="reviewPopupHtmlModalConfig">
    <ng-container *ngTemplateOutlet="reviewPopupTemplate"></ng-container>
    <ng-template #reviewPopupTemplate>
        <div class="incident-reports-review">
            <form novalidate #reviewForm="ngForm" *ngIf="showReviewForm">
                <ngb-accordion>
                    <ngb-panel id="toggle-1">
                        <ng-template ngbPanelHeader>
                            <button ngbPanelToggle class="btn p-0">
                                <i class="fas fa-angle-down"></i>
                                <strong> 1. Project personnel relevant to the incident</strong>
                            </button>
                            <div class="float-right font-italic"><small>{{ ir_row?.relevant_personnel_user_refs?.length || 'No' }} Personnel added</small></div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div class="overflow-auto p-4">
                                <div class="col-10 p-0">
                                    <ng-template ngFor let-opt [ngForOf]="causeOptSelection['relevant_personnel_user_refs'] | reverse" let-i="index">
                                        <div class="mb-3 d-flex align-items-center">
                                            <div class="p-0 d-inline-block col-5">
                                                <inducted-user-selector
                                                    *ngIf="showReviewForm"
                                                    [required]="true"
                                                    placeholder="Select Personnel"
                                                    searchMessage="Type to search Personnel"
                                                    [selection]="opt.id"
                                                    extraFields="employment"
                                                    [name]="'relevant_personnel_user_' + i"
                                                    [projectId]="ir_row?.project_ref?.id || ir_row?.project_ref"
                                                    (selectionChanged)="headSelected('relevant_personnel_user_refs',i, $event)"
                                                ></inducted-user-selector>
                                            </div>
                                            <div class="pr-0 d-inline-block col-6" *ngIf="opt.head">
                                                <ng-select class="dropdown-list" appendTo="body" placeholder="Select Job Role" [(ngModel)]="opt.subhead"
                                                    (change)="subHeadSelected('relevant_personnel_user_refs', i)"
                                                    name="relevant_personnel_role_{{i}}" required class="ir_personnel_opts">
                                                    <ng-option *ngFor="let t of jobRoleList" [value]="t.name">{{ t.name }}</ng-option>
                                                </ng-select>
                                            </div>
                                            <span class="ml-2 material-symbols-outlined text-danger cursor-pointer" *ngIf="i!=0" (click)="removeCauseRow('relevant_personnel_user_refs', i)">
                                                delete
                                            </span>
                                        </div>
                                    </ng-template>
                                </div>
                                <div class="mb-2" style="min-height: 80px;">
                                    <button (click)="saveReviewField(reviewForm, modalLoader, 'relevant_personnel_user_refs', ir_row)"
                                        type="button" class="btn mt-5 btn-outline-primary float-right">Save</button>
                                </div>
                                <div>
                                    <div class="list-unstyled" *ngIf="ir_row?.relevant_personnel_user_refs?.length">
                                        <ng-template ngFor let-comment [ngForOf]="ir_row?.relevant_personnel_user_refs | reverse" let-i="index">
                                            <div *ngIf="comment?.comment || comment?.head">
                                                <div class="d-flex m-2">
                                                    <div class="col-10 p-0 d-inline-block">
                                                        <span [innerText]="(comment?.head)? comment?.head+': '+comment?.subhead : comment.comment"></span> -
                                                        <small class="text-black-50">{{ comment.user_name }}, {{ dayjs(+comment.added_at).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}</small>
                                                    </div>
                                                    <ng-container *ngIf="comment.user_ref == authUser$.id">
                                                        <div class="d-flex col-2 mt-auto">
                                                            <button class="btn p-0 mx-2" (click)="editCommentModal(comment, i, 'relevant_personnel_user_refs')"><i class="fas fa-edit text-color-yellow"></i></button>
                                                            <button class="btn p-0 mx-2" (click)="deleteCommentWarningConfirm(i, 'relevant_personnel_user_refs')">
                                                                <span class="material-symbols-outlined text-danger cursor-pointer">
                                                                    delete
                                                                </span>
                                                            </button>
                                                        </div>
                                                    </ng-container>
                                                </div>
                                            </div>
                                        </ng-template>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
                <ngb-accordion #acc="ngbAccordion" [activeIds]="accordionActiveIds">
                    <ngb-panel id="toggle-1">
                        <ng-template ngbPanelHeader>
                            <button ngbPanelToggle class="btn p-0">
                                <i class="fas fa-angle-down"></i>
                                <strong> 2. Events Leading up to the Incident</strong>
                            </button>
                            <div class="float-right font-italic"><small>{{ ir_row?.incident_events?.length || 'No' }} Comments added</small></div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div class="overflow-auto p-4">
                                <div class="row mb-2 mx-0">
                                    <div class="col-10 p-0 d-inline-block">
                                        <textarea class="form-control" name="incident_events" rows="5"
                                            #incidentEvents="ngModel"
                                            [ngModel]="ir_row.incident_events[ir_row?.incident_events?.length]"
                                            placeholder="Details of Events Leading up to the Incident..."></textarea>
                                    </div>
                                    <div class="d-inline-block col col-lg-2 float-right mt-auto">
                                        <button (click)="saveReviewField(reviewForm, modalLoader, 'incident_events', ir_row)"
                                            type="button" class="btn mt-5 btn-outline-primary float-right"
                                            [disabled]="!incidentEvents.value">Save</button>
                                    </div>
                                </div>
                                <div class="list-unstyled" *ngIf="ir_row?.incident_events?.length">
                                    <ng-template ngFor let-comment [ngForOf]="ir_row?.incident_events | reverse" let-i="index">
                                        <div *ngIf="comment?.comment">
                                            <div class="d-flex m-2">
                                                <div class="col-10 p-0 d-inline-block">
                                                    <span [innerText]="comment.comment"></span> -
                                                    <small class="text-black-50">{{ comment.user_name }}, {{ dayjs(+comment.added_at).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}</small>
                                                </div>
                                                <ng-container *ngIf="comment.user_ref == authUser$.id">
                                                    <div class="d-flex col-2 mt-auto">
                                                        <button class="btn p-0 mx-2" (click)="editCommentModal(comment, i, 'incident_events')"><i class="fas fa-edit text-color-yellow"></i></button>
                                                        <button class="btn p-0 mx-2" (click)="deleteCommentWarningConfirm(i, 'incident_events')">
       <span class="material-symbols-outlined text-color-red">
                                                                delete
                                                            </span>
                                                        </button>
                                                    </div>
                                                </ng-container>
                                            </div>
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
                <ngb-accordion>
                    <ngb-panel id="toggle-2">
                        <ng-template ngbPanelHeader>
                            <button ngbPanelToggle class="btn p-0">
                                <i class="fas fa-angle-down"></i>
                                <strong> 3. Harm</strong>
                            </button>
                            <div class="float-right font-italic"><small>{{ ir_row?.incident_harm?.length || 'No' }} Comments added</small></div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div class="overflow-auto p-4">
                                <div class="row mb-2 mx-0">
                                    <div class="col-10 p-0 d-inline-block">
                                        <textarea class="form-control" name="incident_harm" rows="5"
                                            #incidentHarm="ngModel"
                                            [ngModel]="ir_row.incident_harm[ir_row?.incident_harm?.length]"
                                            placeholder="Details of Incident Harm..."></textarea>
                                    </div>
                                    <div class="d-inline-block col col-lg-2 float-right mt-auto">
                                        <button (click)="saveReviewField(reviewForm, modalLoader, 'incident_harm', ir_row)"
                                            type="button" class="btn mt-5 btn-outline-primary float-right"
                                            [disabled]="!incidentHarm.value">Save</button>
                                    </div>
                                </div>
                                <div class="list-unstyled" *ngIf="ir_row?.incident_harm?.length">
                                    <ng-template ngFor let-comment [ngForOf]="ir_row?.incident_harm | reverse" let-i="index">
                                        <div *ngIf="comment?.comment">
                                            <div class="d-flex m-2">
                                                <div class="col-10 p-0 d-inline-block">
                                                    <span [innerText]="comment.comment"></span> -
                                                    <small class="text-black-50">{{ comment.user_name }}, {{ dayjs(+comment.added_at).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}</small>
                                                </div>
                                                <ng-container *ngIf="comment.user_ref == authUser$.id">
                                                    <div class="d-flex col-2 mt-auto">
                                                        <button class="btn p-0 mx-2" (click)="editCommentModal(comment, i, 'incident_harm')"><i class="fas fa-edit text-color-yellow"></i></button>
                                                        <button class="btn p-0 mx-2" (click)="deleteCommentWarningConfirm(i, 'incident_harm')">
       <span class="material-symbols-outlined text-color-red">
                                                                delete
                                                            </span>
                                                        </button>
                                                    </div>
                                                </ng-container>
                                            </div>
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
                <ngb-accordion>
                    <ngb-panel id="toggle-3">
                        <ng-template ngbPanelHeader>
                            <button ngbPanelToggle class="btn p-0">
                                <i class="fas fa-angle-down"></i>
                                <strong> 4. Investigation Findings</strong>
                            </button>
                            <div class="float-right font-italic"><small>{{ ir_row?.investigation_findings?.length || 'No' }} Comments added</small></div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div class="overflow-auto p-4">
                                <div class="row mb-2 mx-0">
                                    <div class="col-10 p-0 d-inline-block col-10">
                                        <textarea class="form-control" name="investigation_findings" rows="5"
                                            #investigationFindings="ngModel"
                                            [ngModel]="ir_row.investigation_findings[ir_row?.investigation_findings?.length]"
                                            placeholder="Details of Investigation Findings..."></textarea>
                                    </div>
                                    <div class="d-inline-block col col-lg-2 float-right mt-auto">
                                        <button (click)="saveReviewField(reviewForm, modalLoader, 'investigation_findings', ir_row)"
                                            type="button" class="btn mt-5 btn-outline-primary float-right"
                                            [disabled]="!investigationFindings.value">Save</button>
                                    </div>
                                </div>
                                <div>
                                    <div class="list-unstyled" *ngIf="ir_row?.investigation_findings?.length">
                                        <ng-template ngFor let-comment [ngForOf]="ir_row?.investigation_findings | reverse" let-i="index">
                                            <div *ngIf="comment?.comment">
                                                <div class="d-flex m-2">
                                                    <div class="col-10 p-0 d-inline-block">
                                                        <span [innerText]="comment.comment"></span> -
                                                        <small class="text-black-50">{{ comment.user_name }}, {{ dayjs(+comment.added_at).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}</small>
                                                    </div>
                                                    <ng-container *ngIf="comment.user_ref == authUser$.id" class="ml-2">
                                                        <div class="d-flex col-2 mt-auto">
                                                            <button class="btn p-0 mx-2" (click)="editCommentModal(comment, i, 'investigation_findings')"><i class="fas fa-edit text-color-yellow"></i></button>
                                                            <button class="btn p-0 mx-2" (click)="deleteCommentWarningConfirm(i, 'investigation_findings')">
           <span class="material-symbols-outlined text-color-red">
                                                                    delete
                                                                </span>
                                                            </button>
                                                        </div>
                                                    </ng-container>
                                                </div>
                                            </div>
                                        </ng-template>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
                <ngb-accordion>
                    <ngb-panel id="toggle-4">
                        <ng-template ngbPanelHeader>
                            <button ngbPanelToggle class="btn p-0">
                                <i class="fas fa-angle-down"></i>
                                <strong> 5. History of Similar Incidents</strong>
                            </button>
                            <div class="float-right font-italic"><small>{{ ir_row?.similar_incidents?.length || 'No' }} Comments added</small></div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div class="overflow-auto p-4">
                                <div class="row mb-2 mx-0">
                                    <div class="col-10 p-0 d-inline-block">
                                        <textarea class="form-control" name="similar_incidents" rows="5"
                                            #similarIncidents="ngModel"
                                            [ngModel]="ir_row.similar_incidents[ir_row?.similar_incidents?.length]"
                                            placeholder="Details of Similar Incident's History..."></textarea>
                                    </div>
                                    <div class="d-inline-block col col-lg-2 float-right mt-auto">
                                        <button (click)="saveReviewField(reviewForm, modalLoader, 'similar_incidents', ir_row)"
                                            type="button" class="btn mt-5 btn-outline-primary float-right"
                                            [disabled]="!similarIncidents.value">Save</button>
                                    </div>
                                </div>
                                <div class="list-unstyled" *ngIf="ir_row?.similar_incidents?.length">
                                    <ng-template ngFor let-comment [ngForOf]="ir_row?.similar_incidents | reverse" let-i="index">
                                        <div *ngIf="comment?.comment">
                                            <div class="d-flex m-2">
                                                <div class="col-10 p-0 d-inline-block">
                                                    <span [innerText]="comment.comment"></span> -
                                                    <small class="text-black-50">{{ comment.user_name }}, {{ dayjs(+comment.added_at).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}</small>
                                                </div>
                                                <ng-container *ngIf="comment.user_ref == authUser$.id">
                                                    <div class="d-flex col-2 mt-auto">
                                                        <button class="btn p-0 mx-2" (click)="editCommentModal(comment, i, 'similar_incidents')"><i class="fas fa-edit text-color-yellow"></i></button>
                                                        <button class="btn p-0 mx-2" (click)="deleteCommentWarningConfirm(i, 'similar_incidents')">
                                                            <span class="material-symbols-outlined text-color-red">
                                                                delete
                                                            </span>
                                                        </button>
                                                    </div>
                                                </ng-container>
                                            </div>
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
                <ngb-accordion>
                    <ngb-panel id="toggle-5">
                        <ng-template ngbPanelHeader>
                            <button ngbPanelToggle class="btn p-0">
                                <i class="fas fa-angle-down"></i>
                                <strong> 6. Immediate Cause</strong>
                            </button>
                            <div class="float-right font-italic"><small>{{ ir_row?.immediate_causes?.length || 'No' }} Comments added</small></div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div class="overflow-auto p-4">
                                <div class="col-10 p-0">
                                    <ng-template ngFor let-opt [ngForOf]="causeOptSelection['immediate_causes'] | reverse" let-i="index">
                                        <div class="mb-2 d-flex">
                                            <div class="p-0 d-inline-block col-5">
                                                <ng-select class="dropdown-list" appendTo="body" placeholder="Select Category" [items]="causeOptions['immediate_causes']"
                                                    [(ngModel)]="opt.head" (change)="headSelected('immediate_causes',i)"
                                                    name="immediate_cause_head_{{i}}" bindLabel="title" bindValue="title" required class="ir_cause_opts">
                                                </ng-select>
                                            </div>
                                            <div class="pr-0 d-inline-block col-6" *ngIf="opt.head">
                                                <ng-select class="dropdown-list" appendTo="body" [(ngModel)]="opt.subhead" [ngClass]="'custom_select'" placeholder="Select Subcategory"
                                                    (change)="subHeadSelected('immediate_causes', i)"
                                                    name="immediate_cause_subhead_{{i}}" required class="ir_cause_opts">
                                                    <ng-option *ngFor="let t of causeSubOpts['immediate_causes'][opt.head]" [value]="t">{{ t }}</ng-option>
                                                </ng-select>
                                            </div>
                                            <span class="material-symbols-outlined float-right text-danger cursor-pointer" *ngIf="i!=0" (click)="removeCauseRow('immediate_causes', i)">
                                                delete
                                            </span>
                                        </div>
                                    </ng-template>
                                </div>
                                <div class="row mb-2 mx-0">
                                    <div class="col-10 p-0 d-inline-block">
                                        <textarea class="form-control" name="immediate_causes" rows="5" #immediateCause="ngModel"
                                            [ngModel]="ir_row.immediate_causes[ir_row?.immediate_causes?.length]"
                                            placeholder="Details of Immediate Cause..."></textarea>
                                    </div>
                                    <div class="d-inline-block col col-lg-2 float-right mt-auto">
                                        <button (click)="saveReviewField(reviewForm, modalLoader, 'immediate_causes', ir_row)"
                                            type="button" [disabled]="!immediateCause.value" class="btn mt-5 btn-outline-primary float-right">Save</button>
                                    </div>
                                </div>
                                <div>
                                    <div class="list-unstyled" *ngIf="ir_row?.immediate_causes?.length">
                                        <ng-template ngFor let-comment [ngForOf]="ir_row?.immediate_causes | reverse" let-i="index">
                                            <div *ngIf="comment?.comment || comment?.head">
                                                <div class="d-flex m-2">
                                                    <div class="col-10 p-0 d-inline-block">
                                                        <span [innerText]="(comment?.head)? comment?.head+': '+comment?.subhead : comment.comment"></span> -
                                                        <small class="text-black-50">{{ comment.user_name }}, {{ dayjs(+comment.added_at).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}</small>
                                                    </div>
                                                    <ng-container *ngIf="comment.user_ref == authUser$.id">
                                                        <div class="d-flex col-2 mt-auto">
                                                            <button class="btn p-0 mx-2" (click)="editCommentModal(comment, i, 'immediate_causes')"><i class="fas fa-edit text-color-yellow"></i></button>
                                                            <button class="btn p-0 mx-2" (click)="deleteCommentWarningConfirm(i, 'immediate_causes')">
           <span class="material-symbols-outlined text-color-red">
                                                                    delete
                                                                </span>
                                                            </button>
                                                        </div>
                                                    </ng-container>
                                                </div>
                                            </div>
                                        </ng-template>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
                <ngb-accordion>
                    <ngb-panel id="toggle-6">
                        <ng-template ngbPanelHeader>
                            <button ngbPanelToggle class="btn p-0">
                                <i class="fas fa-angle-down"></i>
                                <strong> 7. Underlying Cause</strong>
                            </button>
                            <div class="float-right font-italic"><small>{{ ir_row?.underlying_causes?.length || 'No' }} Comments added</small></div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div class="overflow-auto p-4">
                                <div class="col-10 p-0">
                                    <ng-template ngFor let-opt [ngForOf]="causeOptSelection['underlying_causes'] | reverse" let-i="index">
                                        <div class="mb-2 d-flex">
                                            <div class="p-0 d-inline-block col-5">
                                                <ng-select class="dropdown-list" appendTo="body" placeholder="Select Category" [items]="causeOptions['underlying_causes']"
                                                    [(ngModel)]="opt.head" (change)="headSelected('underlying_causes',i)"
                                                    name="underlying_causes_head_{{i}}" bindLabel="title" bindValue="title" required class="ir_cause_opts">
                                                </ng-select>
                                            </div>
                                            <div class="pr-0 d-inline-block col-6" *ngIf="opt.head">
                                                <ng-select class="dropdown-list" appendTo="body" [(ngModel)]="opt.subhead" [ngClass]="'custom_select'" placeholder="Select Subcategory"
                                                    (change)="subHeadSelected('underlying_causes', i)"
                                                    name="underlying_causes_subhead_{{i}}" required class="ir_cause_opts">
                                                    <ng-option *ngFor="let t of causeSubOpts['underlying_causes'][opt.head]" [value]="t">{{ t }}</ng-option>
                                                </ng-select>
                                            </div>
                                            <span class="material-symbols-outlined float-right text-danger cursor-pointer" *ngIf="i!=0" (click)="removeCauseRow('underlying_causes', i)">
                                                delete
                                            </span>
                                        </div>
                                    </ng-template>
                                </div>
                                <div class="row mb-2 mx-0">
                                    <div class="col-10 p-0 d-inline-block">
                                        <textarea class="form-control" name="underlying_causes" rows="5" #underlyingCause="ngModel"
                                            [ngModel]="ir_row.underlying_causes[ir_row?.underlying_causes?.length]"
                                            placeholder="Details of Underlying Cause..."></textarea>
                                    </div>
                                    <div class="d-inline-block col col-lg-2 float-right mt-auto">
                                        <button (click)="saveReviewField(reviewForm, modalLoader, 'underlying_causes', ir_row)"
                                            type="button" [disabled]="!underlyingCause.value" class="btn mt-5 btn-outline-primary float-right">Save</button>
                                    </div>
                                </div>
                                <div>
                                    <div class="list-unstyled" *ngIf="ir_row?.underlying_causes?.length">
                                        <ng-template ngFor let-comment [ngForOf]="ir_row?.underlying_causes | reverse" let-i="index">
                                            <div *ngIf="comment?.comment || comment?.head">
                                                <div class="d-flex m-2">
                                                    <div class="col-10 p-0 d-inline-block">
                                                        <span [innerText]="(comment?.head)? comment?.head+': '+comment?.subhead : comment.comment"></span> -
                                                        <small class="text-black-50">{{ comment.user_name }} {{ dayjs(+comment.added_at).format(AppConstant.dateTime_MMM_D__YY_at_H_mm) }}</small>
                                                    </div>
                                                    <ng-container *ngIf="comment.user_ref == authUser$.id">
                                                        <span class="d-flex col-2 mt-auto">
                                                            <button class="btn p-0 mx-2" (click)="editCommentModal(comment, i, 'underlying_causes')"><i class="fas fa-edit text-color-yellow"></i></button>
                                                            <button class="btn p-0 mx-2" (click)="deleteCommentWarningConfirm(i, 'underlying_causes')">
                                                                <span class="material-symbols-outlined text-color-red">
                                                                    delete
                                                                </span>
                                                            </button>
                                                        </span>
                                                    </ng-container>
                                                </div>
                                            </div>
                                        </ng-template>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
                <ngb-accordion>
                    <ngb-panel id="toggle-7">
                        <ng-template ngbPanelHeader>
                            <button ngbPanelToggle class="btn p-0">
                                <i class="fas fa-angle-down"></i>
                                <strong> 8. Root Cause</strong>
                            </button>
                            <div class="float-right font-italic"><small>{{ ir_row?.root_causes?.length || 'No' }} Comments added</small></div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div class="overflow-auto p-4">
                                <div class="col-10 p-0">
                                    <ng-template ngFor let-opt [ngForOf]="causeOptSelection['root_causes'] | reverse" let-i="index">
                                        <div class="mb-2 d-flex">
                                            <div class="p-0 d-inline-block col-5">
                                                <ng-select class="dropdown-list" appendTo="body" placeholder="Select Category" [items]="causeOptions['root_causes']"
                                                    [(ngModel)]="opt.head" (change)="headSelected('root_causes',i)"
                                                    name="root_causes_head_{{i}}" bindLabel="title" bindValue="title" required class="ir_cause_opts">
                                                </ng-select>
                                            </div>
                                            <div class="pr-0 d-inline-block col-6" *ngIf="opt.head">
                                                <ng-select class="dropdown-list" appendTo="body" [(ngModel)]="opt.subhead" [ngClass]="'custom_select'" placeholder="Select Subcategory"
                                                    (change)="subHeadSelected('root_causes', i)"
                                                    name="root_causes_subhead_{{i}}" required class="ir_cause_opts">
                                                    <ng-option *ngFor="let t of causeSubOpts['root_causes'][opt.head]" [value]="t">{{ t }}</ng-option>
                                                </ng-select>
                                                <span class="material-symbols-outlined float-right text-danger cursor-pointer" *ngIf="i!=0" (click)="removeCauseRow('root_causes', i)">
                                                    delete
                                                </span>
                                            </div>
                                        </div>
                                    </ng-template>
                                </div>
                                <div class="row mb-2 mx-0">
                                    <div class="col-10 p-0 d-inline-block">
                                        <textarea class="form-control" name="root_causes" rows="5" #rootCause="ngModel"
                                            [ngModel]="ir_row.root_causes[ir_row?.root_causes?.length]"
                                            placeholder="Details of Root Cause..."></textarea>
                                    </div>
                                    <div class="d-inline-block col col-lg-2 float-right mt-auto">
                                        <button (click)="saveReviewField(reviewForm, modalLoader, 'root_causes', ir_row)"
                                            type="button" [disabled]="!rootCause.value" class="btn mt-5 btn-outline-primary float-right">Save</button>
                                    </div>
                                </div>
                                <div>
                                    <div class="list-unstyled" *ngIf="ir_row?.root_causes?.length">
                                        <ng-template ngFor let-comment [ngForOf]="ir_row?.root_causes | reverse" let-i="index">
                                            <div *ngIf="comment?.comment || comment?.head">
                                                <div class="d-flex m-2">
                                                    <div class="col-10 p-0 d-inline-block">
                                                        <span [innerText]="(comment?.head)? comment?.head+': '+comment?.subhead : comment.comment"></span> -
                                                        <small class="text-black-50">{{ comment.user_name }} {{ dayjs(+comment.added_at).format(AppConstant.dateTime_MMM_D__YY_at_H_mm) }}</small>
                                                    </div>
                                                    <ng-container *ngIf="comment.user_ref == authUser$.id">
                                                        <span class="d-flex col-2 mt-auto">
                                                            <button class="btn p-0 mx-2" (click)="editCommentModal(comment, i, 'root_causes')"><i class="fas fa-edit text-color-yellow"></i></button>
                                                            <button class="btn p-0 mx-2" (click)="deleteCommentWarningConfirm(i, 'root_causes')">
                                                                <span class="material-symbols-outlined text-color-red">
                                                                    delete
                                                                </span>
                                                            </button>
                                                        </span>
                                                    </ng-container>
                                                </div>
                                            </div>
                                        </ng-template>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
                <ngb-accordion>
                    <ngb-panel id="toggle-8">
                        <ng-template ngbPanelHeader>
                            <button ngbPanelToggle class="btn p-0">
                                <i class="fas fa-angle-down"></i>
                                <strong> 9. Conclusions</strong>
                            </button>
                            <div class="float-right font-italic"><small>{{ ir_row?.incident_conclusions?.length || 'No' }} Comments added</small></div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div class="overflow-auto p-4">
                                <div class="row mb-2 mx-0">
                                    <div class="col-10 p-0 d-inline-block">
                                        <textarea class="form-control" name="incident_conclusions" rows="5"
                                            #incidentConclusions="ngModel"
                                            [ngModel]="ir_row.incident_conclusions[ir_row?.incident_conclusions?.length]"
                                            placeholder="Details of Incident Conclusion..."></textarea>
                                    </div>
                                    <div class="d-inline-block col col-lg-2 float-right mt-auto">
                                        <button (click)="saveReviewField(reviewForm, modalLoader, 'incident_conclusions', ir_row)"
                                            type="button" class="btn mt-5 btn-outline-primary float-right"
                                            [disabled]="!incidentConclusions.value">Save</button>
                                    </div>
                                </div>
                                <div class="list-unstyled" *ngIf="ir_row?.incident_conclusions?.length">
                                    <ng-template ngFor let-comment [ngForOf]="ir_row?.incident_conclusions | reverse" let-i="index">
                                        <div *ngIf="comment?.comment">
                                            <div class="d-flex m-2">
                                                <div class="col-10 p-0 d-inline-block">
                                                    <span [innerText]="comment.comment"></span> -
                                                    <small class="text-black-50">{{ comment.user_name }}, {{ dayjs(+comment.added_at).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}</small>
                                                </div>
                                                <ng-container *ngIf="comment.user_ref == authUser$.id">
                                                    <div class="d-flex col-2 mt-auto">
                                                        <button class="btn p-0 px-1" (click)="editCommentModal(comment, i, 'incident_conclusions')"><i class="fas fa-edit text-color-yellow"></i></button>
                                                        <button class="btn p-0 px-1" (click)="deleteCommentWarningConfirm(i, 'incident_conclusions')">
                                                           <span class="material-symbols-outlined text-color-red">
                                                                delete
                                                            </span>
                                                        </button>
                                                    </div>
                                                </ng-container>
                                            </div>
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
                <ngb-accordion>
                    <ngb-panel id="toggle-9">
                        <ng-template ngbPanelHeader>
                            <button ngbPanelToggle class="btn p-0">
                                <i class="fas fa-angle-down"></i>
                                <strong> 10. Recommendations</strong>
                            </button>
                            <div class="float-right font-italic"><small>{{ ir_row?.incident_recommendations?.length || 'No' }} Comments added</small></div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div class="overflow-auto p-4">
                                <div class="row mb-2 mx-0">
                                    <div class="col-10 d-inline-block">
                                        <textarea class="form-control" name="incident_recommendations" rows="5"
                                            #incidentRecommendations="ngModel"
                                            [ngModel]="ir_row.incident_recommendations[ir_row?.incident_recommendations?.length]"
                                            placeholder="Details of Recommendation..."></textarea>
                                    </div>
                                    <div class="d-inline-block col col-lg-2 float-right mt-auto">
                                        <button (click)="saveReviewField(reviewForm, modalLoader, 'incident_recommendations', ir_row)"
                                            type="button" class="btn mt-5 btn-outline-primary float-right"
                                            [disabled]="!incidentRecommendations.value">Save</button>
                                    </div>
                                </div>
                                <div class="list-unstyled" *ngIf="ir_row?.incident_recommendations?.length">
                                    <ng-template ngFor let-comment [ngForOf]="ir_row?.incident_recommendations | reverse" let-i="index">
                                        <div *ngIf="comment?.comment">
                                            <div class="d-flex m-2">
                                                <div class="col-10 p-0 d-inline-block">
                                                    <span [innerText]="comment.comment"></span> -
                                                    <small class="text-black-50">{{ comment.user_name }}, {{ dayjs(+comment.added_at).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}</small>
                                                </div>
                                                <ng-container *ngIf="comment.user_ref == authUser$.id">
                                                    <div class="d-flex col-2 mt-auto">
                                                        <button class="btn p-0 px-1" (click)="editCommentModal(comment, i, 'incident_recommendations')"><i class="fas fa-edit text-color-yellow"></i></button>
                                                        <button class="btn p-0 px-1" (click)="deleteCommentWarningConfirm(i, 'incident_recommendations')">
                                                            <span class="material-symbols-outlined text-color-red">
                                                                delete
                                                            </span>
                                                        </button>
                                                    </div>
                                                </ng-container>
                                            </div>
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
                <ngb-accordion>
                    <ngb-panel id="toggle-10">
                        <ng-template ngbPanelHeader>
                            <button ngbPanelToggle class="btn p-0">
                                <i class="fas fa-angle-down"></i>
                                <strong> 11. Attachments</strong>
                            </button>
                            <div class="float-right font-italic"><small>{{ ir_row?.review_photo_ids?.length || 'No' }} items added</small></div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div class="p-4 ml-4 mr-4">
                                <div class="mb-2 p-0 d-inline-block col-10">
                                    <file-uploader-v2 [disabled]="false" [init]="reviewImage"
                                        [category]="'incident-review-photo'"
                                        (uploadDone)="reviewPhotoUploadDone($event)"
                                        [allowedMimeType]="['video/mp4', 'video/quicktime', 'application/pdf', 'application/x-pdf', 'image/jpeg', 'image/jpg', 'image/png']"
                                        [showHyperlink]="(reviewImage.file_mime && ['application/pdf', 'video/mp4', 'video/quicktime'].includes(reviewImage.file_mime)) ? true : false"
                                        [showFileName]="(reviewImage.file_mime && ['application/pdf', 'video/mp4', 'video/quicktime'].includes(reviewImage.file_mime)) ? true : false"
                                        (deleteFileDone)="fileDeleteDone($event)"
                                        [showDeleteBtn]="true" #imgUploader
                                        [dragnDropTxt]="'Drag and drop attachment here'"
                                        [hasImgAndDoc]="true"
                                    >
                                    </file-uploader-v2>
                                </div>
                                <div class="mb-2 row mx-0">
                                    <div class="col-10 p-0 d-inline-block">
                                        <textarea class="form-control" name="attachment_description" rows="5"
                                            #attachmentDescription="ngModel"
                                            [ngModel]="ir_row.review_photo_ids[ir_row?.review_photo_ids?.length]"
                                            placeholder="Attachment Description..."></textarea>
                                    </div>
                                    <div class="d-inline-block col col-lg-2 float-right mt-auto">
                                        <button (click)="saveAttachmentFiles(reviewForm, modalLoader, ir_row, imgUploader)"
                                        type="button" class="btn mt-5 btn-outline-primary float-right"
                                        [disabled]="!attachmentDescription.value || !reviewImage.id">Save</button>
                                    </div>
                                </div>
                                <div class="list-unstyled" *ngIf="ir_row?.review_photo_ids?.length">
                                    <ng-template ngFor let-comment [ngForOf]="ir_row?.review_photo_ids | reverse" let-i="index">
                                        <div class="d-flex m-2">
                                            <div class="col-10 p-0 d-inline-block">
                                                <span [innerText]="comment.description"></span><span>: <a target="_blank" [href]="comment.file?.file_url">{{ comment.file?.name }}</a></span> -
                                                <small class="text-black-50">{{ comment.user_name }}, {{ dayjs(+comment.added_at).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}</small>
                                            </div>
                                            <ng-container *ngIf="comment.user_ref == authUser$.id">
                                                <div class="d-flex col-2 mt-auto">
                                                    <button class="btn p-0 px-1" (click)="deleteCommentWarningConfirm(i, 'review_photo_ids')">
                                                        <span class="material-symbols-outlined text-color-red">
                                                            delete
                                                        </span>
                                                    </button>
                                                </div>
                                            </ng-container>
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
                <ngb-accordion class="actionAccordion">
                    <ngb-panel id="toggle-11">
                        <ng-template ngbPanelHeader>
                            <button ngbPanelToggle class="btn p-0">
                                <div class="float-left">
                                    <i class="fas fa-angle-down mr-1"></i>
                                    <strong>12. Actions</strong>
                                </div>
                            </button>
                            <div class="float-right font-italic"><small>{{ getActionsStatus(ir_row?.incident_actions, 'text') }}</small></div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <div class="overflow-auto mt-2">
                                <ng-container *ngFor="let action of ir_row['incident_actions']; trackBy : trackByRowIndex; let i = index;" >
                                    <div class="card">
                                        <div class="card-header">
                                            <strong>Action {{ i+1 }}</strong>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-bordered mb-0">
                                                <tbody>
                                                    <tr>
                                                        <td style="width: 15%" class="px-2 py-2 fw-500">Category</td>
                                                        <td style="width: 85%" class="px-2 py-1">{{ action.category }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td style="width: 15%" class="px-2 py-2 fw-500">Priority</td>
                                                        <td style="width: 85%" class="px-2 py-1">
                                                            <div [ngClass]="action.priority == 'High' ? 'text-color-red' : action.priority == 'Medium' ? 'text-color-yellow' : 'text-color-green'">
                                                                <span><b>{{ action.priority }}</b></span>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td style="width: 15%" class="px-2 py-2 fw-500">Status</td>
                                                        <td style="width: 85%" class="px-2 py-1">
                                                            <div [ngClass]="action.close_out && action.close_out.close_out_at ? 'text-color-green' : 'text-color-red'">
                                                                <span *ngIf="action.close_out && action.close_out.close_out_at"><b>Closed</b></span>
                                                                <span *ngIf="action.close_out && !action.close_out.close_out_at"><b>Open</b></span>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td style="width: 15%" class="px-2 py-2 fw-500">Tagged User</td>
                                                        <td style="width: 85%" class="px-2 py-1">
                                                            <span>{{ action.tag_user_ref.name }}</span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td style="width: 15%" class="px-2 py-2 fw-500">Due Date</td>
                                                        <td style="width: 85%" class="px-2 py-1">
                                                            <div  [ngClass]="{'text-color-red': (!action.close_out.close_out_at && dayjs().valueOf() > action.due_date)}">
                                                                <span>{{ dayjs(action.due_date).format(AppConstant.defaultDateFormat) }}</span>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr *ngIf="!action.close_out || !action.close_out.close_out_at">
                                                        <td style="width: 15%" class="px-2 py-2 fw-500">Actions</td>
                                                        <td class="vertical-align-middle px-2 py-1" style="width: 85%">
                                                            <span>{{action?.action_detail}}</span>
                                                            <span class="text-brandeis-blue cursor-pointer align-middle float-right fw-500" (click)="closeOutActionModal(i)">
                                                                Close Out
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <ng-container  *ngIf="action.close_out && action.close_out.close_out_at">
                                                        <tr>
                                                            <td style="width: 15%" class="px-2 py-2 fw-500">Closed Out By</td>
                                                            <td style="width: 85%" class="px-2 py-1">{{ action?.close_out?.reviewed_by }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 15%" class="px-2 py-2 fw-500">Closeout Date</td>
                                                            <td style="width: 85%" class="px-2 py-1">{{ dayjs(+action?.close_out?.close_out_at).format(AppConstant.defaultDateFormat) }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 15%" class="px-2 py-2 fw-500">Closeout Details</td>
                                                            <td style="width: 85%" class="px-2 py-1">{{ action?.close_out?.details }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 15%" class="px-2 py-2 fw-500">Photos</td>
                                                            <td style="width: 85%; overflow: hidden;" class="px-2 py-1">
                                                                <pop-up-image-viewer [updateStyle]="{'max-height.px': 150, 'max-width.px': 150}" [alt]="'Close Out Image'" [imgArray]="action?.close_out?.images || []"></pop-up-image-viewer>
                                                            </td>
                                                        </tr>
                                                    </ng-container>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </ng-container>

                                <button class="btn btn-outline-brandeis-blue float-right" (click)="addAction()">
                                    <i class="fa fa-plus" aria-hidden="true" style="font-size: 11px;"></i> Add Action
                                </button>
                            </div>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
            </form>
        </div>
    </ng-template>
</app-generic-modal>

<app-generic-modal #viewIncidentHtml [genericModalConfig]="viewIncidentConfig">
    <ng-container *ngTemplateOutlet="frameTemplate"></ng-container>
    <ng-template #frameTemplate>
        <iframe id="incidentReportFrame" class="border-0" style="width: 100%;height: 100%;position: absolute;"></iframe>
    </ng-template>
    <block-loader alwaysInCenter="true" [show]="iframe_content_loading"></block-loader>
</app-generic-modal>

<share-tool-report-to-email
    [projectId]="projectId" [employerId]="employerId" #shareIncidentReportModal
    [tool_name]="'Share Incident'" (onSave)="shareIncidentReport($event)">
</share-tool-report-to-email>

<app-generic-modal #closeOutHtml [genericModalConfig]="closeOutModalRefConfig">
    <ng-container *ngTemplateOutlet="closeOut"></ng-container>
    <ng-template #closeOut>
            <p class="text-center">
                <span *ngIf="(getPendingIncidentActions(ir_row?.incident_actions) > 0); else warnBlock">
                    Please close out all actions for this incident to enable incident closeout.
                </span>
                <ng-template #warnBlock><span>Are you sure you would like to close out the incident?</span></ng-template>
            </p>
        <ng-template #warnActionBlock>
                <button type="button" class="btn btn-outline-primary" (click)="c('Ok click')">Ok</button>
        </ng-template>
    </ng-template>
</app-generic-modal>
<app-generic-modal #closeOutFormHtml [genericModalConfig]="closeOutFormModalRefConfig">
    <ng-container *ngTemplateOutlet="closeOuttemplate"></ng-container>
    <ng-template #closeOuttemplate>
            <p class="text-center">Incident #{{ ir_row?.record_ref }}</p>
            <form novalidate #closeOutForm="ngForm">
                <div class="mb-2">
                    <p class="mb-1 font-weight-bold">Comments:</p>
                    <input type="hidden" name="ir_id" id="ir_id" [(ngModel)]="ir_row.id"/>
                    <textarea class="form-control" name="closeout_comment" rows="5"
                            [(ngModel)]="ir_row.closeout_comment"
                            ng-value="ir_row.closeout_comment"
                            placeholder="Closeout details"
                            #closeOutComment="ngModel" required></textarea>
                    <div class="alert alert-danger" [hidden]="(!ir_row?.closeout_comment || closeOutComment.valid)">Closeout detail is required</div>
                </div>
                <div class="form-group row" *ngIf="showSignaturePad">
                    <label class="font-weight-bold col-sm-12 col-form-label form-control-label">Signature:</label>
                    <div class="col-md-12 form-inline">
                        <signature-pad-selector
                            [height]="120"
                            [width]="465"
                            (signChanged)="saveSignature($event)"
                            (clear)="clearSignature()"
                        ></signature-pad-selector>
                    </div>
                </div>
            </form>
        <block-loader [show]="closeOutLoader" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>
    </ng-template>
</app-generic-modal>

<app-generic-modal #editCommentFormHtml [genericModalConfig]="editCommentFormModalRefConfig">
    <ng-container *ngTemplateOutlet="editCommentTemplate"></ng-container>
    <ng-template #editCommentTemplate>
            <form novalidate #editCommentForm="ngForm">
            <div *ngIf="showEditForm">
                <div class="mb-2" *ngIf="editCommentData.head && editCommentField != 'relevant_personnel_user_refs'">
                    <div class="p-0 d-inline-block col-5">
                        <ng-select placeholder="Select Category" [items]="causeOptions[editCommentField]"
                            [(ngModel)]="editCommentData.head" (change)="editHeadSelected()"
                            name="cause_head" bindLabel="title" bindValue="title" required class="ir_cause_opts">
                        </ng-select>
                    </div>
                    <div class="pr-0 d-inline-block col-6">
                        <ng-select placeholder="Select Subcategory" [(ngModel)]="editCommentData.subhead"
                            name="cause_subhead" required class="ir_cause_opts">
                            <ng-option *ngFor="let t of causeSubOpts[editCommentField][editCommentData.head]" [value]="t">{{ t }}</ng-option>
                        </ng-select>
                    </div>
                </div>
                <div class="mb-2" *ngIf="editCommentField == 'relevant_personnel_user_refs'">
                    <div class="p-0 d-inline-block col-5">
                        <inducted-user-selector
                            [required]="true"
                            placeholder="Select Personnel"
                            searchMessage="Type to search Personnel"
                            [selection]="editCommentData.id"
                            extraFields="employment"
                            [name]="'cause_head'"
                            [projectId]="ir_row?.project_ref?.id || ir_row?.project_ref"
                            (selectionChanged)="editPersonnelSelected($event)"
                        ></inducted-user-selector>
                    </div>
                    <div class="pr-0 d-inline-block col-6">
                        <ng-select placeholder="Select Job Role" [(ngModel)]="editCommentData.subhead"
                            name="cause_subhead" required class="ir_cause_opts">
                            <ng-option *ngFor="let t of jobRoleList" [value]="t.name">{{ t.name }}</ng-option>
                        </ng-select>
                    </div>
                </div>
                <div class="mb-2" *ngIf="editCommentData.comment">
                    <div class="p-0 d-inline-block col-10">
                        <textarea class="form-control" [name]="editCommentField" rows="5"
                            #commentText="ngModel" [(ngModel)]="editCommentData.comment" required></textarea>
                        <div class="alert alert-danger" [hidden]="(!editCommentData?.comment || commentText.valid)">Comment is required.</div>
                    </div>
                    </div>
                </div>
            </form>
    </ng-template>
</app-generic-modal>


<app-generic-modal #addActionHtml [genericModalConfig]="addActionModalRefConfig">
    <ng-container *ngTemplateOutlet="addActiontemplate"></ng-container>
    <ng-template #addActiontemplate>
            <form novalidate #addActionForm="ngForm">
                <div class="form-group">
                    <label class="d-inline-block">Category: <small class="required-asterisk">*</small></label>
                    <ng-select class="dropdown-list" appendTo="body" name="category" #categorySelector="ngModel" [(ngModel)]="newAction.category"
                            required style="width: 100%;"
                            placeholder="Select Category">
                        <ng-option *ngFor="let category of actionCategories?.value" [value]="category?.label">{{category?.label}}</ng-option>
                    </ng-select>
                    <div class="alert alert-danger" [hidden]="(newAction?.category || addActionForm.valid)">Please select an category.</div>
                </div>
                <div class="form-group">
                    <label>Action: <small class="required-asterisk">*</small></label>
                    <div class="input-group mb-3">
                        <textarea type="text" class="form-control" rows="5" name="action" [(ngModel)]="newAction.action_detail" required></textarea>
                    </div>
                </div>
                <div class="form-group">
                    <label class="d-inline-block">Tag User: <small class="required-asterisk">*</small></label>
                    <inducted-user-selector
                        placeholder="Select a user"
                        [required]="true"
                        [name]="'tag_user_ref'"
                        [projectId]="ir_row?.project_ref?.id || ir_row?.project_ref"
                        (selectionChanged)="onTagUserRefSelected($event)"
                    ></inducted-user-selector>
                </div>
                <div class="form-group">
                    <label class="d-inline-block">Priority: <small class="required-asterisk">*</small></label>
                    <ng-select [items]="actionPriorities" bindLabel="value" bindValue="key" class="custom-select dropdown-list" appendTo="body" [name]="prioritySelector" #prioritySelector="ngModel" [(ngModel)]="newAction.priority"
                           required [ngStyle]="{'width': '100%', 'color': (newAction.priority == 'High') ? '#FE8282' : ((newAction.priority == 'Medium') ? '#ffa500a6' : '#92F282') }"
                           placeholder="Select Priority">
                </ng-select>
                    <div class="alert alert-danger" [hidden]="(newAction?.priority || addActionForm.valid)">Please select priority.</div>
                </div>
                <label class="d-inline-block">Due Date: <small class="required-asterisk ">*</small></label>
                <div class="input-group">
                        <input class="form-control" placeholder="dd-mm-yyyy" readonly required
                            name="dueDate" [(ngModel)]="newAction.due_date" ngbDatepicker
                            #dd="ngbDatepicker" ng-value="newAction.due_date"
                            [minDate]="minDate">
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary calendar" (click)="dd.toggle()" type="button">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </div>
                        <div class="alert alert-danger" [hidden]="(newAction?.due_date || addActionForm.valid)">Please select a valid due date</div>
                    </div>
            </form>
    </ng-template>
</app-generic-modal>

<app-generic-modal #closeOutActionHtml [genericModalConfig]="closeOutActionModalRefConfig">
    <ng-container *ngTemplateOutlet="closeOutActiontemplate"></ng-container>
    <ng-template #closeOutActiontemplate>
        <div class="">
            <div class="text-left">
                <p class="mb-1"><strong>Category:</strong> {{ incidentAction?.category }}</p>
                <p class="mb-1"><strong>Action:</strong> {{ incidentAction?.action_detail }}</p>
                <p class="mb-1"><strong>Tagged User:</strong> {{ incidentAction?.tag_user_ref?.name }}</p>
                <p class="mb-1"><strong>Priority:</strong> {{ incidentAction?.priority }}</p>
                <p class="mb-1"><strong>Due Date:</strong> {{ dayjs(+incidentAction.due_date).format(AppConstant.defaultDateFormat) }}</p>
            </div>
            <hr>
            <div class="mb-2 mt-1">
                <p class="mb-1"><strong>Closeout</strong> <small class="required-asterisk ">*</small></p>
                <textarea class="form-control" name="closeout_detail"
                        [(ngModel)]="closeout_action_detail"
                        ng-value="closeout_detail"
                        placeholder="Closeout detail"
                        #closeOutDetail="ngModel" required></textarea>
                <div class="alert alert-danger" [hidden]="closeOutDetail.valid">Closeout detail is required</div>
            </div>
            <div class="col-md-12 p-0 mt-3">
                <p class="mb-1"><strong>Upload Files</strong></p>
            </div>
            <div class="col-md-12 p-0 mb-4">
                <div class="col-md-12 flex-grow-1 p-0" *ngFor="let c of closeout_action_images">
                    <file-uploader-v2
                        [disabled]="false"
                        [init]="c"
                        [category]="'close-out'"
                        (uploadDone)="mediaUploadDone($event)"
                        [allowedMimeType]="allowedMime"
                        [showHyperlink]="(c.file_mime && c.file_mime == 'application/pdf') ? 1 : 0"
                        [showFileName]="(c.file_mime && c.file_mime == 'application/pdf') ? 1 : 0"
                        [dragnDropTxt]="'Drag and drop image or pdf here'"
                        [hasImgAndDoc]="true"
                        (deleteFileDone)="fileDeleteDone($event, 'closeout_action_images')"
                        [showDeleteBtn]="true" [multipleUpload]="true"
                    >
                    </file-uploader-v2>
                </div>
            </div>
        </div>
    </ng-template>
</app-generic-modal>

<!-- here is modal newreportform -->

<app-generic-modal #newReportFormHtml [genericModalConfig]="newReportConfig">
    <ng-container *ngTemplateOutlet="newReportFormTemplate"></ng-container>
    <ng-template #newReportFormTemplate>
        <div class="col-md-12 m-0 p-0 d-inline-block px-5 mt-4 font-weight-bold position-relative" [ngClass]="{'mb-3': activeStep === 0}">
            <label class="float-left" *ngIf="!incidentReport.incident_type else stepSelector"> Incident Details</label>
            <ng-template #stepSelector>
                <ng-select 
                    class="ir-step-selector"
                    [items]="meta_incident_type_steps"
                    [searchable]="false"
                    [clearable]="false"
                    [ngModel]="{}"
                >
                    <ng-template ng-label-tmp let-item="item" let-clear="clear">
                        <span style="display:block;">{{meta_incident_type_steps[activeStep]}} </span>
                        <span class="float-right">Step {{activeStep+1}}/{{meta_incident_type_steps.length}}</span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-index="index">
                        <a [ngClass]="{
                            'dropdown-item cursor-pointer py-2 pl-0': true,
                            'font-weight-bold': (index == activeStep),
                            'disabled cursor-default font-weight-light':  index > getEnabledStepLimit()
                        }"
                        href="javascript:void(0)" 
                        (click)="moveStep(index, item)">
                            {{item}}
                        </a>
                    </ng-template>
                </ng-select>
            </ng-template>
        </div>
        <div  class="report-form">
            <form novalidate #incidentDetailsP1="ngForm" class="px-5 py-2" [ngClass]="{'pb-4 mb-3': !incidentReport?.incident_type}">
                <fieldset *ngIf="activeStep == 0">
                    <div class="form-group">
                        <label class="d-inline-block">Incident Type: <small class="required-asterisk">*</small></label>
                        <ng-select [disabled]="editReportMode" class="dropdown-list" appendTo="body" name="incidentType" #incidentType="ngModel" [(ngModel)]="incidentReport.incident_type"
                                required style="width: 100%;" placeholder="Select Type"
                                (change)="setIncidentTypeMetaData()"
                        >
                            <ng-option *ngFor="let incidentType of incidentReportMetaData.incident_type" [value]="incidentType">{{ incidentType }}</ng-option>
                        </ng-select>
                    </div>
                    <ng-container *ngIf="incidentReport.incident_type">                    
                        <div class="form-group">
                            <label class="d-inline-block">Date of Incident: <small class="required-asterisk ">*</small></label>
                            <div class="input-group col-sm-12 p-0">
                                <input class="form-control" placeholder="dd-mm-yyyy" readonly
                                    name="incident_date" [(ngModel)]="incidentReport._incident_date" ngbDatepicker
                                    #incd="ngbDatepicker" ng-value="incidentReport._incident_date"
                                    required [maxDate]="maxDate" placement="bottom-left">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary calendar" (click)="incd.toggle()" type="button">
                                        <i class="fa fa-calendar"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
            
                        <div class="form-group pt-2 mb-0">
                            Time of Incident: <small class="required-asterisk ">*</small> <ngb-timepicker #timePicker class="incident-time d-inline-block ml-5" [spinners]="false" [(ngModel)]="incidentReport._incident_time"  name="incident_time" [size]="'small'" required></ngb-timepicker>
                        </div>
                    </ng-container>
                </fieldset>
            </form>
            <div #reportForm [ngClass]="activeStep === 0 ? 'long-form' : 'short-form'">
                <injury-incident
                    #injuryForm
                    *ngIf="incidentReport.incident_type == IncidentType.Injury"
                    [incidentReportMetaData]="incidentReportMetaData"
                    [incidentReport]="incidentReport"
                    [meta_incident_type_steps]="meta_incident_type_steps"
                    [incidentTypeMetaData]="incidentTypeMetaData"
                    [activeStep]="activeStep"
                    [cb]="c"
                    [formatDayJs]="dayjs"
                    [maxDate]="maxDate"
                    [jobRoleList]="jobRoleList"
                    [projectId]="projectId"
                    [projectInfo]="projectInfo"
                    (formsStatus)="updateFormsStatus($event)"
                    (setActiveStep)="updatedActiveStep($event)"
                    (submitInjuryReport)="submitIncidentReport($event)"
                    [changingStep]="changingFormStep"
                    (isValidForm)="newReportConfig.primaryBtnDisabled = $event"
                >
                </injury-incident>

                <health-incident
                #healthIncidentForm
                    *ngIf="incidentReport.incident_type == IncidentType.Health"
                    [incidentReportMetaData]="incidentReportMetaData"
                    [incidentReport]="incidentReport"
                    [meta_incident_type_steps]="meta_incident_type_steps"
                    [incidentTypeMetaData]="incidentTypeMetaData"
                    [activeStep]="activeStep"
                    [cb]="c"
                    [formatDayJs]="dayjs"
                    [maxDate]="maxDate"
                    [jobRoleList]="jobRoleList"
                    [projectId]="projectId"
                    [projectInfo]="projectInfo"
                    (formsStatus)="updateFormsStatus($event)"
                    (setActiveStep)="updatedActiveStep($event)"
                    (submitHealthReport)="submitIncidentReport($event)"
                    [changingStep]="changingFormStep"
                    (isValidForm)="newReportConfig.primaryBtnDisabled = $event"
                >
                </health-incident>

                <road-traffic-incident
                    #roadTrafficForm
                    *ngIf="incidentReport.incident_type == IncidentType.RoadTraffic"
                    [incidentReportMetaData]="incidentReportMetaData"
                    [incidentReport]="incidentReport"
                    [meta_incident_type_steps]="meta_incident_type_steps"
                    [incidentTypeMetaData]="incidentTypeMetaData"
                    [activeStep]="activeStep"
                    [cb]="c"
                    [formatDayJs]="dayjs"
                    [projectInfo]="projectInfo"
                    [maxDate]="maxDate"
                    (formsStatus)="updateFormsStatus($event)"
                    (setActiveStep)="updatedActiveStep($event)"
                    (submitRoadTrafficReport)="submitIncidentReport($event)"
                    [changingStep]="changingFormStep"
                    (isValidForm)="newReportConfig.primaryBtnDisabled = $event"
                >
                </road-traffic-incident>

                <damage-loss-incident
                    #damageLossForm
                    *ngIf="incidentReport.incident_type == IncidentType.DamageOrLoss"
                    [incidentReportMetaData]="incidentReportMetaData"
                    [incidentReport]="incidentReport"
                    [meta_incident_type_steps]="meta_incident_type_steps"
                    [incidentTypeMetaData]="incidentTypeMetaData"
                    [activeStep]="activeStep"
                    [cb]="c"
                    [formatDayJs]="dayjs"
                    [projectInfo]="projectInfo"
                    [maxDate]="maxDate"
                    (formsStatus)="updateFormsStatus($event)"
                    (setActiveStep)="updatedActiveStep($event)"
                    (submitDamageLossIncidentReport)="submitIncidentReport($event)"
                    [changingStep]="changingFormStep"
                    (isValidForm)="newReportConfig.primaryBtnDisabled = $event"
                >
                </damage-loss-incident>

                <violence-abuse-incident
                    #violenceAbuseForm
                    *ngIf="incidentReport.incident_type == IncidentType.ViolenceOrAbuse"
                    [incidentReportMetaData]="incidentReportMetaData"
                    [incidentReport]="incidentReport"
                    [meta_incident_type_steps]="meta_incident_type_steps"
                    [incidentTypeMetaData]="incidentTypeMetaData"
                    [activeStep]="activeStep"
                    [cb]="c"
                    [formatDayJs]="dayjs"
                    [maxDate]="maxDate"
                    (formsStatus)="updateFormsStatus($event)"
                    [jobRoleList]="jobRoleList"
                    [projectId]="projectId"
                    [projectInfo]="projectInfo"
                    (setActiveStep)="updatedActiveStep($event)"
                    (submitViolenceAbuseReport)="submitIncidentReport($event)"
                    [changingStep]="changingFormStep"
                    (isValidForm)="newReportConfig.primaryBtnDisabled = $event"
                >
                </violence-abuse-incident>

                <environmental-incident
                    #environmentaltForm
                    *ngIf="incidentReport.incident_type == IncidentType.Environmental"
                    [incidentReportMetaData]="incidentReportMetaData"
                    [incidentReport]="incidentReport"
                    [meta_incident_type_steps]="meta_incident_type_steps"
                    [incidentTypeMetaData]="incidentTypeMetaData"
                    [activeStep]="activeStep"
                    [cb]="c"
                    [formatDayJs]="dayjs"
                    [maxDate]="maxDate"
                    [projectInfo]="projectInfo"
                    (formsStatus)="updateFormsStatus($event)"
                    (setActiveStep)="updatedActiveStep($event)"
                    (submitEnvironmentalIncidentReport)="submitIncidentReport($event)"
                    [changingStep]="changingFormStep"
                    (isValidForm)="newReportConfig.primaryBtnDisabled = $event"
                >
                </environmental-incident>

                <service-strike-incident
                    #serviceDtrikeForm
                    *ngIf="incidentReport.incident_type == IncidentType.ServiceStrike"
                    [incidentReportMetaData]="incidentReportMetaData"
                    [incidentReport]="incidentReport"
                    [meta_incident_type_steps]="meta_incident_type_steps"
                    [incidentTypeMetaData]="incidentTypeMetaData"
                    [activeStep]="activeStep"
                    [cb]="c"
                    [formatDayJs]="dayjs"
                    [maxDate]="maxDate"
                    [projectInfo]="projectInfo"
                    (formsStatus)="updateFormsStatus($event)"
                    (setActiveStep)="updatedActiveStep($event)"
                    (submitServiceStrikeIncidentReport)="submitIncidentReport($event)"
                    [changingStep]="changingFormStep"
                    (isValidForm)="newReportConfig.primaryBtnDisabled = $event"
                >
                </service-strike-incident>

                <near-miss-incident
                    #nearMissIncidentForm
                    *ngIf="incidentReport.incident_type == IncidentType.NearMiss"
                    [incidentReportMetaData]="incidentReportMetaData"
                    [incidentReport]="incidentReport"
                    [meta_incident_type_steps]="meta_incident_type_steps"
                    [incidentTypeMetaData]="incidentTypeMetaData"
                    [activeStep]="activeStep"
                    [cb]="c"
                    [formatDayJs]="dayjs"
                    [maxDate]="maxDate"
                    [projectInfo]="projectInfo"
                    (formsStatus)="updateFormsStatus($event)"
                    (setActiveStep)="updatedActiveStep($event)"
                    (submitNearMissReport)="submitIncidentReport($event)"
                    [changingStep]="changingFormStep"
                    (isValidForm)="newReportConfig.primaryBtnDisabled = $event"
                >
                </near-miss-incident>

                <unsafe-act-occurrence
                    #unsafeActOccurrenceForm
                    *ngIf="incidentReport.incident_type == IncidentType.UnsafeActOccurrence"
                    [incidentReportMetaData]="incidentReportMetaData"
                    [incidentReport]="incidentReport"
                    [meta_incident_type_steps]="meta_incident_type_steps"
                    [incidentTypeMetaData]="incidentTypeMetaData"
                    [activeStep]="activeStep"
                    [cb]="c"
                    [formatDayJs]="dayjs"
                    [maxDate]="maxDate"
                    [projectInfo]="projectInfo"
                    (formsStatus)="updateFormsStatus($event)"
                    (setActiveStep)="updatedActiveStep($event)"
                    (submitUnsafeActOccurrenceReport)="submitIncidentReport($event)"
                    [changingStep]="changingFormStep"
                    (isValidForm)="newReportConfig.primaryBtnDisabled = $event"
                >
                </unsafe-act-occurrence>
            </div>
        </div>

    </ng-template>
</app-generic-modal>

<block-loader [show]="(loading || loadingIncidents)" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>

<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
<power-bi-dashboard *ngIf="loadPowerBiComponent" (powerBiDashboardClose)="powerBiDashboardClose()" [portalType]="dashboardLevel" [modalTitle]="biModalTitle" [toolLabel]="biToolLabel" [toolName]="biToolName"></power-bi-dashboard>

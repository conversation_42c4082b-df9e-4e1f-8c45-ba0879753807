<style>
    .main-wrapper {
        border: 1px solid var(--chinese-silver);
    }

    .image {
        border: 1px solid var(--light-silver);
        background-color: var(--ghost-white);
    }

    .img-size-fix {
        height: 70px;
        width: 70px;
    }
</style>
<div class="w-100 mb-3 d-flex flex-column justify-content-center align-items-center">
    <p class="h6 fw-500 mb-0 text-quick-silver text-center"> {{ !isEditProfile ? 'CREATE' : 'EDIT'}} YOUR PROFILE</p>
    <p class="h4 fw-500 mb-0 text-center"> PERSONAL DETAILS </p>
</div>
<form role="form" #personalForm="ngForm" class="editForm" novalidate>
    <div class="form-group row mx-0">
        <div class="col-md-12 p-3 main-wrapper rounded">
            <div class="d-flex w-100">
                <svg *ngIf="!img_link; else imgLink" class="rounded img-size-fix" [ngClass]="{'image p-2': !img_link}">
                    <use [attr.xlink:href]="alt_img_link"></use>
                </svg>
                <ng-template #imgLink>
                    <img class="rounded" height="70" width="70" [src]="img_link">
                </ng-template>
                <div class="d-flex flex-column justify-content-end ml-3">
                    <p class="heading mb-0">Upload a profile photo</p>
                    <p class="sub-heading mb-0">PNG, JPG, JPEG (Max: 5MB)</p>
                    <p *ngIf="is_mobile; else webProfileUpload"><button class="btn btn-sm btn-im-helmet px-3" (click)="fileInput()">{{ !img_link ? 'Upload' : 'Edit'}}</button></p>
                    <ng-template #webProfileUpload>
                        <div class="mb-0" style="margin-top: 3px;" ngbDropdown>
                            <button class="btn btn-sm btn-im-helmet px-3 dropdown after-none" ngbDropdownToggle id="uploadP">{{ !img_link ? 'Upload' : 'Edit'}}</button>
                            <ul class="dropdown-menu" style="top:37px;font-size: 0.75rem;" ngbDropdownMenu aria-labelledby="uploadP">
                                <li class="dropdown-item dropdown-item-text cursor-pointer d4 d-flex mb-2 align-items-center" (click)="fileInput()">
                                    <span class="material-symbols-outlined mr-2 xx-large-font">photo_library</span> 
                                    <span class="medium-font">Choose File</span>
                                </li>
                                <li class="dropdown-item dropdown-item-text cursor-pointer d-flex align-items-center" (click)="choosePhoto()">
                                    <span class="material-symbols-outlined mr-2 xx-large-font">photo_camera</span> 
                                    <span class="medium-font">Take Photo</span>
                                </li>
                            </ul>
                        </div>
                    </ng-template>
                    
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-12">
        <crop-image-uploader [category]="'profile-pic'" (uploadDone)="uploadDone($event)" (uploading)="(isProcessing = $event)"
            [allowedMimeType]="allowedMime" [fileName]="'profile-image.png'" [resizeToWidth]="'280'"
            [isCropImageInModal]="true" [roundCropper]="false" (previewCroppedImage)="previewCroppedImage($event)">
        </crop-image-uploader>
    </div>
    <div class="form-group row">
        <label class="col-md-4 col-form-label form-control-label">
            Title <small class="required-asterisk ">*</small>
        </label>
        <div class="col-md-8">
            <ng-select [items]="['Mr', 'Mrs', 'Ms', 'Miss', 'Other']" placeholder="Select Title" name="title"
                #title="ngModel" ng-value="personal.title" (change)="resetForOther('other_title')"
                [(ngModel)]="personal.title" required>
            </ng-select>
            <div class="alert alert-danger mb-0" [hidden]="(title.valid)">Title is required</div>
        </div>
    </div>
    <div class="form-group row" *ngIf="personal?.title == 'Other'">
        <div class="col-md-4">
            Other Title <small class="required-asterisk ">*</small>
        </div>
        <div class="col-md-8">
            <input class="form-control input-md" #other_title="ngModel" [required]="!otherTitleNotRequired()"
                name="other_title" type="text" placeholder="Title here" [hidden]="otherTitleNotRequired()"
                [(ngModel)]="personal.other_title" ng-value="personal.other_title">
            <div class="alert alert-danger mb-0" [hidden]="(otherTitleNotRequired() || other_title.valid)">Title is
                required </div>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-4 col-form-label form-control-label">
            First Name <small class="required-asterisk ">*</small>
        </label>
        <div class="col-md-8">
            <input class="form-control input-md" #firstName="ngModel" required name="firstName" type="text"
                placeholder="First Name" [ngModel]="personal.first_name | capitalizeFirstChar"
                (ngModelChange)="personal.first_name=$event" (focusout)="personal.first_name=$event.target.value"
                ng-value="personal.first_name">
            <div class="alert alert-danger mb-0" [hidden]="(firstName.valid)">First Name is required</div>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-4 col-form-label form-control-label">Middle Name(s)</label>
        <div class="col-md-8">
            <input class="form-control input-md" #middlename="ngModel" name="middlename" type="text"
                placeholder="Middle Name" [ngModel]="personal.middle_name | capitalizeFirstChar"
                (ngModelChange)="personal.middle_name=$event" (focusout)="personal.middle_name=$event.target.value"
                ng-value="personal.middle_name">
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-4 col-form-label form-control-label">
            Last Name <small class="required-asterisk ">*</small>
        </label>
        <div class="col-md-8">
            <input class="form-control input-md" #lastname="ngModel" required id="lastname" name="lastname" type="text"
                placeholder="Last Name" [ngModel]="personal.last_name | capitalizeFirstChar"
                (ngModelChange)="personal.last_name=$event" (focusout)="personal.last_name=$event.target.value"
                ng-value="personal.last_name">
            <div class="alert alert-danger mb-0" [hidden]="lastname.valid">Last Name is required</div>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-4 col-form-label form-control-label">
            Date of Birth <small class="required-asterisk ">*</small>
        </label>
        <div class="col-md-8">
            <div class="input-group">
                <input class="form-control" #dob="ngModel" placeholder="dd-mm-yyyy" readonly required name="selectedDob"
                    [(ngModel)]="personal.selectedDob" ngbDatepicker [maxDate]="maxDate" [minDate]="minDate"
                    (dateSelect)="onDateSelection($event, personalForm)" #d="ngbDatepicker"
                    ng-value="personal.selectedDob">
                <div class="input-group-append">
                    <button class="btn btn-outline-secondary calendar" (click)="d.toggle()" type="button">
                        <i class="fa fa-calendar"></i>
                    </button>
                </div>
                <div class="alert alert-danger mb-0" *ngIf="dob.touched && !dob.valid">Please enter a valid date of birth</div>
            </div>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-4 col-form-label form-control-label">
            Nationality <small class="required-asterisk ">*</small>
        </label>
        <div class="col-md-8">
            <ng-select [items]="countries" bindLabel="demonym" [bindValue]="'demonym'" [virtualScroll]="true"
                class="v-scroll" placeholder="Select Nationality" name="country" #country="ngModel"
                ng-value="personal.country" [(ngModel)]="personal.country" required>
            </ng-select>
            <div class="alert alert-danger mb-0" [hidden]="(country.valid)">Nationality is required</div>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-4 col-form-label form-control-label">
            Gender <small class="required-asterisk ">*</small>
        </label>
        <div class="col-md-8">
            <!-- <select [(ngModel)]="personal.gender" #gender="ngModel" name="gender" class="form-control" required
                    ng-value="personal.gender">
                <option *ngFor="let t of ['Man', 'Woman', 'Non Binary', 'Other', 'Prefer not to say']" [ngValue]="t">
                    {{ t }}
                </option>
            </select> -->
            <ng-select [items]="['Man', 'Woman', 'Non Binary', 'Other', 'Prefer not to say']"
                placeholder="Select Gender" name="gender" #gender="ngModel" ng-value="personal.gender"
                [(ngModel)]="personal.gender" required>
            </ng-select>
            <div class="alert alert-danger mb-0" [hidden]="(gender.valid)">Gender is required</div>
        </div>
    </div>
    <!--<div class="form-group row">
        <label class="col-md-3 col-form-label form-control-label">
            Marital Status <small class="required-asterisk ">*</small>
        </label>
        <div class="col-md-3">
            <select [(ngModel)]="personal.marital_status" #marital_status="ngModel" name="marital_status" class="form-control" required ng-value="personal.marital_status">
                <option *ngFor="let t of MARITAL_STATUS" [ngValue]="t.key">
                    {{ t.value }}
                </option>
            </select>
            <div class="alert alert-danger" [hidden]="(marital_status.valid)">Marital status is required</div>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-3 col-form-label form-control-label">
            Sexual Orientation <small class="required-asterisk ">*</small>
        </label>
        <div class="col-md-3">
            <select [(ngModel)]="personal.sexual_orientation" #sexual_orientation="ngModel" name="sexual_orientation" class="form-control" required ng-value="personal.sexual_orientation">
                <option *ngFor="let t of SEXUAL_ORIENTATION" [ngValue]="t.key">
                    {{ t.value }}
                </option>
            </select>
            <div class="alert alert-danger" [hidden]="(sexual_orientation.valid)">Sexual orientation is required</div>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-3 col-form-label form-control-label">
            Disability <small class="required-asterisk ">*</small>
        </label>
        <div class="col-md-3">
            <select [(ngModel)]="personal.disability" #disability="ngModel" name="disability" class="form-control" required ng-value="personal.disability">
                <option *ngFor="let t of DISABILITY" [ngValue]="t.key">
                    {{ t.value }}
                </option>
            </select>
            <div class="alert alert-danger" [hidden]="(disability.valid)">Disability is required</div>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-3 col-form-label form-control-label">
            Ethnicity <small class="required-asterisk ">*</small>
        </label>
        <div class="col-md-3">
            <select [(ngModel)]="personal.ethnicity" #ethnicity="ngModel" name="ethnicity" class="form-control" required ng-value="personal.ethnicity">
                <option *ngFor="let t of ETHNICITY" [ngValue]="t.key">
                    {{ t.value }}
                </option>
            </select>
            <div class="alert alert-danger" [hidden]="(ethnicity.valid)">Ethnicity is required</div>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-3 col-form-label form-control-label">
            Subethnicity <small class="required-asterisk ">*</small>
        </label>
        <div class="col-md-3">
            <select [(ngModel)]="personal.subethnicity" #subethnicity="ngModel" name="subethnicity" class="form-control" required ng-value="personal.subethnicity">
                <option *ngFor="let t of SUBETHNICITY" [ngValue]="t.key">
                    {{ t.value }}
                </option>
            </select>
            <div class="alert alert-danger" [hidden]="(subethnicity.valid)">Subethnicity is required</div>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-3 col-form-label form-control-label">
            Religion <small class="required-asterisk ">*</small>
        </label>
        <div class="col-md-3">
            <select [(ngModel)]="personal.religion" #religion="ngModel" name="religion" class="form-control" required ng-value="personal.religion">
                <option *ngFor="let t of RELIGION" [ngValue]="t.key">
                    {{ t.value }}
                </option>
            </select>
            <div class="alert alert-danger" [hidden]="(religion.valid)">Religion is required</div>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-3 col-form-label form-control-label">
            Caring Responsibilities <small class="required-asterisk ">*</small>
        </label>
        <div class="col-md-3">
            <select [(ngModel)]="personal.caring_responsibilities" #caring_responsibilities="ngModel" name="caring_responsibilities" class="form-control" required ng-value="personal.caring_responsibilities">
                <option *ngFor="let t of CARING_RESPONSIBILITIES" [ngValue]="t.key">
                    {{ t.value }}
                </option>
            </select>
            <div class="alert alert-danger" [hidden]="(caring_responsibilities.valid)">Caring responsibilities is required</div>
        </div>
    </div>-->

    <div class="d-flex my-1" *ngIf="(isOnboardStepsComplete || showExitButton) else nextButton">
        <button class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="exit()"> Exit </button>
        <button type="submit" class="btn btn-brandeis-blue ml-2 w-50" (click)="save(personalForm, false)" value="Save Changes"
            [disabled]="!personalForm.valid"> Next </button>
    </div>
    <ng-template #nextButton>
        <div class="my-1">
            <button type="submit" class="btn btn-brandeis-blue w-50 float-right" (click)="save(personalForm, false)" value="Save Changes"
                [disabled]="!personalForm.valid"> Next </button>
        </div>
    </ng-template>
    <block-loader [alwaysInCenter]="true" [show]="(isProcessing)" [showBlockBackdrop]="true"></block-loader>
    <!-- <div class="form-group row">
        <label class="col-lg-3 col-form-label form-control-label"></label>
        <div class="col-lg-9">
            <input type="reset" class="btn btn-secondary mr-1" value="Clear">
            <input type="submit" class="btn btn-primary" value="Save Changes" [disabled]="!personalForm.valid"
                   (click)="save(personalForm)"/>
            <block-loader [show]="(isProcessing)"></block-loader>
        </div>
    </div> -->
</form>
<!-- Webcam Component  -->
<app-webcam (onCapture)="getCapturedImage($event)" #webcamComponent></app-webcam>
<!-- Confirmation Modal on route change with save form data  -->
<generic-confirmation-modal #confirmationModalRef (confirmEvent)="confirmationVal($event)"></generic-confirmation-modal>

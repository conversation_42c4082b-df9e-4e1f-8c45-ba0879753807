<style>
    .disabled { opacity: 0.3; pointer-events: none; }
    .disabled .alert-danger{display: none;}
</style>
<ng-container *ngIf="(!isOnBoardStep && !isNewFormUI); else secondForm">
<h4 class="m-y-2">{{ title }}</h4>
<form *ngIf="employment_detail" role="form" #employmentForm="ngForm" class="editForm" [class.row]="!fullWidth" novalidate>
    <div [class.col-md-9]="!fullWidth">
        <div class="form-group row">
            <label class="col-md-4 col-form-label form-control-label">Country of Work<small class="required-asterisk ">*</small></label>
            <div class="col-md-8 row">
                <country-selector-dd class="col-md-12" #countrySelector
                        [placeholderText]="'Select country of work'"
                        [selectedCode]="country_code" [classes]="(fullWidth ? '' : 'w-md-50')"
                        (selectionChanged)="onCountryCodeChange($event)"
                ></country-selector-dd>
                <input type="hidden" name="country_code" required #countryCodeHiddenField="ngModel" [ngModel]="country_code"/>
                <small *ngIf="preferred_country_code && preferred_country_code !== country_code"
                       class="col-md-12 pr-0 text-danger form-text">
                    Please change your country of work to <b>{{getCountryFullName(preferred_country_code)}}</b> to continue.
                </small>
            </div>
        </div>
        <ng-container *ngIf="country_code">
        <div class="form-group row">
            <label class="col-md-4 col-form-label form-control-label">Company Name<small class="required-asterisk ">*</small></label>
            <div class="col-md-8 row">
                <company-selector-v2 class="col-md-12" [name]="'companyName'"
                    [selectedCompany]="employment_detail.employer"
                    [requiredMessage]="'Company Name is required'"
                    [required]="true"
                     [showHelper]="!supplyCompanies.length"
                    [country_code]="country_code"
                    [projectId]="projectId"
                    (selectionChanged)="companySelected($event)"
                    [hasCompanyDomainCheck]="true"
                ></company-selector-v2>
                <ng-container *ngIf="showHelperMessage">
                    <small class="col-md-12 form-text pr-0" *ngIf="employment_detail.employer; else employerNotSelected">
                           By selecting <strong>{{employment_detail.employer}}</strong> as your company, you confirm that all provided information is accurate and consent to sharing your details with them.
                    </small>
                    <ng-template #employerNotSelected><small class="col-md-12 form-text px-0">If your company is not listed, please contact a member of the project's admin team for assistance.</small></ng-template>
                </ng-container>
                <input type="hidden" name="company" required #companyHiddenField="ngModel" [ngModel]="employment_detail.employer"/>
            </div>
        </div>
        <!--<div class="form-group row" *ngIf="employment_detail.employer">
            <div class="col-md-3"></div>
            <small class="col-md-6 form-text text-danger">
                By choosing <strong>{{employment_detail.employer}}</strong> as an company, you are agreeing that all of your information is correct and that you are agreeing to share your information with them
            </small>
        </div>
        <div class="form-group row d-none">
            <label class="col-md-3 col-form-label form-control-label">Reporting to</label>
            <div class="col-md-3">
                <input class="form-control input-md" #reporting_to="ngModel" name="reporting_to" type="text" placeholder="Reporting to" [(ngModel)]="employment_detail.reporting_to" ng-value="employment_detail.reporting_to">
                <div class="alert alert-danger" [hidden]="(reporting_to.valid)">Reporting to name is required</div>
            </div>
        </div>-->
        <!--  <div class="form-group row">
            <label class="col-md-3 col-form-label form-control-label">Role Category</label>
            <div class="col-md-3">
                <select [(ngModel)]="employment_detail.operative_type" #operative_type="ngModel"
                        name="operative_type" class="form-control" required
                        (change)="roleCategoryChanged(operative_type.value)"
                        ng-value="employment_detail.operative_type">
                    <option *ngFor="let t of roleCategories" [ngValue]="t">
                        {{ t }}
                    </option>
                </select>
                <div class="alert alert-danger" [hidden]="(operative_type.valid)">Role category is required</div>
            </div>

            </div> -->
        <div [ngClass]="{'disabled': !employment_detail.employer}">
        <div class="form-group row">
            <label class="col-md-4 col-form-label form-control-label">Job Role<small class="required-asterisk ">*</small></label>
            <div class="col-md-4">
                <ng-select [items]="allowedJobRoles"
                           bindLabel="name"
                           [bindValue]="'name'" required
                           [virtualScroll]="true"
                           placeholder="Select Job Role" #jobRoleSelector
                           name="job_role" #job_role="ngModel"
                           [(ngModel)]="employment_detail.job_role"
                           [disabled]="!employment_detail?.employer">
                </ng-select>
                <div class="alert alert-danger" [hidden]="(job_role.valid)">Job Role is required</div>
            </div>
            <div class="col-md-4">
                <input class="form-control input-md" #other_jr="ngModel"
                       [required]="!otherJobRoleNotRequired()" name="other_jr"
                       type="text" placeholder="Job Role"
                       [hidden]="otherJobRoleNotRequired()"
                       [(ngModel)]="employment_detail.other_job_role"
                       ng-value="employment_detail.other_job_role">
                <div class="alert alert-danger" [hidden]="(otherJobRoleNotRequired() || other_jr.valid)">Job Role is required</div>
            </div>
        </div>
        <div class="form-group row" *ngIf="showTypeOfEmployment()">
            <label class="col-md-4 col-form-label form-control-label">Type of Employment<small class="required-asterisk ">*</small></label>
            <div class="col-md-4">
                <ng-select
                    [items]="typeOfEmployments"
                    [bindLabel]="'label'"
                    [bindValue]="'value'" required
                    [placeholder]="'Select Employment'"
                    [searchable]="false" [clearable]="false"
                    name="type_of_employment" #type_of_employment="ngModel"
                    class="w-md-100"
                    [(ngModel)]="employment_detail.type_of_employment"
                    (ngModelChange)="resetForOther(type_of_employment.value, 'other_type_of_employment')"
                ></ng-select>
                <div class="alert alert-danger" [hidden]="(type_of_employment.valid)">Type of Employment is required</div>
            </div>
            <div class="col-md-4">
                <input class="form-control input-md" #other_toe="ngModel"
                       [required]="!otherTypeOfEmpNotRequired()"
                       name="other_toe"
                       type="text"
                       placeholder="Type of Employment"
                       [hidden]="otherTypeOfEmpNotRequired()"
                       [(ngModel)]="employment_detail.other_type_of_employment"
                       ng-value="employment_detail.other_type_of_employment">
                <div class="alert alert-danger" [hidden]="(otherTypeOfEmpNotRequired() || other_toe.valid)">Type of Employment is required</div>
            </div>
        </div>
        <div class="form-group row" *ngIf="showEmploymentCompanyPicker()">
            <label class="col-md-4 col-form-label form-control-label">{{ employmentCompanyFieldLabel }}<small class="required-asterisk ">*</small></label>
            <div class="col-md-8 row">
                <company-selector-v2 class="col-md-12" [name]="'employmentCompany'"
                    [selectedCompany]="employment_detail.employment_company"
                    [placeholder]="'Select '+employmentCompanyFieldLabel"
                    [searchMessage]="'Type to search '+employmentCompanyFieldLabel"
                    [requiredMessage]="'Company Name is required'"
                    [required]="true"
                    [showHelper]="!supplyCompanies.length"
                    [country_code]="country_code"
                    [projectId]="projectId"
                    (selectionChanged)="employmentCompanySelected($event)"
                    [hasCompanyDomainCheck]="true"
                ></company-selector-v2>
                <input type="hidden" name="employment_company" required #employmentCompanyHiddenField="ngModel" [ngModel]="employment_detail.employment_company"/>
            </div>
        </div>
        <div class="form-group row" *ngIf="showEmploymentStartDate()">
            <label class="col-md-4 col-form-label form-control-label" style="margin: auto 0;">Employment Start Date<small class="required-asterisk ">*</small></label>
            <div class="col-md-4 p-0-">
                <div class="input-group">
                    <input class="form-control" placeholder="dd-mm-yyyy" readonly
                           name="selectedStartDateWithEmployer"
                           [(ngModel)]="employment_detail.selectedStartDateWithEmployer"
                           [maxDate]="todayDate"
                           ngbDatepicker
                           required (dateSelect)="onChangeSelectedStartDateWithEmployer()"
                           #d="ngbDatepicker" ng-value="employment_detail.selectedStartDateWithEmployer">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary calendar" (click)="d.toggle()" type="button"><i class="fa fa-calendar"></i></button>
                    </div>
                </div>
            </div>
            <!--<div class="d-flex align-self-center col-sm-4" *ngIf="employment_detail.start_date_with_employer">-->
            <!--{{ createDurationTime(employment_detail.start_date_with_employer) }}-->
            <!--<div class="alert alert-danger" [hidden]="isValidEmploymentDuration()">Employment Duration is required</div>-->
            <!--</div>-->
        </div>
        <ng-container>
        <div class="form-group row" *ngIf="showMinWage()">
            <label class="col-md-4 col-form-label form-control-label" [ngClass]="{'col-md-9': isBeforInductionStartForm}">Do you earn above min. living wage?<small class="required-asterisk ">*</small></label>
            <div class="col-md-3 form-inline" [ngClass]="{'justify-content-end': isBeforInductionStartForm}">
                <div class="custom-control custom-radio mr-3">
                    <input type="radio" class="custom-control-input" name="earn_mlw_e783" id="earn-yes" required [(ngModel)]="employment_detail.earn_mlw_e783" [value]="1" />
                    <label class="custom-control-label" for="earn-yes"> Yes</label>
                </div>
                <div class="custom-control custom-radio">
                    <input type="radio" class="custom-control-input" name="earn_mlw_e783" id="earn-no" required [(ngModel)]="employment_detail.earn_mlw_e783" [value]="0" />
                    <label class="custom-control-label" for="earn-no"> No </label>
                </div>
            </div>
        </div>
        <div class="form-group row" *ngIf="showNIN()">
            <label class="col-md-4 col-form-label form-control-label">
                <span i18n="@@nin">National Insurance Number</span>
            </label>
            <div class="col-md-4">
                <input class="form-control input-md" [pattern]="patternNin" #ninInput="ngModel" id="nin" name="nin"
                       type="text" placeholder="National Insurance Number" i18n-placeholder="@@nin" [ngModel]="nin | uppercase"
                       (ngModelChange)="nin=$event" ng-value="nin">
                <div class="alert alert-danger" *ngIf="ninInput.errors?.required"><span i18n="@@nin">National Insurance Number</span> is required</div>
                <div class="alert alert-danger" *ngIf="ninInput.errors?.pattern"><span i18n="@@nin">National Insurance Number</span> is not valid.</div>
            </div>
        </div>
        </ng-container>
        </div>
        <!--<div class="form-group row">
            <label class="col-md-3 col-form-label form-control-label">Working Arrangement</label>
            <div class="col-md-3">
                <select [(ngModel)]="employment_detail.working_arrangement" #working_arrangement="ngModel" name="working_arrangement" class="form-control" required ng-value="employment_detail.working_arrangement">
                    <option *ngFor="let t of WORKING_ARRANGEMENT" [ngValue]="t.key">
                        {{ t.value }}
                    </option>
                </select>
                <div class="alert alert-danger" [hidden]="(working_arrangement.valid)">Working arrangement is required</div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-3 col-form-label form-control-label">Working Pattern</label>
            <div class="col-md-3">
                <select [(ngModel)]="employment_detail.working_pattern" #working_pattern="ngModel" name="working_pattern" class="form-control" required ng-value="employment_detail.working_pattern">
                    <option *ngFor="let t of WORKING_PATTERN" [ngValue]="t.key">
                        {{ t.value }}
                    </option>
                </select>
                <div class="alert alert-danger" [hidden]="(working_pattern.valid)">Working pattern is required</div>
            </div>
        </div>-->

        <div class="mt-2" *ngIf="false">
            <h5 class="m-y-2">Payment Details (Not Mandatory)</h5>
            <div class="form-group row">
                <label class="col-md-3 col-form-label form-control-label">Bank Name</label>
                <div class="col-md-3">
                    <input class="form-control input-md" #bank_name="ngModel"
                           name="bank_name"
                           type="text" placeholder="Bank Name"
                           [(ngModel)]="employment_detail.bank_name"
                           ng-value="employment_detail.bank_name">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-3 col-form-label form-control-label">Account Number</label>
                <div class="col-md-3">
                    <input class="form-control input-md" pattern="\d*" #account_number="ngModel"
                           id="account_number" name="account_number" type="text" placeholder="Account Number"
                           [(ngModel)]="employment_detail.account_number"
                           ng-value="employment_detail.account_number">
                    <div class="alert alert-danger" *ngIf="account_number.errors?.pattern">Account Number must be a number.</div>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-3 col-form-label form-control-label">Sort Code</label>
                <div class="col-md-3">
                    <input class="form-control input-md" #sort_code="ngModel"
                           id="sort_code" name="sort_code" type="text" placeholder="Sort Code"
                           [(ngModel)]="employment_detail.sort_code" mask="00-00-00"
                           ng-value="employment_detail.sort_code">
                    <div class="alert alert-danger" *ngIf="sort_code.errors?.pattern">Please enter a valid sort code number (6 numeric digits) without any spaces or dashes.</div>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-3 col-form-label form-control-label">UTR Number</label>
                <div class="col-md-3">
                    <input class="form-control input-md" pattern="\d*" #utr_number="ngModel"
                           id="utr_number" name="utr_number" type="text" placeholder="UTR Number"
                           [(ngModel)]="employment_detail.utr_number"
                           ng-value="employment_detail.utr_number">
                    <div class="alert alert-danger" *ngIf="utr_number.errors?.pattern">UTR Number must be a number.</div>
                </div>
            </div>
        </div>
        </ng-container>
        <input type="hidden" name="employer_name" required #companyHiddenField="ngModel" [ngModel]="employment_detail.employer"/>
        <div class="form-group">
            <div class="d-flex" [ngClass]="isBeforInductionStartForm ? 'justify-content-end': 'justify-content-center'">
                <button class="btn btn-secondary mr-1" type="reset" (click)="formClearAction($event)" *ngIf="showClearButton">Clear</button>
                <button class="btn btn-primary" [disabled]="!employmentForm.valid" (click)="save(employmentForm, false)" [class.disabled]="!employmentForm.valid">Save Changes</button>
                <button class="btn btn-secondary ml-1" (click)="skipStep()" *ngIf="showSkipButton">Skip</button>
                <block-loader [show]="(isProcessing)" alwaysInCenter="alwaysInCenter"></block-loader>
            </div>
        </div>
    </div>
</form>
</ng-container>
<ng-template #secondForm>
    <div *ngIf="showTitle" class="w-100 mb-3 d-flex flex-column justify-content-center align-items-center">
        <p class="h6 fw-500 mb-0 text-quick-silver text-center"> {{ title }}</p>
        <p class="h4 fw-500 mb-0 text-center"> EMPLOYMENT DETAILS </p>
    </div>
    <form *ngIf="employment_detail" role="form" #employmentForm="ngForm" class="editForm" novalidate>
        <div class="form-group row" *ngIf="!isShadowUser">
            <label class="col-md-4 col-form-label form-control-label">
                Country of Work<small class="required-asterisk ">*</small>
            </label>
            <div class="col-md-8">
                <country-selector-dd class="" #countrySelector
                         [placeholderText]="'Select country of work'" [disabled]="isShadowUser"
                        [selectedCode]="country_code" [classes]="'w-md-100'"
                        (selectionChanged)="onCountryCodeChange($event)"
                ></country-selector-dd>
                <input type="hidden" name="country_code" required #countryCodeHiddenField="ngModel" [ngModel]="country_code"/>
                <small *ngIf="preferred_country_code && preferred_country_code !== country_code"
                       class="col-md-12 pr-0 text-danger form-text">
                    Please change your country of work to <b>{{getCountryFullName(preferred_country_code)}}</b> to continue.
                </small>
            </div>
        </div>
        <ng-container *ngIf="country_code">
            <div class="form-group row">
                <label class="col-md-4 col-form-label form-control-label">
                    Company Name<small class="required-asterisk ">*</small>
                </label>
                <div class="col-md-8">
                    <company-selector-v2 class="w-100" [name]="'companyName'"
                        [selectedCompany]="employment_detail.employer"
                        [requiredMessage]="'Company Name is required'"
                        [required]="true"
                         [showHelper]="!supplyCompanies.length"
                        [country_code]="country_code"
                        [projectId]="projectId"
                        (selectionChanged)="companySelected($event)"
                        [hasCompanyDomainCheck]="true"
                    ></company-selector-v2>
                    <ng-container *ngIf="showHelperMessage">
                        <small class="col-md-12 form-text px-0" *ngIf="employment_detail.employer; else employerNotSelected">
                            By selecting <strong>{{employment_detail.employer}}</strong> as your company, you confirm that all provided information is accurate and consent to sharing your details with them.
                        </small>
                        <ng-template #employerNotSelected><small class="col-md-12 form-text px-0">If your company is not listed, please contact a member of the project's admin team for assistance.</small></ng-template>
                    </ng-container>
                    <input type="hidden" name="company" required #companyHiddenField="ngModel" [ngModel]="employment_detail.employer"/>
                </div>
            </div>
            <div [ngClass]="{'disabled': !employment_detail.employer}">
                <div class="form-group row">
                    <label class="col-md-4 col-form-label form-control-label">
                        Job Role<small class="required-asterisk ">*</small>
                    </label>
                    <div class="col-md-8">
                        <ng-select [items]="allowedJobRoles"
                                   bindLabel="name"
                                   [bindValue]="'name'" required
                                   placeholder="Select Job Role" #jobRoleSelector
                                   name="job_role" #job_role="ngModel"
                                   [(ngModel)]="employment_detail.job_role"
                                   [disabled]="!employment_detail?.employer">
                        </ng-select>
                        <div class="alert alert-danger" [hidden]="(job_role.valid)">Job Role is required</div>
                    </div>
                </div>
                <div class="form-group row" *ngIf="!otherJobRoleNotRequired()">
                    <label class="col-md-4 col-form-label form-control-label">
                        Other Job Role<small class="required-asterisk ">*</small>
                    </label>
                    <div class="col-md-8">
                        <input class="form-control input-md" #other_jr="ngModel"
                               [required]="!otherJobRoleNotRequired()" name="other_jr"
                               type="text" placeholder="Job Role"
                               [hidden]="otherJobRoleNotRequired()"
                               [(ngModel)]="employment_detail.other_job_role"
                               ng-value="employment_detail.other_job_role">
                        <div class="alert alert-danger" [hidden]="(otherJobRoleNotRequired() || other_jr.valid)">Job Role is required</div>
                    </div>
                </div>
                <div class="form-group row" *ngIf="showTypeOfEmployment()">
                    <label class="col-md-4 col-form-label form-control-label">Type of Employment<small class="required-asterisk ">*</small></label>
                    <div class="col-md-8">
                        <ng-select
                            [items]="typeOfEmployments"
                            [bindLabel]="'label'"
                            [bindValue]="'value'" required
                            [placeholder]="'Select Employment'"
                            [searchable]="false" [clearable]="false"
                            name="type_of_employment" #type_of_employment="ngModel"
                            class="w-md-100"
                            [(ngModel)]="employment_detail.type_of_employment"
                            (ngModelChange)="resetForOther(type_of_employment.value, 'other_type_of_employment')"
                        ></ng-select>
                        <div class="alert alert-danger" [hidden]="(type_of_employment.valid)">Type of Employment is required</div>
                    </div>
                </div>
                <div class="form-group row" *ngIf="!otherTypeOfEmpNotRequired()">
                    <label class="col-md-4 col-form-label form-control-label">
                        Other Employment<small class="required-asterisk ">*</small>
                    </label>
                    <div class="col-md-8">
                        <input class="form-control input-md" #other_toe="ngModel"
                               [required]="!otherTypeOfEmpNotRequired()"
                               name="other_toe"
                               type="text"
                               placeholder="Type of Employment"
                               [hidden]="otherTypeOfEmpNotRequired()"
                               [(ngModel)]="employment_detail.other_type_of_employment"
                               ng-value="employment_detail.other_type_of_employment">
                        <div class="alert alert-danger" [hidden]="(otherTypeOfEmpNotRequired() || other_toe.valid)">Type of Employment is required</div>
                    </div>
                </div>
                <div class="form-group row" *ngIf="showEmploymentCompanyPicker()">
                    <label class="col-md-4 col-form-label form-control-label">{{ employmentCompanyFieldLabel }}<small class="required-asterisk ">*</small></label>
                    <div class="col-md-8">
                        <company-selector-v2 class="w-100" [name]="'employmentCompany'"
                            [selectedCompany]="employment_detail.employment_company"
                            [placeholder]="'Select '+employmentCompanyFieldLabel"
                            [searchMessage]="'Type to search '+employmentCompanyFieldLabel"
                            [requiredMessage]="'Company Name is required'"
                            [required]="true"
                            [showHelper]="!supplyCompanies.length"
                            [country_code]="country_code"
                            [projectId]="projectId"
                            (selectionChanged)="employmentCompanySelected($event)"
                            [hasCompanyDomainCheck]="true"
                        ></company-selector-v2>
                        <input type="hidden" name="employment_company" required #employmentCompanyHiddenField="ngModel" [ngModel]="employment_detail.employment_company"/>
                    </div>
                </div>
                <div class="form-group row" *ngIf="showEmploymentStartDate()">
                    <label class="col-md-4 col-form-label form-control-label" style="margin: auto 0;">Employment Start Date<small class="required-asterisk ">*</small></label>
                    <div class="col-md-8">
                        <div class="input-group">
                            <input class="form-control" placeholder="dd-mm-yyyy" readonly
                                   name="selectedStartDateWithEmployer"
                                   [(ngModel)]="employment_detail.selectedStartDateWithEmployer"
                                   [maxDate]="todayDate"
                                   [minDate]="minDate"
                                   ngbDatepicker
                                   required (dateSelect)="onChangeSelectedStartDateWithEmployer()"
                                   #d="ngbDatepicker" ng-value="employment_detail.selectedStartDateWithEmployer">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary calendar" (click)="d.toggle()" type="button"><i class="fa fa-calendar"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
                <ng-container>
                    <!-- <div class="form-group row">
                        <label class="col-md-4 col-form-label form-control-label">Do you earn above min. living wage?<small class="required-asterisk ">*</small></label>
                        <div class="col-md-8">
                            <div class="custom-control custom-radio mr-3">
                                <input type="radio" class="custom-control-input" name="earn_mlw_e783" id="earn-yes" required [(ngModel)]="employment_detail.earn_mlw_e783" [value]="1" />
                                <label class="custom-control-label" for="earn-yes"> Yes</label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" class="custom-control-input" name="earn_mlw_e783" id="earn-no" required [(ngModel)]="employment_detail.earn_mlw_e783" [value]="0" />
                                <label class="custom-control-label" for="earn-no"> No </label>
                            </div>
                        </div>
                    </div> -->
                    <div class="form-group row" *ngIf="showMinWage()">
                        <div class="col-sm-9 mb-0">Do you earn above min. living wage?<small class="required-asterisk ">*</small></div>
                        <div class="col-sm-3 font-weight-bold d-flex justify-content-md-end">
                            <fieldset class="d-flex">
                                <div class="custom-control custom-radio mr-3">
                                    <input type="radio" class="custom-control-input"
                                           name="earn_mlw_e783" id="earn-yes"
                                           required [(ngModel)]="employment_detail.earn_mlw_e783" [value]="1" />
                                    <label class="custom-control-label" for="earn-yes"> Yes</label>
                                </div>
                                <div class="custom-control custom-radio">
                                    <input type="radio" class="custom-control-input"
                                           name="earn_mlw_e783" id="earn-no"
                                           required [(ngModel)]="employment_detail.earn_mlw_e783" [value]="0" />
                                    <label class="custom-control-label" for="earn-no"> No </label>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                    <div class="form-group row" *ngIf="showNIN()">
                        <label class="col-md-4 col-form-label form-control-label">
                            <span i18n="@@nin">National Insurance Number</span>
                        </label>
                        <div class="col-md-8">
                            <input class="form-control input-md" [pattern]="patternNin" #ninInput="ngModel" id="nin" name="nin"
                                   type="text" placeholder="National Insurance Number" i18n-placeholder="@@nin" [ngModel]="nin | uppercase"
                                   (ngModelChange)="nin=$event" ng-value="nin">
                            <div class="alert alert-danger" *ngIf="ninInput.errors?.required"><span i18n="@@nin">National Insurance Number</span> is required</div>
                            <div class="alert alert-danger" *ngIf="ninInput.errors?.pattern"><span i18n="@@nin">National Insurance Number</span> is not valid.</div>
                        </div>
                    </div>

                    <div class="form-group row" *ngIf="showEmpNbr()">
                        <label class="col-md-4 col-form-label form-control-label">
                            <span>Employee Number<small class="required-asterisk" *ngIf="isEmpNbrRequired()">*</small></span>
                        </label>
                        <div class="col-md-8">
<!--                            pattern="^(\d{3}/\d{4}/\d{6,7}|\d{6,9})$" format: 123/2015/1234567 and 12345678-->
                            <input class="form-control input-md" #employeeNumberInput="ngModel" name="employee_number"
                                   type="text" placeholder="Employee Number" [(ngModel)]="employment_detail.employee_number"
                                   ng-value="employment_detail.employee_number" [required]="isEmpNbrRequired()">
                            <ng-container *ngIf="isEmpNbrRequired()">
                                <div class="alert alert-danger" *ngIf="employeeNumberInput.errors?.required"><span>Employee Number</span> is required</div>
                                <div class="alert alert-danger" *ngIf="employeeNumberInput.errors?.pattern"><span>Employee Number</span> not valid.</div>
                            </ng-container>
                        </div>
                    </div>
                </ng-container>
            </div>
        </ng-container>
        <input type="hidden" name="employer_name" required #companyHiddenField="ngModel" [ngModel]="employment_detail.employer"/>
        <div *ngIf="isSaveButtonVisible" class="form-group">
            <div class="d-flex" [ngClass]="isBeforInductionStartForm ? 'justify-content-end': 'justify-content-center'">
                <button class="btn btn-secondary mr-1" type="reset" (click)="formClearAction($event)" *ngIf="showClearButton">Clear</button>
                <button class="btn btn-primary" [disabled]="!employmentForm.valid" (click)="save(employmentForm, false)" [class.disabled]="!employmentForm.valid">Save Changes</button>
                <button class="btn btn-secondary ml-1" (click)="skipStep()" *ngIf="showSkipButton">Skip</button>
                <block-loader [show]="(isProcessing)" alwaysInCenter="alwaysInCenter"></block-loader>
            </div>
        </div>
        <div *ngIf="isOnBoardStep" class="d-flex my-1">
            <button class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="goBack()"> Back </button>
            <button type="submit" class="btn btn-brandeis-blue ml-2 w-50"
                (click)="save(employmentForm, false)" value="Save Changes" [disabled]="!employmentForm.valid" [class.disabled]="!employmentForm.valid"> Next </button>
        </div>
        <block-loader [show]="(isProcessing)" [showBlockBackdrop]="true" [alwaysInCenter]="true"></block-loader>
    </form>
</ng-template>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
<!-- Confirmation Modal on route change with save form data  -->
<generic-confirmation-modal #changeConfirmationModalRef (confirmEvent)="confirmationVal($event)"></generic-confirmation-modal>

import {Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild} from '@angular/core';
import {AuthService, EmploymentDetail, UserService, Common, FeatureExclusionUtility, Project, ToastService, ProjectService, SkillMatrixService, User, UserPreferences} from "@app/core";
import {ActivatedRoute, NavigationStart, Router} from "@angular/router";
import {filter, map, takeUntil} from "rxjs/operators";
// import {RoleCategories} from "./employment.meta";
import {forkJoin, Observable, Subject} from "rxjs";
import {FormControl, NgForm, NgModel} from "@angular/forms";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import * as dayjs from 'dayjs';
import {CompanySelectorComponent} from "./../company-selector/company-selector.component";
import {CountrySelectorComponent} from "./../country-selector/country-selector.component";
import {NgSelectComponent} from "@ng-select/ng-select";
import { AppConstant } from '@env/environment';
import { GenericConfirmationModalComponent } from '@app/shared';

@Component({
    selector: 'on-board-employment-form',
    templateUrl: './5_employment.form.component.html',
    styleUrls: []
})
export class OnBoardEmploymentFormComponent implements OnInit, OnChanges {
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;

    @Output()
    onSave: any = new EventEmitter<any>();

    @Output()
    back: any = new EventEmitter<any>();

    @Input()
    isProcessing: boolean = false;

    @Input()
    title: string = 'Employment details';

    @Input()
    showClearButton: boolean = true;
    @Input()
    skillMatrix: boolean = false;
    @Input()
    contractorId: number;

    @Input()
    fullWidth: boolean = false;

    @Input()
    showSkipButton: boolean = false;

    @Input()
    employment_detail: EmploymentDetail = new EmploymentDetail();

    @Input()
    selected_employer_id: any;

    @Input()
    supplyCompanies: Array<number> = [];

    @Input() isBeforInductionStartForm: boolean = false;

    @Input()
    country_code: string;

    @Input()
    preferred_country_code: string;

    @Input()
    nin: string;

    @Input()
    isOnBoardStep: boolean = false;

    @Input()
    saveEmploymentDetailV2: boolean = false;

    @Input()
    isNewFormUI: boolean = false;

    @Input()
    showTitle: boolean = true;

    @Input()
    isSaveButtonVisible: boolean = false;

    @Input()
    isShadowUser: boolean = false;

    @Input()
    showHelperMessage: boolean = false;


    projectId: number;
    project: Project = new Project;
    has_supply_chain_companies: boolean = false;

    typeOfEmployments: Array<object> = new Common().typeOfEmployments();

    // roleCategories: Array<string> = RoleCategories;

    allowedJobRoles: Array<any> = [];
    companiesList: Array<any> = [];

    todayDate: any = {};
    minDate: any = {};

    filteredOptions: Observable<string[]>;

    ninPatternList = {
        GB: "^[A-CEGHJ-PR-TW-Z]{1}[A-CEGHJ-NPR-TW-Z]{1}[0-9]{6}[A-D]{1}$",
        US: "^(?!666|000|9\\d{2})\\d{3}-(?!00)\\d{2}-(?!0{4})\\d{4}$",
        CA: "^(?!666|000|9\\d{2})\\d{3}-(?!00)\\d{2}-(?!0{4})\\d{4}$",
    }

    patternNin = this.ninPatternList[AppConstant.defaultCountryCode];

    WORKING_ARRANGEMENT: Array<any> = [{
        key: 'Annualised hours',
        value: 'Annualised hours'
    },{
        key: 'Compressed hours',
        value: 'Compressed hours'
    },{
        key: 'Flexible shifts',
        value: 'Flexible shifts'
    },{
        key: 'Homeworking',
        value: 'Homeworking'
    },{
        key: 'Job-share',
        value: 'Job-share'
    },{
        key: 'Staggered Hours',
        value: 'Staggered Hours'
    },{
        key: 'Term-time hours',
        value: 'Term-time hours'
    },{
        key: 'None',
        value: 'None'
    },{
        key: 'Other',
        value: 'Other'
    },{
        key: 'Prefer not to say',
        value: 'Prefer not to say'
    },];

    WORKING_PATTERN: Array<any> = [{
        key: 'Full-time',
        value: 'Full-time'
    },{
        key: 'Part-time',
        value: 'Part-time'
    },{
        key: 'Other',
        value: 'Other'
    },{
        key: 'Prefer not to say',
        value: 'Prefer not to say'
    },];

    loading_info: {
        emp: boolean;
        job_role: boolean;
        companies: boolean;
    } = {
        emp: false,
        job_role: false,
        companies: false
    };

    authUser$: User;
    authUserPreferences$: UserPreferences;
    @ViewChild("employmentForm", { static: false }) employmentForm: NgForm;
    @ViewChild("changeConfirmationModalRef") private changeConfirmationModalRef: GenericConfirmationModalComponent;
    initialFormValue;
    onDestroy$: Subject<void> = new Subject();
    formValueChanged: boolean = false;
    targetUrl: string;
    @ViewChild('ninInput') ninInputControl: NgModel;
    constructor(
        private router: Router,
        private authService: AuthService,
        private toastService: ToastService,
        private userService: UserService,
        private skillMatrixService: SkillMatrixService,
        private route: ActivatedRoute,
        private projectService: ProjectService,
        private featureExclusionUtility: FeatureExclusionUtility,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
    ) {
        this.minDate = {year: 1990, month: 1, day: 1};
        this.todayDate = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.router.events
        .pipe(
            takeUntil(this.onDestroy$),
            filter((event) => event instanceof NavigationStart)
        )
        .subscribe((event: NavigationStart) => {
            this.targetUrl = event.url;
            if (this.formValueChanged) {
                this.userService.formValueChanged.next(true);
                this.changeConfirmationModalRef.openConfirmationPopup({
                    headerTitle: 'Warning',
                    title: 'You have unsaved changes. Do you want to save them before leaving this page?',
                    leadingBtnLabel: 'Cancel',
                    confirmLabel: 'Save',
                    cancelLabel: 'No',
                    borderCancelButton: true,
                    hasCancel: true,
                });
            } else {
                this.userService.formValueChanged.next(false);
            }
        });
    }

    ngOnInit() {
        this.projectId = +this.route?.snapshot.params['projectId'] || undefined;
        if (!this.projectId) {
            this.authService.authUser.subscribe(data => {
                this.authUser$ = data;
                this.country_code = this.authUser$?.country_code || AppConstant.defaultCountryCode;
            });
            this.authService.authUserPreference.subscribe(data => {
                this.authUserPreferences$ = data;
            });
        }
        if(this.isShadowUser && this.projectId){
            //When called from shadow user wrapper, load project data along with getJobRoleAndCompany in sync.
            this.loadProject();
        } else {
            this.getJobRoleAndCompany();
        }
    }

    ngOnChanges() {
        this.intakeEmploymentDetails(this.employment_detail);
        setTimeout(() => {
            this.initialFormValue = this.employmentForm.form.value;
        }, 0);
    }

    ngAfterViewInit() {
        setTimeout(()=> {
            this.setNINPattern();
        },0)
    }

   setNINPattern(): void {
        const userPreference_countryCode = this.authUserPreferences$?.locale?.country;
        const countryCode = userPreference_countryCode 
            ? userPreference_countryCode?.toUpperCase()
            : null;

        const isValidCode = countryCode && this.ninPatternList[countryCode];
        const useUserCode = isValidCode && !AppConstant.developmentEnv;

        this.patternNin = useUserCode
            ? this.ninPatternList[countryCode]
            : this.ninPatternList[AppConstant.defaultCountryCode];

        setTimeout(() => {
            const control = this.ninInputControl?.control;
            if (control?.invalid) {
                control.markAsTouched();
                control.markAsDirty();
            }
        }, 0);
    }

    /**
     * Created By: Satyam Hardia
     * To be used only in case of Shadow User.
     * Be carefull while modifying this or any related code.
     */
    loadProject() {
        this.projectService.siteAdmin_getProject(this.projectId).subscribe((data: any) => {
            if(data.project && data.project.id){
                this.project = data.project;
                this.country_code = this.project.custom_field.country_code ? this.project.custom_field.country_code : AppConstant.defaultCountryCode;
                this.has_supply_chain_companies = this.project.custom_field.has_supply_chain_companies;
                this.supplyCompanies = [...(this.project.custom_field.supply_chain_companies || [])];
                this.getJobRoleAndCompany();
            }
        })
    }

    private intakeEmploymentDetails(employment_detail){
        if (employment_detail && employment_detail.id) {
            if(employment_detail.type_of_employment && employment_detail.type_of_employment.indexOf('Other') !== -1){
                employment_detail.other_type_of_employment = employment_detail.type_of_employment.replace(/Other|\(|\)/ig, '').trim();
                employment_detail.type_of_employment = 'Other';
            }
            if(employment_detail.job_role && employment_detail.job_role.indexOf('Other') !== -1){
                employment_detail.other_job_role = employment_detail.job_role.replace(/Other|\(|\)/ig, '').trim();
                employment_detail.job_role = 'Other';
            }
            if(employment_detail.start_date_with_employer){
                employment_detail.selectedStartDateWithEmployer = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+employment_detail.start_date_with_employer));
            }
            this.employment_detail = employment_detail;
            this.loading_info.emp = true;
            this.validateEmploymentDetail();
        }
    }

    private getJobRoleAndCompany(validate = true) {
        console.log('get job role', this.skillMatrix, this.contractorId);
        forkJoin({
            job_role_response: this.skillMatrix ? this.fetchContractorJobRoles(this.contractorId) : this.fetchJobRoles()
        }).subscribe((responseList: any) => {
            let errorKey = Object.keys(responseList).find(key => !responseList[key].success);
            if (responseList[errorKey]) {
                const message = responseList[errorKey].message || 'Failed to get data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: responseList[errorKey] });
                return;
            }
            this.consumeJobRoles(responseList.job_role_response);
            this.loading_info.job_role = true;
            (validate && this.validateEmploymentDetail());
        });
    }

    validateEmploymentDetail(){
        // console.log('check if all loaded?', this.loading_info);
        if((Object.values(this.loading_info).filter(v => !v)).length === 0){
            console.log('validating after loading');
            let employer_record = (this.companiesList || []).find(e => e.name && this.employment_detail.employer&& (e.name.toString().toLowerCase() === this.employment_detail.employer.toString().toLowerCase())) || {};
            if(!employer_record.id){
                this.employment_detail.employer = null;
                this.selected_employer_id = null;
            }
            if(this.employment_detail.employment_company){
                let employer_record = (this.companiesList || []).find(e => e.name && this.employment_detail.employment_company && (e.name.toString().toLowerCase() === this.employment_detail.employment_company.toString().toLowerCase())) || {};
                if(!employer_record.id){
                    this.employment_detail.employment_company = null;
                }
            }
            this.validateJobRole();
        }
    }

    companySelected($event){
        // console.log('update company select', $event);
        this.employment_detail.employer = $event?.record?.name;
        this.selected_employer_id = $event?.record?.id;
    }

    hasEmploymentDetails(){
        return (this.employment_detail.employer || this.employment_detail.job_role || this.employment_detail.type_of_employment);
    }

    @ViewChild('jobRoleSelector', { static: false })
    private jobRoleSelectorRef: NgSelectComponent;

    @ViewChild('countrySelector', { static: false })
    private countrySelectorRef: CountrySelectorComponent;

    @ViewChild('employerSelector', { static: false })
    private employerSelectorRef: CompanySelectorComponent;
    onCountryCodeChange($event){
        if(this.hasEmploymentDetails()) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Confirm',
                title: `Changing your country of work will clear your employment details. Would you like to continue?`,
                confirmLabel: 'Continue',
                onConfirm: () => {
                    this.country_code = $event.code;
                    this.resetFormOnCountryChange(!!$event.code);
                },
                onClose: () => {
                    this.countrySelectorRef.selectedCode = this.country_code;
                    this.countrySelectorRef.fillCountryName(true);
                },
            });
        } else {
            this.country_code = $event.code;
            this.resetFormOnCountryChange(!!$event.code);
        }
    }

    resetFormOnCountryChange(refreshList = true){
        // console.log('resetting data');
        this.employment_detail.job_role = null;
        this.employment_detail.employer = null;
        this.selected_employer_id = null;
        this.employment_detail.type_of_employment = null;
        this.employment_detail.employment_company = null;
        if(!refreshList){
            return true;
        }
        console.log('refresh lists')
        this.getJobRoleAndCompany(false);
    }

    validateJobRole(){
        let job_role_name = ((this.allowedJobRoles || []).find(jr => jr.name === this.employment_detail.job_role) || {}).name || null;
        // console.log('got job role name?', job_role_name);
        if(!job_role_name){
            this.employment_detail.job_role = job_role_name;
            this.jobRoleSelectorRef && this.jobRoleSelectorRef.clearModel();
        }
    }

    formClearAction($event){
        return this.resetFormOnCountryChange(false);
    }

    getCountryFullName(code){
        if(!this.countrySelectorRef){
            return '-';
        }
        return ((this.countrySelectorRef.countries || []).find(c => c.code === code) || {}).name || '-';
    }

    showEmploymentStartDate(){
        return this.featureExclusionUtility.showEmploymentStartDate(this.country_code);
    }
    showTypeOfEmployment(){
        return this.featureExclusionUtility.showTypeOfEmployment(this.country_code);
    }
    showMinWage(){
        return this.featureExclusionUtility.showMinWage(this.country_code);
    }
    showNIN(){
        return this.featureExclusionUtility.showNIN(this.country_code);
    }
    showEmpNbr(){
        return this.featureExclusionUtility.showEmpNbr(this.country_code);
    }
    isEmpNbrRequired(){
        return this.featureExclusionUtility.isEmpNbrMandatory(this.country_code);
    }

    otherTypeOfEmpNotRequired(){
        return (this.employment_detail.type_of_employment !== 'Other');
    }

    otherJobRoleNotRequired(){
        return (this.employment_detail.job_role !== 'Other');
    }

    // roleCategoryChanged(ddSelection: string){
    //     if(JobRoleByCategories[ddSelection]){
    //         this.allowedJobRoles = JobRoleByCategories[ddSelection];
    //         let index = this.allowedJobRoles.findIndex(v => v === this.employment_detail.job_role);
    //         if(index === -1){
    //             this.employment_detail.job_role = null;
    //         }
    //     }
    // }

    resetForOther(ddSelection: string, key: string){
        if(ddSelection !== 'Other'){
            this.employment_detail[key] = null;
        }
        if(!this.showEmploymentCompanyPicker()){
            this.employment_detail.employment_company = null;
        }
    }

    onChangeSelectedStartDateWithEmployer(){
        if(this.employment_detail.selectedStartDateWithEmployer){
            this.employment_detail.start_date_with_employer = this.ngbMomentjsAdapter.ngbDateToDayJs(this.employment_detail.selectedStartDateWithEmployer).valueOf();
        }
    }

    save(form: any, valueChanges, targetUrl?) {
        if (!form.valid) {
            return false;
        }
        if(!valueChanges){
            this.formValueChanged = false;
            this.userService.formValueChanged.next(false);
        }
        this.emitData(targetUrl);
    }

    /* old employment details emitData */
    /* private emitData(){
        if(this.employment_detail.selectedStartDateWithEmployer){
            this.employment_detail.start_date_with_employer = this.ngbMomentjsAdapter.ngbDateToDayJs(this.employment_detail.selectedStartDateWithEmployer).valueOf();
        }
        let employment_detail = Object.assign({}, this.employment_detail);
        if(employment_detail.type_of_employment){
            employment_detail.type_of_employment = `${employment_detail.type_of_employment}${(employment_detail.other_type_of_employment) ? ` (${employment_detail.other_type_of_employment})` : ''}`;
        }
        if(employment_detail.job_role){
            employment_detail.job_role = `${employment_detail.job_role}${(employment_detail.other_job_role) ? ` (${employment_detail.other_job_role})` : ''}`;
        }

        employment_detail.employer_id = (this.selected_employer_id && this.selected_employer_id.id) || this.selected_employer_id;
        console.log(employment_detail);
        this.onSave.emit({
            employment_detail, parent_company: this.selected_employer_id,
            country_code: this.country_code,
            nin: this.nin,
            user_detail: {
                parent_company: this.selected_employer_id,
                country_code: this.country_code,
                nin: this.nin,
            }
        });
    } */


    private emitData(targetUrl?) {
        if(this.employment_detail.selectedStartDateWithEmployer){
            this.employment_detail.start_date_with_employer = this.ngbMomentjsAdapter.ngbDateToDayJs(this.employment_detail.selectedStartDateWithEmployer).valueOf();
        }
        const employment_detail = Object.assign({}, this.employment_detail);
        if(employment_detail.type_of_employment){
            employment_detail.type_of_employment = `${employment_detail.type_of_employment}${(employment_detail.other_type_of_employment) ? ` (${employment_detail.other_type_of_employment})` : ''}`;
        }
        if(employment_detail.job_role){
            employment_detail.job_role = `${employment_detail.job_role}${(employment_detail.other_job_role) ? `(${employment_detail.other_job_role})` : ''}`;
        }

        employment_detail.employer_id = (this.selected_employer_id && this.selected_employer_id.id) || this.selected_employer_id;
        const employment_data = {
            employer: employment_detail.employer,
            reporting_to: employment_detail.reporting_to,
            job_role: employment_detail.job_role,
            type_of_employment: employment_detail.type_of_employment,
            employment_company: employment_detail.employment_company,
            operative_type: employment_detail.operative_type,
            parent_company: this.selected_employer_id.id ?? this.selected_employer_id,
            start_date_with_employer: employment_detail.start_date_with_employer,
            earn_mlw_e783: employment_detail.earn_mlw_e783,
            employee_number: employment_detail.employee_number,
        }
        const personal_data = {
            parent_company: this.selected_employer_id.id ?? this.selected_employer_id,
            country_code: this.country_code,
            nin: this.nin,
        }
        if(!this.nin) {
           delete personal_data.nin;
        }
        console.log(employment_data);
        /* saveMyEmploymentDetail API payload */
        const payload_1 = {
            employment_detail,
            parent_company: this.selected_employer_id,
            country_code: this.country_code,
            nin: this.nin,
            user_detail: {
                parent_company: this.selected_employer_id,
                country_code: this.country_code,
                nin: this.nin,
            },
            targetUrl:targetUrl,
        }
        /* saveMyEmploymentDetailV2 */
        const payload_2 = {
            employment_data: employment_data,
            personal_data: personal_data,
            targetUrl:targetUrl,
        }
        this.onSave.emit( this.saveEmploymentDetailV2 ? payload_2 : payload_1);
    }

    skipStep(){
        this.emitData();
    }

    private fetchJobRoles(){
        return this.userService.getJobRoles({country_code: (this.country_code || undefined)});
    }

    private fetchContractorJobRoles(companyId){
        return this.skillMatrixService.companySkillsList(companyId).pipe(map((data: any) => {
            if (data.success) {
                data.jobrolelist = data.records || [];
            }
            return data;
        }));
    }

    private consumeJobRoles(data){
        this.allowedJobRoles = data.jobrolelist;
    }

    // private fetchCompanies() {
    //     return this.userService.getEmployer((this.country_code || undefined));
    // }

    // private consumeCompanies(data){
    //     console.log("companiesList", data.employerlist);
    //     this.companiesList = data.employerlist;
    //     if(this.supplyCompanies.length){
    //         this.companiesList = this.companiesList.filter(c => this.supplyCompanies.includes(c.id));
    //     }
    // }

    showEmploymentCompanyPicker(){
        return ['Agency'].includes(this.employment_detail.type_of_employment);
    }

    employmentCompanySelected($event){
        // console.log('got emp company selection', $event);
        this.employment_detail.employment_company = $event?.record?.name;
    }

    get employmentCompanyFieldLabel(){
        let t = this.employment_detail.type_of_employment;
        if(t === 'Agency'){
            return 'Agency';
        }
        return '';
    }

    goBack() {
        this.back.emit();
    }

    confirmationVal(confirmVal) {
        if (confirmVal.buttonLabel === "Save") {
          this.formValueChanged = false;
          this.save(this.employmentForm, true, this.targetUrl);
        } else if (confirmVal.buttonLabel === "No") {
          this.formValueChanged = false;
          this.employmentForm.form.patchValue(this.initialFormValue);
          this.router.navigate([this.targetUrl]);
        }
    }

    ngOnDestroy(): void {
        this.onDestroy$.next();
        this.onDestroy$.complete();
        this.userService.formValueChanged.next(false);
    }
}

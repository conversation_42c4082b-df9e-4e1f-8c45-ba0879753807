import {
  Component<PERSON><PERSON><PERSON>,
  TestBed,
  fakeAsync,
  tick,
} from "@angular/core/testing";
import { OnBoardPersonalFormComponent } from "./1_personal.form.component";
import { FormsModule, NgForm, ReactiveFormsModule } from "@angular/forms";
import {
  NgbModule,
  NgbDatepickerConfig,
  NgbModal,
  NgbModalConfig,
  NgbDate,
} from "@ng-bootstrap/ng-bootstrap";
import { HttpClientTestingModule } from "@angular/common/http/testing";
import { RouterTestingModule } from "@angular/router/testing";
import {
  CookieService,
  DoNotAskAgainService,
  ResourceService,
  ToastService,
} from "@app/core";
import { UserService } from "@app/core";
import { AuthService } from "@app/core";
import { HttpService } from "@app/core";
import { ActivatedRoute, Router } from "@angular/router";
import { of } from "rxjs";
import { User } from "@app/core";
import {
  BlockLoader<PERSON>omponent,
  CropImageUploaderComponent,
  IModalComponent,
} from "@app/shared";
import { WebcamComponent } from "@app/shared";
import { GenericConfirmationModalComponent } from "@app/shared";
import { NgbMomentjsAdapter } from "@app/core/ngb-moment-adapter";
import { CommonModule, TitleCasePipe, UpperCasePipe } from "@angular/common";
import { NgSelectModule } from "@ng-select/ng-select";
import { FileUploadModule } from "ng2-file-upload";
import { CapitalizeFirstCharPipe } from "@app/shared/pipe-and-directive";

describe("OnBoardPersonalFormComponent", () => {
  let component: OnBoardPersonalFormComponent;
  let fixture: ComponentFixture<OnBoardPersonalFormComponent>;
  let userService: UserService;
  let toastService: ToastService;
  let router: Router;
  let modalService: NgbModal;

  const mockUser: User = {
    id: 1,
    first_name: "John",
    last_name: "Doe",
    email: "<EMAIL>",
    dob: "1990-01-01",
    country: "US",
    marital_status: "Single",
    sexual_orientation: "Heterosexual",
    disability: "No",
    ethnicity: "White",
    subethnicity: "British",
    religion: "Christian",
    caring_responsibilities: "None",
    selectedDob: new NgbDate(1990, 1, 1),
    user_onboard_status: {
      personal: false,
      address: false,
      health_assessment: false,
      medical_assessments: false,
      employment: false,
      competencies: false,
    },
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        OnBoardPersonalFormComponent,
        CropImageUploaderComponent,
        WebcamComponent,
        GenericConfirmationModalComponent,
        BlockLoaderComponent,
        IModalComponent,
        CapitalizeFirstCharPipe,
      ],
      imports: [
        FormsModule,
        NgbModule,
        HttpClientTestingModule,
        RouterTestingModule,
        NgSelectModule,
        CommonModule,
        ReactiveFormsModule,
        FileUploadModule,
      ],
      providers: [
        UserService,
        CookieService,
        ToastService,
        ResourceService,
        AuthService,
        HttpService,
        NgbDatepickerConfig,
        NgbModalConfig,
        NgbModal,
        NgbMomentjsAdapter,
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({}),
            snapshot: {
              queryParams: {},
            },
          },
        },
        TitleCasePipe,
        UpperCasePipe,
        DoNotAskAgainService,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(OnBoardPersonalFormComponent);
    component = fixture.componentInstance;
    userService = TestBed.inject(UserService);
    toastService = TestBed.inject(ToastService);
    router = TestBed.inject(Router);
    modalService = TestBed.inject(NgbModal);

    component.personal = mockUser;
    component.isProcessing = false;
    component.showExitButton = true;
    component.isShadowUser = false;

    fixture.detectChanges();
  });

  it("should create component", () => {
    expect(component).toBeTruthy();
  });

  it("should initialize form with user data", () => {
    expect(component.personal).toEqual(mockUser);
  });

  it("should handle form submission", () => {
    spyOn(component.onSave, "emit");
    const form = { valid: true, value: mockUser };

    component.save(form, true);
    expect(component.onSave.emit).toHaveBeenCalled();
  });

  it("should handle invalid form submission", () => {
    spyOn(component.onSave, "emit");
    const form = { valid: false, value: mockUser };

    component.save(form, true);
    expect(component.onSave.emit).not.toHaveBeenCalled();
  });

  it("should handle exit confirmation", () => {
    spyOn(component.onExit, "emit");
    component.exit();
    expect(component.onExit.emit).toHaveBeenCalled();
  });

  it("should handle photo upload", () => {
    spyOn(component.saveProfilePicture, "emit");
    component.saveProfilePicture.emit();
    expect(component.saveProfilePicture.emit).toHaveBeenCalled();
  });

  it("should clean up subscriptions on destroy", () => {
    spyOn(component.onDestroy$, "next");
    spyOn(component.onDestroy$, "complete");

    component.ngOnDestroy();
    expect(component.onDestroy$.next).toHaveBeenCalled();
    expect(component.onDestroy$.complete).toHaveBeenCalled();
  });

  it("should call intakePersonalDetails and initialComponent when personal data exists", () => {
    spyOn(component, "intakePersonalDetails");
    spyOn(component, "initialComponent");
    component.personalForm = {
      form: {
        getRawValue: jasmine.createSpy("getRawValue").and.returnValue(mockUser),
      },
    } as any;

    component.personal = mockUser;
    component.ngOnChanges();

    expect(component.intakePersonalDetails).toHaveBeenCalledWith(mockUser);
    expect(component.initialComponent).toHaveBeenCalled();
  });

  it("should not set initialFormValue when personal data does not exist", () => {
    spyOn(component, "intakePersonalDetails");
    spyOn(component, "initialComponent");
    component.personalForm = {
      form: {
        getRawValue: jasmine.createSpy("getRawValue").and.returnValue(mockUser),
      },
    } as any;

    component.personal = undefined;
    component.ngOnChanges();

    expect(component.initialFormValue).toBeUndefined();
  });

  it("should set initialFormValue after timeout when personal data exists", fakeAsync(() => {
    spyOn(component, "intakePersonalDetails");
    spyOn(component, "initialComponent");
    component.personalForm = {
      form: {
        getRawValue: jasmine.createSpy("getRawValue").and.returnValue(mockUser),
      },
    } as any;

    component.personal = mockUser;
    component.ngOnChanges();

    tick(0);
    expect(component.initialFormValue).toEqual(mockUser);
  }));

  it("should handle form value changes correctly", fakeAsync(() => {
    spyOn(component, "intakePersonalDetails");
    spyOn(component, "initialComponent");
    const updatedUser = { ...mockUser, first_name: "Updated Name" };
    const getRawValueSpy = jasmine
      .createSpy("getRawValue")
      .and.returnValue(updatedUser);
    component.personalForm = {
      form: {
        getRawValue: getRawValueSpy,
      },
    } as any;

    component.personal = mockUser;
    component.ngOnChanges();

    tick(0);
    expect(component.initialFormValue).toEqual(updatedUser);
  }));

  it("should set img_link and isUploadDone when profile_pic_ref has id and sm_url", () => {
    const mockProfilePic = {
      id: 123,
      sm_url: "https://example.com/small.jpg",
      file_url: "https://example.com/large.jpg",
    };
    component.personal = { ...mockUser, profile_pic_ref: mockProfilePic };

    component.initialComponent();

    expect(component.img_link).toBe("https://example.com/small.jpg");
    expect(component.isUploadDone).toBe(true);
  });

  it("should set img_link to file_url when sm_url is not available", () => {
    const mockProfilePic = {
      id: 123,
      file_url: "https://example.com/large.jpg",
    };
    component.personal = { ...mockUser, profile_pic_ref: mockProfilePic };

    component.initialComponent();

    expect(component.img_link).toBe("https://example.com/large.jpg");
    expect(component.isUploadDone).toBe(true);
  });

  it("should not set img_link and isUploadDone when profile_pic_ref is missing", () => {
    component.personal = { ...mockUser };
    component.img_link = undefined;
    component.isUploadDone = false;

    component.initialComponent();

    expect(component.img_link).toBeUndefined();
    expect(component.isUploadDone).toBe(false);
  });

  it("should not set img_link and isUploadDone when profile_pic_ref has no id", () => {
    const mockProfilePic = {
      sm_url: "https://example.com/small.jpg",
      file_url: "https://example.com/large.jpg",
    };
    component.personal = { ...mockUser, profile_pic_ref: mockProfilePic };
    component.img_link = undefined;
    component.isUploadDone = false;

    component.initialComponent();

    expect(component.img_link).toBeUndefined();
    expect(component.isUploadDone).toBe(false);
  });

  it("should set error when date is less than 12 years ago", () => {
    const personalForm = {
      form: {
        controls: {
          selectedDob: {
            setErrors: jasmine.createSpy("setErrors"),
          },
        },
      },
    };
    const toastServiceSpy = TestBed.inject(
      ToastService
    ) as jasmine.SpyObj<ToastService>;
    spyOn(toastServiceSpy, "show");

    const tenYearsAgo = new NgbDate(
      new Date().getFullYear() - 10,
      new Date().getMonth() + 1,
      new Date().getDate()
    );

    component.onDateSelection(tenYearsAgo, personalForm);

    expect(toastServiceSpy.show).toHaveBeenCalledWith(
      toastServiceSpy.types.ERROR,
      "Please enter a valid date of birth."
    );
    expect(
      personalForm.form.controls.selectedDob.setErrors
    ).toHaveBeenCalledWith({ incorrect: true });
  });

  it("should clear errors when date is more than 12 years ago", () => {
    const personalForm = {
      form: {
        controls: {
          selectedDob: {
            setErrors: jasmine.createSpy("setErrors"),
          },
        },
      },
    };
    const toastServiceSpy = TestBed.inject(
      ToastService
    ) as jasmine.SpyObj<ToastService>;
    spyOn(toastServiceSpy, "show");

    const twentyYearsAgo = new NgbDate(
      new Date().getFullYear() - 20,
      new Date().getMonth() + 1,
      new Date().getDate()
    );

    component.onDateSelection(twentyYearsAgo, personalForm);

    expect(toastServiceSpy.show).not.toHaveBeenCalled();
    expect(
      personalForm.form.controls.selectedDob.setErrors
    ).toHaveBeenCalledWith(null);
  });

  it("should handle exact 12 years ago date correctly", () => {
    const personalForm = {
      form: {
        controls: {
          selectedDob: {
            setErrors: jasmine.createSpy("setErrors"),
          },
        },
      },
    };
    const toastServiceSpy = TestBed.inject(
      ToastService
    ) as jasmine.SpyObj<ToastService>;
    spyOn(toastServiceSpy, "show");

    const twelveYearsAgo = new NgbDate(
      new Date().getFullYear() - 12,
      new Date().getMonth() + 1,
      new Date().getDate()
    );

    component.onDateSelection(twelveYearsAgo, personalForm);

    expect(toastServiceSpy.show).not.toHaveBeenCalled();
    expect(
      personalForm.form.controls.selectedDob.setErrors
    ).toHaveBeenCalledWith(null);
  });

  it("should return true when title is not 'Other'", () => {
    component.personal = { ...mockUser, title: "Mr" };
    expect(component.otherTitleNotRequired()).toBeTruthy();
  });

  it("should return false when title is 'Other'", () => {
    component.personal = { ...mockUser, title: "Other" };
    expect(component.otherTitleNotRequired()).toBeFalsy();
  });

  it("should return true when title is undefined", () => {
    component.personal = { ...mockUser, title: undefined };
    expect(component.otherTitleNotRequired()).toBeTruthy();
  });

  it("should return true when title is null", () => {
    component.personal = { ...mockUser, title: null };
    expect(component.otherTitleNotRequired()).toBeTruthy();
  });

  it("should set the specified field to null when title is not 'Other'", () => {
    component.personal = {
      ...mockUser,
      title: "Mr",
      other_title: "Custom Title",
    };
    component.resetForOther("other_title");
    expect(component.personal.other_title).toBeNull();
  });

  it("should not modify the specified field when title is 'Other'", () => {
    component.personal = {
      ...mockUser,
      title: "Other",
      other_title: "Custom Title",
    };
    component.resetForOther("other_title");
    expect(component.personal.other_title).toBe("Custom Title");
  });

  it("should handle multiple fields correctly", () => {
    component.personal = {
      ...mockUser,
      title: "Mr",
      other_title: "Custom Title",
    };
    component.resetForOther("other_title");
    expect(component.personal.other_title).toBeNull();
  });

  it("should not throw error when field does not exist", () => {
    component.personal = { ...mockUser, title: "Mr" };
    expect(() => component.resetForOther("non_existent_field")).not.toThrow();
  });

  it("should call clickFileInput on cropImgUploader when fileInput is called", () => {
    spyOn(component.cropImgUploader, "clickFileInput");
    component.fileInput();
    expect(component.cropImgUploader.clickFileInput).toHaveBeenCalled();
  });

  it("should set img_link when event has previewImage", () => {
    const mockEvent = {
      previewImage: "data:image/jpeg;base64,test_image_data",
    };

    component.previewCroppedImage(mockEvent);

    expect(component.img_link).toBe("data:image/jpeg;base64,test_image_data");
  });

  it("should not set img_link when event is null", () => {
    component.img_link = "previous_image";
    component.previewCroppedImage(null);

    expect(component.img_link).toBe("previous_image");
  });

  it("should not set img_link when event has no previewImage", () => {
    component.img_link = "previous_image";
    component.previewCroppedImage({});

    expect(component.img_link).toBe("previous_image");
  });

  it("should handle undefined event", () => {
    component.img_link = "previous_image";
    component.previewCroppedImage(undefined);

    expect(component.img_link).toBe("previous_image");
  });

  it("should call open() on webcamComponentRef when choosePhoto is called", () => {
    spyOn(component['webcamComponentRef'], "open");
    component.choosePhoto();
    expect(component['webcamComponentRef'].open).toHaveBeenCalled();
  });

  it("should call useBase64 on cropImgUploader with the event when getCapturedImage is called", () => {
    spyOn(component.cropImgUploader, "useBase64");
    const mockEvent = { base64: "data:image/jpeg;base64,test_image_data" };
    component.getCapturedImage(mockEvent);
    expect(component.cropImgUploader.useBase64).toHaveBeenCalledWith(mockEvent);
  });

  it("should handle null event in getCapturedImage", () => {
    spyOn(component.cropImgUploader, "useBase64");
    component.getCapturedImage(null);
    expect(component.cropImgUploader.useBase64).toHaveBeenCalledWith(null);
  });

  it("should handle undefined event in getCapturedImage", () => {
    spyOn(component.cropImgUploader, "useBase64");
    component.getCapturedImage(undefined);
    expect(component.cropImgUploader.useBase64).toHaveBeenCalledWith(undefined);
  });

  it("should handle event without base64 property in getCapturedImage", () => {
    spyOn(component.cropImgUploader, "useBase64");
    const mockEvent = { someOtherProperty: "value" };
    component.getCapturedImage(mockEvent);
    expect(component.cropImgUploader.useBase64).toHaveBeenCalledWith(mockEvent);
  });

  it("should handle Save button click in confirmationVal", () => {
    component.personalForm = {
      form: {
        patchValue: jasmine.createSpy("patchValue"),
        getRawValue: jasmine.createSpy("getRawValue").and.returnValue(mockUser),
      },
    } as any;
    component.initialFormValue = mockUser;
    component.targetUrl = "/dashboard";
    spyOn(component, "save");
    spyOn(router, "navigate");

    const confirmVal = { buttonLabel: "Save" };
    component.confirmationVal(confirmVal);

    expect(component.formValueChanged).toBeFalsy();
    expect(component.save).toHaveBeenCalledWith(
      component.personalForm,
      true,
      component.targetUrl
    );
  });

  it("should handle No button click in confirmationVal", () => {
    component.personalForm = {
      form: {
        patchValue: jasmine.createSpy("patchValue"),
        getRawValue: jasmine.createSpy("getRawValue").and.returnValue(mockUser),
      },
    } as any;
    component.initialFormValue = mockUser;
    component.targetUrl = "/dashboard";
    spyOn(component, "save");
    spyOn(router, "navigate");

    const confirmVal = { buttonLabel: "No" };
    component.confirmationVal(confirmVal);

    expect(component.formValueChanged).toBeFalsy();
    expect(component.personalForm.form.patchValue).toHaveBeenCalledWith(
      component.initialFormValue
    );
    expect(router.navigate).toHaveBeenCalledWith([component.targetUrl]);
  });

  it("should not do anything for unknown button labels in confirmationVal", () => {
    component.personalForm = {
      form: {
        patchValue: jasmine.createSpy("patchValue"),
        getRawValue: jasmine.createSpy("getRawValue").and.returnValue(mockUser),
      },
    } as any;
    component.initialFormValue = mockUser;
    component.targetUrl = "/dashboard";
    spyOn(component, "save");
    spyOn(router, "navigate");

    const confirmVal = { buttonLabel: "Unknown" };
    component.confirmationVal(confirmVal);

    expect(component.formValueChanged).toBeFalsy();
    expect(component.save).not.toHaveBeenCalled();
    expect(component.personalForm.form.patchValue).not.toHaveBeenCalled();
    expect(router.navigate).not.toHaveBeenCalled();
  });

  it("should handle successful file upload with all required properties", () => {
    const mockEvent = {
      userFile: {
        id: 123,
        file_url: "https://example.com/image.jpg",
      },
    };
    spyOn(component.saveProfilePicture, "emit");
    spyOn(component.saveImage, "emit");

    component.uploadDone(mockEvent);

    expect(component.userFile).toEqual(mockEvent.userFile);
    expect(component.img_link).toBe("https://example.com/image.jpg");
    expect(component.isUploadDone).toBe(true);
    expect(component.saveProfilePicture.emit).toHaveBeenCalledWith({
      profile_pic_ref: 123,
      profile_pic_expiry: jasmine.any(Number),
    });
    expect(component.saveImage.emit).toHaveBeenCalledWith(
      "https://example.com/image.jpg"
    );
  });

  it("should not process upload when event is null", () => {
    spyOn(component.saveProfilePicture, "emit");
    spyOn(component.saveImage, "emit");

    component.uploadDone(null);

    expect(component.userFile).toBeUndefined();
    expect(component.img_link).toBeUndefined();
    expect(component.saveProfilePicture.emit).not.toHaveBeenCalled();
    expect(component.saveImage.emit).not.toHaveBeenCalled();
  });

  it("should set profile_pic_expiry to 2 years from now", () => {
    const mockEvent = {
      userFile: {
        id: 123,
        file_url: "https://example.com/image.jpg",
      },
    };
    spyOn(component.saveProfilePicture, "emit");
    const now = new Date();
    const twoYearsFromNow = new Date(now);
    twoYearsFromNow.setFullYear(now.getFullYear() + 2);

    component.uploadDone(mockEvent);

    const emittedValue = (
      component.saveProfilePicture.emit as jasmine.Spy
    ).calls.mostRecent().args[0];
    const emittedExpiry = new Date(emittedValue.profile_pic_expiry);
    expect(emittedExpiry.getFullYear()).toBe(twoYearsFromNow.getFullYear());
  });
});

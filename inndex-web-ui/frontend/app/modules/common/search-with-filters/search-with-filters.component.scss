$select:hsl(0, 0%, 95%);
$primary: #9D9D9D;
$tag:#0C69FE;


.search-input {
    height: 35px; margin: 5px 0;
    min-width: 210px;
    background-color: var(--bright-gray);
    font-size: 12px;
    border: none;
    box-shadow: none;
}


.tag {
    background-color: #0C69FE;
    border-radius: 9999px;
    color: #F2F2F2;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 17px;
    width: 17px;
    text-align: center;
    font-size: 12px;
}

.filter-selection {
    background: white;
    padding-bottom: 0px;
    display: flex;
    border-radius: 5px;
    border: 1px solid $primary;
    height: 30px !important;
    & > span{
        padding: 5px 10px;
    }
}
.filter-selection.show.dropdown {
    border: 1px solid $select;
    background: $select;
}
.dropdown-menu[x-placement^=top], .dropdown-menu[x-placement^=right], .dropdown-menu[x-placement^=bottom], .dropdown-menu[x-placement^=left] {
    left: -10px !important;
}

button.clear-btn{
    background-color: #fff;
    border:1px solid $primary;
    border-radius: 4px;
    padding: 4px 6px;
    min-height: 30px;
    box-sizing: border-box !important;
    height: 30px;

}
button.clear-btn:disabled{
    background-color: $select;
    border: none;

}
.cross{
    font-size: 15px;
    height: 15px;
    background: grey;
    color: white;
    border-radius: 9999px;
    opacity: 0.4;
    cursor: pointer;
    font-weight: 300;
}
span.ng-placeholder.custom-placeholder.custom-placeholder {
    display: flex !important;
    align-items: center;
    gap:10px;
}
.ng-dropdown-panel .ng-dropdown-panel-items {
    display: block;
    height: auto;
    box-sizing: border-box;
    max-height: 240px;
    overflow-y: auto;
    min-width: fit-content;
}

.search-filters .ng-select-container {
    max-height: 30px;
}

@media only screen and (max-width: 768px) {
    input.ngx-search[type="search"] {
        height: 50px !important;
        font-size: 18px;
    }
    .filter-selection {
        height: 50px !important;

        &.dropdown {
            padding: 5px 25px !important;
            font-size: 18px;
        }

        .table-dropdown-menu {
            font-size: 18px;
            line-height: 32px;
        }
    }
    button.clear-btn {
        height: 50px !important;
        padding: 4px 15px;
    }
    .search-filters .ng-select-container {
        max-height: 50px !important;
    }
}

<div class="row horizontal-center mb-3 ml-0" [ngClass]="customClasses" style="height: auto; font-size: 14px; gap: 10px;">
   <div [class]="searchWidth" [ngClass]="{'text-search pl-3 pr-0':true, 'd-none':searchDisabled,'horizontal-center':!searchDisabled}">
        <span class="material-symbols-outlined large-font">search</span>
        <input class="w-100 ngx-search mb-2 d-inline-flex"
               #textSearch
               type="search"
               placeholder='Search'
               [disabled] = 'loading'
        />
    </div>
    <div *ngIf="renderFilterButton()" ngbDropdown class="filter-selection ">
        <span class="horizontal-center cursor-pointer gap-2 not-dropdown" ngbDropdownToggle>
            <span style="font-size: 18px;" class="material-symbols-outlined">tune</span><span>Filter</span>
        </span>
        <ul ngbDropdownMenu class="table-dropdown-menu">
            <ng-template ngFor let-filter [ngForOf]="filterData" let-i="index">
                <li *ngIf="filter.enabled" ngbDropdownItem class="cursor-pointer" (click)="filterSelectionHandler(i,filter.name)">
                    <span>Filter By {{filter.name | titlecase}}</span>
                </li>
            </ng-template>
        </ul>
    </div>

    <ng-container *ngFor="let filter of filterData; let i = index">
        <div [ngClass]="{'horizontal-center': filter.state,'d-none':!filter.state}" #filterContainer>
            <ng-select
                #ngSelectRef
                [ngClass]="{
                    'w-100 filter-v2-select rounded-select filter-select mb-2': true,
                    'd-inline-block': filter.state,
                    'd-none': !filter.state,
                    'status': filter.list?.length <= 7
                }"
                [searchable]="false"
                [clearable]="false"
                [closeOnSelect]="false"
                [multiple]="true"
                [items]="loading?[]:filter.list"
                bindLabel="name"    
                (change)="emitFilterData($event)"
                [placeholder]="filter.name | titlecase"
                [(ngModel)]="selected[filter.name]"
                [id]="filter.name"
                [loading]="loading"
                [searchFn]="searchFnWrapper(filter.labelKey)"
                name="filter-select-{{i}}"
                >
                <ng-template ng-multi-label-tmp>
                    <span style="display:block;" class="horizontal-center" class="ng-placeholder custom-placeholder">
                        <span (click)="clearFilter($event, i,filter.name)" class="cross material-symbols-outlined">
                            close_small
                        </span>
                        <span>{{filter.name | titlecase}}</span>
                        <span class="tag">{{this.selected[filter.name].length}}</span>
                    </span>
                </ng-template>
                <ng-template *ngIf="filter.list?.length > 7" ng-header-tmp>
                    <div style="width:100%; max-width:100%;" class="text-search horizontal-center px-2">
                        <span class="material-symbols-outlined large-font">search</span>
                        <input placeholder="Search" style="width: 100%; line-height: 24px" class="ngx-search" type="search" (input)="ngSelectRef.filter($event.target.value)" />
                    </div>
                </ng-template>
                <ng-container *ngIf = '!loading'>
                    <ng-template ng-option-tmp let-item="item" class="horizontal-center" let-item$="item$" let-index="index" let-search="searchTerm">
                        <input id="item-{{index}}" name="search-item-{{index}}" type="checkbox" [ngModel]="item$.selected" class="mr-1" />
                        <span [style.color]="item.color" style="margin-left: 5px;">
                            <!--Utkarsh -  TODO: add a label key in input filterdata object to render the item efficiently -->
                            {{ filter.labelKey ? item[filter.labelKey] : ( item?.title || item?.label || item?.name || item?.alternate_phrase || item?.value || item?.status_message || item ) }}
                        </span>
                    </ng-template>
                </ng-container>
                <ng-container *ngIf="loading">
                    Loading...
                </ng-container>
            </ng-select>
        </div>
    </ng-container> 

    <div *ngIf="shouldShowClearAllButton()">
        <button class="clear-btn" (click)="clearFilters()">
            Clear All
        </button>
    </div>
</div>
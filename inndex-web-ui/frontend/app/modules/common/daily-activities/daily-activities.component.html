<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav *ngIf="!isProjectPortal" [employer]="employer" [companyResolverResponse]="companyResolverResponse" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="col-sm-12 my-3 outer-border" *ngIf="!loadingActivities">
                <div class="outer-border-radius">
            <div class="mb-2 mx-3 flex-wrap gap-8 d-flex justify-content-between">
                <div class="col-md-5 d-inline-block p-0">
                    <h5>Total {{ daPhrase }} <small>({{ page.totalElements }})</small></h5>
                </div>
                <action-button
                    [actionList]="actionButtonMetaData.actionList"
                    (selectedActionEmmiter)="onActionSelection($event)"
                    [hideNewFeatureBtn]="!this.isProjectPortal"
                    [newFeatureTitle]="'New Report'"
                    (onOpenAddNew)="openActivityReport()">
                </action-button>
                <!-- <div class="float-right mr-3">
                    <div ngbDropdown class="d-inline-block float-right mb-1 mr-1" style="z-index: 999;" placement="bottom-right">
                        <button class="btn btn-sm btn-im-helmet" id="dropdownDlReport" ngbDropdownToggle> <i class="far fa-calendar-alt"></i>
                            Download Report</button>
                        <div ngbDropdownMenu aria-labelledby="dropdownDlReport">
                            <div class="form-inline d-flex justify-content-between pl-2 pr-2" style="font-size:13px">
                                <ng-select [items]="downloadOptions"
                                    bindLabel="name"
                                    bindValue="value"
                                    name="xlsxReportType"
                                    [(ngModel)]="xlsxReportType" required style="width: 55%;">
                                </ng-select>
                            </div>
                            <div class="mt-2 form-inline d-flex pl-2 pr-2" style="font-size:13px" *ngIf="xlsxReportType === 'activity_report'">
                                <div class="p-0"><b>Choose activity report format</b></div>
                                <div class="custom-control custom-radio mr-2 ml-3">
                                    <input type="radio" class="custom-control-input" name="activity_report_format" id="activity_report_xlsx"
                                           required value="activity_report_xlsx" [(ngModel)]="activityReportFormat"/>
                                    <label class="custom-control-label radio-btn-label" for="activity_report_xlsx">XLSX</label>
                                </div>
                                <div class="custom-control custom-radio mr-2 ml-3">
                                    <input type="radio" class="custom-control-input" name="activity_report_format" id="activity_report_pdf"
                                           required value="activity_report_pdf" [(ngModel)]="activityReportFormat"/>
                                    <label class="custom-control-label radio-btn-label" for="activity_report_pdf">PDF
                                        <i class="ml-1 fa fa-info-circle small mr-5" ngbTooltip="This will send all reports for the given date range to your account email address." [openDelay]="200" [closeDelay]="500"></i>
                                    </label>
                                </div>
                            </div>
                            <download-report-modal-box [xlsxOnly]="'true'" (downloadModal)="initiateDownload($event)"></download-report-modal-box>
                        </div>
                    </div>
                    <div ngbDropdown class="d-inline-block float-right mb-1 mr-1" placement="bottom-right">
                        <button class="btn btn-sm btn-im-helmet" id="dropdownDlReport1" ngbDropdownToggle>
                            <i class="fas fa-cog ml-1 dropdown-toggle-no-caret cowSetting"></i>
                        </button>
                        <div ngbDropdownMenu aria-labelledby="dropdownDlReport1">
                            <li class="dropdown-item cursor-pointer" (click)="dailyActivitiesImportModal()">Pre-load Activities</li>
                            <li class="dropdown-item cursor-pointer" (click)="metaPlantsImportModal()">Pre-load Plant/Machinery</li>
                        </div>
                    </div>
                    <button class="btn btn-sm btn-im-helmet float-right mb-1 mr-1" *ngIf="isProjectPortal" (click)="openActivityReport()">New Report</button>
                </div> -->
            </div>
            <div class="ml-3">
                <search-with-filters (searchEmitter)="searchFunction($event)"></search-with-filters>
            </div>
            <div class="col-sm-12 my-2">
                <div class="table-responsive-sm">
                    <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                [scrollbarV]="true"
                                [virtualization]="false"
                                [loadingIndicator]="loadingInlineDailyActivity"
                                [rows]="dailyActivitiesRecords"
                                [footerHeight]="40"
                                [columnMode]="'force'"
                                [rowHeight]="'auto'"
                                [externalPaging]="true"
                                [count]="page.totalElements"
                                [offset]="page.pageNumber"
                                [limit]="page.size"
                                (page)="pageCallback($event, true)"
                                [externalSorting]="true"
                                (sort)="onSort($event)"
                                [sorts] = "[sorts]"
                    >
                        <ngx-datatable-column prop="id" headerClass="font-weight-bold min-w-fit-content"  cellClass="min-w-fit-content" [sortable]="true" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Report #
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{row.record_id}}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Prepared
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{ dayjsDisplayDateOrFull(+row.createdAt, false) }}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column prop="submitted_by" headerClass="font-weight-bold" [sortable]="true" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Prepared By
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{row.submitted_by}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column prop="shift_date" headerClass="font-weight-bold" [sortable]="true" minWidth="120">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Shift Time & Date
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{ dayjsDisplayDateOrFull(row.shift_date, true, true) }}
                                <div style="font-size: 11px;">({{ row.shift_time?.from }} - {{ row.shift_time?.to }})</div>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Activities
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{ getActivitiesStr(row.activities) }}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Location/s
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{ getActivitiesLocationStr(row.activities_location) }}</span>
                            </ng-template>
                        </ngx-datatable-column>

                        <ngx-datatable-column headerClass="font-weight-bold" style="backface-visibility:visible" [sortable]="false" minWidth="120" cellClass="no-ellipsis">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Action
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <button-group
                                    [buttons]="baseButtonConfig[row.id]"
                                    (onActionClick)="rowBtnClicked($event, row)">
                                </button-group>
                            </ng-template>
                        </ngx-datatable-column>
                    </ngx-datatable>

                </div>
                <div class="clearfix"></div>
            </div>
        </div>
        </div>
        </div>
    </div>
</div>

<i-modal #editActivityRef [title]="dailyActivity.id ? 'Edit Report' : 'New Report'" cancelBtnText="Close" (onClickRightPB)="updateDailyActivity($event)" rightPrimaryBtnTxt="Save"
    [rightPrimaryBtnDisabled]="(!editActivityForm.valid || !(dailyActivity.activities.length > 0 || dailyActivity.delay_or_additional.length > 0 || dailyActivity.instructions.length > 0))">
        <form novalidate #editActivityForm="ngForm">
            <div class="form-group">
                <label>Shift Date <small class="required-asterisk">*</small></label>
                <div class="input-group mb-3">
                    <input class="form-control" placeholder="dd-mm-yyyy"
                            name="shift_date" [(ngModel)]="shift_date" ngbDatepicker
                            #ed="ngbDatepicker" ng-value="shift_date" readonly required>
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary calendar" (click)="ed.toggle()" type="button">
                            <i class="fa fa-calendar"></i>
                        </button>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-md-6">
                    <label>Start Time <small class="required-asterisk">*</small></label>
                    <div class="input-group mb-3">
                        <ngb-timepicker [(ngModel)]="from" name="from" [size]="'small'" required></ngb-timepicker>
                    </div>
                </div>
                <div class="col-md-6">
                    <label>End Time <small class="required-asterisk">*</small></label>
                    <div class="input-group mb-3">
                        <ngb-timepicker [(ngModel)]="to" name="to" [size]="'small'" required></ngb-timepicker>
                    </div>
                </div>
            </div>

            <hr>
            <div class="col-md-12">
                <h6 class="d-inline-block">Add Instruction</h6>
                <i class="float-right fas fa-plus-circle mr-1 cursor-pointer" (click)="instructionModal()"></i>
            </div>

            <hr>
            <div class="col-md-12">
                <h6 class="d-inline-block">Add Delay/Additional Works</h6>
                <i class="float-right fas fa-plus-circle mr-1 cursor-pointer" (click)="delaysModal()"></i>
            </div>
            <hr>
            <div class="col-md-12">
                <h6 class="d-inline-block">Add Additional Comments</h6>
                <i class="float-right fas fa-plus-circle mr-1 cursor-pointer" (click)="additionalCommentModal()"></i>
            </div>
            <hr>

            <div class="row">
                <div class="col-md-6">
                    <h5>Activities</h5>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-primary float-md-right" (click)="addNewActivityModal()">+ Add New Activity</button>
                </div>
            </div>
            <div *ngFor="let activity of dailyActivity.activities; index as i;" style="border: 1px solid #e8e8e8;
            padding: 10px; margin-top: 10px;">
                <h5 class="d-inline-block mt-1" style="width: 90%;">{{activity.title}}</h5>
                <button title="Delete Activity" (click)="deleteActivity(i)" class="btn btn-sm float-md-right mt-1 pr-0">
                    <span class="material-symbols-outlined text-danger cursor-pointer">
                        delete
                    </span>
                </button>
                <div class="w-100 d-flex justify-content-between">
                    <label>Workforce</label>
                    <label class="float-md-right">{{ totalOperatives(activity.key) }}</label>
                </div>
                <div class="w-100 d-flex justify-content-between">
                    <label>Plant/Machinery</label>
                    <label class="float-md-right">{{ totalPlants(activity.key) }}</label>
                </div>
                <div class="w-100 d-flex justify-content-between">
                    <label>Materials</label>
                    <label class="float-md-right">{{ totalMaterial(activity.key) }}</label>
                </div>
                <div class="w-100 d-flex justify-content-between">
                    <label>Photos</label>
                    <label class="float-md-right">{{ dailyActivity.photos[activity.key] ? dailyActivity.photos[activity.key].length: 0 }}</label>
                </div>

                <button class="btn btn-primary" (click)="openActivityModal(activity)"><i class="fa fa-edit pr-1"></i>Edit</button>
            </div>
            <hr *ngIf="dailyActivity.instructions.length > 0">
            <div *ngFor="let instruction of dailyActivity.instructions; index as k;" style="border: 1px solid #e8e8e8;
            padding: 10px; margin-top: 10px;">
                <h5 class="d-inline-block mt-1" *ngIf="instruction.source === 'subcontractor'">Subcontractor Instruction</h5>
                <h5 class="d-inline-block mt-1" *ngIf="instruction.source === 'client'">Client Instruction</h5>
                <br>
                <button title="Delete Instruction" (click)="deleteInstruction(k)" class="btn btn-sm float-md-right mt-1 pr-0">
                    <span class="material-symbols-outlined text-danger cursor-pointer">
                        delete
                    </span>
                </button>
                <button class="btn btn-primary" (click)="editInstruction(k)"><i class="fa fa-edit pr-1"></i>Edit</button>
            </div>
            <hr *ngIf="dailyActivity.delay_or_additional.length > 0">
            <div *ngFor="let item of dailyActivity.delay_or_additional; index as j;" style="border: 1px solid #e8e8e8;
            padding: 10px; margin-top: 10px;">
                <h5 class="d-inline-block mt-1" *ngIf="item.source === 'additional'">Additional Works</h5>
                <h5 class="d-inline-block mt-1" *ngIf="item.source === 'delay'">Delay</h5>
                <h5 class="d-inline-block mt-1" *ngIf="item.source === 'additional_comment'">Additional Comments</h5>
                <br>
                <button title="Delete" (click)="deleteDelays(j)" class="btn btn-sm float-md-right mt-1 pr-0">
                    <span class="material-symbols-outlined text-danger cursor-pointer">
                        delete
                    </span>
                </button>
                <button class="btn btn-primary" (click)="editDelays(j)"><i class="fa fa-edit pr-1"></i>Edit</button>
            </div>
            <hr>
            <div class="form-group">
                <div class="row float-md-right mr-0">
                    <div class="custom-control custom-radio mr-3">
                        <input type="radio" id="work_carried_out" name="work_carried_out"
                            [(ngModel)]="dailyActivity.work_carried_out" [value]="true"
                            class="custom-control-input" required>
                        <label class="custom-control-label" for="work_carried_out">Yes</label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" id="work_carried_out-no" name="work_carried_out"
                            [(ngModel)]="dailyActivity.work_carried_out" [value]="false"
                            class="custom-control-input" required>
                        <label class="custom-control-label" for="work_carried_out-no">No</label>
                    </div>
                </div>
                <label style="width: 70%;">Was the work carried out in accordance with plan specifications?</label>
            </div>

            <div class="form-group">
                <div class="row float-md-right mr-0">
                    <div class="custom-control  custom-radio mr-3">
                        <input type="radio" id="workons-yes" name="work_on_schedule"
                            [(ngModel)]="dailyActivity.work_on_schedule" [value]="true"
                            class="custom-control-input" required>
                        <label class="custom-control-label" for="workons-yes">Yes</label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" id="workons-no" name="work_on_schedule"
                            [(ngModel)]="dailyActivity.work_on_schedule" [value]="false"
                            class="custom-control-input" required>
                        <label class="custom-control-label" for="workons-no">No</label>
                    </div>
                </div>
                <label style="width: 70%;">Was the work carried out on schedule?</label>
            </div>

            <div class="form-group" *ngIf="!(dailyActivity.work_on_schedule && dailyActivity.work_carried_out)">
                <label>Comment</label>
                <div class="input-group mb-3">
                    <textarea truncate type="text" class="form-control" rows="5" name="comment" [(ngModel)]="dailyActivity.comment"></textarea>
                </div>
            </div>
        </form>
</i-modal>

<i-modal #delaysModalRef title="Delays/Additional Works" [showCancel]="false" (onClickRightPB)="saveDelays($event)" rightPrimaryBtnTxt="Save"
    [rightPrimaryBtnDisabled]="!delaysForm.valid">
        <form novalidate #delaysForm="ngForm">

            <div *ngIf="showModal" class="form-group">
                <label class="d-inline-block mt-1">Type <small class="required-asterisk">*</small></label>
                <ng-select [items]="delaysTypes"
                    class="dropdown-list" appendTo="body"
                    bindLabel="title"
                    bindValue="value"
                    placeholder="Select Type"
                    name="source{{j}}"
                    [(ngModel)]="delay.source" required
                    >
                </ng-select>
            </div>
            <div class="form-group">
                <label>Comments <small class="required-asterisk">*</small></label>
                <div class="input-group mb-3">
                    <textarea type="text" class="form-control" rows="5" name="comment_additional" [(ngModel)]="delay.comments" required></textarea>
                </div>
            </div>

        </form>
</i-modal>

<i-modal #additionalCommentRef title="Additional Comments" [showCancel]="false" (onClickRightPB)="saveAdditionalComments($event)" rightPrimaryBtnTxt="Save"
    [rightPrimaryBtnDisabled]="!addCommentsForm.valid">
        <form novalidate #addCommentsForm="ngForm">
            <div class="form-group">
                <label>Comment <small class="required-asterisk">*</small></label>
                <div class="input-group mb-3">
                    <textarea type="text" class="form-control" rows="5" name="additional_comment" [(ngModel)]="delay.comments" required></textarea>
                </div>
            </div>

        </form>
</i-modal>

<i-modal #instructionsModalRef title="Manage Instructions" [showCancel]="false" (onClickRightPB)="saveInstruction($event)" rightPrimaryBtnTxt="Save"
    [rightPrimaryBtnDisabled]="!instructionsForm.valid">

        <form novalidate #instructionsForm="ngForm">

                <div *ngIf="showModal" class="form-group">
                    <label class="d-inline-block mt-1">Instruction Type <small class="required-asterisk">*</small></label>
                    <ng-select [items]="instructionTypes"
                        class="dropdown-list" appendTo="body"
                        bindLabel="title"
                        bindValue="value"
                        placeholder="Select Instruction Type"
                        name="source{{j}}"
                        [(ngModel)]="instruction.source" required
                        >
                    </ng-select>
                </div>
                <div class="form-group">
                    <label>Comments <small class="required-asterisk">*</small></label>
                    <div class="input-group mb-3">
                        <textarea type="text" class="form-control" rows="5" name="instruction.comment" [(ngModel)]="instruction.comments" required></textarea>
                    </div>
                </div>


        </form>
</i-modal>

<i-modal #plantHoursRef title="Add Plant Hours" [showCancel]="true" (onClickRightPB)="savePlant()" [rightPrimaryBtnDisabled]="!tempPlants.length" rightPrimaryBtnTxt="Save">
        <div class="col-md-12 pr-0 mb-2">
            <button class="btn btn-primary float-md-right" (click)="manualllyAddPlant(addPlantForm)">
                <span *ngIf="!manualPlantEntry">Add Manually</span>
                <span *ngIf="manualPlantEntry">Cancel</span>
            </button>
        </div>
        <form novalidate #addPlantForm="ngForm">
            <div class="form-group" *ngIf="!manualPlantEntry">
                <label>Choose a Plant <small class="required-asterisk">*</small></label>
                <ng-select [items]="metaPlantsMachineryList"
                    bindLabel="name"
                    class="mb-2"
                    bindValue="name"
                    placeholder="Select a Plant"
                    name="plantName"
                    [(ngModel)]="newPlant.plant"
                    required>
                </ng-select>
                <label>Choose an Operator</label>
                <ng-select class="mb-2 v-scroll-dropdown-list" appendTo="body"
                    [virtualScroll]="true"
                    placeholder="Select an Operator"
                    name="operativeName"
                    [(ngModel)]="newPlant.operator">
                    <ng-option *ngFor="let operative of operativesList" [value]="operative.name">
                      {{ operative.name }}
                    </ng-option>
                </ng-select>
            </div>

            <div *ngIf="manualPlantEntry">
                <div class="form-group">
                    <label>Plant Name <small class="required-asterisk">*</small></label>
                    <input type="text" class="form-control" [(ngModel)]="newPlant.plant" name="newPlant.plant" placeholder="Plant Name" required>
                </div>
                <div class="form-group">
                    <label>Plant <span i18n="@@operative">Operative</span></label>
                    <ng-select [items]="operativesList"
                        bindLabel="name"
                        class="mb-2"
                        bindValue="name"
                        placeholder="Select an Operator"
                        name="newPlant.operator"
                        [(ngModel)]="newPlant.operator"
                        >
                    </ng-select>
                </div>
            </div>
            <label>Plant Time (Hour : Minute)</label>
            <ngb-timepicker [(ngModel)]="newPlant.hours[newActivity.key]" name="plantHours"></ngb-timepicker>

            <button type="button" class="btn btn-outline-primary" (click)="saveTempPlant(addPlantForm)"
                [disabled]="!addPlantForm.valid"
                >Add Plant
            </button>
        </form>
        <div class="col-md-12" *ngFor="let item of tempPlants; index as i;">
            <div class="pt-1" *ngIf="item.hours[newActivity.key] != undefined">
                <h6 class="d-inline-block mt-1">{{item.plant}} {{(item.operator) ? '| '+ item.operator : ''}}</h6>
                <button title="Delete Plant" (click)="deleteAndSavePlant(i)" class="btn btn-sm float-md-right mt-1">
                    <span class="material-symbols-outlined text-danger cursor-pointer">
                        delete
                    </span>
                </button>
                <div>Hours: <span *ngIf="item.hours[newActivity.key] != null"> {{item.hours[newActivity.key].hour}} hours, {{item.hours[newActivity.key].minute}} minutes</span></div>
            </div>
        </div>
</i-modal>

<i-modal #materialUsedRef title="Materials Used" [showCancel]="true" (onCancel)="closeMaterialModal()" (onClickRightPB)="saveMaterial()" [rightPrimaryBtnDisabled]="!tempMaterials.length" rightPrimaryBtnTxt="Save">
        <form novalidate #materialUsedForm="ngForm">
            <ng-container *ngIf="showMaterialModal">
                <div class="row form pb-1">
                    <div class="col-md-6 form-group">
                        <input type="text" class="form-control" placeholder="Material Name*"
                        name="materialName" [(ngModel)]="newMaterial.material" required/>
                    </div>
                    <div class="col-md-6 form-group">
                        <input class="form-control" type="number" placeholder="Material Quantity*"
                        name="materialQuantity" [(ngModel)]="newMaterial.quantity[newActivity.key].qty" min="0" pattern="^[0-9]*$" required/>
                    </div>
                    <div class="col-md-6 form-group">
                        <input class="form-control" type="text" placeholder="Material Units"
                        name="materialUnit" [(ngModel)]="newMaterial.quantity[newActivity.key].unit" />
                    </div>

                </div>
                <button type="button" class="btn btn-outline-primary" (click)="saveTempMaterial(materialUsedForm)"
                    [disabled]="!materialUsedForm.valid"
                    >Add Material
                </button>
            </ng-container>
        </form>
        <hr>
        <div class="col-md-12" *ngFor="let item of tempMaterials; index as i;">
            <div class="pt-1" *ngIf="item.quantity[newActivity.key] != undefined">
                <h6 class="d-inline-block mt-1">{{item.material}}</h6>
                <button title="Delete Material" (click)="deleteAndSaveMaterial(i)" class="btn btn-sm float-md-right mt-1">
                    <span class="material-symbols-outlined text-danger cursor-pointer">
                        delete
                    </span>
                </button>
                <div>Quantity: {{item.quantity[newActivity.key].qty}} {{item.quantity[newActivity.key].unit}}</div>
            </div>
        </div>
</i-modal>

<i-modal #addOperativeRef title="Manage Workforce" [showCancel]="true" (onClickRightPB)="saveOperatorHours()" [rightPrimaryBtnDisabled]="!tempOperatives.length" rightPrimaryBtnTxt="Save">
        <div class="col-md-12 pr-0 mb-2">
            <button class="btn btn-primary float-md-right" (click)="manualllyAddWF(addOperativeForm)">
                <span *ngIf="!manualWorkforce">Add Manually</span>
                <span *ngIf="manualWorkforce">Cancel</span>
            </button>
        </div>
        <form novalidate #addOperativeForm="ngForm">
            <ng-container *ngIf="showWorkforceModal">
                <div class="form-group" *ngIf="!manualWorkforce">
                    <label>Workforce <small class="required-asterisk">*</small></label>
                    <ng-select [items]="operativesList"
                        class="v-scroll-dropdown-list" appendTo="body"
                        [virtualScroll]="true"
                        bindLabel="name"
                        placeholder="Select Operative"
                        i18n-placeholder
                        name="operativeWorkforce"
                        [(ngModel)]="operative"
                        (change)="operatorSelected($event)" required>
                    </ng-select>
                </div>
                <div *ngIf="duplicateOperatorError" class="alert alert-danger">
                    The hours for this operator are already added.
                </div>

                <div *ngIf="manualWorkforce">
                    <div class="form-group">
                        <label>Name <small class="required-asterisk">*</small></label>
                        <input truncate class="form-control" [(ngModel)]="newOperative.name" name="newOperativeName" placeholder="Operative Name" i18n-placeholder="@@operativeName" required>
                    </div>
                    <div class="form-group">
                        <label>Job Role <small class="required-asterisk">*</small></label>
                        <ng-select [items]="jobRoleList"
                            class="dropdown-list" appendTo="body"
                            bindLabel="name"
                            bindValue="name"
                            placeholder="Select Job Role"
                            name="operativeTrade"
                            [(ngModel)]="newOperative.trade" required>
                        </ng-select>
                    </div>
                    <div class="form-group">
                    <label>Employer <small class="required-asterisk">*</small></label>
                    <company-selector-v2
                        [required]="true"
                        [country_code]="projectInfo?.custom_field?.country_code"
                        name="operativeEmployer"
                        [selectId]="newOperative.employer"
                        placeholder="Select Employer"
                        class="dropdown-list"
                        (selectionChanged)="newOperative.employer = $event.record?.name"
                        [projectId]="projectId"
                    ></company-selector-v2>
                    
                </div>
                </div>
                <label>Time on Activity (Hour : Minute) <small class="required-asterisk">*</small></label>
                <div class="input-group mb-3">
                    <ngb-timepicker [(ngModel)]="newOperative.hours[newActivity.key]" name="opearativeHours" required></ngb-timepicker>
                </div>

                <button type="button" class="btn btn-outline-primary" (click)="saveTempOperatorHours(addOperativeForm)"
                [disabled]="!addOperativeForm.valid || duplicateOperatorError || !validateTime(newOperative.hours[newActivity.key])"
                >Add <span i18n="@@operative">Operative</span>
                </button>
            </ng-container>
        </form>
        <hr>
        <div class="col-md-12" *ngFor="let item of tempOperatives; index as i;">
            <div class="pt-2" *ngIf="item.hours[newActivity.key] !== undefined">
                <h6 class="d-inline-block mt-1">{{item.name}} | {{item.trade}}</h6>
                <button title="Delete Operative" i18n-title (click)="deleteAndSaveOperative(i)" class="btn btn-sm float-md-right mt-1">
                    <span class="material-symbols-outlined text-danger cursor-pointer">
                        delete
                    </span>
                </button>
                <div> <span *ngIf="item.hours[newActivity.key] != null">Hours: {{ item.hours[newActivity.key].hour}} hours, {{item.hours[newActivity.key].minute}} minutes</span></div>
            </div>
        </div>
</i-modal>

<i-modal #photosModalRef title="Manage Photos" [showCancel]="true" (onClickRightPB)="closePhotoModal($event)" [rightPrimaryBtnDisabled]="(dailyActivity.photos[newActivity.key] && dailyActivity.photos[newActivity.key].length <= 1)" rightPrimaryBtnTxt="Save">
        <div class="col-md-12 p-0 mb-4">
            <div class='col-md-12 flex-grow-1 p-0' *ngFor="let item of dailyActivity.photos[newActivity.key]; index as i" >
                <file-uploader-v2
                    class="mt-1 pt-1 mb-1"
                    [init]="item"
                    [multipleUpload]="true"
                    [category]="'project-media'"
                    [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png']"
                    #imgUploaded
                    
                    [showFileName]="false"
                    [disabled]="false"
                    [showDeleteBtn]="true"
                    [showThumbnail]="true"
                    [file_url]="item && item.file_url"
                    
                    (uploadDone)="photoUploadDone($event)"
                    (deleteFileDone)="fileDeleteDone($event, i)"
                >
                </file-uploader-v2>
            </div>
        </div>

</i-modal>

<i-modal #addActivityTitleModalRef title="Add Activity Manually" cancelBtnText="Close" (onClickRightPB)="saveActivityTitle(addActivityMForm, $event)" rightPrimaryBtnTxt="Save" [rightPrimaryBtnDisabled]="!addActivityMForm.valid">
        <form novalidate #addActivityMForm="ngForm">
            <input truncate class="form-control" [(ngModel)]="activity_name" name="activity_name" placeholder="Activity Title*" required>
        </form>
</i-modal>

<i-modal #addNewActivityRef [title]="(isEditActivity ? 'Edit' : 'Add New') + ' Activity'" cancelBtnText="Close" (onCancel)="closeNewActivityModal()" (onClickRightPB)="saveActivity(addActivityForm)" rightPrimaryBtnTxt="Save" [rightPrimaryBtnDisabled]="!addActivityForm.valid">
        <div class="col-md-12 pr-0 mb-3">
            <button class="btn btn-primary float-md-right mb-2" (click)="manualllyAddActivity()">Add Manually</button>
        </div>

        <form novalidate #addActivityForm="ngForm">
            <div class="form-group">
                <label>Title <small class="required-asterisk">*</small></label>
                <ng-select [items]="metaActivitiesList" [ngClass]="'custom_select'"
                    bindLabel="name"
                    bindValue="name"
                    placeholder="Select Activity"
                    name="activityTitle"
                    [(ngModel)]="newActivity.title" required>
                </ng-select>
            </div>
            <div class="form-group">
                <label>Location</label>
                <input truncate class="form-control" [disabled]="!newActivity.title" [(ngModel)]="activityVal.location" name="newActivity.location" placeholder="Activity Location">
            </div>

            <div class="form-group">
                <label>Progress (%)</label>
                <label class="float-right" *ngIf="newActivity.title">{{activityVal.progress}}</label>
                <input type="range" min="0" max="100" [(ngModel)]="activityVal.progress" class="slider" id="myRange" name="newActivity.progress" (ngModelChange)="setProgress($event, newActivity.key)" value="0" [disabled]="!newActivity.title">

            </div>

            <div class="form-group">
                <h6 class="d-inline-block">Workforce</h6>
                <button title="Edit Workforce" (click)="addOperative()" [disabled]="!newActivity.title" class="btn btn-sm float-right btn-outline-primary mr-1">
                    <i class="fa fa-edit"></i>
                </button>
            </div>
            <hr>

            <div class="form-group">
                <h6 class="d-inline-block">Plant/Machinery</h6>
                <button title="Edit Plant/Machinery" class="btn btn-sm float-right btn-outline-primary mr-1"
                (click)="openPlantModal()" [disabled]="!newActivity.title">
                    <i class="fa fa-edit"></i>
                </button>
            </div>
            <hr>

            <div *ngIf="showActivityModal" class="form-group">
                <h6 class="d-inline-block">Materials</h6>
                <button title="Edit Materials" class="btn btn-sm float-right btn-outline-primary mr-1"
                (click)="materialUsed()" [disabled]="!newActivity.title">
                    <i class="fa fa-edit"></i>
                </button>
            </div>
            <hr>

            <div class="form-group">
                <h6 class="d-inline-block">Photos</h6>
                <button title="Edit Photos" class="btn btn-sm float-right btn-outline-primary mr-1"
                (click)="openPhotosModal()" [disabled]="!newActivity.title">
                    <i class="fa fa-edit"></i>
                </button>
            </div>
            <hr>
            <div class="form-group">
                <label>Description</label>
                <textarea truncate type="text" class="form-control" [(ngModel)]="activityVal.description" name="newActivity.description" rows="5" [disabled]="!newActivity.title" placeholder="Activity Description"></textarea>

            </div>
            <div class="form-group">
                <label>WPP/Method Statements</label>
                <input truncate class="form-control" [(ngModel)]="activityVal.wppMethodStatement" [disabled]="!newActivity.title" name="newActivity.wpp" placeholder="WPP/Method Statements">
            </div>
            <div class="form-group">
                <label>Task Briefing Sheets</label>
                <input truncate class="form-control" name="newActivity.task_briefing_sheets" [disabled]="!newActivity.title" [(ngModel)]="activityVal.taskBrief" placeholder="Task Briefing Sheets">
            </div>
            <div class="form-group">
                <label>Permit Number</label>
                <input truncate class="form-control" name="newActivity?.permit_number" [disabled]="!newActivity.title" [(ngModel)]="activityVal.permitNumber" placeholder="Permit Number">
            </div>
        </form>
</i-modal>

<ng-template #successMessageHtml let-c="close" let-d="dismiss">
    <div class="modal-body">
        <div class="text-center">
            <p class="text-success">Successful!</p>
            <p>{{ successMessage }}</p>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-primary m-auto" (click)="d('Cross click')">Close</button>
    </div>
</ng-template>

<i-modal #dailyActivityViewContentRef [title]="'Report #' + selectedDailyActivity?.record_id" [showCancel]="false" (onCancel)="closeAddNewActivityModal()" 
    (onClickRightPB)="closeAddNewActivityModal()" [rightPrimaryBtnTxt]="!beingReviewed ? 'Done' : ''" size="lg" [windowClass]="'xl-modal'" [showFooter]="!beingReviewed">
    <div class="d-flex justify-content-center flex-wrap overflow-auto h-100">
        <div class="w-100" [style.height]="(beingReviewed) ? '69.5%' : '99%'">
            <iframe #dailyActivityContentFrame id='dailyActivityContentFrame' class="border-0" style="width: 100%;height: 100%;position: relative;"></iframe>
        </div>
        <div class="form-group row mt-1 mr-0" *ngIf="(beingReviewed && showNewActivityModal)">
            <signature-pad-selector
                [height]="150"
                [width]="1140"
                (signChanged)="saveReviewSign($event)"
                (clear)="clearSignature()"
                (signPointsChanged)="pointsChanged($event)"
            ></signature-pad-selector>
        </div>
    </div>
    <block-loader alwaysInCenter="true" [show]="iframe_content_loading"></block-loader>
</i-modal>


<daily-activities-import-modal
    #dailyActivitiesImportModalHtml
    [project]="projectInfo" [companyId]="employerId" (onAddMetaActivity)="updateMetaActivityList($event)" (onOpenModal)="showLoader($event)">
</daily-activities-import-modal>

<meta-plants-import-modal
    #metaPlantsMachineryImportModalHtml
    [project]="projectInfo" [companyId]="employerId" (onProjectUpdate)="autoPullAssetsStatusChange($event)" (onAddMetaPlant)="updateMetaPlantsList($event)" (onOpenModal)="showLoader($event)" (onGetVehiclesType)="populateVehicleType($event)">
</meta-plants-import-modal>

<view-activity-component #viewActivityModalHtml
    [dailyActivity]="dailyActivity">
</view-activity-component>

<share-tool-report-to-email
    [projectId]="projectId" [employerId]="employerId"  #shareDaReportModal
    [tool_name]="'Share '+daSglrPhrase" (onSave)="shareDailyActivityReport($event)">
</share-tool-report-to-email>
<block-loader [show]="(downloadDailyActivityReportLoading || loadingActivities)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
<report-downloader #reportDownloader 
    [xlsxOnly]="true"
    [showActivities]="true"
    [activityList]="downloadOptions"
    [isDailyActivity]="true"
    (onFilterSelection)="dailyActivitiesReportDownload($event)"
    >
</report-downloader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

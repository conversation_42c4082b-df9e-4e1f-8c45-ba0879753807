import { Component, OnInit, TemplateRef, ViewChild, ElementRef, ViewEncapsulation } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { ActivatedRoute, Router } from "@angular/router";
import { Observable, fromEvent } from "rxjs";
import { filter, tap, debounceTime, distinctUntilChanged, map, share } from "rxjs/operators";
import * as dayjs from 'dayjs';
import { NgbModalConfig, NgbModal, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import {
    ProjectDailyActivitiesService,
    AuthService,
    UserService,
    ToastService,
    User,
    Project,
    ProjectService,
    isInheritedProjectOfCompany,
    ProjectDailyActivityReport,
    Common,
    ResourceService,
    HttpService,
    DailyActivitiesActionButtons,
    ActionButtonVal,
} from "@app/core";
import { NgbMomentjsAdapter } from "@app/core/ngb-moment-adapter";
import { saveAs } from "file-saver";
import { DailyActivitiesImportComponent } from "./daily-activities-import.component";
import { MetaPlantsImportComponent } from "./meta-plants/meta-plants-import.component";
import { ViewDailyActivityComponent } from "./view-acitvity/view-activity.component";
import { AppConstant } from "@env/environment";
import { DatatableComponent } from "@swimlane/ngx-datatable";
import { ShareToolReportToEmailComponent } from '../share-tool-report-to-email/share-tool-report-to-email.component';
import { ReportDownloaderComponent } from '../report-downloader/report-downloader.component';
import { ActionBtnEntry, GenericConfirmationModalComponent, IModalComponent } from '@app/shared';

@Component({
    templateUrl: './daily-activities.component.html',
    // add NgbModalConfig and NgbModal to the component providers
    providers: [NgbModalConfig, NgbModal],
    styleUrls: ['./daily-activities.component.scss'],
})
export class DailyActivitiesComponent implements OnInit {
    @ViewChild('reportDownloader') private reportDownloader: ReportDownloaderComponent;
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    iframe_content_loading: boolean = false;
    selectedDailyActivity: any;
    employerId: any = null;
    authUser$: User;
    projects: Array<Project> = [];
    project: Observable<{}>;
    projectsLoading: boolean = false;
    dailyActivitiesRecords: Array<any> = [];
    temp_records: Array<any> = [];
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;
    dayjsDisplayDateOrFull(n: number, onlyDate = true, skipTz: boolean = false) {
        return this.dayjs(n, skipTz).format(onlyDate ? AppConstant.defaultDateFormat : AppConstant.fullDateTimeFormat);
    };
    employer: any = {};
    is_inherited_project: boolean = false;
    downloadDailyActivityReportLoading: boolean = false;
    dailyActivity: ProjectDailyActivityReport = new ProjectDailyActivityReport();
    shift_date;
    newPlant: { plant: string; operator: string; hours: Object } = {
        plant: null,
        operator: null,
        hours: {}
    };
    modalRef;
    editActivityModalRef;
    newOperative: { user_ref: number, name: string; trade: string; employer: string; hours: Object } = {
        user_ref: null,
        name: null,
        trade: null,
        employer: null,
        hours: {}
    };
    tempOperatives: { user_ref: number, name: string; trade: string; employer: string; hours: Object }[] = [];
    tempPlants: { plant: string; operator: string; hours: Object }[] = [];
    tempMaterials: { material: string, quantity: Object }[] = [];
    newActivity: { key: string, title: string; } = {
        key: null,
        title: null,
    };
    newMaterial: { material: string, quantity: Object };
    hasCompanyInspectionTours: boolean = false;
    metaPlantsMachineryList = [];
    metaActivitiesList = [];
    operativesList = [];
    jobRoleList = [];
    manualWorkforce: boolean = false;
    from: any = {};
    to: any = {};
    instructionTypes = [{ value: 'subcontractor', title: 'Subcontractor Instruction' }, { value: 'client', title: 'Client Instruction' }];
    delaysTypes = [{ value: 'additional', title: 'Additional Works' }, { value: 'delay', title: 'Delay' }];
    instruction = { source: null, comments: null };
    delay = { source: null, comments: null };
    loadingActivities: boolean = false;
    tableOffset: number = 0;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    isEdit: boolean = true;
    instructionIndex = null;
    delaysIndex = null;
    operative: any = null;
    duplicateOperatorError: boolean = false;
    manualPlantEntry: boolean = false;
    xlsxReportType: string = 'activity_report';
    successMessage: string = '';
    activityVal: { 
        location: string,
        description: string,
        wppMethodStatement: string,
        progress: number,
        taskBrief: string,
        permitNumber: string
     } = this.initactivityVal();
    activityReportFormat: string = 'activity_report_xlsx';
    paginationData = new Common();
    page = this.paginationData.page;
    search: string = null;
    typingTimer: any = null;
    doneTypingInterval: number = 1000;
    daPhrase: string = ``;
    daSglrPhrase: string = ``;
    beingReviewed: boolean = false;
    daReviewSignature;
    validSignature: boolean = false;
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    isProjectPortal: boolean = false;
    TYPE_OF_VEHICLE: Array<any> = [];
    projectAssetVehicles: Array<any> = [];
    isMobileDevice: boolean = false;
    downloadOptions = [
        { name: 'Activity Report', value: 'activity_report' },
        { name: 'Workforce Hours Report', value: 'workforce_hours' },
        { name: 'Actual vs Reported Hours', value: 'actual_vs_reported_hours' },
        { name: 'Activity/Hours Breakdown', value: 'activity_hours_breakdown' }
    ];
    minDate: NgbDateStruct = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs().subtract(1, 'month'));
    sorts: {
        dir:string, prop: string
    } = {
        dir:'desc', prop: 'id'
    };
    actionButtonMetaData = {
        actionList: [],
        class: 'material-symbols-outlined',
    };
    showModal: boolean = false;
    showWorkforceModal: boolean = false;
    showMaterialModal: boolean = false;
    showActivityModal: boolean = false;
    isEditActivity: boolean = false;
    showNewActivityModal: boolean = false;
    isInitDailyActivity = false;
    loadingInlineDailyActivity: boolean = false;
    baseButtonConfig: Record<string, ActionBtnEntry[]> = {};

    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private modalService: NgbModal,
        private toastService: ToastService,
        private projectDailyActivitiesService: ProjectDailyActivitiesService,
        private authService: AuthService,
        private projectService: ProjectService,
        private userService: UserService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private resourceService: ResourceService,
        private httpService: HttpService,
    ) {
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
        }
    }

    dayjs(n: number, skipTz: boolean = false) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return skipTz ? dayjs(n) : dayjs(n).tz(tz);
    };

    ngOnInit() {
        if (!this.isProjectPortal) {
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
                this.employerId = params['employerId'];
            });
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        } else {
            this.initializeTable();
        }
        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
            }
        });
    }

    initactivityVal() {
        return {
            location: '',
            description: '',
            wppMethodStatement: '',
            progress: 0,
            taskBrief: '',
            permitNumber: ''
        }
    }

    getOperativeandJobRoles() {
        let fromDate = dayjs().add(1, 'd').format(AppConstant.apiRequestDateFormat);
        let toDate = dayjs().subtract(6, 'd').format(AppConstant.apiRequestDateFormat);
        this.projectDailyActivitiesService.getActiveProjectUsers(this.projectId, this.employerId, toDate, fromDate).subscribe((data: any) => {
            if (data && data.daily_logs) {
                for (let log of data.daily_logs) {
                    if (log.user_id?.id && this.operativesList.findIndex(x => x.id === log.user_id.id) <= -1) {
                        this.operativesList.push({ name: log.user_id.name, trade: log.employer ? log.employer.job_role : null, id: log.user_id.id });
                    }
                }
                this.operativesList.sort((a, b) => (a.name > b.name) ? 1 : -1);
            }
        });
        let country_code = this.isProjectPortal ? (this.projectInfo?.custom_field?.country_code || undefined) : (this.employer?.country_code || undefined);
        this.userService.getJobRoles({ country_code: country_code }).subscribe((data: any) => {
            if (data.success) {
                this.jobRoleList = data.jobrolelist;
            }
        });
    }

    openModal(content, size) {
        const modalRef = this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size
        });
        return false;
    }

    getListOfMetaPlants() {
        this.projectDailyActivitiesService.getProjectMetaPlants(this.projectId, this.employerId).subscribe((data: any) => {
            if (data && data.meta_plants) {
                this.metaPlantsMachineryList = data.meta_plants;
                if (this.projectInfo.custom_field.show_assets_in_plants_status) {
                    this.projectAssetVehicles = data.projectAssetVehicles;
                    this.setMetaPlants({ metaPlantsMachineryList: this.metaPlantsMachineryList, projectAssetVehicles: this.projectAssetVehicles });
                    if (this.TYPE_OF_VEHICLE.length) {
                        this.projectAssetVehicles.forEach(vehicle => {
                            let title = this.getVehicleType(vehicle.type_of_vehicle) + ' - ' + vehicle.serial_number;
                            this.metaPlantsMachineryList.push({ name: title });
                        });
                        return;
                    }
                    this.getTypeOfVehicles(this.projectAssetVehicles);
                }
                this.setMetaPlants({ metaPlantsMachineryList: this.metaPlantsMachineryList, projectAssetVehicles: this.projectAssetVehicles });
            }
        });
    }

    getTypeOfVehicles(projectAssetVehicles) {
        this.resourceService.getInnDexSettingByName('type_of_asset_vehicles_en_gb').subscribe((data: any) => {
            if (data.success && data.record && data.record.value) {
                this.TYPE_OF_VEHICLE = data.record.value;
                this.setTypeOfVehicles(this.TYPE_OF_VEHICLE);
                projectAssetVehicles.forEach(vehicle => {
                    let title = this.getVehicleType(vehicle.type_of_vehicle) + ' - ' + vehicle.serial_number;
                    this.metaPlantsMachineryList.push({ name: title });
                });
            } else {
                const message = 'Something went wrong while getting type of vehicles.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    getVehicleType(vehicleTypeKey) {
        let typeInfo = this.TYPE_OF_VEHICLE.find(v => v.key === vehicleTypeKey);
        return typeInfo ? typeInfo.value : '';
    }

    checkIfEditable(record) {
        if(record.reviewer && record.reviewer.name) {
            return false;
        }
        let activityCreatedAt = dayjs(record.createdAt);
        let diff = dayjs().diff(activityCreatedAt, 'day', true);
        if (diff < 28) {
            return true;
        }
        return false;
    }

    getListOfMetaActivities() {
        this.projectDailyActivitiesService.getProjectMetaActivities(this.projectId, this.employerId).subscribe((data: any) => {
            if (data && data.meta_daily_activities) {
                this.metaActivitiesList = data.meta_daily_activities;
                this.setmetaActivities(this.metaActivitiesList);
            }
        });
    }

    setmetaActivities(metaActivities) {
        this.dailyActivitiesImportModalHtmlRef.setMetaActivities(metaActivities);
    }

    setMetaPlants(data) {
        this.metaPlantsMachineryImportModalHtmlRef.setMetaPlants(data);
    }

    setTypeOfVehicles(data) {
        this.metaPlantsMachineryImportModalHtmlRef.setTypeOfVehicles(data);
    }

    @ViewChild('addActivityTitleModalRef') private addActivityTitleModalRef: IModalComponent;
    manualllyAddActivity() {
        this.activity_name = '';
        this.addActivityTitleModalRef.open();
    }

    activity_name = null;
    saveActivityTitle(form, event) {
        if (form.valid) {
            let records = this.metaActivitiesList.filter(activity => activity.name === this.activity_name);
            if (!records.length && this.activity_name) {
                let payload = { name: this.activity_name, project_ref: this.projectId };
                this.projectDailyActivitiesService.addMetaActivity(this.projectId, payload, this.employerId)
                    .subscribe((data: any) => {
                        if (data && data.success && data.activity) {
                            this.newActivity.title = this.activity_name;
                            this.activity_name = '';
                            this.getListOfMetaActivities();
                            return;
                        }
                        const message = `Failed to add daily activity, id: ${this.projectId}.`;
                        this.toastService.show(this.toastService.types.ERROR, message, { data: data });
                        return [];
                    });
            } else {
                const message = 'Activity already exists.';
                this.toastService.show(this.toastService.types.INFO, message);
            }

        }
        event.closeFn();
    }

    @ViewChild('dailyActivitiesImportModalHtml', { static: true }) dailyActivitiesImportModalHtmlRef: DailyActivitiesImportComponent;
    dailyActivitiesImportModal() {
        if (this.dailyActivitiesImportModalHtmlRef && this.dailyActivitiesImportModalHtmlRef.openModal) {
            this.dailyActivitiesImportModalHtmlRef.openModal();
        }
    }

    @ViewChild('metaPlantsMachineryImportModalHtml', { static: true }) metaPlantsMachineryImportModalHtmlRef: MetaPlantsImportComponent;
    metaPlantsImportModal() {
        if (this.metaPlantsMachineryImportModalHtmlRef && this.metaPlantsMachineryImportModalHtmlRef.openModal) {
            this.metaPlantsMachineryImportModalHtmlRef.openModal();
        }
    }

    fetchRelevantData() {
        if (!this.metaPlantsMachineryList.length) {
            this.getListOfMetaPlants();
        }
        if (!this.metaActivitiesList.length) {
            this.getListOfMetaActivities();
        }
        if (!this.jobRoleList.length) {
            this.getOperativeandJobRoles();
        }
    }

    parseSecsToDurationObj(recordArray) {
        for (let record of recordArray) {
            let activitiesWithHrs = record.hours ? Object.keys(record.hours): [];
            for(let j=0; j<activitiesWithHrs.length; j++) {
                let duration_in_sec = record.hours[activitiesWithHrs[j]];
                if (duration_in_sec != null && duration_in_sec != undefined && !isNaN(duration_in_sec)) {
                    let minutes = Math.floor(duration_in_sec / 60);
                    let hours = Math.floor(minutes / 60)
                    minutes = minutes % 60;
                    record.hours[activitiesWithHrs[j]] = {
                        hour: hours,
                        minute: minutes,
                    }
                }
            }
        }
        return recordArray;
    }

    @ViewChild('editActivityRef')
    private editActivityRef: IModalComponent;
    editActivityModal(record) {
        this.fetchRelevantData();
        this.dailyActivity = { ...record };
        this.shift_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(this.dailyActivity.shift_date));
        let shiftFrom = this.dailyActivity.shift_time.from.split(':');
        this.from = { hour: Number(shiftFrom[0]), minute: Number(shiftFrom[1]) };
        let shiftTo = this.dailyActivity.shift_time.to.split(':');
        this.to = { hour: Number(shiftTo[0]), minute: Number(shiftTo[1]) };

        this.dailyActivity.operatives = this.parseSecsToDurationObj(this.dailyActivity.operatives);
        this.dailyActivity.plants = this.parseSecsToDurationObj(this.dailyActivity.plants);
        this.editActivityRef.open();
    }

    setProgress(event, key) {
        this.activityVal.progress = event;
    }

    openActivityReport() {
        this.dailyActivity = new ProjectDailyActivityReport();
        this.shift_date = null;
        this.from = null;
        this.to = null;
        this.fetchRelevantData();
        this.editActivityRef.open();
    }

    @ViewChild('viewActivityModalHtml', { static: true }) viewActivityModalHtmlRef: ViewDailyActivityComponent;
    viewActivity(activity) {
        if (this.viewActivityModalHtmlRef && this.viewActivityModalHtmlRef.openModal) {
            this.dailyActivity = activity;
            this.viewActivityModalHtmlRef.openModal();
        }
    }


    // Will get called for Company route only.
    projectRetrieved(project: Project) {
        this.projectInfo = project;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
        this.initializeTable();
    }

    async initiateDownload(resp) {
        this.from_date = resp.fromDate;
        this.to_date = resp.toDate;
        this.downloadDailyActivities(resp);
    }

    @ViewChild('successMessageHtml')
    private successMessageHtmlRef: TemplateRef<any>;
    downloadDailyActivities(resp) {
        let projectId = this.projectId;
        let fromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.from_date);
        let toDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.to_date);
        if (!fromDate || !toDate) {
            const message = 'Please enter from date and to date h.';
            this.toastService.show(this.toastService.types.INFO, message);
            return false;
        }

        if (toDate.isBefore(fromDate)) {
            const message = 'Incorrect {from,to} date.';
            this.toastService.show(this.toastService.types.INFO, message);
            return false;
        }

        let formattedFromDate = fromDate.format(AppConstant.apiRequestDateFormat);
        let formattedToDate = toDate.format(AppConstant.apiRequestDateFormat);
        let companyId = (this.employerId ? this.employerId : null);
        let request = {
            projectId,
            fromDate: formattedFromDate,
            toDate: formattedToDate,
            companyId: companyId,
            is_inherited_project: this.is_inherited_project,
        };
        this.downloadDailyActivityReportLoading = true;
        if (this.xlsxReportType == 'activity_report') {
            if (this.activityReportFormat === 'activity_report_pdf') {
                this.projectDailyActivitiesService.downloadDailyActivities(request, projectId).subscribe((data: any) => {
                    this.downloadDailyActivityReportLoading = false;
                    if (data.success) {
                        this.successMessage = data.message;
                        return this.openModal(this.successMessageHtmlRef, 'lg');
                    }
                });
            } else {
                this.projectDailyActivitiesService.downloadDailyActivitiesReportXLSX(this.projectId, request, () => {
                    this.downloadDailyActivityReportLoading = false;
                });
            }
        } else if (this.xlsxReportType === 'workforce_hours') {
            this.projectDailyActivitiesService.downloadWorkforceHoursReport(request, this.projectId, () => {
                this.downloadDailyActivityReportLoading = false;
            });
        } else if (this.xlsxReportType == 'actual_vs_reported_hours') {
            this.projectDailyActivitiesService.downloadHoursComparisonReport(request, this.projectId, () => {
                this.downloadDailyActivityReportLoading = false;
            });
        } else if (this.xlsxReportType == 'activity_hours_breakdown') {
            this.projectDailyActivitiesService.downloadActivityHoursBreakdownReport(request, this.projectId, () => {
                this.downloadDailyActivityReportLoading = false;
            });
        }
    }

    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean) {
        if (!this.isInitDailyActivity) {
          this.isInitDailyActivity = true;
          return;
        }
        this.page.pageNumber = pageInfo.offset;
        this.initializeTable(isPageChange);
    }

    private initializeTable(isPageChange?: boolean) {
        if (isPageChange) {
          this.loadingInlineDailyActivity = true;
        } else {
          this.loadingActivities = true;
        }
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('expand_photos', `yes`)
            .set('is_inherited_project', `${this.is_inherited_project}`)
            .set('sortKey', this.sorts.prop)
            .set('sortDir', this.sorts.dir);

        if (this.search && this.search !== null) {
            params = params.append('search', `${this.search}`);
        }

        this.daPhrase = this.projectInfo ? this.projectInfo.custom_field.da_phrase : '';
        this.daSglrPhrase = this.projectInfo ? this.projectInfo.custom_field.da_phrase_singlr : '';
        this.actionButtonMetaData.actionList = [
            {
                code: DailyActivitiesActionButtons.PRELOAD_ACTIVITIES,
                name: `Pre-load Activities`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'handyman',
                enabled: true,
            },
            {
                code: DailyActivitiesActionButtons.PRELOAD_PLANT_MACHINERY,
                name: `Pre-load Plant/Machinery`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'forklift',
                enabled: true,
            },
            {
                code: DailyActivitiesActionButtons.DOWNLOAD_REPORT,
                name: `Download Report`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: true,
            },
        ];
        this.projectDailyActivitiesService.getDailyActivities(this.projectId, params).subscribe((data: any) => {
            this.loadingActivities = false;
            this.loadingInlineDailyActivity = false;
            if (data && data.projectdailyactivities) {
                this.page.totalElements = data.total;
                this.dailyActivitiesRecords = data.projectdailyactivities;
                this.temp_records = data.projectdailyactivities;
                this.getRowButtonGroup(this.dailyActivitiesRecords);
                return data.projectdailyactivities;
            }
            const message = `Failed to fetch project daily activities, id: ${this.projectId}.`;
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            return [];
        });
    }

    getRowButtonGroup(records: any[]): void {
        // Define base button configuration as a constant
        const BASE_BUTTON_CONFIG: Array<ActionBtnEntry> = [
            {
                key: 'view',
                label: '',
                title: 'View Activity',
                mat_icon: 'search'
            },
            {
                key: 'share',
                label: '',
                title: `Share ${this.daSglrPhrase} Details`,
                mat_icon: 'share'
            },
            {
                key: 'download', 
                label: '',
                title: 'Download Images',
                mat_icon: 'download',
                children: []
            },
            {
                key: 'edit',
                label: '',
                title: 'Edit Activity',
                mat_icon: 'edit_note'
            },
            {
                key: 'review',
                label: '',
                title: `Review ${this.daSglrPhrase}`,
                mat_icon: 'rate_review',
            }
        ];

        // Process each record
        records.forEach(row => {
            if (!row?.id) return;
            
            // Extract and format activity photos
            const activityPhotos = this.extractActivityPhotos(row);
            
            // Create download options
            const downloadOptions = this.createDownloadOptions(activityPhotos);
            
            // Create final button configuration
            const updatedButtonConfig = this.createButtonConfig(BASE_BUTTON_CONFIG, downloadOptions, row);
            
            // Store configuration
            this.baseButtonConfig[row.id] = updatedButtonConfig;
        });
    }

    private extractActivityPhotos(row: any): Array<{key: string; label: string; title: string; file_name: string; }> {
        if (!row?.activities || !row?.photos) {
            return [];
        }

        return row.activities.flatMap(activity => {
            const activityPhotos = row.photos[activity.key] || [];
            return activityPhotos.map((image, index) => ({
                key: `download_${image.id || index}`,
                label: `Download ${activity.title} ${index + 1}`,
                title: image.file_url || '',
                file_name: image.name
            }));
        });
    }

    private createDownloadOptions(activityPhotos: Array<any>): Array<any> {
        return [
            {
                key: 'download_pdf',
                label: 'Download PDF',
                title: 'Download PDF',
                mat_icon: ''
            },
            ...activityPhotos
        ];
    }

    private createButtonConfig(
        baseConfig: Array<ActionBtnEntry>, downloadOptions: Array<any>, row: any): Array<ActionBtnEntry> {
        return [
            baseConfig[0],
            baseConfig[1],
            {
                ...baseConfig[2],
                children: downloadOptions
            },
            baseConfig[3],
            {
                ...baseConfig[4],
                hide: !(row.signed_off)
            }
        ];
    }

    rowBtnClicked({entry}: {entry: any}, row: any): void {
        const actionMap = {
            'view': () => this.viewDailyActivity(row),
            'edit': () => this.editActivityModal(row),
            'share': () => this.openShareDailyActivityDetailModal(row),
            'download_pdf': () => this.getDailyActivity(row),
            'download_image': () => this.downloadImage(entry.title, entry.file_name),
            'review': () => this.viewDailyActivity(row, true)
        };

        // Check if action exists in map
        if (actionMap[entry.key]) {
            actionMap[entry.key]();
            return;
        }

        // Handle individual photo downloads
        if (entry.key.startsWith('download_')) {
            this.downloadImage(entry.title, entry.file_name);
        }
    }

    @ViewChild('instructionsModalRef')
    private instructionsModalRef: IModalComponent;
    instructionModal() {
        this.addInstruction();
        this.showModal = true;
        this.instructionsModalRef.open();
    }

    editInstruction(ind) {
        this.instruction = { ...this.dailyActivity.instructions[ind] };
        this.isEdit = true;
        this.instructionIndex = ind;
        this,this.showModal = true;
        this.instructionsModalRef.open();
    }

    addInstruction() {
        this.isEdit = false;
        if (this.dailyActivity.instructions && this.dailyActivity.instructions.length) {
            this.instruction = { source: null, comments: null };;
        } else {
            this.dailyActivity.instructions = [];
            this.instruction = { source: null, comments: null };
        }
    }

    saveInstruction(event) {
        if (this.isEdit) {
            this.dailyActivity.instructions[this.instructionIndex] = this.instruction;
        } else {
            this.dailyActivity.instructions.push(this.instruction);
        }
        event.closeFn();
    }

    deleteInstruction(i) {
        this.dailyActivity.instructions.splice(i, 1);
    }

    deleteDelays(i) {
        this.dailyActivity.delay_or_additional.splice(i, 1);
    }

    deleteActivity(i) {
        let key = this.dailyActivity.activities[i].key;
        delete this.dailyActivity.photos[key];
        delete this.dailyActivity.wpp[key];
        delete this.dailyActivity.activities_location[key];
        delete this.dailyActivity.activities_description[key];
        delete this.dailyActivity.progress[key];
        delete this.dailyActivity.task_briefing_sheets[key];
        let ind = 0;
        for (let item of this.dailyActivity.operatives) {
            if (item.hours[key]) {
                delete this.dailyActivity.operatives[ind].hours[key];
            }
            ind += 1;
        }
        ind = 0;
        for (let item of this.dailyActivity.plants) {
            if (item.hours[key]) {
                delete this.dailyActivity.plants[ind].hours[key];
            }
            ind += 1;
        }
        ind = 0;
        for (let item of this.dailyActivity.materials) {
            if (item.quantity[key]) {
                delete this.dailyActivity.materials[ind].quantity[key];
            }
            ind += 1;
        }
        this.dailyActivity.activities.splice(i, 1);
    }


    @ViewChild('delaysModalRef')
    private delaysModalRef: IModalComponent;
    delaysModal() {
        this.addDelays();
        this.showModal = true;
        this.delaysModalRef.open();
    }

    @ViewChild('additionalCommentRef')
    private additionalCommentRef: IModalComponent;
    additionalCommentModal() {
        this.addDelays();
        this.additionalCommentRef.open();
    }

    editDelays(ind) {
        this.delay = { ...this.dailyActivity.delay_or_additional[ind] };
        this.isEdit = true;
        this.delaysIndex = ind;
        if (this.delay.source === 'additional_comment') {
            this.additionalCommentRef.open();
        } else {
            this.showModal = true;
            this.delaysModalRef.open();
        }
    }

    addDelays() {
        this.isEdit = false;
        if (this.dailyActivity.delay_or_additional && this.dailyActivity.delay_or_additional.length) {
            this.delay = { source: null, comments: null };
        } else {
            this.dailyActivity.delay_or_additional = [];
            this.delay = { source: null, comments: null };
        }
    }

    saveAdditionalComments(event) {
        this.delay.source = 'additional_comment';
        if (this.isEdit) {
            this.dailyActivity.delay_or_additional[this.delaysIndex] = this.delay;
            this.isEdit = false;
        } else {
            this.dailyActivity.delay_or_additional.push(this.delay);
        }
        event.closeFn();
    }

    saveDelays(event) {
        if (this.isEdit) {
            this.dailyActivity.delay_or_additional[this.delaysIndex] = this.delay;
            this.isEdit = false;
        } else {
            this.dailyActivity.delay_or_additional.push(this.delay);
        }
        event.closeFn();
    }

    @ViewChild('plantHoursRef')
    private plantHoursRef: IModalComponent;
    openPlantModal() {
        this.newPlant = {
            operator: null,
            plant: null,
            hours: {}
        };
        let key = this.newActivity.key;
        this.newPlant.hours[key] = { minute: 0, hour: 0 };
        this.tempPlants = [ ...this.dailyActivity.plants ];
        this.manualPlantEntry = false;
        this.plantHoursRef.open();
    }

    saveTempPlant(form) {
        if (form.valid) {
            let index = this.tempPlants.findIndex(op => op.plant === this.newPlant.plant && op.operator === this.newPlant.operator);
            if (this.manualPlantEntry) {
                let payload = { name: this.newPlant.plant, project_ref: this.projectId };
                this.projectDailyActivitiesService.addMetaPlant(this.projectId, payload, this.employerId).subscribe((data: any) => {
                    if (data && data.success && data.plant) {
                        this.getListOfMetaPlants();
                    }
                });
            }
            if (index === -1) {
                this.tempPlants.push(JSON.parse(JSON.stringify(this.newPlant)));
            } else {
                this.tempPlants[index].hours[this.newActivity.key] = this.newPlant.hours[this.newActivity.key];
            }
            this.newPlant.hours[this.newActivity.key] = { minute: 0, hour: 0 };
            this.resetNewPlant();
            form.reset();
            this.manualPlantEntry = false;
        }
    }
    
    savePlant() {
        this.dailyActivity.plants = [ ...this.tempPlants ];
        this.plantHoursRef.close();
    }

    resetNewPlant() {
        this.newPlant = {
            operator: null,
            plant: null,
            hours: {}
        };
    }

    deleteAndSavePlant(index) {
        if (this.tempPlants.length === 1) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Delete Plant Hours',
                title: `Are you sure you want to delete last record? This will remove all plant hours from this record.`,
                confirmLabel: 'Yes',
                onConfirm: () => {
                    this.deletePlant(index);
                    this.savePlant();
                }
            });
        } else {
            this.deletePlant(index);
        }
    }

    deletePlant(index) {
        let pHours = this.tempPlants[index].hours;
        if (Object.keys(pHours).length === 1) {
            this.tempPlants.splice(index, 1);
        } else {
            delete this.tempPlants[index].hours[this.newActivity.key];
        }
    }

    onSort($event){
        let {sorts} = $event;
        let{ dir, prop } = sorts[0];
        this.sorts = { dir, prop };
        this.pageCallback({ offset: 0 }, true);
    }

    @ViewChild('addOperativeRef')
    private addOperativeRef: IModalComponent;
    addOperative() {
        if (!this.dailyActivity.operatives) {
            this.dailyActivity.operatives = [];
            this.tempOperatives = [];
        }
        this.newOperative = {
            name: null,
            trade: null,
            user_ref: null,
            employer: null,
            hours: {}
        };
        this.newOperative.hours[this.newActivity.key] = { minute: 0, hour: 0 };
        this.duplicateOperatorError = false;
        this.manualWorkforce = false;
        this.tempOperatives = [ ...this.dailyActivity.operatives ];
        this.showWorkforceModal = true;
        this.addOperativeRef.open();
    }

    manualllyAddWF(form) {
        this.operative = null;
        this.duplicateOperatorError = false;
        this.manualWorkforce = !this.manualWorkforce;
        if(!this.manualWorkforce) {
            form.reset();
            this.resetNewOperative();
        }
    }

    manualllyAddPlant(form) {
        this.manualPlantEntry = !this.manualPlantEntry;
        if(!this.manualPlantEntry) {
            form.reset();
            this.resetNewPlant();
        }
    }

    validateTime(obj) {
        if ((obj && obj.hour > 0) || (obj && obj.minute) > 0) {
            return true;
        }
        return false;
    }

    saveTempOperatorHours(form) {
        if (form.valid) {
            if (!this.manualWorkforce) {
                this.newOperative.name = this.operative.name;
                this.newOperative.trade = this.operative.trade;
            }
            let index = this.tempOperatives.findIndex(op => op.name === this.newOperative.name && op.trade === this.newOperative.trade);
            if (index === -1) {
                this.tempOperatives.push(this.newOperative);
            } else {
                this.tempOperatives[index].hours[this.newActivity.key] = this.newOperative.hours[this.newActivity.key];
            }
            this.resetNewOperative();
            this.newOperative.hours[this.newActivity.key] = { minute: 0, hour: 0 };
            this.operative = null;
            form.reset();
            this.manualWorkforce = false;
        }
    }

    saveOperatorHours() {
        this.dailyActivity.operatives = [ ...this.tempOperatives ];
        this.addOperativeRef.close();
    }

    resetNewOperative() {
        this.newOperative = {
            user_ref: null,
            name: null,
            trade: null,
            employer: null,
            hours: {}
        };
    }

    operatorSelected(op) {
        if (op) {
            let index = this.dailyActivity.operatives.findIndex(op => op.name === this.operative.name && op.trade === this.operative.trade);
            if (index != -1 && this.dailyActivity.operatives[index].hours[this.newActivity.key] !== undefined) {
                console.log("here in if")
                this.duplicateOperatorError = true;
                this.newOperative = {
                    user_ref: null,
                    name: null,
                    trade: null,
                    employer: null,
                    hours: {}
                };
                this.newOperative.hours[this.newActivity.key] = { minute: 0, hour: 0 };
                this.operative = '';
                return;
            }
            this.newOperative.user_ref = op.id;
        }
        this.duplicateOperatorError = false;
    }

    deleteAndSaveOperative(index) {
        if (this.tempOperatives.length === 1) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Delete Operative Hours',
                title: `Are you sure you want to delete last record? This will remove all operative hours from this record.`,
                confirmLabel: 'Yes',
                onConfirm: () => {
                    this.deleteOperative(index);
                    this.saveOperatorHours();
                }
            });
        } else {
            this.deleteOperative(index);
        }
    }

    deleteOperative(index) {
        let opHours = this.tempOperatives[index].hours;
        if (Object.keys(opHours).length === 1) {
            this.tempOperatives.splice(index, 1);
        } else {
            delete this.tempOperatives[index].hours[this.newActivity.key];
        }
    }

    @ViewChild('materialUsedRef')
    private materialUsedRef: IModalComponent;
    materialUsed() {
        let key = this.newActivity.key;
        this.newMaterial = {
            material: null,
            quantity: {}
        };
        this.newMaterial.quantity[key] = {
            qty: null, unit: null
        };
        this.tempMaterials = [ ...this.dailyActivity.materials ];
        this.showMaterialModal = true;
        this.materialUsedRef.open();
    }

    saveTempMaterial(form) {
        if (form.valid) {
            let index = this.tempMaterials.findIndex(op => op.material === this.newMaterial.material);
            if (index === -1) {
                this.tempMaterials.push(this.newMaterial);
            } else {
                this.tempMaterials[index].quantity[this.newActivity.key] = this.newMaterial.quantity[this.newActivity.key];
            }
            this.newMaterial = {
                material: null,
                quantity: {}
            };
            this.newMaterial.quantity[this.newActivity.key] = {
                qty: null, unit: null
            };
        }
    }

    saveMaterial() {
        this.dailyActivity.materials = [ ...this.tempMaterials ];
        this.showMaterialModal = false;
        this.materialUsedRef.close();
    }

    deleteAndSaveMaterial(index) {
        if (this.tempMaterials.length === 1) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Delete Material',
                title: `Are you sure you want to delete last record? This will remove all materials from this record.`,
                confirmLabel: 'Yes',
                onConfirm: () => {
                    this.deleteMaterial(index);
                    this.saveMaterial();
                }
            });
        } else {
            this.deleteMaterial(index);
        }
    }

    deleteMaterial(index) {
        let mat = this.tempMaterials[index].quantity;
        if (Object.keys(mat).length === 1) {
            this.tempMaterials.splice(index, 1);
        } else {
            delete this.tempMaterials[index].quantity[this.newActivity.key];
        }
    }

    @ViewChild('photosModalRef')
    private photosModalRef: IModalComponent;
    openPhotosModal() {
        if (!this.dailyActivity.photos[this.newActivity.key] || this.dailyActivity.photos[this.newActivity.key].length == 0) {
            this.dailyActivity.photos[this.newActivity.key] = [{}];
        } else {
            if (Object.keys(this.dailyActivity.photos[this.newActivity.key][0]).length > 0) {
                this.dailyActivity.photos[this.newActivity.key].unshift({});
            }
        }
        this.photosModalRef.open();
    }

    cleanDailyActivityPhotos() {
        this.dailyActivity.photos[this.newActivity.key] = this.dailyActivity.photos[this.newActivity.key].filter(s => s && s.id);
    }

    totalOperatives(key) {
        let total = 0;
        for (let op of this.dailyActivity.operatives) {
            if (op.hours[key] !== undefined) {
                total += 1;
            }
        }
        return total;
    }

    totalPlants(key) {
        let total = 0;
        for (let op of this.dailyActivity.plants) {
            if (op.hours[key] !== undefined) {
                total += 1;
            }
        }
        return total;
    }

    totalMaterial(key) {
        let total = 0;
        for (let op of this.dailyActivity.materials) {
            if (op.quantity[key] !== undefined) {
                total += 1;
            }
        }
        return total;
    }

    @ViewChild('addNewActivityRef')
    private addNewActivityRef: IModalComponent;
    addNewActivityModal() {
        this.isEditActivity = false;
        this.newActivity = {
            key: null,
            title: null,
        };
        if (this.dailyActivity.activities) {
            let i = this.dailyActivity.activities.length + 1;
            this.newActivity.key = "Activity_" + i.toString();
        } else {
            this.newActivity.key = "Activity_1";
        }
        this.showActivityModal = true;
        this.initActivityValues();
        this.addNewActivityRef.open();
    }

    openActivityModal(activity) {
        this.isEditActivity = true;
        this.newActivity = { ...activity };
        this.showActivityModal = true;
        this.initActivityValues();
        this.addNewActivityRef.open();
    }

    photoUploadDone(data: any) {
        if (data && data.userFile) {
            this.dailyActivity.photos[this.newActivity.key].splice(1, 0, ...data.userFile);
            this.dailyActivity.photos[this.newActivity.key][0] = {};
        }
    }

    fileDeleteDone($event, i) {
        if ($event && $event.userFile && $event.userFile.id) {
            this.dailyActivity.photos[this.newActivity.key].splice(i, 1);
        }
    }

    checkIfActivityExists() {
        let oldAct = this.dailyActivity.activities.find(item => item.key === this.newActivity.key);
        if (!oldAct) {
            this.dailyActivity.activities.push({ key: this.newActivity.key, title: this.newActivity.title });
            this.setValuesToActivityObj();
        } else {
            let index = this.dailyActivity.activities.indexOf(oldAct);
            this.dailyActivity.activities[index].title = this.newActivity.title;
            this.setValuesToActivityObj();
        }
    }

    private setValuesToActivityObj(): void {
        this.dailyActivity.activities_location[this.newActivity.key] = this.activityVal.location;
        this.dailyActivity.progress[this.newActivity.key] = this.activityVal.progress;
        this.dailyActivity.activities_description[this.newActivity.key] = this.activityVal.description;
        this.dailyActivity.wpp[this.newActivity.key] = this.activityVal.wppMethodStatement;
        this.dailyActivity.task_briefing_sheets[this.newActivity.key] = this.activityVal.taskBrief;
        this.dailyActivity.permit_number[this.newActivity.key] = this.activityVal.permitNumber;
    }

    private initActivityValues(): void {
        this.activityVal.location = this.dailyActivity.activities_location[this.newActivity.key] || '';
        this.activityVal.progress = this.dailyActivity.progress[this.newActivity.key] || 0;
        this.activityVal.description = this.dailyActivity.activities_description[this.newActivity.key] || '';
        this.activityVal.wppMethodStatement = this.dailyActivity.wpp[this.newActivity.key] || '';
        this.activityVal.taskBrief = this.dailyActivity.task_briefing_sheets[this.newActivity.key] || '';
        this.activityVal.permitNumber = this.dailyActivity.permit_number[this.newActivity.key] || '';
    }

    saveActivity(form) {
        if (form.valid) {
            this.checkIfActivityExists();
            this.addNewActivityRef.close();
        }
    }

    parseObjDurationToSecs(recordArray) {
        let duration: { hour: number, minutes: number } = null;
        for (let record of recordArray) {
            let recHours = record.hours;
            let activitiesWithHrs = recHours ? Object.keys(recHours): [];
            for(let j=0; j<activitiesWithHrs.length; j++) {
                duration = record.hours[activitiesWithHrs[j]];
                if (duration != null && duration != undefined) {
                    record.hours[activitiesWithHrs[j]] = this.getDurationAsSecs(duration);
                }
            }
        }
        return recordArray;
    }

    pad(num) {
        return (num.toString().length > 2) ? ("0" + num).slice(-3) : ("0" + num).slice(-2);
    }

    updateDailyActivity(event) {
        this.dailyActivity.shift_date = this.ngbMomentjsAdapter.ngbDateToDayJs(this.shift_date).format(AppConstant.apiRequestDateFormat);
        this.dailyActivity.shift_time = {
            from: this.pad(this.from.hour) + ':' + this.pad(this.from.minute),
            to: this.pad(this.to.hour) + ':' + this.pad(this.to.minute)
        }
        this.dailyActivity.operatives = this.parseObjDurationToSecs(this.dailyActivity.operatives);
        this.dailyActivity.plants = this.parseObjDurationToSecs(this.dailyActivity.plants);
        this.dailyActivity = this.cleanDailyActivity(this.dailyActivity);
        this.downloadDailyActivityReportLoading = true;
        for (let propName in this.dailyActivity.photos) {
            if (this.dailyActivity.photos[propName].length === 0) {
                delete this.dailyActivity.photos[propName];
            }
        }
        this.dailyActivity.project_ref = this.projectId;
        if (this.dailyActivity.id) {
            this.projectDailyActivitiesService.updateDailyActivity(this.projectId, this.dailyActivity.id, this.dailyActivity).subscribe((d: any) => {
                if (!d.success) {
                    const message = d.message || 'Failed to update daily activity.';
                    this.toastService.show(this.toastService.types.ERROR, message, { data: d });
                } else {
                    const message = 'Activity updated successfully.';
                    this.toastService.show(this.toastService.types.SUCCESS, message);
                    event.closeFn();
                    this.initializeTable(true);
                }
                this.downloadDailyActivityReportLoading = false;
            });
        } else {
            this.dailyActivity.submitted_by = this.authUser$.name;
            this.dailyActivity.submitted_datetime = dayjs().valueOf();
            this.projectDailyActivitiesService.createDailyActivity(this.projectId, this.dailyActivity).subscribe((d: any) => {
                if (!d.success) {
                    const message = d.message || 'Failed to create daily activity.';
                    this.toastService.show(this.toastService.types.ERROR, message, { data: d });
                } else {
                    const message = 'Activity report created successfully.';
                    this.toastService.show(this.toastService.types.SUCCESS, message);
                    event.closeFn();
                    this.initializeTable(true);
                }
                this.downloadDailyActivityReportLoading = false;
            });
        }
    }

    cleanDailyActivity(projectDailyActivity) {
        if (projectDailyActivity.photos && Object.keys(projectDailyActivity.photos).length) {
            for (let key in projectDailyActivity.photos) {
                projectDailyActivity.photos[key] = projectDailyActivity.photos[key].filter(s => s && s.id).map(photo => photo.id);
            }
        }
        return projectDailyActivity;
    }

    getDurationAsSecs(obj) {
        return obj.hour * 3600 + obj.minute * 60;
    }


    ngAfterViewInit() {

    }

    getActivitiesStr(activities) {
        return (activities || []).map(e => e.title).join(', ');
    };

    getActivitiesLocationStr(activities_localtion) {
        return Object.values(activities_localtion || {}).join(', ');
    };

    downloadImage(fileUrl, fileName) {
        saveAs(fileUrl, fileName);
    }

    getDailyActivity(row, target = 'pdf') {
        const body = {
            createdAt: row.createdAt,
            companyId: this.employerId,
            type: target
        };
        if (target === 'pdf') {
            this.downloadDailyActivityReportLoading = true;
            this.projectDailyActivitiesService.downloadDailyActivity(this.projectId, body, row.id, () => {
                this.downloadDailyActivityReportLoading = false;
            });
        } else {
            this.projectDailyActivitiesService.downloadDailyActivity(this.projectId, body, row.id).subscribe((html: any) => {
                const iframe = document.getElementById('dailyActivityContentFrame') as HTMLIFrameElement;
                if (typeof iframe !== 'undefined' && iframe !== null) {
                    const doc = iframe.contentDocument;
                    doc.write(html);
                    this.iframe_content_loading = false;
                }
            }, (err) => {
                console.log('error', err);
            });
        }
    }

    @ViewChild('dailyActivityViewContentRef')
    private dailyActivityViewContentRef: IModalComponent;
    viewDailyActivity(row, reviewing: boolean = false) {
        this.beingReviewed = reviewing;
        this.showNewActivityModal = true;
        this.dailyActivityViewContentRef.open();
        this.iframe_content_loading = true;
        this.selectedDailyActivity = row;
        this.getDailyActivity(row, 'html');
    }

    saveReviewSign(data) {
        this.daReviewSignature = (this.validSignature) ? data : null;
        if(!this.daReviewSignature) { return };
        this.iframe_content_loading = true;
        // this.daReviewSignature = data;
        let request = {
            reviewer: { name: this.authUser$.name, user_ref: this.authUser$.id, sign: this.daReviewSignature, timestamp: dayjs().valueOf() }
        };
        this.projectDailyActivitiesService.updateDailyActivity(this.projectId, this.selectedDailyActivity.id, request).subscribe((out: any) => {

            if (!out.success) {
                const message = out.message || 'Failed to store data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
            }
            this.iframe_content_loading = false;
            this.dailyActivityViewContentRef.close();
            this.initializeTable(true);
            this.clearSignature();
        });
    }
    pointsChanged(data) {
        let pointsCount = 0;
        (data || []).forEach(group => {
            pointsCount += group.points.length;
        });
        this.validSignature = pointsCount > 10;
    }
    clearSignature() { this.daReviewSignature = null; }

    @ViewChild('shareDaReportModal') shareDaReportModalRef: ShareToolReportToEmailComponent;
    openShareDailyActivityDetailModal(row) {
        this.shareDaReportModalRef.openEmailFormModal(row);
    }

    shareDailyActivityReport(event) {
        console.log(event);
        this.projectDailyActivitiesService.shareDailyActivityReport(event.req, event.reportId).subscribe((res: any) => {
            event.cb(res);
        });
    }

    autoPullAssetsStatusChange(event) {
        this.projectInfo = event.project;
        this.getListOfMetaPlants();
    }

    updateMetaActivityList(event) {
        this.metaActivitiesList = event.activities;
    }

    updateMetaPlantsList(event) {
        this.metaPlantsMachineryList = event.plantsList;
        if (this.projectInfo.custom_field.show_assets_in_plants_status) {
            event.projectAssetVehicles.forEach(vehicle => {
                let title = this.getVehicleType(vehicle.type_of_vehicle) + ' - ' + vehicle.serial_number;
                this.metaPlantsMachineryList.push({ name: title });
            });
        }
    }

    showLoader(event) {
        console.log("in show loader", event.loading)
        this.downloadDailyActivityReportLoading = event.loading;
    }

    populateVehicleType(event) {
        this.TYPE_OF_VEHICLE = event.vechileType;
    }
    searchFunction(data) {
        this.search = data.search.toLowerCase();
        this.initializeTable(true);
    }

    closeModal(event) {
        event.closeFn();
    }
    
    closePhotoModal(event) {
        event.closeFn();
        this.cleanDailyActivityPhotos();
    }

    closeMaterialModal() {
        this.materialUsedRef.close();
        this.showMaterialModal = false;
    }

    closeNewActivityModal() {
        this.addNewActivityRef.close();
        this.showActivityModal = false;
    }

    closeAddNewActivityModal() {
        this.dailyActivityViewContentRef.close();
        this.showNewActivityModal = false;
    }

    public onActionSelection(actionVal: ActionButtonVal) {
        const code = actionVal.code;
        if(code === DailyActivitiesActionButtons.PRELOAD_ACTIVITIES) {
            this.dailyActivitiesImportModal();
        } else if(code === DailyActivitiesActionButtons.PRELOAD_PLANT_MACHINERY) {
            this.metaPlantsImportModal();
        } else if(code === DailyActivitiesActionButtons.DOWNLOAD_REPORT) {
            this.openDailyActivitiesReportModal();
        }
    }

    openDailyActivitiesReportModal() {
        this.reportDownloader.openModal();
    }

    async dailyActivitiesReportDownload(event) {
        this.xlsxReportType = event.selection.activity;
        this.activityReportFormat = event.selection.type;
        await this.initiateDownload(event.selection);
        event.closeFn();
    }
}

<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav *ngIf="!isProjectPortal" [employer]="employer" [companyResolverResponse]="companyResolverResponse" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="col-sm-12 my-3 outer-border" *ngIf="!loadingCoWs">
                <div class="col-sm-12 outer-border-radius">
            <div class="d-flex flex-column flex-sm-row flex-wrap justify-content-between mb-2 pb-2">
                <h5 class="float-left">Total Defects
                    <small>
                       (Open {{ openCount }}: Raised {{ raisedCount }})
                    </small>
                 </h5>
                     <!-- <button *ngIf="allPinsCoordinates.length" (click)="cowAllLocationPinsModal()" class="ml-1 float-right btn btn-sm btn-im-helmet">
                         <i class="fas fa-map-marked-alt"></i>
                     </button> -->
                    <button class="btn btn-sm btn-brandeis-blue other-action-btn justify-content-center d-flex align-items-center pointer-cursor" (click)="openClerkOfWorksReportModal()" id="dropdownDlReport1">
                        <span class="medium-font m-font-size material-symbols-outlined mr-2">download</span>
                        <div class="medium-font m-font-size">Download Report</div>
                    </button>
            </div>
            <div class="col-md-12 mb-3 d-inline-block p-0">
                <div class="col-md-12 d-inline-block pl-0 text-center">
                        <search-with-filters #searchFilters (searchEmitter)="searchFunction($event)" (filterEmitter)="onFilterSelection($event)" [filterData]="filterData"></search-with-filters>
                    </div>
            </div>
            <div class="clearfix"></div>
                <div class="table-responsive-sm">
                <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                [scrollbarV]="true"
                                [virtualization]="false"
                                [loadingIndicator]="loadingInlineClerkOfWork"
                                [rows]="filteredRecords"
                                [footerHeight]="40"
                                [columnMode]="'force'"
                                [rowHeight]="'auto'"
                                [externalPaging]="true"
                                [count]="page.totalElements"
                                [offset]="page.pageNumber"
                                [limit]="page.size"
                                (page)="pageCallback($event, true)"
                >
                    <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Ref.
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            {{row.cow_ref}}
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Raised
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            {{ dayjsDisplayDT(row.createdAt)}}
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Location
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <span appTooltip>{{row?.cow_location}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold" *ngIf="hasBlocks" [sortable]="false" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Block
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <span appTooltip>{{row?.project_blocks}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold" *ngIf="hasLevels" [sortable]="false" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Level
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <span appTooltip>
                                {{row?.project_levels?.join(',')}}
                            </span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Category
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <span appTooltip>{{row?.cow_category}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Owner
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <span appTooltip>{{row?.cow_owner}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold text-center" [sortable]="false" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Status
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <div class="text-center">
                                <span class="btn-sm badge-pill {{(row.status_message == 'Open') ? 'badge-danger' : ((row.status_message == 'Ongoing') ? 'cow-ongoing' : 'badge-success')}}">
                                    {{ row.status_message }}
                                </span>
                            </div>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Close Out Date
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            {{(row?.closed_out_date) ? dayjsDisplayDT(+row?.closed_out_date, true) : ''}}
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold action-column"
                                          cellClass="action-column no-ellipsis" [sortable]="false" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Action
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <button-group  
                                [buttons]="baseButtonConfig[row.id]"
                                (onActionClick)="rowBtnClicked($event, row)">
                            </button-group>
                        </ng-template>
                    </ngx-datatable-column>
                </ngx-datatable>

                <i-modal #cowDetailsRef [title]="projectInfo.cow_setting.cow_phrase + ' #' + cow_row?.cow_ref + ' Details'" size="lg" [showCancel]="false" rightSecondaryBtnTxt="Edit" 
                    (onClickRightSB)="editCowModal(cow_row)" leftPrimaryBtnClass="btn-danger" leftPrimaryBtnTxt="Delete" (onClickLeftPB)="deleteCowModal(cow_row)" 
                    (onClickRightPB)="closeOutModal(cow_row, c)" [rightPrimaryBtnTxt]="(cow_row.status_message !== 'Closed') ? 'Close Out' : ''">
                        <table class="table table-sm table-bordered" style="font-size: 14px;">
                            <tbody>
                            <tr>
                                <td class="tr-bg-dark-color" style="width: 25%;">
                                    <strong>Raised:</strong>
                                </td>
                                <td>
                                    {{dayjsDisplayDT(cow_row.createdAt)}}
                                </td>
                            </tr>
                            <tr>
                                <td class="tr-bg-dark-color" style="width: 25%;">
                                    <strong>Raised By:</strong>
                                </td>
                                <td>
                                    {{cow_row?.user_ref?.first_name}} {{cow_row?.user_ref?.last_name}}
                                </td>
                            </tr>
                            <tr>
                                <td class="tr-bg-dark-color" style="width: 25%;">
                                    <strong>Location:</strong>
                                </td>
                                <td>
                                    {{ cow_row?.cow_location }}

                                    <div *ngIf="cow_row?.pins && cow_row?.pins.length" class="btn-group dd-wo-caret" ngbDropdown container="body">
                                        <a class="btn btn-sm btn-outline-primary mr-1" ngbDropdownToggle>
                                            <i class="fas fa-map-marked-alt"></i>
                                        </a>
                                        <ul ngbDropdownMenu class="table-dropdown-menu" style="top:37px; max-height: 270px; overflow-y: scroll; right:0px; width: 175px;">
                                            <ng-template ngFor let-pin let-i="index" [ngForOf]="cow_row.pins">
                                                <li *ngIf="pin">
                                                    <a title="Show Level Pin" ngbDropdownItem class="table-dropdown-item" (click)="cowLocationPinModal(pin, cow_row.status)" href="javascript:void(0)">
                                                        Level {{pin.level}}
                                                    </a>
                                                </li>
                                            </ng-template>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <tr *ngIf="!prjct_access?.has_blocks && cow_row?.project_blocks">
                                <td class="tr-bg-dark-color" style="width: 25%;">
                                    <strong>Block:</strong>
                                </td>
                                <td>
                                    {{ cow_row?.project_blocks }}
                                </td>
                            </tr>
                            <tr *ngIf="!prjct_access?.has_levels && cow_row?.project_levels">
                                <td class="tr-bg-dark-color" style="width: 25%;">
                                    <strong>Level:</strong>
                                </td>
                                <td>
                                    {{ cow_row?.project_levels }}
                                </td>
                            </tr>

                            <tr>
                                <td class="tr-bg-dark-color" style="width: 25%;">
                                    <strong>Category:</strong>
                                </td>
                                <td>
                                    {{ cow_row?.cow_category }}
                                </td>
                            </tr>
                            <tr>
                                <td class="tr-bg-dark-color" style="width: 25%;">
                                    <strong>Owner:</strong>
                                </td>
                                <td>
                                    {{ cow_row?.cow_owner }}
                                </td>
                            </tr>
                            <tr>
                                <td class="tr-bg-dark-color" style="width: 25%;">
                                    <strong>Observation:</strong>
                                </td>
                                <td>
                                    {{ cow_row?.cow_observations }}
                                </td>
                            </tr>
                            <tr>
                                <td class="tr-bg-dark-color" style="width: 25%;">
                                    <strong>Remedial Action:</strong>
                                </td>
                                <td>
                                    {{ cow_row?.cow_remedial_action }}
                                </td>
                            </tr>
                            <tr *ngIf="cow_row?.cow_images && cow_row?.cow_images.length">
                                <td colspan="2">
                                    <pop-up-image-viewer [alt]="projectInfo.cow_setting.cow_phrase+' Image'" [imgArray]="cow_row?.cow_images || []"></pop-up-image-viewer>
                                </td>
                            </tr>
                            <tr *ngIf="cow_row?.comments?.length">
                                <td colspan="2" class="tr-bg-dark-color">
                                    <strong>Comments:</strong>
                                </td>
                            </tr>
                            <tr *ngIf="cow_row?.comments?.length">
                                <td colspan="2" >
                                    <ul class="pl-2 list-unstyled">
                                        <li *ngFor="let comment of (cow_row?.comments)">
                                            <div class="col-sm-9 float-left p-0">
                                                <b>{{ comment.added_by }}</b>:
                                                <small>{{ comment.comment }}</small>
                                                <pop-up-image-viewer [updateStyle]="{'max-height.px': 150, 'max-width.px': 150}" [alt]="projectInfo.cow_setting.cow_phrase+' Comment Image'" [imgArray]="comment?.images || []"></pop-up-image-viewer>
                                            </div>
                                            <small class="float-right">{{ dayjsDisplayDT(+comment.createdAt, true) }}</small>
                                        </li>
                                    </ul>
                                </td>
                            </tr>
                            </tbody>
                        </table>

                        <div class="mt-4" *ngIf="cow_row?.closed_out_date">
                            <div class="w-100">
                                <h4>Closeout Details</h4>
                            </div>
                            <table class="table table-sm table-bordered" style="font-size: 14px; table-layout:fixed;">
                                <tbody>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 25%;">
                                            <strong>Closed Date:</strong>
                                        </td>
                                        <td>
                                            {{dayjsDisplayDT(+cow_row?.closed_out_date, true)}}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                </i-modal>
                <i-modal #commentRef [title]="' Add comment for CoW #' + cow_row?.cow_ref" size="md" (onClickRightPB)="addCommentRequest(cowCommentForm, modalLoader, $event)" rightPrimaryBtnTxt="Add" [rightPrimaryBtnDisabled]="!cowCommentForm.valid">
                        <!-- <p class="text-center">Are you sure you would like to close this out?</p> -->
                        <form novalidate #cowCommentForm="ngForm">
                            <div class="mb-2">
                                <p class="mb-1">Comment: <small class="required-asterisk ">*</small></p>
                                <input type="hidden" name="clerk_of_works" id="cow_id" [(ngModel)]="cow_row"/>
                                <textarea class="form-control" name="cow_comment"
                                    [(ngModel)]="cow_row.cow_comment"
                                    ng-value="cow_row.cow_comment"
                                    placeholder="{{ projectInfo.cow_setting.cow_phrase }} comment..."
                                    #cowComment="ngModel" required></textarea>
                            </div>
                            <div class="form-group">
                                <label>Images</label>
                                <div class="col-md-12 p-0 mb-4">
                                    <div class="col-md-12 flex-grow-1 p-0" *ngFor="let c of addedCommentImgs; let i = index">
                                        <file-uploader-v2
                                            [disabled]="false"
                                            [init]="c"
                                            [category]="'project-cow-comments'"
                                            (uploadDone)="mediaUploadDone($event)"
                                            [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png']"
                                            (deleteFileDone)="fileDeleteDone($event, i)"
                                            [showDeleteBtn]="true"
                                            [showFileName]="false" [multipleUpload]="true"
                                            [hasImgAndDoc]="true"
                                        ></file-uploader-v2>
                                    </div>
                                </div>
                            </div>
                        </form>
                    <block-loader [show]="(false)" #modalLoader></block-loader>
                </i-modal>
                <i-modal #downloadXlsxRef title="Xlsx download filters" size="md" (onClickRightPB)="filterAndDownload($event)" rightPrimaryBtnTxt="Download">
                        <div *ngIf="isProjectBlocks">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input"
                                       name="'dw_block_select_all'"
                                       (change)="selectAllBlocks($event)"
                                       [checked]="allBlocksSelected()"
                                       id="dw_block_select_all">
                                <label class="custom-control-label" for="dw_block_select_all"><span class="font-weight-bold">Select All Blocks</span></label>
                            </div>
                            <div class="custom-control custom-checkbox" *ngFor="let block of cow_blocks;">
                                <input type="checkbox" class="custom-control-input"
                                       [name]="'dw_block' + block"
                                       (change)="changeModel($event, block)"
                                       [id]="'dw_block' + block" [checked]="selectedBlocks.includes(block)">
                                <label class="custom-control-label" [for]="'dw_block' + block"><span class="font-weight-bold">Block {{ block }}</span></label>
                            </div>
                        </div>
                        <div class="border-top mt-2 pt-2 form-inline p-0">
                            <div class="custom-control custom-checkbox mr-3">
                                <input type="checkbox" class="custom-control-input"
                                   name="dw_open"
                                   [(ngModel)]="cowDwStatus.open"
                                   id="dw_open">
                                <label class="custom-control-label text-danger" for="dw_open"><span class="font-weight-bold">Open</span></label>
                            </div>
                            <div class="custom-control custom-checkbox mr-3">
                                <input type="checkbox" class="custom-control-input"
                                   name="dw_closed"
                                   [(ngModel)]="cowDwStatus.closed"
                                   id="dw_closed">
                                <label class="custom-control-label text-success" for="dw_closed"><span class="font-weight-bold">Closed</span></label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input"
                                   name="dw_ongoing"
                                   [(ngModel)]="cowDwStatus.ongoing"
                                   id="dw_ongoing">
                                <label class="custom-control-label cow-ongoing-text" for="dw_ongoing"><span class="font-weight-bold">Ongoing</span></label>
                            </div>
                        </div>
                        <div class="border-top mt-2 pt-2 form-inline p-0 d-none">
                            <div class="custom-control custom-checkbox mr-3">
                                <input type="checkbox" class="custom-control-input"
                                   name="dw_print"
                                   [(ngModel)]="downloadPrintVersion"
                                   id="dw_print">
                                <label class="custom-control-label" for="dw_print"><h6>Download Print Version</h6></label>
                            </div>
                        </div>
                    <block-loader [show]="(false)" #modalLoader></block-loader>
                </i-modal>
                <i-modal #editCowPopupRef [title]="'Edit ' + projectInfo.cow_setting.cow_phrase + ' Ref #' + cow_row?.cow_ref" size="md" (onClickRightPB)="editCowRequest(editCowForm, $event)" rightPrimaryBtnTxt="Save" [rightPrimaryBtnDisabled]="!editCowForm.valid">
                    <form novalidate #editCowForm="ngForm">
                        <ng-container *ngIf="showModal">
                            <div class="form-group">
                                <label>Location <small class="required-asterisk">*</small></label>
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control" #cow_location="ngModel" ng-value="cow_row.cow_location" placeholder="Location" name="cow_location" required [(ngModel)]="cow_row.cow_location">
                                </div>
                            </div>
                            <div class="form-group" *ngIf="isProjectBlocks">
                                <label>Block</label>
                                <div class="input-group mb-3">
                                    <ng-select style="width: 100%;" [items]="cowBlockRange" placeholder="Select Block" name="project_blocks" #project_blocks="ngModel" ng-value="cow_row.project_blocks" [(ngModel)]="cow_row.project_blocks"></ng-select>
                                </div>
                            </div>
                            <div class="form-group" *ngIf="isProjectLevels">
                                <label>Levels</label>
                                <div class="input-group mb-3">
                                    <ng-select style="width: 100%;" [multiple]="true" [items]="cowLevelRange" placeholder="Select Levels" name="project_levels" #project_levels="ngModel" ng-value="cow_row.project_levels" [(ngModel)]="cow_row.project_levels"></ng-select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Category <small class="required-asterisk">*</small></label>
                                <div class="input-group mb-3">
                                    <ng-select name="cow_category" #cow_category="ngModel" [(ngModel)]="cow_row.cow_category"
                                               class="w-100"
                                               placeholder="Select Category" required>
                                        <ng-option *ngFor="let t of cowCategory" [value]="t.key" >{{ t.value }}</ng-option>
                                    </ng-select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Owner</label>
                                <div class="input-group mb-3">
                                    <ng-select style="width: 100%;" [items]="employers_list" bindLabel="name" [bindValue]="'name'" placeholder="Select Owner" name="cow_owner" #cow_owner="ngModel" ng-value="cow_row.cow_owner" [(ngModel)]="cow_row.cow_owner"></ng-select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Observations</label>
                                <div class="input-group mb-3">
                                    <textarea class="form-control" name="cow_observations"
                                    [(ngModel)]="cow_row.cow_observations"
                                    ng-value="cow_row.cow_observations"
                                    placeholder="Observations"
                                    #cow_observations="ngModel" truncate></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Remedial Action</label>
                                <div class="input-group mb-3">
                                    <textarea class="form-control" name="cow_remedial_action"
                                    [(ngModel)]="cow_row.cow_remedial_action"
                                    ng-value="cow_row.cow_remedial_action"
                                    placeholder="Remedial Action"
                                    #cow_remedial_action="ngModel" truncate></textarea>
                                </div>
                            </div>
                            <input type="hidden" name="id" id="cow_id" [(ngModel)]="cow_row.id"/>
                            <input type="hidden" name="project_ref" id="cow_project_ref" [(ngModel)]="cow_row.project_ref.id"/>
                        </ng-container>
                    </form>
                    <block-loader [show]="saveCowLoading"></block-loader>
                </i-modal>
                <ng-template #viewDroppedPinImgHtml let-c="close" let-d="dismiss">
                    <div class="modal-header">
                        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group row">
                            <div class="col-sm-12 mt-2 text-center">
                                <div class="d-inline-block position-relative">
                                    <img [src]="siteDrawingImgSrc" (error)="onLogoError(img, siteDrawingImgSrc)" (load)="onLogoLoad(img)" style="width: 100%; height: auto;" #img />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer" *ngIf="showingAllPins">
                        <div class="col-4">
                            <ng-select [items]="allPinLevels" (change)="updateSiteDrawingImg()" bindLabel="level" bindValue="level" placeholder="Select Level" [(ngModel)]="pinLevel" style="width: 100%;"></ng-select>
                        </div>
                        <div class="col-7">
                            <button type="button" class="btn btn-outline-primary float-right" (click)="downloadPinnedSiteDrawing(c)">
                                Download PDF
                            </button>
                        </div>
                    </div>
                </ng-template>
            </div>
            <div class="clearfix"></div>
            <block-loader [show]="exportInProgress" [showBackdrop]="true"></block-loader>
        </div>
        </div>
        </div>
    </div>
    <block-loader [show]="(downloadingSiteD || loadingCoWs)" [showBackdrop]="loadingCoWs" alwaysInCenter="true"></block-loader>
</div>
<block-loader [show]="loadingCoWs" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
<report-downloader #reportDownloader 
    [xlsxOnly]="true" 
    (onFilterSelection)="clerkOfWorksReportDownload($event)"
    >
</report-downloader>
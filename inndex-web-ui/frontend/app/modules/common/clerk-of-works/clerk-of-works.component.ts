import {Component, OnInit, TemplateRef, ViewChild} from '@angular/core';
import { HttpParams } from '@angular/common/http';
import {ActivatedRoute, Router} from "@angular/router";
import {Observable, Observer, forkJoin} from "rxjs";
import {map, share} from "rxjs/operators";
import {NgbModalConfig, NgbModal, NgbDateStruct} from '@ng-bootstrap/ng-bootstrap';
import {
    ClerkOfWorksService,
    AuthService,
    UserService,
    User,
    Project,
    ProjectService,
    CreateEmployer,
    ExcelUtility,
    ResourceService,
    isInheritedProjectOfCompany,
    Common,
    HttpService,
    ClerkOfWork,
    ToastService
} from "@app/core";
import * as dayjs from 'dayjs';
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";

import * as Excel from "exceljs/dist/exceljs.min.js";
import * as ExcelProper from "exceljs";
import * as fs from 'file-saver';
import {AppConstant} from "@env/environment";
import { SearchWithFiltersComponent } from '../search-with-filters/search-with-filters.component';
import { filterData } from '@app/core';
import { ReportDownloaderComponent } from '../report-downloader/report-downloader.component';
import { IModalComponent, GenericConfirmationModalComponent, ActionBtnEntry } from '@app/shared';

@Component({
    templateUrl: './clerk-of-works.component.html',
    // add NgbModalConfig and NgbModal to the component providers
    providers: [NgbModalConfig, NgbModal],
    styleUrls: ['./clerks-of-works.component.scss'],
})
export class ClerkofWorksComponent implements OnInit {
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    @ViewChild('reportDownloader') private reportDownloader: ReportDownloaderComponent;
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;
    blobType: string = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    filteredRecords: Array<any> = [];
    categorys: Array<any> = [];
    owners: Array<any> = [];
    base64Image: any;
    projectName: any;
    employerId: number = 0;
    employerInfo: CreateEmployer;
    projects: Array<Project> = [];
    project: Observable<{}>;
    records: Array<any> = [];
    cow_row: ClerkOfWork = new ClerkOfWork();
    cow_blocks:  Array<any> = [];
    cowBlockRange:  Array<any> = [];
    cowLevelRange:  Array<any> = [];
    selectedBlocks:  Array<any> = [];
    authUser$: User;
    openRecords: Array<any> = [];
    exportInProgress: boolean = false;
    totalCoWs: Array<any> = [];
    projectsLoading: boolean = false;
    isProjectBlocks: boolean = false;
    isProjectLevels: boolean = false;
    employer: any = {};

    hasBlocks: boolean = false;
    hasLevels: boolean = false;
    modalCallBack: any;
    tableLoader: boolean = false;
    employers_list: Array<any> = [];
    saveCowLoading: boolean = false;
    downloadPrintVersion: boolean = false;
    addedCommentImgs: Array<any> = [];
    cowDwStatus: any = {open:true, closed:true, ongoing:true};
    cowCategory = [];
    cowPhrase: string = `Clerk of Works`;
    is_inherited_project: boolean = false;
    loadingCoWs: boolean = false;
    prjct_access: any;
    siteDrawingImgSrc: string = '';
    markerStyle: object = {};
    logoImgRetries:number = 5;
    allPinsCoordinates: Array<any> = [];
    showingAllPins: boolean = false;
    downloadingSiteD: boolean = false;
    allPinLevels: Array<any> = [];
    pinLevel: any;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    paginationData = new Common();
    page = this.paginationData.page;
    openCount:number = 0;
    raisedCount:number = 0;
    globalSearch: any = '';
    categoryFilter: any[]=[];
    ownerFilter: any[]=[];
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    loadingRecords: boolean = false;
    isProjectPortal: boolean = false;
    isMobileDevice: boolean = false;
    filterData:filterData[] = this.renderFilterData();
    showModal: boolean = false;
    reloadFilterData : boolean = true;
    isInitClerkOfWork = false;
    loadingInlineClerkOfWork: boolean = false;
    baseButtonConfig: Record<string, ActionBtnEntry[]> = {};

    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private clerkOfWorksService: ClerkOfWorksService,
        private excelUtility: ExcelUtility,
        private modalService: NgbModal,
        private authService: AuthService,
        private projectService: ProjectService,
        private userService: UserService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private resourceService: ResourceService,
        private httpService: HttpService,
        private toastService: ToastService,
    ) {
        this.from_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.to_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
        }
    }

    dayjs(n: number) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };

    dayjsDisplayDT(n: number, onlyDate = false) {
        return this.dayjs(n).format(onlyDate ? AppConstant.defaultDateFormat : AppConstant.fullDateTimeFormat);
    };

    async initiateDownload(resp) {
        this.from_date = resp.fromDate;
        this.to_date = resp.toDate;
        this.proccessXlsxDownload();
    }

    ngOnInit() {
        if(!this.isProjectPortal) {
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
                this.employerId = params['employerId'];
            });
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
            this.employerInfo = this.employer;
        } else {
            this.initializeTable();
        }
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });

        this.resourceService.getInnDexSettingByName('cow_categories').subscribe((data: any) => {
            if (data.success && data.record && data.record.value) {
                this.cowCategory = data.record.value;
            } else {
                const message = 'Something went wrong while fetching cow categories.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    filterRecords() {
        let globalSearch = (this.globalSearch || '').toLowerCase();
        let categoryFilter = (this.categoryFilter.length > 0 ? this.categoryFilter.join(",").toLowerCase().split(",") : []);
        let ownerFilter =   (this.ownerFilter.length > 0 ? this.ownerFilter.join(",").toLowerCase().split(",") : []);

        // filter data
        const temp = this.records.filter(function (d) {
            let globalFlag = true;
            if (globalSearch.length) {
                globalFlag = (
                    (d.cow_location && d.cow_location.toLowerCase().indexOf(globalSearch) !== -1) ||
                    (d.project_levels?.join(',') && d.project_levels?.join(',').toLowerCase().indexOf(globalSearch) !== -1) ||
                    (d.cow_owner && d.cow_owner.toLowerCase().indexOf(globalSearch) !== -1) ||
                    (d.cow_category && d.cow_category.toLowerCase().indexOf(globalSearch) !== -1) ||
                    (d.cow_ref && d.cow_ref.includes(globalSearch))
                );
            }
            let categoryTypeFlag = true;
            if (categoryFilter.length) {
                categoryTypeFlag = (d.cow_category && categoryFilter.includes(d.cow_category.toLowerCase()));
            }
            let ownerFilterFlag = true;
            if (ownerFilter.length) {
                ownerFilterFlag = (d.cow_owner && ownerFilter.includes(d.cow_owner.toLowerCase()));
            }
            return (globalFlag && categoryTypeFlag && ownerFilterFlag);
        });
        // update the records
        this.filteredRecords = temp;
    }
/*
    initializeProjectsByEmployer() {
        this.projectsLoading = true;
        this.projectService.getCompanyProjects(this.employerId).subscribe((data: any) => {
            this.projectsLoading = false;
            if (data.success) {
                this.projects = [ ...data['projects'].employer_projects, ...data['projects'].additional_projects];
            } else {
                console.log('get Projects call failed', data);
                alert(data.message ? data.message : 'Failed to get data');
            }
        });
    }*/

    // Will get called for Company route only.
    projectRetrieved(project: Project|any){
        this.projectInfo = project;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
        this.initializeTable();
    }

    @ViewChild('searchFilters') searchFilters: SearchWithFiltersComponent;
    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean) {
        if (!this.isInitClerkOfWork) {
          this.isInitClerkOfWork = true;
          return;
        }
        this.page.pageNumber = pageInfo.offset;
        this.scrollToTop()
        this.initializeTable(isPageChange);
    }

    private async initializeTable(isPageChange?: boolean) {
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('is_inherited_project', `${this.is_inherited_project}`);

        if ( this.globalSearch ) {
            params = params.append('q', `${this.globalSearch}`)
        }
        if( this.ownerFilter.length ){
            params = params.append('owner', this.ownerFilter.join(','))
        } 
        if( this.categoryFilter.length ){
            params = params.append('category', this.categoryFilter.join(','))
        }
        let prjctCowSettings = this.projectInfo.cow_setting;
        this.prjct_access = this.projectInfo.project_section_access;
        this.from_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(this.projectInfo.createdAt));
        this.isProjectBlocks = !this.prjct_access.has_blocks && prjctCowSettings.company_project_blocks > 0;
        this.isProjectLevels = !this.prjct_access.has_levels && prjctCowSettings.company_project_levels > 0;
        this.projectName = this.projectInfo.name;
        this.cowBlockRange = this.createRange(+prjctCowSettings.company_project_blocks);
        this.cowLevelRange = [...prjctCowSettings.non_numeric_levels, ...this.createRange(+prjctCowSettings.company_project_levels)];
        this.cowPhrase = prjctCowSettings.cow_phrase;
        if (isPageChange) {
          this.loadingInlineClerkOfWork = true;
        } else {
          this.loadingCoWs = true;
        }

        let serviceCalls = [this.clerkOfWorksService.getClerkOfWorks(this.projectId, this.employerId, params), this.clerkOfWorksService.getProjectCowFilterData(this.projectId)];

        forkJoin(serviceCalls)
          .subscribe(
            ([clerkOfWorksData,filterData]:[any,{ cowCategories:string[]; cowOwners:string[] }]) => {
             
              this.openRecords = [];
              this.totalCoWs = [];
              this.loadingCoWs = false;
              this.loadingInlineClerkOfWork = false;
        
              if (clerkOfWorksData && clerkOfWorksData.project_cows) {
                this.records = clerkOfWorksData.project_cows;
                this.page.totalElements = clerkOfWorksData.records_total;
                this.raisedCount = clerkOfWorksData.total_record_count;
                this.openCount = clerkOfWorksData.open_count;

                const BASE_BUTTON_CONFIG: Array<ActionBtnEntry> = [
                    {
                        key: 'view',
                        label: '',
                        title: `View ${(this.projectInfo ? this.projectInfo.cow_setting.cow_phrase : '')}`,
                        mat_icon: 'search'
                    },
                    {
                        key: 'download', 
                        label: '',
                        title: 'Download',
                        mat_icon: 'download',
                        children: []
                    },
                    {
                        key: 'add_comment',
                        label: '',
                        title: 'Add Comments',
                        mat_icon: 'chat_bubble'
                    },
                    {
                        key: 'close',
                        label: '',
                        title: 'Close Out',
                        mat_icon: 'close'
                    }
                ];
        
                this.records.forEach(r => {
                  if ([1, 3].includes(r.status)) {
                    (r.pins || []).forEach(pin => {
                      this.allPinsCoordinates.push({
                        "level": pin.level,
                        "drawing_url": (pin.file_id && pin.file_id.file_url) ? pin.file_id.file_url : null,
                      });
                    });
                    this.openRecords.push(r);
                  }
        
                  if ((this.prjct_access && !this.prjct_access.has_levels) && !this.hasLevels && r.project_levels && r.project_levels.length > 0 && r.project_levels[0] !== 0) {
                    this.hasLevels = true;
                  }
        
                  if ((this.prjct_access && !this.prjct_access.has_blocks) && !this.hasBlocks && r.project_blocks > 0) {
                    this.hasBlocks = true;
                  }
        
                  this.totalCoWs.push(r);
                  // Add button group configuration for each record
                  if (r?.id) {
                    const photos = this.photoDownloadOpts(r.cow_images || []);
                    const downloadOptions = [
                        {
                            key: 'download_pdf',
                            label: 'Download PDF',
                            title: 'Download PDF',
                            mat_icon: ''
                        },
                        ...this.createPhotoDownloadOptions(photos)
                    ];

                    const updatedButtonConfig = [
                        BASE_BUTTON_CONFIG[0],
                        {
                            ...BASE_BUTTON_CONFIG[1],
                            children: downloadOptions
                        },
                        {
                            ...BASE_BUTTON_CONFIG[2],
                            hide: (r.status_message === 'Closed')
                        },
                        {
                            ...BASE_BUTTON_CONFIG[3],
                            hide: (r.status_message === 'Closed')
                        }
                    ];
                    this.baseButtonConfig[r.id] = updatedButtonConfig;
                  }
                });
        
                this.allPinLevels = this.allPinsCoordinates.filter((s1, pos, arr) => arr.findIndex((s2) => s2.level === s1.level) === pos);
                this.filteredRecords = this.records;
              } else {
                const message = `Failed to fetch clerkOfWorks, id: ${this.projectId}.`;
                this.toastService.show(this.toastService.types.ERROR, message, { data: clerkOfWorksData });
              }
        
              if (this.reloadFilterData) {
                this.reloadFilterData = false;
                this.categorys = filterData.cowCategories;
                this.owners = filterData.cowOwners;
                this.filterData = this.renderFilterData();
              }
            },
            (error) => {
              console.error('Error fetching data:', error);
              this.loadingCoWs = false;
            }
          );
    }

    photoDownloadOpts(array){
        return array.sort((a, b) => b?.id - a?.id);
    }

    createPhotoDownloadOptions(photos: any[]): Array<ActionBtnEntry> {
        return photos.map((photo, index) => ({
            key: `download_${photo.id}`,
            label: `Download ${(photo?.file_mime == "application/pdf") ? 'PDF' : 'Image'} ${index+1}`,
            title: photo.file_url || '',
        }));
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actionMap = {
            'view': () => this.clerkOfWorksDetailModal(row),
            'download_pdf': () => this.downloadClerkOfWorks(row),
            'add_comment': () => this.cowCommentModal(row),
            'close': () => this.closeOutModal(row, () => {})
        };

        // Check if action exists in map
        if (actionMap[entry.key]) {
            actionMap[entry.key]();
            return;
        }

        // Handle individual photo downloads
        const photos = this.photoDownloadOpts(row?.cow_images || []);
        const downloadOptions = this.createPhotoDownloadOptions(photos);
        
        const matchingOption = downloadOptions.find(opt => opt.key === entry.key);
        if (matchingOption) {
            this.downloadImage(matchingOption.title, matchingOption.label);
        }
    }

    getEmployersList(){
        let country_code = this.isProjectPortal ? (this.projectInfo?.custom_field?.country_code || undefined) : (this.employer?.country_code || undefined);
        this.userService.getEmployer(country_code).subscribe((data: any) => {
            if(data.employerlist){
                this.employers_list = data.employerlist;
            }else{
                const message = data.message || 'Failed to get employer data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    @ViewChild('cowDetailsRef')
    private cowDetailsRef: IModalComponent;
    clerkOfWorksDetailModal(row){
        this.cow_row = row;
        console.log(this.cow_row);
        return this.cowDetailsRef.open();
    }

    closeOutModal(row, c){
        //close view clerk of works popup
        if(c) {
            c('Clock Click');
        }
        this.cow_row = row;
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Close Out',
            title: `Are you sure you would like to close out <span class="fw-500">${this.projectInfo.cow_setting.cow_phrase} Ref # ${this.cow_row?.cow_ref}</span>?`,
            confirmLabel: 'Close Out',
            onConfirm: () => {
                this.closeOutRequest(this.cow_row);
            }
        });
    }

    @ViewChild('editCowPopupRef')
    private editCowPopupRef: IModalComponent;
    async editCowModal(row) {
        if(this.employers_list.length === 0) {
            await this.getEmployersList();
        }
        this.cow_row = row;
        this.showModal = true;
        return this.editCowPopupRef.open();
    }

    @ViewChild('commentRef')
    private commentRef: IModalComponent;
    cowCommentModal(row){
        this.cow_row = row;
        this.addedCommentImgs = [{}];
        return this.commentRef.open();
    }

    @ViewChild('viewDroppedPinImgHtml')
    private viewDroppedPinImgHtmlRef: TemplateRef<any>;
    cowLocationPinModal(pin, status){
        this.showingAllPins = false;
        this.populateSiteDrawingImg(pin);
        return this.openCowModal(this.viewDroppedPinImgHtmlRef);
    }

    cowAllLocationPinsModal(){
        console.log(this.allPinsCoordinates);
        if(!this.pinLevel && this.allPinsCoordinates.length) {
            this.pinLevel = this.allPinsCoordinates[0].level;
        }
        this.populateSiteDrawingImg(this.allPinsCoordinates[0]);
        this.showingAllPins = true;
        return this.openCowModal(this.viewDroppedPinImgHtmlRef);
    }

    getLeftPinPos(data){
        let def_scale = data.default_scale_width || 360;
        let x_scale = (data.img_width/def_scale);
        return ((x_scale*(data.x-4))/data.img_width) * 100;
    }

    getTopPinPos(data){
        let def_scale = data.default_scale_height || 275;
        let y_scale = (data.img_height/def_scale);
        return ((y_scale*(data.y-11))/data.img_height) * 100;
    }

    populateSiteDrawingImg(pin) {
        this.siteDrawingImgSrc = (pin.file_id && pin.file_id.file_url) ? pin.file_id.file_url : null;
    }

    updateSiteDrawingImg() {
        let site_drawing = (this.projectInfo.cow_setting.cow_site_drawings || []).find((sd:any) => sd.site_level == this.pinLevel);
        this.siteDrawingImgSrc = (site_drawing && site_drawing.file_id) ? site_drawing.file_id.file_url : "";
    }

    onLogoError($img:any, targetSrc:any) {
        $img.src = '/images/project-placeholder.png';
        if(targetSrc && targetSrc.length && this.logoImgRetries) {
            setTimeout(() => {
                this.logoImgRetries--;
                $img.src = targetSrc;
            }, 2000);
        }
    }

    onLogoLoad($img) {
        this.logoImgRetries = 5;
    }

    downloadPinnedSiteDrawing(cb = () => {}) {
        this.downloadingSiteD = true;
        this.clerkOfWorksService.downloadCowPinnedLevelMap(this.projectId, this.pinLevel, this.employerId, {}, (data) => {
            cb();
            this.downloadingSiteD = false;
        });
    }

    downloadClerkOfWorks(row) {
        this.downloadingSiteD = true;
        let body = {
            createdAt: row.createdAt,
            companyId: this.employerId,
            type: 'pdf'
        };

        this.clerkOfWorksService.downloadClerkOfWorks(body, row.id, () => {
            this.downloadingSiteD = false;
        });
    }

    openCowModal(content, windowClass='', cb = () => {}){
        const modalRef = this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            windowClass: windowClass
        });
        this.modalCallBack = cb;
        return false;
    }

    deleteCowModal(row) {
        this.cow_row = row;
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Are you sure you would like to delete <span class="fw-500">${this.projectInfo.cow_setting.cow_phrase} Ref # ${this.cow_row?.cow_ref}</span> observation?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.deleteCow(this.cow_row?.id); 
            }
        });
    }

    addCommentRequest(form, $modalLoader, event) {
        if (!form.valid) {
            console.log('Modal form is not valid');
            return;
        }
        let cow = form.value.clerk_of_works;

        let additionalInfo = {
            projectData: cow.project_ref,
            userData: cow.user_ref,
        };

        let addCommentReq = {
            cow_id: cow.id,
            user_ref: cow.user_ref.id,
            added_by: this.authUser$.name,
            images: [],
            comment: form.value.cow_comment
        };
        addCommentReq.images = (this.addedCommentImgs.filter(m => m && m.id) || []).map(m => m.id);
        let request = {
            add_comment: addCommentReq,
            additional_data: additionalInfo,
        };

        $modalLoader.show = true;
        this.clerkOfWorksService.addCoWComment(request).subscribe(out => {
            if(!out.success){
                const message = out.message || 'Failed to store data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
            } else {
                const message = 'Comment added successfully.';
                this.toastService.show(this.toastService.types.SUCCESS, message);
            }
            this.initializeTable(true);
            event.closeFn();
            $modalLoader.show = false;
        });
    }

    mediaUploadDone(data:any){
        if(data && data.userFile){
            this.addedCommentImgs.splice(1, 0,...data.userFile);
            this.addedCommentImgs[0] = {};
        }
    }

    addImageBlock(){
        if(!this.addedCommentImgs){
            this.addedCommentImgs = [];
        }
        this.addedCommentImgs.push({});
    }

    fileDeleteDone($event, i) {
        if($event && $event.userFile && $event.userFile.id) {
            this.addedCommentImgs.splice(i, 1);
        }
    }

    closeOutRequest(cowObj) {
        let closeOutReq = cowObj;

        let cowImagesArr = [];
        closeOutReq.cow_images.map(obj => {
            if (obj) {
                cowImagesArr.push(obj.id);
            }
            return cowImagesArr;
        });

        let additionalInfo = {
            projectData: closeOutReq.project_ref,
            userData: closeOutReq.user_ref
        };
        closeOutReq.cow_images = cowImagesArr;
        closeOutReq.status = 2;
        closeOutReq.project_ref = closeOutReq.project_ref.id;
        closeOutReq.user_ref = closeOutReq.user_ref.id;
        closeOutReq.closed_out_by = this.authUser$.name;
        let request = {
            close_out_request: closeOutReq,
            additional_data: additionalInfo,
        };
        this.loadingCoWs = true;
        this.clerkOfWorksService.updateClerkOfWorks(request, closeOutReq.id).subscribe(out => {
            if(!out.success){
                const message = out.message || 'Failed to update data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
            } else {
                const message = `${this.projectInfo.cow_setting.cow_phrase} Ref # ${this.cow_row?.cow_ref} closed out successfully.`;
                this.toastService.show(this.toastService.types.SUCCESS, message);
            }
            this.modalService.dismissAll();
            this.initializeTable(true);
            this.loadingCoWs = false;
        });
    }

    editCowRequest(form, event) {
        if (!form.valid) {
            console.log('Modal form is not valid');
            return;
        }
        let closeOutReq = form.value;
        let request = {
            close_out_request: closeOutReq,
            additional_data: {},
        };
        this.saveCowLoading = true;
        this.clerkOfWorksService.updateClerkOfWorks(request, closeOutReq.id).subscribe(out => {
            if(!out.success){
                const message = out.message || 'Failed to store data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
            } else {
                const message = `${this.projectInfo.cow_setting.cow_phrase} updated successfully.`;
                this.toastService.show(this.toastService.types.SUCCESS, message);
            }
            this.saveCowLoading = false;
            event.closeFn();
            this.modalService.dismissAll();
            this.showModal = false;
            this.initializeTable(true);
        });
    }

    createRange(number){
        let rangeItems = []
        for(var i = 1; i <= number; i++){
            rangeItems.push(i);
        }
        return rangeItems;
    }

    @ViewChild('downloadXlsxRef')
    private downloadXlsxRef: IModalComponent;
    proccessXlsxDownload(){
        if (this.isProjectBlocks !== false) {
            this.cow_blocks = this.records.reduce(function(filtered, r) {
                if (r.project_blocks > 0 && !filtered.includes(r.project_blocks)) {
                    filtered.push(r.project_blocks);
                }
                return filtered;
            }, []);
            this.cow_blocks = [...new Set([...this.cowBlockRange, ...this.cow_blocks])].sort((a,b) => a-b);
            this.selectedBlocks = [...this.cow_blocks];
        }
        return this.downloadXlsxRef.open();
    }

    filterAndDownload(event) {
        this.loadingCoWs = true;
        let statuses = [];
        let statusValue = {open: 1, closed: 2, ongoing: 3};
        for (const key in this.cowDwStatus) {
            if (this.cowDwStatus[key]) {
                statuses.push(statusValue[key]);
            }
        }

        const fromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.from_date).startOf('day').valueOf();
        const toDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.to_date).endOf('day').valueOf();
        let request = {
            from_date: fromDate,
            to_date: toDate,
            statuses,
            blocks: this.selectedBlocks,
            is_inherited_project: this.is_inherited_project,
        };

        this.clerkOfWorksService.downloadClerkOfWorksReport(this.projectId, this.employerId, request).subscribe((data:any) => {
            this.loadingCoWs = false;
            if (data && data.project_cows) {
                this.downloadCCReport(data.project_cows);
            } else {
                const message = 'Something went wrong, Please try again.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
        event.closeFn();
    }

    changeModel(ev, val) {
        if (ev.target.checked) {
            this.selectedBlocks.push(val);
            return;
        }
        let i = this.selectedBlocks.indexOf(val);
        this.selectedBlocks.splice(i, 1);
    }

    selectAllBlocks($event) {
        this.selectedBlocks = [];
        if ($event.target.checked) {
            (this.cow_blocks || []).map(block => {
                this.selectedBlocks.push(block);
            })
        }
    }

    deleteCow(id) {
        this.loadingCoWs = true;
        this.clerkOfWorksService.deleteCow(id).subscribe((data:any) => {
            if(!data.success){
                const message = data.message || 'Failed to delete data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            } else {
                const message = `${this.projectInfo.cow_setting.cow_phrase} deleted successfully.`;
                this.toastService.show(this.toastService.types.SUCCESS, message);
            }
            this.modalService.dismissAll();
            this.initializeTable(true);
            this.loadingCoWs = false;
        });
    }

    async downloadCCReport(records) {
        this.exportInProgress = true;
        let workbook: ExcelProper.Workbook = new Excel.Workbook();
        let worksheet = workbook.addWorksheet(this.cowPhrase, {
            views: [{zoomScale: 65}],
            pageSetup:{paperSize: 9, orientation:'landscape', scale: 67}
        });
        worksheet.pageSetup.margins.left = 0.5;
        worksheet.pageSetup.margins.right = 0.5;
        worksheet.pageSetup.margins.top = 0.3;
        worksheet.pageSetup.margins.bottom = 0.5;
        worksheet.pageSetup.printTitlesRow = '1:3';

        let maxImagesPerCoW = this.records.reduce((max, r) => {
            max = (r.cow_images && r.cow_images.length > max) ? r.cow_images.length : max
        }, 1);
        let needColSpanning = maxImagesPerCoW > 1;
        let colSpanningSize = 3; //(needRowSpanning ? 3 : maxImagesPerCoW);

        worksheet.columns = [
            { header: "Ref.", key: "ref", width: 5, style: {alignment : { vertical: 'middle', horizontal: 'center' }}},
            { header: "Block", key: "block", width: 5.5, style: {alignment : { vertical: 'middle', horizontal: 'center' }}},
            { header: "Level", key: "level", width: 6.25, style: {alignment : { vertical: 'middle', horizontal: 'center', wrapText: true}}},
            { header: "Location", key: "loc", width: 9.2, style: {alignment : { vertical: 'middle', horizontal: 'center', wrapText: true}}},
            { header: "Date", key: "date", width: 9.25, style: {alignment : { vertical: 'middle', horizontal: 'center', wrapText: true}}},
            { header: "Category", key: "cat", width: 12, style: {alignment : { vertical: 'middle', horizontal: 'center', wrapText: true}}},
            { header: "Observations", key: "obsrvn", width: 27.5, style: {alignment : { vertical: 'middle', horizontal: 'center', wrapText: true}}},
            { header: "Remedial Action", key: "rem_act", width: 26, style: {alignment : { vertical: 'middle', horizontal: 'center', wrapText: true}}},
            { header: "Owner", key: "owner", width: 7.8, style: {alignment : { vertical: 'middle', horizontal: 'center', wrapText: true}}},
            { header: "Photos", key: "photos", width: 19, style: {alignment : { vertical: 'middle', horizontal: 'center'}}},
            { header: "Photos2", key: "photos2", width: 19, style: {alignment : { vertical: 'middle', horizontal: 'center' }}},
            { header: "Photos3", key: "photos3", width: 19, style: {alignment : { vertical: 'middle', horizontal: 'center' }}},
            { header: "Further Comments", key: "comments", width: 19, style: {alignment : { vertical: 'middle', horizontal: 'center', wrapText: true}}},
            { header: "Status", key: "status", width: 8, style: {alignment : { vertical: 'middle', horizontal: 'center'}}},
            { header: "Date Closed", key: "closed_on", width: 11, style: {alignment : { vertical: 'middle', horizontal: 'center', wrapText: true}}},
        ];
        let i = 4;
        //filter records based on status and sort opened ones ASC and closed DESC.
        let filteredRecords = [
                ...records.filter(f => f.status == 1 || f.status == 3).sort((a, b) => (a.id < b.id ? -1 : 1)),
                ...records.filter(f => f.status == 2).sort((a, b) => (a.id > b.id ? -1 : 1))
            ];

        if (this.downloadPrintVersion) {
            //changes for print version xlsx.
            filteredRecords.sort((a, b) => (a.project_blocks < b.project_blocks ? -1 : 1));
        }

        let imgIdMaps = [];
        let minDt = dayjs().valueOf(), maxDt = 0;
        let rows = await Promise.all(filteredRecords.map(async r => {
            let commentImgs = [];
            // Prepare rich text format to insert into Excel file.
            let comment = (r.comments || []).reduce((richTextArr, c) => {
                if(c.comment) {
                    commentImgs.push(...c.images);
                    richTextArr.push({ 'font': {'bold': true}, 'text': this.dayjs(+c.createdAt).format(AppConstant.defaultDateFormat) + ': ' });
                    richTextArr.push({ 'text': c.comment + '\n' });
                    return richTextArr;
                }
                return [];
            }, []);
            imgIdMaps.push(...(await this.addImagesToWorkbook(workbook, r.cow_ref, [...r.cow_images, ...commentImgs])));
            minDt = (+r.createdAt < minDt) ? +r.createdAt : minDt;
            maxDt = (+r.createdAt > maxDt) ? +r.createdAt : maxDt;
            i++;

            return {
                "ref": r.cow_ref,
                "block": r.project_blocks,
                "level": (r.project_levels && r.project_levels.length) ? r.project_levels.join(", ") : null,
                "date": r.createdAt ? this.dayjs(+r.createdAt).format(AppConstant.defaultDateFormat) : null,

                "loc": r.cow_location,
                "cat": r.cow_category,
                "obsrvn": r.cow_observations,
                "rem_act": r.cow_remedial_action,
                "owner": r.cow_owner,
                "photos": null,
                "photos2": null,
                "photos3": null,
                "comments": (comment.length) ? { 'richText': comment } : null,
                "status": r.status_message,
                "closed_on": r.closed_out_date ? this.dayjs(+r.closed_out_date).format(AppConstant.defaultDateFormat) : null

            };
        }));
        let logoImgId = [];
        if (this.employerInfo) {
            logoImgId = await this.addImagesToWorkbook(workbook, 'company_logo', [{file_url: this.employerInfo.logo_file_url, file_mime: "image/png"}]);
            console.log(logoImgId);
        }

        worksheet.autoFilter = 'A3:O'+i;
        worksheet.getRow(1).commit();
        worksheet.getRow(2).commit();
        worksheet.addRow(["Ref.","Block","Level","Location","Date","Category","Observations","Remedial Action","Owner","Photos",'','',"Further Comments","Status",/*"Days Open",*/"Date Closed"]);
        worksheet.getRow(3).height = 35;
        worksheet.mergeCells('A1:F1'); worksheet.mergeCells('G1:L1'); worksheet.mergeCells('N1:O1');
        worksheet.getCell('G1').value = this.projectName + ' - Observation Tracker - Blocks ' + this.selectedBlocks.join(', ');
        worksheet.getCell('G1').alignment = { vertical: 'middle', horizontal: 'center' };
        worksheet.getCell('G1').font = {size: 18, bold: true };
        worksheet.addRows(rows);
        console.log('Merging column for photos', colSpanningSize);
        worksheet.mergeCells(`J3:L3`);
        let lastRowIndex = 0;
        let increaseRowHeightIfNeeded = this.excelUtility.increaseRowHeightIfNeeded;
        let addBorderToCell = this.excelUtility.addBorderToCell;
        let borderSkipped = [], imgColBorder = [];
        // Iterate over all rows in a worksheet
        worksheet.eachRow({ includeEmpty: true},function(row, rowNumber) {
            lastRowIndex++;
            let actualRow = worksheet.getRow(lastRowIndex);
            if(rowNumber >= 3 && row.hasValues){
                // Iterate over all (including empty) cells in a row
                actualRow.eachCell({ includeEmpty: true }, function(cell, colNumber) {
                    cell.border = {
                        top: {style:'thin'},
                        left: {style:'thin'},
                        bottom: {style:'thin'},
                        right: {style:'thin'}
                    };
                    if(rowNumber === 3) {
                        cell.alignment = { vertical: 'middle', horizontal: 'center' };
                        cell.font = {bold: true, color: {argb: 'FFFFFF'} };
                        cell.fill = {
                            type: 'pattern',
                            pattern:'solid',
                            fgColor:{argb:'0F3326'}
                        };
                    } else if(colNumber === 14) {
                        let v = cell.value, o = 'Open', c = 'Closed', og = 'Ongoing';
                        if (v === o || v === c || v === og) {
                            cell.fill = {
                                type: 'pattern',
                                pattern:'solid',
                                fgColor:{argb: (v===o ? 'dc3545' : (v===c ? '28a745' : 'FFA500'))}
                            };
                        }
                    }
                });
            }
            if(rowNumber <= 3){
                return;
            }
            console.log('Processing old row #', rowNumber, 'last row index was', lastRowIndex);
            let maxHeightNeeded = 0;
            let imgs = (imgIdMaps || []).filter(i => i.col == actualRow.getCell('ref').value) || [];
            if(imgs.length) {
                let mergeStartAt = lastRowIndex;
                let mergeEndAt = lastRowIndex;
                imgs.map(async (img, loopIndex) => {
                    let tlCol = 9.02 + (loopIndex%3);
                    let tlRow = (lastRowIndex-1) + 0.1;
                    let tl = { col: tlCol, row: tlRow };
                    worksheet.addImage(img.id, {
                        tl: tl,
                        ext: img.ext,
                        editAs: 'undefined',
                    });
                    maxHeightNeeded = increaseRowHeightIfNeeded(worksheet.getRow(lastRowIndex), img.ext.height, maxHeightNeeded);

                    // Just before it need, add an empty row
                    if(loopIndex > 0 && (loopIndex % 3) === 2 && loopIndex < (imgs.length-1)){
                        console.log('Adding empty row');
                        worksheet.spliceRows(lastRowIndex+1, 0, []);
                        mergeEndAt++;
                        lastRowIndex++;

                        let cellBorderStyle = {
                            right: {style:'thin'},
                            bottom: {style:'thin'},
                        };
                        addBorderToCell(worksheet, `J${mergeEndAt}`, cellBorderStyle);
                        addBorderToCell(worksheet, `K${mergeEndAt}`, cellBorderStyle);
                        addBorderToCell(worksheet, `L${mergeEndAt}`, cellBorderStyle);
                    }
                });
                if(mergeEndAt > mergeStartAt){
                    console.log(`Merging cells vertically ${mergeStartAt} to ${mergeEndAt}`);
                    worksheet.mergeCells(`A${mergeStartAt}:A${mergeEndAt}`);
                    worksheet.mergeCells(`B${mergeStartAt}:B${mergeEndAt}`);
                    worksheet.mergeCells(`C${mergeStartAt}:C${mergeEndAt}`);
                    worksheet.mergeCells(`D${mergeStartAt}:D${mergeEndAt}`);
                    worksheet.mergeCells(`E${mergeStartAt}:E${mergeEndAt}`);
                    worksheet.mergeCells(`F${mergeStartAt}:F${mergeEndAt}`);
                    worksheet.mergeCells(`G${mergeStartAt}:G${mergeEndAt}`);
                    worksheet.mergeCells(`H${mergeStartAt}:H${mergeEndAt}`);
                    worksheet.mergeCells(`I${mergeStartAt}:I${mergeEndAt}`);
                    worksheet.mergeCells(`M${mergeStartAt}:M${mergeEndAt}`);
                    worksheet.mergeCells(`N${mergeStartAt}:N${mergeEndAt}`);
                    worksheet.mergeCells(`O${mergeStartAt}:O${mergeEndAt}`);
                }
                worksheet.getRow(lastRowIndex).eachCell({includeEmpty: true}, function(cell, colNumber) {
                    cell.border = {
                        ...(cell.border ? cell.border : {}),
                        bottom: {style:'thin'},
                    };
                });
            } else {
                actualRow.height = 50;
                worksheet.getRow(lastRowIndex).eachCell({includeEmpty: true}, function(cell, colNumber) {
                    cell.border = {
                        ...(cell.border ? cell.border : {}),
                        bottom: {style:'thin'},
                    };
                });
            }

        });
        imgColBorder.push(worksheet.rowCount);

        worksheet.getCell('M1').value = null;
        worksheet.getCell('N1').value = null;
        worksheet.getRow(1).height = 65;
        worksheet.getCell(`A1`).alignment = {vertical: 'bottom', horizontal: 'left'};
        // Adding company logo
        if(logoImgId.length) {
            let heightNeeded = 0;
            logoImgId.map(async (img, loopIndex) => {
                let tl = { col: 13.02, row: 0.03 };
                worksheet.addImage(img.id, {
                    tl: tl,
                    ext: img.ext,
                    editAs: 'undefined'
                });
                increaseRowHeightIfNeeded(worksheet.getRow(1), img.ext.height, 0);
            });
        }

        if (this.isProjectBlocks === false) {
            worksheet.getColumn(2).hidden = true;
        }
        if (this.isProjectLevels === false) {
            worksheet.getColumn(3).hidden = true;
        }
        worksheet.headerFooter.oddFooter = "&LPowered by innDex &RPage &P of &N";

        if(!this.downloadPrintVersion && this.employerInfo) {
            worksheet.getCell("A1").alignment = { wrapText: true };
            worksheet.getCell('A1').value = {
              richText: [
                {'font': {'size': 12, 'bold': true}, text: `${this.employerInfo.name} - ${this.cowPhrase} `},
                {'font': {'size': 12}, text: ' \r\n' + `Date: ${dayjs().format(AppConstant.defaultDateFormat)}`},
              ]
            };
        }
        workbook.xlsx.writeBuffer().then(data => {
            const blob = new Blob([data], { type: this.blobType });
            fs.saveAs(blob, `${this.cowPhrase} Report-${dayjs().format(AppConstant.apiRequestDateFormat)}.xlsx`);
            this.exportInProgress = false;
        });
    }

    addImagesToWorkbook = this.excelUtility.addImagesToWorkbook;

    isStatusMatched(cow_status) {
        return (cow_status === 1 && this.cowDwStatus.open) || (cow_status === 2 && this.cowDwStatus.closed) || (cow_status === 3 && this.cowDwStatus.ongoing)
    }
    downloadImage(fileUrl, fileName) {
        fs.saveAs(fileUrl, fileName);
    }
    onFilterSelection(data){
        this.categoryFilter = data.category.map(a=>a);
        this.ownerFilter = data.owner.map(a=>a);
        this.pageCallback({offset:0},  true);
    }
    searchFunction(data){
        this.globalSearch = data.search;
        this.pageCallback({offset:0}, true);
    }
    renderFilterData(){
        return [
            {
                name:'category',
                list:this.categorys,
                enabled:true,
                state:false,
            },
            {
                name:'owner',
                list:this.owners,
                enabled:true,
                state:false,
            },
        ];
    }

    async clerkOfWorksReportDownload(event) {
        await this.initiateDownload(event.selection);
        event.closeFn();
    }

    scrollToTop() {
        window.scrollTo({top: 0, behavior: 'smooth'});
    }

    allBlocksSelected(){
        return this.selectedBlocks.length === this.cow_blocks.length
    }

    public openClerkOfWorksReportModal() {
        this.reportDownloader.openModal();
    }
}

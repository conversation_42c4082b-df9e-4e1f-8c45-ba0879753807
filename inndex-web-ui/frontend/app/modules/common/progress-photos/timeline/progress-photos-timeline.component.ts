import { Component, OnInit, Input, ViewChild, TemplateRef } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { Project, ProgressPhotosService, Common, UserService, AuthService, User, ProgressPhotosTimelineActionButtons, ActionButtonVal, ToastService } from "@app/core";
import { HttpParams } from '@angular/common/http';
import * as dayjs from 'dayjs';
import { NgbAccordion, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { NgbMomentjsAdapter } from "@app/core/ngb-moment-adapter";
import { AppConstant } from "@env/environment";
import { ProgressPhotosAddComponent } from "../add/progress-photos-add.component";
import { ProgressPhotosGalleryComponent } from "../gallery/progress-photos-gallery.component";
import { ProgressPhotosDownloadComponent } from "../download/progress-photos-download.component";
import { GenericModalComponent } from "../../generic-modal/generic-modal.component";
import { GenericModalConfig } from "../../generic-modal/generic-modal.config";
import { ReportDownloaderComponent } from "../../report-downloader/report-downloader.component";

@Component({
    selector: 'progress-photos-timeline',
    templateUrl: './progress-photos-timeline.component.html',
    styleUrls: ['./progress-photos-timeline.component.scss'],
})
export class ProgressPhotosTimelineComponent implements OnInit {
    progress_photos_row: any;
    showMapWithMultiPin: boolean;
    savePPLoading: boolean = false;
    users_employer: any = [];
    authUser$: User;
    @Input()
    projectId: number;

    @Input()
    project: Project;

    @Input()
    isProjectPortal: boolean = false;

    paginationData = new Common();
    page = this.paginationData.page;
    search: any = null;
    is_inherited_project: boolean = false;
    loadingProgressPhotos: boolean = false;
    records: any = [];
    employerId: number;
    activeId: string;
    images: any = [];
    months: any = [];
    recordsByMonth: any = {};
    employer: any = {};
    projectName: string;
    progressPhotosData: any[] = [];
    progressPhotosCount: number;
    user_album: any = [];
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;
    filterByOwnerId: number;
    ownerList: any = [];
    actionButtonMetaData = {
        actionList: [],
        class: 'material-symbols-outlined',
    };
    @ViewChild('accordion', { static: true }) accordion: NgbAccordion;
    @ViewChild('downloadReportConfirmation', { static: false }) progressPhotosDownload: ProgressPhotosDownloadComponent;
    @ViewChild('addProgressPhotos', { static: false }) progressPhotosAdd: ProgressPhotosAddComponent;
    @ViewChild('viewImgGallery', { static: false }) progressPhotosGallery: ProgressPhotosGalleryComponent;
    @ViewChild('reportDownloader') private reportDownloader: ReportDownloaderComponent;

    constructor(
        private readonly activatedRoute: ActivatedRoute,
        private readonly progressPhotosService: ProgressPhotosService,
        private readonly authService: AuthService,
        private readonly userService: UserService,
        private readonly ngbMomentjsAdapter: NgbMomentjsAdapter,
        private readonly toastService: ToastService,
    ) { }

    ngOnInit() {
        this.from_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.to_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        if (!this.isProjectPortal) {
            this.getParams();
        }
        this.getAuthUser();
        this.getInductionsUsersEmployer();
        this.getProgressPhotosAlbums();
        this.actionButtonMetaData.actionList = [
            {
                code: ProgressPhotosTimelineActionButtons.MAP,
                name: `Location Map`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'location_on',
                enabled: true,
            },
            {
                code: ProgressPhotosTimelineActionButtons.DOWNLOAD_REPORT,
                name: `Export`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: true,
            },
        ];
    }

    getAuthUser() {
        this.authService.authUser.subscribe(data => {
            if (data?.id) {
                this.authUser$ = data;
            }
        });
    }

    getParams() {
        const params = this.activatedRoute.snapshot.params;
        this.projectId = this.projectId ?? params?.projectId;
        this.employerId = this.employerId ?? params?.employerId;
        console.log('activatedRoute params:', params);
        // this.activatedRoute.params.subscribe(params => {
        //     this.projectId = this.projectId ?? params['projectId'];
        //     this.employerId = params['employerId'];
        // });
        this.page.size = 100;
        this.getInductionsUsersEmployer();
    }

    initializeTable() {
        this.images = []
        this.loadingProgressPhotos = true;
        const params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('is_inherited_project', `${this.is_inherited_project}`)
            .set('search', `${this.search}`);
        let companyId = (this.employerId ? this.employerId : null);
        this.projectName = this.project.name;
        this.progressPhotosService.getProgressPhotos(this.projectId, companyId, params).subscribe((data: any) => {
            if (data && data.progress_photos) {
                this.records = data.progress_photos;
                this.checkTagOwner();
                this.processRecords();
                this.page.totalElements = data.total_record_count;
                this.progressPhotosData = this.records;
                if (this.page.pageNumber == 1) {
                    this.progressPhotosCount = data.progress_photos_count
                }
                return;
            }
            this.loadingProgressPhotos = false;
            const message = `Failed to fetch progress photos, id: ${this.projectId}.`;
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            return [];
        });
    }

    processRecords() {
        let d;
        this.recordsByMonth = {};
        this.months = [];
        this.images = []
        this.records.forEach(r => {
            d = dayjs(r.createdAt).format('MMM-YYYY');
            if (this.recordsByMonth[d]) {
                this.recordsByMonth[d].push(...r.pp_images)
            } else {
                this.recordsByMonth[d] = [...r.pp_images];
                this.months.push(d);
            }

            (r.pp_images || []).map(file => {
                if (file?.img_translation?.length) {
                    file.img_translation.forEach(r => {
                        this.images.push({ "file_url": r, "preloaded": false, "id": file?.id });
                    });
                } else if (file?.file_url) {
                    this.images.push({ "file_url": file.file_url, "preloaded": false, "id": file?.id });
                }
            });
        });
        this.loadingProgressPhotos = false;
        setTimeout(() => {
            this.accordion.expandAll();
        }, 100);
    }

    AppConstant = AppConstant;
    isSiteAdminRoute() {
        let routeName = this.activatedRoute.snapshot.url[0].path;
        return routeName === 'site-admin';
    }

    onPageChange(event) {
        this.page.pageNumber = (event < 1) ? 0: event -1;
        this.initializeTable();
        window.scrollTo(0, 0);
    }

    checkTagOwner() {
        this.ownerList = [];
        this.records.forEach(r => {
            if (r.tagged_owner && r.tagged_owner.name) {
                this.ownerList.push(r.tagged_owner);
            }
        });
        this.ownerList = this.ownerList.filter((e, i) => this.ownerList.findIndex(a => a?.id === e?.id) === i).sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
    }

    dayjs(n: number) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };

    addProgressPhotosModal() {
        this.progressPhotosAdd.openProgressPhotosAddEditModal({
            projectId: this.projectId,
            progress_photos_row: {},
            imgIds: [],
            newPhotos: [],
            users_employer: this.users_employer,
            user_album: this.user_album,
            tagOwner: null,
            selectedAlbum: null
        });
    }

    async initiateDownload(resp) {
        if(resp){
        this.from_date = resp.fromDate;
        this.to_date = resp.toDate;
        let data = {
            from_date: resp.fromDate,
            to_date: resp.toDate,
            type: resp.type,
            projectId: this.projectId,
            album_id: 0,
            employerId: resp.company,
            employer: this.employer,
            is_inherited_project: this.is_inherited_project,
            filterByOwnerId: this.filterByOwnerId,
            project: this.project
        };
        return this.progressPhotosDownload.initiateDownloadReportModal(data);}
    }

    viewImage(event: Event, imgSrc, i) {
        event.stopPropagation && event.stopPropagation();
        const index = this.images.findIndex(a => a.file_url === imgSrc);
        const slideNo = 'slide-' + index.toString();
        this.activeId = slideNo;
        this.images[index].preloaded = true;
        if (this.images[index + 1]) {
            this.images[index + 1].preloaded = true;
        }
        return this.progressPhotosGallery.openProgressPhotosGalleryModal({
            activeId: this.activeId,
            images: this.images
        });
    }

    public openMapWithMultiPin() {
        this.showMapWithMultiPin = true;
        this.progressPhotosGallery.progressPhotosModalShowMap({
            title: 'Timeline map',
            showMapWithMultiPin: this.showMapWithMultiPin,
            progress_photos_row: this.progress_photos_row,
            progressPhotosData: this.progressPhotosData,
        });
    }

    getInductionsUsersEmployer() {
        this.userService.getProjectInductionsUsersEmployer(this.projectId, 2).subscribe((data: any) => {
            if (data.users_employer) {
                this.users_employer = data.users_employer;
            } else {
                const message = data.message || 'Failed to get companies of the users who have had an approved induction on the project.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    getProgressPhotosAlbums() {
        this.loadingProgressPhotos = true;
        console.log(this.projectId);
        this.progressPhotosService.getProgressPhotosAlbum(this.projectId).subscribe((data: any) => {
            console.log(data);
            if (data?.user_album) {
                this.user_album = data.user_album;
                console.log("ProgressPhotosAlbums: ", this.user_album);
            } else {
                const message = data.message || 'Failed to get album of the user.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
            this.loadingProgressPhotos = false;
        });
    }

    updateProgressPhotoView(resp) {
        let pageNumber = this.page.pageNumber;
        this.onPageChange(pageNumber);
    }

    public onActionSelection(actionVal: ActionButtonVal) {
        const code = actionVal.code;
        if(code === ProgressPhotosTimelineActionButtons.MAP) {
            this.openMapWithMultiPin();
        } else if(code === ProgressPhotosTimelineActionButtons.DOWNLOAD_REPORT) {
            this.openProgressPhotosTimelineReportModal();
        }
    }

    openProgressPhotosTimelineReportModal() {
        this.reportDownloader.openModal();
    }

    async progressPhotosTimelineReportDownload(event) {
        this.filterByOwnerId = event.selection.company;
        await this.initiateDownload(event.selection);
        event.closeFn();
    }
}

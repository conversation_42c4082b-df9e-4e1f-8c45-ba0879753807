import {Component, OnInit, TemplateRef, ViewChild, ViewEncapsulation, ElementRef, Input} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import { HttpParams } from '@angular/common/http';
import * as dayjs from 'dayjs';
import {NgbModalConfig, NgbModal, NgbDateStruct} from '@ng-bootstrap/ng-bootstrap';
import {fromEvent} from "rxjs";
import {debounceTime, distinctUntilChanged, filter, tap} from "rxjs/operators";
import {ProgressPhotosService, AuthService, UserService, User, Project, ProjectService, isInheritedProjectOfCompany, Common, ProgressPhotosSubmissionsActionButtons, ActionButtonVal, ToastService} from "@app/core";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import {AppConstant} from "@env/environment";
import * as fs from "file-saver";
import {ProgressPhotosAddComponent} from "../add/progress-photos-add.component";
import {ProgressPhotosGalleryComponent} from "../gallery/progress-photos-gallery.component";
import {ProgressPhotosDownloadComponent} from "../download/progress-photos-download.component";
import { SearchWithFiltersComponent } from '../../search-with-filters/search-with-filters.component';
import { filterData } from '@app/core';
import { ReportDownloaderComponent } from '../../report-downloader/report-downloader.component';
import { ActionBtnEntry } from '@app/shared';

@Component({
    selector: 'progress-photos-submissions',
    templateUrl: './progress-photos-submissions.component.html',
    styleUrls: ['./progress-photos-submissions.component.scss'],
    // add NgbModalConfig and NgbModal to the component providers
    providers: [NgbModalConfig, NgbModal],
    styles: ['.progress-photos-head > div.col-sm-12.row { display: none; }.ngx-datatable .datatable-body-cell','.datatable-body-cell {overflow-x:visible}','.dropdown-item.table-dropdown-item {padding: 0.2rem 1.0rem;} ','.dropdown-menu.table-dropdown-menu{font-size:0.75rem}', '.pp_ellipses{white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}'],
    encapsulation: ViewEncapsulation.None,  // Use to disable CSS Encapsulation for this component
})
export class ProgressPhotosSubmissionsComponent implements OnInit {
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;
    locationLatLong: any;

    @Input()
    projectId: number;

    @Input()
    project: Project;

    @Input()
    isProjectPortal: boolean = false;

    employerId: number = 0;
    projects: Array<Project> = [];
    records: Array<any> = [];
    temp_records: Array<any> = [];
    progress_photos_row: any = {};
    userFile: any;
    authUser$: User;
    downloadProgressPhotosReportLoading: boolean = false;
    employer: any = {};
    projectName: string = null;
    modalCallBack: any;
    is_inherited_project: boolean = false;
    loadingProgressPhotos: boolean = false;
    tableOffset: number = 0;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: number;
    paginationData = new Common();
    page = this.paginationData.page;
    search:any = null;
    users_employer: any = [];
    selectedOwner: number[]=[];
    filterByOwnerId: number;
    ownerList: any = [];
    progressPhotosData: any[] = [];
    showMapWithMultiPin = false;
    user_album: any = [];
    isFolderDownload: boolean = false;
    imagesArray:  Array<any> = [];
    filterationData:any;
    filterData:filterData[] =this.renderFilterData();
    reloadFilterData:boolean = false;
    actionButtonMetaData = {
        actionList: [],
        class: 'material-symbols-outlined',
    };
    sorts: {
        dir:string, prop: string
    } = {
        dir:'desc', prop: 'id'
    };

    AppConstant = AppConstant;
    @ViewChild('searchFilters') searchFilters: SearchWithFiltersComponent;
    @ViewChild('downloadReportConfirmation', { static: false }) progressPhotosDownload: ProgressPhotosDownloadComponent;
    @ViewChild('progressPhotos', { static: false }) progressPhotosGallery: ProgressPhotosGalleryComponent;
    @ViewChild('reportDownloader') private reportDownloader: ReportDownloaderComponent;

    isInitProgressPhoto: boolean = false;
    loadingInlineProgressPhoto: boolean = false;
    baseButtonConfig: Record<string, ActionBtnEntry[]> = {};

    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private progressPhotosService: ProgressPhotosService,
        private modalService: NgbModal,
        private authService: AuthService,
        private projectService: ProjectService,
        private userService: UserService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private toastService: ToastService,
    ) {
        this.from_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.to_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
    }

    ngOnInit() {
        if(!this.isProjectPortal) {
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
                this.employerId = params['employerId'];
            });
        }
        this.authService.authUser.subscribe(data =>{
            if(data?.id){
                this.authUser$ = data;
            }
        });

        this.initializeTable();
        this.getInductionsUsersEmployer();
        this.getProgressPhotosAlbums();
    }

    private initializeTable(isPageChange?: boolean) {
        if (isPageChange) {
          this.loadingInlineProgressPhoto = true;
        } else {
          this.loadingProgressPhotos = true;
        }
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('is_inherited_project', `${this.is_inherited_project}`)
            .set('search', `${this.search}`)
            .set('sortKey', this.sorts.prop)
            .set('sortDir', this.sorts.dir);

            if(this.selectedOwner.length){
                params = params.append('tagged_owner',this.selectedOwner.join(','));
            }

        this.from_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(this.project.createdAt));
        this.projectName = this.project.name;
        let companyId = (this.employerId ? this.employerId : null);
        this.progressPhotosService.getProgressPhotos(this.projectId, companyId, params).subscribe((data:any) => {
            this.loadingProgressPhotos= false;
            this.loadingInlineProgressPhoto = false;
            if (data && data.progress_photos) {
                this.records = data.progress_photos;
                this.temp_records = data.progress_photos;
                this.page.totalElements = data.total_record_count;
                if(!this.reloadFilterData){
                    this.ownerList = data.taggedOwnersList;
                    this.filterData = this.renderFilterData();
                }
                this.reloadFilterData = true;
                this.progressPhotosData = data.progress_photos;
                if(this.selectedOwner.length !=0){
                    this.filterByOwner();
                }
                this.getRowButtonGroup(this.records);
                return data.progress_photos;
            }
            const message = `Failed to fetch progress photos, id: ${this.projectId}.`;
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            return [];
        });
        this.actionButtonMetaData.actionList = [
            {
                code: ProgressPhotosSubmissionsActionButtons.MAP,
                name: `Location Map`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'location_on',
                enabled: true,
            },
            {
                code: ProgressPhotosSubmissionsActionButtons.DOWNLOAD_REPORT,
                name: `Export`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: true,
            },
        ];
    }

    getRowButtonGroup(records: any[]): void {
        const BASE_BUTTON_CONFIG: Array<ActionBtnEntry> = [
            {
                key: 'view',
                label: '',
                title: 'View Progress Photos',
                mat_icon: 'search'
            },
            {
                key: 'download', 
                label: '',
                title: 'Download Progress Photos',
                mat_icon: 'download',
                children: []
            }
        ];

        records.forEach(row => {
            if (!row?.id) return;
            const photos = this.photoDownloadOpts(row.pp_images || []);
            const downloadOptions = [
                {
                    key: 'download_pdf',
                    label: 'Download PDF',
                    title: 'Download PDF',
                    mat_icon: ''
                },
                ...this.createPhotoDownloadOptions(photos)
            ];

            const updatedButtonConfig = [
                BASE_BUTTON_CONFIG[0],
                {
                    ...BASE_BUTTON_CONFIG[1],
                    children: downloadOptions
                }
            ];
            this.baseButtonConfig[row.id] = updatedButtonConfig;
        });
    }

    createPhotoDownloadOptions(photos: any[]): Array<ActionBtnEntry> {
        return photos.map((photo, index) => ({
            key: `download_${photo.id}`,
            label: `Download ${(photo?.file_mime == "application/pdf") ? 'Attached PDF' : 'Image'} ${index+1}`,
            title: photo.file_url || '',
        }));
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actionMap = {
            'view': () => this.progressPhotosModal(row),
            'download_pdf': () => this.downloadProgressPhotos(row)
        };

        // Check if action exists in map
        if (actionMap[entry.key]) {
            actionMap[entry.key]();
            return;
        }

        // Handle individual photo downloads
        const photos = this.photoDownloadOpts(row?.pp_images || []);
        const downloadOptions = this.createPhotoDownloadOptions(photos);
        
        const matchingOption = downloadOptions.find(opt => opt.key === entry.key);
        if (matchingOption) {
            this.downloadImage(matchingOption.title, matchingOption.label);
        }
    }

    ngAfterViewInit() {
    }

    dayjs(n: number) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };

    @ViewChild('addProgressPhotos', { static: false }) progressPhotosAdd: ProgressPhotosAddComponent;
    progressPhotosModal(row) {
        return this.progressPhotosGallery.progressPhotosModal(row,true);
    }

    onMouseOver(infoWindow, _event: MouseEvent) {
        infoWindow.open();
    }

    onMouseOut(infoWindow, _event: MouseEvent) {
        infoWindow.close();
    }

    isSiteAdminRoute() {
        let routeName = this.activatedRoute.snapshot.url[0].path;
        return routeName === 'site-admin';
    }

    photoDownloadOpts(array){
        return array.sort((a, b) => b?.id - a?.id);
    }

    downloadImage(fileUrl, fileName) {
        fs.saveAs(fileUrl, fileName);
    }

    downloadProgressPhotos(row) {
        this.downloadProgressPhotosReportLoading = true;
        let body = {
            createdAt: row.createdAt,
            companyId: this.employerId
        };

        this.progressPhotosService.downloadProgressPhotos(body, row?.id, () => {
            this.downloadProgressPhotosReportLoading = false;
        });
    }

    getInductionsUsersEmployer() {
        this.userService.getProjectInductionsUsersEmployer(this.projectId, 2).subscribe((data: any) => {
            if(data.users_employer){
                this.users_employer = data.users_employer;
            } else {
                const message = data.message || 'Failed to get companies of the users who have had an approved induction on the project.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    filterByOwner() {
        let filteredRecords = [];
        this.temp_records.forEach(r=> {
            if(this.selectedOwner.includes(r?.tagged_owner?.id)) {
                filteredRecords.push(r);
            }
        });
        this.records = filteredRecords;
    }


    onClearTagOwner() {
        this.records = this.temp_records;
    }

    public openMapWithMultiPin() {
        this.showMapWithMultiPin = true;
        return this.progressPhotosGallery.progressPhotosModalShowMap({
            title: 'Submission map',
            showMapWithMultiPin : this.showMapWithMultiPin,
            progress_photos_row: this.progress_photos_row,
            progressPhotosData: this.progressPhotosData,
        });
    }

    async initiateDownload(resp) {
        this.from_date = resp.fromDate;
        this.to_date = resp.toDate;
        let data = {
            from_date: resp.fromDate,
            to_date: resp.toDate,
            type: resp.type,
            projectId: this.projectId,
            album_id: 0,
            employerId: resp.company,
            employer: this.employer,
            is_inherited_project: this.is_inherited_project,
            filterByOwnerId: this.filterByOwnerId,
            project: this.project
        };
        return this.progressPhotosDownload.initiateDownloadReportModal(data);
    }
    addProgressPhotosModal() {
        return this.progressPhotosAdd.openProgressPhotosAddEditModal({
            projectId: this.projectId,
            progress_photos_row: {},
            imgIds: [],
            newPhotos: [],
            users_employer: this.users_employer,
            user_album: this.user_album,
            tagOwner: null,
            selectedAlbum: null
        });
    }

    getProgressPhotosAlbums() {
        this.progressPhotosService.getProgressPhotosAlbum(this.projectId).subscribe((data: any) => {
            if (data.user_album) {
                this.user_album = data.user_album;
            } else {
                const message = data.message || 'Failed to get album of the user.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    updateProgressPhotoView(resp) {
        this.initializeTable(true)
    }
    onFilterSelection(data){
        this.selectedOwner = data.owner.map(a=>a.id);
     this.pageCallback({ offset: 0 }, true);
             
    }
    searchFunction(data){
        this.search = data.search;
        let stopReload = data?.stopReload;
        if(!stopReload){
            this.initializeTable(true);
        }
        this.page.pageNumber = 0;
    }
    renderFilterData(){
        return [
            {
                name:'owner',
                list:this.ownerList,
                enabled:true,
                state:false,
            }
        ];
    }
    
    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean) {
        if (!this.isInitProgressPhoto) {
          this.isInitProgressPhoto = true;
          return;
        }
        this.page.pageNumber = pageInfo.offset;
        this.initializeTable(isPageChange);
    }

    onSort($event){
        let {sorts} = $event;
        let{ dir, prop } = sorts[0];
        this.sorts = { dir, prop };
        this.pageCallback({ offset: 0 }, true);
    }

    public onActionSelection(actionVal: ActionButtonVal) {
        const code = actionVal.code;
        if(code === ProgressPhotosSubmissionsActionButtons.MAP) {
            this.openMapWithMultiPin();
        } else if(code === ProgressPhotosSubmissionsActionButtons.DOWNLOAD_REPORT) {
            this.openProgressPhotosSubmissionsReportModal();
        }
    }

    openProgressPhotosSubmissionsReportModal() {
        this.reportDownloader.openModal();
    }

    async progressPhotosSubmissionsReportDownload(event) {
        this.filterByOwnerId = event.selection.company;
        await this.initiateDownload(event.selection);
        event.closeFn();
    }
}

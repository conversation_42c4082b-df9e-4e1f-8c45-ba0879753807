<div class="col-sm-12 my-2 pr-2 pl-2" *ngIf="!loadingProgressPhotos">
    <div class="d-flex flex-wrap pb-2 justify-content-between gap-8">
            <h5 class="float-left">Total Submissions <small>({{ page?.totalElements }})</small></h5>
                <action-button 
                    [actionList]="actionButtonMetaData.actionList"
                    (selectedActionEmmiter)="onActionSelection($event)"
                    [newFeatureTitle]="'Add Photos'"
                    (onOpenAddNew)="addProgressPhotosModal()">
                </action-button>
    </div>
    <div>
        <div>
            <div>
                <search-with-filters #searchFilters [filterData]="filterData" (searchEmitter)="searchFunction($event)" (filterEmitter)="onFilterSelection($event)"></search-with-filters>
            </div>
        </div>
    </div>
    <div class="table-responsive-sm">
        <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                        [scrollbarV]="true"
                        [virtualization]="false"
                        [loadingIndicator]="loadingInlineProgressPhoto"
                        (page)="pageCallback($event, true)"
                        [columnMode]="'force'"
                        [count]="page.totalElements"
                        [externalPaging]="true"
                        [footerHeight]="40"
                        [limit]="page.size"
                        [offset]="tableOffset"
                        [offset]="page.pageNumber"
                        [rowHeight]="'auto'"
                        [rows]="records"
                        [externalSorting]="true"
                        (sort)="onSort($event)"
                        [sorts] = "[sorts]"
                       >
            <ngx-datatable-column prop="id" headerClass="font-weight-bold" [sortable]="true" minWidth="100">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Date & Time
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    {{ dayjs(row.createdAt).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS)}}
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column prop="pp_title" headerClass="font-weight-bold" [sortable]="true" minWidth="100">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Title
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    <div appTooltip>{{row.pp_title}}</div>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Submitted By
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    <div appTooltip style="display: inline-grid;">
                        <span>{{row?.user_ref?.first_name}} {{row?.user_ref?.last_name}}</span>
                        <span style="font-size: 11px;">({{row?.user_employer?.employer}})</span>
                    </div>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column headerClass="font-weight-bold" *ngIf="ownerList.length" [sortable]="false" minWidth="100">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Owner
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    <span appTooltip>{{row?.tagged_owner?.name}}</span>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Location
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    <span appTooltip>{{row?.pp_location}}</span>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column headerClass="font-weight-bold action-column" cellClass="action-column myclass no-ellipsis" style="backface-visibility:visible" [sortable]="false" minWidth="100">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Action
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    <button-group
                        [buttons]="baseButtonConfig[row.id]"
                        (onActionClick)="rowBtnClicked($event, row)">
                    </button-group>
                </ng-template>
            </ngx-datatable-column>
        </ngx-datatable>

        <ng-template #progressPhotosHtml let-c="close" let-d="dismiss">
            <div class="modal-header">
                <h4 class="modal-title">
                    {{ progress_photos_row?.pp_title }} - Progress Photos
                </h4>
                <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <progress-photos-details
                    [progress_photos_row]="progress_photos_row"
                    [ppImgArray]="imagesArray"
                    (openMapWithPin)="openMapWithPin()">
                </progress-photos-details>
            </div>

            <div class="modal-footer">
                <div class="mr-auto">
                    <button *ngIf="!progress_photos_row?.has_daily_activity" class="btn ml-1 btn-outline-primary" (click)="editProgressPhotosModal(progress_photos_row, c)">
                        Edit
                    </button>
                </div>
                <button *ngIf="!progress_photos_row.tagged_owner && isProjectPortal" type="button" class="btn btn-outline-primary" (click)="openTagOwnerModal(progress_photos_row)">Tag Owner</button>
                <button type="button" class="btn btn-outline-primary" (click)="d('Cross click')">OK</button>
            </div>
        </ng-template>

        <ng-template #progressPhotosHtmlMap let-c="close" let-d="dismiss">
            <progress-photo-location-map
                [showMapWithMultiPin]="showMapWithMultiPin"
                [row_data]="progress_photos_row"
                [project_data]="progressPhotosData"
                [dismiss]="d"
                mapTitle="Progress Photo"
                feature="pp"
                (view)="progressPhotosModal($event)"
            >
            </progress-photo-location-map>
        </ng-template>

        <!-- Open download report confirmation modal box -->
        <progress-photos-download #downloadReportConfirmation>
        </progress-photos-download>

        <!-- Open view gallery modal box -->
        <progress-photos-gallery #progressPhotos
                                 (updateProgressPhotoGallery) = updateProgressPhotoView($event) [employerId]="employerId"
                                 [projectId]="projectId" [user_album]="user_album"
                                 [users_employer]="users_employer">
        </progress-photos-gallery>

        <!-- Open add modal box -->
        <progress-photos-add #addProgressPhotos (updateProgressPhotoAdd)="updateProgressPhotoView($event)">
        </progress-photos-add>
    </div>
    <div class="clearfix"></div>
</div>
<block-loader [show]="(downloadProgressPhotosReportLoading || loadingProgressPhotos)" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>
<report-downloader #reportDownloader 
    [xlsxOnly]="false"
    (onFilterSelection)="progressPhotosSubmissionsReportDownload($event)"
    [showCompany]="true"
    [companyList]="ownerList"
    [isTagOwner]="true"
    >
</report-downloader>
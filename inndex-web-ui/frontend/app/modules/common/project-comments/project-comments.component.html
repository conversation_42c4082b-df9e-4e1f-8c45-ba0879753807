<div *ngIf="comments.length" [ngClass]="containerClass">
    <ng-container *ngIf="!isFromChangeLog; else tableViewComments">
        <ul class="list-group induction-comments">
            <!-- this block is being used for add/edit induction area only.  -->
            <ng-container *ngFor="let comment of (comments)">
                <li *ngIf="!(comment.origin === 'system' && comment.user_id === currentUser.id)"
                    [ngClass]="{'list-group-item p-1': true,'text-muted': comment.origin === 'system'}"
                    [title]="comment.origin === 'system' ? 'This is System generated comment' : ''">
                    <div class="col-sm-9 float-left p-0">
                        <div class="small"><b>{{ comment.user_id === currentUser.id ? 'You' : comment.name }}</b></div>
                        <small>{{ comment.note }}</small>
                    </div>
                    <small class="float-right">{{ dayjs(+comment.timestamp).format(dateFormat) }}</small>
                </li>
            </ng-container>
        </ul>
    </ng-container>
    <ng-template #tableViewComments>
        <div>
            <ngb-accordion class="i-accordion" [activeIds]="isPanelOpen ? 'panel-0' : ''">
                <ngb-panel id="panel-0" class="toggle-admin-access" [cardClass]="'yash-p'">
                    <ng-template ngbPanelHeader let-opened="opened">
                        <button [ngbPanelToggle]="!isCollapsible" class="btn p-0">
                            <div class="w-100 d-flex justify-content-between">
                                <div>
                                    <p class="m-0 fw-600">{{ tableViewCommentsTitle }}</p>
                                </div>
                                <div *ngIf="isCollapsible">
                                    <i [ngClass]="opened ? 'fas fa-angle-up' : 'fas fa-angle-down'"></i>
                                </div>
                            </div>
                        </button>
                    </ng-template>
                    <ng-template ngbPanelContent>
                        <div>
                            <table class="table table-bordered mb-0 border-0">
                                <tbody>
                                    <tr>
                                        <th class="date-col-w">Date</th>
                                        <th class="comments-by-col-w">Comments By</th>
                                        <th>Details</th>
                                    </tr>
                                    <tr *ngFor="let comment of (comments)">
                                        <td>
                                            {{ dayjsDisplayDateOrTime(+comment.timestamp) }}
                                            <div class="comments-time-label">({{ dayjsDisplayDateOrTime(+comment.timestamp, false) }})</div>
                                        </td>
                                        <td>{{ comment.user_id === currentUser.id ? 'You' : comment.name }}</td>
                                        <td>{{ comment.note }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </ng-template>
                </ngb-panel>
            </ngb-accordion>
        </div>
    </ng-template>
</div>

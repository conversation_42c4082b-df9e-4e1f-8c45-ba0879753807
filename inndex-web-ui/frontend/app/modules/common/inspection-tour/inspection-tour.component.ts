import {Component, OnInit, TemplateRef, ViewChild, Input, Output, EventEmitter, SimpleChanges, OnChanges, ChangeDetectorRef, AfterViewInit} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {
    AuthService,
    UserService,
    User,
    Project,
    InspectionTour,
    Checklist,
    MetaChecklist,
    ProjectService,
    ProjectInspectionTourService,
    Common,
    ToastService
} from "@app/core";
import {NgbDateStruct, NgbModal, NgbDate} from "@ng-bootstrap/ng-bootstrap";
import * as dayjs from 'dayjs';
let advancedFormat = require('dayjs/plugin/advancedFormat');
dayjs.extend(advancedFormat);
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import {AppConstant} from "@env/environment";
import { ShareToolReportToEmailComponent } from '../share-tool-report-to-email/share-tool-report-to-email.component';
import { GenericModalComponent } from '../generic-modal/generic-modal.component';
import { GenericModalConfig } from '../generic-modal/generic-modal.config';
import { ActionBtnEntry, IModalComponent } from '@app/shared';

@Component({
    templateUrl: './inspection-tour.component.html',
    selector: 'inspection-tour',
    styleUrls: ['./inspection-tour.component.scss']
})
export class InspectionTourComponent implements OnInit, OnChanges, AfterViewInit {

    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private modalService: NgbModal,
        private authService: AuthService,
        private projectService: ProjectService,
        private userService: UserService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private projectInspectionTourService: ProjectInspectionTourService,
        private domSanitizer: DomSanitizer,
        private cdRef: ChangeDetectorRef,
        private toastService: ToastService,
    ) {
    }

    @Input()
    projectId: number = 0;

    @Input()
    employerId: number = 0;

    @Input()
    filterParam: any = {};

    @Input()
    project: any;

    @Input()
    search: string;

    @Input()
    isProjectPortal: boolean = false;

    @Output()
    isChildComponentLoaded: any = new EventEmitter<any>();

    @Output()
    totalInspection: any = new EventEmitter<any>();

    authUser$: User;
    inspectionTourRecords: Array<any> = [];
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;
    AppConstant = AppConstant;
    inspection_tour_row: InspectionTour = new InspectionTour;
    isRailProject: boolean = false;
    isIndustrialProject: boolean = false;
    meta_checklist:  Array<any> = [];
    inspectionTourRowChecklist: Array<any> = [];
    closeout_detail: string = '';
    closeout_images:  Array<any> = [{}];
    itemQueId: any = null;
    itemType: string = '';
    closeoutLoading: boolean = false;
    checklistItem:  Checklist = new Checklist();
    metaChecklistItem:  MetaChecklist = new MetaChecklist();
    employer: any = {};
    allowedMime : Array<any> = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    meta_common_checklist: any = [];
    meta_rail_checklist: any = [];
    meta_industrial_checklist: any = [];
    negativeRating: Array<any> = ['no','poor', 'fair'];
    positiveRating: Array<any> = ['yes','good'];
    itemImgSrc: string = '';
    dashboardStartDate: NgbDate;
    blockLoader: boolean = false;
    dashboardHtmlContent: any;
    dashboardReportFrom: any;
    dashboardReportTo: any;
    unsatisfactoryItemsClass:string = '';
    hasUnsatisfactory: boolean = false;
    loadingInspections: boolean = false;
    isOpen: boolean = false;
    unsatisfactoryColumnTxt: string = '';
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    pagination = new Common();
    page = this.pagination.page;
    itemMapData: any = {};
    decimalConfig = AppConstant.decimelConfig;
    isInitInspectionTour: boolean = false;
    loadingInlineInspectionTour: boolean = false;
    dayjs(n: number) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };
    rowButtonGroup: Array<ActionBtnEntry> = [
        {
            key: 'view_inspection',
            label: '',
            title: 'View Inspection Tour',
            mat_icon: 'search',
        },
        {
            key: 'download_inspection',
            label: '',
            title: 'Download PDF',
            mat_icon: 'download',
        },
        {
            key: 'share_inspection',
            label: '',
            title: 'Share Inspection Details',
            mat_icon: 'share',
        },
        {
            key: 'close_inspection',
            label: '',
            title: 'Close Out',
            mat_icon: 'close',
        },
    ];

    ngOnInit() {
        if(!this.isProjectPortal) {
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        }
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });

        this.isRailProject = (this.project.project_type == 'rail');
        this.isIndustrialProject =  (this.project.project_type == 'industrial');
        this.getInspectionTours();
    }
    ngOnChanges(changes: SimpleChanges){
        if(changes.search.currentValue !== changes.search.previousValue){
            this.getInspectionTours()
        }
    }

    ngAfterViewInit() {
        this.cdRef.detectChanges();
    }

    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean) {
        if (!this.isInitInspectionTour) {
          this.isInitInspectionTour = true;
          return;
        }
        this.page.pageNumber = pageInfo.offset;
        this.getInspectionTours(isPageChange);
    }

    getInspectionTours(isPageChange?: boolean) {
        if (isPageChange) {
          this.loadingInlineInspectionTour = true;
        } else {
          this.loadingInspections = true;
        }
        if(this.search){
            this.loadingInlineInspectionTour = true;
            this.filterParam.q = this.search;
            this.page.pageNumber = 0;
        }else{
            this.filterParam.q = "";
        }
        this.filterParam.pageNumber = this.page.pageNumber;
        this.filterParam.pageSize = this.page.size;
        this.projectInspectionTourService.getProjectInspectionTours(this.projectId, this.employerId, this.filterParam)
            .subscribe((data:any) => {
                this.loadingInspections = false;
                this.loadingInlineInspectionTour = false;
                this.isChildComponentLoaded.emit(false);
                if (data && data.project_inspection_tours && data.common_checklist && data.rail_checklist) {
                    //Prepare Metadata
                    this.meta_common_checklist = data.common_checklist;
                    this.meta_rail_checklist = data.rail_checklist;
                    this.meta_industrial_checklist = data.industrial_checklist;
                    this.prepareMetaChecklist();
                    this.inspectionTourRecords = data.project_inspection_tours;
                    this.page.totalElements = data.total_record_count;
                    this.sendDataToParent(data.total_record_count);
                    return data.project_inspection_tours;
                }
                const message = `Failed to fetch project inspection tour, id: ${this.projectId}.`;
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
                return [];
            });
    }

    prepareMetaChecklist(additional_checklist = []) {
        this.meta_checklist = this.meta_common_checklist;
        this.inspection_tour_row.common_checklist.forEach(function (element) {
            element.checklist_name = "common_checklist";
        });
        this.inspectionTourRowChecklist = [...this.inspection_tour_row.common_checklist];
        if (this.isRailProject) {
            this.inspection_tour_row.rail_checklist.forEach(function (element) {
                element.checklist_name = "rail_checklist";
            });
            this.meta_checklist = this.meta_checklist.concat(this.meta_rail_checklist);
            this.inspectionTourRowChecklist = [...this.inspection_tour_row.common_checklist, ...this.inspection_tour_row.rail_checklist];
        }

        if (this.isIndustrialProject) {
            additional_checklist = (Object.keys(this.inspection_tour_row).length && !additional_checklist.length) ? this.inspection_tour_row.additional_checklist : additional_checklist;
            additional_checklist.forEach(function (element) {
                element.checklist_name = "additional_checklist";
            });
            this.meta_checklist = this.meta_industrial_checklist;
            this.meta_checklist = this.meta_checklist.concat(additional_checklist);
            this.inspection_tour_row.industrial_checklist.forEach(function (element) {
                element.checklist_name = "industrial_checklist";
            });
            this.inspectionTourRowChecklist = [...this.inspection_tour_row.industrial_checklist, ...this.inspection_tour_row.additional_checklist];
        }

        this.inspectionTourRowChecklist = this.inspectionTourRowChecklist.reduce((obj, ans) => {
            obj[ans.question_id] = ans;
            return obj;
        }, {});

        let temp_meta_checklist = this.meta_checklist;
        this.meta_checklist = this.meta_checklist.reduce((obj, que) => {
            obj[que.question_id] = que;
            return obj;
        }, {});

        //get correct display number
        this.meta_checklist = this.updateItemsDisplayNumber(this.meta_checklist, temp_meta_checklist);
    }

    updateItemsDisplayNumber(meta_checklist, temp_meta_checklist) {
        let metaChecklistCategoriesData = [];
        let metaChecklistCategories = [];
        let sequenceNum = 0;
        let sequenceNumArr = {};
        for (let item of temp_meta_checklist) {
            if (item.category) {
                if (!metaChecklistCategoriesData[item.category]) {
                    metaChecklistCategoriesData[item.category] = [];
                    sequenceNumArr[item.category] = sequenceNum += 1;
                }

                meta_checklist[item.question_id].display_number = `${sequenceNumArr[item.category]}.${metaChecklistCategoriesData[item.category].length+1}`;
                metaChecklistCategoriesData[item.category].push(item);
                if (!metaChecklistCategories.includes(item.category)) {
                    metaChecklistCategories.push(item.category);
                }
            }
        }

        let checklist = [];
        for (let key in meta_checklist) {
            checklist.push(meta_checklist[key]);
        }

        let finalMetaChecklist = checklist.sort((a, b) => a.display_number - b.display_number);
        return finalMetaChecklist;
    }

    @ViewChild('managementReviewRef')
    private managementReviewRef: IModalComponent;
    managementReviewModal(row, c){
        //close view inspection tour popup
        if(c) {
            c('Clock Click');
        }
        this.inspection_tour_row = row;
        this.prepareMetaChecklist(this.inspection_tour_row.additional_checklist);
        this.managementReviewRef.open();
    }

    @ViewChild('viewChecklistItemRef')
    private viewChecklistItemRef: IModalComponent;
    viewUnsatisfactoryItemModal(checklistItem, metaChecklistItem, itemQueId, type) {
        this.checklistItem = checklistItem;
        this.metaChecklistItem = metaChecklistItem;
        if (type == 'common_checklist') {
            this.itemQueId = itemQueId;
            this.itemType = 'common_checklist';
        }

        if (type == 'rail_checklist') {
            this.itemQueId = itemQueId;
            this.itemType = 'rail_checklist';
        }

        if (type == 'industrial_checklist') {
            this.itemQueId = itemQueId;
            this.itemType = 'industrial_checklist';
        }

        if (type == 'additional_checklist') {
            this.itemQueId = itemQueId;
            this.itemType = 'additional_checklist';
        }
        this.viewChecklistItemRef.open();
        this.isOpen = true;
    }

    @ViewChild('closeOutRef')
    private closeOutRef: IModalComponent;
    closeoutItemModal (item, itemQueId, type) {
        this.checklistItem = item;
        this.metaChecklistItem = (this.meta_checklist || []).find(meta_item => meta_item.question_id == item.question_id);
        this.prepareMetaChecklist(this.inspection_tour_row.additional_checklist);
        this.closeout_detail = '';
        this.closeout_images = [{}];
        if (type == 'common_checklist') {
            this.itemQueId = itemQueId;
            this.itemType = 'common_checklist';
        }

        if (type == 'rail_checklist') {
            this.itemQueId = itemQueId;
            this.itemType = 'rail_checklist';
        }

        if (type == 'industrial_checklist') {
            this.itemQueId = itemQueId;
            this.itemType = 'industrial_checklist';
        }

        if (type == 'additional_checklist') {
            this.itemQueId = itemQueId;
            this.itemType = 'additional_checklist';
        }
        this.closeOutRef.open();
    }

    openModal(content, size='', windowClass='') {
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size,
            windowClass: windowClass
        });
    }

    closeOutRequest(event) {
        this.closeoutLoading = true;
        let images = (this.closeout_images || []).reduce((result, elm) => {
            if (elm.id) {
                result.push(elm.id);
            }
            return result;
        },[]);
        console.log(`Close out Item with Question Id ${this.itemQueId} in checklist ${this.itemType}`);

        let closeOutItemInfo = {};
        (this.inspection_tour_row[this.itemType] || []).map(item => {
            if(item.question_id == this.itemQueId) {
                item.close_out = {
                    "reviewed_by": this.authUser$.first_name+' '+this.authUser$.last_name,
                    "close_out_at": dayjs().valueOf(),
                    "details": this.closeout_detail,
                    "images": images
                }
                closeOutItemInfo = item;
            }
            return item;
        });

        let closeout_status = 1;
        if (!this.hasUnsatisfactoryItems(this.inspection_tour_row)) {
            closeout_status = 2;
        }

        let body = {
            "checklist_type": this.itemType,
            "closeout_status": closeout_status,
            "item_info": closeOutItemInfo
        }

        this.projectInspectionTourService.updateChecklistToCloseOutItem(body, this.projectId, this.inspection_tour_row.id).subscribe((data: any) => {
            this.closeoutLoading = false;
            if(data && data.success) {
                event.closeFn();
                this.getInspectionTours();
                if (this.isOpen) {
                    this.viewChecklistItemRef.close();
                    this.isOpen = false;
                }

                if (data.project_inspection_tour.status == 2 && this.managementReviewRef) {
                    this.managementReviewRef.close();
                }
                return;
            }
            const message = 'Failed to closed out the item.';
            this.toastService.show(this.toastService.types.ERROR, message);
            event.closeFn();
        });
    }

    closeModal(event) {
        event.closeFn();
    }

    getTaggedCompanyNames(taggedCompanies){
        return (taggedCompanies || []).map(company => company.name).join(',');
    }

    trackByRowIndex(index: number, obj: any){
        return index;
    }

    hasUnsatisfactoryItems(row) {
        if (this.isIndustrialProject) {
            let unsatisfactoryAdditionalItems = row.additional_checklist.filter(checklistItem => ((checklistItem.answer == 'no' || checklistItem.answer == 'poor' || checklistItem.answer == 'fair') && !checklistItem.close_out.close_out_at));
            //console.log(`Additional Unsatisfactory Items: ${unsatisfactoryAdditionalItems.length}`);
            let unsatisfactoryIndustrialItems = row.industrial_checklist.filter(checklistItem => ((checklistItem.answer == 'no' || checklistItem.answer == 'poor' || checklistItem.answer == 'fair') && !checklistItem.close_out.close_out_at));
            //console.log(`Industrial Unsatisfactory Items: ${unsatisfactoryIndustrialItems.length}`);
            return (unsatisfactoryIndustrialItems.length || unsatisfactoryAdditionalItems.length);
        }

        let unsatisfactoryCommonItems = row.common_checklist.filter(checklistItem => ((checklistItem.answer == 'no' || checklistItem.answer == 'poor' || checklistItem.answer == 'fair') && !checklistItem.close_out.close_out_at));
        //console.log(`Common Unsatisfactory Items: ${unsatisfactoryCommonItems.length}`);
        let unsatisfactoryRailItems = [];
        if (this.isRailProject) {
            unsatisfactoryRailItems = row.rail_checklist.filter(checklistItem => ((checklistItem.answer == 'no' || checklistItem.answer == 'poor' || checklistItem.answer == 'fair') && !checklistItem.close_out.close_out_at));
            //console.log(`Rail Unsatisfactory Items: ${unsatisfactoryRailItems.length}`);
        }

        return (unsatisfactoryCommonItems.length || unsatisfactoryRailItems.length);
    }

    addMoreImageRow() {
        this.closeout_images.push({
            identifier: (new Date()).getTime()
        })
    }

    logoImgRetries:number = 5;

    onLogoError($img:any, targetSrc:any) {
        $img.src = '/images/project-placeholder.png';
        if(targetSrc && targetSrc.length && this.logoImgRetries) {
            setTimeout(() => {
                this.logoImgRetries--;
                $img.src = targetSrc;
            }, 2000);
        }
    }

    onLogoLoad($img) {
        this.logoImgRetries = 5;
    }

    mediaUploadDone($event) {
        this.closeout_images.splice(1, 0,...$event.userFile);
        this.closeout_images[0] = {};
    }

    fileDeleteDone($event) {
       if($event && $event.userFile && $event.userFile.id) {
            this.closeout_images = this.closeout_images.filter(r => (r.id !== $event.userFile.id));
       }
    }

    capitalizeFirstLetter(string) {
        if(string) {
            return string.charAt(0).toUpperCase() + string.slice(1);
        }
    }

    getColor(rating) {
        if (rating === 'no' || rating === 'poor') {
            return "red";
        } else if (rating === 'fair') {
            return "orange";
        }
    }

    viewImage(imgSrc, viewItemImgHtml) {
        this.itemImgSrc = imgSrc;
        this.modalService.open(viewItemImgHtml, {
            backdropClass: 'light-blue-backdrop',
            keyboard: true,
            size: "lg"
        });
    }

    getOpenText(row) {
            let totalUnsatisfactory = 0;
            let totalOpenUnsatisfactory = 0;
            if (this.isIndustrialProject) {
                totalUnsatisfactory += ((row.additional_checklist || []).filter(checklistItem => ((checklistItem.answer == 'no' || checklistItem.answer == 'poor' || checklistItem.answer == 'fair')))).length;
                totalOpenUnsatisfactory += ((row.additional_checklist || []).filter(checklistItem => ((checklistItem.answer == 'no' || checklistItem.answer == 'poor' || checklistItem.answer == 'fair') && !checklistItem.close_out.close_out_at))).length;

                totalUnsatisfactory += ((row.industrial_checklist || []).filter(checklistItem => ((checklistItem.answer == 'no' || checklistItem.answer == 'poor' || checklistItem.answer == 'fair')))).length;
                totalOpenUnsatisfactory += ((row.industrial_checklist || []).filter(checklistItem => ((checklistItem.answer == 'no' || checklistItem.answer == 'poor' || checklistItem.answer == 'fair') && !checklistItem.close_out.close_out_at))).length;

                if (!totalOpenUnsatisfactory && !totalUnsatisfactory) {
                    this.hasUnsatisfactory = false;
                    this.unsatisfactoryColumnTxt = `None`;
                    return;
                } else if (!totalOpenUnsatisfactory) {
                    this.hasUnsatisfactory = false;
                    this.unsatisfactoryColumnTxt = `All Closed`;
                    return;
                }
                this.hasUnsatisfactory = true;
                this.unsatisfactoryColumnTxt = `Open: ${totalOpenUnsatisfactory}/${totalUnsatisfactory}`;
                return;
            }

            totalUnsatisfactory += ((row.common_checklist || []).filter(checklistItem => ((checklistItem.answer == 'no' || checklistItem.answer == 'poor' || checklistItem.answer == 'fair')))).length;
            totalOpenUnsatisfactory += ((row.common_checklist || []).filter(checklistItem => ((checklistItem.answer == 'no' || checklistItem.answer == 'poor' || checklistItem.answer == 'fair') && !checklistItem.close_out.close_out_at))).length;

            if (this.isRailProject) {
                totalUnsatisfactory += ((row.rail_checklist || []).filter(checklistItem => ((checklistItem.answer == 'no' || checklistItem.answer == 'poor' || checklistItem.answer == 'fair')))).length;
                totalOpenUnsatisfactory += ((row.rail_checklist || []).filter(checklistItem => ((checklistItem.answer == 'no' || checklistItem.answer == 'poor' || checklistItem.answer == 'fair') && !checklistItem.close_out.close_out_at))).length;
            }

            if (!totalOpenUnsatisfactory && !totalUnsatisfactory) {
                this.hasUnsatisfactory = false;
                this.unsatisfactoryColumnTxt = `None`;
                return;
            } else if (!totalOpenUnsatisfactory) {
                this.hasUnsatisfactory = false;
                this.unsatisfactoryColumnTxt = `All Closed`;
                return;
            }
            this.hasUnsatisfactory = true;
            this.unsatisfactoryColumnTxt = `Open: ${totalOpenUnsatisfactory}/${totalUnsatisfactory}`;
            return;
    }

    @ViewChild('dashboardHtml', { static: true })
    private dashboardHtmlRef: TemplateRef<any>;

    updateDashboard($event, myDrop) {
        this.blockLoader = true;
        let fileName = 'Inspections-Tour-Dashboard-' + dayjs().format(AppConstant.dateFormat_MM_DD_YYYY);
        this.dashboardReportFrom = this.ngbMomentjsAdapter.ngbDateToDayJs($event.fromDate).startOf('day').valueOf();
        this.dashboardReportTo = this.ngbMomentjsAdapter.ngbDateToDayJs($event.toDate).endOf('day').valueOf();


        let body = {
            companyId: this.employerId,
            file_name: fileName,
            from_date: this.dashboardReportFrom,
            to_date: this.dashboardReportTo
        };

        this.projectInspectionTourService.dashboardOfInspectionTour(body, this.projectId, 'html', fileName).subscribe((data:any) => {
            if(myDrop) {
                myDrop.close();
            }
            this.blockLoader = false;
            this.dashboardHtmlContent = this.domSanitizer.bypassSecurityTrustHtml(data);
        });
    }

    @ViewChild('inspectionTourContentRef') private inspectionTourContentRefModal: IModalComponent;
    viewInspectionTour(row) {
        this.inspection_tour_row = row;
        this.blockLoader = true;
        let request = {
            createdAt: row.createdAt,
            companyId: this.employerId,
            type: 'html'
        };

        this.projectInspectionTourService.downloadInspectionTour(request, this.projectId, row.id).subscribe((data:any) => {
            this.inspection_tour_row = row;
            //let dataURI = 'data:text/html,' + encodeURIComponent(data);
            this.blockLoader = false;
            this.inspectionTourContentRefModal.open();
            let iframe = document.getElementById('previewFrame') as HTMLIFrameElement;
            let doc = iframe.contentDocument;
            doc.write(data);
            this.cdRef.detectChanges();
        });
    }

    downloadInspectionTour(row) {
        this.blockLoader = true;
        let request = {
            createdAt: row.createdAt,
            companyId: this.employerId,
            type: 'pdf'
        };

        this.projectInspectionTourService.downloadInspectionTour(request, this.projectId, row.id, () => {
            this.blockLoader = false;
        });
    }

    public openMapWithPin () {
        this.itemMapData = {
            location: this.checklistItem['location_tag'],
            user_ref: this.inspection_tour_row['user_ref'],
            createdAt: this.inspection_tour_row['createdAt'],
            record_id: this.inspection_tour_row['record_ref'],
            status_message: 'Open'
        };
        this.showInspecionPinMapModal();
    }
    
    @ViewChild('inspectionPinMapHtml')  private inspectionPinMapHtmlModal: GenericModalComponent 
    private inspectionPinMapHtmlRef: TemplateRef<any>;
    showInspecionPinMapModal(){
        const metaChecklistData :any = this.metaChecklistItem
        this.inspectionPinMapConfig.modalTitle = `${metaChecklistData?.category}: ${metaChecklistData?.question}`
        return this.inspectionPinMapHtmlModal.open()
    }

    public inspectionPinMapConfig: GenericModalConfig = {
        modalTitle: '',
        hideFooter: true,
        modalOptions: {
            size: 'lg',
        }
    }

    @ViewChild('shareITReportModal') shareITReportModalRef: ShareToolReportToEmailComponent;
    openShareITDetailModal(row) {
        this.shareITReportModalRef.openEmailFormModal(row);
    }

    shareInspectionTourReport(event) {
        this.projectInspectionTourService.shareInspectionTourReport(event.req, event.reportId).subscribe((res:any) => {
            event.cb(res);
        });
    }

    sendDataToParent(data: string) {
        this.totalInspection.emit(data);
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actionMap = {
            'view_inspection': () => this.viewInspectionTour(row),
            'download_inspection': () => this.downloadInspectionTour(row),
            'share_inspection': () => this.openShareITDetailModal(row),
            'close_inspection': () => this.managementReviewModal(row, ''),
        };

        const action = actionMap[entry.key];
        if (action) {
            action();
        } else {
            console.warn(`Unhandled action key: ${entry.key}`);
        }
    }
}

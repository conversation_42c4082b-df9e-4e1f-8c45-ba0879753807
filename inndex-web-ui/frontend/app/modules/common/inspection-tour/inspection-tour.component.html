    <div class="table-responsive-sm">
        <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                       [scrollbarV]="true"
                       [virtualization]="false"
                       [loadingIndicator]="loadingInlineInspectionTour"
                       [rows]="inspectionTourRecords"
                       [footerHeight]="40"
                       [columnMode]="'force'"
                       [rowHeight]="'auto'"
                       [externalPaging]="true"
                       [count]="page.totalElements"
                       [offset]="page.pageNumber"
                       [limit]="page.size"
                       (page)="pageCallback($event, true)"
        >
            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="110">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Inspection Tour #
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    {{row.record_ref}}
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="130">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Date & Time of Inspection
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    {{ (+row.report_datetime) ? dayjs(+row.report_datetime).format(AppConstant.fullDateTimeFormat) : ''}}
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="130">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Date & Time Prepared
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    {{ dayjs(row.createdAt).format(AppConstant.fullDateTimeFormat)}}
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="120">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Prepared By
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    <span appTooltip>{{row?.user_ref?.first_name}} {{row?.user_ref?.middle_name || ''}} {{row?.user_ref?.last_name}}</span>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="140">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Unsatisfactory Items
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    <div style="margin-left: 10px;">
                        {{ getOpenText(row) }}
                        <button *ngIf="!hasUnsatisfactory" [ngClass]="{'unsatisfactoryItemsStatus bgSuccess cursor-default': true}">
                            {{ unsatisfactoryColumnTxt }}
                        </button>
                        <button *ngIf="hasUnsatisfactory" [ngClass]="{'unsatisfactoryItemsStatus bgDanger': true}" (click)="managementReviewModal(row, '')">
                            {{ unsatisfactoryColumnTxt }}
                        </button>
                    </div>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column headerClass="font-weight-bold action-column"
                                  cellClass="action-column no-ellipsis" [sortable]="false" minWidth="100">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Action
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    <button-group
                        [buttons]="rowButtonGroup"
                        [btnConditions]="[
                            true,
                            true,
                            true,
                            (row.status_message === 'Open') && hasUnsatisfactoryItems(row)
                        ]"
                        (onActionClick)="rowBtnClicked($event, row)">
                    </button-group>
                </ng-template>
            </ngx-datatable-column>
        </ngx-datatable>
    </div>


<i-modal #managementReviewRef title="Review and Close Out" size="md" [showCancel]="false" rightPrimaryBtnTxt="Close" (onClickRightPB)="closeModal($event)">
        <div class="tb-attendees" *ngIf="inspection_tour_row?.common_checklist.length || (inspection_tour_row?.rail_checklist.length && isRailProject) || ((inspection_tour_row?.industrial_checklist.length || inspection_tour_row?.additional_checklist.length) && isIndustrialProject)">
            <h6><strong>Inspection Tour #{{ inspection_tour_row?.record_ref }} - Unsatisfactory Items</strong></h6>
            <div>
                <table class="table small table-sm table-striped">
                    <thead>
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Action</th>
                    </tr>
                    </thead>
                    <tbody>
                        <ng-container *ngFor="let item of meta_checklist; trackBy : trackByRowIndex; let i = index;">
                            <tr *ngIf="negativeRating.includes(inspectionTourRowChecklist[item.question_id]?.answer)">
                                <td style="padding-right: 0px; width:8%">{{ item.display_number }}</td>
                                <td style="padding-right: 0px; padding-left: 0px; width:80%">
                                    <p style="margin: 0px;font-size: 10px;">{{ item.category+ ": "}}{{item.question}}</p>
                                    <p style="margin: 0px;font-size: 8px;">(<i>{{(project.project_type === 'industrial') ? "Rating: " : "Satisfactory: "}}<span [ngStyle]="{color: getColor(inspectionTourRowChecklist[item.question_id].answer)}">{{ capitalizeFirstLetter(inspectionTourRowChecklist[item.question_id]?.answer) }}</span></i>)</p>
                                </td>
                                <td *ngIf="!inspectionTourRowChecklist[item.question_id]?.close_out?.close_out_at" style="width:12%; text-align: right;">
                                    <button title="View Inspection Tour" style="font-size: 10px;"
                                            class="btn btn-sm btn-outline-primary mr-1 p-1"
                                            (click)="viewUnsatisfactoryItemModal(inspectionTourRowChecklist[item.question_id], item,  item.question_id, inspectionTourRowChecklist[item.question_id]?.checklist_name)">
                                        <i class="fa fa-search"></i></button>

                                    <button title="Close Out" class="btn btn-sm btn-outline-primary p-1" style="font-size: 10px;"
                                            (click)="closeoutItemModal(inspectionTourRowChecklist[item.question_id], item.question_id, inspectionTourRowChecklist[item.question_id]?.checklist_name)">
                                        <i class="fa fa-times" aria-hidden="true"></i></button>
                                </td>
                                <td *ngIf="inspectionTourRowChecklist[item.question_id]?.close_out?.close_out_at" style="width:10%; text-align: center;">
                                    <i class="fa fa-check-square text-success"></i>
                                </td>
                            </tr>
                        </ng-container>
                    </tbody>
                </table>
            </div>
        </div>
    <block-loader [show]="(false)" #modalLoader></block-loader>
</i-modal>

<i-modal #inspectionTourContentRef [title]="'Inspection Tour #'+ inspection_tour_row?.record_ref" size="lg" [showCancel]="false" rightPrimaryBtnTxt="Done" 
    (onClickRightPB)="closeModal($event)" [windowClass]="'xl-modal'">
    <iframe class="border-0 w-100 h-100" previewFrame id='previewFrame' style="position: absolute;"></iframe>
</i-modal>

<i-modal #closeOutRef [title]="'Closeout - ' + metaChecklistItem?.question" size="md" cancelBtnText="Cancel" rightPrimaryBtnTxt="Close Out" 
(onClickRightPB)="closeOutRequest($event)" [rightPrimaryBtnDisabled]="!closeOutDetail.valid">
        <div class="text-left mb-3">
            <table class="table table-sm table-bordered mb-0" style="font-size: 14px;">
                <tbody>
                    <tr>
                        <td class="tr-bg-dark-color w-25"><strong>Category:</strong></td>
                        <td>
                            {{ metaChecklistItem?.category }} <span *ngIf="metaChecklistItem?.category && metaChecklistItem?.question"> - </span> {{ metaChecklistItem?.question }}
                        </td>
                    </tr>
                    <tr>
                        <td class="tr-bg-dark-color w-25">
                            <strong>{{(project.project_type === 'industrial') ? "Rating:" : "Satisfactory:"}}</strong>
                        </td>
                        <td>
                            <span [ngStyle]="{color: getColor(checklistItem.answer)}"> {{ capitalizeFirstLetter(checklistItem.answer) }}</span>
                        </td>
                    </tr>
                    <tr *ngIf="checklistItem && checklistItem.summary">
                        <td class="tr-bg-dark-color w-25"><strong>Summary:</strong></td>
                        <td>
                            {{ checklistItem?.summary }}
                        </td>
                    </tr>
                    <tr *ngIf="checklistItem && checklistItem.corrective_action_required">
                        <td class="tr-bg-dark-color w-25">
                            <strong *ngIf="checklistItem && (checklistItem.answer != 'fair')">Corrective Action Required:</strong>
                            <strong *ngIf="checklistItem && (checklistItem.answer == 'fair')">Action Required:</strong>
                        </td>
                        <td>
                            {{ checklistItem?.corrective_action_required }}
                        </td>
                    </tr>
                    <tr *ngIf="checklistItem?.location_tag?.lat && checklistItem?.location_tag?.long">
                        <td class="tr-bg-dark-color w-25">
                            <strong>Location Tag (Lat, Long):</strong>
                        </td>
                        <td>
                            <span style="color: blue; font-size: 14px;" (click)="openMapWithPin()" class="cursor-pointer">
                                ({{checklistItem?.location_tag.lat | number : decimalConfig}}, {{checklistItem?.location_tag.long | number : decimalConfig}})
                            </span>
                        </td>
                    </tr>
                    <tr *ngIf="checklistItem && checklistItem.responsible_user_ref && checklistItem.responsible_user_ref.id">
                        <td class="tr-bg-dark-color w-25">
                            <strong>Assigned To:</strong>
                        </td>
                        <td>
                            {{ checklistItem?.responsible_user_ref.name }}
                        </td>
                    </tr>
                    <tr *ngIf="checklistItem && checklistItem.tagged_company_ref && checklistItem.tagged_company_ref.length">
                        <td class="tr-bg-dark-color w-25">
                            <strong>Responsible Company:</strong>
                        </td>
                        <td>
                            {{ getTaggedCompanyNames(checklistItem?.tagged_company_ref) }}
                        </td>
                    </tr>
                    <tr *ngIf="checklistItem && checklistItem.closeout_due_on">
                        <td class="tr-bg-dark-color w-25">
                            <strong>Closeout Due Date:</strong>
                        </td>
                        <td>
                            {{ dayjs(checklistItem.closeout_due_on).format(AppConstant.defaultDateFormat) }}
                        </td>
                    </tr>
                    <tr *ngIf="checklistItem && checklistItem.appendix && checklistItem.appendix.length">
                        <td colspan="2">
                            <div class="row">
                                <div class="col-md-4" *ngFor="let t of checklistItem?.appendix">
                                    <img class="img-thumbnail" [src]="t?.sm_url || t?.file_url" alt="Item - Image" style="width: 100%; cursor: pointer;" (click)="viewImage(t?.file_url, viewItemImgHtml)">
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="mb-2 mt-1">
            <p class="mb-1"><strong>Details</strong> <small class="required-asterisk ">*</small></p>
            <textarea class="form-control" name="closeout_detail"
                      [(ngModel)]="closeout_detail"
                      ng-value="closeout_detail"
                      placeholder="Closeout detail"
                      #closeOutDetail="ngModel" required></textarea>
            <div class="alert alert-danger" [hidden]="closeOutDetail.valid">Closeout detail is required</div>
        </div>
        <div class="col-md-12 p-0 mt-3">
            <p class="mb-1"><strong>Upload Files</strong></p>
        </div>
        <div class="col-md-12 p-0 d-flex flex-wrap mb-4">
            <div *ngFor="let c of closeout_images" [ngClass]='{"flex-grow-1 p-0": true, "col-md-4": c.id, "col-md-12 mb-4": !c.id}'>
                <file-uploader-v2
                    [disabled]="false"
                    [init]="c"
                    [category]="'close-out'"
                    (uploadDone)="mediaUploadDone($event)"
                    [allowedMimeType]="allowedMime"
                    [showHyperlink]="(c.file_mime && c.file_mime == 'application/pdf') ? 1 : 0"
                    [showFileName]="(c.file_mime && c.file_mime == 'application/pdf') ? 1 : 0"
                    (deleteFileDone)="fileDeleteDone($event)"
                    [showDeleteBtn]="true" [multipleUpload]="true"
                >
                </file-uploader-v2>
            </div>
        </div>
 </i-modal>
<block-loader [show]="(closeoutLoading)" [showBackdrop]="true" alwaysInCenter="true"></block-loader>

<i-modal #viewChecklistItemRef title="Item Details" size="md" cancelBtnText="OK" [rightPrimaryBtnTxt]="(!checklistItem?.close_out?.close_out_at) ? 'Close Out' : ''" 
    (onClickRightPB)="closeoutItemModal(checklistItem, itemQueId, itemType)">
        <div class="text-left">
            <table class="table table-sm table-bordered mb-0" style="font-size: 14px;">
                <tbody>
                <tr *ngIf="metaChecklistItem?.category || metaChecklistItem?.question">
                    <td class="tr-bg-dark-color w-25"> <strong>Category: </strong> </td>
                    <td>{{ metaChecklistItem?.category }} <span *ngIf="metaChecklistItem.category && metaChecklistItem?.question"> - </span> {{ metaChecklistItem?.question }} </td>
                </tr>
                <tr>
                    <td class="tr-bg-dark-color w-25">
                        <strong>{{(project.project_type === 'industrial') ? "Rating:" : "Satisfactory:"}}</strong>
                    </td>
                    <td>
                        <span [ngStyle]="{color: getColor(checklistItem.answer)}"> {{ capitalizeFirstLetter(checklistItem.answer) }}</span>
                    </td>
                </tr>
                <tr *ngIf="checklistItem && checklistItem.summary">
                    <td class="tr-bg-dark-color w-25">
                        <strong>Summary:</strong>
                    </td>
                    <td>
                        {{ checklistItem?.summary }}
                    </td>
                </tr>
                <tr *ngIf="checklistItem && checklistItem.corrective_action_required">
                    <td class="tr-bg-dark-color w-25">
                        <strong *ngIf="checklistItem && (checklistItem.answer != 'fair')">Corrective Action Required:</strong>
                        <strong *ngIf="checklistItem && (checklistItem.answer == 'fair')">Action Required:</strong>
                    </td>
                    <td>
                        {{ checklistItem?.corrective_action_required }}
                    </td>
                </tr>
                <tr *ngIf="checklistItem?.location_tag?.lat && checklistItem?.location_tag?.long">
                    <td class="tr-bg-dark-color w-25">
                        <strong>Location Tag (Lat, Long):</strong>
                    </td>
                    <td>
                        <span style="color: blue; font-size: 14px;" (click)="openMapWithPin()" class="cursor-pointer">
                            ({{checklistItem?.location_tag.lat | number : decimalConfig}}, {{checklistItem?.location_tag.long | number : decimalConfig}})
                        </span>
                    </td>
                </tr>
                <tr *ngIf="checklistItem && checklistItem.responsible_user_ref && checklistItem.responsible_user_ref.id">
                    <td class="tr-bg-dark-color w-25">
                        <strong>Assigned To:</strong>
                    </td>
                    <td>
                        {{ checklistItem?.responsible_user_ref.name }}
                    </td>
                </tr>
                <tr *ngIf="checklistItem && checklistItem.tagged_company_ref && checklistItem.tagged_company_ref.length">
                    <td class="tr-bg-dark-color w-25">
                        <strong>Resonsible Company:</strong>
                    </td>
                    <td>
                        {{ getTaggedCompanyNames(checklistItem?.tagged_company_ref) }}
                    </td>
                </tr>
                <tr *ngIf="checklistItem && checklistItem.closeout_due_on">
                    <td class="tr-bg-dark-color w-25">
                        <strong>Closeout Due Date:</strong>
                    </td>
                    <td>
                        {{ dayjs(checklistItem.closeout_due_on).format(AppConstant.defaultDateFormat) }}
                    </td>
                </tr>
                <tr *ngIf="checklistItem && checklistItem.appendix && checklistItem.appendix.length">
                    <td colspan="2">
                        <div class="row">
                            <div class="col-md-4" *ngFor="let t of checklistItem?.appendix">
                                <img class="img-thumbnail" [src]="t?.sm_url || t?.file_url" alt="Item - Image" style="width: 100%; cursor: pointer;" (click)="viewImage(t?.file_url, viewItemImgHtml)">
                            </div>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
</i-modal>

<ng-template #viewItemImgHtml let-c="close" let-d="dismiss">
    <div class="modal-header">
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="form-group row">
            <div class="col-sm-12 mt-2 text-center">
                <img [src]="itemImgSrc" (error)="onLogoError(img, itemImgSrc)" (load)="onLogoLoad(img)" style="width: 100%; height: auto;" #img />
            </div>
        </div>
    </div>
</ng-template>

<ng-template #dashboardHtml let-c="close" let-d="dismiss">
    <div class="modal-body p-1">
        <div ngbDropdown class="d-inline-block col-md-12 p-0" placement="bottom-right" [autoClose]="'outside'" #myDrop="ngbDropdown" style="margin-top: 4px;">
            <div class="float-right d-flex align-items-sm-center">
                <button style="width: 250px;" class="btn btn-sm dropdownBtnMakeup" id="dropdownDlReport1" ngbDropdownToggle>
                    <i class="far fa-calendar"></i>
                    <span>{{ dayjs(this.dashboardReportFrom).format(AppConstant.dateFormat_Do_MMM_YYYY) }} - {{ dayjs(this.dashboardReportTo).format(AppConstant.dateFormat_Do_MMM_YYYY) }}</span>
                    <i class="fa fa-caret-down" aria-hidden="true"></i>
                </button>
                <div ngbDropdownMenu aria-labelledby="dropdownDlReport1">
                    <download-report-modal-box [actionBtnText]="'Update Report'" [xlsxOnly]="true" [fromDate]="dashboardStartDate" (downloadModal)="updateDashboard($event, myDrop)"></download-report-modal-box>
                </div>
                <button type="button" class="close mb-2 ml-3 mr-1" aria-label="Close" (click)="d('Cross click')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        </div>

        <div [innerHtml]="dashboardHtmlContent" style="min-height: 450px;"></div>
    </div>
    <div class="modal-footer" style="border: none;">
        <button title="Download Dashboard" class="btn btn-outline-success"
                (click)="downloadDashboardPdf()">Download</button>
        <button type="button" class="btn btn-outline-primary" (click)="d('Cross click')">OK</button>
    </div>
</ng-template>
<block-loader [show]="(blockLoader)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>

<app-generic-modal #inspectionPinMapHtml [genericModalConfig]="inspectionPinMapConfig">
    <ng-container *ngTemplateOutlet="cc"></ng-container>
    <ng-template #cc>
        <progress-photo-location-map
            [showMapWithMultiPin]="false"
            [row_data]="itemMapData"
            [project_data]=""
            mapTitle="{{ metaChecklistItem?.category }}: {{metaChecklistItem?.question}}"
            feature="inspections"
        >
        </progress-photo-location-map>

    </ng-template>
</app-generic-modal>


<share-tool-report-to-email
    [projectId]="projectId" [employerId]="employerId" #shareITReportModal
    [tool_name]="'Share Inspection Tour'" (onSave)="shareInspectionTourReport($event)">
</share-tool-report-to-email>
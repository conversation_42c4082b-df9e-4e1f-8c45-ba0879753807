import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { AttachmentFile, CreateEmployer, ITP_TYPE, Project, QualityChecklist, QualityChecklistsService, getUniqueId } from "@app/core";
import { GenericConfirmationModalComponent } from '@app/shared';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { IModalComponent } from "@app/shared";
import { DragulaService } from 'ng2-dragula';
import { NgForm } from '@angular/forms';
import { HttpParams } from '@angular/common/http';

@Component({
    selector: 'create-or-edit-itp',
    templateUrl:'create-or-edit-itp.component.html',
    providers: [ NgbModal ],
    styleUrls:['./create-or-edit-itp.component.scss'],
})
export class CreateorEditITPComponent implements OnInit {
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    @Input() project: Project = new Project;
    @Input() company: CreateEmployer = new CreateEmployer; 
    qualityChecklist: any = [];
    @Input() qualityCL: QualityChecklist = new QualityChecklist;
    initQCL: QualityChecklist = new QualityChecklist;
    @Output() fetchQCLData = new EventEmitter;
    fieldsValid: boolean = true;
    queId: number = getUniqueId();
    loading: boolean = false;
    editModalOpened = false;
    has_subheadings: boolean = false;
    has_changed: boolean = false;
    qc_docs: Array<AttachmentFile>= [{}]
    constructor(
        private qualityCLService: QualityChecklistsService,
        private readonly dragulaService: DragulaService,
        ) {
            this.dragulaCreateGroup('heading', 'drag-main');
            this.dragulaCreateGroup('subheading', 'drag-sub');
    }
     
    ngOnInit() {
    }

    @ViewChild('itpForm') itpForm;
    @ViewChild('itpAccessModalHtml') private itpAccessGenricModal: IModalComponent;
     expandItpsModal() {
        return this.itpAccessGenricModal.open();
    }
   
    getQCLdata(){
        this.fetchQCLData.emit({emit: true})
    }
    
    @ViewChild('addOrEditITPModal') private addOrEditITPModal: IModalComponent;
    @ViewChild('qclForm') private qclForm: NgForm;
    openCreateOrOpenItpModal(qualityCL?, itpType?){
        this.qualityCL = qualityCL;
        if(qualityCL && qualityCL.id){
            this.qualityCL = qualityCL;
            this.initQCL = {...qualityCL};
            this.qc_docs = [{}].concat(this.qc_docs.filter(doc => Object.keys(doc).length !== 0));
            this.editModalOpened = true;
            this.has_subheadings = this.qualityCL.has_subheadings;
            this.has_changed = false;
        }else{
            this.qclForm.resetForm();
            this.qualityCL =  new QualityChecklist;
            this.has_subheadings = false;
            this.qualityCL.itp_type = itpType;
            this.editModalOpened = false;
        }
        
        this.addOrEditITPModal.open();
    }
    closeItpAccessModal(){
        this.itpAccessGenricModal.close();
    }

    resetItpForm() {
        this.qualityCL = new QualityChecklist;
    }

    validityChanged($event) {
        this.fieldsValid = $event;
        this.valueChanged();
    }
    mediaUploadDone(data: any){
        if(data && data.userFile){
            this.qc_docs.splice(1, 0, ...data.userFile);
            this.qc_docs[0] = {};
        }
    }
    fileDeleteDone($event, i) {
        if($event && $event.userFile && $event.userFile.id) {
            this.qc_docs.splice(i, 1);
        }
    }
    removeCheckItem($event, questionIndex) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Remove',
            title: `Are you sure you want to remove this question?`,
            confirmLabel: 'Remove',
            onConfirm: () => {
                this.qualityCL.qc_items.splice(questionIndex, 1);
            }
        });
        this.valueChanged();
    }
    addCheckItem(has_subheading) {
        this.qualityCL.qc_items.push({
            [has_subheading ? 'id' : 'item_id']: this.queId,
            item_que : ''
        });

        this.queId = getUniqueId();
        this.valueChanged();
    }
    addUserBlock() {
        if (!this.qualityCL.sign_off_parties) {
            this.qualityCL.sign_off_parties = [];
        }
        this.qualityCL.sign_off_parties.push({
            role: ""
        });
        this.valueChanged();
    }
    
    submitAddOrEditItp(form){
        if (!form.valid) {
            console.log('form is not valid');
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Alert',
                title: `form is not valid`,
                cancelLabel: 'ok'
            });
            return;
        }
        let qclReq = this.qualityCL;
        qclReq.qc_doc = this.qc_docs.map(({ id }) => id).filter(id => id != null);
        
        let projectId = this.project.id;
        let companyId = this.company.id;
        this.loading = true;
        if (qclReq.id) {
            this.confirmUpdate(qclReq, qclReq.id, projectId, companyId);
        } else {
            this.createChecklist.call(this, qclReq, projectId, companyId);
        }
    }
    handleResponse(out) {
        if (!out.success) {
            console.error('Operation failed', out);
            alert(out.message ? out.message : 'Operation failed');
        }
        this.resetItpForm();
        this.addOrEditITPModal.close();
        this.getQCLdata();
        this.loading = false;
    }
    
    updateChecklist(qclReq, id, projectId, companyId, deletePartialCLI = false) {
        this.loading = true
        const params = new HttpParams().set('deletePartialCLI', deletePartialCLI.toString());
        if (qclReq.itp_type === ITP_TYPE.project) {
            qclReq.project_ref = projectId;
            this.qualityCLService.updateQChecklist(projectId, id, qclReq, params).subscribe(out => this.handleResponse.call(this, out));
        } else {
            qclReq.company_ref = companyId;
            this.qualityCLService.updateCompanyQChecklist(companyId, id, qclReq, params).subscribe(out => this.handleResponse.call(this, out));
        }
    }
    
    createChecklist(qclReq, projectId, companyId) {
        if (qclReq.itp_type === ITP_TYPE.project) {
            qclReq.project_ref = projectId;
            this.qualityCLService.createQChecklist(projectId, qclReq).subscribe(out => this.handleResponse.call(this, out));
        } else {
            qclReq.company_ref = companyId;
            this.qualityCLService.createCompanyQChecklist(companyId, qclReq).subscribe(out => this.handleResponse.call(this, out));
        }
    }
    
   
    removeAlertRecipient($event, index) {
        this.qualityCL.sign_off_parties.splice(index, 1);
        this.valueChanged();
    }

    isItpChecked(item:QualityChecklist){
        if(item.itp_type === 'project'){
            return item.enabled;
        }else{
            let existsOnProjects = item.activate_on_projects.find(a=> a.project_id === this.project.id);
            return existsOnProjects ? existsOnProjects.enabled : false;
        }
    }

    shareItpReport(event) {
        this.qualityCLService.shareItpReport(event.req, event.reportId).subscribe((res:any) => {
            event.cb(res);
        });
    }

    toggleHasSubheading($event) {
        let hasSubheads = $event.target.checked;
        let tickTxt = (hasSubheads) ? 'ticking' : 'unticking';
        let headTxt = (hasSubheads) ? 'checklists' : 'subheadings';
        if(this.qualityCL.qc_items && this.qualityCL.qc_items.length) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Warning',
                title: `By ${tickTxt} this on, all pre-filled ${headTxt} will be cleared. Do you wish to continue?`,
                confirmLabel: 'Continue',
                onConfirm: () => {
                    this.qualityCL.has_subheadings = hasSubheads;
                    this.toggleHasSubheadingConfirm(hasSubheads);
                },
                onClose: () => {
                    this.has_subheadings = !hasSubheads;
                    this.qualityCL.has_subheadings = !hasSubheads;
                }
            });
        } else {
            this.toggleHasSubheadingConfirm(hasSubheads);
        }
        this.valueChanged();
    }

     private toggleHasSubheadingConfirm(hasSubheads: boolean) {
        let newChecklist = {};
        if(hasSubheads){
            newChecklist = {
                id : getUniqueId(),
                heading : '',
                subheadings : [{
                    item_id : 1,
                    item_que : ''
                }]
            };
        } else {
            newChecklist = {
                item_id : getUniqueId(),
                item_que : ''
            };
            this.qualityCL.section_approval_required = false;
        }

        this.qualityCL.qc_items = [newChecklist];
    }
    private dragulaCreateGroup(groupName: string, className: string): void {
        this.dragulaService.destroy(groupName);
        this.dragulaService.createGroup(groupName, {
            moves: (el, container, handle) => {
                return handle.className.includes(className);
            }
        });
    }

    confirmUpdate(itpRequest, itpId, projectId, companyId) {
        const getPartiallyCompletedReports = (itp_type) => {
            const service = itp_type === ITP_TYPE.company
                ? this.qualityCLService.getPartiallyCompletedCLinspectionsCountCA(itpId, companyId)
                : this.qualityCLService.getPartiallyCompletedCLinspectionsCountSA(itpId, projectId);
    
            service.subscribe((updateResponse) => {
                this.loading = false;
                if (updateResponse.success) {
                    this.handleITPUpdateResponse(updateResponse, itpRequest, itpId, projectId, companyId);
                }
            });
        };
    
        getPartiallyCompletedReports(itpRequest.itp_type);
    }


    handleITPUpdateResponse(updateResponse, itpRequest, itpId, projectId, companyId) {
        if ( updateResponse.totalPartiallyCompletedReports > 0 && this.has_changed) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Warning',
                title: `Updating this ITP will delete ${updateResponse.totalPartiallyCompletedReports} partially completed ITPs. Are you sure you want to proceed?`,
                cancelLabel: 'cancel',
                confirmLabel: 'OK',
                onConfirm: () => {
                    itpRequest.deletepartialcli = true;
                    this.updateChecklist(itpRequest, itpId, projectId, companyId, true);
                }
            });
        } else {
            this.updateChecklist(itpRequest, itpId, projectId, companyId);
        }
    }

    valueChanged(){
        if(this.editModalOpened){
            this.has_changed = true;
        } 
    }

    addSubItem(HeadIndex, queId) {
        if(!this.qualityCL.qc_items[HeadIndex].subheadings){
            this.qualityCL.qc_items[HeadIndex].subheadings = [];
        }
        this.qualityCL.qc_items[HeadIndex].subheadings.push({
            item_id : getUniqueId(),
            item_que : ''
        });
        this.valueChanged();
    }
    removeSubItem($event, HeadIndex, SubIndex) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete Item',
            title: `Are you sure you want to delete this item?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.qualityCL.qc_items[HeadIndex].subheadings.splice(SubIndex, 1);
            }
        });
        this.valueChanged();
    }
}
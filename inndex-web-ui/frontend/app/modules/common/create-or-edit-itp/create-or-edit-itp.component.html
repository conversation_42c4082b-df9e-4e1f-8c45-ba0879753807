<i-modal
  #addOrEditITPModal
  [title]="editModalOpened ? ('Edit ' + qualityCL.qc_title) : ('Add ITP')"
  size="lg"
  [rightPrimaryBtnDisabled]="!qclForm.valid || !fieldsValid"
  [rightPrimaryBtnTxt]="editModalOpened ? 'Update' : 'Create'"
  (onClickRightPB)="submitAddOrEditItp(qclForm)" 
  (onCancel)="resetItpForm()">
        <form novalidate #qclForm="ngForm">
            <div class="form-group">
                <label>Form Title<small class="required-asterisk">*</small></label>
                <div class="input-group mb-3">
                    <input type="text"  class="form-control" #qc_title="ngModel" ng-value="qualityCL.qc_title" placeholder="Form title..." name="qc_title" required [(ngModel)]="qualityCL.qc_title">
                    <div class="alert alert-danger mb-0 mt-1 py-1" [hidden]="qc_title.valid">title is required.</div>
                </div>
            </div>
            <div class="form-group">
                <label>Form Reference<small class="required-asterisk">*</small></label>
                <div class="input-group mb-3">
                    <input type="text"  class="form-control" #qc_ref="ngModel" ng-value="qualityCL.qc_ref" placeholder="Form reference..." name="qc_ref" required [(ngModel)]="qualityCL.qc_ref">
                    <div class="alert alert-danger mb-0 mt-1 py-1" [hidden]="qc_ref.valid">Reference is required.</div>
                </div>
            </div>
            <label class="font-weight-bold details">Details:</label>
               <custom-detail-fields-manager (validityChanged)="validityChanged($event)" [container_obj]="qualityCL" [fields_key]="'custom_fields'"></custom-detail-fields-manager>
            <label class="font-weight-bold">Documents:</label>
            <div class="form-group">
                <div class="col-md-12 p-0">
                    <div *ngFor="let c of qc_docs; let i = index" class="p-0 col-md-12 mt-2">
                        <div [ngClass]="{'file-ref':c.file_url}">
                            <file-uploader-v2
                                (deleteFileDone)="fileDeleteDone($event, i)"
                                (uploadDone)="mediaUploadDone($event)"
                                [allowedMimeType]="['application/pdf', 'application/x-pdf']"
                                [disabled]="false"
                                [init]="c"
                                [multipleUpload]="true"
                                [showThumbnail]="false"
                                [showDeleteBtn]="true"
                                [showFileName]="true"
                                [hasImgAndDoc]="false"
                                [category]="'qc-docs'"
                                [showHyperlink]="true"
                                [dragnDropTxt]="'Drag and drop pdf here'"
                                [showFileFullName]="true"
                                class="pl-0"
                            >
                            </file-uploader-v2>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="font-weight-bold">Checklist:<small class="required-asterisk">*</small></label>
                <div class="form-group">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" name="has_subheadings"
                               id="has_subheadings" [disabled]="editModalOpened" [(ngModel)]="has_subheadings" (click)="toggleHasSubheading($event)">
                        <label class="custom-control-label font-weight-bold" for="has_subheadings">Include Sections</label>
                        <i ngbTooltip="An example of section could be 'Documentation' and checklist 'Induction Forms'." class="ml-1 fas fa-info-circle mt-1"></i>
                    </div>
                </div>
                <div class="d-block">
                    <div class="col-12 p-0 mb-2 heading-list" dragula="heading" [(dragulaModel)]="qualityCL.qc_items">
                        <ng-template ngFor let-item [ngForOf]="(qualityCL.qc_items || [])" let-i="index" *ngIf="!qualityCL.has_subheadings">
                            <div class="drag-box py-2">
                                <div class="row mx-0">
                                    <div class="col-1 text-center align-self-center p-0">
                                        <i class="fa fa-bars drag-main" aria-hidden="true"></i>
                                    </div>
                                    <div class="col-10 p-0 align-self-center">
                                        <input  type="text" class="form-control" placeholder="Add an item..." (change)="valueChanged()" #checkItem="ngModel"
                                            [(ngModel)]="qualityCL.qc_items[i].item_que" name="question{{i}}"
                                            ng-value="qualityCL.qc_items[i].item_que" required autocomplete="off">
                                        <div class="alert alert-danger mb-0 mt-1 py-1" [hidden]="checkItem.valid">Checklist item is required.</div>
                                    </div>
                                    <div class="col-1 text-start align-self-center p-0" *ngIf = "qualityCL.qc_items.length > 1">
                                        <span class="m-2  text-danger bin cursor-pointer align-top material-symbols-outlined" (click)="removeCheckItem($event, i)">delete</span>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                        <ng-template ngFor let-item [ngForOf]="(qualityCL.qc_items || [])" let-i="index" *ngIf="qualityCL.has_subheadings">
                            <div class="drag-box py-2">
                                <div class="row mx-0 mb-3 ml-1">
                                    <div class="col-1 text-center align-self-center p-0">
                                        <i class="fa fa-bars drag-main" aria-hidden="true"></i>
                                    </div>
                                    <div class="col-10 p-0 align-self-center">
                                        <input  type="text" class="form-control" placeholder="Section Name" #headItem="ngModel"
                                           [(ngModel)]="item.heading" name="heading{{item.id}}" (change)="valueChanged()" required autocomplete="off">
                                        <div class="alert alert-danger mb-1 mt-1 py-1" [hidden]="headItem.valid">Section name is required.</div>
                                    </div>
                                    <div class="col-1 text-start align-self-center p-0" *ngIf = "qualityCL.qc_items.length > 1">
                                        <span class="m-2  text-danger bin material-symbols-outlined cursor-pointer align-top" (click)="removeCheckItem($event, i)">delete</span>
                                    </div>
                                </div>
                                <div class="row mx-0 ml-5">
                                    <div class="col-12 p-0 mb-2 sub-heading-list" dragula="subheading" [(dragulaModel)]="item.subheadings" id="subheading">
                                        <ng-template ngFor let-subitem [ngForOf]="(item.subheadings || [])" let-k="index">
                                            <div class="drag-box mb-3 pr-1">
                                                <div class="row mx-0">
                                                    <div class="col-1 text-center align-self-center p-0">
                                                        <i class="fa fa-bars drag-sub" aria-hidden="true"></i>
                                                    </div>
                                                    <div class="col-10 p-0 align-self-center">
                                                        <input type="text"  class="form-control" (change)="valueChanged()" placeholder="Checklist Item" #checkItem="ngModel"
                                                            [(ngModel)]="subitem.item_que" name="question_{{item.id}}_{{subitem.item_id}}" required autocomplete="off">
                                                        <div class="alert alert-danger mb-1 mt-1 py-1" [hidden]="checkItem.valid">Checklist item is required.</div>
                                                    </div>
                                                    <div class="col-1 text-start align-self-center p-0">
                                                        <span class="m-2  text-danger bin cursor-pointer material-symbols-outlined align-top" (click)="removeSubItem($event, i, k)">delete</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </ng-template>
                                    </div>
                                    <div class="col-12 p-0 py-1 mt-n3">
                                        <span class="text-primary cursor-pointer medium-font d-inline font-weight-bold horizontal-center ml-3" (click)="addSubItem(i, item.id)"><span class="material-symbols-outlined font-weight-bolder small-font">add</span> Add Checklist Item</span>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </div>
                    <div class="input-group">
                       <button type="button" class="btn btn-sm btn-outline-brandeis-blue btn-hover m-btn-size-lg" id="newBtn" (click)="addCheckItem(qualityCL.has_subheadings)">
                            <div class="d-flex align-items-center justify-content-center">
                                <span class="material-symbols-outlined x-large-font mr-1">
                                    add
                                </span>
                                <span class="mr-1">Add Section</span>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
            <div *ngIf="qualityCL.has_subheadings">
                <div class="form-group">
                    <span class="d-block"><strong>Section Approval</strong></span>
                    <label class="justify-content-between mb-0">Require Sign-off for individual sections
                        <div class="custom-control custom-switch d-inline-block" >
                            <input type="checkbox" class="custom-control-input" id="section_approval_required" name="section_approval_required"
                            [checked]="true" (change)="valueChanged()" [(ngModel)]="qualityCL.section_approval_required">
                            <label class="custom-control-label" for="section_approval_required"></label>
                        </div>
                    </label>
                </div>
            </div>
            <div class="form-group" class='sign-off'>
                <label class="font-weight-bold">Final Approval<small class="required-asterisk">*</small> <i container="body" class="fa fa-info-circle small" [closeDelay]="500"
                    [ngbTooltip]="'Use this section to add all parties that will be required to sign off the report.'"
                    [openDelay]="200"></i></label>
                <table class="table">
                    <tbody>
                    <ng-template ngFor let-item [ngForOf]="(qualityCL.sign_off_parties || [])" let-i="index">
                        <tr class="horizontal-center justify-content-between" style="padding-right: 1.15rem;">
                            <td class="flex-fill">
                                <input type="text" (change)="valueChanged()" class="form-control" placeholder="Client, Contractor, etc" #roleItem="ngModel"
                                    [(ngModel)]="qualityCL.sign_off_parties[i].role" name="{{'role' + i}}"
                                    ng-value="qualityCL.sign_off_parties[i].role" required autocomplete="off">
                                    <div class="alert alert-danger mb-0 mt-1 py-1" [hidden]="roleItem.valid">Role is required.</div>
                            </td>
                            <td class="text-center ml-2" *ngIf = "qualityCL.sign_off_parties.length > 1">
                                <span class=" text-danger cursor-pointer x-large-font material-symbols-outlined" (click)="removeAlertRecipient($event, i)">delete</span>
                            </td>
                        </tr>
                    </ng-template>
                    <tr>
                        <td colspan="2">
                            <button type="button" class="btn btn-sm btn-outline-brandeis-blue btn-hover m-btn-size-lg" id="newBtn" (click)="addUserBlock()">
                                <div class="d-flex align-items-center justify-content-center">
                                    <span class="material-symbols-outlined x-large-font mr-1">
                                        add
                                    </span>
                                    <span class="mr-1">Add Sign-off</span>
                                </div>
                            </button>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </form>
        <block-loader [show]="loading" [showBlockBackdrop]="true" [alwaysInCenter]="true" #modalLoader></block-loader>
</i-modal>
<block-loader [show]="loading" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

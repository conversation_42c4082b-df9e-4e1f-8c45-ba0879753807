import {Component, OnInit, Input, Output, EventEmitter, ViewChild, ElementRef} from '@angular/core';
import { FormControl, FormGroup, NgModel } from '@angular/forms';
import { CreateEmployer, ToastService, UserService } from '@app/core';
import { AssetsUrl, GenericConfirmationModalComponent, IModalComponent } from '@app/shared';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
    selector: 'supply-chain-selector',
    templateUrl: './supply-chain-selector.component.html',
    styleUrls: ["./supply-chain-selector.component.scss"]
})
export class SupplyChainSelectorComponent implements OnInit {

    @Input()
    project_id: number;

    @Input()
    selected_supply_chain_companies: Array<any> = [];

    @Input()
    is_project_supply_chain: boolean = false;

    @Input()
    project_has_supply_chain_companies: boolean = false;

    @Input()
    employer: CreateEmployer = {};

    @Input()
    company_sc_setting: any = {};

    @Input()
    project_country_code: string = 'GB';

    @Output()
    selectionChangeEmmiter: any = new EventEmitter<any>();

    @Output()
    scValidityChange = new EventEmitter<boolean>();

    @ViewChild('viewSelectedSCModalHtml') viewSelectedSCModalRef: IModalComponent;
    @ViewChild('addNewCompanyModalHtml') addNewCompanyModalRef: IModalComponent;
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    @ViewChild('supplyChainCompanies') supplyChainCompaniesRef: NgModel;

    ngSelectElement!: ElementRef;
    @ViewChild('supplyChainField', { read: ElementRef, static: false }) set supplyChainField(element: ElementRef) {
        this.ngSelectElement = element;
        this.calculateVisibleItems();
    }

    get selectedSCValue() {
        return this.selected_supply_chain_companies;
    }

    set selectedSCValue(val: Array<[]>) {
        this.selected_supply_chain_companies = val;
        this.selectionChangeEmmiter.emit(val);
        this.emitValidity();
    }

    ngSelectConcateLimit:number = 3;
    averageItemWidth: number = 150;
    moreTextWidth: number = 100;
    companyIdNameMap: Array<any> = [];
    employers_list: Array<any> = [];
    processLoader: boolean = false;
    disable_company_sc_edit: boolean = false;
    sc_companies_list: Array<any> = [];

    createEmployerForm = new FormGroup({
        employerName: new FormControl(''),
    });

    constructor(
        private modalService: NgbModal,
        private toastService: ToastService,
        private userService: UserService,
    ) { }

    ngOnInit(): void {
        this.getEmployersList();
    }

    getEmployersList(){
        let country_code = (this.is_project_supply_chain) ? this.project_country_code : this.employer.country_code;
        this.processLoader = true;
        this.userService.getEmployer(country_code, false, true).subscribe((data: any) => {
            this.processLoader = false;
            if(data.employerlist){
                this.employers_list = data.employerlist;
                this.sc_companies_list = this.employers_list.filter(e => e.id != this.employer.id);
                this.sc_companies_list.map(c => { this.companyIdNameMap[c.id] = c.name; });
                this.onSelectionChange();
                (this.is_project_supply_chain && this.employer && this.employer.id) && this.checkApplicableSupplyChain();
            } else {
                const message = data.message || 'Failed to get employer data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    sortItems(items: any[]): any[] {
        return items.sort((a, b) => a?.name?.localeCompare(b?.name));
    }

    sortSelectedItems(items: any[]): any[] {
        return items.sort((a, b) => (this.companyIdNameMap[a] || '').localeCompare((this.companyIdNameMap[b] || '')));
    }

    onSelectionChange() {
        // adding supply chain ids into a Set for faster lookup.
        const supplyChainIdsSet = new Set(this.selected_supply_chain_companies);

        // apply sort on sc_companies_list based on whether company id is available in company supply chain.
        this.sc_companies_list.sort((a, b) => {
            const aIsInSet = supplyChainIdsSet.has(a.id);
            const bIsInSet = supplyChainIdsSet.has(b.id);
            return (aIsInSet && !bIsInSet) ? -1 : (!aIsInSet && bIsInSet) ? 1 : 0;
        });
        //remove project contractor from supply chain list while displaying, It will be silently added while saving.
        this.selected_supply_chain_companies = [...this.selected_supply_chain_companies].filter(id => id !== this.employer.id);
    }

    onCompanySearch(term: string, item) {
        return (item.name.toLowerCase().indexOf(term.toLowerCase()) !== -1);
    }

    onFormDataChange($event) {
        this.selectionChangeEmmiter.emit($event);
    }

    calculateVisibleItems() {
        if (!this.ngSelectElement || !this.ngSelectElement.nativeElement) { return; }
        const containerWidth = this.ngSelectElement.nativeElement.querySelector('.ng-select-container')?.clientWidth || 0;
        if (containerWidth > 0) {
            const availableWidth = containerWidth - this.moreTextWidth - 20; // Subtracted assumed padding/margin (~20px)
            const calculatedLimit = Math.max(1, Math.floor(availableWidth / this.averageItemWidth));
            this.ngSelectConcateLimit = calculatedLimit;
        }
    }

    ngAfterViewInit() {
        this.calculateVisibleItems();
        window.addEventListener('resize', () => {
            this.calculateVisibleItems();
        });

        this.supplyChainCompaniesRef.statusChanges?.subscribe(() => {
            this.emitValidity();
        });
    }
    
    private emitValidity() {
        this.scValidityChange.emit(this.isSupplyChainValid());
    }

    isSupplyChainValid() {
        return this.disable_company_sc_edit || (this.selectedSCValue && this.selectedSCValue.length > 0);
    }

    ngOnDestroy() {
        window.removeEventListener('resize', this.calculateVisibleItems);
    }

    openViewSelectedSCModal(): void {
        this.viewSelectedSCModalRef.open();
    }

    removeSelectedSCCompany(companyId: number): void {
        // Filter out the company ID from the company selected supply chain
        this.selected_supply_chain_companies = this.selected_supply_chain_companies.filter(id => id !== companyId);

        // Trigger necessary change detection and form updates
        this.onSelectionChange();
        this.onFormDataChange(this.selected_supply_chain_companies);
    }

    public closeModal(event) {
        event.closeFn();
    }

    openAddCompanyModal() {
        this.createEmployerForm.reset();
        this.addNewCompanyModalRef.open();
    }

    addEmployerToList(form, $modalLoader, event) {
        let country = (this.is_project_supply_chain) ? this.project_country_code : this.employer.country_code;
        if (!form.valid) {
            console.log('Modal form is not valid');
            return;
        }
        $modalLoader.show = true;
        this.userService.createEmployer({
            name: this.createEmployerForm.value.employerName,
            country_code: country
        }).subscribe((result: any) => {
            if (!result.employer) {
                const message = result.message || 'Failed to create record.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: result });
                return;
            }
            event.closeFn();
            $modalLoader.show = false;
            this.getEmployersList();
            this.selected_supply_chain_companies = this.getUniqueIds([result.employer.id, ...(this.selected_supply_chain_companies || [])]);
            this.selectionChangeEmmiter.emit(this.selected_supply_chain_companies);
            // this.detailsUpdated = true;
            this.createEmployerForm.reset();
        }, error => {
            $modalLoader.show = false;
            const message = 'Failed to create record.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: error });
        });
    }

    checkIfEmployerExist(){
        let text = (this.createEmployerForm.value.employerName || '').toString().trim();
        let alreadyRecord = (this.employers_list || []).find((r, i) => r.name.toLowerCase() === text.toLowerCase());
        if(alreadyRecord && alreadyRecord.id){
            this.modalService.dismissAll();
            this.createEmployerForm.reset();
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: "Alert",
                title: "Company has already been added.",
                confirmLabel: "Ok",
                hasCancel: false
            });
        }else{
            this.createEmployerForm.patchValue({
                employerName: text,
            });
        }
    }

    canRemoveCompanyFromSC(companyId) {
        return ((this.disable_company_sc_edit && this.company_sc_setting?.supply_chain.includes(companyId))) ? false : true;
    }

    checkApplicableSupplyChain(setting_from_parent:any = '') {
        this.company_sc_setting = (setting_from_parent !== '') ? setting_from_parent : this.company_sc_setting;
        if(!this.company_sc_setting || !this.company_sc_setting.active_for_all_projects) {
            this.sc_companies_list = this.employers_list.filter(e => e.id !== this.employer.id);
            this.sc_companies_list.map(c => { this.companyIdNameMap[c.id] = c.name; });
            return;
        }

        this.disable_company_sc_edit = (this.company_sc_setting.pre_select && !this.company_sc_setting.allowed_to_edit);
        if(this.project_has_supply_chain_companies === false && this.company_sc_setting.active_for_all_projects) {
            // By default, enable project supply chain if it's activated for all projects of company.
            this.project_has_supply_chain_companies = this.company_sc_setting.active_for_all_projects;
        }

        if(this.company_sc_setting.active_for_all_projects) {
            // If restricted to company supply chain only
            this.sc_companies_list = [
                this.employer,

                // In sc companies list, add any companies which were already present with the project level supply chain setup.
                ...(this.employers_list || []).filter(e => {
                    return this.company_sc_setting.supply_chain.includes(e.id) ||
                        this.selected_supply_chain_companies.includes(e.id);
                })
            ];

        } else { // Else show list of all companies to select from
            this.sc_companies_list = this.employers_list;
        }

        // This will be executed for new project to pre-fill the supply chain, on existing projects it's handled from the BE (replaced supply chain in custom_field.supply_chain_companies).
        if(this.company_sc_setting.pre_select && !this.project_id) {            
            this.selected_supply_chain_companies = this.getUniqueIds([
                ...this.company_sc_setting.supply_chain,
                ...(this.selected_supply_chain_companies || [])
            ]); // Unique supply chain ids.
        }

        if(this.company_sc_setting.pre_select && this.disable_company_sc_edit) {            
            this.sc_companies_list.map((c) => { c.disabled = this.company_sc_setting.supply_chain.includes(c.id); return c; });
        }
        // if it's a project supply chain and it's a new project and disabled supply chain field
        // we need to emit the supply chain companies set from company portal.
        if(this.is_project_supply_chain && !this.project_id && this.disable_company_sc_edit){
            this.selectionChangeEmmiter.emit(this.company_sc_setting.supply_chain);
        }

        // removing employer from list, as they should not be shown on FE.
        this.sc_companies_list = this.sc_companies_list.filter(e => e.id !== this.employer.id);
        this.sc_companies_list.map(c => { this.companyIdNameMap[c.id] = c.name; });
    }

    private getUniqueIds(items = []) {
        return items.filter((elem, index, self) => index === self.indexOf(elem));
    }
}

import { Component, Injectable, Input, OnChanges, OnInit, SimpleChanges, TemplateRef, ViewChild } from '@angular/core'
import { GenericModalConfig } from './generic-modal.config'
import { NgbModal, NgbModalOptions, NgbModalRef } from '@ng-bootstrap/ng-bootstrap'
import {FormDirtyService} from '@app/core';

@Component({
  selector: 'app-generic-modal',
  templateUrl: './generic-modal.component.html',
  styleUrls: ['./generic-modal.component.scss']
})
@Injectable()
export class GenericModalComponent implements OnInit,OnChanges {
  @Input() public genericModalConfig: GenericModalConfig;
  @Input() dynamicText :any = null;
    /**
     * @deprecated, use `<i-modal></i-modal>` instead.
     */
  @ViewChild('genericModal') private modalContent: TemplateRef<GenericModalComponent>
  private genericModalRef: NgbModalRef

  constructor(private modalService: NgbModal, private formDirtyService: FormDirtyService) { }

  ngOnInit(): void { }

  ngOnChanges(changes: SimpleChanges){
  }
  // This function is responsible for opening the modal
    /**
     * @deprecated, use `<i-modal></i-modal>` instead.
     */
  open(): void {
    const defaultNgbModalOptions: NgbModalOptions = {
      backdropClass: 'light-blue-backdrop',
      backdrop: 'static',
      keyboard: true,
      ...this.genericModalConfig.modalOptions
    }

    // Show cancel button by default
    if (this.genericModalConfig.showCancelTrailingBtn === undefined) {
      this.genericModalConfig.showCancelTrailingBtn = true;
    }
    this.genericModalRef = this.modalService.open(this.modalContent, defaultNgbModalOptions)
  }

  clearFormDirtyState(){
    setTimeout(() => {
      if(this.formDirtyService.dirtyForms.size > 0){
        this.formDirtyService.dirtyForms.clear();
      }
    }, 200);
  }

  // This function will called when modal is closed
  close(): void {
    this.clearFormDirtyState();
    const closeModal = this.genericModalConfig.closeTrailingBtnAction ? this.genericModalConfig.closeTrailingBtnAction() : true
    if(!closeModal){
      return;
    }
    const result = this.genericModalConfig.onClose === undefined || (this.genericModalConfig.onClose())
    this.genericModalRef.close(result)
  }

  // This function will called by default at last on click of cancel button
  cancel(): void {
    if (this.genericModalConfig.cancelTrailingBtnAction) {
      this.genericModalConfig.cancelTrailingBtnAction()
    }
    this.close()
  }
}

.modal-content {
    padding: 1.25rem;
    border: none;
    margin-top: 30%;
  
    a {
      color: var(--info);
    }
  
    .modal-header {
      justify-content: center;
      flex-direction: column;
      border: none;
      text-align: center;
    }
  
    .modal-title {
      width: 100%;
      color: black;
    }
  
    .modal-body {
      font-size: 0.875rem;
    }
  
    .modal-footer {
      border-top: none;
    }
  }
  .text-primary {
    color:var(--primary) !important;
  }
  
  ::ng-deep.modal-btn {
    padding-right: 0.8em;
    padding-left: 0.8em;
    border-radius: -3rem !important;
    font-weight: 500;
}

.modal-footer
{
 justify-content:space-between !important;
}

.remove-overflow {
  overflow-y: unset !important
}
.dynamic-text{
    box-shadow: none;
    cursor: inherit !important;
}
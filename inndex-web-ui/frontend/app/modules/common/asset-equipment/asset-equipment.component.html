<div class="mb-2 mx-3 flex-wrap gap-8 d-flex">
    <div class="col-md-5 d-inline-block p-0">
        <h5 class="float-md-left col-md-12 p-0">Total On-site Equipment: <small> ({{onSiteEquipmentCount}}) </small></h5>
    </div>
</div>

<div class="col-md-12 mb-3 p-0">
    <div class="row w-100 p-0 text-center">
        <div class="col-12 col-md-6 tablet-full-width">
            <div class="ml-md-3" *ngIf="taggedOwners.length">
                <search-with-filters [filterData] ='filterData' (searchEmitter)="searchFunction($event)" (filterEmitter)="onFilterSelection($event)"></search-with-filters>
            </div>
        </div>
        <div class="col-12 col-md-6 tablet-full-width m-ml-2 mb-3 px-0 d-flex justify-content-sm-center justify-content-md-end">
            <div class="d-flex w-sm-100 tablet-ml justify-content-sm-center justify-content-md-end align-items-center">
                <span type="button" [ngClass]="{'material-icon-disabled': isListView}" class="material-symbols-outlined mr-2 icon-btn" (click)="setViewType()"> list </span>
                <span type="button" [ngClass]="{'material-icon-disabled': !isListView}" class="material-symbols-outlined mr-2 icon-btn" (click)="setViewType()"> grid_view </span>
                <action-button 
                    [actionList]="actionButtonMetaData.actionList"
                    (selectedActionEmmiter)="onActionSelection($event)"
                    [newFeatureTitle]="'Add Equipment'"
                    (onOpenAddNew)="addEquipmentPopup()">
                </action-button>
            </div>
        </div>
    </div>
</div>

<div class="col-md-12 mb-3 p-0" *ngIf="isListView; else cardView">
    <div class="table-responsive-sm ngx-datatable-custom">
        <ngx-datatable #table class="ngx-datatable bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h fixed-header scroll-vertical"
            [rows]="filteredEquipmentRecords ? filteredEquipmentRecords : []"
            [columns]="[
                {name:'ID', prop:'equipment_id', sortable: false, headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'py-1 table-cell-text'},
                {name:'Type', prop:'equipment_type',sortable: true, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: type},
                {name:'Owner(s)', prop:'tagged_owner', sortable: true, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: taggedOwnerCell},
                {name:'Reg./Serial No.', prop:'serial_number', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: regSerialNumber },
                {name:'Expired Certs', prop:'examination_cert_expiry_date', sortable: true, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: expiredCerts},
                {name:'Last Inspected', prop:'latest_inspection', sortable: true, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: latestInspected},
                {name:'Open Faults', prop:'fault_count', sortable: true, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text'},
                {name:'Status', prop:'', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: listStatus},
                {name:'Action', prop:'', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text no-ellipsis', cellTemplate: listAction}
            ]"
            [columnMode]="'force'"
            [footerHeight]="36"
            [rowHeight]="'auto'"
               [externalPaging]="true"
               [externalSorting]="true"
               [count]="page.totalElements"
               [offset]="page.pageNumber"
               [limit]="page.size"
               (page)="pageCallback($event, false, true)"
               [sortType]="'single'"
               (sort)="sortCallback($event, false)"
               [scrollbarV]="true"
               [virtualization]="false"
               [loadingIndicator]="loadingInlineAssetEquipment"
        >
            <ng-template #type let-row="row" let-column="column">
                <a (click)="viewEquipmentOptionModal(row, 1)" class="text-info" href="javascript:void(0)"><span appTooltip>{{row.type_of_equipment_name}}</span></a>
            </ng-template>
            <ng-template #taggedOwnerCell  let-row="row" let-column="column">
                <div class="text-left">
                    <small><span appTooltip>{{ getTaggedOwners(row.tagged_owner) }}</span></small>
                </div>
            </ng-template>
            <ng-template #regSerialNumber  let-row="row" let-column="column">
                <div class="text-left">
                    <span appTooltip>{{ row.serial_number }}</span>
                </div>
            </ng-template>
            <ng-template #expiredCerts let-value="value" let-row="row" let-column="column">
                {{ row.expired_certs}}
                <i *ngIf="row?.expiry_dates.length"
                    class="fa fa-info-circle"
                    [ngbTooltip]="htmlContent"
                    container="body"
                    aria-hidden="true"></i>
                <ng-template #htmlContent>
                    <ng-container *ngFor="let data of row?.expiry_dates; let i=index">
                        <ng-container *ngIf="data.isExpired === 0; else item_name">
                            <div class="text-center text-white w-100"
                                [ngClass]="data?.isExpired === 0 ? 'background-red' : data?.isExpired === 1 ? 'background-orange' : 'background-green'">
                                <small>{{ data.name | uppercase }} EXPIRED!</small>
                            </div>
                        </ng-container>
                        <ng-template #item_name>
                            <div class="text-center text-white w-100"
                                [ngClass]="data?.isExpired === 0 ? 'background-red' : data?.isExpired === 1 ? 'background-orange' : 'background-green'">
                                <small>{{ data.name }} Expiry: {{ dayjs(data.expiry_date, false) }}</small>
                            </div>
                        </ng-template>
                        <hr *ngIf="i < row?.expiry_dates.length - 1" class="white-line">
                    </ng-container>
                </ng-template>
            </ng-template>
            <ng-template #latestInspected  let-row="row" let-column="column">
                <div class="text-left">
                    {{row.last_inspected}}
                </div>
            </ng-template>
            <ng-template #listStatus  let-row="row" let-column="column">
                <div class="d-flex">
                    <div *ngIf="row.approval_pending === 1; else noAction">
                        <div *ngIf="(!hasOnlySiteManagement)" class="d-inline-block" ngbDropdown #actionDrop="ngbDropdown" container="body">
                            <button class="btn btn-sm font-md-small btn-outline-primary" ngbDropdownAnchor (click)="actionDrop.open()"> Pending </button>
                            <div ngbDropdownMenu>
                                <button class="dropdown-item cursor-pointer" (click)="approveEquipment(row)"> Approve </button>
                                <button class="dropdown-item btn-delete" (click)="declineEquipmentConfirmation(row)"> Decline </button>
                            </div>
                        </div>
                    </div>
                    <ng-template #noAction>
                        <span class="d-flex align-items-center" [ngClass]="{'approved-text': row.approval_pending === 2, 'redText': row.approval_pending === 3}">
                            {{ row.approval_pending_message }}
                        </span>
                    </ng-template>
                </div>
            </ng-template>
            <ng-template #listAction let-row="row" let-column="column">
                <button-group
                    [buttons]="baseButtonConfig"
                    [btnConditions]="[true, false]"
                    (onActionClick)="rowBtnClicked($event, row)">
                </button-group>
            </ng-template>
        </ngx-datatable>
    </div>
</div>

<ng-template #cardView>
<div class="m-0 p-0 card-deck col-sm-12">
    <ngx-skeleton-loader *ngIf="loadingEquipmentRecords || loadingMetaConfig" [count]="(filteredEquipmentRecords.length) || 6" [theme]="{ 'border-radius': '4px', height: '214px', width: '258px', 'margin-left': '20px' }"></ngx-skeleton-loader>
    <ng-template ngFor let-item [ngForOf]="filteredEquipmentRecords" let-i="index" *ngIf="!loadingEquipmentRecords && !loadingMetaConfig">
        <div class="card mb-3">
            <div *ngIf="item.fault_count" class="custom-badge">
                <div class="badge-pill background-red d-flex align-items-center justify-content-center" style="height: 30px;">
                    <button class="text-white btn btn-sm faultBadgeBtn" (click)="viewEquipmentOptionModal(item, 3)">
                        <span class="mr-1"> {{item.fault_count}} Open Faults </span>
                        <i class="fa fa-search" aria-hidden="true"></i>
                    </button>
                </div>
            </div>

            <img *ngIf="!item.equipment_photos.length && !subTypeEquipmentsItems.includes(item.equipment_type)" class="card-img-top project-img pt-1 pb-3 mt-3"
                [src]="'/images/'+item.equipment_type+'.png'"
                alt="Equipment Photo"
            />

            <img *ngIf="!item.equipment_photos.length && item.equipment_type == 'access_equipment'" class="card-img-top project-img pt-1 pb-3 mt-3"
                [src]="'/images/ae_'+replaceAll((item.item).toLowerCase(), ' ', '_')+'.png'"
                alt="Equipment Photo"
            />

            <img *ngIf="!item.equipment_photos.length && item.equipment_type == 'fall_arrest_systems'" class="card-img-top project-img pt-1 pb-3 mt-3"
                 [src]="'/images/fas_'+replaceAll((item.item).toLowerCase(), ' ', '_')+'.png'"
                 alt="Equipment Photo"
            />

            <photo-collage
                *ngIf="item.equipment_photos.length && item.equipment_photos[0].file_url"
                [ngStyle]="{'margin': (item.approval_pending === 1) ? '0px' : '25px 0px 14px 0px'}"
                [photos]="item.equipment_photos"
                [uploadBtnSrc]="'/images/equipment-circle.png'"
                [uploadCategory]="'equipment-photo'"
                [showDeleteFileBtn]="false"
                [collageWidth]="'100px'"
                [collageHeight]="'100px'"
                [addMorePhotos]="false"
                class="mt-1 mb-3"
            >
            </photo-collage>
            <div [ngClass]="{'card-body pt-0 pb-1 text-center': true}">
                <h6 class="card-title mt-2">
                    <ng-container>
                        <a (click)="viewEquipmentOptionModal(item, 1)" *ngIf="subTypeEquipmentsItems.includes(item.equipment_type) else otherEquipmentItemTitle" class="text-info" href="javascript:void(0)">{{(item.equipment_id) ? item.equipment_id+' - ' : '' }} {{ getSubEquipmentAlternatePhrase(item.item) }}
                        </a>
                        <ng-template #otherEquipmentItemTitle>
                            <a (click)="viewEquipmentOptionModal(item, 1)" class="text-info" href="javascript:void(0)">{{(item.equipment_id) ? item.equipment_id+' - ' : '' }} {{item.item}}
                            </a>
                        </ng-template>
                    </ng-container>
                </h6>
                <p class="card-subtitle mb-2 text-muted small">{{getEquipmentType(item.equipment_type) || ''}}</p>
                <h6 *ngIf="!hasOnlySiteManagement" class="card-subtitle mb-2 text-muted small">Owner(s): {{ getTaggedOwners(item.tagged_owner) }}</h6>
                <p class="card-subtitle mb-2 text-muted small">Reg./Serial Number: {{ item.serial_number }}</p>
                <p *ngIf="item.approval_pending === 2" class="card-subtitle mb-2 text-muted small">
                    Last Daily Inspection: {{ item?.lastDailyInspectionAt ? dayjs(item?.lastDailyInspectionAt?.updatedAt, false) : 'N/A' }}
                </p>
            </div>

            <div *ngIf="item.approval_pending === 1" class="text-center m-1">
                <div *ngIf="hasOnlySiteManagement" class="w-100">
                    <div [ngClass]="{ 'border-bottom-none' : dropdownIndex === i }" class="d-inline-block w-100 background-orange border-redias-5 p-0">
                        <button (click)="openCloseDropdown(i)" class="btn btn-sm font-size-small text-white shadow-none w-100 pr-1 pl-1"> Approval Pending
                        </button>
                    </div>
                </div>
                <div *ngIf="!hasOnlySiteManagement" class="w-100">
                    <div [ngClass]="{ 'border-bottom-none' : dropdownIndex === i }" class="d-inline-block w-100 background-black border-redias-5 p-0">
                        <button class="btn btn-sm font-size-small text-white shadow-none w-100 pr-1 pl-1" (click)="openCloseDropdown(i)"> Approval Pending
                            <i class="fa fa-angle-down ml-3" [ngClass]="dropdownIndex === i ? 'fa-angle-up' : 'fa-angle-down'" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div class="dropdown-mask" *ngIf="dropdownIndex === i" (click)="openCloseDropdown(i)"></div>
                    <div class="w-100 dropdownMenu" style="z-index: 9999; padding-right: 8px;">
                        <div class="w-100 bg-white p-0 border-color-black rounded-bottom" *ngIf="dropdownIndex === i">
                            <div class="text-left w-100 pl-2" role="button" (click)="approveEquipment(item)"> <small>Approve</small> </div>
                            <hr class="h-line">
                            <div class="text-left w-100 rounded-bottom pl-2" role="button" (click)="declineEquipmentConfirmation(item)"> <small>Decline</small> </div>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="item.approval_pending === 2 && item?.expiry_dates.length > 0" class="text-center m-1">
                <div class="w-100">
                    <div class="d-inline-block w-100 border-redias-5 p-0"
                         [class.border-bottom-none]="dropdownIndex === i && item?.expiry_dates.length > 1"
                         [ngClass]="item?.expiry_dates[0].isExpired === 0 ? 'background-red' : item?.expiry_dates[0].isExpired === 1 ? 'background-orange' : 'background-green'">
                        <button class="btn btn-sm font-size-small text-white shadow-none w-100 pr-1 pl-1" (click)="openCloseDropdown(i)">
                            <ng-container *ngIf="item?.expiry_dates[0].isExpired === 0; else item_name">
                                <span >{{ item?.expiry_dates[0].name | uppercase }} EXPIRED!</span>
                            </ng-container>
                            <ng-template #item_name>
                                <span>{{ item?.expiry_dates[0].name }} Expiry: {{ dayjs(item?.expiry_dates[0].expiry_date, false) }}</span>
                            </ng-template>
                            <i *ngIf="item?.expiry_dates.length > 1" class="fa fa-angle-down ml-2" [ngClass]="dropdownIndex === i ? 'fa-angle-up' : 'fa-angle-down'" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div class="dropdown-mask" *ngIf="dropdownIndex === i" (click)="openCloseDropdown(i)"></div>
                    <ng-container *ngIf="item?.expiry_dates.length > 1">
                        <div class="w-100 dropdownMenu" style="padding-right: 8px; z-index: 9999;" (click)="openCloseDropdown(i)">
                            <div class="w-100 bg-white p-0" *ngIf="dropdownIndex === i">
                                <ng-container *ngFor="let data of item?.expiry_dates; let i=index">
                                    <ng-container *ngIf="data.isExpired === 0; else item_name">
                                        <div [class.border-bottom-5]="i === item?.expiry_dates.length - 1" class="text-center text-white w-100" role="button" *ngIf="i !== 0" [ngClass]="data?.isExpired === 0 ? 'background-red' : data?.isExpired === 1 ? 'background-orange' : 'background-green'">
                                            <small>{{ data.name | uppercase }} EXPIRED!</small>
                                        </div>
                                    </ng-container>
                                    <ng-template #item_name>
                                        <div [class.border-bottom-5]="i === item?.expiry_dates.length - 1" class="text-center text-white w-100" role="button" *ngIf="i !== 0" [ngClass]="data?.isExpired === 0 ? 'background-red' : data?.isExpired === 1 ? 'background-orange' : 'background-green'">
                                            <small>{{ data.name }} Expiry: {{ dayjs(data.expiry_date, false) }}</small>
                                        </div>
                                    </ng-template>
                                    <hr *ngIf="i < item?.expiry_dates.length - 1" class="white-line">
                                </ng-container>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </div>
        </div>
    </ng-template>
</div>
<div class="col-12 pt-4 d-flex justify-content-end">
    <ngb-pagination  (pageChange)="paginationCallback($event)"
                     [(page)]="page.pageNumber"
                     [pageSize]="page.size"
                     [collectionSize]="page.totalElements"
                     [boundaryLinks]="page.totalElements > page.size"
                     [maxSize]="5"
                     [rotate]="true"
    >
    </ngb-pagination>
</div>
<div class="col-sm-12" *ngIf="!equipmentsLoading && !filteredEquipmentRecords.length && !loadingEquipmentRecords">
    <p class="col-sm text-center" > No equipment found.</p>
</div>
</ng-template>

<block-loader [show]="(equipmentsLoading)" alwaysInCenter="true" zIndex="1200" showBackdrop="true"></block-loader>

<i-modal #addEquipmentModalRef [title]="(!assetEquipment?.id) ? 'Add Equipment' : 'Equipment Information'" size="lg" (onCancel)="closeVehicleModal()" cancelBtnText="Close" (onClickRightPB)="saveEquipment()" 
    [rightPrimaryBtnTxt]="(!assetEquipment?.id) ? 'Add' : 'Update'" [rightPrimaryBtnDisabled]="(!addEquipmentForm.valid)">
        <form novalidate #addEquipmentForm="ngForm" class="form-container">
            <div class="form-group row" *ngIf="!assetTypeSelected">
                <div class="col-sm-12 p-0">
                    <div class="col-sm-3 p-0 pr-1 float-right">
                        <photo-collage
                            *ngIf="showPhotoCollage"
                            [photos]="assetEquipment.equipment_photos"
                            [uploadBtnSrc]="'/images/equipment-circle.png'"
                            [uploadCategory]="'equipment-photo'"
                            (photoUploadDone)="equipmentPhotoCollageUploadDone($event, true)"
                            (photoDeleteDone)="fileDeleteDone($event)"
                        >
                        </photo-collage>
                    </div>
                </div>

                <div class="col-sm-12 p-0">
                    <div class="col-sm-3 mt-2 p-0 float-right" style="position: relative; left: 20px;">
                        <file-uploader-v2
                            [disabled]="false"
                            [chooseFileBtnText]="'+ Add Photos'"
                            [showDragnDrop]="false"
                            [category]="'equipment-photo'"
                            (uploadDone)="equipmentPhotoUploadDone($event)"
                            [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png']"
                            [showFileName]="false"
                            [showThumbnail]="true"
                            [showViewFileModal]="false"
                            [hide_output]="true"
                        >
                        </file-uploader-v2>
                    </div>

                </div>
            </div>

            <div class="form-group row">
                <label>Equipment Type: <small class="required-asterisk ">*</small></label>
                <ng-select [(ngModel)]="assetEquipment.equipment_type" bindLabel="alternate_phrase" bindValue="key" [items]="TYPE_OF_EQUIPMENT" name="equipment_type" class="w-100 dropdown-list" appendTo="body" required ng-value="assetEquipment.equipment_type" (change)="assetTypeChanged()">
                </ng-select>
            </div>

            <div class="form-group row" *ngIf="assetEquipment.equipment_type && (subTypeEquipmentsItems.includes(assetEquipment.equipment_type)) else otherEquipment" @slideUpDown>
                <label>
                    Item: <small class="required-asterisk ">*</small>
                </label>
                <ng-select
                    [items]="subItems"
                    bindLabel="alternate_phrase" bindValue="name"
                    class="w-100 dropdown-list" appendTo="body" required
                    [placeholder]="'Select Item'"
                    name="item"
                    [(ngModel)]="assetEquipment.item" (change)="assetSubTypeChanged()"
                >
                </ng-select>
            </div>
            <ng-template #otherEquipment>
                <div class="form-group row" *ngIf="!assetTypeSelected" @slideUpDown>
                    <label>
                        Item: <small class="required-asterisk ">*</small>
                    </label>
                    <input type="text" class="form-control" required #item="ngModel" [(ngModel)]="assetEquipment.item" name="item"
                           placeholder="Item" />
                    <div class="alert alert-danger" [hidden]="(item.valid)">Item is required.</div>
                </div>
            </ng-template>
            

            <fieldset *ngIf="assetEquipment.equipment_type && !assetTypeSelected">
                <div @slideUpDown>
                    <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.equipment_id?.alwaysDisplay || assetConfig.defaultFields?.equipment_id?.display)">
                        <label>
                            Equipment ID: <small class="required-asterisk " *ngIf="assetConfig.defaultFields?.equipment_id?.required">*</small>
                        </label>
                        <input type="text" class="form-control" required #equipment_id="ngModel" [(ngModel)]="assetEquipment.equipment_id" name="equipment_id"
                            placeholder="Equipment ID" [maxlength]="maxlength"/>
                        <span class="small text-danger mx-1">
                            Characters remaining: {{(maxlength - (assetEquipment.equipment_id || '').length)}}/{{maxlength}}
                        </span>
                        <div class="alert alert-danger" [hidden]="(equipment_id.valid)">Equipment Id is required.</div>
                    </div>
                    
                    <div class="form-group row" *ngIf="assetEquipment.equipment_type && (assetEquipment.equipment_type == 'lifting_equipment') && assetConfig.defaultFields?.safe_working_load?.display">
                        <label>
                            Safe Working Load: <small class="required-asterisk " *ngIf="assetConfig.defaultFields?.safe_working_load?.required">*</small>
                        </label>
                        <input type="text" class="form-control" #safe_working_load="ngModel" [(ngModel)]="assetEquipment.safe_working_load" name="safe_working_load"
                            placeholder="Safe Working Load" [required]="assetConfig.defaultFields?.safe_working_load?.required"/>
                    </div>
                
    
                    <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.equipment_id?.alwaysDisplay || assetConfig.defaultFields?.equipment_id?.display)">
                        <label>
                            Reg./Serial Number: <small class="required-asterisk " *ngIf="assetConfig.defaultFields?.serial_number?.required">*</small>
                        </label>
                        <input type="text" class="form-control" required #serial_number="ngModel" [(ngModel)]="assetEquipment.serial_number" name="serial_number"
                            placeholder="Reg./Serial Number" [required]="assetConfig.defaultFields?.serial_number?.required" [maxlength]="maxlength"/>
                        <span class="small text-danger mx-1">
                            Characters remaining: {{(maxlength - (assetEquipment.serial_number || '').length)}}/{{maxlength}}
                        </span>
                        <div class="alert alert-danger" [hidden]="(serial_number.valid)">Reg./Serial Number is required.</div>
                    </div>
    
                    <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.tagged_owner?.alwaysDisplay || assetConfig.defaultFields?.tagged_owner?.display)">
                        <label>Owner(s): <small class="required-asterisk ">*</small></label>
                        <company-selector-v2
                            [required]="true"
                            [country_code]="project?.custom_field?.country_code"
                            name="tagged_owner"
                            [selectId]="assetEquipment.tagged_owner"
                            placeholder="Select Owner(s)"
                            class="w-100 dropdown-list"
                            [disabled]="hasOnlySiteManagement"
                            [multiple]="true"
                            (selectionChanged)="assetEquipment.tagged_owner = $event.selected"
                            [projectId]="projectId"
                        ></company-selector-v2>
                    </div>
    
                    <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.arrived_at?.alwaysDisplay || assetConfig.defaultFields?.arrived_at?.display)">
                        <label class="col-sm-12 p-0">
                            Arrived on site: <small class="required-asterisk" *ngIf="assetConfig.defaultFields?.arrived_at?.required">*</small>
                        </label>
                        <div class="input-group col-sm-8 p-0">
                            <input #sed="ngbDatepicker" [(ngModel)]="assetEquipment._arrivedAt"
                                class="form-control" name="arrived_at" ng-value="assetEquipment._arrivedAt"
                                ngbDatepicker [placeholder]="displayDateFormat" [required]="assetConfig.defaultFields?.arrived_at?.required"
                                readonly>
                            <div class="input-group-append">
                                <button (click)="sed.toggle()" class="btn btn-outline-secondary calendar" type="button">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </div>
                        </div>
                    </div>
    
                <!-- <div class="form-group row" *ngIf="!assetTypeSelected && assetEquipment.item">
                    <label>
                        Thorough Examination Certificate Number:<small class="required-asterisk" *ngIf="assetConfig.defaultFields?._arrivedAt?.required">*</small>
                    </label>
                    <input type="text" class="form-control" #examination_cert_number="ngModel" [(ngModel)]="assetEquipment.examination_cert_number" name="examination_cert_number"
                           placeholder="Thorough examination certificate number" />
                </div> -->
    
                <!-- <div class="form-group row" *ngIf="hasExaminationNumber()">
                    <label class="col-sm-12 p-0">
                        Thorough Examination Certificate Expiry Date: <small class="required-asterisk ">*</small>
                    </label>
                    <div class="input-group col-sm-8 p-0">
                        <input class="form-control" [placeholder]="displayDateFormat" readonly
                               name="examination_cert_expiry_date" [(ngModel)]="assetEquipment._examinationCertExpiryDate" ngbDatepicker
                               #eced="ngbDatepicker" ng-value="assetEquipment._examinationCertExpiryDate"
                               [minDate]="minDate"
                               required>
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary calendar" (click)="eced.toggle()" type="button">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </div>
                    </div>
                </div> -->
    
                <!-- <div class="form-group row" *ngIf="hasExaminationNumber()">
                    <label class="col-md-7 p-0">
                        Thorough Examination Certificate: <small *ngIf="assetEquipment?._examinationCertExpiryDate" class="required-asterisk ">*</small>
                    </label>
                    <ng-template ngFor let-item [ngForOf]="(assetEquipment.examination_certificates || [])" let-i="index">
                        <div class="col-sm-10 p-0 pl-3">
                            <file-uploader-v2
                                [disabled]="false"
                                [category]="'examination-certificate'"
                                [init]="item"
                                (uploadDone)="examinationCertificateUploadDone($event)"
                                [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']"
                                [showViewFileModal]="item && item.file_url"
                                [fileModalTitle]="'Examination Certificate'"
                                [showFileName]="false"
                                (deleteFileDone)="deleteExaminationCertificateRecord($event)"
                                [showDeleteBtn]="true"
                                [showThumbnail]="false"
                                [chooseFileBtnText]="'+ Add Certificate'"
                            >
                            </file-uploader-v2>
                            <div class="alert alert-danger" *ngIf="assetEquipment?._examinationCertExpiryDate && assetEquipment.examination_certificates.length <= 1">
                                Thorough Examination Certificate is required.
                            </div>
                        </div>
                    </ng-template>
                </div> -->

                    <div *ngIf="assetTypeSelected || (assetConfig.defaultFields?.examination_certification?.alwaysDisplay || assetConfig.defaultFields?.examination_certification?.display)" >
                        <div class="form-group row">
                            <label>
                                Examination Certificate Number: <small class="required-asterisk " *ngIf="assetConfig.defaultFields?.examination_certification?.required && assetConfig.defaultFields?.examination_certification?.document_number_mandatory">*</small>
                            </label>
                            <input type="text" class="form-control" #examination_cert_number="ngModel" [(ngModel)]="assetEquipment.examination_cert_number" name="examination_cert_number"
                                (change)="examinationNumberChanged()" placeholder="Examination certificate number" [required]="assetConfig.defaultFields?.examination_certification?.required && assetConfig.defaultFields?.examination_certification?.document_number_mandatory" [maxlength]="maxlength"/>
                            <span class="small text-danger mx-1">
                                Characters remaining: {{(maxlength - (assetEquipment.examination_cert_number || '').length)}}/{{maxlength}}
                            </span>
                        </div>
        
                        <div class="form-group row" *ngIf="hasExaminationNumber()">
                            <label class="col-sm-12 p-0">
                                Examination Certificate Expiry Date: <small class="required-asterisk ">*</small>
                            </label>
                            <div class="input-group col-sm-8 p-0">
                                <input class="form-control" [placeholder]="displayDateFormat" readonly
                                    (dateSelect)="examinationCertExpDateChanged()" name="examination_cert_expiry_date" [(ngModel)]="assetEquipment._examinationCertExpiryDate" ngbDatepicker
                                    #eced="ngbDatepicker" ng-value="assetEquipment._examinationCertExpiryDate" [minDate]="minDate" [required]="assetConfig.defaultFields?.examination_certification?.expiry_date_mandatory">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary calendar" (click)="eced.toggle()" type="button">
                                        <i class="fa fa-calendar"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
            
                        <div class="form-group row" *ngIf="hasExaminationCertExpDate()">
                            <label class="col-md-6 p-0">
                                Examination Certificate: <small *ngIf="assetConfig.defaultFields?.examination_certification?.attachment_mandatory" class="required-asterisk ">*</small>
                            </label>
                            <ng-template ngFor let-item [ngForOf]="(assetEquipment.examination_certificates || [])" let-i="index">
                                <div class="col-sm-10 p-0 pl-3">
                                    <file-uploader-v2
                                        [disabled]="false"
                                        [category]="'examination-certificate'"
                                        [init]="item"
                                        (uploadDone)="examinationCertificateUploadDone($event)"
                                        [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']"
                                        [showViewFileModal]="item && item.file_url"
                                        [fileModalTitle]="'Examination Certificate'"
                                        [showFileName]="false"
                                        (deleteFileDone)="deleteExaminationCertificateRecord($event)"
                                        [showDeleteBtn]="true"
                                        [showThumbnail]="false"
                                        [chooseFileBtnText]="'+ Add Certificate'">
                                    </file-uploader-v2>
                                    <div class="alert alert-danger" *ngIf="assetEquipment?._examinationCertExpiryDate && assetConfig.defaultFields?.examination_certification?.attachment_mandatory && assetEquipment.examination_certificates.length <= 1">
                                        <input type="hidden" id="certification-attachment-exam" name="certification-attachment-exam" [ngModel]="assetEquipment.examination_certificates.length >=2 ? true: undefined" required>
                                        Examination Certificate is required.
                                    </div>
                                </div>
                            </ng-template>
                        </div>
                    </div>

                    <div class="form-group row" *ngIf="assetConfig.defaultFields?.pat_test_expiry_date?.display && (assetEquipment.equipment_type == 'electrical_equipment' || assetEquipment.equipment_type == 'monitoring_equipment' || assetEquipment.equipment_type == 'engineer_equipment')">
                        <label class="col-sm-12 p-0">
                            PAT Test:
                        </label>
                        <div class="input-group col-sm-8 p-0">
                            <input class="form-control" [placeholder]="displayDateFormat" readonly
                                name="pat_test_expiry_date" [(ngModel)]="assetEquipment._patTestExpiryDate" ngbDatepicker
                                #pted="ngbDatepicker" ng-value="assetEquipment._patTestExpiryDate"
                                [minDate]="minDate" [required]="assetConfig.defaultFields?.pat_test_expiry_date?.required">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary calendar" (click)="pted.toggle()" type="button">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <ng-container *ngFor="let field of assetConfig.custom_fields;let i = index; trackBy: trackByKey">
                        <div class="form-group row" *ngIf="field.type === assetCustomConfigFieldTypes.Text">
                            <label> {{field.title}} <small class="required-asterisk" *ngIf="field.required">*</small>
                            </label>
                            <input type="text" class="form-control" #item="ngModel" [(ngModel)]="assetEquipment.custom_fields[i].value" [name]="field.title" placeholder="Enter text" [required]="field.required" [maxlength]="maxlength"/>
                            <span class="small text-danger mx-1">
                                Characters remaining: {{(maxlength - (assetEquipment.custom_fields[i].value || '').length)}}/{{maxlength}}
                            </span>
                            <!-- <div class="alert alert-danger" [hidden]="(item.valid)">Item is required.</div> -->
                        </div>
                        <div class="form-group row" *ngIf="field.type === assetCustomConfigFieldTypes.Dropdown">
                            <label> {{field.title}}: <small class="required-asterisk" *ngIf="field.required">*</small>
                            </label>
                            <ng-select class="w-100 dropdown-list" appendTo="body" [(ngModel)]="assetEquipment.custom_fields[i].value" placeholder="Select option" [items]="field.options" [name]="field.title" [required]="field.required">
                            </ng-select>
                        </div>
                        <div class="form-group row" *ngIf="field.type === assetCustomConfigFieldTypes.Date">
                            <label class="col-sm-12 p-0">
                                {{field.title}}: <small class="required-asterisk" *ngIf="field.required">*</small>
                            </label>
                            <div class="input-group col-sm-8 p-0">
                                <input #aa="ngbDatepicker" [(ngModel)]="assetEquipment.custom_fields[i].value" class="form-control" [name]="field.title" ngbDatepicker [placeholder]="displayDateFormat" [required]="field.required" readonly>
                                <div class="input-group-append">
                                    <button (click)="aa.toggle()" class="btn btn-outline-secondary calendar" type="button">
                                        <i class="fa fa-calendar"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <ng-container *ngIf="field.type === assetCustomConfigFieldTypes.Certification">

                            <div class="form-group row">
                                <label class="col-sm-12 p-0">
                                    {{field.title}} Expiry Date: <small class="required-asterisk" *ngIf="field.required">*</small>
                                </label>
                                <div class="input-group col-sm-8 p-0">
                                    <input class="form-control" [placeholder]="displayDateFormat" readonly
                                            name="expiry_date_{{i}}" [(ngModel)]="assetEquipment.custom_fields[i]._examinationCertExpiryDate" ngbDatepicker
                                            #eced="ngbDatepicker" ng-value="assetEquipment._examinationCertExpiryDate"
                                            [minDate]="minDate"
                                            [required]="field.required">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary calendar" (click)="eced.toggle()" type="button">
                                            <i class="fa fa-calendar"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row" *ngIf="assetEquipment.custom_fields[i]._examinationCertExpiryDate">
                                <label>
                                    {{field.title}} Certificate Number: <small class="required-asterisk" *ngIf="field.required && field.document_number_mandatory">*</small>
                                </label>
                                <input type="text" class="form-control" [(ngModel)]="assetEquipment.custom_fields[i].certificate_number" [name]="field.title+'_number'"
                                        placeholder="Enter certificate number" [required]="field.required && field.document_number_mandatory" [maxlength]="maxlength"/>
                                <span class="small text-danger mx-1">
                                    Characters remaining: {{(maxlength - (assetEquipment.custom_fields[i].certificate_number || '').length)}}/{{maxlength}}
                                </span>
                            </div>

                            <div class="form-group row" *ngIf="assetEquipment.custom_fields[i]._examinationCertExpiryDate">
                                <label class="col-md-7 p-0">
                                    {{field.title}} Attachment: <small *ngIf="field.attachment_mandatory" class="required-asterisk ">*</small>
                                </label>
                                <ng-template ngFor let-item [ngForOf]="(assetEquipment.custom_fields[i].certificates || [])" let-j="index">
                                    <div class="col-sm-10 p-0 pl-3">
                                        <file-uploader-v2
                                            [disabled]="false"
                                            [category]="'custom-certificate'"
                                            [init]="item"
                                            (uploadDone)="certificateUploadDone($event, i)"
                                            [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']"
                                            [showViewFileModal]="item && item.file_url"
                                            [fileModalTitle]="field.title + ' Certificate'"
                                            [showFileName]="false"
                                            (deleteFileDone)="deleteCertificateRecord($event, i)"
                                            [showDeleteBtn]="true"
                                            [showThumbnail]="false"
                                            [chooseFileBtnText]="'+ Add Certificate'"
                                        >
                                        </file-uploader-v2>
                                        <div class="alert alert-danger" *ngIf="field.required && field.attachment_mandatory && assetEquipment.custom_fields[i].certificates.length <= 1">
                                            <input type="text" class="d-none" name="certification-attachment-{{i}}" [ngModel]="assetEquipment.custom_fields[i].certificates.length >=2 ? true: undefined" required>
                                            {{field.title}} attachment is required.
                                        </div>
                                    </div>
                                </ng-template>
                            </div>
                
                        </ng-container>
                        

                    </ng-container>
                </div>
            </fieldset>

            
            
        </form>
</i-modal>

<i-modal #viewFileRef [title]="fileModalTitle" size="lg" cancelBtnText="Close" (onCancel)="closePdfImgModal()" (onClickRightSB)="closePdfImgModal($event)">
    <div class="form-group row" *ngIf="showPdfImg">
        <div class="col-sm-12 mt-2 text-center">
            <div *ngIf="!isPdfDocument" style="width: 600px; display:inline-block;" class="d-inline-block">
                <img [src]="certificateUrl" style="width: 100%; height: auto;" #img />
            </div>

            <iframe *ngIf="certificateUrl && isPdfDocument" class="border-0" [src]="certificatePreviewURL" width="750px" height="500px">
            </iframe>
        </div>
    </div>
</i-modal>

<i-modal #viewEquipmentOptionsRef [title]="(assetEquipment?.equipment_id) ? assetEquipment?.equipment_id + ' - ' + getEquipmentType(assetEquipment?.equipment_type) + '(' + assetEquipment?.serial_number + ')' : ''"
         size="lg" [showCancel]="false" [rightPrimaryBtnTxt]="(assetEquipment.id && !assetEquipment.is_archived) ? 'Archive' : 'Unarchive'" [rightSecondaryBtnTxt]="(activeNav === 1) ? 'Edit' : ''"
         (onClickRightPB)="(assetEquipment.id && !assetEquipment.is_archived) ?  archiveEquipmentAsset(assetEquipment.id) : unArchiveEquipmentAsset(assetEquipment.id, false, true)"
         (onClickRightSB)="getEquipment(equipmentId, true, false)">
        <ul ngbNav #nav="ngbNav" [(activeId)]="activeNav" class="nav-tabs n-tab">
            <li [ngbNavItem]="1">
                <a ngbNavLink class="nav-a" (click)="toggler(1)">Details</a>
                <ng-template ngbNavContent>
                    <div class="">
                        <div class="col-sm-12 p-0">
                            <div class="w-100" style="height: 7.5rem;" *ngIf="assetEquipment.equipment_photos && assetEquipment.equipment_photos.length && assetEquipment.equipment_photos[0].file_url">
                                <div style="position: absolute; right: 0px;">
                                    <photo-collage
                                        [ngStyle]="{'margin': (assetEquipment.approval_pending === 1) ? '0px' : '25px 0px 14px 0px'}"
                                        [photos]="assetEquipment.equipment_photos"
                                        [uploadBtnSrc]="'/images/equipment-circle.png'"
                                        [uploadCategory]="'equipment-photo'"
                                        [showDeleteFileBtn]="false"
                                        [collageWidth]="'100px'"
                                        [collageHeight]="'100px'"
                                        [addMorePhotos]="false"
                                    >
                                    </photo-collage>
                                </div>
                            </div>
                        </div>
                        <table class="table table-sm table-bordered">
                            <tbody>
                            <tr *ngIf="assetEquipment?.equipment_id">
                                <td width="30%" class="tr-bg-dark-color"> <strong>Equipment ID:</strong> </td>
                                <td colspan="3"> {{ assetEquipment.equipment_id }} </td>
                            </tr>
                            <tr *ngIf="assetEquipment?.equipment_type">
                                <td width="30%" class="tr-bg-dark-color"> <strong>Equipment Type:</strong> </td>
                                <td colspan="3"> {{ getEquipmentType(assetEquipment.equipment_type) }} </td>
                            </tr>
                            <tr *ngIf="assetEquipment?.safe_working_load">
                                <td width="30%" class="tr-bg-dark-color"> <strong>Safe Working Load:</strong> </td>
                                <td colspan="3"> {{ assetEquipment.safe_working_load }} </td>
                            </tr>
                            <tr *ngIf="assetEquipment?.item">
                                <td width="30%" class="tr-bg-dark-color"> <strong>Item:</strong> </td>
                                <td colspan="3" *ngIf="subTypeEquipmentsItems.includes(assetEquipment.equipment_type) else otherEquipmentItems" > {{ getSubEquipmentAlternatePhrase(assetEquipment.item) }} </td>
                                <ng-template #otherEquipmentItems>
                                    <td colspan="3"> {{ assetEquipment.item }} </td>
                                </ng-template>
                                

                            </tr>
                            <tr *ngIf="assetEquipment?.serial_number">
                                <td width="30%" class="tr-bg-dark-color"> <strong>Reg./Serial Number:</strong> </td>
                                <td colspan="3"> {{ assetEquipment.serial_number }} </td>
                            </tr>
                            <tr *ngIf="assetEquipment?.arrived_at">
                                <td class="tr-bg-dark-color" width="30%"> <strong>Arrived on site:</strong> </td>
                                <td colspan="3"> {{ dayjs(assetEquipment.arrived_at, false) }} </td>
                            </tr>
                            <tr *ngIf="!hasOnlySiteManagement && assetEquipment.tagged_owner.length">
                                <td width="30%" class="tr-bg-dark-color"> <strong>Owner(s):</strong> </td>
                                <td colspan="3"> {{ getTaggedOwners(assetEquipment.tagged_owner) }} </td>
                            </tr>
                            <tr *ngIf="assetEquipment.examination_cert_number || assetEquipment.examination_cert_expiry_date || assetEquipment?.examination_certificates?.length - 1">
                                <td width="30%" class="tr-bg-dark-color"> <strong>Examination Certificate:</strong> </td>
                                <td width="17.5%" class="p-0">
                                    <div class="w-100 tr-bg-dark-color custom-padding">Document No.</div>
                                    <div class="w-100 custom-padding">{{ assetEquipment.examination_cert_number }}</div>
                                </td>
                                <td width="17.5%" class="p-0">
                                    <div class="w-100 tr-bg-dark-color custom-padding">Expiry Date</div>
                                    <div class="w-100 custom-padding">{{ dayjs(assetEquipment.examination_cert_expiry_date, false) }}</div>
                                </td>
                                <td width="35%" class="p-0">
                                    <div class="w-100 tr-bg-dark-color custom-padding">Attachments</div>
                                    <div class="w-100 custom-padding">
                                        <p class="upload-name mb-0" *ngFor="let cert of assetEquipment?.examination_certificates">
                                            <a href="javascript:void(0)" (click)="viewFileModal(cert?.file_url, 'Examination Certificate')">{{ cert?.name }}</a>
                                        </p>
                                    </div>
                                </td>
                            </tr>
                            <tr *ngIf="assetEquipment.equipment_type == 'electrical_equipment' || assetEquipment.equipment_type == 'monitoring_equipment' || assetEquipment.equipment_type == 'engineer_equipment'">
                                <td width="30%" class="tr-bg-dark-color"> <strong>PAT Test:</strong></td>
                                <td width="17.5%" class="p-0">
                                    <div class="w-100 custom-padding">N/A</div>
                                </td>
                                <td width="17.5%" class="p-0">
                                    <div class="w-100 custom-padding">{{ dayjs(assetEquipment.pat_test_expiry_date, false) }}</div>
                                </td>
                                <td width="35%" class="p-0">
                                    <div class="w-100 custom-padding">N/A</div>
                                </td>
                            </tr>
                            <ng-container *ngFor="let field of assetEquipment.custom_fields;let i = index; trackBy: trackByKey">
                                <tr *ngIf="field.type === assetCustomConfigFieldTypes.Text  || field.type === assetCustomConfigFieldTypes.Dropdown">
                                    <td class="tr-bg-dark-color" width="30%"> <strong>{{field.title}}:</strong> </td>
                                    <td colspan="3"> {{ field.value }} </td>
                                </tr>
                                <tr *ngIf="field.type === assetCustomConfigFieldTypes.Date">
                                    <td class="tr-bg-dark-color" width="30%"> <strong>{{field.title}}:</strong> </td>
                                    <td colspan="3"> {{ dayjsFormat(field.value, false) }} </td>
                                </tr>
                                <tr *ngIf="field.type === assetCustomConfigFieldTypes.Certification">
                                    <td width="30%" class="tr-bg-dark-color"> <strong>{{field.title}}:</strong> </td>
                                    <td width="17.5%" class="p-0">
                                        <div class="w-100 tr-bg-dark-color custom-padding">Document No.</div>
                                        <div class="w-100 custom-padding">{{ field.certificate_number }}</div>
                                    </td>
                                    <td width="17.5%" class="p-0">
                                        <div class="w-100 tr-bg-dark-color custom-padding">Expiry Date</div>
                                        <div class="w-100 custom-padding" *ngIf="field.expiry_date">{{ dayjsFormat(field.expiry_date, false) }}</div>
                                    </td>
                                    <td width="35%" class="p-0">
                                        <div class="w-100 tr-bg-dark-color custom-padding">Attachments</div>
                                        <div class="w-100 custom-padding">
                                            <p class="upload-name mb-0" *ngFor="let cert of field.certificates">
                                                <a href="javascript:void(0)" (click)="viewFileModal(cert?.file_url, 'Examination Certificate')">{{ cert?.name }}</a>
                                            </p>
                                        </div>
                                    </td>
                                </tr>
                            </ng-container>
                            </tbody>
                        </table>
                    </div>
                </ng-template>
            </li>
            <li *ngIf="assetEquipment.equipment_type == 'access_equipment'" [ngbNavItem]="2">
                <a ngbNavLink class="nav-a" (click)="toggler(2)">Inspections</a>
                <ng-template ngbNavContent>
                    <div class="overflow-auto">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th class="tr-bg-dark-color vertical-align-middle p-1 px-2 date-col-width"> Week Commencing </th>
                                <th class="tr-bg-dark-color vertical-align-middle p-1 px-2 td-col-width" *ngFor="let day of week_Days"> {{ day }} </th>
                                <th class="tr-bg-dark-color vertical-align-middle p-1 px-2"> Actions </th>
                            </tr>
                            </thead>
                            <tbody>
                            <ng-container *ngIf="hasInspections">
                                <tr *ngFor="let row of assetEquipment?.availableMondays; let i=index">
                                    <td class="vertical-align-middle p-1 px-2"> {{row?.date}} </td>
                                    <td class="vertical-align-middle p-1 px-2 text-center" *ngFor="let day of week_Days">
                                        <ng-container *ngIf="dayIndex(row.weekdays, day) !== -1">
                                            <i class="fa fa-check-circle text-success" aria-hidden="true" [ngbTooltip]="' Daily inspection was carried out by '&nbsp;+row?.weekdays[dayIndex(row.weekdays, day)]?.user_name+' on '&nbsp;+row?.weekdays[dayIndex(row.weekdays, day)]?.createdAt"></i>
                                        </ng-container>
                                    </td>
                                    <td class="vertical-align-middle p-1 px-2">
                                        <button-group
                                            [buttons]="weeklyInspectionButtonConfig"
                                            (onActionClick)="rowBtnClicked($event, row)">
                                        </button-group>
                                    </td>
                                </tr>
                            </ng-container>
                            <tr *ngIf="!hasInspections">
                                <td colspan="9" class="text-center"> No inspections found </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </ng-template>
            </li>
            <li *ngIf="assetEquipment.equipment_type != 'access_equipment'" [ngbNavItem]="2">
                <a ngbNavLink class="nav-a" (click)="toggler(2)">Inspections</a>
                <ng-template ngbNavContent>
                    <div class="overflow-auto">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th class="tr-bg-dark-color vertical-align-middle p-1 px-2 date-col-width"> Week Commencing </th>
                                <th class="tr-bg-dark-color vertical-align-middle p-1 px-2 td-col-width" *ngFor="let day of week_Days"> {{ day }} </th>
                                <th class="tr-bg-dark-color vertical-align-middle p-1 px-2"> Actions </th>
                            </tr>
                            </thead>
                            <tbody>
                            <ng-container *ngIf="hasInspections">
                                <tr *ngFor="let row of assetEquipment?.availableMondays; let i=index">
                                    <td class="vertical-align-middle p-1 px-2"> {{row?.date}} </td>
                                    <td class="vertical-align-middle p-1 px-2 text-center" *ngFor="let day of week_Days">
                                        <ng-container *ngIf="dayIndex(row.weekdays, day) !== -1">
                                            <i class="fa fa-check-circle text-success" aria-hidden="true" [ngbTooltip]="' Daily inspection was carried out by '&nbsp;+row?.weekdays[dayIndex(row.weekdays, day)]?.user_name+' on '&nbsp;+row?.weekdays[dayIndex(row.weekdays, day)]?.createdAt"></i>
                                        </ng-container>
                                    </td>
                                    <td class="vertical-align-middle p-1 px-2">
                                        <button-group
                                            [buttons]="weeklyInspectionButtonConfig"
                                            (onActionClick)="rowBtnClicked($event, row)">
                                        </button-group>
                                    </td>
                                </tr>
                            </ng-container>
                            <tr *ngIf="!hasInspections">
                                <td colspan="9" class="text-center"> No inspections found </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </ng-template>
            </li>
            <li [ngbNavItem]="3">
                <a ngbNavLink class="nav-a" (click)="toggler(3)">Faults</a>
                <ng-template ngbNavContent>
                    <div class="overflow-hidden">
                        <table class="table table-sm table-bordered">
                            <thead>
                            <tr>
                                <th class="tr-bg-dark-color vertical-align-middle">Fault No.</th>
                                <th class="tr-bg-dark-color vertical-align-middle">Reported</th>
                                <th class="tr-bg-dark-color vertical-align-middle">Reported By</th>
                                <th class="tr-bg-dark-color vertical-align-middle">Fault Details</th>
                                <th class="tr-bg-dark-color vertical-align-middle">Status</th>
                                <th class="tr-bg-dark-color vertical-align-middle">Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr *ngIf="!equipmentFaults.length else faultTable">
                                <td colspan="9" class="text-center"> No faults found</td>
                            </tr>
                            <ng-template #faultTable>
                                <ng-container *ngFor="let fault of equipmentFaults; let i=index">
                                    <tr [ngStyle]="{'background-color': (i % 2) ? 'none' : 'rgba(0, 0, 0, 0.05)'}">
                                        <td [rowSpan]="getRowSpan(fault)">{{fault.fault_id}}</td>
                                        <td>
                                            <span *ngIf="fault.date_reported">{{ dayjs(fault.date_reported) }}</span>
                                        </td>
                                        <td>
                                            <span *ngIf="fault.reported_by">{{ fault.reported_by }}</span>
                                        </td>
                                        <td>
                                            <span *ngIf="fault.fault">{{ fault.fault}}</span>
                                        </td>

                                        <td>
                                            <span *ngIf="fault.status" [ngClass]="{'redText': (fault.status == 'open'), 'greenText': (fault.status == 'closed')}">
                                                {{ (fault.status || '').toUpperCase() }}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <button-group
                                                [buttons]="faultActionButtonConfig"
                                                (onActionClick)="rowBtnClicked($event, fault)">
                                            </button-group>
                                        </td>
                                    </tr>
                                    <tr *ngIf="fault.images && fault.images.length" [ngStyle]="{'background-color': (i % 2) ? 'none' : 'rgba(0, 0, 0, 0.05)'}">
                                        <td colspan="5">
                                            <pop-up-image-viewer [alt]="'Image'" [imgArray]="fault.images || []"></pop-up-image-viewer>
                                        </td>
                                    </tr>
                                    <tr *ngIf="fault.closedout_at" [ngStyle]="{'background-color': (i % 2) ? 'none' : 'rgba(0, 0, 0, 0.05)'}">
                                        <td colspan="5" class="p-0">
                                            <table class="table table-bordered mb-0">
                                                <thead>
                                                <td class="tr-bg-dark-color vertical-align-middle p-1 px-2">Closed Out</td>
                                                <td class="tr-bg-dark-color vertical-align-middle p-1 px-2">Closeout By</td>
                                                <td class="tr-bg-dark-color vertical-align-middle p-1 px-2">Closeout Details</td>
                                                </thead>
                                                <tbody>
                                                <td class="vertical-align-middle p-1 px-2">
                                                    <span *ngIf="fault.closedout_at">{{ dayjs(fault.closedout_at) }}</span>
                                                </td>
                                                <td class="vertical-align-middle p-1 px-2">
                                                    <span *ngIf="fault.closedout_by">{{ fault.closedout_by }}</span>
                                                </td>
                                                <td class="vertical-align-middle p-1 px-2">
                                                    <span *ngIf="fault.closedout_details">{{ fault.closedout_details }}</span>
                                                </td>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr *ngIf="fault.closedout_at && fault.closedout_images && fault.closedout_images.length" [ngStyle]="{'background-color': (i % 2) ? 'none' : 'rgba(0, 0, 0, 0.05)'}">
                                        <td colspan="5">
                                            <pop-up-image-viewer [alt]="'Image'" [imgArray]="fault.closedout_images || []"></pop-up-image-viewer>
                                        </td>
                                    </tr>
                                </ng-container>
                            </ng-template>
                            </tbody>
                        </table>
                    </div>
                </ng-template>
            </li>
            <li [ngbNavItem]="4">
                <a ngbNavLink class="nav-a" (click)="toggler(4)">QR Code</a>
                <ng-template ngbNavContent>
                    <qrcode-generator [qrData]="getQRCodeString(assetEquipment)" [fileName]="getFileName(assetEquipment)" elementType="canvas"></qrcode-generator>
                </ng-template>
            </li>
        </ul>
        <div [ngbNavOutlet]="nav" class="col-sm-12 my-2 pt-2 nav-panel"></div>
        <div *ngIf="assetActivityLogs && assetActivityLogs?.length && activeNav === 1" class="materialRow table-sm">
            <p class="mb-1"><strong>Activity Log:</strong></p>
            <table class="table table-sm table-bordered mb-0">
                <thead>
                <tr>
                    <th class="tr-bg-dark-color"><strong>Date & Time</strong> </th>
                    <th class="tr-bg-dark-color"><strong>Activity</strong> </th>
                    <th class="tr-bg-dark-color"><strong>Activity By</strong></th>
                </tr>
                </thead>
                <tbody>
                <ng-container *ngFor="let t of assetActivityLogs">
                    <tr *ngIf="t.timestamp">
                        <td>{{ dayjs(t.timestamp) }}</td>
                        <td>{{ t.type }}</td>
                        <td>{{ t.name }}</td>
                    </tr>
                </ng-container>
                </tbody>
            </table>
        </div>
</i-modal>

<ng-template #dailyInspectionOptionsHtml let-c="close" let-d="dismiss">
    <div class="modal-header">
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body text-center">
        <div class="card-body">
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th class="vertical-align-middle">
                        Week Commencing
                    </th>
                    <th class="vertical-align-middle">
                        Actions
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let row of assetEquipment?.availableMondays">
                    <td class="vertical-align-middle">{{row?.date}}</td>
                    <td class="vertical-align-middle">
                        <button-group
                            [buttons]="weeklyInspectionButtonConfig"
                            (onActionClick)="rowBtnClicked($event, row)">
                        </button-group>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</ng-template>

<i-modal #archivedEquipmentListRef [title]="('Total Archived Equipment (' + archivedEquipmentRecords.length + ')')" size="xl"
    [showCancel]="false" [showFooter]="false" (onCancel)="closeArchiveModal()">
        <div class="card-body">
            <div class="col-12" *ngIf="archivedTaggedOwners.length || archivedEquipmentRecords.length">
                <search-with-filters #searchComponentRef [filterData] ='archiveFilterData' (searchEmitter)="searchFunction($event, true)" (filterEmitter)="onFilterSelection($event, true)"></search-with-filters>
            </div>
            <div *ngIf="showModal && archivedEquipmentRecords.length else notFound" class="table-responsive-sm pl-3 pr-3 min-h-250">
                <ngx-datatable #archiveTable class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h asset-archive-table"
                               [rows]="archivedEquipmentRecords"
                               [columns]="[
                                   {name:'ID', prop:'equipment_id', sortable: false, headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'py-1 table-cell-text'},
                                   {name:'Reg./Serial No.', prop:'serial_number', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: serialNo},
                                   {name:'Owner(s)', prop:'tagged_owner', sortable: true, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: taggedOwnerCell},
                                   {name:'Type of equipment', prop:'equipment_type', sortable: true, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text',cellTemplate: type},
                                   {name:'Arrived on site', prop:'fault_count', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: arrivedOnSite},
                                   {name:'Status', prop:'', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: listStatus},
                                   {name:'Action', prop:'', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text no-ellipsis', cellTemplate: action}]"
                               [limit]="30"
                               [footerHeight]="36"
                               [columnMode]="'force'"
                               [rowHeight]="'auto'"
                               [externalPaging]="true"
                               [externalSorting]="true"
                               [count]="archivedPage.totalElements"
                               [offset]="archivedPage.pageNumber"
                               [limit]="archivedPage.size"
                               (page)="pageCallback($event, true, true)"
                               [sortType]="'single'"
                               (sort)="sortCallback($event, true)"
                               [scrollbarV]="true"
                               [virtualization]="false"
                               [loadingIndicator]="loadingInlineArchivedAssetEquipment"
                >
                    <ng-template #serialNo let-row="row" let-column="column">
                        <span appTooltip>{{row.serial_number}}</span>
                    </ng-template>
                    <ng-template #type let-row="row" let-column="column">
                        <span appTooltip>{{ getEquipmentType(row.equipment_type) }}</span>
                    </ng-template>
                    <ng-template #taggedOwnerCell  let-row="row" let-column="column">
                        <div class="text-left">
                            <small><span appTooltip>{{ getTaggedOwners(row.tagged_owner) }}</span></small>
                        </div>
                    </ng-template>
                    <ng-template #arrivedOnSite  let-row="row" let-column="column">
                        <div class="text-left">
                            <small>{{ row.arrived_at ? dayjsFormat(row.arrived_at,false) : ''}}</small>
                        </div>
                    </ng-template>
                    <ng-template #listStatus  let-row="row" let-column="column">
                        <div class="d-flex">
                            <span class="d-flex align-items-center" [ngClass]="{'text-warning': row.approval_pending === 1, 'approved-text': row.approval_pending === 2, 'redText': row.approval_pending === 3}" >{{row.approval_pending_message}}</span>
                        </div>
                    </ng-template>
                    <ng-template #action  let-row="row" let-column="column">
                        <button-group
                            [buttons]="baseButtonConfig"
                            (onActionClick)="rowBtnClicked($event, row)">
                        </button-group>
                    </ng-template>
                </ngx-datatable>
            </div>
            <ng-template #notFound>
                <div class="min-h-250 d-flex align-items-center">
                    <p class="col-sm text-center" > No equipments found.</p>
                </div>
            </ng-template>
        </div>
</i-modal>

<block-loader [show]="(downloadingWeeklyInspection)" alwaysInCenter="true" zIndex="1200" showBackdrop="true"></block-loader>

<users-selector-modal
    [users_list]="project_admins"
    [selectedUsers]="equipment_managers"
    [title]="'Equipment Managers'"
    [heading]="'Equipment managers will be notified when new equipments are added or documents are expiring'"
    (selectedUserIds)="saveEquipmentManagers($event)"
    #usersSelectorModal
>
</users-selector-modal>

<fault-closeout
    #faultCloseOutRef
    [faultCloseOutReq]="faultCloseOutReq"
    (onCloseOut)="requestCloseOut($event)">
</fault-closeout>

<asset-decline
    #assetDeclineRef
    [declined_comment]="declined_comment"
    (onDecline)="declineEquipment($event)">
</asset-decline>

<block-loader [alwaysInCenter]="true" [show]="blockLoader" [showBackdrop]="true"></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

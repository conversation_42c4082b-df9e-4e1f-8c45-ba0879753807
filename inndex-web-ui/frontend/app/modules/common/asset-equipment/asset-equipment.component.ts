import {Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild} from "@angular/core";
import {ActivatedRoute, Router} from "@angular/router";
import {NgbDateStruct, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {
    AuthService,
    Project,
    ProjectAssetEquipment,
    ProjectAssetService,
    ProjectService,
    User,
    UserService,
    ResourceService,
    UACProjectDesignations,
    AssetActivityLog,
    Common,
    ToastService,
    AssetManagementConfModalType,
    AssetEquipmentActionButtons,
    ActionButtonVal,
    CompanyAssetConfigService,
    AssetCustomConfig,
    AssetCustomField,
    AssetCustomConfigFieldTypes,
} from "@app/core";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import {DomSanitizer} from "@angular/platform-browser";
import {AppConstant} from "@env/environment";
import * as dayjs from 'dayjs';
import {SearchWithFiltersComponent} from '../search-with-filters/search-with-filters.component';
import {UsersSelectorModalComponent} from "../users-selector-modal/users-selector-modal.component";
import { filterData } from "@app/core";
import { FaultCloseoutComponent } from "../fault-closeout/fault-closeout.component";
import { AssetDeclineComponent } from "../asset-decline/asset-decline.component";
import {ActionBtnEntry, AssetsUrl, GenericConfirmationModalComponent, IModalComponent} from "@app/shared";
import {HttpParams} from '@angular/common/http';
import {DatatableComponent} from '@swimlane/ngx-datatable';
import {SLIDE_UP_DOWN_ON_SHOW_HIDE} from "@app/core/animations";

@Component({
    animations: [SLIDE_UP_DOWN_ON_SHOW_HIDE],
    templateUrl: './asset-equipment.component.html',
    selector: 'asset-equipment',
})
export class AssetEquipmentComponent implements OnInit {

    @Input()
    projectId: number = 0;

    @Input()
    employerId: number = 0;

    @Input()
    project: any;

    @Input()
    isProjectPortal: boolean = false

    @Output()
    isChildComponentLoaded: any = new EventEmitter<any>();

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    @ViewChild('searchComponentRef') searchComponentRef: SearchWithFiltersComponent;
    @ViewChild('faultCloseOutRef') faultCloseOutRef: FaultCloseoutComponent;
    @ViewChild('assetDeclineRef') assetDeclineRef: AssetDeclineComponent;
    
    authUser$: User;
    employer: any = {};
    dayjs(x:number, fullDate:boolean = true){
        return (+x) ? dayjs(+x).format(fullDate ? AppConstant.fullDateTimeFormat : AppConstant.defaultDateFormat) : '';
    }
    allowedMime: Array<any> = ['image/jpeg', 'image/jpg', 'image/png'];
    addEquipmentModal: any = null;
    assetEquipment: ProjectAssetEquipment = new ProjectAssetEquipment;
    TYPE_OF_EQUIPMENT: Array<any> = [];
    examinationCertPreviewURL: any;
    equipmentRecords: Array<any> = [];
    archivedEquipmentRecords: Array<any> = [];
    filteredEquipmentRecords: Array<any> = [];
    equipmentsLoading: boolean = false;
    certificateType: string = '';
    certificateUrl: string = '';
    certificatePreviewURL: any;
    isPdfDocument: boolean = false;
    showPdfImg = false;
    minDate: NgbDateStruct = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs().add(1,'day'));
    globalSearch: any = '';
    ownerFilter: any[] = [];
    equipmentTypeFilter: any[] = [];
    viewEquipmentOptionModalRef: any = null;
    dailyInspectionOptionsModalRef: any = null;
    displayDateFormat: string = AppConstant.defaultDateFormat;
    downloadingWeeklyInspection: boolean = false;
    hasInspections: boolean = false;
    loadingEquipmentRecords: boolean = false;
    loadingArchivedEquipmentRecords: boolean = false;
    projectAdminIds: Array<any> = [];
    project_admins: Array<any> = [];
    equipmentManagerIds: Array<any> = [];
    equipment_managers: Array<any> = [{}];
    projectRequest: Project = new Project;
    declined_comment: string = '';
    onSiteEquipmentCount: number = 0;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    isEquipmentImageUploaded: boolean = false;
    companiesListFilter: Array<any> = [];
    fileUrlToView: string = '';
    equipmentPhotos: Array<any> = [];
    closeFormModal: boolean = true;
    equipmentId: any = null;
    accessEquipmentItems: Array<any> = [];
    dropdownIndex: number = null;
    fileModalTitle: any;
    week_Days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    activeNav: number = 1;
    assetEquipmentItems : any;
    archivedEquipmentListModalRef: any = null;
    equipmentFaults: Array<any> = [];
    faultCloseOutReq: any = {};
    blockLoader: boolean = false;
    hasOnlySiteManagement: boolean = false;
    userEmployer: any = {};
    projectResolverResponse: any = {};
    assetActivityLogs: Array<AssetActivityLog> = [];
    isListView: boolean = false;
    total_asset: number = 0;
    STATUS_CODES_LIST: Array<any> = new Common().ASSET_STATUS_CODES;
    selectedStatus: any[] = [];
    filterData:filterData[] = this.renderFilterData();
    archiveFilterData:filterData[] = this.renderArchivedFilterData();
    actionButtonMetaData = {
        actionList: [],
        class: 'material-symbols-outlined',
    };
    showEquipmentModal: boolean = false;
    showPhotoCollage: boolean = false;
    showModal: boolean = false;
    indexNumber: number = 0;
    metaConfig;
    assetConfig: AssetCustomConfig = new AssetCustomConfig;
    assetTypeSelected: boolean = true;
    assetCustomConfigFieldTypes = AssetCustomConfigFieldTypes;
    // Filter and pagination
    paginationData = new Common();
    archivePaginationData = new Common();
    page = this.paginationData.page;
    archivedPage = this.archivePaginationData.page;
    @ViewChild(DatatableComponent) table: DatatableComponent;
    isInitAssetEquipment: boolean = false;
    loadingInlineAssetEquipment: boolean = false;
    loadingInlineArchivedAssetEquipment: boolean = false;
    taggedOwners: Array<any> = [];
    archivedTaggedOwners: Array<any> = [];
    sortValues: any = null;
    loadingMetaConfig:boolean = false;
    fallArrestItems: Array<any> = [];
    subTypeEquipmentsItems: Array<string> = ['access_equipment', 'fall_arrest_systems'];
    public maxlength: number = 30;
    baseButtonConfig: Array<ActionBtnEntry> = [
        {
            key: 'view',
            label: '',
            title: 'View',
            mat_icon: 'search'
        },
        {
            key: 'unarchive',
            label: '',
            title: 'Unarchive Vehicle',
            mat_icon: 'undo'
        }
    ];
    weeklyInspectionButtonConfig: Array<ActionBtnEntry> = [
        {
            key: 'viewWeeklyInspection',
            label: '',
            title: 'View',
            mat_icon: 'search'
        },
        {
            key: 'downloadWeeklyInspection',
            label: '',
            title: 'Download',
            mat_icon: 'download'
        }
    ];
    faultActionButtonConfig: Array<ActionBtnEntry> = [
        {
            key: 'closeOutFault',
            label: '',
            title: 'Close Out',
            mat_icon: 'close'
        }
    ];

    dayjsFormat(x:number, fullDate: boolean = true){
        return dayjs(+x).format(fullDate ? AppConstant.fullDateTimeFormat : AppConstant.defaultDateFormat);
    }

    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private modalService: NgbModal,
        private toastService: ToastService,
        private authService: AuthService,
        private projectService: ProjectService,
        private userService: UserService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private domSanitizer: DomSanitizer,
        private projectAssetService: ProjectAssetService,
        private resourceService: ResourceService,
        private companyAssetConfigService: CompanyAssetConfigService,
    ) {
        // this.resourceService.getInnDexSettingByName('type_of_asset_equipments_en_gb').subscribe((data:any) => {
        //     if(data.success && data.record && data.record.value) {
        //         this.TYPE_OF_EQUIPMENT = data.record.value;
        //         this.filterData = this.renderFilterData();
        //         this.archiveFilterData = this.renderArchivedFilterData();
        //     } else {
        //         alert('Something went wrong while getting type of equipments.');
        //     }
        // });

        // this.resourceService.getInnDexSettingByName('asset_equipment_items').subscribe((data:any) => {
        //     if(data.success && data.record && data.record.value) {
        //         this.assetEquipmentItems = data.record.value;
        //     } else {
        //         alert('Something went wrong while fetching asset equipment items.');
        //     }
        // })
    }

    ngOnInit() {
        if (!this.isProjectPortal) {
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        }
        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
            }
        });

        this.userService.getProjectAdmins(this.projectId, false, false, (!this.isProjectPortal ? this.employer.id : undefined)).subscribe((data:any) =>{
            if(data && data.admins) {
                this.projectAdminIds = (data.admins || []).reduce((acc, item) => {
                    if (item.user_ref) {
                      acc.push(item.user_ref.id || item.user_ref);
                    }
                    return acc;
                  }, []);
            } else {
                const message = data.message || 'Failed to get list of admins.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });

        this.getViewType();
        this.getTaggedOwnersList();
        this.hasOnlySiteManagement = (this.project._my_designations.length === 1 && this.project._my_designations.includes(UACProjectDesignations.DELIVERY_MANAGER));
        this.setEquipmentManagerIds(this.project);

        this.loadingEquipmentRecords = true;
        this.loadingMetaConfig = true;
        this.getCompanyAssetConfig();
        if(this.isListView){
            this.loadingEquipmentRecords = true;
            this.initEquipments(false, 0);
        }
        this.loadingArchivedEquipmentRecords = true;
        this.initEquipments(true, 0);
        this.actionButtonMetaData.actionList = [
            {
                code: AssetEquipmentActionButtons.EQUIPMENT_MANAGERS,
                name: `Equipment Managers`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'person',
                enabled: true,
            },
            {
                code: AssetEquipmentActionButtons.ARCHIVED_LIST,
                name: `Archived List`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'inventory_2',
                enabled: true,
            },
            {
                code: AssetEquipmentActionButtons.DOWNLOAD_REGISTER,
                name: `Download Register`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: true,
            },
        ];
    }

    getCompanyAssetConfig() {
        this.loadingMetaConfig = true;
        const params = new HttpParams().set('assetType',  'asset-equipment');
        this.companyAssetConfigService.getProjectAssetConfig(this.project.id, params).subscribe((data: any)=> {
            this.loadingMetaConfig = false;
            if(data.error) {
                alert("Invalid request! Failed to get company meta asset configuration!")
                return;
            }
            this.metaConfig = data.records;
            this.mapAlternatePhrase();
        });
    }

    
    mapAlternatePhrase() {
        this.TYPE_OF_EQUIPMENT = this.metaConfig.map(a=> {
            return {
                key: a.key,
                value: a.name,
                alternate_phrase: a.alternate_phrase,
                parent_key: a.parent_key
            }
        });
        this.TYPE_OF_EQUIPMENT = this.TYPE_OF_EQUIPMENT.filter(t=> !t.parent_key);
        this.filterData= this.renderFilterData();
    }

    setEquipmentManagerIds(project) {
        this.equipmentManagerIds = (project.custom_field.equipment_managers || []).reduce((arr, item) => {
            if (item && typeof item === 'number') {
                arr.push(item);
            }
            return arr;
        }, []);
    }

    sortCallback({sorts}, is_archived: boolean = false) {
        let [firstSort] = sorts || [];
        let {dir, prop} = firstSort;
        this.sortValues = firstSort;
        this.initEquipments( is_archived, 0, true, {sortKey: prop, sortDir: dir});
    }

    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, is_archived: boolean = false, isPageChange: boolean) {
        if (!this.isInitAssetEquipment) {
            this.isInitAssetEquipment = true;
            return;
        }
        console.log("here we are in pageCallback", this.page.pageNumber, pageInfo.offset, pageInfo);
        let pageNumber = 0;
        pageNumber = pageInfo.offset;
        this.initEquipments(is_archived, pageNumber, isPageChange, this.sortValues != null ? {sortKey: this.sortValues.prop, sortDir: this.sortValues.dir} : {sortKey: 'id'});
    }
    paginationCallback(event:any){
        this.page.pageNumber = event-1;
        this.initEquipments(false, this.page.pageNumber, true);
    }
    getTaggedOwnersList(is_archived = 'false') {
        this.projectAssetService.getAssetTaggedOwners(this.projectId, is_archived, 'asset-equipment').subscribe((res:any) => {
            if(res.success) {
                if (is_archived === 'false') {
                    this.taggedOwners = res.tagged_owners;
                    this.filterData = this.renderFilterData();
                } else {
                    this.archivedTaggedOwners = res.tagged_owners;
                    this.archiveFilterData = this.renderArchivedFilterData();
                }
            }
        })
    }
    initEquipments(is_archived:boolean = false, pageNumber:number = 0, isPageChange:boolean = false, sortInfo: any = {sortKey: 'id'}){
        if (!isPageChange) {
            !is_archived ? (this.loadingEquipmentRecords = true) : (this.loadingArchivedEquipmentRecords = true);
        }
        if(isPageChange){
            !is_archived ? (this.loadingInlineAssetEquipment = true) : (this.loadingInlineArchivedAssetEquipment = true);
        }
        let pageSize = this.page.size;
        if(is_archived){
            pageSize = this.archivedPage.size;
        }
        let params = new HttpParams()
            .set('pageNumber', `${pageNumber}`)
            .set('pageSize', `${pageSize}`)
            .set('sortKey', `${sortInfo.sortKey}`);
        if(this.globalSearch !== null){
            params = params.append('q',`${encodeURIComponent(this.globalSearch)}`);
        }
        if(this.equipmentTypeFilter.length != 0){
            params  = params.append('type', `${this.equipmentTypeFilter.join(",")}`);
        }
        if(this.ownerFilter.length != 0){
            params = params.append('owner', `${this.ownerFilter.join(",")}`);
        }
        if (this.selectedStatus.length) {
            params = params.append('status', `${this.selectedStatus.join(",")}`);
        }
        if(sortInfo.sortDir){
            params = params.append('sortDir', `${sortInfo.sortDir}`);
        }
        params = params.append('expand_inspections', false.toString());
        params = params.append('is_archived', is_archived.toString());

        this.projectAssetService.getProjectAssetEquipments(this.projectId, params).subscribe((data:any) => {
            if(data.success && data.records) {
                this.loadingInlineAssetEquipment = false;
                this.equipmentsLoading = false;
                if(!is_archived) {
                    this.loadingInlineAssetEquipment = false;
                    this.loadingEquipmentRecords = false;
                    this.total_asset = data.total_asset || data.records.length;
                    this.page.totalElements = data.totalCount;
                    let equipments_list = this.associateAvailableMondays(data.records);
                    this.onSiteEquipmentCount = data.totalOnsiteAssets;
                    this.equipmentRecords = equipments_list;
                    this.filteredEquipmentRecords = equipments_list;
                    this.filteredEquipmentRecords.map( d => {
                        d.expiry_dates = [];
                        d.expiry_dates_count = 0;
                        d.type_of_equipment_name = this.getEquipmentType(d.equipment_type);
                        if (d?.examination_cert_expiry_date) {
                            const isExpired = this.isExpired(d.examination_cert_expiry_date);
                            d.expiry_dates.push({ 'name': 'Examination', 'expiry_date': d.examination_cert_expiry_date, 'isExpired': isExpired });
                            if (isExpired === 0) {
                                d.expiry_dates_count += 1;
                            }
                        }

                        if (d?.pat_test_expiry_date) {
                            const isExpired = this.isExpired(d.pat_test_expiry_date);
                            d.expiry_dates.push({ 'name': 'PAT Test', 'expiry_date': d.pat_test_expiry_date, 'isExpired': isExpired });
                            if (isExpired === 0) {
                                d.expiry_dates_count += 1;
                            }
                        }
                        for(let k=0; k<d.custom_fields.length; k++) {
                            if(d.custom_fields[k].type === this.assetCustomConfigFieldTypes.Certification) {
                                const isExpired = this.isExpired(d.custom_fields[k].expiry_date);
                                d.expiry_dates.push({ 'name': d.custom_fields[k].title, 'expiry_date': d.custom_fields[k].expiry_date, 'isExpired': isExpired });
                                if (isExpired === 0) {
                                    d.expiry_dates_count += 1;
                                }
                            }
                        }

                        if (d?.expiry_dates && d?.expiry_dates?.length) {
                            d.expiry_dates = d.expiry_dates.sort((a: { isExpired: number; }, b: { isExpired: number; }) => a.isExpired - b.isExpired);
                        }

                        if (d?.latest_inspection && d?.latest_inspection?.createdAt) {
                            d.lastDailyInspectionAt = d.latest_inspection;
                        }
                        d.last_inspected = d?.lastDailyInspectionAt?.updatedAt ? this.dayjsFormat(d.lastDailyInspectionAt.updatedAt, false) : 'N/A';
                        d.expired_certs = `${d.expiry_dates_count}/${d.expiry_dates.length}`;
                        d.fault_count = d.fault_count ?? 0;
                        return d;
                    });
                    this.userEmployer = data.userEmployer;
                } else {
                    this.loadingArchivedEquipmentRecords = false;
                    this.loadingInlineArchivedAssetEquipment = false;
                    this.archivedEquipmentRecords = this.associateAvailableMondays(data.records);
                    this.archivedPage.totalElements = data.totalCount;
                }
            }
        });
    }

    private isExpired(expiry_date: number) {
        const today = dayjs().unix() * 1000;
        let priorDateInTimestamp = dayjs().add(30, 'day').unix() * 1000;
        if(expiry_date < today) {
            return 0;
        } else if (expiry_date > today && expiry_date <= priorDateInTimestamp) {
            return 1;
        } else if (expiry_date > priorDateInTimestamp) {
            return 2;
        }
    }

    isValid() {
        if(this.assetEquipment._examinationCertExpiryDate && this.assetEquipment.examination_certificates?.length === 1) { return false }
        return true;
    }

    manageEquipmentFaults() {
        let openFaults = [];
        let closedFaults = [];
        this.equipmentFaults = (this.equipmentFaults).sort((a, b) => (a.fault_id < b.fault_id) ? 1 : ((b.fault_id < a.fault_id) ? -1 : 0))
        this.equipmentFaults.map(fault => {
            if ((!fault.status && !fault.closedout_at) || fault.status == 'open') {
                openFaults.push(fault);
            } else {
                closedFaults.push(fault);
            }
        });
        closedFaults = (closedFaults).sort((a, b) => (a.date_reported < b.date_reported) ? 1 : ((b.date_reported < a.date_reported) ? -1 : 0))

        this.equipmentFaults = [...openFaults, ...closedFaults];
    }

    getTaggedOwners(tagged_owner) {
        let ownersInfo = [];
        (tagged_owner || []).map(company => {
            if (company && company.name) {
                ownersInfo.push(company.name);
            }
            return company;
        });
        return ownersInfo.join(', ');
    }

    getCompanies() {
        let companyIds = [];
        (this.equipmentRecords || []).map(asset => {
            if (asset.tagged_owner && asset.tagged_owner.length) {
                (asset.tagged_owner || []).map(owner => {
                    if (owner.id && !companyIds.includes(owner.id)) {
                        companyIds.push(owner.id);
                        this.companiesListFilter.push({ 'id': owner.id, 'name': owner.name });
                    }
                });
            }
        });
        this.companiesListFilter.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
        this.equipmentsLoading = false;
    }

    dayjsTz(n: number) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };

    associateAvailableMondays(equipments_list) {
        equipments_list.forEach((el) => {
            let equipment_inspections = el.equipment_inspections;
            let availableMondays = (equipment_inspections || []).reduce((arr, ei) => {
                let dateOnMonday = dayjs(+ei.createdAt).startOf('isoWeek').format(this.displayDateFormat);
                let existingWeekIndex = arr.findIndex(v => v.date === dateOnMonday);
                let existingWeek = arr.find(v => v.date === dateOnMonday);
                let weekDay = this.dayjsTz(+ei.createdAt).format('ddd');
                let createdAt = this.dayjsTz(+ei.createdAt).format(this.displayDateFormat+'  HH:mm:ss');
                let user_name = ei.user_ref.first_name + ' ' + ei.user_ref.last_name;

                if (existingWeekIndex == -1) {
                    let newWeek: any = {};
                    newWeek.date = dateOnMonday;
                    newWeek.weekdays = [{
                        day: weekDay,
                        user_name,
                        createdAt
                    }];
                    arr.push(newWeek)
                } else {
                    let weekDayIndex = (existingWeek.weekdays || []).findIndex(v => v.day === weekDay);
                    if (weekDayIndex == -1) {
                        existingWeek.weekdays.push({
                            day: weekDay,
                            user_name,
                            createdAt
                        });
                    }
                    arr[existingWeekIndex] = existingWeek;
                }

                let inspectionFaults = (ei.fault_details || []).reduce((arr, fault) => {
                    fault.ei_ref = ei.id;
                    fault.ei_createdAt = ei.createdAt;
                    fault.status = (!fault.status || !fault.closedout_at) ? 'open' : fault.status;
                    arr.push(fault);
                    return arr;
                }, []);

                this.equipmentFaults.push(...inspectionFaults);

                return arr;
            }, []);
            el.availableMondays = availableMondays.sort((a, b) => (dayjs(b.date, this.displayDateFormat).diff(dayjs(a.date, this.displayDateFormat))));
        });
        return equipments_list;
    }

    dayIndex(weekdays, day): number {
        return weekdays.findIndex(d => d.day === day);
    }

    getEquipment(equipmentId: number, isEditable: boolean, viewOptionModal: boolean) {
        this.equipmentId = equipmentId ? equipmentId : null;
        this.projectAssetService.getEquipmentAsset(this.projectId, equipmentId, 'true').subscribe((data:any) => {
            this.blockLoader = false;
            this.assetTypeSelected = true;
            if (viewOptionModal) {
                this.viewEquipmentOptionsRef.open();
            }
            if(data.success && data.asset_equipment) {
                let todayMs = dayjs().valueOf();
                //data.asset_equipment.equipment_photos.push({});
                data.asset_equipment.examination_certificates.push({});
                if(data.asset_equipment.examination_cert_expiry_date && (+data.asset_equipment.examination_cert_expiry_date - todayMs) >= 0) {
                    data.asset_equipment._examinationCertExpiryDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+data.asset_equipment.examination_cert_expiry_date));
                }

                data.asset_equipment._patTestExpiryDate = (data.asset_equipment.pat_test_expiry_date) ? this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+data.asset_equipment.pat_test_expiry_date)) : null;
                data.asset_equipment._arrivedAt = (data.asset_equipment.arrived_at) ? this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+data.asset_equipment.arrived_at)) : null;
                this.assetEquipment = data.asset_equipment;
                this.equipmentFaults = [];
                let equipmentWithInspections = this.associateAvailableMondays([data.asset_equipment]);
                this.assetEquipment = equipmentWithInspections[0];
                this.hasInspections = (this.assetEquipment['availableMondays'] && this.assetEquipment['availableMondays'].length) ? true : false;
                this.manageEquipmentFaults();
                if (isEditable) {
                    this.assetTypeChanged();
                    this.transformDatesToNgb();
                    if (this.viewEquipmentOptionsRef) {
                        this.viewEquipmentOptionsRef.close();
                        this.assetEquipment.tagged_owner = (this.assetEquipment.tagged_owner || []).map(owner => owner.id);
                        this.showEquipmentModal = true;
                        this.checkPhotoCollage();
                        this.addEquipmentModalRef.open();
                    }
                }
                this.isEquipmentImageUploaded = false;
                if(this.assetEquipment.equipment_photos && this.assetEquipment.equipment_photos.length && this.assetEquipment.equipment_photos[0].file_url) {
                    this.isEquipmentImageUploaded = true;
                }
            }
        });
    }

    @ViewChild('addEquipmentModalRef')
    private addEquipmentModalRef: IModalComponent;
    addEquipmentPopup() {
        this.isEquipmentImageUploaded = false;
        this.assetEquipment = new ProjectAssetEquipment();
        this.assetEquipment.tagged_owner = (this.hasOnlySiteManagement && this.userEmployer.id) ? [this.userEmployer.id] : [];
        this.assetEquipment.equipment_photos = [{}];
        this.assetEquipment.equipment_id = `${this.total_asset + 1}`;
        this.showEquipmentModal = true;
        this.checkPhotoCollage();
        this.assetTypeSelected = true;
        this.addEquipmentModalRef.open();
    }

    openModal(content, size) {
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size
        });
    }

    examinationCertificateUploadDone($event:any){
        if($event && $event.userFile && $event.userFile.id) {
            let index = (this.assetEquipment.examination_certificates.length) ? this.assetEquipment.examination_certificates.length-1 : 0;
            this.assetEquipment.examination_certificates[index] = $event.userFile;
            this.assetEquipment.examination_certificates[this.assetEquipment.examination_certificates.length] = {};
        }
    }

    deleteExaminationCertificateRecord($event:any) {
        if($event && $event.userFile && $event.userFile.id) {
            this.assetEquipment.examination_certificates = this.assetEquipment.examination_certificates.filter(r => (r.id !== $event.userFile.id));
        }
    }

    isPDF(url, type) {
        if (url && url.split('.').pop() && url.split('.').pop().toLowerCase() === 'pdf') {
            this.examinationCertPreviewURL = this.domSanitizer.bypassSecurityTrustResourceUrl(url + '?embed=frame');
            this.certificatePreviewURL = this.domSanitizer.bypassSecurityTrustResourceUrl(url + '?embed=frame');
            return true;
        }
        return false;
    }

    logoImgRetries:number = 5;

    onLogoError($img:any, targetSrc:any, isLogo = true){
        if (isLogo) {
            $img.src = AssetsUrl.siteAdmin.logoPlaceholder; // AppConstant.apiServerUrl + '/images/logo-placeholder.png';
        } else {
            $img.src = AssetsUrl.siteAdmin.projectPlaceholder; // AppConstant.apiServerUrl + '/images/project-placeholder.png';
        }

        if(targetSrc && targetSrc.length && this.logoImgRetries){
            setTimeout(() => {
                this.logoImgRetries--;
                $img.src = targetSrc;
            }, 2000);
        }
    }

    onLogoLoad($img){
        this.logoImgRetries = 5;
    }

    saveEquipment() {
        this.equipmentsLoading = true;
        this.assetEquipment.examination_cert_expiry_date = this.assetEquipment._examinationCertExpiryDate ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.assetEquipment._examinationCertExpiryDate).valueOf() : null;
        this.assetEquipment.pat_test_expiry_date = this.assetEquipment._patTestExpiryDate ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.assetEquipment._patTestExpiryDate).valueOf() : null;
        this.assetEquipment.arrived_at = this.assetEquipment._arrivedAt ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.assetEquipment._arrivedAt).valueOf() : null;
        this.transformDatesToDayJS();
        if (!this.assetEquipment.id) {
            this.setActivityLogRequest("Added");
            this.assetEquipment.approval_pending = this.hasOnlySiteManagement ? 1 : 2;
            this.projectAssetService.addEquipmentAsset(this.assetEquipment, this.projectId).subscribe(this.responseHandler.bind(this, false, false));
        } else {
            this.setActivityLogRequest("Modified");
            let request:any = Object.assign({}, this.assetEquipment);
            delete request.availableMondays;
            delete request.equipment_inspections;
            this.projectAssetService.updateEquipmentAsset(request, this.projectId, this.assetEquipment.id).subscribe(this.responseHandler.bind(this, false, false));
        }
        this.closeVehicleModal();
        this.addEquipmentModalRef.close();
    }

    responseHandler(loadEquipment = false, isArchived:boolean = false, out: any, data?: any) {
        if(out.success) {
            (this.addEquipmentModal && this.closeFormModal) ? this.addEquipmentModal.close() : '';
            if (data) {
                data.closeFn();
            }
            this.initEquipments(isArchived, 0, true);
            if (loadEquipment) {
                this.getEquipment(this.assetEquipment?.id, false, false);
            }
        } else {
            const message = out.message || 'Failed to store data.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: out });
        }
    }

    hasExaminationNumber() {
        if (!this.assetEquipment.examination_cert_number) {
            return false;
        }
        return true;
    }

    hasExaminationCertExpDate() {
        if (!this.assetEquipment._examinationCertExpiryDate) {
            return false;
        }
        return true;
    }

    examinationNumberChanged(){
        if (!this.assetEquipment.examination_cert_number) {
            this.assetEquipment.examination_certificates = [{}];
            this.assetEquipment.examination_cert_expiry_date = null;
            this.assetEquipment._examinationCertExpiryDate = null;
        }
    }

    examinationCertExpDateChanged(){
        if (!this.assetEquipment.examination_certificates.length || !this.assetEquipment._examinationCertExpiryDate) {
            this.assetEquipment.examination_certificates = [{}];
        }
    }

    filterRecords(is_archived:boolean = false) {
        this.initEquipments(is_archived, 0, true);
    }

    resetFilter(filter_field) {
        if (filter_field === 'equipment_type_filter') {
            this.equipmentTypeFilter = null;
        } else if (filter_field === 'owner_filter') {
            this.ownerFilter = null;
        } else {
            this.globalSearch = '';
        }
        this.filterRecords();
    }

    getEquipmentType(equipmentTypeKey) {
        let typeInfo = this.TYPE_OF_EQUIPMENT.find(v => v.key === equipmentTypeKey);
        return typeInfo ? typeInfo.alternate_phrase : '';
    }

    getSubEquipmentAlternatePhrase(item) {
        let typeInfo = this.metaConfig.find(v => v.name === item);
        return typeInfo ? typeInfo.alternate_phrase : '';
    }

    @ViewChild('viewEquipmentOptionsRef') private viewEquipmentOptionsRef: IModalComponent;
    viewEquipmentOptionModal(equipment, tab= 1) {
        this.blockLoader = true;
        this.assetEquipment = equipment;
        this.getEquipment(this.assetEquipment?.id, false, true);
        this.activeNav = tab;
        this.assetActivityLogs = [...this.assetEquipment.activity_logs].reverse();
        this.modalService.dismissAll();
    }

    @ViewChild('dailyInspectionOptionsHtml', { static: true }) private dailyInspectionOptionsHtmlRef = TemplateRef;
    dailyInspectionOptionsModal(equipment:any = {}) {
        this.assetEquipment = (equipment && equipment.id) ? equipment : this.assetEquipment;
        if (this.assetEquipment['availableMondays'] && !this.assetEquipment['availableMondays'].length) {
            const message = 'No inspection available.';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }

        if(this.viewEquipmentOptionModalRef) {
            this.viewEquipmentOptionModalRef.close();
        }
        this.dailyInspectionOptionsModalRef = this.openModal(this.dailyInspectionOptionsHtmlRef, 'sm');
    }

    downloadWeeklyInspection(fromDate, format, equipmentId) {
        this.downloadingWeeklyInspection = true;
        let fileName = 'Equipment-Weekly-Inspections-Report-' + dayjs().format(AppConstant.apiRequestDateFormat);

        let request = {
            from_day: dayjs(fromDate, this.displayDateFormat).format(AppConstant.apiRequestDateFormat),
            format,
            file_name: fileName
        };

        if (format === 'pdf') {
            this.projectAssetService.downloadWeeklyInspection(this.projectId, 'asset-equipment', equipmentId, request, () => {
                this.downloadingWeeklyInspection = false;
            });
        } else {
            this.projectAssetService.downloadWeeklyInspection(this.projectId, 'asset-equipment', equipmentId, request).subscribe((data:any) => {
                this.downloadingWeeklyInspection = false;
                let newWindow = window.open('', '_blank', 'location1=no,height1=570,width1=520,scrollbars=yes,status=yes,toolbar=no');
                newWindow.document.body.innerHTML = data;
            });
        }
    }

    archiveEquipmentAsset(id) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Archive Equipment',
            title: `Are you sure you want to archive this piece of equipment?`,
            confirmLabel: 'Archive',
            onConfirm: () => {
                this.equipmentsLoading = true;
                this.setActivityLogRequest("Archived");
                let request = {
                    is_archived: true,
                    activity_logs: this.assetEquipment.activity_logs
                };
                this.projectAssetService.archiveUnarchiveEquipmentAsset(request, this.projectId, id).subscribe(this.responseHandler.bind(this, false, false));
                this.viewEquipmentOptionsRef.close();
            }
        });
    }

    unArchiveEquipmentAsset(id, isArchived: boolean = false, isViewModal) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Unarchive Equipment',
            title: `Are you sure you want to unarchive this piece of equipment?`,
            confirmLabel: 'Unarchive',
            onConfirm: () => {
                this.equipmentsLoading = true;
                this.setActivityLogRequest("Unarchived");
                let request = {
                    is_archived: false,
                    activity_logs: this.assetEquipment.activity_logs
                };
                this.projectAssetService.archiveUnarchiveEquipmentAsset(request, this.projectId, id).subscribe(this.responseHandler.bind(this, false, isArchived));
                if(isViewModal){
                    this.viewEquipmentOptionsRef.close();
                }
                this.initEquipments(false, 0, true);
            }
        });
    }

    @ViewChild('archivedEquipmentListRef') archivedEquipmentListRef: IModalComponent
    showArchivedEquipments() {
        this.getTaggedOwnersList('true');
        this.initEquipments(true, 0, true);
        this.showModal = true;
        this.archivedEquipmentListRef.open();
    }

    @ViewChild('usersSelectorModal') usersSelectorModalRef: UsersSelectorModalComponent
    equipmentManagersPopup() {
        this.userService.getUsersById(Array.from(new Set([...this.equipmentManagerIds, ...this.projectAdminIds])), ['email', 'first_name', 'last_name'], this.projectId).subscribe((data: any) => {
            if (data.success && data.users) {
                this.equipment_managers = (data.users || []).filter(user => this.equipmentManagerIds.includes(user.id)).map(user => ({ id: user.id })) || [{}];
                this.project_admins = (data.users || []).filter(user => this.projectAdminIds.includes(user.id));
                this.usersSelectorModalRef.openUsersSelectorModalModal();
            } else {
                const message = data.message || 'Failed to get data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    saveEquipmentManagers($event) {
        this.equipmentsLoading = true;
        this.equipmentManagerIds = ($event || []).reduce((arr, item) => {
            if (item.id) {
                arr.push(item.id);
            }
            return arr;
        }, []);

        this.projectRequest = {
            "id": this.projectId,
            "custom_field": {
                "equipment_managers": this.equipmentManagerIds
            }
        };

        this.projectService.updateProjectPartially(this.projectRequest).subscribe((data: any) => {
            this.equipmentsLoading = false;
            if (data.success) {
                this.project.custom_field = data.project.custom_field;
                this.setEquipmentManagerIds(data.project);
                console.log("Equipment managers added successfully.");
            }
        });
    }

    approveEquipment(item) {
        this.dropdownIndex = null;
        this.assetEquipment = item;
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Approve Equipment',
            title: `Are you sure you want to approve this piece of equipment?`,
            confirmLabel: 'Approve',
            onConfirm: () => {
                this.equipmentsLoading = true;
                this.setActivityLogRequest("Approved");
                let request = {
                    "approval_pending": 2,
                    "activity_logs": this.assetEquipment.activity_logs
                };
                this.projectAssetService.updateEquipmentAsset(request, this.projectId, this.assetEquipment.id).subscribe(this.responseHandler.bind(this, false, false));
            }
        });
    }

    declineEquipmentConfirmation(item) {
        this.dropdownIndex = null;
        this.assetEquipment = item;
        this.declined_comment = '';
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Decline Equipment',
            title: `Are you sure you want to decline this piece of equipment?`,
            confirmLabel: 'Decline',
            onConfirm: () => {
                this.assetDeclineRef.openAssetDeclineModal();
            }
        });
    }

    declineEquipment(data) {
        this.equipmentsLoading = true;
        let request = {
            "approval_pending": 3,
            "declined_comment": data.comment
        };
        this.projectAssetService.updateEquipmentAsset(request, this.projectId, this.assetEquipment.id).subscribe(res => this.responseHandler(false, false, res, data));
    }

    equipmentPhotoUploadDone($event:any, uploadMore = false) {
        if($event && $event.userFile && $event.userFile.id) {
            let index = (this.assetEquipment.equipment_photos.length && Object.keys(this.assetEquipment.equipment_photos[0]).length) ? this.assetEquipment.equipment_photos.length : 0;
            index = (uploadMore) ? index - 1 : index;
            this.assetEquipment.equipment_photos[index] = $event.userFile;
            if(uploadMore) {
                this.assetEquipment.equipment_photos[this.assetEquipment.equipment_photos.length] = {};
            }
            this.checkPhotoCollage();
        }
    }

    equipmentPhotoCollageUploadDone($event:any, uploadMore = false) {
        if ($event && $event.userFile && $event.userFile.id) {
            if ((this.assetEquipment.equipment_photos.length && Object.keys(this.assetEquipment.equipment_photos[0]).length)) {
                this.indexNumber = this.assetEquipment.equipment_photos.length;
            } else {
                this.indexNumber = 0;
                uploadMore = false;
            }
            this.indexNumber = (uploadMore) ? this.indexNumber - 1 : this.indexNumber;
            uploadMore = true;
            this.assetEquipment.equipment_photos[this.indexNumber] = $event.userFile;
            if (uploadMore) {
                this.assetEquipment.equipment_photos[this.assetEquipment.equipment_photos.length] = {};
            }
            this.checkPhotoCollage();
        }
    }

    checkPhotoCollage() {
        if((this.assetEquipment.equipment_photos && this.assetEquipment.equipment_photos.length && this.assetEquipment.equipment_photos[0].file_url)) {
            this.showPhotoCollage = true;
        }
    }

    fileDeleteDone($event:any) {
        if($event && $event.userFile && $event.userFile.id) {
            this.assetEquipment.equipment_photos = this.assetEquipment.equipment_photos.filter(r => (r.id !== $event.userFile.id));
            this.checkPhotoCollage();
        }
    }

    @ViewChild('viewFileRef') private viewFileRef: IModalComponent;
    viewFileModal(fileUrl, title) {
        this.certificateUrl = fileUrl;
        this.fileModalTitle = title;
        if(fileUrl) {
            this.showPdfImg = true;
            this.isPdfDocument = this.isPDF(this.certificateUrl, this.fileModalTitle);
            this.viewFileRef.open();
        }
    }

    closePdfImgModal(event?) {
        this.showPdfImg = false;
        if(event) {
            event.closeFn();
        }
    }

    equipmentTypeChange() {
        this.assetEquipment.item = '';
    }

    replaceAll(str, find, replace) {
        const escapedFind = find.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, '\\$1');
        return str.replace(new RegExp(escapedFind, 'g'), replace);
    }

    downloadRegister() {
        let projectId = this.projectId;
        let equipmentIds = ([...this.equipmentRecords, ...this.archivedEquipmentRecords] || []).reduce((arr, item) => {
            if (item.approval_pending == 2) {
                arr.push(item.id);
            }
            return arr;
        }, []);
        let request = {
            asset_ids: equipmentIds
        };
        this.equipmentsLoading = true;

        this.projectAssetService.downloadRegisterXLSX(request, projectId, 'equipment', `asset-register-equipment-${this.project.project_number}-${this.project.name}.xlsx`, () => {
            this.equipmentsLoading = false;
        });
    }

    openCloseDropdown(index) {
        this.dropdownIndex = index !== this.dropdownIndex ? index : null;
    }

    toggler(activeNav: number) {
        this.activeNav = activeNav;
    }

    faultCloseoutModal(faultDetail) {
        this.faultCloseOutReq = faultDetail;
        this.faultCloseOutReq.closedout_images = [{}];
        this.faultCloseOutReq.closedout_at = null;
        this.faultCloseOutReq.closedout_by = null;
        this.faultCloseOutReq.closedout_details = '';

        this.faultCloseOutRef.openFaultCloseOutModal();
    }

    uploadDoneFault($event) {
        if($event && $event.userFile) {
            this.faultCloseOutReq.closedout_images.splice(1, 0, ...$event.userFile);
            this.faultCloseOutReq.closedout_images[0] = {};
        }
    }

    fileDeleteDoneFault($event: any) {
        if ($event && $event.userFile && $event.userFile.id) {
            this.faultCloseOutReq.closedout_images = this.faultCloseOutReq.closedout_images.filter(r => (r.id !== $event.userFile.id));
        }
    }

    requestCloseOut(data) {
        this.faultCloseOutRequest(data);
    }

    faultCloseOutRequest(data) {
        this.faultCloseOutReq = data.faultCloseOutReqObj;
        this.faultCloseOutReq.closedout_images = this.faultCloseOutReq.closedout_images.reduce((arr, image) => {
            if (image.id) {
                arr.push(image.id);
            }
            return arr;
        }, []);

        this.faultCloseOutReq.images = this.faultCloseOutReq.images.reduce((arr, image) => {
            if (image.id) {
                arr.push(image.id);
            }
            return arr;
        }, []);

        this.faultCloseOutReq.reported_by = (this.faultCloseOutReq.reported_by) ? this.faultCloseOutReq.reported_by : "N/A";

        let inspectionId = this.faultCloseOutReq.ei_ref;
        //Remove extra properties
        delete this.faultCloseOutReq.ei_ref;
        delete this.faultCloseOutReq.ei_createdAt;

        this.faultCloseOutReq.status = 'closed';
        this.faultCloseOutReq.closedout_at = dayjs().valueOf();
        this.faultCloseOutReq.closedout_by = this.authUser$.first_name+' '+this.authUser$.last_name;
        delete this.faultCloseOutReq.reported_to;

        this.blockLoader = true;
        this.projectAssetService.updateAssetEquipmentInspection({'fault': this.faultCloseOutReq}, this.projectId, this.assetEquipment.id, inspectionId).subscribe(res => this.responseHandler(true, false, res, data));
    }

    getRowSpan(fault) {
        let rowSpan = 1;
        if (fault.images && fault.images.length) {
            rowSpan += 1;
        }

        if (fault.closedout_at) {
            rowSpan += 1;
        }

        if (fault.closedout_at && fault.closedout_images && fault.closedout_images.length) {
            rowSpan += 1;
        }

        return rowSpan;
    }

    setActivityLogRequest(type: string) {
        let activityLog = new AssetActivityLog();
        activityLog.type = type;
        activityLog.name = this.authUser$.first_name+' '+this.authUser$.last_name;
        activityLog.user_id = this.authUser$.id;
        activityLog.timestamp = dayjs().valueOf();
        this.assetEquipment.activity_logs.push(activityLog);
    }

    getViewType() {
        this.isListView = JSON.parse(localStorage.getItem("isListView"));
    }

    setViewType() {
        this.isListView = !this.isListView;
        localStorage.setItem("isListView", JSON.stringify(this.isListView));
    }
    onFilterSelection(data, is_archived: boolean = false){
        console.log("On filter", data);
        this.equipmentTypeFilter = data.type.map(a=>a.key);
        this.ownerFilter = data.owner.map(a=>a.id);
        if(!is_archived){
            this.selectedStatus = data.status.map(a => +a.code);
        }
        if(this.isListView) {
            this.table.offset = 0;
        }
        this.filterRecords(is_archived);
    }

    getQRCodeString(asset) {
        let str = this.projectId + '-AME-'+asset.id;
        return str;
    }

    getFileName(asset) {
        let fileName = 'Asset-Equpiment-' + asset.id + '-qr-code';
        return fileName;
    }
    searchFunction(data, is_archived:boolean = false){
        this.globalSearch = data.search;
        if(this.isListView) {
            this.table.offset = 0;
        }
        this.filterRecords(is_archived);
    }
    closeVehicleModal() {
        this.showEquipmentModal = false;
        this.showPhotoCollage = false;
        this.assetConfig = {};
    }
    closeArchiveModal() {
        if(this.archivedEquipmentRecords.length){
            this.searchComponentRef.clearFilters();
        }
        this.showModal = false;
    }
    renderFilterData(){
        return [
            {
                name:'type',
                list:this.TYPE_OF_EQUIPMENT,
                state:false,
                enabled:true,
            },
            {
                name:'owner',
                list:this.taggedOwners,
                state:false,
                enabled:true,
            },
            {
                name:'status',
                list:this.STATUS_CODES_LIST,
                state:false,
                enabled:true,
            }
        ];
    }

    renderArchivedFilterData() {
        return [
            {
                name:'type',
                list:this.TYPE_OF_EQUIPMENT,
                enabled:true,
                state:false
            },
            {
                name:'owner',
                list:this.archivedTaggedOwners,
                enabled:true,
                state:false
            }
        ];
    }

    public onActionSelection(actionVal: ActionButtonVal) {
        const code = actionVal.code;
        if(code === AssetEquipmentActionButtons.EQUIPMENT_MANAGERS) {
            this.equipmentManagersPopup();
        } else if(code === AssetEquipmentActionButtons.ARCHIVED_LIST) {
            this.showArchivedEquipments();
        } else if(code === AssetEquipmentActionButtons.DOWNLOAD_REGISTER) {
            this.downloadRegister();
        }
    }

    syncCustomFields() {
        let updatedCustomFields = [];
        this.assetConfig.custom_fields.forEach((configField, index) => {
            const matchingIndex = this.assetEquipment.custom_fields.findIndex(
                tempField => tempField.key === configField.key
            );
            if (matchingIndex !== -1) {
                if(this.assetEquipment.custom_fields[matchingIndex].type === AssetCustomConfigFieldTypes.Certification) {
                    this.assetEquipment.custom_fields[matchingIndex].certificates = [...this.assetEquipment.custom_fields[matchingIndex].certificates, {}];
                }
                updatedCustomFields[index] = {
                    ...this.assetEquipment.custom_fields[matchingIndex],
                };
            } else {
                //add newly added field
                if(configField.type === AssetCustomConfigFieldTypes.Certification) {
                    configField.certificates = [{}];
                } else if(configField.type === AssetCustomConfigFieldTypes.Date) {
                    configField[configField.key] = null;
                }
                updatedCustomFields[index] = {
                    ...configField,
                };
            }
        });
        this.assetEquipment.custom_fields = updatedCustomFields;
    }

    subItems = [];
    assetTypeChanged() {
        this.blockLoader = true;
        
        let params = new HttpParams()
            .set('assetType', 'asset-equipment')
            .set('assetKey', this.assetEquipment.equipment_type)
            .set('extra', 'fields');
        this.companyAssetConfigService.getProjectAssetConfig(this.project.id, params).subscribe((data:any) => {
            if(data.success && data.records) {
                this.blockLoader = false;
                this.assetConfig = data.records.find(m=> m.key === this.assetEquipment.equipment_type);
                this.assetConfig = this.assetConfig ? this.assetConfig: {} as Partial<AssetCustomConfig>;
                if(this.assetConfig.sub_types && this.assetConfig.sub_types.length) {
                    this.subItems = this.assetConfig.sub_types;
                    if(this.assetEquipment.item) {
                        this.assetSubTypeChanged();
                    }
                    return;
                }
                this.assetTypeSelected = this.assetEquipment.equipment_type ? false: true;
                this.setAssetConfig();
            }
            this.blockLoader = false;
        });

        
    }

    setAssetConfig() {
        if(this.assetConfig && this.assetEquipment.equipment_type) {
            this.assetConfig.defaultFields = this.assetConfig.default_fields.reduce((a, v) => ({ ...a, [v.key]: v}), {});
            if(this.assetEquipment.id) {
                this.syncCustomFields();
            } else {
                this.assetEquipment.custom_fields = [];
                this.assetEquipment.custom_fields = this.assetConfig.custom_fields.map(d=> {
                    let a: any = {
                        'key': d.key,
                        'title': d.title,
                        'type': d.type
                    };
                    if(d.type === AssetCustomConfigFieldTypes.Certification) {
                       a.certificates = [{}];
                    }
                    return a;
                });
            }
        }
    }

    assetSubTypeChanged() {
        if(this.assetEquipment.item) {
            this.assetConfig = this.subItems.find(a=> a.name === this.assetEquipment.item);
            this.assetTypeSelected = this.assetEquipment.equipment_type ? false: true;
            this.setAssetConfig();   
        }        
    }


    transformDatesToDayJS() {
        this.assetEquipment.custom_fields = this.assetEquipment.custom_fields.reduce<AssetCustomField[]>((result,a)=> {
            if(a.type === AssetCustomConfigFieldTypes.Certification) {
                a.expiry_date = a._examinationCertExpiryDate ? this.ngbMomentjsAdapter.ngbDateToDayJs(a._examinationCertExpiryDate).valueOf() : null;
                a.certificates = a.certificates.filter(c=> JSON.stringify(c) !== '{}');
                delete a._examinationCertExpiryDate;
            } else if(a.type === AssetCustomConfigFieldTypes.Date) {
                a.value = a.value ? this.ngbMomentjsAdapter.ngbDateToDayJs(a.value).valueOf() : null;
            }
            if((a.type === AssetCustomConfigFieldTypes.Certification && a.expiry_date) || (a.value)) {
                result.push(a);
            }
            return result;
        }, []);
    }

    transformDatesToNgb() {
        this.assetEquipment.custom_fields = this.assetEquipment.custom_fields.map(a=> {
            if(a.type === AssetCustomConfigFieldTypes.Certification) {
                a._examinationCertExpiryDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+a.expiry_date));
            } else if(a.type === AssetCustomConfigFieldTypes.Date) {
                a.value = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+a.value));
            }
            return a;
        });
    }

    trackByKey(index: number, field: any): string {
        return field.key;
    }

    certificateUploadDone($event:any, i){
        if($event && $event.userFile && $event.userFile.id) {
            let index = (this.assetEquipment.custom_fields[i].certificates.length) ? this.assetEquipment.custom_fields[i].certificates.length-1 : 0;
            this.assetEquipment.custom_fields[i].certificates[index] = $event.userFile;
            this.assetEquipment.custom_fields[i].certificates[this.assetEquipment.custom_fields[i].certificates.length] = {};
        }
    }

    deleteCertificateRecord($event:any, i) {
        if($event && $event.userFile && $event.userFile.id) {
            this.assetEquipment.custom_fields[i].certificates = this.assetEquipment.custom_fields[i].certificates.filter(r => (r.id !== $event.userFile.id));
        }
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actionMap = {
            'view': () => this.viewEquipmentOptionModal(row),
            'unarchive': () => this.unArchiveEquipmentAsset(row.id, true, false),
            'viewWeeklyInspection': () => this.downloadWeeklyInspection(row.date, 'html', this.assetEquipment?.id),
            'downloadWeeklyInspection': () => this.downloadWeeklyInspection(row.date, 'pdf', this.assetEquipment?.id),
            'closeOutFault': () => this.faultCloseoutModal(row),
        };
        const action = actionMap[entry.key];
        if (action) {
            action();
        }
    }
}

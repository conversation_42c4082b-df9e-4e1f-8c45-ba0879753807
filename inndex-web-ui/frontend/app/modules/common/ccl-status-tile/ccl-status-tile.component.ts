import {Component, Input, OnInit} from '@angular/core';
import {CCL_COMPETENCY_GRADES, CclCheckData, User} from '@app/core';
import {AssetsUrl} from '@app/shared';

// Create a consolidated interface for all inputs
export interface CCLStatusTileConfig {
    cclCheckData: CclCheckData;
    parentCompany: number;
    contractorCclConfig: {
        enabled: boolean;
        is_excluded_project: boolean;
    };
    inductionPhrase?: string;
    authUser?: User;
    fromApproval?: boolean;
}

// Interface for grade data to avoid repeated function calls
export interface GradeDisplayData {
    label: string;
    textClass: string;
    icon: string;
    gradeDescription: string;
}

@Component({
    selector: 'ccl-status-tile',
    templateUrl: './ccl-status-tile.component.html',
    styleUrls: ['./ccl-status-tile.component.scss'],
})
export class CCLStatusTileComponent implements OnInit {
    CC_Logo: string = AssetsUrl.siteAdmin.Complete_Competence_Logo;
    CCL_COMPETENCY_GRADES = CCL_COMPETENCY_GRADES;
    
    // Single input with default values
    @Input() config: CCLStatusTileConfig = {
        cclCheckData: null,
        parentCompany: null,
        contractorCclConfig: {
            enabled: false,
            is_excluded_project: false
        },
        inductionPhrase: '',
        fromApproval: false
    };
    // Virtual property to store grade display data
    gradeDisplayData: GradeDisplayData;

    constructor() { }

    ngOnInit(): void {
        this.updateGradeDisplayData();
    }

    ngOnChanges(): void {
        this.updateGradeDisplayData();
    }

    // Update the virtual property whenever config changes
    private updateGradeDisplayData(): void {
        if (this.config?.cclCheckData?.competency_grade) {
            const grade = this.config.cclCheckData.competency_grade;
            const label = this.config.cclCheckData.competency_label || 'N/A';
            const description = this.config.cclCheckData.competency_description;

            this.gradeDisplayData = {
                label: label,
                textClass: this.getCCLGradeData(grade, 'textClass'),
                icon: this.getCCLGradeData(grade, 'icon'),
                gradeDescription: description || this.getCCLGradeData(grade, 'gradeDescription')
            };
        } else {
            // Default values if no grade data is available
            this.gradeDisplayData = {
                label: this.getCCLGradeData(CCL_COMPETENCY_GRADES.N_A.grade, 'label'),
                textClass: this.getCCLGradeData(CCL_COMPETENCY_GRADES.N_A.grade, 'textClass'),
                icon: this.getCCLGradeData(CCL_COMPETENCY_GRADES.N_A.grade, 'icon'),
                gradeDescription: this.getCCLGradeData(CCL_COMPETENCY_GRADES.N_A.grade, 'gradeDescription')
            };
        }
    }

    getCCLGradeData(grade: string, field: string = 'textClass') {
        switch (grade) {
            case CCL_COMPETENCY_GRADES.A.grade:
                return CCL_COMPETENCY_GRADES.A[field];
            case CCL_COMPETENCY_GRADES.B.grade:
                return CCL_COMPETENCY_GRADES.B[field];
            case CCL_COMPETENCY_GRADES.C.grade:
                return CCL_COMPETENCY_GRADES.C[field];
            case CCL_COMPETENCY_GRADES.D.grade:
                return CCL_COMPETENCY_GRADES.D[field];
            case CCL_COMPETENCY_GRADES.NULL.grade:
                return CCL_COMPETENCY_GRADES.NULL[field];
            default:
                return CCL_COMPETENCY_GRADES.N_A[field];
        }
    }
}
/**
 * Created by spatel on 25/05/20.
 */
import {Component, Input, OnChanges, OnInit, ViewChild} from '@angular/core';
import {AppConstant} from "@env/environment";
import * as dayjs from 'dayjs';
import {OptimaService, Project, TimeUtility, ToastService, VisitorService} from "@app/core";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import {ShiftConfigurationProviderComponent} from "./../shift-configuration/shift-configuration-provider.component";
import {ViewVisitorLogComponent} from "./../view-visitor-log/view-visitor-log.component";
import {ViewVisitorRmcDetailComponent} from "./../view-visitor-rmc-detail/view-visitor-rmc-detail.component";

@Component({
    selector: 'visitor-logs-for-date',
    templateUrl: './visitor-logs-for-date.component.html',
})
export class VisitorLogsForDateComponent implements OnInit, OnChanges {

    dbDateFormat: string = AppConstant.apiRequestDateFormat;
    displayDateFormat: string = AppConstant.displayDateFormat;

    unix(n: number) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs.unix(n).tz(tz);
    };

    getTimeFromSeconds(n: number) {
        return dayjs().startOf('day').add(dayjs.duration(+n, 'seconds').asSeconds(), 'second');
    };

    @Input()
    projectId: number;

    @Input()
    companyId: number;

    @Input()
    project: Project;

    @Input()
    day_of_yr: string;

    @Input()
    showMedicalInfo: boolean = true;

    @Input()
    hideTitle: boolean = false;

    @Input()
    filterParam: any = {};

    @Input()
    searchText: string;

    @Input()
    filterCompany;

    tableLoading: boolean = false;

    visitor_logs: Array<any> = [];
    temp_visitor_logs: Array<any> = [];

    shiftConfigLoaded: Boolean = false;
    shiftConfigs: Array<any> = [];

    has_selected_today : boolean = false;
    isInitVisitorLog: boolean = false;
    loadingInlineVisitorLog: boolean = false;

    constructor(
        public timeUtility: TimeUtility,
        private optimaService: OptimaService,
        private visitorService: VisitorService,
        public ngbMomentjsAdapter: NgbMomentjsAdapter,
        private toastService: ToastService,
    ) {
    }

    ngOnInit(): void {
        if (!this.isInitVisitorLog) {
          this.isInitVisitorLog = true;
        }
        // this.getVisitorLogsForDate(this.day_of_yr);
    }

    ngOnChanges() {
        this.getVisitorLogsForDate(this.day_of_yr);
        this.has_selected_today = this.day_of_yr && dayjs(this.day_of_yr, this.dbDateFormat).isSame(dayjs(), 'day');
    }

    private getVisitorLogsForDate(day_of_yr){
        if (!this.isInitVisitorLog) {
          this.tableLoading = true;
        } else {
          this.loadingInlineVisitorLog = true;
        }
        this.visitorService.getVisitorTimeLogsByDay(this.projectId, day_of_yr, this.companyId, this.filterParam).subscribe((data: any) => {
            this.tableLoading = false;
            this.loadingInlineVisitorLog = false;
            if(data.visitor_logs){
                this.visitor_logs = (data.visitor_logs || []).map(ir => {
                    ir.geo_fence = {
                        clock_in: this.timeUtility.isGeoFenceLog(ir.events, 'IN', ir.clock_in),
                        clock_out: this.timeUtility.isGeoFenceLog(ir.events, 'OUT', ir.clock_out)
                    };
                    return ir;
                });
                this.temp_visitor_logs = this.visitor_logs
                this.filterVisiterLog()
            }else{
                this.visitor_logs = [];
                const message = data.message || 'Failed to fetch visitor time data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
            this.getVisitorSiteConfigs();
        });
    }


    isToday(row: any = {}): boolean {
        let still_in = false;
        if (row.clock_in) {
            let hrs_since_started = dayjs().diff(dayjs.unix(+row.clock_in), 'h');
            if (hrs_since_started <= 16) {
                // seems time log of today
                still_in = true;
            }
        }
        return still_in || this.has_selected_today;
    }

    timeSoFar(recentIn: any, secondsOnly = false): string|number {
        let seconds =  dayjs().unix() - (+recentIn);
        if(secondsOnly){
            return seconds;
        }
        return seconds ? dayjs().startOf('day').add(dayjs.duration(seconds, 'seconds').asSeconds(), 'second').format('HH:mm:ss') : '-';
    }

    private getVisitorSiteConfigs(){
        this.shiftConfigLoaded = false;
        if(this.visitor_logs && this.visitor_logs.length){

            let timestamp =  dayjs(this.day_of_yr, this.dbDateFormat).add(1, 'h').unix();
            let visitorIds = this.visitor_logs.map(ir => {
                return {
                    visitor_ref: ir.visitor_id,
                    timestamp: (+ir.clock_in || timestamp)
                }
            });

            this.optimaService.getUserShiftConfigs(this.projectId, [], visitorIds).subscribe(data => {
                this.shiftConfigLoaded = true;
                if(data.configs){
                    this.shiftConfigs = data.configs;
                }else{
                    const message = data.message || 'Failed to fetch shift config.';
                    this.toastService.show(this.toastService.types.ERROR, message, { data: data });
                }
            });
        }else{
            this.shiftConfigLoaded = true;
            this.shiftConfigs = [];
        }
    }


    get getSelectedDay(){
        return this.day_of_yr && this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(this.day_of_yr, this.dbDateFormat)) || null;
    }

    getVisitorShift(visitorId){
        let config = (this.shiftConfigs || []).find(c => c.visitor_ref === visitorId) || {};
        return config.id ? this.getHhMmSs(this.createStartTime(config.start_time, config.tolerance_hrs)) : '-';
    }

    getHhMmSs = this.timeUtility.getHhMmSs;
    createStartTime = this.timeUtility.createStartTime;

    @ViewChild('shiftConfiguration', { static: true }) shiftConfigurationRef: ShiftConfigurationProviderComponent;

    manageVisitorShiftConfig(event, row, user_meta = {}){
        if(this.shiftConfigurationRef && this.shiftConfigurationRef.manageUserShiftConfig){
            let shiftConfigReadOnly = (this.shiftConfigs || []).find(c => c.visitor_ref === row.visitor_id) || {};
            this.shiftConfigurationRef.manageUserShiftConfig(event, row, shiftConfigReadOnly);
        }
    }

    onShiftChange($event){
        this.getVisitorLogsForDate(this.day_of_yr);
    }

    @ViewChild('viewVisitorLogModal') viewVisitorLogModalRef: ViewVisitorLogComponent;

    openVisitorLogDetail(event, {visitor_id}, {prop}){
        event.target.closest('datatable-body-cell').blur();
        if (this.viewVisitorLogModalRef && this.viewVisitorLogModalRef.openTimeLogDetail) {
            this.viewVisitorLogModalRef.openTimeLogDetail({id: visitor_id}, 0,  prop, this.day_of_yr,() => {
            });
        }
    }

    @ViewChild('viewVisitorRmcDetail', { static: true }) viewVisitorRmcDetailRef: ViewVisitorRmcDetailComponent;
    openMedicalConditionsModal(event, row: any) {
        event.target.closest('datatable-body-cell').blur();
        this.viewVisitorRmcDetailRef.openViewMedicalConditionsModal(row);
    }

    filterVisiterLog() {
        const val = this.searchText.toLowerCase();
        let values = [...this.filterCompany];
        const tempTempVisitorLog = this.temp_visitor_logs.filter(function (d) {
            let searchFlag = true
            if (val != "") {
                searchFlag = (
                    (d.name && d.name.toLowerCase().indexOf(val) !== -1) ||
                    (d.employer && d.employer.toLowerCase().indexOf(val) !== -1) ||
                    !val
                )
            }
            let employerFlag = true
            if (values.length !== 0) {
                employerFlag = (d.employer && values.includes(d.employer.toLowerCase()))
            }
            return (searchFlag && employerFlag);
        });

        this.visitor_logs = tempTempVisitorLog;
    }
}

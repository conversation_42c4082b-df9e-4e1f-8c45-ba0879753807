<block-loader [show]="tableLoading" [showBackdrop]="true" alwaysInCenter="true"></block-loader>
<div class="mt-4">
    <h5 class="m-0" *ngIf="!hideTitle">Visitors</h5>
    <div class="table-responsive-sm" *ngIf="!tableLoading">
        <ngx-datatable
            #visitorTable
            class="bootstrap ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
            [scrollbarV]="true"
            [virtualization]="false"
            [loadingIndicator]="loadingInlineVisitorLog"
            [rows]="visitor_logs"
            [columns]="[
                {name:'Visitor', prop: 'name', cellTemplate: visitorNameColumn, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                {name:'Company', prop: 'employer', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                {name:'Job Role', prop: 'job_role', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                {name:'In time', prop: 'clock_in', cellTemplate: timestampCellTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', width:125, sortable: false, minWidth:'100'},
                {name:'Out time', prop: 'clock_out', cellTemplate: timestampCellTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', width:125, sortable: false, minWidth:'100'},
                {name:'Total time', prop: 'duration_in_sec', cellTemplate: totalInTimeCell, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                {name:'Travel time', prop: 'travel_time', cellTemplate: travelTimeCell, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'},
                {name:'total', cellTemplate: totalTimeCell, headerTemplate: totalTimeHeadTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', width:110, sortable: false, minWidth:'100'},
                {name:'distance_traveled', prop: 'travel_time.distance_traveled', cellTemplate: distanceTCellTemplate, headerTemplate: distanceTHeadTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', width:120, sortable: false, minWidth:'120'},
                {name:'Shift starts At', cellTemplate: shiftStartTimeCell, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', sortable: false, minWidth:'100'}]"
            [columnMode]="'force'"
            [headerHeight]="30"
            [footerHeight]="36"
            [rowHeight]="'auto'">
            <ng-template #visitorNameColumn let-row="row" let-value="value">
                <div class="d-flex p-0 m-0">
                    <div *ngIf="showMedicalInfo && row.has_rmc && row.rmc_detail" style="width: 18px;">
                    <span (click)="openMedicalConditionsModal($event, row)" role="button">
                        <img class="mr-1 mb-1" src="/assets/images/files-medical-icon-red.svg">
                    </span>
                    </div>
                    <span>{{ value }}</span>
                </div>
            </ng-template>
            <ng-template #timestampCellTemplate let-value="value" let-row="row" let-column="column">
                {{ value ? unix(+value).format('HH:mm:ss') : '-'}}
                <i *ngIf="row?.geo_fence[column.prop]" class="cursor-pointer fas fa-info-circle" (click)="openVisitorLogDetail($event, row, column)"></i>
            </ng-template>
            <ng-template #totalInTimeCell let-row="row" let-value="value">
                {{ value ? getTimeFromSeconds(+row.effective_time).format('HH:mm:ss') : '' }}

                <div *ngIf="!value">
                    <div *ngIf="row.clock_in && (isToday(row))" class="text-success"> {{ timeSoFar(row.clock_in) }}*</div>
                </div>
            </ng-template>
            <ng-template #travelTimeCell let-row="row" let-value="value">
                {{ timeUtility.getTotalTravelDuration(value, 'minutes') }}
            </ng-template>
            <ng-template #totalTimeHeadTemplate>
                Total Time <br/><small>(inc. Travel)</small>
            </ng-template>
            <ng-template #totalTimeCell let-row="row">
                <div *ngIf="row.effective_time"> {{ timeUtility.getTotalTime(row.effective_time, row.travel_time) }} </div>

                <div *ngIf="!row.effective_time">
                    <div *ngIf="row.clock_in && (isToday(row))" class="text-success">{{ timeUtility.getTotalTime(timeSoFar(row.clock_in, true), row.travel_time) }}*</div>
                </div>
            </ng-template>

            <ng-template #distanceTHeadTemplate>
                Distance Travelled <br/><small>(km)</small>
            </ng-template>
            <ng-template #distanceTCellTemplate let-value="value">
                {{ (value && +value) ? ((+value) / 1000).toFixed(2) : null }}
            </ng-template>
            <ng-template #shiftStartTimeCell let-row="row">
                <div *ngIf="shiftConfigLoaded" (click)="manageVisitorShiftConfig($event, row)"
                     class="cursor-pointer text-info btn-link">{{ getVisitorShift(row.visitor_id) }}</div>
            </ng-template>

        </ngx-datatable>
    </div>
</div>

<shift-configuration-provider
    #shiftConfiguration
    [projectId]="projectId"
    [selectedDay]="getSelectedDay"
    (shiftChanged)="onShiftChange($event)">
</shift-configuration-provider>


<view-visitor-log-modal
    #viewVisitorLogModal
    [showEditTemperature]="false"
    [showTemperature]="false"
    [tz]="project?.custom_field?.timezone"
    [projectId]="projectId">
</view-visitor-log-modal>

<view-visitor-rmc-detail #viewVisitorRmcDetail></view-visitor-rmc-detail>
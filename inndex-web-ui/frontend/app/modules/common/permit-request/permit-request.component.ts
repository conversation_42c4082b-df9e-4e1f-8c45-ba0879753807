import {Component, OnInit, TemplateRef, ViewChild} from "@angular/core";
import {
    isInheritedProjectOfCompany,
    PermitRequest,
    PermitService,
    Project,
    HttpService,
    Common,
    PermitRequestActionButtons,
    PERMIT_STATUSES,
    ToastService,
} from "@app/core";

import {ActivatedRoute} from "@angular/router";
import {HttpParams} from "@angular/common/http";
import {AppConstant} from "@env/environment";
import * as dayjs from "dayjs";
import {ActionBtnEntry, AssetsUrl, IModalComponent} from "@app/shared";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {saveAs} from "file-saver";
import { ShareToolReportToEmailComponent } from '../share-tool-report-to-email/share-tool-report-to-email.component';

@Component({
    templateUrl: './permit-request.component.html',
    styleUrls: ['./permit-request.component.scss']
})

export class PermitRequestComponent implements OnInit {
    AssetsUrlSiteAdmin = AssetsUrl.siteAdmin;
    defaultDateFormat = AppConstant.defaultDateFormat;
    defaultTimeFormat = AppConstant.defaultTimeFormat;
    timFormatWithoutSecs = AppConstant.timFormatWithoutSecs;
    dateTimeFormat_D_MMM_YYYY_HH_MM_SS = AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS;
    dayjs = dayjs;
    isProjectPortal: boolean = false;
    projectResolverResponse: any = {};
    projectInfo: any = {};
    projectId: number = null;
    permitRequests: Array<PermitRequest> = [];
    permitRequest: PermitRequest = new PermitRequest;
    permitTypes: Array<string> = [];
    permitLocations: Array<string> = [];
    employer: any = {};
    employerId: number = null;
    companyResolverResponse: any;
    is_inherited_project: boolean = false;
    isMobileDevice: boolean = false;
    paginationData = new Common();
    page = this.paginationData.page;
    permitPhrase: string = 'Permits';
    permitSglrPhrase: string = 'Permit';
    search: string = '';
    pageLoading: boolean = false;
    processingLoader: boolean = false;
    allStatus: Array<{
        status: number,
        status_message: string,
    }> = PERMIT_STATUSES;
    statusFilter: Array<number> = [];
    permitTypeFilter: Array<string> = [];
    fillablePdf: any = {};
    filter: {
        permitTypes: any[];
        permitLocations: any[];
        statuses: any[];
        search: string;
    } = {
        permitTypes: [],
        permitLocations: [],
        statuses: [],
        search:"",
    };
    filterData: any = this.renderFilterData();
    isFilterDataRendered: boolean = false;
    showModal: boolean = false;

    isRegisterChecked: boolean = false
    isAttachmentChecked: boolean = false
    downloadPDFModalTitle: string = 'PDF Download'
    actionButtonMetaData = {
        isTraining: false,
        actionList: [],
        class: 'material-symbols-outlined',
    };
    loadPowerBiComponent: boolean = false;
    biToolName: string = 'permits';
    biToolLabel: string = `Permit`;
    biModalTitle: string = 'Permit Dashboard';
    isInitPermitReq: boolean = false;
    loadingInlinePermitReq: boolean = false;
    rowButtonGroup: Array<ActionBtnEntry> = [];
    columns: Array<any> = [];

    @ViewChild('recordIdTmpl', { static: true }) recordIdTmpl!: TemplateRef<any>;
    @ViewChild('permitTypeTmpl', { static: true }) permitTypeTmpl!: TemplateRef<any>;
    @ViewChild('requestedByTmpl', { static: true }) requestedByTmpl!: TemplateRef<any>;
    @ViewChild('dateRequestedTmpl', { static: true }) dateRequestedTmpl!: TemplateRef<any>;
    @ViewChild('startOnDateTimeTmpl', { static: true }) startOnDateTimeTmpl!: TemplateRef<any>;
    @ViewChild('locationTmpl', { static: true }) locationTmpl!: TemplateRef<any>;
    @ViewChild('statusLabelTmpl', { static: true }) statusLabelTmpl!: TemplateRef<any>;
    @ViewChild('actionTmpl', { static: true }) actionTmpl!: TemplateRef<any>;

    constructor(
        private permitService: PermitService,
        private activatedRoute: ActivatedRoute,
        private httpService: HttpService,
        private modalService: NgbModal,
        private toastService: ToastService,
    ) {
        this.isMobileDevice = this.httpService.isMobileDevice();
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
        }
    }

    ngOnInit() {
        if(!this.isProjectPortal) {
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
                this.employerId = params['employerId'];
            });
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        } else {
            this.initializeTable();
        }

        this.actionButtonMetaData.actionList = [
            {
                code: PermitRequestActionButtons.DASHBOARD,
                name: `View Dashboard`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'dashboard',
                enabled: true,
            },
            {
                code: PermitRequestActionButtons.DOWNLOAD_REGISTER,
                name: `Download Permit Register`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: true,
            },
        ];
    }

    openModal(modalRef, size = 'sm', windowClass = "") {
        return this.modalService.open(modalRef, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size,
            windowClass: windowClass,
            beforeDismiss: () => {
                console.log("Modal close");
                return true;
            },
        });
    }

    initializeTable(isPageChange?: boolean) {
        if (isPageChange) {
          this.loadingInlinePermitReq = true;
        } else {
          this.pageLoading = true;
        }
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('is_inherited_project', `${this.is_inherited_project}`)
            .set('sortKey', 'status');

        let { search, permitTypes, permitLocations, statuses } = this.filter;

        if(search) {
            params = params.append('q',`${search}`);
        }

        if (permitTypes.length) {
            params = params.append('permit_type', `${permitTypes.join(",")}`)
        }

        if (permitLocations.length) {
            params = params.append('permit_location', `${permitLocations.join(",")}`)
        }

        if (statuses.length) {
            params = params.append('status', `${(statuses.map(item => item.status)).join(",")}`);
        }

        this.permitPhrase = this.projectInfo ? this.projectInfo.custom_field.permit_phrase : '';
        this.permitSglrPhrase = this.projectInfo ? this.projectInfo.custom_field.permit_phrase_singlr : this.permitSglrPhrase;
        this.buildButtonGroup();

        this.permitService.getPermitRequestsByProject(this.projectId, params).subscribe((data:any) => {
            this.pageLoading = false;
            this.loadingInlinePermitReq = false;
            let beforeLocationColumns = [
                {name:'Permit #', prop:'record_id', headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'py-1 table-cell-text', minWidth: 80, sortable: false, cellTemplate: this.recordIdTmpl},
                {name:'Permit Type', prop:'type_of_equipment_name', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', minWidth: 100, sortable: false, cellTemplate: this.permitTypeTmpl},
                {name:'Requested By', prop:'tagged_owner', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', minWidth: 110, sortable: false, cellTemplate: this.requestedByTmpl},
                {name:'Date Requested', prop:'createdAt', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', minWidth: 120, sortable: false, cellTemplate: this.dateRequestedTmpl},
                {name:'Start On', prop:'start_on', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', minWidth: 100, sortable: false, cellTemplate: this.startOnDateTimeTmpl}
            ];
            let locationColumn = [{name:'Location', prop:'location', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', minWidth: 100, sortable: false, cellTemplate: this.locationTmpl}]
            let afterLocationColumns = [
                {name:'Status', prop: 'status_label', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1', minWidth: 100,  sortable: false, cellTemplate: this.statusLabelTmpl},
                {name:'Action', prop:'fault_count', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text no-ellipsis', minWidth: 100, sortable: false, cellTemplate: this.actionTmpl}
            ];
            let defaultColumns = [
                ...beforeLocationColumns,
                ...locationColumn,
                ...afterLocationColumns,
            ];
            if (data.success && data.permit_requests) {
                let hasLocation = data.permit_requests.findIndex(pr => pr.tags);
                if (hasLocation > -1) {
                    this.columns = defaultColumns;
                } else {
                    this.columns = [
                        ...beforeLocationColumns,
                        ...afterLocationColumns,
                    ];
                }

                this.permitRequests = data.permit_requests;
                this.page.totalElements = data.totalCount;

                if (!this.isFilterDataRendered) {
                    this.permitTypes = data.permit_types;
                    this.permitLocations = data.permit_locations;
                    this.filterData = this.renderFilterData(true);
                }

                return;
            }
            this.columns = defaultColumns;
            const message = 'Something went wrong while fetching project permit requests.';
            this.toastService.show(this.toastService.types.ERROR, message);
        })
    }

    buildButtonGroup() {
        this.rowButtonGroup = [
            {
                key: 'preview_pdf',
                label: '',
                title: 'Preview Pdf',
                mat_icon: 'search',
            },
            {
                key: 'download_pdf',
                label: '',
                title: 'Download Pdf',
                mat_icon: 'download',
            },
            {
                key: 'audit_trail',
                label: '',
                title: 'Audit Trail',
                mat_icon: 'checklist',
            },
            {
                key: 'share_permit',
                label: '',
                title: `Share ${this.permitSglrPhrase} Details`,
                mat_icon: 'share',
            },
        ];
    }


    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actionMap = {
            'preview_pdf': () => this.viewDownloadPermitPdf(row),
            'download_pdf': () => this.viewDownloadPermitPdf(row, 'download'),
            'audit_trail': () => this.viewAuditTrail(row),
            'share_permit': () => this.openSharePermitModal(row),
        };
        const action = actionMap[entry.key];
        if (action) {
            action();
        }
    }

    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }) {
        this.page.pageNumber = pageInfo.offset;
        if (!this.isInitPermitReq) {
          this.isInitPermitReq = true;
          return;
        }
        this.initializeTable(true);
    }

    // Will get called for Company route only.
    projectRetrieved(project: Project) {
        this.projectInfo = project;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
        this.initializeTable();
    }

    @ViewChild('auditTrailModal') private auditTrailModalRef: TemplateRef<any>;
    viewAuditTrail(row) {
        this.processingLoader = true;
        this.permitService.getPermitRequestById(this.projectId, row.id).subscribe((data:any) => {
            this.processingLoader = false;
            if (data && data.success) {
                this.permitRequest = data.permit_request;
                this.openModal(this.auditTrailModalRef, 'lg', "modalHeightAuto modal_v2 l-modal");
                return true;
            }
            const message = 'Something went wrong while fetching the permit request.';
            this.toastService.show(this.toastService.types.ERROR, message);
            return;
        });
    }

    trackByRowIndex(index: number, obj: any){
        return index;
    }

    @ViewChild('previewPdfDownloadModalRef') private previewPdfDownloadModalRef: IModalComponent;
    downloadPermitRequestsModal() {
        this.showModal = true;
        this.previewPdfDownloadModalRef.open();
    }

    downloadPermitRequests() {
        this.processingLoader = true;
        this.permitService.downloadPermitRequests(this.projectId, {}, () => {
            this.processingLoader = false;
        });
    }

    @ViewChild('previewPdfModalRef') private previewPdfModalRef: IModalComponent;
    fetchPermitDocument(row, params, cb) {
        this.permitRequest = row;
        this.processingLoader = true;
        this.permitService.viewPermitDocument(this.projectId, row.id, params).subscribe((data: any) => {
            this.processingLoader = false;
            if (data.success && data.filled_pdf) {
                this.fillablePdf = data.filled_pdf;
                cb();
            } else {
                const message = 'Something went wrong while getting permit detail.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }
    
    viewDownloadPermitPdf(row, action = 'view') {
        let params = new HttpParams()
        .set('operative_register', 'true')
        .set('attachment_type', 'grid')
        .set('action', action)

        if (action === 'view') {
            this.fetchPermitDocument(row, params, () => {
                this.showModal = true;
                this.previewPdfModalRef.open();
            });
        } else {
            const permitRef = row.permit_ref
            this.downloadPDFModalTitle = `${permitRef.ref_number} - ${permitRef.permit_type} Download`
            this.permitRequest = row;
            this.isRegisterChecked = false
            this.isAttachmentChecked = false
            if(!row.has_register && !row.mandatory_attachments.length){
                this.downloadPermitPdf();
                return
            }
            this.showModal = true;
            this.downloadPermitRequestsModal();
        }
    }
    
    downloadPermitPdf() {
        let params = new HttpParams()
            .set('action', 'download');
        if (this.isRegisterChecked) {
            params = params.append('operative_register', 'true');
        }

        if (this.isAttachmentChecked) {
            params = params.append('attachment_type', 'full');
        }
        else if (this.permitRequest.mandatory_attachments) {
            params = params.append('attachment_type', 'grid');
        }

        this.fetchPermitDocument(this.permitRequest, params, () => {
            saveAs(this.fillablePdf.file_url, this.fillablePdf.file_name);
        });
    }
    

    closeModal() {
        this.showModal = false;
        this.isRegisterChecked = false;
        this.isAttachmentChecked = false;
    }

    onFilterSelection(data) {
        this.filter.permitTypes = data['permit type'].map(a => a);
        this.filter.permitLocations = data['permit location'].map(a => a);
        this.filter.statuses = [...data.status];
        this.filter.search = data.search;

        this.initializeTable(true);
    }

    searchFunction(data) {
        this.filter.search = data.search.trim().toLowerCase();
        this.initializeTable(true);
    }

    renderFilterData(isRendered = false) {
        this.isFilterDataRendered = isRendered;
        return [
            {
                name: 'permit type',
                list: this.permitTypes,
                enabled: this.permitTypes.length,
                state: false,
            },
            {
                name: 'permit location',
                list: this.permitLocations,
                enabled: this.permitLocations.length,
                state: false,
            },
            {
                name: 'status',
                list: this.allStatus,
                enabled: this.allStatus.length,
                state: false,
            },
        ];
    }

    getLogMetaInfo(status_log, type='label') {
        let status_meta = (status_log.status_meta || {});
        return (status_meta[type] || null);
    }

    @ViewChild('sharePermitModal') sharePermitModalRef: ShareToolReportToEmailComponent;
    openSharePermitModal(row) {
        this.sharePermitModalRef.openEmailFormModal(row);
    }

    sharePermit(event) {
        console.log(event);
        this.permitService.sharePermit(this.projectId, event.req, event.reportId).subscribe((res: any) => {
            event.cb(res);
        });
    }

    public onActionSelection(actionVal: any) {
        const code = actionVal.code;
        this.actionButtonMetaData.isTraining = false;
        if(code === PermitRequestActionButtons.DASHBOARD) {
            this.openDashboardModal();
        } else if(code === PermitRequestActionButtons.DOWNLOAD_REGISTER) {
            this.downloadPermitRequests();
        }
    }

    openDashboardModal() {
        this.loadPowerBiComponent = true;
        this.biModalTitle = `${this.biToolLabel} Dashboard`;
    }

    powerBiDashboardClose() {
        this.loadPowerBiComponent = false;
    }

    getTagsValue(tags, valueType) {
        if (!tags) return '-';
        let locations = tags.split('|').filter(tag => tag.startsWith(`${valueType}=`))
            .map(tag => tag.split(`${valueType}=`)[1]);
        return locations.join(', ') || '-';
    }
}

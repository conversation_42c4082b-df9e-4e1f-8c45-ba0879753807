import {Component, Input, OnInit} from '@angular/core';
import * as dayjs from 'dayjs';
import {InductionRecentChange} from "@app/core";
import {AppConstant} from "@env/environment";

@Component({
    selector: 'induction-change-log',
    templateUrl: './induction-change-log.component.html',
    styleUrls: ['./induction-change-log.component.scss']
})
export class InductionChangeLogComponent implements OnInit {

    @Input() activeIds = ['list0'];
    dateDisplayFormat = AppConstant.defaultDateFormat;
    CHANGE_TYPES = {
        'profile.personal:updated': 'Personal Detail',
        'profile.contact:updated': 'Contact Detail',
        'profile.employment:updated': 'Employment Detail',
        'profile.health:updated': 'Health Assessment',
        'profile.medical:updated': 'Medical Assessment',
        'profile.doc:updated': 'Competency',
    };

    CONTACT_DETAIL_LABELS = {
        'house_no': 'House No.',
        'street': 'Address Line',
        'city': 'City',
        'post_code': 'Post Code',
        'country': 'Country',
        'home_number': 'Home Phone No.',
        'mobile_number': 'Mobile No.',
        'emergency_contact': 'Emergency Contact',
        'emergency_contact_number': 'Emergency Contact No.',
    };

    COMPETENCY_LABELS = {
        'name': 'Name',
        'doc_number': 'Document No.',
        'expiry_date': 'Expiry Date',
        'description': 'Description',
        'expiry_date_key': 'expiry_date',
    };
    
    CONTACT_NUMBER_LABELS = [
        "home_number",
        "mobile_number",
        "emergency_contact_number",
    ];

    @Input() changes: Array<InductionRecentChange> = [];
    @Input() tz: string;

    _changes: Array<any>;

    constructor() {
    }

    ngOnInit(): void {
        // console.log('got list of changes', this.changes);
        this.updateChangesForContactDetail()
        let objects = (this.changes || []).sort((a, b) => (b.timestamp - a.timestamp)).reduce((result, row) => {
            let day = dayjs(+row.timestamp).format('DD/MM/YYYY');
            if (!result[day]) {
                result[day] = {
                    day: day,
                    changes: []
                };
            }
            let list = (row.changes || []).map(c => {
                return {
                    type: row.type,
                    ...c,
                }
            });
            if (list.length) {
                result[day].changes.push(...list)
            }
            return result;
        }, []);

        this._changes = Object.values(objects);
        console.log('got result of changes', this._changes);
    }

    updateChangesForContactDetail() {
        this.changes.forEach(({changes}) => {
            changes.forEach((contact) => {
                if(this.CONTACT_NUMBER_LABELS.includes(contact.entity)){
                    contact.to = this.formatNumber(contact.to);
                    contact.from = this.formatNumber(contact.from);
                }
            });
        });
    }

    formatNumber(phone) {
        return typeof phone === 'object' ? `(+${phone?.code}) ${phone?.number}` : phone;
    }

    dayjs(n: number, format?: any) {
        let tz = this.tz;
        return dayjs(n, format).tz(tz);
    }

    unix(n: number) {
        return dayjs(n);
    };

    numberToYesNo(num) {
        if (+num === 1) {
            return 'Yes';
        } else if (+num === 0) {
            return 'No';
        } else if (num === null) {
            return '-';
        }
        return num;
    }

    changeTypeToLabel(type){
        return this.CHANGE_TYPES[type] || type;
    }
}

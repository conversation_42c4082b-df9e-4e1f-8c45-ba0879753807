<ngb-accordion [activeIds]="activeIds" class="i-accordion">
    <ngb-panel *ngFor="let data of _changes; let changeLogIndex= index;" [id]="'list'+ changeLogIndex" cardClass="panel-class accord-table-pad mb-2 custom-accord-border">
        <ng-template ngbPanelHeader let-opened="opened">
            <button ngbPanelToggle class="btn p-0">
                <div class="w-100 d-flex justify-content-between">
                    <div>
                        <p class="m-0 fw-600">Profile Updated: {{data.day}}</p>
                    </div>
                    <div>
                        <i [ngClass]="opened ? 'fas fa-angle-up' : 'fas fa-angle-down'"></i>
                    </div>
                </div>
            </button>
        </ng-template>
        <ng-template ngbPanelContent>
            <table class="table table-bordered mb-0 border-0">
                <thead>
                <tr>
                    <th class="table-head-cat-w" scope="col">Category</th>
                    <th class="table-head-details-w" scope="col">Details</th>
                    <th class="table-head-changedfrom-w" scope="col">Changed From</th>
                    <th scope="col">Changed To</th>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let row of data.changes;">
                    <td>
                        <div>
                            {{changeTypeToLabel(row.type)}}
                        </div>
                    </td>
                    <td>
                        <div *ngIf="row.type === 'profile.contact:updated'">
                            {{ CONTACT_DETAIL_LABELS[row.entity] || row.entity}}
                        </div>
                        <div *ngIf="row.type === 'profile.doc:updated'">
                            {{row.entity}}
                            <span *ngIf="row.sub_entity">({{COMPETENCY_LABELS[row.sub_entity] || row.sub_entity}})</span>
                            <i class="text-muted" *ngIf="row.newly_added">(Newly Added)</i>
                            <i class="text-muted" *ngIf="row.removed">(Removed)</i>
                        </div>
                        <div *ngIf="!['profile.contact:updated','profile.doc:updated'].includes(row.type)">
                            {{row.entity}}
                            <span *ngIf="row.sub_entity">({{row.sub_entity}})</span>
                            <i class="text-muted" *ngIf="row.newly_added">(Newly Added)</i>
                            <i class="text-muted" *ngIf="row.removed">(Removed)</i>
                        </div>
                    </td>

                    <td>
                        <div *ngIf="['profile.health:updated', 'profile.medical:updated'].includes(row.type); else elseNotBool">
                            {{numberToYesNo(row.from)}}
                        </div>
                        <ng-template #elseNotBool>
                            <span *ngIf="row.sub_entity === COMPETENCY_LABELS.expiry_date_key && row.from; else elseBlock">
                                {{dayjs(+row.from).format(dateDisplayFormat)}}
                            </span>
                            <ng-template #elseBlock>{{row.from}}</ng-template>
                        </ng-template>
                    </td>
                    <td>
                        <div *ngIf="['profile.health:updated', 'profile.medical:updated'].includes(row.type); else elseNotBool2">
                            {{numberToYesNo(row.to)}}
                        </div>
                        <ng-template #elseNotBool2>
                            <span *ngIf="row.sub_entity === COMPETENCY_LABELS.expiry_date_key && row.to; else elseBlock2">
                                {{dayjs(+row.to).format(dateDisplayFormat)}}
                            </span>
                            <ng-template #elseBlock2>{{row.to}}</ng-template>
                        </ng-template>
                    </td>
                </tr>
                </tbody>
            </table>
        </ng-template>
    </ngb-panel>
</ngb-accordion>

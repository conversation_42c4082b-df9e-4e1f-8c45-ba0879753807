<style>
    agm-map {
        /*height:83vh;*/
        height:50vh;
        width:100%
    }
    .location{
        color: blue;
    }
    .location:hover{
        cursor: pointer;
        text-decoration: underline;
    }
</style>
<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav *ngIf="!isProjectPortal" [employer]="employer" [companyResolverResponse]="companyResolverResponse" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <ng-container *ngIf="!loadingCloseCalls">
            <div class="col-sm-12 my-3 outer-border">
                <div class="outer-border-radius">
            <div class="col-sm-12">
                <div class="mb-2 pb-2 d-flex justify-content-between flex-wrap gap-8">
                    <h5 class="float-md-left">{{ (projectInfo ? projectInfo.custom_field.cc_phrase : '') }} Total
                        <small>
                            (Open {{ openRecords }} : Raised {{ page.totalElements }})
                        </small>
                    </h5>
                    <action-button
                        [actionList]="actionButtonMetaData.actionList"
                        (selectedActionEmmiter)="onActionSelection($event)"
                        [newFeatureTitle]="'New ' + this.ccSglrPhrase"
                        (onOpenAddNew)="openAddNewCloseCallModal()">
                    </action-button>
                </div>
            </div>
            <div class="form-group pr-0 ml-3">
                <search-with-filters [filterData]="filterData" (searchEmitter)="searchFunction($event)" (filterEmitter)="onFilterSelection($event)"></search-with-filters>
            </div>
            <div class="col-sm-12 mt-2">
                <div class="table-responsive-sm">
                    <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                   [scrollbarV]="true"
                                   [virtualization]="false"
                                   [loadingIndicator]="loadingInlineCloseCall"
                                   [rows]="records"
                                   [footerHeight]="40"
                                   [columnMode]="'force'"
                                   [rowHeight]="'auto'"
                                   [externalPaging]="true"
                                   [count]="page.totalElements"
                                   [offset]="page.pageNumber"
                                   [limit]="page.size"
                                   (page)="pageCallback($event, true)"
                    >
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                {{ projectInfo?.custom_field?.cc_phrase_singlr }} #
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{row.cc_number}}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Raised
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{ dayjsDisplayDateOrTime(row.createdAt) }}
                                <div style="font-size: 11px;">({{ dayjsDisplayDateOrTime(row.createdAt, false) }})</div>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Raised By
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <div *ngIf="!row.is_anonymous" style="display: inline-grid;">
                                    <span appTooltip>{{row?.user_ref?.first_name}} {{row?.user_ref?.last_name}}</span>
                                    <span appTooltip style="font-size: 11px;">({{row?.user_employer?.employer}})</span>
                                </div>
                                <span *ngIf="row.is_anonymous">Anonymous</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Company
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span *ngIf="row?.tagged_owner" appTooltip>{{row?.tagged_owner?.name}}</span>
                                <span *ngIf="!row?.tagged_owner">-</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Category
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip *ngIf="row?.hazard_category">{{row?.hazard_category}}</span>
                                <span *ngIf="!row?.hazard_category">-</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Assigned To
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip *ngIf="row?.assigned_to">{{row?.assigned_to?.name}}</span>
                                <span *ngIf="!row?.assigned_to">-</span>
                            </ng-template>
                        </ngx-datatable-column>

                        <ngx-datatable-column headerClass="font-weight-bold action-column text-center"
                                              cellClass="action-column" [sortable]="false" minWidth="120">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Status
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <div class="text-center">
                                    <button *ngIf="row.status_message != 'Open'" class="btn btn-sm badge-pill shadow-none text-white badge-success d-block" style="width: 90px; margin: 0 auto;">
                                        {{ row?.status_message }}
                                    </button>
                                    <button *ngIf="row.status_message == 'Open'" (click)="closeCallDetailModal(row)" class="btn btn-sm shadow-none text-white badge-pill badge-danger cursor-pointer d-block" style="width: 90px; margin: 0 auto;">
                                        {{ row?.status_message }}
                                    </button>
                                </div>
                            </ng-template>
                        </ngx-datatable-column>

                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Closed Out
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{ getCloseOutDate(row.closed_out_date) }}
                            </ng-template>
                        </ngx-datatable-column>

                        <ngx-datatable-column headerClass="font-weight-bold action-column"
                                              cellClass="action-column no-ellipsis" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Action
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <button-group
                                    [buttons]="rowButtonGroup"
                                    [btnConditions]="[true, true, (row.status_message === 'Open' && isCloseoutAllowed())]"
                                    (onActionClick)="rowBtnClicked($event, row)">
                                </button-group>
                            </ng-template>
                        </ngx-datatable-column>
                    </ngx-datatable>

                    <app-generic-modal #closeCallDetailsHtml [genericModalConfig]="closeCallDetailsModalRefConfig">
                        <ng-container *ngTemplateOutlet="closeCallDetails"></ng-container>
                        <ng-template #closeCallDetails>
                                <table class="table table-sm table-bordered" style="font-size: 14px;">
                                    <tbody>
                                        <tr>
                                            <td class="tr-bg-dark-color" style="width: 25%;">
                                                <strong>Raised:</strong>
                                            </td>
                                            <td *ngIf="close_call_row?.createdAt">
                                                {{dayjs(close_call_row?.createdAt).format(dateFormat)}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="tr-bg-dark-color" style="width: 25%;">
                                                <strong>Raised By:</strong>
                                            </td>
                                            <td>
                                                <span *ngIf="!close_call_row?.is_anonymous">
                                                    {{close_call_row?.user_ref?.first_name}} {{close_call_row?.user_ref?.last_name}} ({{close_call_row?.user_employer?.employer}})
                                                </span>
                                                <span *ngIf="close_call_row?.is_anonymous"> Anonymous</span>
                                            </td>
                                        </tr>
                                        <tr *ngIf="close_call_row?.hasOwnProperty('hazard_category')">
                                            <td class="tr-bg-dark-color" style="width: 25%;">
                                                <strong>Hazard Category:</strong>
                                            </td>
                                            <td>
                                                <div class="d-flex my-1">
                                                    <div class="input-group ml-1">
                                                        <ng-select class="w-100" [items]="hazardousCategoryList" bindLabel="name" bindValue="name"
                                                            name="hazard_category" placeholder="Select Category"
                                                            [(ngModel)]="close_call_row.hazard_category" [disabled]="hazardCategoryEditDisable" required>
                                                        </ng-select>
                                                    </div>
                                                    <ng-container *ngIf="hazardCategoryEditDisable; else hazardCatEdit">
                                                        <button type="button" class="btn-act-editable border-0 d-flex align-items-center justify-content-center px-4 mr-1" (click)="enableEdit()">
                                                            <img class="img-icon pr-1" [src]="AssetsUrlSiteAdmin.pencilSquare" alt="">
                                                            <span class="mt-1">Edit</span>
                                                        </button>
                                                    </ng-container>
                                                    <ng-template #hazardCatEdit>
                                                        <button type="button" class="btn-act-editable border-0 pl-3 pr-4 mr-1" (click)="updateHazardCategory(close_call_row.hazard_category)">
                                                            <span class="mt-1">Save</span>
                                                        </button>
                                                    </ng-template>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr *ngIf="close_call_row?.lighting_conditions">
                                            <td class="tr-bg-dark-color" style="width: 25%;">
                                                <strong>Lighting Conditions:</strong>
                                            </td>
                                            <td>
                                                {{ close_call_row.lighting_conditions }}
                                            </td>
                                        </tr>
                                        <tr *ngIf="close_call_row?.location_and_description">
                                            <td class="tr-bg-dark-color p-1" style="width: 25%;">
                                                <strong>Location:</strong>
                                            </td>
                                            <td>
                                                {{ close_call_row.location_and_description }}
                                            </td>
                                        </tr>
                                        <tr *ngIf="close_call_row?.additional_detail">
                                            <td class="tr-bg-dark-color p-1" style="width: 25%;">
                                                <strong>Details:</strong>
                                            </td>
                                            <td style="white-space: normal; word-break: break-all;">
                                                {{ getDescription(close_call_row.additional_detail) }}
                                            </td>
                                        </tr>
                                        <tr *ngIf="close_call_row?.location">
                                            <td class="tr-bg-dark-color" style="width: 25%;">
                                                <strong>Location Tag (Lat, Long):</strong>
                                            </td>
                                            <td>
                                                <span class="location" *ngIf="close_call_row.location?.lat && close_call_row.location?.long" (click)="openMapWithPin()">
                                                    ({{close_call_row.location.lat | number : decimalConfig}}, {{close_call_row.location.long | number : decimalConfig}})
                                                </span>
                                            </td>
                                        </tr>
                                        <tr *ngIf="close_call_row?.cc_detail">
                                            <td class="tr-bg-dark-color" style="width: 25%;">
                                                <strong>What could have happened:</strong>
                                            </td>
                                            <td class="p-1">
                                                <span *ngIf="close_call_row?.cc_detail" [innerHtml]="replaceAll(close_call_row.cc_detail, '\n', '<br>')"></span>
                                            </td>
                                        </tr>
                                        <ng-template ngFor let-item [ngForOf]="(close_call_row.custom_fields)" let-i="index">
                                            <tr *ngIf="!item.is_default && item.is_active">
                                                <td class="tr-bg-dark-color" style="width: 25%;">
                                                    <strong>{{item.label}}:</strong>
                                                </td>
                                                <td>
                                                    {{item.value}}
                                                </td>
                                            </tr>
                                        </ng-template>
                                        <tr>
                                            <td class="tr-bg-dark-color" style="width: 25%;">
                                                <strong>{{(close_call_row.assigned_to?.name) ? "Assigned To:" : "Assign To:"}}</strong>
                                            </td>

                                            <td *ngIf="close_call_row.assigned_to?.name else selectUserToTag">
                                                {{close_call_row.assigned_to?.name}}
                                            </td>
                                            <ng-template #selectUserToTag>
                                                <td>
                                                    <ng-container *ngIf="this.selectedStatus === 1">
                                                        <form class="my-1 mx-1" novalidate #addForm="ngForm">
                                                        <inducted-user-selector
                                                            #userSelector
                                                            placeholder="Select User"
                                                            name="user_selector"
                                                            [projectId]="projectId"
                                                            [required]="true"
                                                            [selection]="tagUser"
                                                            (selectionChanged)="onUserSelection($event, userSelector)"
                                                        ></inducted-user-selector>
                                                        </form>
                                                    </ng-container>
                                                </td>
                                            </ng-template>
                                        </tr>
                                        <tr *ngIf="isProjectPortal">
                                            <td class="tr-bg-dark-color" style="width: 25%;">
                                                <strong>{{(close_call_row?.tagged_owner?.name) ? "Tagged Company:" : "Tag Company:" }}</strong>
                                            </td>
                                            <td *ngIf="close_call_row?.tagged_owner?.name else selectTaggedOwner">
                                                {{ close_call_row?.tagged_owner?.name }}
                                            </td>
                                            <ng-template #selectTaggedOwner>
                                                <td>
                                                    <ng-select [name]="companySelector" #companySelector [(ngModel)]="tagOwner"
                                                               class="dropdown-list my-1 mx-auto" appendTo="body" required style="width: 98%;" placeholder="Select Company"
                                                               (change)="tagOwnerRequest(companySelector)">
                                                        <ng-option *ngFor="let employer of inductedUsersEmployer" [value]="employer.id" >{{employer.name}}</ng-option>
                                                    </ng-select>
                                                </td>
                                            </ng-template>
                                        </tr>
                                        <tr *ngIf="imagesArray && imagesArray.length">
                                            <td colspan="2">
                                                <pop-up-image-viewer [updateStyle]="{'max-height.px': 150, 'max-width.px': 150}" [alt]="'Close Out Image'" [imgArray]="imagesArray || []"></pop-up-image-viewer>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                                <div class="mt-4" *ngIf="close_call_row?.closed_out_date && close_call_row?.corrective_detail"> 
                                    <div class="w-100">
                                        <h4>Closeout Details</h4>
                                    </div>
                                    <table class="table table-sm table-bordered" style="font-size: 14px;">
                                        <tbody>
                                            <ng-template [ngTemplateOutlet]="closeCallCloseoutDetail"></ng-template>
                                        </tbody>
                                    </table>
                                </div>
                        </ng-template>
                    </app-generic-modal>

                    <app-generic-modal #closeOutHtml [genericModalConfig]="closeOutModalRefConfig">
                        <ng-container *ngTemplateOutlet="closeOut"></ng-container>
                        <ng-template #closeOut>
                                <p class="text-center">{{ projectInfo?.custom_field?.cc_phrase_singlr }} number #{{ close_call_row?.cc_number }}</p>
                                <form novalidate #closeOutForm="ngForm">
                                    <div class="mb-2">
                                        <p class="mb-1">Description <small class="required-asterisk">*</small></p>
                                        <input type="hidden" name="close_call" id="cc_id"
                                            [(ngModel)]="close_call_row"/>
                                        <textarea class="form-control" name="corrective_detail"
                                                [(ngModel)]="close_call_row.corrective_detail"
                                                ng-value="close_call_row.corrective_detail"
                                                placeholder="Closeout Details"
                                                #closeOutDetail="ngModel" required></textarea>
                                        <div class="alert alert-danger" [hidden]="(!close_call_row?.corrective_detail || closeOutDetail.valid)">Close out detail is required</div>
                                    </div>

                                    <div class="mb-2 mt-2 col-md-12 p-0">
                                        <p class="mb-1">Upload Photo</p>
                                        <input type="hidden" name="corrective_images" id="corrective_images"
                                            [(ngModel)]="corrective_images"/>
                                    </div>
                                    <div class="col-md-12 p-0 mb-4">
                                        <div class="col-md-12 flex-grow-1 p-0" *ngFor="let c of close_out_images">
                                            <file-uploader-v2
                                                [disabled]="false" [multipleUpload]="true"
                                                [init]="c"
                                                [category]="'close-call'"
                                                (uploadDone)="uploadDone($event)"
                                                [allowedMimeType]="allowedMime"
                                                (deleteFileDone)="fileDeleteDone($event)"
                                                [showDeleteBtn]="true"
                                                [showFileName]="false"
                                                [hasImgAndDoc]="true"
                                            ></file-uploader-v2>
                                        </div>
                                    </div>
                                </form>
                        </ng-template>
                    </app-generic-modal>


                </div>
                <block-loader [show]="exportInProgress" [showBackdrop]="true"></block-loader>
                <block-loader [show]="updateRequestLoader" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
            </div>
        </div>
        </div>
        </ng-container>
        </div>
    </div>
</div>

<app-generic-modal #progressPhotosHtmlMap [genericModalConfig]="progressPhotoMapConfig">
    <ng-container *ngTemplateOutlet="cc"></ng-container>
    <ng-template #cc>
        <progress-photo-location-map
            *ngIf="showProgressPhotosHtmlMap"
            [showMapWithMultiPin]="showMapWithMultiPin"
            [row_data]="close_call_row"
            [project_data]="project_close_calls"
            [dismiss]="d"
            [mapTitle]="projectInfo?.custom_field.cc_phrase_singlr"
            feature="cc"
            (view)="closeCallDetailModal($event)">
        </progress-photo-location-map>
    </ng-template>
</app-generic-modal>

<power-bi-dashboard *ngIf="loadPowerBiComponent" (powerBiDashboardClose)="powerBiDashboardClose()" [modalTitle]="biModalTitle" [toolLabel]="biToolLabel" [toolName]="biToolName"></power-bi-dashboard>

<ng-template #closeCallCloseoutDetail>
    <tr>
        <td class="tr-bg-dark-color" style="width: 25%;">
            <strong>Closed Out By:</strong>
        </td>
        <td>
            {{ close_call_row?.closed_out_by ? close_call_row?.closed_out_by : ''}}
        </td>
    </tr>
    <tr>
        <td class="tr-bg-dark-color" style="width: 25%;">
            <strong>Closed Out:</strong>
        </td>
        <td>
            {{ close_call_row?.closed_out_date ? dayjs(+close_call_row?.closed_out_date).format(dateFormat) : ''}}
        </td>
    </tr>
    <tr>
        <td class="tr-bg-dark-color" style="width: 25%;">
            <strong>Description:</strong>
        </td>
        <td>
            {{ close_call_row?.corrective_detail }}
        </td>
    </tr>
    <tr *ngIf="closeOutImageArray && closeOutImageArray.length">
        <td colspan="2">
            <pop-up-image-viewer [updateStyle]="{'max-height.px': 150, 'max-width.px': 150}" [alt]="'Close Out Image'" [imgArray]="closeOutImageArray || []"></pop-up-image-viewer>
        </td>
    </tr>
</ng-template>

<!-- Start: New Close-Call -->
    <app-generic-modal #addCloseCallModalRef [genericModalConfig]="addCloseCallModalRefConfig">
        <form novalidate #addCloseCallForm="ngForm">
            <div class="form-group" *ngIf="checkIsActive('hazard_category')">
                <label class="d-inline-block"
                    >Category <small class="required-asterisk">*</small></label
                >
                <ng-select class="w-100 dropdown-list" appendTo="body" [items]="hazardousCategoryList" bindLabel="name" bindValue="name"
                    name="hazard_category" placeholder="Select Category"
                    [(ngModel)]="closeCallObj.hazard_category" required>
                </ng-select>
            </div>
            <div class="form-group" *ngIf="checkIsActive('lighting_conditions')">
                <label class="d-inline-block"
                    >Lighting Conditions <small class="required-asterisk">*</small></label
                >
                <ng-select class="w-100 dropdown-list" appendTo="body" [items]="lightingConditionList" bindLabel="name" bindValue="name"
                    name="lighting_conditions" placeholder="Select Lighting Condition"
                    [(ngModel)]="closeCallObj.lighting_conditions" required>
                </ng-select>
            </div>
            <div class="form-group" *ngIf="checkIsActive('location_and_description')">
                <label>Location <small class="required-asterisk">*</small></label>
                <div class="input-group mb-3">
                    <input
                        #location_and_description="ngModel"
                        [(ngModel)]="closeCallObj.location_and_description"
                        class="form-control"
                        name="location_and_description"
                        ng-value="closeCallObj.location_and_description"
                        placeholder="Location"
                        type="text"
                        required
                    />
                </div>
            </div>
            <div class="form-group" *ngIf="checkIsActive('additional_detail')">
                <label>Details <small class="required-asterisk">*</small></label>
                <div class="input-group mb-3">
                    <textarea
                        #additional_detail="ngModel"
                        [(ngModel)]="closeCallObj.additional_detail"
                        class="form-control"
                        name="additional_detail"
                        ng-value="closeCallObj.additional_detail"
                        placeholder="Details"
                        required
                    ></textarea>
                </div>
            </div>
            <div class="form-group" *ngIf="checkIsActive('cc_detail')">
                <label>What could have happened? <small class="required-asterisk">*</small></label>
                <div class="input-group mb-3">
                    <textarea
                        #cc_detail="ngModel"
                        [(ngModel)]="closeCallObj.cc_detail"
                        class="form-control"
                        name="cc_detail"
                        ng-value="closeCallObj.cc_detail"
                        placeholder="What could have happened?"
                        required
                    ></textarea>
                </div>
            </div>
            <ng-container *ngFor="let customField of closeCallCustomFields; let customFieldInd = index">
                <div class="form-group" *ngIf="customField.is_active">
                    <label>{{customField.label}} <small *ngIf="customField.is_mandatory" class="required-asterisk">*</small></label>
                    <div class="input-group mb-3">
                        <input *ngIf="customField.field_type === 'textbox'"
                            #customFieldTxtBox="ngModel"
                            [(ngModel)]="customField.value"
                            class="form-control"
                            name="{{customField.label}}"
                            ng-value="customField.value"
                            placeholder="{{customField.label}}"
                            type="text"
                            required
                        />
                        <textarea *ngIf="customField.field_type === 'textarea'"
                            #customFieldTxtArea="ngModel"
                            [(ngModel)]="customField.value"
                            class="form-control"
                            name="{{customField.label}}"
                            ng-value="customField.value"
                            placeholder="{{customField.label}}"
                            [required]="customField.is_mandatory"
                        ></textarea>
                        <ng-select *ngIf="customField.field_type === 'dropdown'"
                            class="w-100 dropdown-list" appendTo="body" [items]="sortCustomFieldOptions(customField.options)" bindLabel="label" bindValue="label"
                            name="label" placeholder="{{customField.label}}"
                            [(ngModel)]="customField.value"  [required]="customField.is_mandatory">
                        </ng-select>
                    </div>
                </div>
            </ng-container>
            <div class="form-group mt-4" *ngIf="checkIsActive('is_anonymous')">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6>Submit Anonymously</h6>
                    </div>
                    <div>
                        <input
                            type="radio"
                            class="form-check-input"
                            id="radio1"
                            name="optradio"
                            [value]="closeCallObj.is_anonymous"
                            style="
                                width: 1rem;
                                height: 1rem;
                                margin-top: 2px !important;"
                            [checked]="closeCallObj.is_anonymous"
                            (click)="changeIsAnnonymous()"
                        />
                    </div>
                </div>
            </div>
            <div class="form-group" >
                <label class="d-inline-block"
                    >Assign To <small *ngIf="isAssignToMandatory" class="required-asterisk">*</small></label
                >
                    <inducted-user-selector
                        placeholder="Select Assignee"
                        name="assignTo"
                        [projectId]="projectId"
                        [required]="isAssignToMandatory"
                        [selection]="closeCallObj?.assigned_to"
                        (selectionChanged)="onAssigneeSelection($event)"
                    ></inducted-user-selector>
            </div>
            <div class="form-group" *ngIf="closeCallObj">
                <label class="d-inline-block">Tag Company </label>
                <ng-select class="dropdown-list" appendTo="body" [items]="inductedUsersEmployer" bindLabel="name" bindValue="id"
                    name="tagCompany" placeholder="Select Company"
                    [(ngModel)]="closeCallObj.tagged_owner">
                </ng-select>
            </div>
            <div class="col-md-12 p-0 mt-3 mb-3"><h6>Add Photos</h6></div>
            <div class="col-md-12 p-0 mb-4">
                <div class="col-md-12 flex-grow-1 p-0"
                    *ngFor="let item of newPhotos"
                >
                    <file-uploader-v2
                        #imgUploader
                        (deleteFileDone)="imgDeleteDone($event)"
                        (uploadDone)="mediaUploadDone($event)"
                        [allowedMimeType]="allowedMime"
                        [category]="'close-call'"
                        [disabled]="false"
                        [init]="item"
                        [multipleUpload]="true"
                        [showDeleteBtn]="true"
                        [showFileName]="false"
                        [hasImgAndDoc]="true"
                        class="pl-0"
                    >
                    </file-uploader-v2>
                </div>
            </div>
        </form>

    </app-generic-modal>
<!-- End: New Close-Call -->
<block-loader [show]="(dashboardLoader || loadingCloseCalls)" [showBackdrop]="true" alwaysInCenter="true"></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
<report-downloader #reportDownloader
    [xlsxOnly]="true"
    [showCategory]="true"
    [categoryList]="hazardousCategoryList"
    [showEmployer]="true"
    [employerList]="employersList"
    [showCompany]="tagged_owners.length && isProjectPortal"
    [companyList]="tagged_owners"
    [showStatus]="true"
    [statusObj]="closeCallsDwStatus"
    [showNoCloseout]="false"
    (onFilterSelection)="closeCallReportDownload($event)"
    >
</report-downloader>

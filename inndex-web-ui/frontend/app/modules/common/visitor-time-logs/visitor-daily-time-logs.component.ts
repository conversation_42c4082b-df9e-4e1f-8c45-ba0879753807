/**
 * Created by spatel on 22/05/20.
 */
import {Component, Input, OnInit, SimpleChanges, ViewChild} from '@angular/core';
import {AppConstant} from "@env/environment";
import * as dayjs from 'dayjs';
import {Project, TimeUtility, ToastService, VisitorService} from "@app/core";
import {ViewVisitorRmcDetailComponent} from "./../view-visitor-rmc-detail/view-visitor-rmc-detail.component";

@Component({
    selector: 'visitor-daily-time-logs',
    templateUrl: './visitor-daily-time-logs.component.html',
})
export class VisitorDailyTimeLogsComponent implements OnInit {

    dbDateFormat: string = AppConstant.apiRequestDateFormat;
    displayDateFormat: string = AppConstant.displayDateFormat;

    dayjs(n?: number, format?: any) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs(n, format).tz(tz);
    };

    unix(n: number) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs.unix(n).tz(tz);
    };

    getTimeFromSeconds(n: number) {
        return dayjs().startOf('day').add(dayjs.duration(+n, 'seconds').asSeconds(), 'second');
    };

    @Input()
    projectId: number;

    @Input()
    companyId: number;

    @Input()
    showMedicalInfo: boolean = true;

    @Input()
    filterParam: any = {};

    @Input()
    project: Project;

    @Input()
    searchValue?: string;

    @Input()
    hideTitle: boolean = false;

    private expandedRow: any = null;

    tableLoading: boolean = false;

    visitor_logs: Array<any> = [];
    temp_visitor_logs: Array<any> = [];

    constructor(
        public timeUtility: TimeUtility,
        private visitorService: VisitorService,
        private toastService: ToastService,
    ) {
    }

    ngOnInit(): void {
        this.getVisitorLogs();
    }

    ngOnChanges(changes:SimpleChanges) {
        const searchValue = changes["searchValue"].currentValue
        // Update result on clear of searchbox
        if(searchValue || searchValue === '') {
            this.searchFunction(searchValue)
        }
    }

    private getVisitorLogs(){
        this.tableLoading = true;
        let nowMs: number = dayjs().valueOf();
        this.visitorService.getVisitorTimeLogs(this.projectId, this.companyId, {...this.filterParam, nowMs}).subscribe((data: any) => {
            this.tableLoading = false;
            if(data.visitor_logs){
                this.visitor_logs = data.visitor_logs;
                this.temp_visitor_logs = data.visitor_logs;
            }else{
                this.visitor_logs = [];
                this.temp_visitor_logs = [];
                const message = data.message || 'Failed to fetch visitor time data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    @ViewChild('allVisitorTable') allVisitorTable: any;
    toggleExpandRow(row, expanded) {
        if (this.expandedRow && this.expandedRow !== row) {
            this.allVisitorTable.rowDetail.toggleExpandRow(this.expandedRow);
        }
        this.expandedRow = expanded ? null : row;
        this.allVisitorTable.rowDetail.toggleExpandRow(row);
    }

    @ViewChild('viewVisitorRmcDetail', { static: true }) viewVisitorRmcDetailRef: ViewVisitorRmcDetailComponent;
    openMedicalConditionsModal(event, row: any) {
        event.target.closest('datatable-body-cell').blur();
        this.viewVisitorRmcDetailRef.openViewMedicalConditionsModal(row);
    }
    
    searchFunction(searchValue: string){
        let val= searchValue.toLowerCase();
        this.filterFn(val);
    }

    filterFn(val){
        const temp = this.temp_visitor_logs.filter(function (d) {
            return (
                (d.name.toLowerCase().indexOf(val) !== -1) ||
                (d.employer && d.employer.toLowerCase().indexOf(val) !== -1) ||
                (d.job_role && d.job_role.toLowerCase().indexOf(val) !== -1) ||
                !val
            );
        });

        this.visitor_logs = temp;
    }
}

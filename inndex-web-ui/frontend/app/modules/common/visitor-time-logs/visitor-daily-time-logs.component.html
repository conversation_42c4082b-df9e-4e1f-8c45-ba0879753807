<block-loader [show]="tableLoading" [showBackdrop]="true" alwaysInCenter="true"></block-loader>
<div *ngIf="!tableLoading">
    <h5 class="m-0" *ngIf="!hideTitle">Visitors</h5>
    <div class="table-responsive-sm">
        <ngx-datatable
            #allVisitorTable
            class="bootstrap visitorsTable ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
            [scrollbarV]="true"
            [scrollbarH]="true"
            [virtualization]="false"
            [rows]="visitor_logs"
            [sorts]="[{prop: 'is_still_on_site', dir: 'desc'}]"
            [columnMode]="'force'"
            [rowHeight]="'auto'"
            [headerHeight]="30"
            [footerHeight]="36"
            [columns]="[
                {
                    name:'', prop: 'visitor_id', width: 30,
                    cellClass: 'py-2 p-2',
                    cellTemplate: toggleBtnColumn,
                    headerClass: 'font-weight-bold p-2',
                    minWidth:'40',
                    maxWidth:'50',
                    sortable: false
                },
                {
                    name:'Name',prop: 'name', cellTemplate: visitorNameColumn,
                    cellClass: 'py-2 p-2',
                    headerClass: 'font-weight-bold p-2',
                    minWidth:'100'
                },
                {
                    name:'Company', prop: 'employer',
                    cellClass: 'py-2 p-2',
                    cellTemplate: companyColumn,
                    headerClass: 'font-weight-bold p-2',
                    minWidth:'100'
                },
                {
                    name:'Job Role', prop: 'job_role',
                    cellClass: 'py-2 p-2',
                    cellTemplate: jobRoleColumn,
                    headerClass: 'font-weight-bold p-2',
                    minWidth:'100'
                },
                {
                    name:'On Site', prop: 'is_still_on_site',
                    cellClass: 'py-2 p-2', cellTemplate: onSiteColumn,
                    headerClass: 'font-weight-bold p-2',
                    minWidth:'100'
                }
            ]">
            <ng-template #toggleBtnColumn let-row="row" let-value="value" let-expanded="expanded">
                <i [class.fa-plus-square]="!expanded"
                   [class.text-muted]="!expanded && (!row.daily_logs || row.daily_logs.length === 0)"
                   [class.fa-minus-square]="expanded"
                   title="Expand/Collapse Row"
                   class="fa cursor-pointer"
                   (click)="toggleExpandRow(row, expanded)">
                </i>
            </ng-template>
            <ng-template #visitorNameColumn let-row="row" let-value="value">
                <div class="d-flex p-0 m-0">
                    <div *ngIf="showMedicalInfo && row.has_rmc && row.rmc_detail" style="width: 18px;">
                    <span (click)="openMedicalConditionsModal($event, row)" role="button">
                        <img class="mr-1 mb-1" src="/assets/images/files-medical-icon-red.svg">
                    </span>
                    </div>
                    <span appTooltip>{{ value }}</span>
                </div>
            </ng-template>
            <ng-template #companyColumn let-row="row" let-value="value">
                <span appTooltip>{{ value }}</span>
            </ng-template>
            <ng-template #jobRoleColumn let-row="row" let-value="value">
                <span appTooltip>{{ value }}</span>
            </ng-template>
            <ng-template #onSiteColumn let-value="value">
                <ng-container *ngIf="value">
                    <i class="fa fa-check-circle text-success"> </i>
                </ng-container>
                <ng-container *ngIf="!value">
                    <i class="fa fa-times-circle text-danger"> </i>
                </ng-container>
            </ng-template>
            <ngx-datatable-row-detail
                #rowDetail
                [rowHeight]="'auto'">
                <ng-template let-row="row" let-expanded="expanded" ngx-datatable-row-detail-template>
                    <div *ngIf="!row.daily_logs || !row.daily_logs.length" class="text-center my-2">
                        No data to display
                    </div>
                    <div class="pl-4" *ngIf="row.daily_logs && row.daily_logs.length">
                        <ngx-datatable
                            class="bootstrap visitorDetail"
                            [rows]="row.daily_logs"
                            [columns]="[
                                {name:'Day', prop: 'day_of_yr', cellTemplate: dayOfYr, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1'},
                                {name:'In time', prop: 'clock_in', cellTemplate: timestampCellTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1'},
                                {name:'Out time', prop: 'clock_out', cellTemplate: timestampCellTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1'},
                                {name:'Total time', prop: 'duration_in_sec', cellTemplate: totalInTimeCell, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1'},
                                {name:'Travel time', prop: 'travel_time', cellTemplate: travelTimeCell, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1'},
                                {name:'Distance', prop: 'travel_time.distance_traveled', headerTemplate:distanceTHeadTemplate, cellTemplate: distanceTCellTemplate, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1'}
                            ]"
                            [columnMode]="'force'"
                            [footerHeight]="38"
                            [limit]="10"
                            [sorts]="[{prop: 'day_of_yr', dir: 'desc'}]"
                            [rowHeight]="'auto'">

                            <ng-template #dayOfYr let-value="value">
                                {{ value ? dayjs(value, dbDateFormat).format(displayDateFormat) : '-'}}
                            </ng-template>
                            <ng-template #timestampCellTemplate let-value="value" let-row="row">
                                {{ value ? unix(+value).format('HH:mm:ss') : '-'}}
                            </ng-template>
                            <ng-template #totalInTimeCell let-value="value" let-row="row">
                                {{ value ? getTimeFromSeconds(row.effective_time).format('HH:mm:ss'): '' }}
                            </ng-template>
                            <ng-template #travelTimeCell let-value="value">
                                {{ timeUtility.getTotalTravelDuration(row.travel_time, 'minutes') }}
                            </ng-template>
                            <ng-template #distanceTHeadTemplate>
                                <span title="Distance Travelled (Km)">Distance Travelled</span>
                            </ng-template>
                            <ng-template #distanceTCellTemplate let-value="value">
                                {{ (row.travel_time && row.travel_time.distance_traveled && +row.travel_time.distance_traveled) ? ((+row.travel_time.distance_traveled) / 1000).toFixed(2) : null }}
                            </ng-template>
                        </ngx-datatable>
                    </div>
                </ng-template>
            </ngx-datatable-row-detail>

        </ngx-datatable>
    </div>
</div>

<view-visitor-rmc-detail #viewVisitorRmcDetail></view-visitor-rmc-detail>
import {Component, OnInit, ViewChild} from "@angular/core";
import { HttpParams } from '@angular/common/http';
import {ActivatedRoute} from "@angular/router";
import * as dayjs from 'dayjs';
import { DomSanitizer } from '@angular/platform-browser';
import {
    AuthService,
    Project,
    User,
    SiteMessagingService,
    SiteMessaging,
    UserService,
    Common, isInheritedProjectOfCompany, HttpService,
    ToastService,
} from "@app/core";
import { NgbModal, NgbModalConfig,NgbDateStruct, NgbTimepickerConfig} from "@ng-bootstrap/ng-bootstrap";
import { AppConstant } from "@env/environment";
import {innDexConstant} from "@env/constants";
import { NgbMomentjsAdapter } from "@app/core/ngb-moment-adapter";
import { GenericModalComponent } from "../generic-modal/generic-modal.component";
import { GenericModalConfig } from '../generic-modal/generic-modal.config';
import { filterData } from "@app/core";
import { ActionBtnEntry, GenericConfirmationModalComponent } from "@app/shared";

@Component({
    templateUrl: './site-messaging.component.html',
    providers: [NgbModalConfig, NgbModal, NgbTimepickerConfig],
    styleUrls: ['./site-messaging.component.scss'],
})

export class SiteMessagingComponent implements OnInit {
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    authUser$: User;
    employer: any = {};
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse:any;
    employerId: number = 0;
    loadingSiteMessaging: boolean = false;
    tableOffset: number = 0;
    requestLoader: boolean = false;
    project_messages: Array<any> = [];
    current_message: SiteMessaging = new SiteMessaging;
    allowedMime : Array<any> = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf', 'video/mp4', 'video/quicktime', 'audio/mpeg'];
    approved_induction_company: Array<any> = [];
    visited_users_by_employer: Array<any> = [];
    today_visited_user: Array<any> = [];
    last30_visited_user: Array<any> = [];
    approved_induction_user: Array<any> = [];
    selectedUsers: Array<any> = [];
    usersToSelect: Array<any> = [];
    defaultUsersToSelect: Array<any> = [];
    employersToSelect: Array<any> = [];
    selectedEmployers: Array<any> = [];
    today_visited_user_checked: boolean = false;
    last30_visited_user_checked: boolean = false;
    approved_induction_user_checked: boolean = false;
    common = new Common();
    page = this.common.page;
    successMessage: string = '';
    is_inherited_project: boolean = false;
    messagesReadAndAcceptStatus: any = '';
    nameSearch: string = '';
    usersJobRole: Array<string> = [];
    selectedJobRoles: Array<string> = [];

    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    isProjectPortal: boolean = false;
    isMobileDevice: boolean = false;
    showSendMessageForm: boolean = false;
    
    filter: { search: string, user: any[] } = { search: "", user: [] }
    user_filter_data: any[] = []
    time:{hour:any,minute:any}={hour:"",minute:""};
    
    selectedDay: NgbDateStruct = null;
    resent:boolean = false;
    showError:boolean = false;
    minDate:any = null;
    maxDate:any = null;
    sorts: {
        dir:string, prop: string
    } = {
        dir:'desc', prop: 'id'
    };
    filterData:filterData[] = this.renderFilterData();
    maxFileSize: number = 10*1024*1024;
    isInitSiteMessage: boolean = false;
    loadingInlineSiteMessage: boolean = false;
    rowButtonGroup: Array<ActionBtnEntry> = [
        {
            key: 'view',
            label: '',
            title: 'View Message',
            mat_icon: 'search',
        },
    ];

    constructor(
        private activatedRoute: ActivatedRoute,
        private authService: AuthService,
        private siteMessagingService: SiteMessagingService,
        private modalService: NgbModal,
        private userService: UserService,
        private sanitizer: DomSanitizer,
        private httpService: HttpService,
        public ngbMomentjsAdapter: NgbMomentjsAdapter,
        private toastService: ToastService,
    ) {
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
        }
    }

    ngOnInit(): void {
        if(!this.isProjectPortal) {
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
            });
        } else {
            this.initializeTable(true);
        }

        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });
        this.minDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.maxDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs().add(2, 'M'));
    }

    initializeTable(fromInit?: boolean) {
        this.loadingSiteMessaging = true;
        this.pageCallback({ offset: 0 }, false, fromInit);
        this.getActiveUsers();
    }

    /**
     * Populate the table with new data based on the page number
     * @param page The page to select
     */
    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean, fromInit?: boolean) {
        if (!fromInit && !this.isInitSiteMessage) {
          this.isInitSiteMessage = true;
          return;
        }
        this.page.pageNumber = pageInfo.offset;
        this.reloadTable(isPageChange);
        this.scrollToTop();
    }

    /**
     * You will render the table once at the beginning in ngOnInit()
     * and then every time the page changed
     */
    reloadTable(isPageChange?: boolean) {
        // NOTE: those params key values depends on your API!
        const params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('search', this.filter.search)
            .set('user',this.filter.user.length ?this.filter.user.join(','):'')
            .set('sortKey',this.sorts.prop)
            .set('sortDir',this.sorts.dir)
            
        if (isPageChange) {
          this.loadingInlineSiteMessage = true;
        } else {
          this.loadingSiteMessaging = true;
        }
        this.siteMessagingService.getProjectMessages(this.projectId, params).subscribe((data: any) => {
            this.loadingSiteMessaging = false;
            this.loadingInlineSiteMessage = false;
            if (data && data.records) {
                if (data.user_filter_data && data.user_filter_data.length) {
                    this.user_filter_data = data.user_filter_data;
                    this.filterData = this.renderFilterData();
                }
                this.page.totalElements = data.totalCount;
                const updatedProjectMessages = data.records.map((message) => {
                    const readRate = this.getReadRate(message); // Calculate the readRate using your function
                    let latest_resent;
                    if(message.resend_logs.length){
                        latest_resent = Math.max(...message.resend_logs.map(a=>a.resend_at));
                    }
                    return { ...message, readRate, latest_resent };
                })
                this.project_messages = updatedProjectMessages;

                if(this.current_message && this.current_message.id) {
                    this.current_message = (this.project_messages || []).find(msg => msg.id === this.current_message.id);
                }
                return;
            }
            const message = `Failed to fetch messages on project, id: ${this.projectId}.`;
            this.toastService.show(this.toastService.types.ERROR, message, { data: data});
            return;
        });
    }

    getActiveUsers() {
        this.requestLoader = true;
        this.userService.getProjectActiveUsers(this.projectId).subscribe((data: any) => {
            this.requestLoader = false;
            if (data && data.success) {
                this.approved_induction_user = data.approved_induction_user;
                this.approved_induction_company = data.approved_induction_company;
                this.today_visited_user = data.today_visited_user;
                this.last30_visited_user = data.last30_visited_user;
                this.visited_users_by_employer = data.visited_users_by_employer;

                ([...data.approved_induction_company, ...data.visited_users_by_employer] || []).map(item => {
                    let existingEmployer = this.employersToSelect.find(emp => emp.id == item.employer_id);
                    if (!existingEmployer) {
                        this.employersToSelect.push({
                            name: item.employer,
                            id: item.employer_id
                        });
                    }
                });

                this.employersToSelect = [...this.employersToSelect];

                let users = [...data.approved_induction_user, ...data.today_visited_user, ...data.last30_visited_user];
                users = (users || []).sort((a,b) => ((a.name).toLowerCase() > (b.name).toLowerCase()) ? 1 : (((b.name).toLowerCase() > (a.name).toLowerCase()) ? -1 : 0));
                this.usersToSelect = this.defaultUsersToSelect = (users || []).reduce((arr, user) => {
                    let existingUser = arr.find(item => item.id == user.id);
                    if (!existingUser && !this.authService.isShadowUser(user)) {
                        arr.push(user);
                        if (user.job_role) {
                            this.usersJobRole.push(user.job_role);
                        }
                    }
                    return arr;
                }, []);
                this.usersJobRole = (this.usersJobRole || []).reduce((arr, jobRole) => {
                    let existingJobRole = arr.find(item => item == jobRole);
                    if (!existingJobRole && jobRole) {
                        arr.push(jobRole);
                    }
                    return arr;
                }, []);

                return data.project;
            }
            const message = `Failed to fetch project active users, id: ${this.projectId}.`;
            this.toastService.show(this.toastService.types.ERROR, message, { data: data});
        });
    }

    AppConstant = AppConstant;
    //  TODO : Utkarsh - change to import of format to appConstant when pushing to prod
    fullDateTimeFormat_without_ss = innDexConstant.fullDateTimeFormat_without_ss;
    
    dayjs(n: number) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(+n).tz(tz);
    };

    openModal(content, size, windowClass=''){
        const modalRef = this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            centered:true,
            size: size,
            windowClass: windowClass
        });
        return false;
    }

    @ViewChild('sendMessageForm') sendMessageForm;
    public sendMessageModalConfig: GenericModalConfig = {
        modalTitle: 'Message Details',
        primaryTrailingBtnLabel: 'Send',
        primaryTrailingBtnAction: () => {
            this.confirmSendMessage(this.sendMessageForm);
            return true;
        },
        primaryBtnDisabled: true,
        cancelTrailingBtnAction:()=> {
            this.showSendMessageForm = false;
            return true;
        },
        closeTrailingBtnAction:() => {
            this.showSendMessageForm = false;
            return true;
        },
        modalOptions: {
            size: 'lg',
            windowClass: 'sendMessageForm',
            
        },
        plainText:true
    }
    
    @ViewChild('sendMessageFormHtml') private sendMessageFormHtmlGenericModal: GenericModalComponent
    async sendMessagePopup() {
        this.current_message = new SiteMessaging;
        this.current_message.should_accepted = true;
        this.getActiveUsers();
        this.selectedUsers = [];
        this.today_visited_user_checked = false;
        this.last30_visited_user_checked = false;
        this.selectedEmployers = [];
        this.selectedJobRoles = [];
        this.showSendMessageForm = true;
        this.showError = false;
        if(this.sendMessageForm){
            this.sendMessageForm?.form.markAsUntouched();
            this.sendMessageForm.form.statusChanges.subscribe(status => {
                this.sendMessageModalConfig.primaryBtnDisabled = status !== 'VALID';
            });
        }
        return this.sendMessageFormHtmlGenericModal.open()
    }

    mediaUploadDone($event) {
        let { uploaded_items, userFile } = $event;
        userFile.map((item, index)=>{
            item.size = +(uploaded_items[index].file.size);
        })
        this.current_message.msg_files.splice(1, 0,...userFile);
        this.current_message.msg_files[0] = {};
    }

    fileDeleteDone($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.current_message.msg_files = this.current_message.msg_files.filter(r => (r.id !== $event.userFile.id));
        }
    }

    trackByRowIndex(index: number, obj: any){
        return index;
    }

    changeSelectedUsers($event, user) {
        if ($event.target.checked) {
            if (!this.selectedUsers.includes(user.id)) {
                this.selectedUsers.push(user.id);
            }
        } else {
            let index = this.selectedUsers.indexOf(user.id);
            if (index > -1) {
                this.selectedUsers.splice(index, 1);
            }
        }
    }

    /*selectedEmployerUsers() {
        if (this.selectedEmployers.length) {
            this.selectedUsers = [];
            this.selectedEmployers.map(emp => {
                let employerInfo = (this.approved_induction_company || []).find(item => item.employer_id == emp);
                if (employerInfo) {
                    (employerInfo.users || []).map(user => {
                        if (!this.selectedUsers.includes(user.id)) {
                            this.selectedUsers.push(user.id);
                        }
                    })
                }

                if (this.approved_induction_user_checked) {
                    let employerInfo = (this.visited_users_by_employer || []).find(item => item.employer_id == emp);
                    if (employerInfo) {
                        (employerInfo.users || []).map(user => {
                            let approvedInductionUser = this.approved_induction_user.find(v_user => v_user.id == user.id);
                            if (approvedInductionUser && !this.selectedUsers.includes(user.id)) {
                                this.selectedUsers.push(user.id);
                            }
                        })
                    }
                }

                if(this.last30_visited_user_checked) {
                    let employerInfo = (this.visited_users_by_employer || []).find(item => item.employer_id == emp);
                    if (employerInfo) {
                        (employerInfo.users || []).map(user => {
                            let userFromLast30Days = this.last30_visited_user.find(v_user => v_user.id == user.id);
                            if (userFromLast30Days && !this.selectedUsers.includes(user.id)) {
                                this.selectedUsers.push(user.id);
                            }
                        })
                    }
                }

                if(this.today_visited_user_checked) {
                    let employerInfo = (this.visited_users_by_employer || []).find(item => item.employer_id == emp);
                    if (employerInfo) {
                        (employerInfo.users || []).map(user => {
                            let userFromToday = this.today_visited_user.find(v_user => v_user.id == user.id);
                            if (userFromToday && !this.selectedUsers.includes(user.id)) {
                                this.selectedUsers.push(user.id);
                            }
                        })
                    }
                }

                if(this.approved_induction_user_checked) {
                    this.selectApprovedInductionUser();
                }
            });
        } else {
            this.selectedUsers = [];
            if(this.last30_visited_user_checked) {
                this.selectLast30VisitedUser();
            }

            if(this.today_visited_user_checked) {
                this.selectTodayVisitedUser();
            }

            if(this.approved_induction_user_checked) {
                this.selectApprovedInductionUser();
            }

            this.selectedJobRolesUsers()
        }
    }

    selectTodayVisitedUser() {
        if (this.today_visited_user_checked) {
            if (this.last30_visited_user_checked) {
                this.last30_visited_user_checked = false;
                this.selectLast30VisitedUser();
            }
            if (this.approved_induction_user_checked) {
                this.approved_induction_user_checked = false;
                this.selectApprovedInductionUser();
            }

            (this.today_visited_user || []).map(user => {
                if (!this.selectedUsers.includes(user.id)) {
                    this.selectedUsers.push(user.id);
                }
            })
        } else {
            (this.today_visited_user || []).map(user => {
                let index = this.selectedUsers.indexOf(user.id);
                if (index > -1) {
                    this.selectedUsers.splice(index, 1);
                }
            });

            this.selectedEmployerUsers();
            this.selectedJobRolesUsers()
        }
    }

    selectLast30VisitedUser() {
        if (this.last30_visited_user_checked) {
            if (this.today_visited_user_checked) {
                this.today_visited_user_checked = false;
                this.selectTodayVisitedUser();
            }
            if (this.approved_induction_user_checked) {
                this.approved_induction_user_checked = false;
                this.selectApprovedInductionUser();
            }
            (this.last30_visited_user || []).map(user => {
                if (!this.selectedUsers.includes(user.id)) {
                    this.selectedUsers.push(user.id);
                }
            })
        } else {
            (this.last30_visited_user || []).map(user => {
                let index = this.selectedUsers.indexOf(user.id);
                if (index > -1) {
                    this.selectedUsers.splice(index, 1);
                }
            });

            this.selectedEmployerUsers();
            this.selectedJobRolesUsers()
        }
    }

    selectApprovedInductionUser() {
        if (this.approved_induction_user_checked) {
            if (this.today_visited_user_checked) {
                this.today_visited_user_checked = false;
                this.selectTodayVisitedUser();
            }
            if (this.last30_visited_user_checked) {
                this.last30_visited_user_checked = false;
                this.selectLast30VisitedUser();
            }
            (this.approved_induction_user || []).map(user => {
                if (!this.selectedUsers.includes(user.id)) {
                    this.selectedUsers.push(user.id);
                }
            })
        } else {
            (this.approved_induction_user || []).map(user => {
                let index = this.selectedUsers.indexOf(user.id);
                if (index > -1) {
                    this.selectedUsers.splice(index, 1);
                }
            });

            this.selectedEmployerUsers();
            this.selectedJobRolesUsers()
        }
    }*/

    isSelectAtleastOneUser() {
        return (this.selectedUsers.length) ? true : null;
    }

    public messageDetailsModalConfig: GenericModalConfig = {
        modalTitle: '',
        primaryTrailingBtnLabel: 'OK',
        primaryTrailingBtnAction: () => {
            this.messageDetailsHtmlGenericModal.close();
            return true;
        },
        showCancelTrailingBtn: false,
        modalOptions: {
            size: 'lg'
        }
    }

    @ViewChild('messageDetailsHtml') private messageDetailsHtmlGenericModal: GenericModalComponent
    viewMessageModel(row) {
        this.messageDetailsModalConfig.modalTitle = `Message #${row?.record_id} Details`

        this.resent= false;
        if (row.accepted_by_recipients.length && row.should_accepted) {
            this.messagesReadAndAcceptStatus =  this.sanitizer.bypassSecurityTrustHtml(`<i class="w-100 d-inline-block">Read: ${row.read_by_recipients.length}/${row.recipients.length}</i>
 <i class="w-100 d-inline-block">Accepted: ${row.accepted_by_recipients.length}/${row.recipients.length}</i>`);
        } else {
            this.messagesReadAndAcceptStatus =  this.sanitizer.bypassSecurityTrustHtml(`<i class="w-100 d-inline-block">Read: ${row.read_by_recipients.length}/${row.recipients.length}</i>`);
        }

        this.current_message = row;
        //this.current_message.msg_text = (this.current_message.msg_text || '').replaceAll("\n", "<br>");
        this.messageDetailsHtmlGenericModal.open();
    }

    isValidNumber(value: any): boolean {
        return typeof value === 'number' && !isNaN(value);
    }

    getReadRate(row) {
        const readRate =  Math.min(parseInt(((row?.read_by_recipients?.length / row?.recipients?.length) * 100).toFixed(0)), 100);
        return this.isValidNumber(readRate) ? readRate : null;
    }

    getMarkMessageAt(item, type) {
        if (type === 'Accepted' && this.current_message.should_accepted) {
            return `${type}: ${this.dayjs(item.accepted_at).format(AppConstant.fullDateTimeFormat)}`;
        }
        let additionalStr = (!this.current_message.should_accepted) ? '&#10;No acceptance required' : '';
        return `${type}: ${this.dayjs(item.read_at).format(AppConstant.fullDateTimeFormat)}${additionalStr}`;
    }

    getUserEmployer(user) {
        return (user && user.parent_company && user.parent_company.name) ? `(${user.parent_company.name})` : '';
    }

    getMessageMarkTick(recipient, read_by_recipients, accepted_by_recipients) {
        let acceptedItem = accepted_by_recipients.find(accepted_by => accepted_by.id == recipient.id);
        let readItem = read_by_recipients.find(read_by => read_by.id == recipient.id);
        let htmlContent = `<span></span>`;
        if (acceptedItem && this.current_message.should_accepted) {
            let readTitle = (readItem) ? `${this.getMarkMessageAt(readItem, 'Read')}&#10;` : '';
            htmlContent = `<span><i class="fas fa-check-double" title="${readTitle+this.getMarkMessageAt(acceptedItem, 'Accepted')}"></i></span>`;
        } else if(readItem) {
            htmlContent = `<span><i class="fas fa-check" title="${this.getMarkMessageAt(readItem, 'Read')}"></i></span>`;
        }

        return this.sanitizer.bypassSecurityTrustHtml(htmlContent);
    }

    hasMarkedAsAction(recipient, read_by_recipients, accepted_by_recipients) {
        let acceptedItem = accepted_by_recipients.find(accepted_by => accepted_by.id == recipient.id);
        let readItem = read_by_recipients.find(read_by => read_by.id == recipient.id);
        return (acceptedItem || readItem) ? true : false;
    }

    resendMessage(messageId) {
        let recipientIds = []
        for (let recipient of this.current_message.recipients) {
            if (!this.hasMarkedAsAction(recipient, this.current_message.read_by_recipients, this.current_message.accepted_by_recipients)) {

                recipientIds.push(recipient.id)
            }
        }
        let request = {
            message_id: messageId,
            recipient_id: recipientIds,

        }
        this.requestLoader = true;
        this.siteMessagingService.resendMessage(request, this.projectId).subscribe(out => {
            this.requestLoader = false;
            if(out.success) {
                let resend_logs = out?.messageInfo?.resend_logs;
                let recipients = resend_logs[resend_logs.length - 1].id;
                this.reloadTable(true);
                const message = `Your message has been resent to ${recipients.length} users.`;
                this.toastService.show(this.toastService.types.SUCCESS, message);
                this.resent = true;
            } else {
                const message = 'Failed to resend message.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out});
            }
        });
    }

    getResendContent(recipient, resend_logs) {
        let titleArr = [];
        (resend_logs || []).map(log => {
            if (log.id == recipient.id) {
                titleArr.push(`Resent: ${this.dayjs(log.resend_at).format(AppConstant.fullDateTimeFormat)}`);
            }
            return log;
        });

        let title = (titleArr.length) ? titleArr.join('&#10;') : 'Resend Message';
        return `<img style="width: 21px;" src="/images/resend.png" alt="Resend Message" title="${title}">`;
    }

    // Will get called for Company route only.
    projectRetrieved(project: Project){
        this.projectInfo = project;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
        this.initializeTable();
    }

    filterUsersByName() {
        this.usersToSelect = this.defaultUsersToSelect;
        if (this.nameSearch) {
            this.usersToSelect = (this.usersToSelect || []).filter(user => ((user.name).toLowerCase() || '').includes((this.nameSearch).toLowerCase()));
        }
    }

    /*selectedJobRolesUsers() {
        if (this.selectedJobRoles.length) {
            this.selectedUsers = [];
            this.selectedJobRoles.map(job_role => {
                ([...this.approved_induction_user, ...this.today_visited_user, ...this.last30_visited_user]).map(user => {
                    if (user.job_role == job_role) {
                        this.selectedUsers.push(user.id);
                    }
                })
            });
        } else {
            this.selectedUsers = [];
            if(this.last30_visited_user_checked) {
                this.selectLast30VisitedUser();
            }

            if(this.today_visited_user_checked) {
                this.selectTodayVisitedUser();
            }

            if(this.approved_induction_user_checked) {
                this.selectApprovedInductionUser();
            }

            this.selectedEmployerUsers();
        }
    }*/

    selectUsers() {
        this.selectedUsers = [];
        if (this.selectedEmployers.length) {
            this.employersToSelect = [...this.employersToSelect.sort((a, b) => this.selectedEmployers.includes(a.id) ? -1 : 1 )];
            this.selectedEmployers.map(emp => {
                if (this.approved_induction_user_checked) {
                    let employerInfo = (this.visited_users_by_employer || []).find(item => item.employer_id == emp);
                    if (employerInfo) {
                        (employerInfo.users || []).map(user => {
                            let approvedInductionUser = this.approved_induction_user.find(v_user => v_user.id == user.id);
                            if (approvedInductionUser && !this.selectedUsers.includes(user.id)) {
                                this.selectedUsers.push(user.id);
                            }
                        })
                    }
                } else {
                    let employerInfo = (this.approved_induction_company || []).find(item => item.employer_id == emp);
                    if (employerInfo) {
                        (employerInfo.users || []).map(user => {
                            if (!this.selectedUsers.includes(user.id)) {
                                this.selectedUsers.push(user.id);
                            }
                        })
                    }
                }

                if(this.last30_visited_user_checked) {
                    let employerInfo = (this.visited_users_by_employer || []).find(item => item.employer_id == emp);
                    if (employerInfo) {
                        (employerInfo.users || []).map(user => {
                            let userFromLast30Days = this.last30_visited_user.find(v_user => v_user.id == user.id);
                            if (userFromLast30Days && !this.selectedUsers.includes(user.id)) {
                                this.selectedUsers.push(user.id);
                            }
                        })
                    }
                }

                if(this.today_visited_user_checked) {
                    let employerInfo = (this.visited_users_by_employer || []).find(item => item.employer_id == emp);
                    if (employerInfo) {
                        (employerInfo.users || []).map(user => {
                            let userFromToday = this.today_visited_user.find(v_user => v_user.id == user.id);
                            if (userFromToday && !this.selectedUsers.includes(user.id)) {
                                this.selectedUsers.push(user.id);
                            }
                        })
                    }
                }
            });
        } else {
            if(this.last30_visited_user_checked) {
                (this.last30_visited_user || []).map(user => {
                    if (!this.selectedUsers.includes(user.id)) {
                        this.selectedUsers.push(user.id);
                    }
                })
            }

            if(this.today_visited_user_checked) {
                (this.today_visited_user || []).map(user => {
                    if (!this.selectedUsers.includes(user.id)) {
                        this.selectedUsers.push(user.id);
                    }
                })
            }

            if(this.approved_induction_user_checked) {
                (this.approved_induction_company || []).map(user => {
                    if (!this.selectedUsers.includes(user.id)) {
                        this.selectedUsers.push(user.id);
                    }
                })
            }
        }


        if (this.selectedJobRoles?.length) {
            let usersByJobRole = [];
            this.usersJobRole=[...this.usersJobRole.sort((a,b)=> this.selectedJobRoles.includes(a) ? -1 : 1)];
            this.selectedJobRoles.map(job_role => {
                ([...this.approved_induction_user, ...this.today_visited_user, ...this.last30_visited_user]).map(user => {
                    if (user.job_role == job_role && !usersByJobRole.includes(user.id)) {
                        usersByJobRole.push(user.id);
                    }
                })
            });

            //if only job roles selected
            if (!this.selectedUsers.length) {
                this.selectedUsers = usersByJobRole;
            } else {
                this.selectedUsers = this.selectedUsers.reduce((arr, userId) => {
                    if (usersByJobRole.includes(userId)) {
                        arr.push(userId);
                    }
                    return arr;
                }, [])
            }
        }

        this.selectedUsers = this.selectedUsers.filter((value, index, self) => self.indexOf(value) === index);

        console.log("selectedUsers: ", this.selectedUsers);
    }
    onFilterSelection(data) {
        this.filter.user = data['author'].map(a => +a.id);
        this.page.pageNumber = 0;
        this.pageCallback({ offset: 0 }, true);
    }
    searchFunction(data){
        this.filter.search = data.search;
        let stopReload = data?.stopReload;
        if(!stopReload){
            this.pageCallback({ offset: 0 }, true);
        };
    }
    renderFilterData(){
        return [
            {
                name:'author',
                list:this.user_filter_data,
                enabled:true,
                state:false,
            }
        ];
    }

 
    reorderUsersArray() {
        this.usersToSelect = [...this.usersToSelect.sort((a,b)=> this.selectedUsers.includes(a.id) ? -1 : 1)];
    }

    confirmSendMessage(form, $modalLoader?, cb?){
        if (!form.valid || this.selectedUsers.length==0 || (this.current_message.scheduleDelivery && this.selectedDay == null)) {
            const message = 'Please fill all the required fields.';
            this.toastService.show(this.toastService.types.INFO, message);
            this.showError = true;
            return;
        }
        let totalFileSize = this.current_message.msg_files.reduce((total,file,index)=>{
            if(file && file?.id){
                total += +file.size;
            }
            return total;
        },0)

        if(totalFileSize > this.maxFileSize){
            const message = 'The total size of the uploaded files exceeds 10 MB. Please review and consider optimizing the size or number of files accordingly.';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }
        if(this.current_message.scheduleDelivery){
            let tz = this.projectInfo?.custom_field?.timezone;
            let dt= `${this.selectedDay.day}-${this.selectedDay.month}-${this.selectedDay.year} ${this.time.hour}:${this.time.minute}:00`;
            this.current_message.scheduled_at = dayjs(dt,'D-M-YYYY HH:mm:ss').tz(tz,true).valueOf();
            this.current_message.is_sent = false;
            if(dayjs().tz(tz,true).valueOf() > this.current_message.scheduled_at){
                const message = 'Choose a future date and time.';
                this.toastService.show(this.toastService.types.INFO, message);
                return;
            }
        }
        this.confirmationModalRef.openConfirmationPopup({
            title: "Are you sure you wish to send this message?",
            hasCancel:true,
            confirmLabel: "send",
            onConfirm: () => {
                let request = this.current_message;
                request.project_ref = this.projectId;
                request.recipients = this.selectedUsers;
                request.msg_files = (request.msg_files || []).reduce((arr,file) => {
                    if(file && file.id) {
                        arr.push(file.id);
                    }
                    return arr;
                }, []);
          
                this.requestLoader = true;
                this.siteMessagingService.sendMessage(request, this.projectId).subscribe(out => {
                    this.sendMessageFormHtmlGenericModal.close()
                    this.showSendMessageForm = false;
                    if(out.success) {
                        this.reloadTable(true);
                        const message = `Your message has been sent to ${request.recipients.length} users.`;
                        this.modalService.dismissAll();
                        return this.toastService.show(this.toastService.types.SUCCESS, message);
                    } else {
                        const message = 'Failed to send message.';
                        this.toastService.show(this.toastService.types.ERROR, message, { data: out});
                    }
                    this.initializeTable();
                }).add(() =>( this.requestLoader = false ));
            }
        })
    }
    inputChange(e){
        let target = e.target;
        if(+target.value > +target.max){
            this.time[target.name] = +target.max;
            target.value = +target.max;
            return;
        }
        if(+target.value < +target.min){
            this.time[target.name] = +target.min;
            target.value = +target.min;
            return;
        }
        this.time[target.name] = +target.value;
    }
    onSort($event){
        let {sorts} = $event;
        let{ dir, prop } = sorts[0];
        this.sorts = { dir, prop };
        this.loadingSiteMessaging = true;
        this.pageCallback({ offset: 0 }, true);
    }
    scrollToTop() {
        window.scrollTo(0, 0);
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        switch(entry.key) {
            case 'view':
                this.viewMessageModel(row);
                break;
            default:
                console.warn(`Unhandled action key: ${entry.key}`);
        }
    }
}

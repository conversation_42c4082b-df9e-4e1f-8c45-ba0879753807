<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav *ngIf="!isProjectPortal" [companyResolverResponse]="companyResolverResponse" [employer]="employer" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="col-sm-12 my-3 outer-border" *ngIf="!loadingSiteMessaging">
                <div class="col-sm-12 outer-border-radius">
                <div class="d-flex flex-column flex-sm-row flex-wrap justify-content-between mb-2 pb-2">
                    <h5>Total messages sent<small class="ml-1">({{ page.totalElements }})</small></h5>
                    <button class="btn btn-sm justify-content-center other-action-btn btn-brandeis-blue d-flex align-items-center btn-invite pointer-cursor" (click)="sendMessagePopup()" >
                        <span class="medium-font m-font-size material-symbols-outlined mr-2">send</span>
                        <div class="medium-font m-font-size">Send Message</div>
                    </button>
                </div>
                <search-with-filters [filterData]="filterData" (filterEmitter)="onFilterSelection($event)" (searchEmitter)="searchFunction($event)"></search-with-filters>

                <div class="table-responsive-sm">
                    <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                   [scrollbarV]="true"
                                   [virtualization]="false"
                                   [loadingIndicator]="loadingInlineSiteMessage"
                                   [rows]="project_messages"
                                   [footerHeight]="40"
                                   [columnMode]="'force'"
                                   [rowHeight]="'auto'"
                                   [externalPaging]="true"
                                   [count]="page.totalElements"
                                   [offset]="page.pageNumber"
                                   [limit]="page.size"
                                   (page)="pageCallback($event, true)"
                                   [externalSorting]="true"
                                   (sort)="onSort($event)"
                                   [sorts]= "[sorts]"
                    >
                        <ngx-datatable-column prop="id"  headerClass="font-weight-bold" [sortable]="true" minWidth="70">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Message #
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{row.record_id}}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column prop="msg_title" headerClass="font-weight-bold" [sortable]="true" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Title
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{row?.msg_title}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Author
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{row?.user_ref?.name}}</span>
                            </ng-template>
                        </ngx-datatable-column>

                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Date Sent
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <div class="d-flex flex-column" *ngIf='row?.is_sent '>
                                    <span>
                                        {{(dayjs(row.createdAt).format(AppConstant.defaultDateFormat))}}
                                    </span>
                                    <span class="d-block font-disabled">{{(dayjs(row.createdAt).format(AppConstant.defaultTimeFormat))}}</span>
                                </div>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Read Rate
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span>
                                    {{ row?.read_by_recipients?.length }}/{{ row?.recipients?.length }}
                                </span>
                                <span [ngStyle]="{'color': row.readRate < 50 ? '#F20000' : row.readRate < 75  ? '#EDB531' : '#00992B', 'font-weight': 500}">
                                    ({{ row.readRate != null ? row.readRate + '%' : '' }})
                                </span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold action-column" cellClass="action-column no-ellipsis" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Action
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <button-group
                                    [buttons]="rowButtonGroup"
                                    (onActionClick)="rowBtnClicked($event, row)">
                                </button-group>
                            </ng-template>
                        </ngx-datatable-column>
                    </ngx-datatable>

                    <app-generic-modal #messageDetailsHtml [genericModalConfig]="messageDetailsModalConfig">
                        <div *ngIf="current_message" class="modal-body">
                            <div class="form-group">
                                <label><strong>Message Title:</strong></label>
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control" #msgTitle="ngModel" ng-value="current_message.msg_title" placeholder="Message Title"
                                           name="msg_title" disabled [(ngModel)]="current_message.msg_title">
                                </div>
                            </div>

                            <div class="form-group">
                                <label><strong>Contents:</strong></label>
                                <div class="input-group mb-3">
                                    <div class="form-control" style="background-color: white;color:#9d9d9d; height: fit-content;">
                                        <span style="white-space: pre-line;" [innerHTML] = "current_message.msg_text | urlify" class="d-block"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label><strong>Date Sent:</strong></label>
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control"
                                           name="msg_title" disabled [value]="dayjs(current_message.createdAt).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS)">
                                </div>
                            </div>
                          <div *ngIf="current_message.msg_files.length">
                          <div>
                            <label class="font-weight-bold">Reference Documents</label>
                        </div>
                        <ng-container *ngFor="let file of current_message.msg_files">
                            <div class="form-group p-2 custom-input-div mt-0"> 
                                <a class="d-block text-info" target="_blank" [href]="file.file_url">{{file.name}}</a>
                            </div>
                        </ng-container>
                        </div>

                        <div>
                            <label class="font-weight-bold">Recipients:</label>
                        </div>
                        <div>
                            </div>
                          <div *ngIf="current_message.read_by_recipients.length" class="border-grey">
                            <div class="d-flex align-items-center mt-1 ml-2 mb-2 justify-content-between gap-2">
                                <div class="horizontal-center"><span style="color: #0066FE;" class="material-symbols-outlined mr-1">
                                done_all
                                </span><span>Read by</span></div><span class="mr-2 employer">{{current_message.read_by_recipients.length}} {{current_message.read_by_recipients.length > 1 ? 'Users':'User'}}</span> </div>
                                <hr>
                                <div style="overflow: auto ; max-height: 300px;">
                                    <ng-container class=" ml-2 d-flex mt-2 mb-2 flex-column gap-5" *ngFor = "let recipient of current_message.recipients">
                                        <div *ngIf="hasMarkedAsAction(recipient, current_message.read_by_recipients, current_message.accepted_by_recipients)" class="ml-2 d-flex mt-2 mb-2 flex-column gap-5">
                                            <div>
                                                <span style="line-height: 12px;" class="mr-2">{{recipient?.name||" "}}</span>
                                                <span class="employer">({{recipient?.parent_company?.name|| " "}})</span>
                                            </div>
                                        </div>
                                    </ng-container>
                                </div>
                        </div>
                        <div *ngIf="current_message.read_by_recipients?.length != current_message.recipients?.length" class="border-grey mt-2">
                            <div  class="d-flex align-items-center justify-content-between mt-1 ml-2 mb-2 gap-2"><span class="horizontal-center"><span class="material-symbols-outlined mr-1">
                                done_all
                                </span><span>Delivered to</span></span>
                                <span class="mr-2 employer" >{{current_message.recipients.length-current_message.read_by_recipients.length}} {{(current_message.recipients.length-current_message.read_by_recipients.length) > 1 ? 'Users':'User'}}</span>

                                   </div>
                                <hr>
                                <div style="overflow: auto ; max-height: 300px;">
                                    <ng-container  *ngFor = "let recipient of current_message.recipients">
                                        <div *ngIf="!hasMarkedAsAction(recipient, current_message.read_by_recipients, current_message.accepted_by_recipients)" class="ml-2 d-flex mt-2 mb-2 flex-column gap-2">
                                            <div>
                                                <span style="line-height: 12px;" class="mr-2">{{recipient?.name||" "}}</span>
                                                <span class="employer">({{recipient?.parent_company?.name|| " "}})</span>
                                            </div>
                                        </div>
                                    </ng-container>
                                </div>
                                <hr>
                                <span class="horizontal-center justify-content-between">
                                    <span (click)="!resent && resendMessage(current_message.id)" [ngClass]="{'horizontal-center ml-3 my-2':true, 'cursor-pointer' : !resent}" [ngStyle]="{'color': resent ? '#9d9d9d' : '#0066FF', 'margin-right': '20px'}">
                                        <span style="font-size: 18px;" class="material-symbols-outlined mr-1">rotate_left</span>
                                        <span>{{resent ? 'Resent' : 'Resend'}}</span>
                                    </span>
                                    <span *ngIf = "current_message.latest_resent" class="mr-3" style="font-size: 14px;">Latest Resend: {{dayjs(current_message?.latest_resent).format(fullDateTimeFormat_without_ss)}}  </span>
                                </span>
                            </div>
                        </div>
                    </app-generic-modal>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>

<app-generic-modal #sendMessageFormHtml [genericModalConfig]="sendMessageModalConfig" [dynamicText]='selectedUsers.length ? "Users Selected: "+selectedUsers.length:""'>
    <form *ngIf="current_message" novalidate #sendMessageForm="ngForm">
        <ng-container *ngIf="showSendMessageForm">
            <div class="form-group">
                <label><strong>Message Title</strong><small class="required-asterisk">*</small></label>
                <div class="input-group mb-3">
                    <input type="text" class="form-control" #msgTitle="ngModel" ng-value="current_message.msg_title" placeholder="Message Title"
                           name="msg_title" required [(ngModel)]="current_message.msg_title" >
                </div>
            </div>

            <div class="form-group mb-0 ">
                <label><strong>Contents</strong><small class="required-asterisk">*</small></label>
                <div class="input-group mb-0 d-flex flex-column">
                                    <textarea class="form-control w-100" name="msg_text"
                                              [(ngModel)]="current_message.msg_text"
                                              ng-value="current_message.msg_text"
                                              style="min-height: 110px;"
                                              placeholder="Message Content"
                                              #msgText="ngModel"
                                              maxlength="1500" required ></textarea>
                                    <div>
                                        <span class="float-right small-font mt-1 text-muted">Maximum Characters - 1500</span>
                                    </div>
                </div>
            </div>

            <div *ngIf="!requestLoader" class="form-group">
                <label><strong>Add attachment</strong></label>
                <div class="col-md-12 p-0 d-flex flex-column flex-wrap">
                    <div *ngFor="let c of current_message.msg_files" [ngClass]='{"flex-grow-1 p-0 mt-2": true, "col-md-12": c.id, "col-md-12": !c.id}'>
                        <div>
                            <file-uploader-v2
                                [disabled]="false"
                                [init]="c"
                                [category]="'message-files'"
                                (uploadDone)="mediaUploadDone($event)"
                                [allowedMimeType]="allowedMime"
                                [showHyperlink]="true"
                                [showFileName]="true"
                                (deleteFileDone)="fileDeleteDone($event)"
                                [showDeleteBtn]="true"
                                [showFileFullName]="true"
                                [showThumbnail] ="false"
                                [maxFileSize]='maxFileSize'
                                [multipleUpload]="true"
                                [showSize] = "true"
                            >
                            </file-uploader-v2>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="justify-content-between mb-0"><span><strong>Acceptance</strong></span>
                    <div class="custom-control custom-switch d-inline-block" >
                        <input type="checkbox" class="custom-control-input" id="acceptance" name="acceptance"
                        [(ngModel)]="current_message.should_accepted">
                        <label class="custom-control-label" for="acceptance"></label>
                    </div>
                </label>
                <div class="mb-3">
                    <div class="mb-1">
                        <label for="acceptance">Would you like add a tick box for user to confirm that they have read and understood the message?</label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="justify-content-between mb-0"><span><strong>Send message as email</strong></span>
                     <div class="custom-control custom-switch d-inline-block" >
                    <input type="checkbox" class="custom-control-input" id="messageAsEmail" name="messageAsEmail"
                    [checked]="true" [(ngModel)]="current_message.mail_alert">
                    <label class="custom-control-label" for="messageAsEmail">

                    </label>
                </div></label>
                <div class="mb-3">
                    <div class="mb-1">
                        <label  for="message_as_email">Messages will be sent as push notification to the mobile app, would you like to send by email in addition?</label>
                    </div>
                </div>
            </div>
            <!-- will be visible when message scheduling functionality is completed. -->
            <!-- <div class="form-group">
                <label class="justify-content-between mb-0"><span><strong>Schedule:</strong></span>
                     <div class="custom-control custom-switch d-inline-block" >
                    <input type="checkbox" class="custom-control-input" id="scheduleDelivery" name="scheduleDelivery"
                    [checked]="true" [(ngModel)]="current_message.scheduleDelivery">
                    <label class="custom-control-label" for="scheduleDelivery">

                    </label>
                </div></label>
                <div class="mb-3">
                    <div class="mb-1">
                        <label  for="scheduleDelivery">Select a time and date to schedule the message.</label>
                    </div>
                </div>
            </div> -->
            <div class="form-group site-messaging-datepicker" *ngIf="current_message?.scheduleDelivery">
                <div class="input-group d-inline-flex px-1">
                    <input #sed="ngbDatepicker" [(ngModel)]="selectedDay" placement="bottom-right"
                    class="form-control h-40" name="date" ng-value="selectedDay"
                    ngbDatepicker placeholder="dd-mm-yyyy" [minDate]="minDate"
                    [maxDate]="maxDate" readonly [attr.required]="scheduleDelivery ? true : null">
             <div class="input-group-append">
                 <button (click)="sed.toggle()" class="btn btn-outline-secondary calendar" type="button">
                     <i class="fa fa-calendar"></i>
                 </button>
             </div>
                </div>

            <div class="justify-content-between horizontal-center my-3" >
                <label for="scheduleDelivery">Select Delivery Time<small class="required-asterisk">*</small></label>
                <div style="gap: 5px;" class="horizontal-center">
                    <input
                        type="number"
                        name="hour"
                        class="form-control w-70 time-input"
                        placeholder="00"
                        (input) = "inputChange($event)"
                        max="23"
                        min="00"
                    />
                  :
                    <input
                        type="number"
                        name="minute"
                        style="text-align: center;"
                        class="form-control w-70 time-input"
                        placeholder="00"
                        (input) = "inputChange($event,'minute')"
                        max="59"
                        min="00"
                    />
                </div>
            </div>
            <div class="alert alert-danger" [hidden]="!showError && current_message?.selectedDay !== null">Select date and time.</div>
        </div>

            <div class="form-group">
                <label><strong>Recipients</strong><small class="required-asterisk">*</small></label>

                <div class="input-group">
                    <div class="custom-control custom-checkbox mb-1">
                        <input type="checkbox" class="custom-control-input"
                               id="today_visited_user"
                               name="today_visited_user"
                               [(ngModel)]="today_visited_user_checked"
                               (change)="selectUsers()"
                        >
                        <label class="custom-control-label" for="today_visited_user">All on-site operatives</label>
                    </div>
                </div>

                <div class="input-group mb-3">
                    <div class="custom-control custom-checkbox mb-1">
                        <input type="checkbox" class="custom-control-input"
                               id="last30_visited_user"
                               name="last30_visited_user"
                               [(ngModel)]="last30_visited_user_checked"
                               (change)="selectUsers()"
                        >
                        <label class="custom-control-label" for="last30_visited_user">All operatives active in the past 30 days</label>
                    </div>
                </div>

                <div class="input-group mb-3" *ngIf="0">
                    <div class="custom-control custom-checkbox mb-1">
                        <input type="checkbox" class="custom-control-input"
                               id="approved_induction_user"
                               name="approved_induction_user"
                               [(ngModel)]="approved_induction_user_checked"
                               (change)="selectUsers()"
                        >
                        <label class="custom-control-label" for="approved_induction_user">All Inducted <span i18n="@@operatives">Operatives</span></label>
                    </div>
                </div>

                <div class="input-group mb-3">
                    <ng-select
                        #selectEmployers
                        style="width: 100%;"
                        class="dropdown-list sm-select filter-v2-select h-auto w-100" placeholder="Users by Employer"
                        [items]="employersToSelect"
                        name="selectedEmployers"
                        (change)="selectUsers()"
                        [closeOnSelect]="false"
                        [(ngModel)]="selectedEmployers"
                        [bindLabel]="'name'"
                        [bindValue]="'id'"
                        [multiple]="true"
                        [searchable] = 'false'
                        dropdownPosition="top"
                    >
                    <ng-template ng-header-tmp >
                        <div style="width:100%; max-width:100%;"  class="text-search horizontal-center px-2">
                            <span class="material-symbols-outlined large-font">search</span>
                            <input style="width: 100%; line-height: 24px" placeholder="Search" class="ngx-search " type="search" (input)="selectEmployers.filter($event.target.value)"/>
                        </div>
                    </ng-template>
                        <ng-template ng-option-tmp let-item="item" class="horizontal-center" let-item$="item$" let-index="index" let-search="searchTerm">
                            <label>
                                <input type="checkbox" [checked]="item$.selected" class="mr-1"/>
                                <span>{{item.name}}</span>
                            </label>
                        </ng-template>
                    </ng-select>
                </div>
                <div class="input-group mb-3">
                    <ng-select
                        #selectJobRoles
                        style="width: 100%;"
                        class="dropdown-list sm-select filter-v2-select h-auto w-100"  placeholder="Users by Job Role"
                        [items]="usersJobRole"
                        #selectAtleastOne="ngModel"
                        name="selectedJobRoles"
                        (change)="selectUsers()"
                        [(ngModel)]="selectedJobRoles"
                        [multiple]="true"
                        [closeOnSelect]="false"
                        [searchable] = 'false'
                        dropdownPosition="top"
                    >
                    <ng-template  ng-header-tmp >
                        <div style="width:100%; max-width:100%;"  class="text-search horizontal-center px-2">
                            <span class="material-symbols-outlined large-font">search</span>
                            <input style="width: 100%; line-height: 24px" placeholder="Search" class="ngx-search " type="search" (input)="selectJobRoles.filter($event.target.value)"/>
                        </div>
                    </ng-template>
                        <ng-template ng-option-tmp let-item="item" class="horizontal-center" let-item$="item$" let-index="index" let-search="searchTerm">
                            <label>
                                <input type="checkbox" [checked]="item$.selected" class="mr-1"/>
                                <span>{{item}}</span>
                            </label>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div>
                <ng-select #selectUserRef  [searchable]="false" (change)="reorderUsersArray()" [closeOnSelect]="false" name = "selectUserDropdown" placeholder="Select Users" class="dropdown-list sm-select filter-v2-select h-auto w-100" bindLabel="name"  bindValue="id" [(ngModel)]="selectedUsers" [items] = 'usersToSelect' required [multiple] = "true">
                    <ng-template  ng-header-tmp >
                        <div style="width:100%; max-width:100%;"  class="text-search horizontal-center px-2">
                            <span class="material-symbols-outlined large-font">search</span>
                            <input style="width: 100%; line-height: 24px" placeholder="Search" class="ngx-search " type="search" (input)="selectUserRef.filter($event.target.value)"/>
                        </div>
                    </ng-template>
                        <ng-template ng-option-tmp let-item="item" class="horizontal-center" let-item$="item$" let-index="index" let-search="searchTerm">
                            <label>
                                <input type="checkbox" [checked]="item$.selected" class="mr-1"/>
                                <span>{{item.name}}</span>
                            </label>
                        </ng-template>
                </ng-select>
                <div [hidden]="!(showError && !selectedUsers.length)" class="input-group my-3 overflow-auto">
                    <div class="alert alert-danger mb-0" >Select atleast 1 user.</div>
                </div>
              </div>
            <input type="hidden" name="current_message" id="current_message"
                   [(ngModel)]="current_message"/>
        </ng-container>
    </form>
    <block-loader [show]="(false)" #modalLoader></block-loader>

</app-generic-modal>
<block-loader [show]="requestLoader || loadingSiteMessaging" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

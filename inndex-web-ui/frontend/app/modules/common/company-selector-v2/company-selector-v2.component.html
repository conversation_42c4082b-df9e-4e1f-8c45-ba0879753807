<ng-select
    class="dropdown-list"
    appendTo="body"
    [class]="classes"
    [items]="records$ | async"
    #employer_ref
    bindLabel="name"
    bindValue="id"
    [minTermLength]="2"
    [required]="required"
    [disabled]="disabled"
    [placeholder]="placeholder"
    [multiple]="multiple"
    [closeOnSelect]="!multiple"
    [clearSearchOnAdd]="clearSearchOnAdd"
    [name]="name"
    #company_ref="ngModel"
    [(ngModel)]="selectedCompanyId"
    [typeahead]="userInput$"
    [clearable]="clearable"
    [trackByFn]="trackByFn"
    [loading]="recordsLoading"
    [typeToSearchText]="searchMessage"
    (open)="onOpen()"
    (clear)="onClear()"
    (close)="onClear()"
    (ngModelChange)="onSelection($event, employer_ref)"
>
    <ng-template ng-option-tmp let-item="item" let-disabled="disabled">
        <div (mousedown)="showDisallowedDomainAlert(item)">
            {{ item.name }}
        </div>
    </ng-template>
    <ng-container *ngIf="!multiple">
        <ng-template ng-label-tmp let-item="item">
            <span class="ng-value-label text-truncate">{{item.name}}</span>
        </ng-template>
    </ng-container>
    <ng-template ng-notfound-tmp let-searchTerm="searchTerm">
        <div class="ng-option disabled">
            No record found for "{{searchTerm}}"
        </div>
    </ng-template>
<!--    <ng-template ng-loadingtext-tmp let-searchTerm="searchTerm">
        <div class="ng-option disabled">
            Fetching data for "{{searchTerm}}"
        </div>
    </ng-template>-->
</ng-select>
<div class="alert alert-danger" [class]="errorClasses" [hidden]="!(company_ref.errors && company_ref.errors.required)">{{requiredMessage}}</div>
<small *ngIf="showHelper" class="form-text font-weight-bold"> Can't find a <span class="text-lowercase">{{fieldLabel}}</span>? Click <a class="text-info" href="javascript:void(0)" (click)="openAddModal()">here</a> to add a new one. </small>

<i-modal
        [title]="'Add new ' + fieldLabel.toLowerCase()"
        rightPrimaryBtnTxt="Confirm"
        (onCancel)="modalInfo.ready = false;"
        (onClickRightPB)="saveInfo($event, addForm)"
        [rightPrimaryBtnDisabled]="addForm.invalid"
        #addModalHtml>
    <form #addForm="ngForm" class="" novalidate>
        <div *ngIf="modalInfo.ready" class="mx-2">
            <div class="form-group row">
                <div class="col-md-12">
                    <input name="new_employer_name" autocomplete="off"
                           [(ngModel)]="modalInfo.new_employer_name"
                           type="text" class="form-control input-md"
                           [placeholder]="fieldLabel" required>
                </div>
            </div>
        </div>
        <block-loader [show]="(modalInfo.saving)" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>
    </form>
</i-modal>

<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
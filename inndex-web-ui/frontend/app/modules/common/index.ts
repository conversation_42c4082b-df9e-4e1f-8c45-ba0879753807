/**
 * Created by spatel on 28/10/18.
 */

export * from './on-board-forms';
export * from './project-live-dashboard/project-live-dashboard.component';
export * from './project-dashboard/project-dashboard.component';
export * from './pdf-flip-book/pdf-flip-book.component';
export * from './im-modal/im-modal.component';
export * from './manage-company-access/manage-company-access.component';
export * from './manage-blacklisting/manage-blacklisting.component';
export * from './inducted-user-selector/inducted-user-selector.component';
export * from './user-search-box/user-search-box.component';
export * from './ppac-status-card/ppac-status-card.component';
export * from './time-adjustment/time-adjustment-provider.component';
export * from './time-entry-provider/time-entry-provider.component';
export * from './procore-project-linker/procore-project-linker.component';
export * from './ccl-status-tile/ccl-status-tile.component';
export * from './company-selector/company-selector.component';
export * from './company-selector-v2/company-selector-v2.component';
export * from './country-selector/country-selector.component';
export * from './shift-configuration/shift-configuration-provider.component';
export * from './additional-doc-uploader/additional-doc-uploader.component';
export * from './project-comments/project-comments.component';
export * from './support/support.component';
export * from './verify-email/verify-email.component';
export * from './view-visitor-rmc-detail/view-visitor-rmc-detail.component';
export * from './visitor-time-logs/visitor-daily-time-logs.component';
export * from './week-date-selector/week-date-selector.component';
export * from './visitor-logs-for-date/visitor-logs-for-date.component';
export * from './view-geo-fence-log/view-geo-fence-log.component';
export * from './view-visitor-log/view-visitor-log.component';
export * from './declaration-answers/declaration-answers.component';
export * from './update-badge-event/update-badge-event.component';
export * from './policies/cookie-policy.component';
export * from './policies/privacy-policy.component';
export * from './progress-photos/progress-photos.component';
export * from './delivery-notes/delivery-notes.component';
export * from './daily-activities/daily-activities.component';
export * from './toolbox-talks/toolbox-talks.component';
export * from './close-call/close-call.component';
export * from './clerk-of-works/clerk-of-works.component';
export * from './incident-report/incident-report.component';
export * from './incident-report/injury-incident/injury-incident.component';
export * from './incident-report/health-incident/health-incident.component';
export * from './incident-report/road-traffic-incident/road-traffic-incident.component';
export * from './incident-report/damage-loss-incident/damage-loss-incident.component';
export * from './incident-report/violence-abuse-incident/violence-abuse-incident.component';
export * from './incident-report/environmental-incident/environmental-incident.component';
export * from './incident-report/service-strike-incident/service-strike-incident.component';
export * from './inspection-tour/inspection-tour.component';
export * from './e-learning/e-learning-module.component';
export * from './e-learning/options-row.component';
export * from './good-call/good-call.component';
export * from './site-messaging/site-messaging.component';
export * from './task-briefings/task-briefings.component';
export * from './daily-activities/daily-activities-import.component';
export * from './inspections-modal-box/inspections-modal-box.component';
export * from './inspections/inspections.component';
export * from './inspection-builder/inspection-builder.component';
export * from './assets-modal-box/assets-modal-box.component';
export * from './assets/assets.component';
export * from './asset-vehicles/asset-vehicles.component';
export * from './project-geofence/project-geofence.component';
export * from './work-package-plans/work-package-plans.component';
export * from './user-select-box/user-select-box.component';
export * from './download-report/download-report.component';
export * from './pop-up-image-viewer/pop-up-image-viewer.component';
export * from './tag-owner-modal/tag-owner-modal.component';
export * from './users-selector-modal/users-selector-modal.component';
export * from './quality-checklists/quality-checklists.component';
export * from "./daily-activities/meta-plants/meta-plants-import.component";
export * from "./daily-activities/view-acitvity/view-activity.component";
export * from './asset-equipment/asset-equipment.component';
export * from './alternative-phrase-setting/alternative-phrase-setting.component';
export * from './progress-photos/submissions/progress-photos-submissions.component';
export * from './progress-photos/timeline/progress-photos-timeline.component';
export * from './incident-notification-preference/incident-notification-preference.component';
export * from './progress-photo-location-map/progress-photo-location-map.component';
export * from './manage-categories-modal/manage-categories-modal.component';
export * from './inspection-root-cause/inspection-root-cause.component';
export * from './project-rams/project-rams.component';
export * from './project-rams/assessment-form/assessment-form.component';
export * from './project-rams/briefing-table/briefing-table.component';
export * from './custom-fields-manager/custom-fields-manager.component';
export * from './progress-photos/albums/progress-photos-albums.component';
export * from './progress-photos/progress-photos-details/progress-photos-details.component';
export * from './custom-detail-fields-manager/custom-detail-fields-manager.component';
export * from './induction-report-downloader/induction-report-downloader.component';
export * from './induction-change-log/induction-change-log.component';
export * from './progress-photos/add/progress-photos-add.component';
export * from './progress-photos/download/progress-photos-download.component';
export * from './progress-photos/gallery/progress-photos-gallery.component';
export * from './share-tool-report-to-email/share-tool-report-to-email.component';
export * from './asset-temporary-works/asset-temporary-work.component';
export * from './generic-modal/generic-modal.component'
export * from './fault-closeout/fault-closeout.component';
export * from './asset-decline/asset-decline.component';
export * from './search-with-filters/search-with-filters.component';
export * from './qrcode-generator/qrcode-generator.component';
export * from './permit-request/permit-request.component';
export * from './action-button/action-button.component';
export * from './incident-file-link-with-desc/incident-file-link-with-desc.component';
export * from './tool-invite-modal/tool-invite-modal.component';
export * from './date-range-picker/date-range-picker.component';
export * from  "./report-downloader/report-downloader.component";
export * from "./create-or-edit-itp/create-or-edit-itp.component";
export * from './induction-report-downloader-v2/induction-report-downloader-v2.component';
export * from './itp-modal-box/itp-modal-box.component';
export * from './inducted-admin-selector/inducted-admin-selector.component';
export * from './supply-chain-selector/supply-chain-selector.component';
export * from './incident-report/near-miss/near-miss.component'
export * from './incident-report/unsafe-act-occurrence/unsafe-act-occurrence.component';

import {OnBoardPersonalFormComponent,
    OnBoardContactFormComponent,
    OnBoardHealthAssessmentFormComponent,
    OnBoardMedicalAssessmentFormComponent,
    OnBoardEmploymentFormComponent,
    OnBoardMyCompetenciesFormComponent,
    // OnBoardProfilePicFormComponent,
} from './on-board-forms';
import {ProjectLiveDashboardComponent} from './project-live-dashboard/project-live-dashboard.component';
import {ProjectDashboardComponent} from './project-dashboard/project-dashboard.component';
import {PdfFlipBookComponent} from './pdf-flip-book/pdf-flip-book.component';
import {ImModalComponent} from './im-modal/im-modal.component';
import {ManageCompanyAccessComponent} from './manage-company-access/manage-company-access.component';
import {ManageBlacklistingComponent} from './manage-blacklisting/manage-blacklisting.component';
import {InductedUserSelectorComponent} from "@app/modules/common/inducted-user-selector/inducted-user-selector.component";
import {SignaturePadSelectorComponent} from '@app/modules/common/signature-pad-selector/signature-pad-selector.component';
import {UserSearchBoxComponent} from './user-search-box/user-search-box.component';
import {PpacStatusCardComponent} from './ppac-status-card/ppac-status-card.component';
import {TimeAdjustmentProviderComponent} from './time-adjustment/time-adjustment-provider.component';
import {TimeEntryProviderComponent} from './time-entry-provider/time-entry-provider.component';
import {ProcoreProjectLinkerComponent} from './procore-project-linker/procore-project-linker.component';
import {CCLStatusTileComponent} from './ccl-status-tile/ccl-status-tile.component';
import {CompanySelectorV2Component} from './company-selector-v2/company-selector-v2.component';
import {CompanySelectorComponent} from './company-selector/company-selector.component';
import {CountrySelectorComponent} from './country-selector/country-selector.component';
import {ShiftConfigurationProviderComponent} from './shift-configuration/shift-configuration-provider.component';
import {AdditionalDocUploaderComponent} from './additional-doc-uploader/additional-doc-uploader.component';
import {ProjectCommentsComponent} from './project-comments/project-comments.component';
import {SupportComponent} from './support/support.component';
import {VerifyEmailComponent} from './verify-email/verify-email.component';
import {ViewVisitorRmcDetailComponent} from './view-visitor-rmc-detail/view-visitor-rmc-detail.component';
import {VisitorDailyTimeLogsComponent} from './visitor-time-logs/visitor-daily-time-logs.component';
import {VisitorLogsForDateComponent} from './visitor-logs-for-date/visitor-logs-for-date.component';
import {WeekDateSelectorComponent} from './week-date-selector/week-date-selector.component';
import {ViewGeoFenceLogComponent} from './view-geo-fence-log/view-geo-fence-log.component';
import {ViewVisitorLogComponent} from './view-visitor-log/view-visitor-log.component';
import {DeclarationAnswersComponent} from './declaration-answers/declaration-answers.component';
import {UpdateBadgeEventComponent} from './update-badge-event/update-badge-event.component';
import {CookiePolicyComponent} from './policies/cookie-policy.component';
import {PrivacyPolicyComponent} from './policies/privacy-policy.component';
import {ProgressPhotosComponent} from './progress-photos/progress-photos.component';
import {DeliveryNotesComponent} from './delivery-notes/delivery-notes.component';
import {DailyActivitiesComponent} from './daily-activities/daily-activities.component';
import {ToolboxTalksComponent} from './toolbox-talks/toolbox-talks.component';
import {CloseCallComponent} from './close-call/close-call.component';
import {ClerkofWorksComponent} from './clerk-of-works/clerk-of-works.component';
import {IncidentReportComponent} from './incident-report/incident-report.component';
import {InjuryIncidentComponent} from './incident-report/injury-incident/injury-incident.component';
import {HealthIncidentComponent} from './incident-report/health-incident/health-incident.component';
import {RoadTrafficIncidentComponent} from './incident-report/road-traffic-incident/road-traffic-incident.component';
import {DamageLossIncidentComponent} from './incident-report/damage-loss-incident/damage-loss-incident.component';
import {ViolenceAbuseIncidentComponent} from './incident-report/violence-abuse-incident/violence-abuse-incident.component';
import {EnvironmentalIncidentComponent} from './incident-report/environmental-incident/environmental-incident.component';
import {ServiceStrikeIncidentComponent} from './incident-report/service-strike-incident/service-strike-incident.component';
import {InspectionTourComponent} from './inspection-tour/inspection-tour.component';
import {ELearningModuleComponent} from './e-learning/e-learning-module.component';
import {OptionsRowComponent} from './e-learning/options-row.component';
import {GoodCallComponent} from './good-call/good-call.component';
import {SiteMessagingComponent} from './site-messaging/site-messaging.component';
import {TaskBriefingsComponent} from './task-briefings/task-briefings.component';
import {DailyActivitiesImportComponent} from './daily-activities/daily-activities-import.component';
import {InspectionsModalBoxComponent} from './inspections-modal-box/inspections-modal-box.component';
import {InspectionsComponent} from './inspections/inspections.component';
import {AssetsModalBoxComponent} from './assets-modal-box/assets-modal-box.component';
import {AssetsComponent} from './assets/assets.component';
import {AssetVehiclesComponent} from './asset-vehicles/asset-vehicles.component';
import { ProjectGeofenceModalBoxComponent } from './project-geofence/project-geofence.component';
import { WorkPackagePlanComponent } from './work-package-plans/work-package-plans.component';
import {UserSelectBoxComponent} from './user-select-box/user-select-box.component';
import { DownloadReportModalComponent } from './download-report/download-report.component';
import {PopupImageViewerComponent} from './pop-up-image-viewer/pop-up-image-viewer.component';
import {TagOwnerModalComponent} from './tag-owner-modal/tag-owner-modal.component';
import {UsersSelectorModalComponent} from './users-selector-modal/users-selector-modal.component';
import {QualityChecklistsComponent} from './quality-checklists/quality-checklists.component';
import { MetaPlantsImportComponent } from "./daily-activities/meta-plants/meta-plants-import.component";
import { ViewDailyActivityComponent } from "./daily-activities/view-acitvity/view-activity.component";
import {AssetEquipmentComponent} from './asset-equipment/asset-equipment.component';
import { FaultCloseoutComponent } from './fault-closeout/fault-closeout.component';
import { AssetDeclineComponent } from './asset-decline/asset-decline.component';
import {AlternativePhraseSettingComponent} from './alternative-phrase-setting/alternative-phrase-setting.component';
import { ProgressPhotosSubmissionsComponent} from './progress-photos/submissions/progress-photos-submissions.component';
import { ProgressPhotosTimelineComponent} from './progress-photos/timeline/progress-photos-timeline.component';
import { IncidentNotificationPreferenceComponent } from './incident-notification-preference/incident-notification-preference.component';
import { ProgressPhotoLocationMapComponent } from './progress-photo-location-map/progress-photo-location-map.component';
import {ManageCategoriesModalComponent} from './manage-categories-modal/manage-categories-modal.component';
import {InspectionRootCauseComponent} from './inspection-root-cause/inspection-root-cause.component';
import { ProjectRamsComponent } from './project-rams/project-rams.component';
import { AssessmentFormComponent } from './project-rams/assessment-form/assessment-form.component';
import { BriefingTableComponent } from './project-rams/briefing-table/briefing-table.component';
import { CustomFieldsManagerComponent } from './custom-fields-manager/custom-fields-manager.component';
import { ProgressPhotosDetailsComponent } from './progress-photos/progress-photos-details/progress-photos-details.component';
import { CustomDetailFieldsManagerComponent } from './custom-detail-fields-manager/custom-detail-fields-manager.component';
import { InductionReportDownloaderComponent } from './induction-report-downloader/induction-report-downloader.component';
import {InductionChangeLogComponent} from './induction-change-log/induction-change-log.component';
import { ProgressPhotosAlbumsComponent } from "./progress-photos/albums/progress-photos-albums.component";
import { ProgressPhotosAddComponent } from './progress-photos/add/progress-photos-add.component';
import { ItpModalBoxComponent } from './itp-modal-box/itp-modal-box.component';
import { ProgressPhotosDownloadComponent } from './progress-photos/download/progress-photos-download.component';
import { ProgressPhotosGalleryComponent } from './progress-photos/gallery/progress-photos-gallery.component';
import { ShareToolReportToEmailComponent } from './share-tool-report-to-email/share-tool-report-to-email.component';
import { AssetTemporaryWorkComponent } from './asset-temporary-works/asset-temporary-work.component';
import { InspectionBuilderComponent } from './inspection-builder/inspection-builder.component';
import { GenericModalComponent } from './generic-modal/generic-modal.component';
import { SearchWithFiltersComponent } from './search-with-filters/search-with-filters.component';
import { QRCodeGeneratorComponent } from './qrcode-generator/qrcode-generator.component';
import { CountryCodeContactInputComponent } from './country-code-contact-input/country-code-contact-input.component';
import { NearMissComponent } from './incident-report/near-miss/near-miss.component';
import { PermitRequestComponent } from './permit-request/permit-request.component';
import { ActionButtonComponent } from './action-button/action-button.component';
import { IncidentFileLinkWithDescComponent } from './incident-file-link-with-desc/incident-file-link-with-desc.component';
import { ToolInviteModalComponent } from './tool-invite-modal/tool-invite-modal.component';
import { DateRangePickerComponent } from './date-range-picker/date-range-picker.component';
import { ReportDownloaderComponent } from './report-downloader/report-downloader.component';
import { InductionReportDownloaderV2Component } from './induction-report-downloader-v2/induction-report-downloader-v2.component';
import { CreateorEditITPComponent } from './create-or-edit-itp/create-or-edit-itp.component';
import { InductedAdminSelectorComponent } from './inducted-admin-selector/inducted-admin-selector.component';
import { SupplyChainSelectorComponent } from './supply-chain-selector/supply-chain-selector.component';
import { UnsafeActOccurrenceComponent } from './incident-report/unsafe-act-occurrence/unsafe-act-occurrence.component';


export const ALL_COMMON_COMPONENTS: Array<any> = [
    // OnBoardPersonalFormComponent,
    // OnBoardContactFormComponent,
    // OnBoardHealthAssessmentFormComponent,
    // OnBoardMedicalAssessmentFormComponent,
    // OnBoardEmploymentFormComponent,
    // OnBoardMyCompetenciesFormComponent,
    // OnBoardProfilePicFormComponent,
    ProjectDashboardComponent,
    ProjectLiveDashboardComponent,
    PdfFlipBookComponent,
    ImModalComponent,
    ManageBlacklistingComponent,
    ManageCompanyAccessComponent,
    InductedUserSelectorComponent,
    SignaturePadSelectorComponent,
    UserSearchBoxComponent,
    PpacStatusCardComponent,
    TimeEntryProviderComponent,
    ProcoreProjectLinkerComponent,
    TimeAdjustmentProviderComponent,
    CCLStatusTileComponent,
    // CompanySelectorV2Component,
    // CompanySelectorComponent,
    // CountrySelectorComponent,
    ShiftConfigurationProviderComponent,
    AdditionalDocUploaderComponent,
    ProjectCommentsComponent,
    SupportComponent,
    VerifyEmailComponent,
    ViewVisitorRmcDetailComponent,
    VisitorDailyTimeLogsComponent,
    VisitorLogsForDateComponent,
    WeekDateSelectorComponent,
    ViewGeoFenceLogComponent,
    ViewVisitorLogComponent,
    DeclarationAnswersComponent,
    UpdateBadgeEventComponent,
    CookiePolicyComponent,
    PrivacyPolicyComponent,
    // SidebarComponent,
    ProgressPhotosComponent,
    DeliveryNotesComponent,
    DailyActivitiesComponent,
    ToolboxTalksComponent,
    CloseCallComponent,
    ClerkofWorksComponent,
    IncidentReportComponent,
    InjuryIncidentComponent,
    NearMissComponent,
    UnsafeActOccurrenceComponent,
    HealthIncidentComponent,
    RoadTrafficIncidentComponent,
    DamageLossIncidentComponent,
    ViolenceAbuseIncidentComponent,
    EnvironmentalIncidentComponent,
    ServiceStrikeIncidentComponent,
    InspectionTourComponent,
    ELearningModuleComponent,
    OptionsRowComponent,
    GoodCallComponent,
    SiteMessagingComponent,
    TaskBriefingsComponent,
    DailyActivitiesImportComponent,
    InspectionsModalBoxComponent,
    InspectionsComponent,
    InspectionBuilderComponent,
    AssetsModalBoxComponent,
    AssetsComponent,
    AssetVehiclesComponent,
    ProjectGeofenceModalBoxComponent,
    WorkPackagePlanComponent,
    UserSelectBoxComponent,
    DownloadReportModalComponent,
    PopupImageViewerComponent,
    TagOwnerModalComponent,
    UsersSelectorModalComponent,
    QualityChecklistsComponent,
    MetaPlantsImportComponent,
    ViewDailyActivityComponent,
    AssetEquipmentComponent,
    AlternativePhraseSettingComponent,
    ProgressPhotosSubmissionsComponent,
    ProgressPhotosTimelineComponent,
    ProgressPhotosAlbumsComponent,
    IncidentNotificationPreferenceComponent,
    ProgressPhotoLocationMapComponent,
    ManageCategoriesModalComponent,
    InspectionRootCauseComponent,
    ProjectRamsComponent,
    AssessmentFormComponent,
    BriefingTableComponent,
    CustomFieldsManagerComponent,
    ProgressPhotosDetailsComponent,
    CustomDetailFieldsManagerComponent,
    InductionReportDownloaderComponent,
    InductionChangeLogComponent,
    ProgressPhotosAddComponent,
    ProgressPhotosDownloadComponent,
    ProgressPhotosGalleryComponent,
    ShareToolReportToEmailComponent,
    AssetTemporaryWorkComponent,
    GenericModalComponent,
    FaultCloseoutComponent,
    AssetDeclineComponent,
    SearchWithFiltersComponent,
    QRCodeGeneratorComponent,
    PermitRequestComponent,
    ActionButtonComponent,
    IncidentFileLinkWithDescComponent,
    ToolInviteModalComponent,
    DateRangePickerComponent,
    ReportDownloaderComponent,
    CreateorEditITPComponent,
    InductionReportDownloaderV2Component,
    ItpModalBoxComponent,
    InductedAdminSelectorComponent,
    SupplyChainSelectorComponent,
];

export const ON_BOARD_FORMS: Array<any> = [
    OnBoardPersonalFormComponent,
    OnBoardContactFormComponent,
    OnBoardHealthAssessmentFormComponent,
    OnBoardMedicalAssessmentFormComponent,
    OnBoardEmploymentFormComponent,
    OnBoardMyCompetenciesFormComponent,
    // OnBoardProfilePicFormComponent,
    CountryCodeContactInputComponent
];

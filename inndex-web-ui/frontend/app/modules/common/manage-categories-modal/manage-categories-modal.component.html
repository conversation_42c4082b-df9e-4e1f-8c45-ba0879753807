<i-modal #categoryModal [title]="feature_name + ' Categories'" size="lg" [showCancel]="true" (onClickRightPB)="saveRecords($event)"
    rightPrimaryBtnTxt="Save" [rightPrimaryBtnDisabled]="!configForm.valid">
        <form novalidate #configForm="ngForm">
            <div class="px-4">
                <span>{{ (feature_name == 'Observations') ? 'Add, Remove or Activate/Deactivate categories for this tool' : ''}}</span>
                <div class="form-group row mx-0">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">Category</th>
                                <th class="text-center">Active <i class="ml-1 fa fa-info-circle small" ngbTooltip="Deactivating a category will hide the option when submitting an observation" [openDelay]="200" [closeDelay]="500"></i></th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <ng-container *ngFor="let item of records trackBy : trackByRowIndex; let i = index;">
                                <tr>
                                    <td>
                                        <input *ngIf="i == 0" type="text" class="form-control" [name]="'categoryName' + i"
                                               #categoryName="ngModel" [(ngModel)]="item.name"
                                               [value]="item.name | titlecase"
                                               (input)="item.name = $event.target.value"
                                               placeholder="Enter category name then press add" autocomplete="off" />
                                        <input *ngIf="i != 0" type="text" class="form-control" [name]="'categoryName' + i"
                                               #categoryName="ngModel" [(ngModel)]="item.name"
                                                placeholder="Enter category name" autocomplete="off" [disabled]="item.is_default" />
                                    </td>
                                    <td class="text-center">
                                        <div class="custom-control custom-checkbox" *ngIf="i != 0">
                                            <input type="checkbox" class="custom-control-input" [id]="'is_active_' + i" [name]="'is_active_' + i"
                                                   #isActive="ngModel" [(ngModel)]="item.is_active" />
                                            <label class="custom-control-label" [for]="'is_active_' + i"></label>
                                        </div>
                                    </td>
                                    <td *ngIf="i == 0" class="text-center">
                                        <button class="btn btn-sm btn-outline-primary" (click)="addCategory(i)" [disabled]="!item?.name">
                                            <i class="fa fa-plus"></i> Add
                                        </button>
                                    </td>
                                    <td *ngIf="i > 0 && !item.is_default" class="text-center">
                                        <span class="material-symbols-outlined text-danger cursor-pointer" (click)="removeCategoryRow(i)" *ngIf="!(i == 0)">
                                            delete
                                        </span>
                                    </td>
                                </tr>
                            </ng-container>
                        </tbody>
                    </table>
                </div>
            </div>
        </form>
</i-modal>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav [employer]="employer" [companyResolverResponse]="companyResolverResponse" *ngIf="!isProjectPortal" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="pt-3 col-12" *ngIf="!loadingPackagePlans">
            <div class="col-sm-12 pr-0 outer-border outer-border-radius">
                <div>
            <div class="mb-2 mx-3 flex-wrap gap-8 d-flex justify-content-between">
                <div class="col-md-5 d-inline-block p-0">
                    <h5 class="float-md-left col-md-12 p-0">Total {{ wppPhrase }} <small>({{ page.totalElements }})</small></h5>
                </div>
                <action-button
                    [actionList]="actionButtonMetaData.actionList"
                    (selectedActionEmmiter)="onActionSelection($event)"
                    [newFeatureTitle]="'New ' + this.wppSglrPhrase"
                    (onOpenAddNew)="showAddWorkPackagePlanPop(addWorkPackagePlanForm)">
                </action-button>
            </div>

            <div class="col-sm-12 my-2">
                <div class="pr-1">
                    <div class="mt-2">
                    <search-with-filters #searchFilters [filterData]="filterData" (filterEmitter)="onFilterSelection($event)" (searchEmitter)="searchFunction($event)"></search-with-filters>
                </div>
                <block-loader [show]="(processingWorkPackagePlanDownload)"  [showBackdrop]="true" alwaysInCenter="true"></block-loader>
                <div class="clearfix"></div>
                <div class="table-responsive-sm" *ngIf="isProjectPortal || isCompanyRoute()">
                    <!-- Work Package Plan table -->
                    <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                   [scrollbarV]="true"
                                   [virtualization]="false"
                                   [loadingIndicator]="loadingInlineWorkPackagePlan"
                                   [rows]="records"
                                   [footerHeight]="40"
                                   [columnMode]="'force'"
                                   [rowHeight]="'auto'"
                                   [externalPaging]="true"
                                   [count]="page.totalElements"
                                   [offset]="page.pageNumber"
                                   [limit]="page.size"
                                   (page)="pageCallback($event, true)"
                                   [externalSorting]="true"
                                   (sort)="onSort($event)"
                                   [sorts] = "[sorts]"
                    >
                        <ngx-datatable-column prop="id" headerClass="font-weight-bold min-w-fit-content" cellClass="min-w-fit-content" [sortable]="true" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                {{ wppSglrPhrase }} #
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{row.record_id}}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column prop="title" headerClass="font-weight-bold" [sortable]="true" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Title
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{row.briefing_title}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Uploaded By
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{row.user_ref.first_name}} {{row.user_ref?.middle_name || ''}} {{row.user_ref.last_name}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [width]="200" [sortable]="false" minWidth="140">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Date & Time Uploaded
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{ dayjs(+row.createdAt).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS)}}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Company
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{ row?.tagged_owner?.name}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="130">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                No. Times Briefed
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{row?.briefed_count}}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="130">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Last Briefed
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <div class="d-flex flex-column">
                                    <span *ngIf="row?.last_briefed_at else notBriefed">
                                        {{ dayjs(+row?.last_briefed_at).format(AppConstant.defaultDateFormat)}} 
                                        <div class="small">({{ dayjs(+row?.last_briefed_at).format(AppConstant.defaultTimeFormat) }})</div>
                                    </span>
                                    <ng-template #notBriefed>-</ng-template>
                                </div>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Available
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <div class="col-6">
                                    <input type="checkbox" class="form-control checkbox-small"
                                           [checked]="checkAvailability(row)"
                                           (click)="makeBriefingAvailable($event, row)"/>
                                </div>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold action-column" cellClass="action-column no-ellipsis" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Action
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <button-group
                                    [buttons]="rowButtonGroup"
                                    (onActionClick)="rowBtnClicked($event, row)">
                                </button-group>
                            </ng-template>
                        </ngx-datatable-column>
                    </ngx-datatable>
                    <!-- END Work Package Plan table -->
                    <block-loader [show]="(processingWorkPackagePlan)" [showBackdrop]="true" alwaysInCenter="true"></block-loader>
                </div>
                <div class="clearfix"></div>
            </div>
        </div>
        </div>
            </div>
        </div>
    </div>
</div>

<i-modal #addWorkPackagePlanRef [title]="'New ' +  wppSglrPhrase" (onCancel)="onCancel()" (onClickRightPB)="addWorkPackagePlanRequest(addWorkPackagePlanForm, $event)" rightPrimaryBtnTxt="Save"
    [rightPrimaryBtnDisabled]="!addWorkPackagePlanForm.valid || (!briefingFile)">
        <form novalidate #addWorkPackagePlanForm="ngForm">
            <div class="form-group">
                <label class="font-weight-bold">Title: <small class="required-asterisk ">*</small></label>
                <div class="input-group mb-3">
                    <input type="text" class="form-control"
                           placeholder="{{ wppSglrPhrase }} title..."
                           name="briefing_title"
                           required="true"
                           [(ngModel)]="briefing_title">
                </div>
            </div>
            <div class="form-group">
                <label>Company</label>
                <company-selector-v2
                    [required]="false"
                    [country_code]="this.projectInfo?.custom_field?.country_code"
                    [name]="'tagged_owner'"
                    [placeholder]="'Select Company'"
                    [selectId]="tagged_owner"
                    (selectionChanged)="tagged_owner = $event.selected"
                    [projectId]="projectId"
                ></company-selector-v2>
            </div>
            <div class="form-group" *ngIf="isModalOpen">
                <label class="font-weight-bold">Upload File (PDF or Word Doc.): <small class="required-asterisk ">*</small></label>
                <input type="hidden" name="briefing_file_ref" id="briefing_file_ref"
                       [(ngModel)]="briefing_file_ref"/>
                <file-uploader-v2
                    [init]="briefingFile"
                    (uploadDone)="uploadDone($event)"
                    [allowedMimeType]="allowedMime"
                    [dragnDropTxt]="'Drag and drop pdf or doc here'"
                    (deleteFileDone)="fileDeleteDone($event)"
                    [showDeleteBtn]="true"
                    [disabled]="false"
                    [showHyperlink]="true"
                    [category]="'wpp'"
                ></file-uploader-v2>
            </div>
        </form>
</i-modal>

<i-modal #workPackagePlansDetailsRef [showCancel]="false" [title]="briefing_row?.briefing_title + ' Details'" (onCancel)="onCancel()" (onClickRightPB)="closeDetailReport($event)" rightPrimaryBtnTxt="OK">
        <div *ngIf="(isProjectPortal || isCompanyRoute()) && showModal">
            <form novalidate #detailForm="ngForm">
            <table class="table table-sm table-bordered" style="font-size: 14px;">
                <tbody>
                <tr *ngIf="isProjectPortal
">
                    <td class="tr-bg-dark-color" style="width: 25%;">
                        <strong>Uploaded By:</strong>
                    </td>
                    <td>
                        {{ briefing_row?.user_ref.name }}
                    </td>
                </tr>

                <tr>
                    <td class="tr-bg-dark-color" style="width: 25%;">
                        <strong>Title:</strong>
                    </td>
                    <td>
                        <div class="input-group">
                            <input type="text" class="form-control"
                                   placeholder="{{ wppSglrPhrase }} title..."
                                   name="briefing_title"
                                   required="true"
                                   [disabled]="titleEditDisable"
                                   [(ngModel)]="briefing_title">
                            <div class="input-group-append">
                                <button *ngIf="titleEditDisable" class="btn btn-dark" (click)="enableEdit('title')" type="button">Edit</button>
                                <button *ngIf="!titleEditDisable" [disabled]="!briefing_title" class="btn btn-dark" (click)="updateWPP(briefing_row)"  type="button">Save</button>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="tr-bg-dark-color" style="width: 25%;">
                        <strong>Company:</strong>
                    </td>
                    <td>
                        <div *ngIf="isModalOpen" class="input-group">
                            <company-selector-v2
                                [class.select-with-button]="true"
                                [class]="companyEditDisable ? 'taggedOwnerDD' : 'taggedOwnerSaveDD'"
                                [required]="true"
                                errorClasses="d-none"
                                [country_code]="projectInfo?.custom_field?.country_code"
                                name="tagged_owner"
                                [selectId]="tagged_owner"
                                placeholder="Select Company"
                                (selectionChanged)="tagged_owner = $event.selected"
                                [disabled]="companyEditDisable"
                                [projectId]="projectId"
                            ></company-selector-v2>
                            <div class="input-group-append">
                                <button *ngIf="companyEditDisable" class="btn btn-dark" (click)="enableEdit('company')" type="button">Edit</button>
                                <button *ngIf="!companyEditDisable" [disabled]="!tagged_owner" class="btn btn-dark" (click)="updateWPP(briefing_row)"  type="button">Save</button>
                            </div>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td class="tr-bg-dark-color" style="width: 25%;">
                        <strong>Document:</strong>
                    </td>
                    <td>
                        <a class="text-info" [title]="briefing_row?.briefing_file_ref?.name" [href]="briefing_row?.briefing_file_ref?.file_url" target="_blank"
                        >
                            {{ briefing_row?.briefing_file_ref?.name }}
                        </a>
                    </td>
                </tr>

                <ng-container *ngIf="briefing_row.register.length">
                    <tr>
                        <td class="tr-bg-dark-color" style="width: 25%;">
                            <strong>Last Briefed:</strong>
                        </td>
                        <td>
                            {{dayjs(+briefing_row.register[0]?.briefed_at).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS)}}
                        </td>
                    </tr>
                    <tr>
                        <td class="tr-bg-dark-color" style="width: 25%;">
                            <strong>Last Briefed by:</strong>
                        </td>
                        <td>
                            {{briefing_row.register[0]?.briefed_by_name}}
                            <span *ngIf="briefing_row.register[0]?.induction_company">
                                ({{briefing_row.register[0]?.induction_company}})
                            </span>
                        </td>
                    </tr>
                </ng-container>

                <tr *ngIf="briefing_row.register.length" class="tr-bg-dark-color">
                    <td colspan="2">
                        <strong>Register:</strong>
                    </td>
                </tr>
                <tr *ngIf="briefing_row.register.length">
                    <td colspan="2">
                        <table class="table table-sm table-striped table-borderless m-0">
                            <thead>
                            <tr>
                                <th>Briefing</th>
                                <th>Briefed By</th>
                                <th>No. Briefed</th>
                                <th>Register</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr *ngFor="let briefed of briefing_row.register">
                                <td>{{briefed.briefed_at ? dayjs(+briefed.briefed_at).format(AppConstant.defaultDateFormat) : null}} <br> <small>({{briefed.briefed_at ? dayjs(+briefed.briefed_at).format('HH:mm:ss') : null}})</small></td>
                                <td>{{briefed.briefed_by_name ? briefed.briefed_by_name : null}}</td>
                                <td>{{briefed.allattendees.length}}</td>
                                <td>
                                    <button-group
                                        [buttons]="workPackagePlansRegisterButtonGroup"
                                        (onActionClick)="rowBtnClicked($event, briefed)">
                                    </button-group>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>
            </form>
        </div>
</i-modal>

<i-modal #briefingInviteModalRef title="Invite to Brief" (onCancel)="closeInviteModal(inviteBriefingForm)" (onClickRightPB)="inviteUsers(inviteBriefingForm)" rightPrimaryBtnTxt="Send Invite" [rightPrimaryBtnDisabled]="!inviteBriefingForm.valid || !hasSelectedUsers">
        <form novalidate #inviteBriefingForm="ngForm">
            <tool-invite-modal
                [hasEmployerFilter]="true"
                [hasSearchByName]="true"
                [isFilteredUsersReset]="isFilteredUsersReset"
                [toolSingularPhrase]="wppPhrase"
                [availableRecords]="availableRecords"
                [jobRoles]="jobRoles"
                [availableEmployers]="availableEmployers"
                [projectUsersData]="projectUsers"
                [filteredUsersData]="filteredUsers"
                (onSelectDeselectUser)="checkHasSelectedUsers($event)"
                ></tool-invite-modal>
        </form>
        <div>
            <div *ngIf="inviteApiError" class="alert alert-danger">
                {{ inviteApiError }}
            </div>
            <div *ngIf="inviteApiSuccess" class="alert alert-success">
                {{ inviteApiSuccess }}
            </div>
        </div>
 </i-modal>
<block-loader [show]="(blockLoader || loadingPackagePlans)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
<report-downloader #reportDownloader
    [xlsxOnly]="true"
    (onFilterSelection)="workPackagesPlansReportDownload($event)"
    >
</report-downloader>

<style>
    agm-map {
        /*height:83vh;*/
        height: 66.5vh;
        width: 100%
    }
    .avtar-img {
        border-radius: 50% !important;
        width: 80px;
        position: absolute;
        top: 6px;
        left: 6px;
    }
    .icon-tooltip {
        width: 12px;
        cursor: pointer;
        position: absolute;
        top: 3px;
        right: 20px;
    }
    .badge-position {
        position: absolute;
        top: 20px;
        right: 4px;
    }
    .icon-tooltip-hower:hover {
        filter: brightness(0.25);
    }
    .bg-color-red { background-color: #DC3545; }
    .bg-color-green { background-color: #28A745; }
    .bg-color-grey { background-color: #B2B2B2; }
    .color-white { color: #FFFFFF; }
    .text-color-grey { color: #B2B2B2; }
    .img-border-grey { border: 4px solid #B2B2B2;}
    .img-border-red { border: 4px solid #DC3545;}
    .modal-header {
        background-color: var(--cultured);
        border: none;
    }
</style>
<div>
    <div *ngIf="!showMapWithMultiPin">
        <agm-map 
            [latitude]="+row_data?.location?.lat"   
            [longitude]="+row_data?.location?.long" 
            [zoom]="17"
            [fullscreenControl]="true" 
            [mapTypeControl]="true" 
            scrollwheel="false" 
            streetViewControl="false"
        >
            <agm-marker
                [title]="row_data?.user_ref?.first_name+' '+row_data?.user_ref?.last_name" 
                [latitude]="+row_data?.location?.lat"
                [longitude]="+row_data?.location?.long"
                [iconUrl]="row_data?.status_message === 'Open' ? redLocationPin : blackLocationPin"
            >
                <agm-info-window [disableAutoPan]="false" #infoWindow>
                    <div style="min-width: 300px; max-height: 170px;">
                        <img title="View" class="icon-tooltip icon-tooltip-hower" src="/assets/images/view-tooltip-icon.png" alt="" (click)="dismiss ? dismiss('Cross click') : null">
                        <table class="table table-borderless mb-0">
                            <tbody>
                                <tr>
                                    <td width="28%" class="p-0">
                                        <img class="img-thumbnail avtar-img p-0"
                                            [ngClass]="row_data?.status_message === 'Open' ? 'img-border-red bg-color-red' : 'img-border-grey bg-color-grey'"
                                            [src]="row_data?.user_ref?.profile_pic_ref?.sm_url ? row_data?.user_ref?.profile_pic_ref?.sm_url : avatar_dummy_pic"
                                            alt="">
                                    </td>
                                    <td width="72%" class="p-0">
                                        <div>
                                            <h6 *ngIf="row_data?.pp_title">
                                                {{row_data?.pp_title}}
                                            </h6>
                                            <h6 *ngIf="!row_data?.pp_title">
                                                {{mapTitle}} #{{row_data?.cc_number ? row_data?.cc_number : row_data?.record_id}}
                                            </h6>
                                        </div>
                                        <ng-container *ngIf="row_data?.status_message">
                                            <span *ngIf="row_data?.status_message === 'Open'" class="badge badge-pill bg-color-red badge-position">
                                                <small class="color-white">{{row_data?.status_message}}</small>
                                            </span>
                                            <span *ngIf="row_data?.status_message === 'Closed Out'" class="badge badge-pill bg-color-green badge-position">
                                                <small class="color-white">{{row_data?.status_message}}</small>
                                            </span>
                                        </ng-container>
                                        <div class="mt-1">
                                            <span *ngIf="feature === 'pp' || feature === 'inspections'" class="text-color-grey"><b><small>Submitted By: </small></b></span>
                                            <span *ngIf="feature === 'cc' || feature === 'gc'" class="text-color-grey"><b><small>Raised By: </small></b></span>
                                            <span>
                                                <small *ngIf="row_data && row_data.user_ref">
                                                    {{ row_data.is_anonymous ? "Anonymous" : row_data?.user_ref?.first_name+' '+row_data.user_ref?.last_name}}
                                                </small>
                                            </span>
                                        </div>
                                        <div class="mt-1">
                                            <span *ngIf="feature === 'pp' || feature === 'inspections'" class="text-color-grey"><b><small>Date Submitted: </small></b></span>
                                            <span *ngIf="feature === 'cc' || feature === 'gc'" class="text-color-grey"><b><small>Date Raised: </small></b></span>
                                            <span>
                                                <small *ngIf="row_data && row_data.createdAt">
                                                    {{ dayjs(row_data.createdAt).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) }}
                                                </small>
                                            </span>
                                        </div>
                                        <div class="mt-1" *ngIf="row_data?.hazard_category">
                                            <span *ngIf="feature === 'cc'" class="text-color-grey"><b><small>Hazard Category: </small></b></span>
                                            <span *ngIf="feature === 'pp' || feature === 'gc'" class="text-color-grey"><b><small>Category: </small></b></span>
                                            <span>
                                                <small>
                                                    {{ row_data.hazard_category }}
                                                </small>
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="28%" class="p-0"></td>
                                    <td width="72" class="p-0">
                                        <div class="w-100 d-flex flex-wrap justify-content-start mt-2">
                                            <ng-container *ngIf="row_data?.pp_images || row_data?.cc_images">
                                                <div *ngFor="let img_data of row_data.pp_images ? row_data.pp_images : row_data.cc_images">
                                                    <img 
                                                        [src]="img_data && img_data.sm_url ? img_data.sm_url : img_data?.file_url" 
                                                        alt="image not found"
                                                        class="img-thumbnail p-0 mr-2"
                                                        style="width: 60px;"
                                                    >
                                                </div>
                                            </ng-container>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </agm-info-window>
            </agm-marker>
        </agm-map>
    </div>
    <div *ngIf="showMapWithMultiPin">
        <agm-map 
            [latitude]="defaultLat ? defaultLat : ''" 
            [longitude]="defaultLong ? defaultLong : ''"
            [zoom]="defaultLat && defaultLong ? 12 : 2"
            [fullscreenControl]="true"
            [mapTypeControl]="true"
            scrollwheel="false" 
            streetViewControl="false">
            <agm-marker *ngFor="let project of project_data" 
                [title]="project?.user_ref?.first_name+' '+project?.user_ref?.last_name"
                [latitude]="project?.location?.lat" 
                [longitude]="project?.location?.long"
                [iconUrl]="project?.status_message === 'Open' ? redLocationPin : blackLocationPin"
            >
                <agm-info-window [disableAutoPan]="false" #infoWindow>
                    <div style="min-width: 300px; max-height: 170px;">
                        <img title="View" class="icon-tooltip icon-tooltip-hower" src="/assets/images/view-tooltip-icon.png" alt="" (click)="crossClick(project)">
                        <table class="table table-borderless mb-0">
                            <tbody>
                                <tr>
                                    <td width="28%" class="p-0">
                                        <img class="img-thumbnail avtar-img p-0"
                                            [ngClass]="project?.status_message === 'Open' ? 'img-border-red bg-color-red' : 'img-border-grey bg-color-grey'"
                                            [src]="project?.user_ref?.profile_pic_ref?.sm_url ? project?.user_ref?.profile_pic_ref?.sm_url : avatar_dummy_pic"
                                            alt="">
                                    </td>
                                    <td width="72%" class="p-0">
                                        <div>
                                            <h6 *ngIf="project?.pp_title">
                                                {{project?.pp_title}}
                                            </h6>
                                            <h6 *ngIf="!project?.pp_title">
                                                {{mapTitle}} #{{project?.cc_number ? project?.cc_number : project?.record_id}}
                                            </h6>
                                        </div>
                                        <ng-container *ngIf="project?.status_message">
                                            <span *ngIf="project.status_message === 'Open'" class="badge badge-pill bg-color-red badge-position">
                                                <small class="color-white">{{project.status_message}}</small>
                                            </span>
                                            <span *ngIf="project.status_message === 'Closed Out'" class="badge badge-pill bg-color-green badge-position">
                                                <small class="color-white">{{project.status_message}}</small>
                                            </span>
                                        </ng-container>
                                        <div class="mt-1">
                                            <span *ngIf="feature === 'pp'" class="text-color-grey"><b><small>Submitted By: </small></b></span>
                                            <span *ngIf="feature === 'cc' || feature === 'gc'" class="text-color-grey"><b><small>Raised By: </small></b></span>
                                            <span>
                                                <small>
                                                    {{project.is_anonymous ? "Anonymous" : project.user_ref?.first_name+' '+project.user_ref?.last_name}}
                                                </small>
                                            </span>
                                        </div>
                                        <div class="mt-1">
                                            <span *ngIf="feature === 'pp'" class="text-color-grey"><b><small>Date Submitted: </small></b></span>
                                            <span *ngIf="feature === 'cc' || feature === 'gc'" class="text-color-grey"><b><small>Date Raised: </small></b></span>
                                            <span>
                                                <small>
                                                    {{ dayjs(project.createdAt).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS)}}
                                                </small>
                                            </span>
                                        </div>
                                        <div class="mt-1" *ngIf="project?.hazard_category">
                                            <span *ngIf="feature === 'cc'" class="text-color-grey"><b><small>Hazard Category: </small></b></span>
                                            <span *ngIf="feature === 'pp' || feature === 'gc'" class="text-color-grey"><b><small>Category: </small></b></span>
                                            <span>
                                                <small>
                                                    {{ project.hazard_category }}
                                                </small>
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="28%" class="p-0"></td>
                                    <td width="72" class="p-0">
                                        <div class="w-100 d-flex flex-wrap justify-content-start mt-2">
                                            <ng-container *ngIf="project?.pp_images || project.cc_images">
                                                <div *ngFor="let img_data of project.pp_images ? project.pp_images : project.cc_images">
                                                    <img 
                                                        [src]="img_data && img_data.sm_url ? img_data.sm_url : img_data?.file_url" 
                                                        alt="image not found"
                                                        class="img-thumbnail p-0 mr-2"
                                                        style="width: 60px;"
                                                    >
                                                </div>
                                            </ng-container>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </agm-info-window>
            </agm-marker>
        </agm-map>
    </div>
</div>
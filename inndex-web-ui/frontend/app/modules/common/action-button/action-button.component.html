<style>
    .option-padding {
        padding: 14px 20px;
    }
/*    .btn-hover:hover {
        background-color: var(--Lavender);
        color: var(--brandeis-blue) !important;
    }*/
</style>
<div class="p-0 text-right d-flex flex-column flex-sm-row w-100 justify-content-end gap-8">
    <button *ngIf="!hideNewFeatureBtn" class="btn btn-sm btn-outline-brandeis-blue m-btn-size-lg" id="newBtn" (click)="onOpenAddNew.emit()">
        <div class="d-flex align-items-center justify-content-center">
            <span class="material-symbols-outlined x-large-font mr-1">
                {{iconText}}
            </span>
            <span class="mr-1">{{newFeatureTitle}}</span>
        </div>
    </button>
    <div *ngIf="showActionDropdown" ngbDropdown class="filter-selection float-right" placement="bottom-right" (openChange)="toggleArrow($event)">
        <button [ngClass]="{'btn-outline-brandeis-blue': lightBgButton, 'btn-brandeis-blue': !lightBgButton, 'btn btn-sm d-flex align-items-center justify-content-between float-md-right not-dropdown m-btn-size-lg m-w-100': true}" ngbDropdownToggle>
            {{actionListTitle}}
            <span class="material-symbols-outlined x-large-font ml-1">
                {{(isDropdownOpen) ? 'expand_less' : 'expand_more'}}
            </span>
        </button>
        <ul ngbDropdownMenu class="table-dropdown-menu py-0 m-w-100">
            <ng-container *ngFor="let action of actionList">
                <li *ngIf="action.enabled" ngbDropdownItem class="cursor-pointer d-flex option-padding" [ngClass]="{'disabled': action.disabled}" (click)="actionTrigger(action.code, action.name, action.disabled)">
                    <div class="d-flex justify-content-center align-items-center mr-3">
                        <span class="xxx-large-font" [class]="action.iconClass">{{action.iconClassLabel}}</span>
                    </div>
                    <div> <span class="medium-font">{{action.name}}</span> </div>
                </li>
            </ng-container>
        </ul>
    </div>
</div>

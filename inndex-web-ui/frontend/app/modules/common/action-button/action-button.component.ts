import {Component, OnInit, Input, Output, EventEmitter} from '@angular/core';
import { AssetsUrl } from '@app/shared';

@Component({
  selector: 'action-button',
  templateUrl: './action-button.component.html',
})
export class ActionButtonComponent implements OnInit {

  AssetsUrlSiteAdmin = AssetsUrl.siteAdmin;

  @Input()
  actionList:Array<any> = [];

  @Input()
  newFeatureTitle: string;

  @Input()
  hideNewFeatureBtn: boolean = false;

  @Input()
  iconText: string = 'add';

  @Input()
  showActionDropdown: boolean = true;

  @Input()
  actionListTitle: string = 'Actions';

  @Input()
  lightBgButton: boolean = false;

  @Output() selectedActionEmmiter: any = new EventEmitter<any>();
  @Output() onOpenAddNew: any = new EventEmitter<any>();
  isDropdownOpen: boolean = false;

  constructor() { }

  ngOnInit(): void {
  }

  public actionTrigger(code: number, name: string, disabled?: boolean) {
    if(disabled) {
      return false;
    }
    this.selectedActionEmmiter.emit({code, name });
  }

  toggleArrow(event) {
    this.isDropdownOpen = (event === true) ? true: false ;
  }
}

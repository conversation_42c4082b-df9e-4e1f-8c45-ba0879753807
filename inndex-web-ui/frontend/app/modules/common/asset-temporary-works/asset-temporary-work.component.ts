import { Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild, ElementRef, QueryList, ViewChildren  } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbDateStruct, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
    AuthService,
    Project,
    ProjectAssetTemporaryWork,
    ProjectAssetService,
    ProjectService,
    User,
    ToastService,
    UserService,
    WeeklyTimeSheet,
    ResourceService,
    Common, UACProjectDesignations,
    AssetActivityLog,
    AssetManagementConfModalType,
    AssetTemporaryWorksActionButtons,
    ActionButtonVal, CompanyAssetConfigService,
    AssetCustomConfig,
    AssetCustomField,
    AssetCustomConfigFieldTypes,
} from "@app/core";
import { NgbMomentjsAdapter } from "@app/core/ngb-moment-adapter";
import { DomSanitizer } from "@angular/platform-browser";
import { AppConstant } from "@env/environment";
import * as dayjs from 'dayjs';
import { UsersSelectorModalComponent } from "../users-selector-modal/users-selector-modal.component";
import { FaultCloseoutComponent } from "../fault-closeout/fault-closeout.component";
import { AssetDeclineComponent } from "../asset-decline/asset-decline.component";
import { HttpParams } from '@angular/common/http';
import { fromEvent } from "rxjs";
import {debounceTime, distinctUntilChanged, filter, tap} from "rxjs/operators";
import { AssetsUrl } from '@app/shared/assets-urls/assets-urls';
import { filterData } from "@app/core";
import { NgbDatepicker } from '@ng-bootstrap/ng-bootstrap';
import { forkJoin } from "rxjs";
import {SLIDE_UP_DOWN_ON_SHOW_HIDE} from "@app/core/animations";
import { ActionBtnEntry, GenericConfirmationModalComponent, IModalComponent } from "@app/shared";

@Component({
    animations: [SLIDE_UP_DOWN_ON_SHOW_HIDE],
    templateUrl: './asset-temporary-work.component.html',
    selector: 'asset-temporary-works',
})
export class AssetTemporaryWorkComponent implements OnInit {

    @Input()
    projectId: number = 0;

    @Input()
    employerId: number = 0;

    @Input()
    project: any;

    @Input()
    isProjectPortal: boolean = false;

    @Output()
    isChildComponentLoaded: any = new EventEmitter<any>();

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    @ViewChild('faultCloseOutRef') faultCloseOutRef: FaultCloseoutComponent;
    @ViewChild('assetDeclineRef') assetDeclineRef: AssetDeclineComponent;

    authUser$: User;
    employer: any = {};
    dayjsFormat(x:number, fullDate: boolean = true){
        return (+x) ? dayjs(+x).format(fullDate ? AppConstant.fullDateTimeFormat : AppConstant.defaultDateFormat) : '';
    }
    allowedMime: Array<any> = ['image/jpeg', 'image/jpg', 'image/png'];

    assetTemporaryWork: ProjectAssetTemporaryWork = new ProjectAssetTemporaryWork;
    addTemporaryWorkModal : any = null;
    assetTemporaryIcons = AssetsUrl.assetTemporaryIcons;


    Type_Of_Temporary_Works: Array<any> = [];
    examinationCertPreviewURL: any;
    serviceCertPreviewURL: any;
    motCertPreviewURL: any;

    companiesListFilter: Array<any> = [];
    taggedOwners: Array<any> = [];
    archivedTaggedOwners: Array<any> = [];
    isTemporaryWorkImageUploaded: boolean = false;
    tempWorkRecords: Array<any> = [];
    archivedtempWorkRecords: Array<any> = [];
    filteredTempWorkRecords: Array<any> = [];
    tempWorkLoading: boolean = false;

    minDate: NgbDateStruct = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs().add(1, 'day'));
    globalSearch: any = '';
    ownerFilter: any[] = [];
    tempWorksFilter: any[] = [];
    viewTempWorkOptionModalRef: any = null;
    dailyInspectionOptionsModalRef: any = null;

    displayDateFormat: string = AppConstant.defaultDateFormat;
    downloadingWeeklyInspection: boolean = false;
    hasInspections: boolean = false;
    loadingTempWorkRecords: boolean = false;
    projectAdminIds: Array<any> = [];
    project_admins: Array<any> = [];
    tempWorkManagerIds: Array<any> = [];
    temp_works_managers: Array<any> = [{}];
    projectRequest: Project = new Project;
    declined_comment: string = '';
    onSiteTempWorkCount: number = 0;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    closeFormModal: boolean = true;
    years: number[] = [];
    dropdownIndex: number = null;
    fileModalTitle: string = '';
    activeNav: number = 1;
    week_Days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    archivedTempWorkListModalRef: any = null;
    tempWorkFaults: Array<any> = [];
    faultCloseOutReq: any = {};
    blockLoader: boolean = false;
    hasOnlySiteManagement: boolean = false;
    userEmployer: any = {};
    projectResolverResponse: any = {};
    assetActivityLogs: Array<AssetActivityLog> = [];
    isListView: boolean = false;
    paginationData = new Common();
    page = this.paginationData.page;
    riskCategories: Array<number> = [0, 1, 2, 3];
    STATUS_CODES_LIST: Array<any> = new Common().ASSET_STATUS_CODES;
    selectedStatus: any[] = [];
    filterData:filterData[] = this.renderFilterData();
    renderFilter:boolean = false;
    actionButtonMetaData = {
        actionList: [],
        class: 'material-symbols-outlined',
    };
    showTemporaryWorkModal: boolean = false;
    showPhotoCollage: boolean = false;
    indexNumber: number = 0;
    showModal: boolean = false;
    metaConfig;
    assetConfig: AssetCustomConfig = new AssetCustomConfig;
    assetTypeSelected: boolean = true;
    assetCustomConfigFieldTypes = AssetCustomConfigFieldTypes;
    baseButtonConfig: Array<ActionBtnEntry> = [
        {
            key: 'view',
            label: '',
            title: 'View',
            mat_icon: 'search'
        },
        {
            key: 'unarchive',
            label: '',
            title: 'Unarchive Vehicle',
            mat_icon: 'undo'
        }
    ];
    dayjs(x:number, fullDate:boolean = true){
        return (+x) ? dayjs(+x).format(fullDate ? AppConstant.fullDateTimeFormat : AppConstant.defaultDateFormat) : '';
    }
    certificatePreviewURL: any;
    certificateUrl: string = '';
    isPdfDocument: boolean = false;
    showPdfImg = false;

    public maxlength: number = 30;
    weeklyInspectionButtonConfig: Array<ActionBtnEntry> = [
        {
            key: 'viewWeeklyInspection',
            label: '',
            title: 'View',
            mat_icon: 'search'
        },
        {
            key: 'downloadWeeklyInspection',
            label: '',
            title: 'Download',
            mat_icon: 'download'
        }
    ];
    faultActionButtonConfig: Array<ActionBtnEntry> = [
        {
            key: 'closeOutFault',
            label: '',
            title: 'Close Out',
            mat_icon: 'close'
        }
    ];

    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private modalService: NgbModal,
        private toastService: ToastService,
        private authService: AuthService,
        private projectService: ProjectService,
        private userService: UserService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private domSanitizer: DomSanitizer,
        private projectAssetService: ProjectAssetService,
        private resourceService: ResourceService,
        private companyAssetConfigService: CompanyAssetConfigService,
    ) {
        // this.resourceService.getInnDexSettingByName('type_of_temporary_works_en_gb').subscribe((data: any) => {
        //     if (data.success && data.record && data.record.value) {
        //         this.Type_Of_Temporary_Works = data.record.value;
        //     } else {
        //         alert('Something went wrong while getting type of temporary works.');
        //     }
        // });

        this.years = new Common().getYears(2000);
    }

    ngOnInit() {
        if (!this.isProjectPortal) {
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        }
        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
            }
        });

        this.projectAdminIds = (this.project.admins || []).reduce((arr, item) => {
            if (item.user_ref) {
                arr.push(item.user_ref);
            }
            return arr;
        }, []);
        this.getViewType();
        this.getTaggedOwnersList();
        this.hasOnlySiteManagement = (this.project._my_designations.length === 1 && this.project._my_designations.includes(UACProjectDesignations.DELIVERY_MANAGER));
        this.settempWorkManagerIds(this.project);
        this.loadingTempWorkRecords = true;
        this.page.size = 10;
        if(this.isListView) {
            this.initiateDataRequests();
        } else {
            this.getCompanyAssetConfig();
        }
        this.actionButtonMetaData.actionList = [
            {
                code: AssetTemporaryWorksActionButtons.TEMPORARY_WORKS_MANAGERS,
                name: `Temp. Works Managers`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'person',
                enabled: true,
                disabled: false,
            },
            {
                code: AssetTemporaryWorksActionButtons.ARCHIVED_LIST,
                name: `Archived List`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'inventory_2',
                enabled: true,
                disabled: false,
            },
            {
                code: AssetTemporaryWorksActionButtons.DOWNLOAD_REGISTER,
                name: `Download Register`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: true,
                disabled: false,
            },
        ];
    }

    initiateDataRequests() {
        const assetParams = this.setParams();
        let getProjectAssetTempWorks = this.projectAssetService.getProjectAssetTemporaryWorks(this.projectId, assetParams);
        const params = new HttpParams().set('assetType',  'asset-temporary');
        let getCompanyConfig = this.companyAssetConfigService.getProjectAssetConfig(this.project.id, params)
        this.initTempWorks();
        forkJoin([getProjectAssetTempWorks, getCompanyConfig]).subscribe(res => {
            let responseList: any = res;

            let errorKey = Object.keys(responseList).find(key => !responseList[key].success);
            if (responseList[errorKey]) {
                this.loadingTempWorkRecords = false;
                alert(responseList[errorKey].message ? responseList[errorKey].message : 'Failed to get data');
                console.log('one of the API call failed.', responseList[errorKey]);
                return;
            }

            this.metaConfig = responseList[1].records;
            this.mapAlternatePhrase();
            this.processTempWorkData(responseList[0]);
        });
    }

    getCompanyAssetConfig() {
        const params = new HttpParams().set('assetType',  'asset-temporary');
        this.companyAssetConfigService.getProjectAssetConfig(this.project.id, params).subscribe((data: any)=> {
            if(data.error) {
                alert("Invalid request!")
                return;
            }
            this.metaConfig = data.records;
            this.mapAlternatePhrase();
        });
    }


    mapAlternatePhrase() {
        this.Type_Of_Temporary_Works = this.metaConfig.map(a=> {
            return {
                key: a.key,
                value: a.name,
                alternate_phrase: a.alternate_phrase
            }
        });
        this.filterData= this.renderFilterData();
    }

    syncCustomFields() {
        let updatedCustomFields = [];
        this.assetConfig.custom_fields.forEach((configField, index) => {
            const matchingIndex = this.assetTemporaryWork.custom_fields.findIndex(
                tempField => tempField.key === configField.key
            );
            if (matchingIndex !== -1) {
                if(this.assetTemporaryWork.custom_fields[matchingIndex].type === AssetCustomConfigFieldTypes.Certification) {
                    this.assetTemporaryWork.custom_fields[matchingIndex].certificates = [...this.assetTemporaryWork.custom_fields[matchingIndex].certificates, {}];
                }
                updatedCustomFields[index] = {
                    ...this.assetTemporaryWork.custom_fields[matchingIndex],
                };
            } else {
                //add newly added field
                if(configField.type === AssetCustomConfigFieldTypes.Certification) {
                    configField.certificates = [{}];
                } else if(configField.type === AssetCustomConfigFieldTypes.Date) {
                    configField[configField.key] = null;
                }
                updatedCustomFields[index] = {
                    ...configField,
                };
            }
        });
        this.assetTemporaryWork.custom_fields = updatedCustomFields;
    }

    assetTypeChanged() {
        this.blockLoader = true;
        
        let params = new HttpParams()
            .set('assetType', 'asset-temporary')
            .set('assetKey', this.assetTemporaryWork.type_of_works)
            .set('extra', 'fields');
        this.companyAssetConfigService.getProjectAssetConfig(this.project.id, params).subscribe((data:any) => {
            if(data.success && data.records) {
                this.assetConfig = data.records.find(m=> m.key === this.assetTemporaryWork.type_of_works);
                this.assetConfig = this.assetConfig ? this.assetConfig: {} as Partial<AssetCustomConfig>;
                this.setAssetConfig();
                this.assetTypeSelected = this.assetTemporaryWork.type_of_works ? false: true;
            }
            this.blockLoader = false;
        });  
    }

    setAssetConfig() {
        if(this.assetConfig && this.assetTemporaryWork.type_of_works) {
            this.assetConfig.defaultFields = this.assetConfig.default_fields.reduce((a, v) => ({ ...a, [v.key]: v}), {});
            if(this.assetTemporaryWork.id) {
                this.syncCustomFields();
            } else {
                this.assetTemporaryWork.custom_fields = [];
                this.assetTemporaryWork.custom_fields = this.assetConfig.custom_fields.map(d=> {
                    let a: any = {
                        'key': d.key,
                        'title': d.title,
                        'type': d.type,
                        'value': null
                    };
                    if(d.type === AssetCustomConfigFieldTypes.Certification) {
                       a.certificates = [{}];
                    }
                    return a;
                });
            }
        }
    }

    toggler(activeNav: number) {
        this.activeNav = activeNav;
    }

    openCloseDropdown(index) {
        this.dropdownIndex = index !== this.dropdownIndex ? index : null;
    }


    settempWorkManagerIds(project) {
        this.tempWorkManagerIds = (project.custom_field.temp_works_managers || []).reduce((arr, item) => {
            if (item && typeof item === 'number') {
                arr.push(item);
            }
            return arr;
        }, []);
    }

    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }) {
        console.log("here we are in pageCallback", this.page.pageNumber, pageInfo.offset);
        this.page.pageNumber = pageInfo.offset;
        console.log("here we are in pageCallback", this.page.pageNumber);
        this.initTempWorks();
    }

    paginationCallback(event) {
        console.log("here we are in 2",event, event-1, this.page.pageNumber,);
        this.page.pageNumber = event-1;
        this.initTempWorks();
    }

    setParams() {
        let params = new HttpParams()
        .set('pageNumber', `${this.page.pageNumber}`)
        .set('pageSize', `${this.page.size}`)

        if(this.globalSearch !== null){
            params = params.append('search',`${this.globalSearch}`);
        }
        if(this.tempWorksFilter.length != 0){
            params  = params.append('type', `${this.tempWorksFilter.join(",")}`);
        }
        if(this.ownerFilter.length != 0){
            params = params.append('owner', `${this.ownerFilter.join(",")}`);
        }
        if (this.selectedStatus.length) {
            params = params.append('status', `${this.selectedStatus.join(",")}`);
        }
        params = params.append('expand_inspections', false.toString());
        params = params.append('include_archived', true.toString());

        return params;
    }

    getTaggedOwnersList(is_archived = 'false') {
        this.projectAssetService.getAssetTaggedOwners(this.projectId, is_archived, 'asset-temporary').subscribe((res:any) => {
            if(res.success) {
                if (is_archived === 'false') {
                    this.taggedOwners = res.tagged_owners;
                    this.filterData = this.renderFilterData();
                } else {
                    this.archivedTaggedOwners = res.tagged_owners;
                }
            }
        })
    }


    initTempWorks() {
        console.log("here we are in initTempWorks");
        let params = this.setParams();
        this.projectAssetService.getProjectAssetTemporaryWorks(this.projectId, params).subscribe((data: any) => {
            this.processTempWorkData(data);
        });
    }

    processTempWorkData(data) {
        if (data.success && data.project_asset_temp_works) {
            this.isChildComponentLoaded.emit(false);
            this.loadingTempWorkRecords = false;
            let temp_work_list = data.unarchived_asset_temp_works;
            this.onSiteTempWorkCount = (temp_work_list || []).reduce((accumulator, item) => {
                if (item.approval_pending === 2) {
                    accumulator += 1;
                }
                return accumulator;
            }, 0);
            this.page.totalElements = data.totalCount;
            this.tempWorkRecords = temp_work_list;
            this.filteredTempWorkRecords = temp_work_list;
            this.filteredTempWorkRecords.forEach(record => {
                record.type_of_works_name = this.getWorksType(record.type_of_works);
                record.expiry_dates = [];
                record.expiry_dates_count = 0;
                if (record?.examination_cert_expiry_date) {
                    const isExpired = this.isExpired(record.examination_cert_expiry_date);
                    record.expiry_dates.push({ 'name': 'Examination', 'expiry_date': record.examination_cert_expiry_date, 'isExpired': isExpired });
                    if (isExpired === 0) {
                        record.expiry_dates_count += 1;
                    }
                }
                if (record?.latest_inspection?.createdAt) {
                    record.lastDailyInspectionAt = record.latest_inspection;
                }
                record.last_inspected = record?.lastDailyInspectionAt?.updatedAt ? this.dayjsFormat(record.lastDailyInspectionAt.updatedAt, false) : 'N/A';
                for(let k=0; k<record.custom_fields.length; k++) {
                    if(record.custom_fields[k].type === this.assetCustomConfigFieldTypes.Certification) {
                        const isExpired = this.isExpired(record.custom_fields[k].expiry_date);
                        record.expiry_dates.push({ 'name': record.custom_fields[k].title, 'expiry_date': record.custom_fields[k].expiry_date, 'isExpired': isExpired });
                        if (isExpired === 0) {
                            record.expiry_dates_count += 1;
                        }
                    }
                }

                if (record?.expiry_dates?.length) {
                    record.expiry_dates.sort((a, b) => a.isExpired - b.isExpired);
                }
                record.expired_certs = `${record.expiry_dates_count}/${record.expiry_dates.length}`;
            });
            this.archivedtempWorkRecords = data.archived_asset_temp_works;this.actionButtonMetaData.actionList.forEach(item => {
                    if(item.code === AssetTemporaryWorksActionButtons.DOWNLOAD_REGISTER){
                        item.disabled = !(this.filteredTempWorkRecords.length || this.archivedtempWorkRecords.length);
                    }
                    if(item.code === AssetTemporaryWorksActionButtons.ARCHIVED_LIST){
                        item.disabled = !this.archivedtempWorkRecords.length;
                    }
                });
                this.archivedtempWorkRecords = data.archived_asset_temp_works;
                this.userEmployer = data.userEmployer;
                if(!this.renderFilter){
                    this.filterData= this.renderFilterData();
                    this.renderFilter=true;
                }
            }

    }

    private isExpired(expiry_date: number) {
        const today = dayjs().unix() * 1000;
        let priorDateInTimestamp = dayjs().add(30, 'day').unix() * 1000;
        if (expiry_date < today) {
            return 0;
        } else if (expiry_date > today && expiry_date <= priorDateInTimestamp) {
            return 1;
        } else if (expiry_date > priorDateInTimestamp) {
            return 2;
        }
    }

    dayjsTz(n: number) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };

    associateAvailableMondays(tempWork_list) {
        tempWork_list.forEach(vl => {
            let work_inspections = vl.temporary_inspections;
            let availableMondays = (work_inspections || []).reduce((arr, vi) => {
                let dateOnMonday = dayjs(+vi.createdAt).startOf('isoWeek').format(this.displayDateFormat);
                let existingWeekIndex = arr.findIndex(v => v.date === dateOnMonday);
                let existingWeek = arr.find(v => v.date === dateOnMonday);
                let weekDay = this.dayjsTz(+vi.createdAt).format('ddd');
                let createdAt = this.dayjsTz(+vi.createdAt).format(this.displayDateFormat+'  HH:mm:ss');
                let user_name = vi.user_ref.first_name + ' ' + vi.user_ref.last_name;

                if (existingWeekIndex == -1) {
                    let newWeek: any = {};
                    newWeek.date = dateOnMonday;
                    newWeek.weekdays = [{
                        day: weekDay,
                        user_name,
                        createdAt
                    }];
                    arr.push(newWeek)
                } else {
                    let weekDayIndex = (existingWeek.weekdays || []).findIndex(v => v.day === weekDay);
                    if (weekDayIndex == -1) {
                        existingWeek.weekdays.push({
                            day: weekDay,
                            user_name,
                            createdAt
                        });
                    }
                    arr[existingWeekIndex] = existingWeek;
                }

                let inspectionFaults = (vi.fault_details || []).reduce((arr, fault) => {
                    fault.vi_ref = vi.id;
                    fault.vi_createdAt = vi.createdAt;
                    fault.status = (!fault.status || !fault.closedout_at) ? 'open' : fault.status;
                    arr.push(fault);
                    return arr;
                }, []);

                this.tempWorkFaults.push(...inspectionFaults);

                return arr;
            }, []);
            vl.availableMondays = availableMondays.sort((a, b) => (dayjs(b.date, this.displayDateFormat).diff(dayjs(a.date, this.displayDateFormat))));
        });
        return tempWork_list;
    }

    dayIndex(weekdays, day): number {
        return weekdays.findIndex(d => d.day === day);
    }

    // viewFileModal(viewFileModal, fileUrl, title) {
    //     this.certificateUrl = fileUrl;
    //     this.fileModalTitle = title;
    //     if (fileUrl) {
    //         this.openModal(viewFileModal, 'lg');
    //     }
    // }


    @ViewChild('viewFileRef') private viewFileRef: IModalComponent;
    viewFileModal(fileUrl, title) {
        this.certificateUrl = fileUrl;
        this.fileModalTitle = title;
        if(fileUrl) {
            this.showPdfImg = true;
            this.isPdfDocument = this.isPDF(this.certificateUrl, this.fileModalTitle);
            this.viewFileRef.open();
        }
    }

    closePdfImgModal(event?) {
        this.showPdfImg = false;
        if(event) {
            event.closeFn();
        }
    }

    getTempWork(itemId: number, isEditable: boolean, viewOptionModal: boolean) {
        this.projectAssetService.getTemporaryWorkAsset(this.projectId, itemId, 'true').subscribe((data: any) => {
            this.blockLoader = false;
            this.assetTypeSelected = true;
            if (viewOptionModal) {
                this.viewTempWorkOptionsRef.open();
            }
            if (data.success && data.asset_temporary) {
                let todayMs = dayjs().valueOf();

                data.asset_temporary.examination_certificates.push({});
                if(data.asset_temporary.examination_cert_expiry_date && (+data.asset_temporary.examination_cert_expiry_date - todayMs) >= 0) {
                    data.asset_temporary._examinationCertExpiryDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+data.asset_temporary.examination_cert_expiry_date));
                }

                data.asset_temporary._arrivedAt = (data.asset_temporary.arrived_at) ? this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+data.asset_temporary.arrived_at)) : null;

                this.assetTemporaryWork = data.asset_temporary;
                this.tempWorkFaults = [];
                let worksWithInspections = this.associateAvailableMondays([data.asset_temporary]);
                this.assetTemporaryWork = worksWithInspections[0];
                this.hasInspections = (this.assetTemporaryWork['availableMondays'] && this.assetTemporaryWork['availableMondays'].length) ? true : false;
                this.manageTempWorkFaults();
                if (isEditable) {
                    this.assetTypeChanged();
                    this.transformDatesToNgb();
                    if (this.viewTempWorkOptionsRef) {
                        this.viewTempWorkOptionsRef.close();
                        this.assetTemporaryWork.tagged_owner = (this.assetTemporaryWork.tagged_owner || []).map(owner => owner.id);
                        this.showTemporaryWorkModal = true;
                        this.checkPhotoCollage();
                        this.addTemporaryWorkModalRef.open();
                    }
                }
                this.isTemporaryWorkImageUploaded = false;
                if (this.assetTemporaryWork.photos.length && this.assetTemporaryWork.photos[0].file_url) {
                    this.isTemporaryWorkImageUploaded = true;
                }
            }
        });
    }

    manageTempWorkFaults() {
        let openFaults = [];
        let closedFaults = [];
        this.tempWorkFaults = (this.tempWorkFaults).sort((a, b) => (a.fault_id < b.fault_id) ? 1 : ((b.fault_id < a.fault_id) ? -1 : 0))
        this.tempWorkFaults.map(fault => {
            if ((!fault.status && !fault.closedout_at) || fault.status == 'open') {
                openFaults.push(fault);
            } else {
                closedFaults.push(fault);
            }
        });
        closedFaults = (closedFaults).sort((a, b) => (a.closedout_at < b.closedout_at) ? 1 : ((b.closedout_at < a.closedout_at) ? -1 : 0))

        this.tempWorkFaults = [...openFaults, ...closedFaults];
    }

    getCompanies() {
        let companyIds = [];
        this.companiesListFilter = [];
        (this.tempWorkRecords || []).map(tempwork => {
            if (tempwork.tagged_owner && tempwork.tagged_owner.length) {
                (tempwork.tagged_owner || []).map(owner => {
                   if (owner.id && !companyIds.includes(owner.id)) {
                       companyIds.push(owner.id);
                       this.companiesListFilter.push({ 'id': owner.id, 'name': owner.name });
                   }
                });
            }
        });
        this.companiesListFilter.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
        this.tempWorkLoading = false;
    }

    @ViewChild('addTemporaryWorkModalRef')
    private addTemporaryWorkModalRef: IModalComponent;
    addTemporaryWorkPopup() {
        this.isTemporaryWorkImageUploaded = false;
        this.assetTemporaryWork = new ProjectAssetTemporaryWork();
        this.assetTemporaryWork.tagged_owner = (this.hasOnlySiteManagement && this.userEmployer.id) ? [this.userEmployer.id] : [];
        this.assetTemporaryWork.photos = [{}];
        this.assetTemporaryWork.item_id = `${this.tempWorkRecords.length + 1}`;
        this.showTemporaryWorkModal = true;
        this.checkPhotoCollage();
        this.assetTypeSelected = true;
        this.addTemporaryWorkModalRef.open();
    }

    openModal(content, size) {
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size
        });
    }

    isPDF(url, type) {
        if (url && url.split('.').pop() && url.split('.').pop().toLowerCase() === 'pdf') {
            this.examinationCertPreviewURL = this.domSanitizer.bypassSecurityTrustResourceUrl(url + '?embed=frame');
            this.certificatePreviewURL = this.domSanitizer.bypassSecurityTrustResourceUrl(url + '?embed=frame');
            return true;
        }
        return false;
    }

    logoImgRetries: number = 5;

    onLogoError($img: any, targetSrc: any, isLogo = true) {
        if (isLogo) {
            $img.src = AssetsUrl.siteAdmin.logoPlaceholder; // AppConstant.apiServerUrl + '/images/logo-placeholder.png';
        } else {
            $img.src = AssetsUrl.siteAdmin.projectPlaceholder; // AppConstant.apiServerUrl + '/images/project-placeholder.png';
        }

        if (targetSrc && targetSrc.length && this.logoImgRetries) {
            setTimeout(() => {
                this.logoImgRetries--;
                $img.src = targetSrc;
            }, 2000);
        }
    }

    onLogoLoad($img) {
        this.logoImgRetries = 5;
    }

    transformDatesToDayJS() {
        this.assetTemporaryWork.custom_fields = this.assetTemporaryWork.custom_fields.reduce<AssetCustomField[]>((result,a)=> {
            if(a.type === AssetCustomConfigFieldTypes.Certification) {
                a.expiry_date = a._examinationCertExpiryDate ? this.ngbMomentjsAdapter.ngbDateToDayJs(a._examinationCertExpiryDate).valueOf() : null;
                a.certificates = a.certificates.filter(c=> JSON.stringify(c) !== '{}');
                delete a._examinationCertExpiryDate;
                delete a.value;
            } else if(a.type === AssetCustomConfigFieldTypes.Date) {
                a.value = a.value ? this.ngbMomentjsAdapter.ngbDateToDayJs(a.value).valueOf() : null;
            }
            if((a.type === AssetCustomConfigFieldTypes.Certification && a.expiry_date) || (a.value)) {
                result.push(a);
            }
            return result;
        }, []);
    }

    transformDatesToNgb() {
        this.assetTemporaryWork.custom_fields = this.assetTemporaryWork.custom_fields.map(a=> {
            if(a.type === AssetCustomConfigFieldTypes.Certification) {
                a._examinationCertExpiryDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+a.expiry_date));
            } else if(a.type === AssetCustomConfigFieldTypes.Date) {
                a.value = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+a.value));
            }
            return a;
        });
    }

    saveAssetTemporaryWork() {
        this.tempWorkLoading = true;
        this.assetTemporaryWork.arrived_at = this.assetTemporaryWork._arrivedAt ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.assetTemporaryWork._arrivedAt).valueOf() : null;
        this.assetTemporaryWork.examination_cert_expiry_date = this.assetTemporaryWork._examinationCertExpiryDate ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.assetTemporaryWork._examinationCertExpiryDate).valueOf() : null;
        this.transformDatesToDayJS();
        if (!this.assetTemporaryWork.id) {
            this.setActivityLogRequest("Added");
            this.assetTemporaryWork.approval_pending = this.hasOnlySiteManagement ? 1 : 2;
            this.projectAssetService.addTemporaryWorkAsset(this.assetTemporaryWork, this.projectId, this.employerId).subscribe(this.responseHandler.bind(this, false));
        } else {
            this.setActivityLogRequest("Modified");
            let request:any = Object.assign({}, this.assetTemporaryWork);
            delete request.availableMondays;
            delete request.work_inspections;
            this.projectAssetService.updateTemporayrWorkAsset(request, this.projectId, this.assetTemporaryWork.id, this.employerId).subscribe(this.responseHandler.bind(this, false));
        }
        this.closeVehicleModal();
        this.addTemporaryWorkModalRef.close();
        this.getTaggedOwnersList();
        this.tempWorkLoading = false;
    }

    responseHandler(loadAsset = false, out: any, data?: any) {
        this.blockLoader = false;
        this.tempWorkLoading = false;
        if (out.success) {
            (this.addTemporaryWorkModal) ? this.addTemporaryWorkModal.close() : '';
            if (data) {
                data.closeFn();
            }
            if(!this.isListView) {
                this.page.pageNumber = this.page.pageNumber - 1;
            }
            this.initTempWorks();
            if (loadAsset) {
                this.getTempWork(this.assetTemporaryWork?.id, false, false);
            }
        } else {
            const message = out.message || 'Failed to store data.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: out });
        }
    }


    ngAfterViewInit() {
    }

    filterRecords() {
        this.page.pageNumber = 0;
        this.initTempWorks();   
    }

    getWorksType(workTypeKey) {
        let typeInfo = this.Type_Of_Temporary_Works.find(v => v.key === workTypeKey);
        return typeInfo? typeInfo.alternate_phrase : '';
    }

    @ViewChild('viewTempWorkOptionsRef') private viewTempWorkOptionsRef: IModalComponent;
    viewTempWorkOptionModal(asset, tab= 1) {
        this.blockLoader = true;
        this.assetTemporaryWork = asset;
        this.getTempWork(this.assetTemporaryWork?.id, false, true);
        this.activeNav = tab;
        this.assetActivityLogs = [...this.assetTemporaryWork.activity_logs].reverse();
        this.archivedTempWorkListModalRef ? this.archivedTempWorkListModalRef.close() : '';
    }

    @ViewChild('dailyInspectionOptionsHtml', {static: true}) private dailyInspectionOptionsHtmlRef = TemplateRef;
    dailyInspectionOptionsModal(asset: any = {}) {
        this.assetTemporaryWork = (asset && asset.id) ? asset : this.assetTemporaryWork;
        if (this.assetTemporaryWork['availableMondays'] && !this.assetTemporaryWork['availableMondays'].length) {
            const message = 'No inspection available.';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }

        if (this.viewTempWorkOptionModalRef) {
            this.viewTempWorkOptionModalRef.close();
        }
        this.dailyInspectionOptionsModalRef = this.openModal(this.dailyInspectionOptionsHtmlRef, 'sm');
    }

    downloadWeeklyInspection(fromDate, format, assetId) {
        this.downloadingWeeklyInspection = true;
        let fileName = 'Temporary-Works-Weekly-Inspections-Report-' + dayjs().format(AppConstant.apiRequestDateFormat);

        let request = {
            from_day: dayjs(fromDate, this.displayDateFormat).format(AppConstant.apiRequestDateFormat),
            format,
            file_name: fileName
        };

        if (format === 'pdf') {
            this.projectAssetService.downloadWeeklyTemporaryWorkInspection(this.projectId, assetId, request, () => {
                this.downloadingWeeklyInspection = false;
            });
        } else {
            this.projectAssetService.downloadWeeklyTemporaryWorkInspection(this.projectId, assetId, request).subscribe((data: any) => {
                this.downloadingWeeklyInspection = false;
                let newWindow = window.open('', '_blank', 'location1=no,height1=570,width1=520,scrollbars=yes,status=yes,toolbar=no');
                newWindow.document.body.innerHTML = data;
            });
        }
    }

    archiveTemporaryAsset(id) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Archive',
            title: `Are you sure you want to archive this asset?`,
            confirmLabel: 'Archive',
            onConfirm: () => { 
                this.tempWorkLoading = true;
                this.setActivityLogRequest("Archived");
                let request = {
                    is_archived: true,
                    activity_logs: this.assetTemporaryWork.activity_logs
                };
                this.projectAssetService.updateTemporayrWorkAsset(request, this.projectId, id, this.employerId).subscribe(this.responseHandler.bind(this, false));
                this.getTaggedOwnersList();
                this.viewTempWorkOptionsRef.close();
            }
        });
    }

    unarchiveTemporaryAsset(id) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Unarchive',
            title: `Are you sure you want to unarchive this asset?`,
            confirmLabel: 'Unarchive',
            onConfirm: () => {
                this.tempWorkLoading = true;
                this.setActivityLogRequest("Unarchived");
                let request = {
                    is_archived: false,
                    activity_logs: this.assetTemporaryWork.activity_logs
                };
                this.projectAssetService.updateTemporayrWorkAsset(request, this.projectId, id, this.employerId).subscribe(this.responseHandler.bind(this, false));
                this.viewTempWorkOptionsRef.close();
            }
        });
    }

    @ViewChild('archivedTempWorkListRef') archivedTempWorkListRef: IModalComponent
    showArchivedTemporaryWorks() {
        if (!this.archivedtempWorkRecords.length) {
            const message = 'No archived temporary works available.';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }
        this.getTaggedOwnersList('true');
        this.showModal = true;
        this.archivedTempWorkListRef.open();
    }

    @ViewChild('usersSelectorModal') usersSelectorModalRef: UsersSelectorModalComponent
    tempWorkManagersPopup() {
        this.userService.getUsersById(Array.from(new Set([...this.tempWorkManagerIds, ...this.projectAdminIds])), ['email', 'first_name', 'last_name'], this.projectId).subscribe((data: any) => {
            if (data.success && data.users) {
                this.temp_works_managers = (data.users || []).filter(user => this.tempWorkManagerIds.includes(user.id)).map(user => ({ id: user.id })) || [{}];;
                this.project_admins = (data.users || []).filter(user => this.projectAdminIds.includes(user.id));
                this.usersSelectorModalRef.openUsersSelectorModalModal();
            } else {
                const message = data.message || 'Failed to get data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    saveTempWorkManagers($event) {
        this.tempWorkLoading = true;
        this.tempWorkManagerIds = ($event || []).reduce((arr, item) => {
            if (item.id) {
                arr.push(item.id);
            }
            return arr;
        }, []);

        this.projectRequest = {
            "id": this.projectId,
            "custom_field": {
                "temp_works_managers": this.tempWorkManagerIds
            }
        };

        this.projectService.updateProjectPartially(this.projectRequest).subscribe((data: any) => {
            this.tempWorkLoading = false;
            if (data.success) {
                this.project.custom_field = data.project.custom_field;
                this.settempWorkManagerIds(data.project);
                console.log("Temporary work managers added successfully.");
            }
        });
    }

    approveTempWork(item) {
        this.dropdownIndex = null;
        this.assetTemporaryWork = item;
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Approve',
            title: `Are you sure you want to approve this asset?`,
            confirmLabel: 'Approve',
            onConfirm: () => {
                this.tempWorkLoading = true;
                this.setActivityLogRequest("Approved");
                let request = {
                    "approval_pending": 2,
                    "activity_logs": this.assetTemporaryWork.activity_logs
                };
                this.projectAssetService.updateTemporayrWorkAsset(request, this.projectId, this.assetTemporaryWork.id, this.employerId).subscribe(this.responseHandler.bind(this, false));
            }
        });    
    }

    declineTempWorkConfirmation(item) {
        this.dropdownIndex = null;
        this.assetTemporaryWork = item;
        this.declined_comment = '';
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Decline',
            title: `Are you sure you want to decline this asset?`,
            confirmLabel: 'Decline',
            onConfirm: () => {
                this.assetDeclineRef.openAssetDeclineModal();
            }
        });
    }

    declineTempWork(data) {
        this.tempWorkLoading = true;
        let request = {
            "approval_pending": 3,
            "declined_comment": data.comment
        };
        this.projectAssetService.updateTemporayrWorkAsset(request, this.projectId, this.assetTemporaryWork.id, this.employerId).subscribe(res => this.responseHandler(false, res, data));
    }

    assetPhotoUploadDone($event: any, uploadMore = false) {
        if ($event && $event.userFile && $event.userFile.id) {
            let index = (this.assetTemporaryWork.photos.length && Object.keys(this.assetTemporaryWork.photos[0]).length) ? this.assetTemporaryWork.photos.length : 0;
            index = (uploadMore) ? index - 1 : index;
            this.assetTemporaryWork.photos[index] = $event.userFile;
            if (uploadMore) {
                this.assetTemporaryWork.photos[this.assetTemporaryWork.photos.length] = {};
            }
            this.checkPhotoCollage();
        }
    }

    assetPhotoCollageUploadDone($event: any, uploadMore = false) {
        if ($event && $event.userFile && $event.userFile.id) {
            if ((this.assetTemporaryWork.photos.length && Object.keys(this.assetTemporaryWork.photos[0]).length)) {
                this.indexNumber = this.assetTemporaryWork.photos.length;
            } else {
                this.indexNumber = 0;
                uploadMore = false;
            }
            this.indexNumber = (uploadMore) ? this.indexNumber - 1 : this.indexNumber;
            uploadMore = true;
            this.assetTemporaryWork.photos[this.indexNumber] = $event.userFile;
            if (uploadMore) {
                this.assetTemporaryWork.photos[this.assetTemporaryWork.photos.length] = {};
            }
            this.checkPhotoCollage();
        }
    }

    checkPhotoCollage() {
        if((this.assetTemporaryWork.photos.length && this.assetTemporaryWork.photos[0].file_url)) {
            this.showPhotoCollage = true;
        }
    }

    fileDeleteDone($event: any) {
        if ($event && $event.userFile && $event.userFile.id) {
            this.assetTemporaryWork.photos = this.assetTemporaryWork.photos.filter(r => (r.id !== $event.userFile.id));
            this.checkPhotoCollage();
        }
    }

    getCompanyName(tagged_owner) {
        let ownersInfo = [];
        if (tagged_owner.length) {
            tagged_owner.map(company => {
                ownersInfo.push(company.name);
                return company;
            });
        }
        return ownersInfo.join(',');
    }

    /*findCompanyName(tagged_owner) {
        let ownersInfo = [];
        if (tagged_owner.length) {
            this.companiesList.filter(company => {
                if((tagged_owner || []).includes(company.id)) {
                    ownersInfo.push(company.name);
                }
            });
        }
        return ownersInfo.join(',');
    }*/

    downloadRegister() {
        let projectId = this.projectId;
        let tempWorkIds = ([...this.tempWorkRecords, ...this.archivedtempWorkRecords] || []).reduce((arr, item) => {
            if (item.approval_pending == 2) {
                arr.push(item.id);
            }
            return arr;
        }, []);
        let request = {
            asset_ids: tempWorkIds
        };
        this.tempWorkLoading = true;
        this.projectAssetService.downloadRegisterXLSX(request, projectId, 'asset-temporary', `asset-register-temporary-works-${this.project.project_number}-${this.project.name}.xlsx`, () => {
            this.tempWorkLoading = false;
        });
    }

    faultCloseoutModal(faultDetail) {
        this.faultCloseOutReq = faultDetail;
        this.faultCloseOutReq.closedout_images = [{}];
        this.faultCloseOutReq.closedout_at = null;
        this.faultCloseOutReq.closedout_by = null;
        this.faultCloseOutReq.closedout_details = '';

        this.faultCloseOutRef.openFaultCloseOutModal();
    }

    uploadDoneFault($event) {
        if($event && $event.userFile) {
            this.faultCloseOutReq.closedout_images.splice(1, 0, ...$event.userFile);
            this.faultCloseOutReq.closedout_images[0] = {};
        }
    }

    fileDeleteDoneFault($event: any) {
        if ($event && $event.userFile && $event.userFile.id) {
            this.faultCloseOutReq.closedout_images = this.faultCloseOutReq.closedout_images.filter(r => (r.id !== $event.userFile.id));
        }
    }

    requestCloseOut(data) {
        this.faultCloseOutRequest(data);
    }

    faultCloseOutRequest(data) {
        this.faultCloseOutReq = data.faultCloseOutReqObj;
        this.faultCloseOutReq.closedout_images = this.faultCloseOutReq.closedout_images.reduce((arr, image) => {
            if (image.id) {
                arr.push(image.id);
            }
            return arr;
        }, []);

        this.faultCloseOutReq.images = this.faultCloseOutReq.images.reduce((arr, image) => {
            if (image.id) {
                arr.push(image.id);
            }
            return arr;
        }, []);

        this.faultCloseOutReq.reported_by = (this.faultCloseOutReq.reported_by) ? this.faultCloseOutReq.reported_by : "N/A";

        let inspectionId = this.faultCloseOutReq.vi_ref;
        //Remove extra properties
        delete this.faultCloseOutReq.vi_ref;
        delete this.faultCloseOutReq.vi_createdAt;

        this.faultCloseOutReq.status = 'closed';
        this.faultCloseOutReq.closedout_at = dayjs().valueOf();
        this.faultCloseOutReq.closedout_by = this.authUser$.first_name+' '+this.authUser$.last_name;
        delete this.faultCloseOutReq.reported_to;

        this.blockLoader = true;
        this.projectAssetService.updateAssetTemporaryWorkInspection({'fault': this.faultCloseOutReq}, this.projectId, this.assetTemporaryWork.id, inspectionId).subscribe(res => this.responseHandler(true, res, data));
    }

    closeArchieveModal() {
        this.showModal = false;
    }

    closeVehicleModal() {
        this.showTemporaryWorkModal = false;
        this.showPhotoCollage = false;
        this.assetConfig = {};
    }

    getRowSpan(fault) {
        let rowSpan = 1;
        if (fault.images && fault.images.length) {
            rowSpan += 1;
        }

        if (fault.closedout_at) {
            rowSpan += 1;
        }

        if (fault.closedout_at && fault.closedout_images && fault.closedout_images.length) {
            rowSpan += 1;
        }

        return rowSpan;
    }

    setActivityLogRequest(type: string) {
        let activityLog = new AssetActivityLog();
        activityLog.type = type;
        activityLog.name = this.authUser$.first_name+' '+this.authUser$.last_name;
        activityLog.user_id = this.authUser$.id;
        activityLog.timestamp = dayjs().valueOf();
        this.assetTemporaryWork.activity_logs.push(activityLog);
    }

    getViewType() {
        this.isListView = JSON.parse(localStorage.getItem("isListView"));
    }

    setViewType() {
        this.isListView = !this.isListView;
        localStorage.setItem("isListView", JSON.stringify(this.isListView));
        if(!this.isListView) {
            this.page.pageNumber = this.page.pageNumber + 1;
        } else {
            this.page.pageNumber = this.page.pageNumber - 1;
        }
    }

    onFilterSelection(data){
        this.tempWorksFilter =  data.type.map(a=>a.key);
        this.ownerFilter = data.company.map(a=>a.id);
        this.selectedStatus = data.status.map(a => +a.code);
        this.filterRecords();
    }

    getQRCodeString(asset) {
        let str = this.projectId + '-AMT-'+asset.id;
        return str;
    }

    getFileName(asset) {
        let fileName = 'Asset-Temporary-Work-' + asset.id + '-qr-code';
        return fileName;
    }

    searchFunction(data){
        this.globalSearch = data.search;
        this.filterRecords();
    }

    renderFilterData(){
        return [
            {
                name:'type',
                list:this.Type_Of_Temporary_Works,
                enabled:true,
                state:false,
            },
            {
                name:'company',
                list:this.taggedOwners,
                enabled:!this.hasOnlySiteManagement,
                state:false,
            },
            {
                name:'status',
                list:this.STATUS_CODES_LIST,
                enabled:true,
                state:false,
            },
        ];
    }

    public onActionSelection(actionVal: ActionButtonVal) {
        const code = actionVal.code;
        if(code === AssetTemporaryWorksActionButtons.TEMPORARY_WORKS_MANAGERS) {
            this.tempWorkManagersPopup();
        } else if(code === AssetTemporaryWorksActionButtons.ARCHIVED_LIST) {
            this.showArchivedTemporaryWorks();
        } else if(code === AssetTemporaryWorksActionButtons.DOWNLOAD_REGISTER) {
            this.downloadRegister();
        }
    }

    trackByKey(index: number, field: any): string {
        return field.key;
    }

    certificateUploadDone($event:any, i){
        if($event && $event.userFile && $event.userFile.id) {
            let index = (this.assetTemporaryWork.custom_fields[i].certificates.length) ? this.assetTemporaryWork.custom_fields[i].certificates.length-1 : 0;
            this.assetTemporaryWork.custom_fields[i].certificates[index] = $event.userFile;
            this.assetTemporaryWork.custom_fields[i].certificates[this.assetTemporaryWork.custom_fields[i].certificates.length] = {};
        }
    }

    deleteCertificateRecord($event:any, i) {
        if($event && $event.userFile && $event.userFile.id) {
            this.assetTemporaryWork.custom_fields[i].certificates = this.assetTemporaryWork.custom_fields[i].certificates.filter(r => (r.id !== $event.userFile.id));
        }
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actionMap = {
            'view': () => this.viewTempWorkOptionModal(row),
            'unarchive': () => this.unarchiveTemporaryAsset(row.id),
            'viewWeeklyInspection': () => this.downloadWeeklyInspection(row.date, 'html', this.assetTemporaryWork?.id),
            'downloadWeeklyInspection': () => this.downloadWeeklyInspection(row.date, 'pdf', this.assetTemporaryWork?.id),
            'closeOutFault': () => this.faultCloseoutModal(row),
        };
        const action = actionMap[entry.key];
        if (action) {
            action();
        }
    }
    hasExaminationNumber() {
        if (!this.assetTemporaryWork.examination_cert_number) {
            return false;
        }
        return true;
    }

    hasExaminationCertExpDate() {
        if (!this.assetTemporaryWork._examinationCertExpiryDate) {
            return false;
        }
        return true;
    }

    examinationNumberChanged(){
        if (!this.assetTemporaryWork.examination_cert_number) {
            this.assetTemporaryWork.examination_certificates = [{}];
            this.assetTemporaryWork.examination_cert_expiry_date = null;
            this.assetTemporaryWork._examinationCertExpiryDate = null;
        }
    }

    examinationCertExpDateChanged(){
        if (!this.assetTemporaryWork.examination_certificates.length || !this.assetTemporaryWork._examinationCertExpiryDate) {
            this.assetTemporaryWork.examination_certificates = [{}];
        }
    }

    examinationCertificateUploadDone($event:any){
        if($event && $event.userFile && $event.userFile.id) {
            let index = (this.assetTemporaryWork.examination_certificates.length) ? this.assetTemporaryWork.examination_certificates.length-1 : 0;
            this.assetTemporaryWork.examination_certificates[index] = $event.userFile;
            this.assetTemporaryWork.examination_certificates[this.assetTemporaryWork.examination_certificates.length] = {};
        }
    }

    deleteExaminationCertificateRecord($event:any) {
        if($event && $event.userFile && $event.userFile.id) {
            this.assetTemporaryWork.examination_certificates = this.assetTemporaryWork.examination_certificates.filter(r => (r.id !== $event.userFile.id));
        }
    }

}

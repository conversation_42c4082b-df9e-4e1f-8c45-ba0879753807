<div class="mb-2 mx-3 flex-wrap gap-8 d-flex">
    <div class="col-md-5 d-inline-block p-0">
        <h5 class="float-md-left col-md-12 p-0">Total Temporary Works <small> ({{onSiteTempWorkCount}}) </small></h5>
    </div>
</div>

<div class="col-md-12 mb-3 p-0">
    <div class="row w-100 p-0 text-center">
        <div class="col-12 col-md-6 tablet-full-width">
            <div class="ml-md-3">
                <search-with-filters #searchFilters (filterEmitter)="onFilterSelection($event)" [filterData]="filterData" (searchEmitter)="searchFunction($event)"></search-with-filters>
            </div>
        </div>
        <div class="col-12 col-md-6 tablet-full-width m-ml-2 mb-3 px-0 d-flex justify-content-sm-center justify-content-md-end">
            <div class="d-flex w-sm-100 tablet-ml justify-content-sm-center justify-content-md-end align-items-center">
                <span type="button" [ngClass]="{'material-icon-disabled': isListView}" class="material-symbols-outlined mr-2 icon-btn" (click)="setViewType()"> list </span>
                <span type="button" [ngClass]="{'material-icon-disabled': !isListView}" class="material-symbols-outlined mr-2 icon-btn" (click)="setViewType()"> grid_view </span>
                <action-button 
                    [actionList]="actionButtonMetaData.actionList"
                    (selectedActionEmmiter)="onActionSelection($event)"
                    [newFeatureTitle]="'Add Temporary Works'"
                    (onOpenAddNew)="addTemporaryWorkPopup()">
                </action-button>
            </div>
        </div>
    </div>
</div>

<div class="col-md-12 mb-3 p-0" *ngIf="isListView; else cardView">
    <div class="table-responsive-sm ngx-datatable-custom">
        <ngx-datatable class="bootstrap table table-hover table-sm"
            [rows]="filteredTempWorkRecords ? filteredTempWorkRecords : []"
            [columns]="[
                {name:'ID', prop:'item_id', headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'py-1 table-cell-text'},
                {name:'Type', prop:'type_of_works_name', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text',cellTemplate: type},
                {name:'Owner(s)', prop:'tagged_owner', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: taggedOwnerCell},
                {name:'Reg./Serial No.', prop:'serial_number', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: regSerialNumber},
                {name:'Last Inspected', prop:'last_inspected', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text'},
                {name:'Open Faults', prop:'fault_count', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text'},
                {name:'Status', prop:'', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: listStatus},
                {name:'Action', prop:'', headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text no-ellipsis', cellTemplate: action}
            ]"
            [columnMode]="'force'"
            [limit]="50"
            [footerHeight]="36"
            [rowHeight]="'auto'"
            [externalPaging]="true"
                                   [count]="page.totalElements"
                                   [offset]="page.pageNumber"
                                   [limit]="page.size"
                                   (page)="pageCallback($event)">
            <ng-template #type let-row="row" let-column="column">
                <a (click)="viewTempWorkOptionModal(row, 1)" class="text-info" href="javascript:void(0)"> <span appTooltip>{{row.type_of_works_name}}</span></a>
            </ng-template>
            <ng-template #taggedOwnerCell  let-row="row" let-column="column">
                <div class="text-center">
                    <small> <span appTooltip>{{ getCompanyName(row.tagged_owner) }}</span></small>
                </div>
            </ng-template>
            <ng-template #regSerialNumber  let-row="row" let-column="column">
                <div class="text-left">
                    <span appTooltip>{{ row.serial_number }}</span>
                </div>
            </ng-template>
            <ng-template #listStatus  let-row="row" let-column="column">
                <div *ngIf="row.approval_pending === 1; else noAction">
                    <div *ngIf="(!hasOnlySiteManagement)" class="d-inline-block" ngbDropdown #actionDrop="ngbDropdown" container="body">
                        <button class="btn btn-sm font-md-small btn-outline-primary" ngbDropdownAnchor (click)="actionDrop.open()"> Pending </button>
                        <div ngbDropdownMenu>
                            <button class="dropdown-item cursor-pointer" (click)="approveTempWork(row)"> Approve </button>
                            <button class="dropdown-item btn-delete" (click)="declineTempWorkConfirmation(row)"> Decline </button>
                        </div>
                    </div>
                </div>
                <ng-template #noAction>
                     <span class="d-flex align-items-center" [ngClass]="{'approved-text': row.approval_pending === 2, 'redText': row.approval_pending === 3}">
                        {{ row.approval_pending_message }}
                    </span>
                </ng-template>
            </ng-template>
            <ng-template #action let-row="row" let-column="column">
                <button-group 
                    [buttons]="baseButtonConfig"
                    [btnConditions]="[true, false]"
                    (onActionClick)="rowBtnClicked($event, row)">
                </button-group>
            </ng-template>
        </ngx-datatable>
    </div>
</div>

<ng-template #cardView>
<div class="m-0 p-0 card-deck col-sm-12">
    <ngx-skeleton-loader *ngIf="loadingTempWorkRecords" count="6" [theme]="{ 'border-radius': '4px', height: '214px', width: '258px', 'margin-left': '20px' }"></ngx-skeleton-loader>
    <ng-template ngFor let-item [ngForOf]="filteredTempWorkRecords" let-i="index" *ngIf="!loadingTempWorkRecords">
        <div class="card mb-3">
            <div *ngIf="item.fault_count" class="custom-badge">
               <div class="badge-pill background-red d-flex align-items-center justify-content-center" style="height: 30px;">
                    <button class="text-white btn btn-sm faultBadgeBtn" (click)="viewTempWorkOptionModal(item, 3)">
                        <span class="mr-1"> {{item.fault_count}} Open Faults </span>
                        <i class="fa fa-search" aria-hidden="true"></i>
                    </button>
                </div>
            </div>

            <svg *ngIf="!item.photos.length" class="ard-img-top project-img mt-sm-2 mb-sm-2"><use [attr.xlink:href]="assetTemporaryIcons[item.type_of_works]"></use></svg>
            <!-- <img *ngIf="!item.photos.length" class="card-img-top project-img mt-sm-2 mb-sm-2" style="padding-top: 5px; padding-bottom: 5px;"
                [src]="'/images/concrete_pump.png'"
                 alt="Temporay Works Photo"
            /> -->

            <photo-collage
                *ngIf="item.photos.length && item.photos[0].file_url"
                [ngStyle]="{'margin': (item.approval_pending === 1) ? '0px' : '25px 0px 14px 0px'}"
                [photos]="item.photos"
                [uploadBtnSrc]="'/images/equipment-circle.png'"
                [uploadCategory]="'temporary-work-photo'"
                [showDeleteFileBtn]="false"
                [collageWidth]="'100px'"
                [collageHeight]="'100px'"
                [addMorePhotos]="false"
                class="mt-1 mb-3"
            >
            </photo-collage>

            <div [ngClass]="{'card-body pt-0 pb-1 text-center': true}">
                <h6 class="card-title" id="rr">
                    <ng-container>
                        <a (click)="viewTempWorkOptionModal(item)" class="text-info" href="javascript:void(0)">
                            {{item.item_id != null ? item.item_id + ' - ' + item.type_of_works_name : item.type_of_works_name || ''}}
                        </a>
                    </ng-container>
                </h6>
                <h6 *ngIf="!hasOnlySiteManagement" class="card-subtitle mb-2 text-muted small">Owner(s): {{assetTemporaryIcons[item.key]}}{{ getCompanyName(item.tagged_owner) }}</h6>
                <p class="card-subtitle mb-2 text-muted small">Ref./Serial Number: {{ item.serial_number }}</p>
                <p *ngIf="item.approval_pending === 2" class="card-subtitle mb-2 text-muted small">
                    Last Daily Inspection: {{ item?.lastDailyInspectionAt ? dayjsFormat(item?.lastDailyInspectionAt?.updatedAt, false) : 'N/A' }}
                </p>
            </div>
            <div *ngIf="item.approval_pending === 1" class="text-center m-1">
                <div *ngIf="hasOnlySiteManagement" class="w-100">
                    <div [ngClass]="{ 'border-bottom-none' : dropdownIndex === i }" class="d-inline-block w-100 background-orange border-redias-5 p-0">
                        <button (click)="openCloseDropdown(i)" class="btn btn-sm font-size-small text-white shadow-none w-100"> Approval Pending
                        </button>
                    </div>
                </div>
                <div *ngIf="!hasOnlySiteManagement" class="w-100">
                    <div [ngClass]="{ 'border-bottom-none' : dropdownIndex === i }" class="d-inline-block w-100 background-black border-redias-5 p-0">
                        <button class="btn btn-sm font-size-small text-white shadow-none w-100" (click)="openCloseDropdown(i)"> Approval Pending
                            <i class="fa fa-angle-down ml-3" [ngClass]="dropdownIndex === i ? 'fa-angle-up' : 'fa-angle-down'" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div class="dropdown-mask" *ngIf="dropdownIndex === i" (click)="openCloseDropdown(i)"></div>
                    <div class="w-100 dropdownMenu" style="padding-right: 8px; z-index: 9999;">
                        <div class="w-100 bg-white p-0 border-color-black rounded-bottom" *ngIf="dropdownIndex === i">
                            <div class="text-left w-100 pl-2" role="button" (click)="approveTempWork(item)"> <small>Approve</small> </div>
                            <hr class="h-line">
                            <div class="text-left w-100 rounded-bottom pl-2" role="button" (click)="declineTempWorkConfirmation(item)"> <small>Decline</small> </div>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="item.approval_pending === 2 && item?.expiry_dates.length > 0" class="text-center m-1">
                <div class="w-100">
                    <div class="d-inline-block w-100 border-redias-5 p-0"
                         [class.border-bottom-none]="dropdownIndex === i && item?.expiry_dates.length > 1"
                         [ngClass]="item?.expiry_dates[0].isExpired === 0 ? 'background-red' : item?.expiry_dates[0].isExpired === 1 ? 'background-orange' : 'background-green'">
                        <button class="btn btn-sm font-size-small text-white shadow-none w-100 pr-1 pl-1" (click)="openCloseDropdown(i)">
                            <ng-container *ngIf="item?.expiry_dates[0].isExpired === 0; else item_name">
                                <span >{{ item?.expiry_dates[0].name | uppercase }} EXPIRED!</span>
                            </ng-container>
                            <ng-template #item_name>
                                <span>{{ item?.expiry_dates[0].name }} Expiry: {{ dayjsFormat(item?.expiry_dates[0].expiry_date, false) }}</span>
                            </ng-template>
                            <i *ngIf="item?.expiry_dates.length > 1" class="fa fa-angle-down ml-2" [ngClass]="dropdownIndex === i ? 'fa-angle-up' : 'fa-angle-down'" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div class="dropdown-mask" *ngIf="dropdownIndex === i" (click)="openCloseDropdown(i)"></div>
                    <ng-container *ngIf="item?.expiry_dates.length > 1">
                        <div class="w-100 dropdownMenu" style="padding-right: 8px; z-index: 9999;" (click)="openCloseDropdown(i)">
                            <div class="w-100 bg-white p-0" *ngIf="dropdownIndex === i">
                                <ng-container *ngFor="let data of item?.expiry_dates; let i=index">
                                    <ng-container *ngIf="data.isExpired === 0; else item_name">
                                        <div [class.border-bottom-5]="i === item?.expiry_dates.length - 1" class="text-center text-white w-100" role="button" *ngIf="i !== 0" [ngClass]="data?.isExpired === 0 ? 'background-red' : data?.isExpired === 1 ? 'background-orange' : 'background-green'">
                                            <small>{{ data.name | uppercase }} EXPIRED!</small>
                                        </div>
                                    </ng-container>
                                    <ng-template #item_name>
                                        <div [class.border-bottom-5]="i === item?.expiry_dates.length - 1" class="text-center text-white w-100" role="button" *ngIf="i !== 0" [ngClass]="data?.isExpired === 0 ? 'background-red' : data?.isExpired === 1 ? 'background-orange' : 'background-green'">
                                            <small>{{ data.name }} Expiry: {{ dayjsFormat(data.expiry_date, false) }}</small>
                                        </div>
                                    </ng-template>
                                    <hr *ngIf="i < item?.expiry_dates.length - 1" class="white-line">
                                </ng-container>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </div>
        </div>
        
        
    </ng-template>
</div>
<div class="d-flex justify-content-end pt-4" *ngIf="!(!tempWorkLoading && !filteredTempWorkRecords.length && !loadingTempWorkRecords)">
    <ngb-pagination size="sm" (pageChange)="paginationCallback($event)"
        [(page)]="page.pageNumber"
        [pageSize]="page.size"
        [collectionSize]="page.totalElements">
    </ngb-pagination>
</div>
<div class="col-sm-12 asset-error-block align-items-center d-flex" *ngIf="!tempWorkLoading && !filteredTempWorkRecords.length && !loadingTempWorkRecords">
    <p class="col-sm text-center" > No Temporary Works found.</p>
</div>
</ng-template>


<block-loader [show]="(tempWorkLoading)" alwaysInCenter="true" zIndex="1200" showBackdrop="true"></block-loader>

<i-modal #addTemporaryWorkModalRef [title]="(!assetTemporaryWork?.id) ? 'Temporary Works' : 'Temporary Works Information'" size="lg" (onCancel)="closeVehicleModal()" cancelBtnText="Close" (onClickRightPB)="saveAssetTemporaryWork()" 
    [rightPrimaryBtnTxt]="(!assetTemporaryWork?.id) ? 'Add' : 'Update'" [rightPrimaryBtnDisabled]="(!addTemporaryworkForm.valid)">
        <form novalidate #addTemporaryworkForm="ngForm" class="form-container">
            <div class="form-group row" *ngIf="!assetTypeSelected">
                <div class="col-sm-12 p-0">
                    <div class="col-sm-3 p-0 pr-1 float-right">
                        <photo-collage
                            *ngIf="showPhotoCollage"
                            [photos]="assetTemporaryWork.photos"
                            [uploadBtnSrc]="'/images/equipment-circle.png'"
                            [uploadCategory]="'temporary-work-photo'"
                            (photoUploadDone)="assetPhotoCollageUploadDone($event, true)"
                            (photoDeleteDone)="fileDeleteDone($event)"
                        >
                        </photo-collage>
                    </div>
                </div>

                <div class="col-sm-12 p-0">
                    <div class="col-sm-3 mt-2 p-0 float-right" style="position: relative; left: 20px;">
                        <file-uploader-v2
                            [disabled]="false"
                            [chooseFileBtnText]="'+ Add Photos'"
                            [showDragnDrop]="false"
                            [category]="'temporary-work-photo'"
                            (uploadDone)="assetPhotoUploadDone($event)"
                            [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png']"
                            [showFileName]="false"
                            [showThumbnail]="true"
                            [showViewFileModal]="false"
                            [hide_output]="true"
                        >
                        </file-uploader-v2>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label>Type:<small class="required-asterisk ">*</small></label>
                <ng-select class="w-100 dropdown-list" appendTo="body" [(ngModel)]="assetTemporaryWork.type_of_works" [items]="Type_Of_Temporary_Works" name="type_of_works" bindLabel="alternate_phrase" bindValue="key" required ng-value="assetTemporaryWork.type_of_works" (change)="assetTypeChanged()">
                </ng-select>
            </div>

            <fieldset *ngIf="assetTemporaryWork.type_of_works && !assetTypeSelected" >
                <div @slideUpDown>
                <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.item_id?.alwaysDisplay || assetConfig.defaultFields?.item_id?.display)">
                    <label>
                        Item ID: <small class="required-asterisk" *ngIf="assetConfig.defaultFields?.item_id?.required">*</small>
                    </label>
                    <input type="text" class="form-control" #item_id="ngModel" [(ngModel)]="assetTemporaryWork.item_id" name="item_id"
                           placeholder="Item Id" [required]="assetConfig.defaultFields?.item_id?.required" [maxlength]="maxlength"/>
                    <span class="small text-danger mx-1">
                        Characters remaining: {{(maxlength - (assetTemporaryWork.item_id || '').length)}}/{{maxlength}}
                    </span>
                    <div class="alert alert-danger" [hidden]="(item_id.valid)">Item Id is required.</div>
                </div>
    
                <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.item?.alwaysDisplay || assetConfig.defaultFields?.item?.display)">
                    <label> Item <small class="required-asterisk" *ngIf="assetConfig.defaultFields?.item?.required">*</small>
                    </label>
                    <input type="text" class="form-control" #item="ngModel" [(ngModel)]="assetTemporaryWork.item" name="item"
                           placeholder="Item" [required]="assetConfig.defaultFields?.item?.required" [maxlength]="maxlength"/>
                    <span class="small text-danger mx-1">
                        Characters remaining: {{(maxlength - (assetTemporaryWork.item || '').length)}}/{{maxlength}}
                    </span>
                    <div class="alert alert-danger" [hidden]="(item.valid)">Item is required.</div>
                </div>
                <!-- *ngIf="(!hasOnlySiteManagement && showTemporaryWorkModal)" -->
                <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.tagged_owner?.alwaysDisplay || assetConfig.defaultFields?.tagged_owner?.display)" >
                    <label>Owner(s): <small class="required-asterisk" *ngIf="assetConfig.defaultFields?.tagged_owner?.required">*</small></label>
                    <company-selector-v2
                        [required]="assetConfig.defaultFields?.tagged_owner?.required"
                        [country_code]="project?.custom_field?.country_code"
                        name="tagged_owner"
                        [selectId]="assetTemporaryWork.tagged_owner"
                        placeholder="Select Owner(s)"
                        class="w-100"
                        [disabled]="hasOnlySiteManagement"
                        [multiple]="true"
                        (selectionChanged)="assetTemporaryWork.tagged_owner = $event.selected"
                        [projectId]="projectId"
                    ></company-selector-v2>
                </div>
    
                <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.serial_number?.alwaysDisplay || assetConfig.defaultFields?.serial_number?.display)">
                    <label>
                        Ref./Serial Number: <small class="required-asterisk" *ngIf="assetConfig.defaultFields?.serial_number?.required">*</small>
                    </label>
                    <input type="text" class="form-control" #serial_number="ngModel" [(ngModel)]="assetTemporaryWork.serial_number" name="serial_number"
                           placeholder="Ref./Serial Number" [required]="assetConfig.defaultFields?.serial_number?.required" [maxlength]="maxlength"/>
                    <span class="small text-danger mx-1">
                        Characters remaining: {{(maxlength - (assetTemporaryWork.serial_number || '').length)}}/{{maxlength}}
                    </span>
                    <div class="alert alert-danger" [hidden]="(serial_number.valid)">Ref./Serial Number is required.</div>
                </div>
    
                <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.risk_category?.alwaysDisplay || assetConfig.defaultFields?.risk_category?.display)">
                    <label class="col-sm-12 p-0">
                        Risk Category <small class="required-asterisk" *ngIf="assetConfig.defaultFields?.risk_category?.required">*</small>
                    </label>
                    <ng-select [items]="riskCategories"
                        class="w-100 dropdown-list" appendTo="body"
                        [placeholder]="'Select Risk Category'"
                        name="risk_category"
                        [(ngModel)]="assetTemporaryWork.risk_category" 
                        [dropdownPosition]="'bottom'"
                        [required]="assetConfig.defaultFields?.risk_category?.required"
                    >
                    </ng-select>
                </div>
    
                <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.arrived_at?.alwaysDisplay || assetConfig.defaultFields?.arrived_at?.display)">
                    <label class="col-sm-12 p-0">
                        Date Erected:<small class="required-asterisk" *ngIf="assetConfig.defaultFields?.arrived_at?.required">*</small>
                    </label>
                    <div class="input-group col-sm-8 p-0">
                        <input #aa="ngbDatepicker" [(ngModel)]="assetTemporaryWork._arrivedAt"
                               class="form-control" name="arrived_at" ng-value="assetTemporaryWork._arrivedAt"
                               ngbDatepicker [placeholder]="displayDateFormat" [required]="assetConfig.defaultFields?.arrived_at?.required"
                               readonly>
                        <div class="input-group-append">
                            <button (click)="aa.toggle()" class="btn btn-outline-secondary calendar" type="button">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div *ngIf="assetTypeSelected || (assetConfig.defaultFields?.examination_certification?.alwaysDisplay || assetConfig.defaultFields?.examination_certification?.display)" >
                    <div class="form-group row">
                        <label>
                            Examination Certificate Number: <small class="required-asterisk " *ngIf="assetConfig.defaultFields?.examination_certification?.required && assetConfig.defaultFields?.examination_certification?.document_number_mandatory">*</small>
                        </label>
                        <input type="text" class="form-control" #examination_cert_number="ngModel" [(ngModel)]="assetTemporaryWork.examination_cert_number" name="examination_cert_number"
                               (change)="examinationNumberChanged()" placeholder="Examination certificate number" [required]="assetConfig.defaultFields?.examination_certification?.required && assetConfig.defaultFields?.examination_certification?.document_number_mandatory" [maxlength]="maxlength"/>
                        <span class="small text-danger mx-1">
                            Characters remaining: {{(maxlength - (assetTemporaryWork.examination_cert_number || '').length)}}/{{maxlength}}
                        </span>
                    </div>
        
                    <div class="form-group row" *ngIf="hasExaminationNumber()">
                        <label class="col-sm-12 p-0">
                            Examination Certificate Expiry Date: <small class="required-asterisk ">*</small>
                        </label>
                        <div class="input-group col-sm-8 p-0">
                            <input class="form-control" [placeholder]="displayDateFormat" readonly
                                   (dateSelect)="examinationCertExpDateChanged()" name="examination_cert_expiry_date" [(ngModel)]="assetTemporaryWork._examinationCertExpiryDate" ngbDatepicker
                                   #eced="ngbDatepicker" ng-value="assetTemporaryWork._examinationCertExpiryDate" [minDate]="minDate" [required]="assetConfig.defaultFields?.examination_certification?.expiry_date_mandatory">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary calendar" (click)="eced.toggle()" type="button">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </div>
                        </div>
                    </div>
        
                    <div class="form-group row" *ngIf="hasExaminationCertExpDate()">
                        <label class="col-md-6 p-0">
                            Examination Certificate: <small *ngIf="assetConfig.defaultFields?.examination_certification?.attachment_mandatory" class="required-asterisk ">*</small>
                        </label>
                        <ng-template ngFor let-item [ngForOf]="(assetTemporaryWork.examination_certificates || [])" let-i="index">
                            <div class="col-sm-10 p-0 pl-3">
                                <file-uploader-v2
                                    [disabled]="false"
                                    [category]="'examination-certificate'"
                                    [init]="item"
                                    (uploadDone)="examinationCertificateUploadDone($event)"
                                    [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']"
                                    [showViewFileModal]="item && item.file_url"
                                    [fileModalTitle]="'Examination Certificate'"
                                    [showFileName]="false"
                                    (deleteFileDone)="deleteExaminationCertificateRecord($event)"
                                    [showDeleteBtn]="true"
                                    [showThumbnail]="false"
                                    [chooseFileBtnText]="'+ Add Certificate'">
                                </file-uploader-v2>
                                <div class="alert alert-danger" *ngIf="assetTemporaryWork?._examinationCertExpiryDate && assetConfig.defaultFields?.examination_certification?.attachment_mandatory && assetTemporaryWork.examination_certificates.length <= 1">
                                    <input type="hidden" id="certification-attachment-exam" name="certification-attachment-exam" [ngModel]="assetTemporaryWork.examination_certificates.length >=2 ? true: undefined" required>
                                    Examination Certificate is required.
                                </div>
                            </div>
                        </ng-template>
                    </div>
                </div>

                <ng-container *ngFor="let field of assetConfig.custom_fields;let i = index; trackBy: trackByKey">
                    <div class="form-group row" *ngIf="field.type === assetCustomConfigFieldTypes.Text">
                        <label> {{field.title}} <small class="required-asterisk" *ngIf="field.required">*</small>
                        </label>
                        <input type="text" class="form-control" #item="ngModel" [(ngModel)]="assetTemporaryWork.custom_fields[i].value" [name]="field.title" placeholder="Enter text" [required]="field.required" [maxlength]="maxlength"/>
                        <span class="small text-danger mx-1">
                            Characters remaining: {{(maxlength - (assetTemporaryWork.custom_fields[i].value || '').length)}}/{{maxlength}}
                        </span>
                        <!-- <div class="alert alert-danger" [hidden]="(item.valid)">Item is required.</div> -->
                    </div>
                    <div class="form-group row" *ngIf="field.type === assetCustomConfigFieldTypes.Dropdown">
                        <label> {{field.title}}: <small class="required-asterisk" *ngIf="field.required">*</small>
                        </label>
                        <ng-select class="w-100 dropdown-list" appendTo="body" [(ngModel)]="assetTemporaryWork.custom_fields[i].value" placeholder="Select option" [items]="field.options" [name]="field.title" [required]="field.required">
                        </ng-select>
                    </div>
                    <div class="form-group row" *ngIf="field.type === assetCustomConfigFieldTypes.Date">
                        <label class="col-sm-12 p-0">
                            {{field.title}}: <small class="required-asterisk" *ngIf="field.required">*</small>
                        </label>
                        <div class="input-group col-sm-8 p-0">
                            <input #aa="ngbDatepicker" [(ngModel)]="assetTemporaryWork.custom_fields[i].value" class="form-control" [name]="field.title" ngbDatepicker [placeholder]="displayDateFormat" [required]="field.required" readonly>
                            <div class="input-group-append">
                                <button (click)="aa.toggle()" class="btn btn-outline-secondary calendar" type="button">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <ng-container *ngIf="field.type === assetCustomConfigFieldTypes.Certification">

                        <div class="form-group row">
                            <label class="col-sm-12 p-0">
                                {{field.title}} Expiry Date: <small class="required-asterisk" *ngIf="field.required">*</small>
                            </label>
                            <div class="input-group col-sm-8 p-0">
                                <input class="form-control" [placeholder]="displayDateFormat" readonly
                                       name="expiry_date_{{i}}" [(ngModel)]="assetTemporaryWork.custom_fields[i]._examinationCertExpiryDate" ngbDatepicker
                                       #eced="ngbDatepicker" ng-value="assetTemporaryWork._examinationCertExpiryDate"
                                       [minDate]="minDate"
                                       [required]="field.required">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary calendar" (click)="eced.toggle()" type="button">
                                        <i class="fa fa-calendar"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row" *ngIf="assetTemporaryWork.custom_fields[i]._examinationCertExpiryDate">
                            <label>
                                {{field.title}} Certificate Number: <small class="required-asterisk" *ngIf="field.required && field.document_number_mandatory">*</small>
                            </label>
                            <input type="text" class="form-control" [(ngModel)]="assetTemporaryWork.custom_fields[i].certificate_number" [name]="field.title+'_number'"
                                   placeholder="Enter certificate number" [required]="field.required && field.document_number_mandatory" [maxlength]="maxlength"/>
                            <span class="small text-danger mx-1">
                                Characters remaining: {{(maxlength - (assetTemporaryWork.custom_fields[i].certificate_number || '').length)}}/{{maxlength}}
                            </span>
                        </div>

                        <div class="form-group row" *ngIf="assetTemporaryWork.custom_fields[i]._examinationCertExpiryDate">
                            <label class="col-md-7 p-0">
                                {{field.title}} Attachment: <small *ngIf="field.attachment_mandatory" class="required-asterisk ">*</small>
                            </label>
                            <ng-template ngFor let-item [ngForOf]="(assetTemporaryWork.custom_fields[i].certificates || [])" let-j="index">
                                <div class="col-sm-10 p-0 pl-3">
                                    <file-uploader-v2
                                        [disabled]="false"
                                        [category]="'custom-certificate'"
                                        [init]="item"
                                        (uploadDone)="certificateUploadDone($event, i)"
                                        [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']"
                                        [showViewFileModal]="item && item.file_url"
                                        [fileModalTitle]="field.title + ' Certificate'"
                                        [showFileName]="false"
                                        (deleteFileDone)="deleteCertificateRecord($event, i)"
                                        [showDeleteBtn]="true"
                                        [showThumbnail]="false"
                                        [chooseFileBtnText]="'+ Add Certificate'"
                                    >
                                    </file-uploader-v2>
                                    <div class="alert alert-danger" *ngIf="field.required && field.attachment_mandatory && assetTemporaryWork.custom_fields[i].certificates.length <= 1">
                                        <input type="text" class="d-none" name="certification-attachment-{{i}}" [ngModel]="assetTemporaryWork.custom_fields[i].certificates.length >=2 ? true: undefined" required>
                                        {{field.title}} attachment is required.
                                    </div>
                                </div>
                            </ng-template>
                        </div>
            
                    </ng-container>
                    

                </ng-container>

                </div>
            </fieldset>
            
        </form>
</i-modal>

<!-- <ng-template #viewFileHtml let-c="close" let-d="dismiss">
    <div class="modal-header">
        <h4 class="modal-title">
            {{ fileModalTitle }}
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="form-group row">
            <div class="col-sm-12 mt-2 text-center">
                <div *ngIf="!isPDF(certificateUrl, fileModalTitle)" style="width: 600px; display:inline-block;" class="d-inline-block">
                    <img [src]="certificateUrl" style="width: 100%; height: auto;" #img />
                </div>

                <iframe *ngIf="certificateUrl && isPDF(certificateUrl, fileModalTitle)" [src]="certificatePreviewURL" width="750px" height="500px">
                </iframe>
            </div>
        </div>

    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-outline-primary" (click)="d('Cross click')">Close</button>
    </div>
</ng-template> -->

<i-modal #viewFileRef [title]="fileModalTitle" size="lg" cancelBtnText="Close" (onCancel)="closePdfImgModal()" (onClickRightSB)="closePdfImgModal($event)">
    <div class="form-group row" *ngIf="showPdfImg">
        <div class="col-sm-12 mt-2 text-center">
            <div *ngIf="!isPdfDocument" style="width: 600px; display:inline-block;" class="d-inline-block">
                <img [src]="certificateUrl" style="width: 100%; height: auto;" #img />
            </div>

            <iframe *ngIf="certificateUrl && isPdfDocument" class="border-0" [src]="certificatePreviewURL" width="750px" height="500px">
            </iframe>
        </div>
    </div>
</i-modal>

<i-modal #viewTempWorkOptionsRef [title]="(assetTemporaryWork?.item_id) ? assetTemporaryWork?.item_id + ' - ' + getWorksType(assetTemporaryWork?.type_of_works) + '(' + assetTemporaryWork?.serial_number + ')' : ''" 
    size="lg" [showCancel]="false" [rightPrimaryBtnTxt]="(assetTemporaryWork.id && !assetTemporaryWork.is_archived) ? 'Archive' : 'Unarchive'" [rightSecondaryBtnTxt]="(activeNav === 1) ? 'Edit' : ''"
    (onClickRightPB)="(assetTemporaryWork.id && !assetTemporaryWork.is_archived) ? archiveTemporaryAsset(assetTemporaryWork.id) : unarchiveTemporaryAsset(assetTemporaryWork.id)"
    (onClickRightSB)="getTempWork(assetTemporaryWork?.id, true, false)">
        <div class="card-body">
            <div class="row">
                <ul ngbNav #nav="ngbNav" [(activeId)]="activeNav" class="nav-tabs n-tab">
                    <li [ngbNavItem]="1">
                        <a ngbNavLink class="nav-a" (click)="toggler(1)">Details</a>
                        <ng-template ngbNavContent>
                            <div class="">
                                <div class="col-sm-12 p-0">
                                    <div class="w-100" style="height: 10rem" *ngIf="assetTemporaryWork.photos?.length && assetTemporaryWork.photos[0].file_url">
                                        <div style="position: absolute; right: 0px;">
                                            <photo-collage
                                            [photos]="assetTemporaryWork.photos"
                                            [uploadBtnSrc]="'/images/equipment-circle.png'"
                                            [uploadCategory]="'temporary-work-photo'"
                                            (photoUploadDone)="assetPhotoUploadDone($event, true)"
                                            (photoDeleteDone)="fileDeleteDone($event)"
                                            [showDeleteFileBtn]="false"
                                            [addMorePhotos]="false"
                                            >
                                            </photo-collage>
                                        </div>
                                    </div>
                                </div>
                                <table class="table table-sm table-bordered">
                                    <tbody>
                                    <tr *ngIf="assetTemporaryWork?.item_id">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Item ID:</strong> </td>
                                        <td colspan="3"> {{ assetTemporaryWork.item_id }} </td>
                                    </tr>
                                    <tr *ngIf="assetTemporaryWork?.item">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Item:</strong> </td>
                                        <td colspan="3"> {{ assetTemporaryWork.item }} </td>
                                    </tr>
                                    <tr *ngIf="assetTemporaryWork?.type_of_works">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Type of works:</strong> </td>
                                        <td colspan="3"> {{ getWorksType(assetTemporaryWork.type_of_works) }} </td>
                                    </tr>
                                    
                                    <tr *ngIf="!hasOnlySiteManagement && assetTemporaryWork?.tagged_owner.length">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Owner(s):</strong> </td>
                                        <td colspan="3"> {{ getCompanyName(assetTemporaryWork.tagged_owner) }} </td>
                                    </tr>
                                    <tr *ngIf="assetTemporaryWork?.serial_number">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Ref./Serial Number:</strong> </td>
                                        <td colspan="3"> {{ assetTemporaryWork.serial_number }} </td>
                                    </tr>
                                    <tr *ngIf="assetTemporaryWork?.risk_category!= null">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Risk Category:</strong> </td>
                                        <td colspan="3"> {{ assetTemporaryWork.risk_category }} </td>
                                    </tr>
                                    <tr *ngIf="assetTemporaryWork?.arrived_at">
                                        <td class="tr-bg-dark-color" width="30%"> <strong>Date Erected:</strong> </td>
                                        <td colspan="3"> {{ dayjsFormat(assetTemporaryWork.arrived_at, false) }} </td>
                                    </tr>
                                    <tr *ngIf="assetTemporaryWork.examination_cert_number || assetTemporaryWork.examination_cert_expiry_date || assetTemporaryWork?.examination_certificates?.length - 1">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Examination Certificate:</strong> </td>
                                        <td width="17.5%" class="p-0">
                                            <div class="w-100 tr-bg-dark-color custom-padding">Document No.</div>
                                            <div class="w-100 custom-padding">{{ assetTemporaryWork.examination_cert_number }}</div>
                                        </td>
                                        <td width="17.5%" class="p-0">
                                            <div class="w-100 tr-bg-dark-color custom-padding">Expiry Date</div>
                                            <div class="w-100 custom-padding">{{ dayjs(assetTemporaryWork.examination_cert_expiry_date, false) }}</div>
                                        </td>
                                        <td width="35%" class="p-0">
                                            <div class="w-100 tr-bg-dark-color custom-padding">Attachments</div>
                                            <div class="w-100 custom-padding">
                                                <p class="upload-name mb-0" *ngFor="let cert of assetTemporaryWork?.examination_certificates">
                                                    <a href="javascript:void(0)" (click)="viewFileModal(cert?.file_url, 'Examination Certificate')">{{ cert?.name }}</a>
                                                </p>
                                            </div>
                                        </td>
                                    </tr>
                                    <ng-container *ngFor="let field of assetTemporaryWork.custom_fields;let i = index; trackBy: trackByKey">
                                        <tr *ngIf="field.type === assetCustomConfigFieldTypes.Text || field.type === assetCustomConfigFieldTypes.Dropdown">
                                            <td class="tr-bg-dark-color" width="30%"> <strong>{{field.title}}:</strong> </td>
                                            <td colspan="3"> {{ field.value }} </td>
                                        </tr>
                                        <tr *ngIf="field.type === assetCustomConfigFieldTypes.Date">
                                            <td class="tr-bg-dark-color" width="30%"> <strong>{{field.title}}:</strong> </td>
                                            <td colspan="3"> {{ dayjsFormat(field.value, false) }} </td>
                                        </tr>
                                        <tr *ngIf="field.type === assetCustomConfigFieldTypes.Certification">
                                            <td width="30%" class="tr-bg-dark-color"> <strong>{{field.title}}:</strong> </td>
                                            <td width="17.5%" class="p-0">
                                                <div class="w-100 tr-bg-dark-color custom-padding">Document No.</div>
                                                <div class="w-100 custom-padding">{{ field.certificate_number }}</div>
                                            </td>
                                            <td width="17.5%" class="p-0">
                                                <div class="w-100 tr-bg-dark-color custom-padding">Expiry Date</div>
                                                <div class="w-100 custom-padding" *ngIf="field.expiry_date">{{ dayjsFormat(field.expiry_date, false) }}</div>
                                            </td>
                                            <td width="35%" class="p-0">
                                                <div class="w-100 tr-bg-dark-color custom-padding">Attachments</div>
                                                <div class="w-100 custom-padding">
                                                    <p class="upload-name mb-0" *ngFor="let cert of field.certificates">
                                                        <a href="javascript:void(0)" (click)="viewFileModal(cert?.file_url, 'Examination Certificate')">{{ cert?.name }}</a>
                                                    </p>
                                                </div>
                                            </td>
                                        </tr>
                                    </ng-container>

                                    </tbody>
                                </table>
                            </div>
                        </ng-template>
                    </li>
                    <li [ngbNavItem]="2">
                        <a ngbNavLink class="nav-a" (click)="toggler(2)">Inspections</a>
                        <ng-template ngbNavContent>
                            <div class="overflow-hidden">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th class="tr-bg-dark-color vertical-align-middle p-1 px-2 date-col-width"> Week Commencing </th>
                                            <th class="tr-bg-dark-color vertical-align-middle p-1 px-2 td-col-width" *ngFor="let day of week_Days"> {{ day }} </th>
                                            <th class="tr-bg-dark-color vertical-align-middle p-1 px-2"> Actions </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <ng-container *ngIf="hasInspections">
                                            <tr *ngFor="let row of assetTemporaryWork?.availableMondays; let i=index">
                                                <td class="vertical-align-middle p-1 px-2"> {{row?.date}} </td>
                                                <td class="vertical-align-middle p-1 px-2 text-center" *ngFor="let day of week_Days">
                                                    <ng-container *ngIf="dayIndex(row.weekdays, day) !== -1">
                                                        <i class="fa fa-check-circle text-success" aria-hidden="true" [ngbTooltip]="' Daily inspection was carried out by '&nbsp;+row?.weekdays[dayIndex(row.weekdays, day)]?.user_name+' on '&nbsp;+row?.weekdays[dayIndex(row.weekdays, day)]?.createdAt"></i>
                                                    </ng-container>
                                                </td>
                                                <td class="vertical-align-middle p-1 px-2">
                                                    <button-group
                                                        [buttons]="weeklyInspectionButtonConfig"
                                                        (onActionClick)="rowBtnClicked($event, row)">
                                                    </button-group>
                                                </td>
                                            </tr>
                                        </ng-container>
                                        <tr *ngIf="!hasInspections">
                                            <td colspan="9" class="text-center"> No inspections found </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </ng-template>
                    </li>

                    <li [ngbNavItem]="3">
                        <a ngbNavLink class="nav-a" (click)="toggler(3)">Faults</a>
                        <ng-template ngbNavContent>
                            <div class="overflow-hidden">
                                <table class="table table-sm table-bordered">
                                <thead>
                                <tr>
                                    <th class="tr-bg-dark-color vertical-align-middle">Fault No.</th>
                                    <th class="tr-bg-dark-color vertical-align-middle">Reported</th>
                                    <th class="tr-bg-dark-color vertical-align-middle">Reported By</th>
                                    <th class="tr-bg-dark-color vertical-align-middle">Fault Details</th>
                                    <th class="tr-bg-dark-color vertical-align-middle">Status</th>
                                    <th class="tr-bg-dark-color vertical-align-middle">Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr *ngIf="!tempWorkFaults.length else faultTable">
                                    <td colspan="9" class="text-center"> No faults found</td>
                                </tr>
                                <ng-template #faultTable>
                                    <ng-container *ngFor="let fault of tempWorkFaults; let i=index">
                                        <tr [ngStyle]="{'background-color': (i % 2) ? 'none' : 'rgba(0, 0, 0, 0.05)'}">
                                            <td [rowSpan]="getRowSpan(fault)">{{fault.fault_id}}</td>
                                            <td>
                                                <span *ngIf="fault.date_reported">{{ dayjsFormat(fault.date_reported) }}</span>
                                            </td>
                                            <td>
                                                <span *ngIf="fault.reported_by">{{ fault.reported_by }}</span>
                                            </td>
                                            <td>
                                                <span *ngIf="fault.fault">{{ fault.fault}}</span>
                                            </td>

                                            <td>
                                                <span *ngIf="fault.status" [ngClass]="{'redText': (fault.status == 'open'), 'greenText': (fault.status == 'closed')}">
                                                    {{ (fault.status || '').toUpperCase() }}
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <button-group
                                                    [buttons]="faultActionButtonConfig"
                                                    (onActionClick)="rowBtnClicked($event, fault)">
                                                </button-group>
                                            </td>
                                        </tr>
                                        <tr *ngIf="fault.images && fault.images.length" [ngStyle]="{'background-color': (i % 2) ? 'none' : 'rgba(0, 0, 0, 0.05)'}">
                                            <td colspan="5">
                                                <pop-up-image-viewer [alt]="'Image'" [imgArray]="fault.images || []"></pop-up-image-viewer>
                                            </td>
                                        </tr>
                                        <tr *ngIf="fault.closedout_at" [ngStyle]="{'background-color': (i % 2) ? 'none' : 'rgba(0, 0, 0, 0.05)'}">
                                            <td colspan="5" class="p-0">
                                                <table class="table table-bordered mb-0">
                                                    <thead>
                                                        <td class="tr-bg-dark-color vertical-align-middle p-1 px-2">Closed Out</td>
                                                        <td class="tr-bg-dark-color vertical-align-middle p-1 px-2">Closeout By</td>
                                                        <td class="tr-bg-dark-color vertical-align-middle p-1 px-2">Closeout Details</td>
                                                    </thead>
                                                    <tbody>
                                                    <td class="vertical-align-middle p-1 px-2">
                                                        <span *ngIf="fault.closedout_at">{{ dayjsFormat(fault.closedout_at) }}</span>
                                                    </td>
                                                    <td class="vertical-align-middle p-1 px-2">
                                                        <span *ngIf="fault.closedout_by">{{ fault.closedout_by }}</span>
                                                    </td>
                                                    <td class="vertical-align-middle p-1 px-2">
                                                        <span *ngIf="fault.closedout_details">{{ fault.closedout_details }}</span>
                                                    </td>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr *ngIf="fault.closedout_at && fault.closedout_images && fault.closedout_images.length" [ngStyle]="{'background-color': (i % 2) ? 'none' : 'rgba(0, 0, 0, 0.05)'}">
                                            <td colspan="5">
                                                <pop-up-image-viewer [alt]="'Image'" [imgArray]="fault.closedout_images || []"></pop-up-image-viewer>
                                            </td>
                                        </tr>
                                    </ng-container>
                                </ng-template>
                                </tbody>
                            </table>
                            </div>
                        </ng-template>
                    </li>
                    <li [ngbNavItem]="4">
                        <a ngbNavLink class="nav-a" (click)="toggler(4)">QR Code</a>
                        <ng-template ngbNavContent>
                            <qrcode-generator [qrData]="getQRCodeString(assetTemporaryWork)" [fileName]="getFileName(assetTemporaryWork)" elementType="canvas"></qrcode-generator>
                        </ng-template>
                    </li>
                </ul>
                <div [ngbNavOutlet]="nav" class="col-sm-12 my-2 pt-2 nav-panel"></div>
            </div>
        </div>
        <div *ngIf="assetActivityLogs && assetActivityLogs?.length && activeNav === 1" class="materialRow table-sm">
            <p class="mb-1"><strong>Activity Log:</strong></p>
            <table class="table table-sm table-bordered mb-0">
                <thead>
                <tr>
                    <th class="tr-bg-dark-color"><strong>Date & Time</strong> </th>
                    <th class="tr-bg-dark-color"><strong>Activity</strong> </th>
                    <th class="tr-bg-dark-color"><strong>Activity By</strong></th>
                </tr>
                </thead>
                <tbody>
                <ng-container *ngFor="let t of assetActivityLogs">
                    <tr *ngIf="t.timestamp">
                        <td>{{ dayjsFormat(t.timestamp) }}</td>
                        <td>{{ t.type }}</td>
                        <td>{{ t.name }}</td>
                    </tr>
                </ng-container>
                </tbody>
            </table>
        </div>
</i-modal>

<ng-template #dailyInspectionOptionsHtml let-c="close" let-d="dismiss">
    <div class="modal-header">
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body text-center">
        <div class="card-body">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th class="vertical-align-middle">
                            Week Commencing
                        </th>
                        <th class="vertical-align-middle">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let row of assetTemporaryWork?.availableMondays">
                        <td class="vertical-align-middle">{{row?.date}}</td>
                        <td class="vertical-align-middle">
                            <button-group
                                [buttons]="weeklyInspectionButtonConfig"
                                (onActionClick)="rowBtnClicked($event, row)">
                            </button-group>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</ng-template>

<i-modal #archivedTempWorkListRef [title]="('Total Archived Temporary Works (' + archivedtempWorkRecords.length + ')')" size="xl"
    [showCancel]="false" [showFooter]="false" (onCancel)="closeArchieveModal()">
        <div class="card-body">
            <div *ngIf="showModal" class="table-responsive-sm pl-3 pr-3">
                <ngx-datatable #table class="bootstrap table table-hover table-sm archive-table"
                               [rows]="archivedtempWorkRecords"
                               [limit]="30"
                               [footerHeight]="40"
                               [columnMode]="'force'"
                               [rowHeight]="'auto'"
                >
                    <ngx-datatable-column headerClass="font-weight-bold">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            ID
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            {{row.item_id}}
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Ref./Serial Number
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                             <span appTooltip>{{row.serial_number}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold" *ngIf="!hasOnlySiteManagement">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Owner(s)
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                             <span appTooltip>{{ getCompanyName(row.tagged_owner) }}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Type of works
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <span appTooltip>{{ getWorksType(row.type_of_works) }}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Date Erected
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <span>{{ row.arrived_at ? dayjsFormat(row.arrived_at,false) : ''}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Status
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <div class="d-flex">
                                <span class="d-flex align-items-center" [ngClass]="{'text-warning': row.approval_pending === 1, 'approved-text': row.approval_pending === 2, 'redText': row.approval_pending === 3}" >{{row.approval_pending_message}}</span>
                            </div>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold action-column"
                                          cellClass="action-column no-ellipsis">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Action
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <button-group
                                [buttons]="baseButtonConfig"
                                (onActionClick)="rowBtnClicked($event, row)">
                            </button-group>
                        </ng-template>
                    </ngx-datatable-column>
                </ngx-datatable>
            </div>
        </div>
</i-modal>

<block-loader [show]="(downloadingWeeklyInspection)" alwaysInCenter="true" zIndex="1200" showBackdrop="true"></block-loader>

<users-selector-modal
    [users_list]="project_admins"
    [selectedUsers]="temp_works_managers"
    [title]="'Temporary Works Managers'"
    [heading]="'Temporary Works managers will be notified when new temporary works are added'"
    (selectedUserIds)="saveTempWorkManagers($event)"
    #usersSelectorModal
>
</users-selector-modal>

<fault-closeout
    #faultCloseOutRef
    [faultCloseOutReq]="faultCloseOutReq"
    (onCloseOut)="requestCloseOut($event)">
</fault-closeout>

<asset-decline
    #assetDeclineRef
    [declined_comment]="declined_comment"
    (onDecline)="declineTempWork($event)">
</asset-decline>

<block-loader [alwaysInCenter]="true" [show]="blockLoader" [showBackdrop]="true"></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

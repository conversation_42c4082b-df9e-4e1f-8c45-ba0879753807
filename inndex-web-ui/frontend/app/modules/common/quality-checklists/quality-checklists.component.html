<style>
    .qclInspectionContent {margin-top: 2px !important;border: 1px solid #dee2e6;}
    .dropdown-menu li a {padding: 10px 15px;font-weight: 300;cursor: pointer;}
    .multi-column-dropdown {list-style: none;margin: 0px;padding: 0px;text-align: center;}
    .multi-column-dropdown li a {display: block;clear: both;line-height: 1.428571429;color: #000000;}
    .multi-column-dropdown li a:hover {text-decoration: underline;color: #edb61d;background-color: #fff;}
    .dropdown-menu .searchHead {position: absolute;top: -38px;left: 0;width: 170px;height: 41px;padding: 4px 1px;background-color: #fff;display: block;border: 1px solid rgba(0, 0, 0, 0.15);border-bottom: 0;border-radius: 0.25rem;border-bottom-right-radius: unset;border-bottom-left-radius: unset;}
    .dropdown-menu .searchHead input.form-control {padding-left: 28px;width: 97%;margin: auto;height: 36px;background-color: #F5F4F4;border: none;}
    .dropdown-menu .searchHead .miglass {position: absolute;top: 36%;pointer-events: none;vertical-align: bottom;box-sizing: border-box;right: auto;left: 0.6rem;color: #868686;}
    .dropdown-menu .qcl-list {max-height: 210px;overflow: auto;}
    #select-inspection-button.btn-primary:focus {box-shadow: none;}
    #qcl-checklist-drop .form-control::-webkit-input-placeholder { /* WebKit, Blink, Edge */color: #8D8B8A;}
    #qcl-checklist-drop .form-control::-moz-placeholder { /* Mozilla Firefox 19+ */color: #8D8B8A;opacity:  1;}
    #qcl-checklist-drop .form-control::-ms-input-placeholder { /* Microsoft Edge */color: #8D8B8A;}
    #qcl-checklist-drop ::placeholder {color: #8D8B8A;}
        .ng-select.ng-select-single .ng-select-container {
        height: 30px;
    }
</style>
<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav *ngIf="!isProjectPortal" [employer]="employer" [companyResolverResponse]="companyResolverResponse" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="col-sm-12 my-3 outer-border">
                <div class="my-3 outer-border-radius">
                <div class="col-sm-12 outer-border">
                    <div *ngIf="contentLoading">
                        <ngx-skeleton-loader count="1" [theme]="{width: '140px', height: '20px'}"></ngx-skeleton-loader>
                        <ngx-skeleton-loader count="2" [theme]="{'border-radius': '0', height: '50px'}"></ngx-skeleton-loader>
                    </div>
                    <div *ngIf="!contentLoading">
                        <div>
                            <div class="d-flex justify-content-between">
                                <h6 class="mt-2 ml-1 ">{{currentItem && currentItem.qc_title}} <small>({{ page.totalElements }})</small></h6>
                            </div>
                            <div class="d-flex">
                                <search-with-filters (searchEmitter)="searchFunction($event)" customClasses = "d-inline" >
                                </search-with-filters> 
                                <ng-select
                                #ngSelectRef
                                [ngClass]="{
                                    'w-25 filter-v2-select it-dropdown rounded-select filter-select mb-2': true,
                                    'd-block': qclChecklists,
                                    'ml-3': true,
                                    'X': qclChecklists?.length <= 7
                                }"
                                [searchable]="false"
                                [clearable]="false"
                                [multiple]="false"
                                [items]="qclChecklists"
                                bindLabel="qc_title"    
                                (change)="changeContent($event)"
                                [(ngModel)] = "currentItem"
                                >
                                    <ng-template  ng-multi-label-tmp>
                                        <span style="display:block;" class="horizontal-center" class="ng-placeholder custom-placeholder text-truncate">
                                            <span>{{currentItem && currentItem.qc_title}}</span>
                                        </span>
                                    </ng-template>
                                    <ng-template *ngIf="qclChecklists?.length > 7" ng-header-tmp>
                                        <div style="width:100%; max-width:100%;" class="text-search horizontal-center px-2">
                                            <span class="material-symbols-outlined large-font">search</span>
                                            <input placeholder="Search" style="width: 100%; line-height: 24px" class="ngx-search" type="search" (input)="ngSelectRef.filter($event.target.value)" />
                                        </div>
                                    </ng-template>
                                    <ng-template ng-option-tmp let-item="item" class="horizontal-center" let-item$="item$" let-index="index" let-search="searchTerm">
                                        <span style="margin-left: 5px;">
                                            {{item.qc_title}}                          
                                        </span>
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>    
                        <ngx-datatable #table class="bootstrap table table-hover table-sm"
                                [rows]="inspectionRecords"
                                [footerHeight]="40"
                                [columnMode]="'force'"
                                [rowHeight]="'auto'"
                                [externalPaging]="true"
                                [count]="page.totalElements"
                                [offset]="page.pageNumber"
                                [limit]="page.size"
                                (page)="pageCallback($event, +selectedTabId)"
                       >
                            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    Report #
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    {{ row.record_ref }}
                                </ng-template>
                            </ngx-datatable-column>
                            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    Time & Date Prepared
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    {{ dayjsShowDT(row.report_datetime ? +row.report_datetime : +row.createdAt) }}
                                </ng-template>
                            </ngx-datatable-column>
                            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    Prepared By
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    <span appTooltip>{{row?.user_ref?.first_name}} {{row?.user_ref?.middle_name || ''}} {{row?.user_ref?.last_name}}</span>
                                </ng-template>
                            </ngx-datatable-column>
                            <ngx-datatable-column headerClass="font-weight-bold action-column text-center"
                                              cellClass="action-column" [sortable]="false">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    Defects
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    <div class="text-center">
                                        {{ getDefectsStatusText(row.items, row.has_subheadings) }}
                                        <span *ngIf="!hasAllDefectsClosed" class="btn-sm badge-pill badge-success">
                                            {{ defectsColumnTxt }}
                                        </span>
                                        <span *ngIf="hasAllDefectsClosed" (click)="defectsListModal(defectsHtml, row)" class="btn-sm badge-pill badge-danger cursor-pointer">
                                            {{ defectsColumnTxt }}
                                        </span>
                                    </div>
                                </ng-template>
                            </ngx-datatable-column>
                            <ngx-datatable-column headerClass="font-weight-bold action-column" cellClass="action-column no-ellipsis" [sortable]="false">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    Action
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    <button-group
                                        [buttons]="baseButtonConfig"
                                        [btnConditions]="[true, true, true, hasAllDefectsClosed]"
                                        (onActionClick)="rowBtnClicked($event, row, qclReportContent, defectsHtml)">
                                    </button-group>
                                </ng-template>
                            </ngx-datatable-column>
                        </ngx-datatable>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>
<block-loader alwaysInCenter="true" [show]="loading" [showBlockBackdrop]="true" #qclLoader></block-loader>
<ng-template #defectsHtml let-c="close" let-d="dismiss">
    <div class="modal-header">
        <h4 class="modal-title">Defects</h4>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="tb-attendees">
            <h6><strong>ITP #{{ itpInspection?.record_ref }}</strong></h6>
            <div>
                <table class="table small table-sm table-striped">
                    <thead>
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Action</th>
                    </tr>
                    </thead>
                    <tbody>
                        <ng-container *ngIf="itpInspection.has_subheadings; else noSubheadings">
                            <ng-container *ngFor="let heading of itpInspection.items; trackBy: trackByRowIndex; let i = index;">
                                <ng-container *ngFor="let item of heading.subheadings; trackBy: trackByRowIndex; let j = index;">
                                    <tr *ngIf="item?.is_defect">
                                        <td class="pr-0" style="width:8%">{{ (i+1) }}:{{ (j+1) }}</td>
                                        <td class="px-0"><p class="m-0">{{item.question}}</p></td>
                                        <td *ngIf="!item?.close_out?.close_out_at" style="width:12%; text-align: right;">
                                            <button title="View Inspection Tour" style="font-size: 10px;"
                                                    class="btn btn-sm btn-outline-primary mr-1 p-1"
                                                    (click)="viewDefectedItemModal(viewChecklistItemHtml, item, i)">
                                                <i class="fa fa-search"></i></button>
                        
                                            <button title="Close Out" class="btn btn-sm btn-outline-primary p-1" style="font-size: 10px;"
                                                    (click)="closeoutItemModal(itemCloseOutHtml, item, c, i, j)">
                                                <i class="fa fa-times" aria-hidden="true"></i></button>
                                        </td>
                                        <td *ngIf="item?.close_out?.close_out_at" style="width:10%; text-align: center;">
                                            <i class="fa fa-check-square text-success"></i>
                                        </td>
                                    </tr>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                        
                        <ng-template #noSubheadings>
                            <ng-container *ngFor="let item of itpInspection.items; trackBy: trackByRowIndex; let i = index;">
                                <tr *ngIf="item?.is_defect">
                                    <td class="pr-0" style="width:8%">{{ (i+1) }}</td>
                                    <td class="px-0"><p class="m-0">{{item.question}}</p></td>
                                    <td *ngIf="!item?.close_out?.close_out_at" style="width:12%; text-align: right;">
                                        <button title="View Inspection Tour" style="font-size: 10px;"
                                                class="btn btn-sm btn-outline-primary mr-1 p-1"
                                                (click)="viewDefectedItemModal(viewChecklistItemHtml, item, i)">
                                            <i class="fa fa-search"></i></button>
                        
                                        <button title="Close Out" class="btn btn-sm btn-outline-primary p-1" style="font-size: 10px;"
                                                (click)="closeoutItemModal(itemCloseOutHtml, item, c, i)">
                                            <i class="fa fa-times" aria-hidden="true"></i></button>
                                    </td>
                                    <td *ngIf="item?.close_out?.close_out_at" style="width:10%; text-align: center;">
                                        <i class="fa fa-check-square text-success"></i>
                                    </td>
                                </tr>
                            </ng-container>
                        </ng-template>
                        
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-outline-primary" (click)="d('Cross click')">Close</button>
    </div>
</ng-template>

<ng-template #itemCloseOutHtml let-c="close" let-d="dismiss">
    <div class="modal-header">
        <h4 class="modal-title">
            Defect Closeout
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="text-left">
            <table class="table table-sm table-bordered">
                <tbody>
                <tr>
                    <td class="tr-bg-dark-color w-25"> <strong>Checklist item:</strong> </td>
                    <td> {{checklistItem?.question}} </td>
                </tr>
                <tr *ngIf="checklistItem && checklistItem.summary">
                    <td class="tr-bg-dark-color w-25"> <strong>Summary:</strong> </td>
                    <td style="white-space: pre-line;"> {{ checklistItem?.summary }} </td>
                </tr>
                <tr *ngIf="checklistItem && checklistItem?.tagged_user_ref && checklistItem?.tagged_user_ref?.id">
                    <td class="tr-bg-dark-color w-25"> <strong>Assigned To:</strong> </td>
                    <td> {{ checklistItem.tagged_user_ref.name }} </td>
                </tr>
                <tr *ngIf="checklistItem && checklistItem.corrective_action_required">
                    <td class="tr-bg-dark-color w-25"> <strong>Corrective Action Required:</strong> </td>
                    <td style="white-space: pre-line;"> {{ checklistItem?.corrective_action_required }} </td>
                </tr>
                <tr *ngIf="checklistItem && checklistItem.photos.length">
                    <td colspan="2">
                        <div class="row pl-3 pr-3">
                            <div class="pr-3 pt-1" *ngFor="let t of checklistItem?.photos">
                                <img [src]="t?.sm_url || t?.file_url" alt="Item - Image"
                                    (click)="viewImage(t?.file_url, viewItemImgHtml)"
                                    style="max-height:200px; max-width: 200px; cursor: pointer; border-radius: 5px;"
                                >
                            </div>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="mb-2 mt-1">
            <p class="mb-1"><strong>Details</strong> <small class="required-asterisk ">*</small></p>
            <textarea class="form-control" name="closeout_detail"
                      [(ngModel)]="closeout_detail"
                      ng-value="closeout_detail"
                      placeholder="Closeout detail"
                      #closeOutDetail="ngModel" required></textarea>
            <div class="alert alert-danger" [hidden]="closeOutDetail.valid">Closeout detail is required</div>
        </div>
        <div class="col-md-12 p-0 mt-3">
            <p class="mb-1"><strong>Upload Files</strong></p>
        </div>
        <div class="col-md-12 p-0 mb-4">
            <div class="col-md-12 flex-grow-1 p-0" *ngFor="let c of closeout_images">
                <file-uploader-v2
                    [disabled]="false"
                    [init]="c"
                    [category]="'itp-close-out'"
                    [dragnDropTxt]="'Drag and drop image or pdf here'"
                    [hasImgAndDoc]="true"
                    (uploadDone)="uploadDone($event)"
                    [allowedMimeType]="allowedMime"
                    [showFileName]="false"
                    (deleteFileDone)="deleteDone($event)"
                    [showDeleteBtn]="true" [multipleUpload]="true"
                >
                </file-uploader-v2>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-primary" (click)="d('Cross click')">Cancel</button>
        <button type="button" class="btn btn-success" (click)="closeOutRequest(c, viewChecklistItemHtml)" [disabled]="!closeOutDetail.valid">Close Out</button>
    </div>
</ng-template>
<block-loader [show]="(closeoutLoading)" [showBackdrop]="true" alwaysInCenter="true"></block-loader>

<ng-template #viewChecklistItemHtml let-c="close" let-d="dismiss">
    <div class="modal-header">
        <h4 class="modal-title">
            Item Details
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="text-left pb-2">
            <table class="table table-sm table-bordered mb-0">
                <tbody>
                <tr>
                    <td class="tr-bg-dark-color w-25"> <strong>Checklist item:</strong> </td>
                    <td> {{checklistItem?.question}} </td>
                </tr>
                <tr *ngIf="checklistItem && checklistItem.summary">
                    <td class="tr-bg-dark-color w-25"> <strong>Summary:</strong> </td>
                    <td style="white-space: pre-line;"> {{ checklistItem?.summary }} </td>
                </tr>
                <tr *ngIf="checklistItem && checklistItem?.tagged_user_ref && checklistItem?.tagged_user_ref?.id">
                    <td class="tr-bg-dark-color w-25"> <strong>Assigned To:</strong> </td>
                    <td> {{ checklistItem.tagged_user_ref.name }} </td>
                </tr>
                <tr *ngIf="checklistItem && checklistItem.corrective_action_required">
                    <td class="tr-bg-dark-color w-25"> <strong>Corrective Action Required:</strong> </td>
                    <td style="white-space: pre-line;"> {{ checklistItem?.corrective_action_required }} </td>
                </tr>
                <tr *ngIf="checklistItem && checklistItem.photos.length">
                    <td colspan="2">
                        <div class="row pl-3 pr-3">
                            <div class="pr-3 pt-1" *ngFor="let t of checklistItem?.photos">
                                <img [src]="t?.sm_url || t?.file_url" alt="Item - Image"
                                    (click)="viewImage(t?.file_url, viewItemImgHtml)"
                                    style="max-height:200px; max-width: 200px; cursor: pointer; border-radius: 5px;"
                                >
                            </div>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="modal-footer">
        <button *ngIf="!checklistItem?.close_out?.close_out_at" title="Close Out" class="btn btn-outline-success"
                (click)="closeoutItemModal(itemCloseOutHtml, checklistItem, c, itemIdx)">Close Out</button>
        <button type="button" class="btn btn-outline-primary" (click)="d('Cross click')">OK</button>
    </div>
</ng-template>

<ng-template #viewItemImgHtml let-c="close" let-d="dismiss">
    <div class="modal-header">
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="form-group row">
            <div class="col-sm-12 mt-2 text-center">
                <img [src]="itemImgSrc" (error)="onLogoError(img, itemImgSrc)" (load)="onLogoLoad(img)" style="width: 100%; height: auto;" #img />
            </div>
        </div>
    </div>
</ng-template>

<ng-template #qclReportContent let-c="close" let-d="dismiss">
    <div class="modal-header">
        <h4 class="modal-title"> <span *ngIf="selectedQcl && selectedQcl.checklist_ref">{{ selectedQcl.checklist_ref.qc_title }}</span> - #<span *ngIf="selectedQcl">{{ selectedQcl?.record_ref }}</span></h4>
        <button type="button" class="close" aria-label="Close" (click)="c('Cancel click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body scroll-wrapper">
        <iframe #qclReportFrame id='qclReportFrame' class="border-0" style="width: 98%;height: 100%;position: absolute;"></iframe>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-light" (click)="d('Ok')">Ok</button>
    </div>
    <block-loader alwaysInCenter="true" [show]="iframe_content_loading" #qclFrameLoader></block-loader>
</ng-template>

<share-tool-report-to-email
    [projectId]="projectId" [employerId]="employerId" #shareItpReportModal
    [tool_name]="'Share '+itpSglrPhrase" (onSave)="shareItpReport($event)">
</share-tool-report-to-email>

<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
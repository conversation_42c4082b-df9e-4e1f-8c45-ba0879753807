/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-05-11 13:26:00
 * @Last Modified By: <PERSON><PERSON><PERSON>
 * @Last Modified Time: 2018-09-20 18:03:04
 */
import {Component, OnInit, ViewChild, ElementRef, TemplateRef} from "@angular/core";
import {ActivatedRoute, Router} from "@angular/router";
import { HttpParams } from '@angular/common/http';
import {NgbModalConfig, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {AuthService, isInheritedProjectOfCompany, Project, ProjectService, User, QualityChecklistsService, QualityChecklist, Common, HttpService} from "@app/core";
import { fromEvent } from 'rxjs';
import { filter, debounceTime, distinctUntilChanged, tap } from 'rxjs/operators';
import * as dayjs from 'dayjs';
import { ShareToolReportToEmailComponent } from "../share-tool-report-to-email/share-tool-report-to-email.component";
import {AppConstant} from "@env/environment";
import { DragulaService } from 'ng2-dragula';
import { ActionBtnEntry, GenericConfirmationModalComponent } from "@app/shared";

@Component({
    selector: 'quality-checklists',
    templateUrl: './quality-checklists.component.html',
    styleUrls: ['./quality-checklista.component.scss'],
    providers: [NgbModalConfig, NgbModal],
})

export class QualityChecklistsComponent implements OnInit {

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    employer: any = {};
    employerId: number = 0;
    queId: number = 2;
    authUser$: User;
    is_inherited_project: boolean = false;
    iframe_content_loading: boolean = false;
    loading: boolean = false;
    selectedTabId: number;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    inspectionRecords: Array<any> = [];
    temp: Array<any> = [];
    qualityCL: QualityChecklist = new QualityChecklist;
    selectedQcl: QualityChecklist;
    paginationData = new Common();
    page = this.paginationData.page;
    contentLoading: boolean = true;
    currentItem: QualityChecklist = {};
    colCount: number = 1;
    qclTitlesChunk:  Array<any> = [];
    private _qcl_checklists: Array<QualityChecklist> = [];
    public get qclChecklists(): Array<QualityChecklist> { return this._qcl_checklists; }
    public set qclChecklists(qcl_checklists: Array<QualityChecklist>) {
        this._qcl_checklists = qcl_checklists;
        this.qclTitlesChunk = this.chunk(qcl_checklists, 3);
    }
    hasAllDefectsClosed: boolean = false;
    defectsColumnTxt: string = '';
    itpInspection: any;
    checklistItem: any;
    closeout_detail: string = '';
    closeout_images:  Array<any> = [];
    closeoutLoading: boolean = false;
    allowedMime : Array<any> = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    itemImgSrc: string = '';
    itemIdx: number = 0;
    subItemIdx: number = 0;
    itpPhrase: string = ``;
    itpSglrPhrase: string = ``;
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    isProjectPortal: boolean = false;
    fieldsValid: boolean = true;
    isMobileDevice: boolean = false;
    search : string = "";
    baseButtonConfig: Array<ActionBtnEntry> = [];

    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private authService: AuthService,
        private modalService: NgbModal,
        private projectService: ProjectService,
        private qualityCLService: QualityChecklistsService,
        private httpService: HttpService,
        private readonly dragulaService: DragulaService,
    ) {
        let selectedQcl = this.activatedRoute.snapshot.queryParams['selected_qcl'];
        if(selectedQcl) { this.selectedTabId = selectedQcl; }
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
        }
    }

    dayjs(n: number) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };
    dayjsShowDT(n: number) {
        return this.dayjs(n).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS);
    };

    columns(a, cols) {
        return [...Array(cols).keys()].map(c => a.filter((_, i) => i % cols === c));
    }

    chunk(arr, size) {
        if(arr.length >= 17) {
            this.colCount = 3; //3x4 = 12 bootstrap cols.
            return this.columns(arr, 4);
        }
        var newArr = [];
        if(arr.length >= 7) { size = 4;}
        for (var i=0; i<arr.length; i+=size) {
            newArr.push(arr.slice(i, i+size));
        }
        this.colCount = (newArr.length/12) || 1;
        return newArr;
    }

    ngOnInit() {
        this.buildButtonGroup();
        if(!this.isProjectPortal) {
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
                this.employerId = params['employerId'];
            });
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        } else {
            this.initQCLTabs();
        }
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });
    }

    buildButtonGroup() {
        this.baseButtonConfig = [
            {
                key: 'view',
                label: '',
                title: `View ${(this.itpSglrPhrase ? this.itpSglrPhrase : '')}`,
                mat_icon: 'search',
            },
            {
                key: 'download',
                label: '',
                title: `Download ${(this.itpSglrPhrase ? this.itpSglrPhrase : '')}`,
                mat_icon: 'download',
            },
            {
                key: 'share',
                label: '',
                title: `Share ${(this.itpSglrPhrase ? this.itpSglrPhrase : '')}`,
                mat_icon: 'share',
            },
            {
                key: 'close_out',
                label: '',
                title: `Close Out ${(this.itpSglrPhrase ? this.itpSglrPhrase : '')}`,
                mat_icon: 'close',
            }
        ];
    }

    
    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any, qclReportContent, defectsHtml): void {
        const actionMap = {
            'view': () => this.viewQclReport(row, qclReportContent),
            'download': () => this.getQclReport(row, 'pdf'),
            'share': () => this.openShareItpDetailModal(row),
            'close_out': () => this.defectsListModal(defectsHtml, row),
        };
        const action = actionMap[entry.key];
        if (action) {
            action();
        }
    }

    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, selectedTabId) {
        this.page.pageNumber = pageInfo.offset;
        this.selectedTabId = selectedTabId;
        this.initQCLTabs();
    }

    private initQCLTabs() {
        const params = new HttpParams()
        .set('enabled', 'true');
        this.itpPhrase = this.projectInfo ? this.projectInfo.custom_field.qcl_phrase : '';
        this.itpSglrPhrase = this.projectInfo ? this.projectInfo.custom_field.qcl_phrase_singlr : '';
        this.qualityCLService.getProjectQChecklists(this.projectId, this.employerId, params).subscribe((data:any) => {
            if (data && data.project_checklists) {
                this.qclChecklists = data.project_checklists;
                this.temp = this.qclChecklists;
                this.selectedTabId = (!this.selectedTabId && this.temp.length) ? this.temp[0].id : this.selectedTabId;
                if(this.selectedTabId) {
                    this.currentItem = (this.qclChecklists || []).find(cl => cl.id == this.selectedTabId);
                    this.loadChecklistInspections(this.selectedTabId);
                } else { this.contentLoading = false; }
                return data.project_checklists;
            }
            console.log('Failed to fetch ITPs. ', data);
            alert('Failed to fetch ITPs, id:' + this.projectId);
            return [];
        });
    }

    changeContent(item) {
        this.currentItem = item;
        this.inspectionRecords = [];
        this.page.pageNumber = 0;
        this.contentLoading = true;
        this.selectedTabId = +item.id;
        this.loadChecklistInspections(+item.id);
    }

    loadChecklistInspections(qclId: number) {
        let selectedQcl = this.qclChecklists.find(a => +a.id === +qclId);
        if (!selectedQcl) {
          this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Remove',
            title: `this ITP is disabled. you are being redirected to next enabled ITP.`,
            confirmLabel: 'OK',
            hasCancel: false,
            onConfirm: () => {
              let enabledChecklist = this.qclChecklists.find(obj => obj.enabled === true);
              this.selectedTabId = enabledChecklist.id;
              this.initQCLTabs();
              return;
            }
        });
    }
  
        let params = new HttpParams()
        .set('pageNumber', `${this.page.pageNumber}`)
        .set('pageSize', `${this.page.size}`)
        .set('excludeFinalised', 'false');
        if(this.search){
            params = params.append('q', this.search);
        }

        const requestStartTime = Date.now();
        const minimumLoadingTime = 500;
        this.qualityCLService.getCLInspectionsByclId(qclId, this.projectId, params).subscribe((data: any) => {
            if (data && data.cl_inspections) {
                this.inspectionRecords = data.cl_inspections;
                this.page.totalElements = data.total_record_count;

                const elapsedTime = Date.now() - requestStartTime;
                const waitTime = Math.max(minimumLoadingTime - elapsedTime, 0);
                setTimeout(() => {
                    this.contentLoading = false;
                }, waitTime);

                return data.cl_inspections;
            }
            console.log('Failed to fetch ITP Inspections. ', data);
            alert('Failed to fetch ITP Inspections, id:' + qclId);
            this.contentLoading = false; // Set contentLoading to false in case of an error
            return [];
        });
        this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { selected_qcl: qclId }, queryParamsHandling: 'merge' });
      }

    // Will get called for Company route only.
    projectRetrieved(project: Project|any){
        this.projectInfo = project;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
        this.initQCLTabs();
    }

    // TODO: Utkarsh: April 10th - update to i-modal based Implementation
    openModal(content, size = 'md', cb = () => {}) {
        const modalRef = this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size
        }).result.then((result) => {
            this.resetQclVars();
            cb();
        }, (reason) => {
            this.resetQclVars();
            cb();
        });
        return false;
    }

    editModal(content, cb) {
        this.qualityCL = this.selectedQcl;
        this.qualityCL.qc_doc.splice(0,0, {});
        this.openModal(content, 'lg', cb);
    }

    filterTabs(textElem: any = '') {
        let searchText = (textElem || '').toLowerCase();
        console.log(`filter by text: ${searchText}`);
        this.qclChecklists = this.temp.filter(function (d) {
            return (!searchText || (d.qc_title && d.qc_title.toLowerCase().indexOf(searchText) !== -1));
        });
        return this.qclChecklists;
    }

    resetQclVars() {
        this.qualityCL = new QualityChecklist;
        this.selectedQcl = null;
    }

    viewQclReport(row, content) {
        this.selectedQcl = row;
        this.modalService.open(content, { size: 'lg', windowClass : "xl-modal" }).result.then((result) => {
            this.resetQclVars();
        }, (reason) => {
            this.resetQclVars();
        });
        this.iframe_content_loading = true;
        this.getQclReport(row, 'html', (html) => {
            let iframe = document.getElementById('qclReportFrame') as HTMLIFrameElement;
            let doc = iframe.contentDocument;
            doc.write(html);
            this.iframe_content_loading = false;
        }, false);
    }

    getQclReport(row, target = 'html', cb = (data) => {}, openWindow = true){
        let params: any = {
            nowMs: dayjs().valueOf(),
        };
        if (this.authUser$.timezone) {
            params.tz = this.authUser$.timezone;
        }
        if (!openWindow) {
            params.embed = 'frame';
        }
        this.loading = true;
        this.qualityCLService.viewOrDownloadQclReport(row.id, row.checklist_ref.id, this.projectId, row.updatedAt,  params, target, (data) => {
            this.loading = false;
            cb(data);
        }, openWindow);
    }

    getDefectsStatusText(itpItems, hasSubheadings) {
        let allDefects;
        if (hasSubheadings) {
            allDefects = itpItems.flatMap(item => item.subheadings.filter(subitem => subitem.is_defect));
        } else {
            allDefects = itpItems.filter(item => item.is_defect);
        }
        
        if (!allDefects.length) {
            this.hasAllDefectsClosed = false;
            this.defectsColumnTxt = `None`;
            return;
        }
        let closedOutDefects = allDefects.filter(item => item?.close_out?.close_out_at);
        if(closedOutDefects.length == allDefects.length) {
            this.hasAllDefectsClosed = false;
            this.defectsColumnTxt = `All Closed`;
            return;
        }
        this.hasAllDefectsClosed = true;
        this.defectsColumnTxt = `Open: ${(allDefects.length - closedOutDefects.length)}/${allDefects.length}`;
        return;
    }

    defectsListModal(content, itpItems){
        this.itpInspection = itpItems;
        this.openModal(content);
    }

    getColor(rating) {
        if (rating === 'no') {
            return "red";
        }
    }

    viewImage(imgSrc, viewItemImgHtml) {
        this.itemImgSrc = imgSrc;
        this.modalService.open(viewItemImgHtml, {
            backdropClass: 'light-blue-backdrop',
            keyboard: true,
            size: "lg"
        });
    }

    logoImgRetries:number = 5;

    onLogoError($img:any, targetSrc:any) {
        $img.src = '/images/project-placeholder.png';
        if(targetSrc && targetSrc.length && this.logoImgRetries) {
            setTimeout(() => {
                this.logoImgRetries--;
                $img.src = targetSrc;
            }, 2000);
        }
    }

    onLogoLoad($img) {
        this.logoImgRetries = 5;
    }

    uploadDone($event) {
        this.closeout_images.splice(1, 0,...$event.userFile);
        this.closeout_images[0] = {};
    }

    deleteDone($event) {
       if($event && $event.userFile && $event.userFile.id) {
            this.closeout_images = this.closeout_images.filter(r => (r.id !== $event.userFile.id));
       }
    }

    viewDefectedItemModal(content, checklistItem, itemIdx, subItemIdx = 0) {
        this.checklistItem = checklistItem;
        this.itemIdx = itemIdx;
        this.subItemIdx = subItemIdx;
        this.openModal(content);
    }

    closeoutItemModal (content, item, c, itemIdx, subItemIdx = 0) {
        this.checklistItem = item;
        this.itemIdx = itemIdx;
        this.subItemIdx = subItemIdx;
        this.closeout_detail = '';
        this.closeout_images = [{}];

        return this.openModal(content, 'md', c);
    }

    closeOutRequest(c) {
        this.closeoutLoading = true;
        let images = (this.closeout_images || []).reduce((result, elm) => {
            if (elm.id) {
                result.push(elm.id);
            }
            return result;
        },[]);

        console.log(`Close out Item with Idx ${this.itemIdx} in checklist.`);

        let closeOutItemInfo = {};
        if(this.itpInspection.hasSubheadings){
           (this.itpInspection.items || []).forEach((heading, headingIndex) => {
            console.log(heading, headingIndex)
            if(headingIndex === this.itemIdx) {
                heading.subheadings.forEach((item, subIndex) => {
                    if(subIndex == this.subItemIdx) {
                        item.close_out = {
                            "reviewed_by": this.authUser$.first_name+' '+this.authUser$.last_name,
                            "close_out_at": dayjs().valueOf(),
                            "details": this.closeout_detail,
                            "images": images
                        }
                        closeOutItemInfo = item;
                    }
                })
            }
           });
        }else{

            (this.itpInspection.items || []).map((item, index) => {
                if(index == this.itemIdx) {
                    item.close_out = {
                        "reviewed_by": this.authUser$.first_name+' '+this.authUser$.last_name,
                        "close_out_at": dayjs().valueOf(),
                        "details": this.closeout_detail,
                        "images": images
                    }
                    closeOutItemInfo = item;
                }
                return item;
            });
        }

        let body = {
            "item_idx": this.itemIdx,
            "sub_item_idx": this.subItemIdx,
            "item_info": closeOutItemInfo
        }

        this.qualityCLService.updateITPToCloseOutItem(this.itpInspection.id, this.projectId, body).subscribe((data: any) => {
            this.closeoutLoading = false;
            if(data && data.success) {
                this.modalService.dismissAll();
                this.initQCLTabs();
                return;
            }
            alert("Failed to closed out the item.");
            this.modalService.dismissAll();
        });
    }

    @ViewChild('shareItpReportModal') shareItpReportModalRef: ShareToolReportToEmailComponent;
    openShareItpDetailModal(row) {
        this.shareItpReportModalRef.openEmailFormModal(row);
    }

    shareItpReport(event) {
        this.qualityCLService.shareItpReport(event.req, event.reportId).subscribe((res:any) => {
            event.cb(res);
        });
    }
    

    searchFunction(data){
        this.search = data.search;
        this.loadChecklistInspections(this.selectedTabId);

    }
    getUniqueId() {
        return Date.now() + Math.round(performance.now());
    }
}

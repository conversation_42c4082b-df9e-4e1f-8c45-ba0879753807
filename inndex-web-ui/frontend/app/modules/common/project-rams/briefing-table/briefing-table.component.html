<ngx-datatable
    #registerTable
    class="bootstrap table table-hover table-sm registerTable expandable    "
    [rows]="briefings"
    columnMode="force"
    [rowHeight]="'auto'"
    [footerHeight]="0"
    [columns]="[
        {name:'', headerClass: 'text-hide', prop: 'expand_btn', minWidth: 20, width: 20, cellTemplate: expandBtnColumn },
        {name:'Date Briefed', prop:'date_briefed',  width: 100, cellTemplate: dateBriefedColumn },
        {name:'Briefed By', prop:'briefed_by_name'},
        {name:'No. of people briefed', prop: 'briefed_count', cellTemplate: peopleBriefedColumn},
        {name:'', headerClass: 'text-hide', prop: 'download', cellTemplate: downloadAttendeesColumn }
    ]"
>
    <ng-template #expandBtnColumn let-row="row" let-value="value" ngx-datatable-cell-template>
    
            <span   (click)="toggleAllattendees(row.briefed_at)" class="material-symbols-outlined cursor-pointer arrow-small">
                {{(expandAllattendees.includes(row.briefed_at)?'expand_less':'expand_more')}}
            </span>
    
    </ng-template>

    <ng-template #dateBriefedColumn let-row="row" let-value="value" ngx-datatable-cell-template>
        {{row.briefed_at ? dayjs(+row.briefed_at).format(AppConstant.defaultDateFormat) : null}}
    </ng-template>

    <ng-template #peopleBriefedColumn let-row="row" let-value="value" ngx-datatable-cell-template>
        <div>
            <p class="mb-0">{{row.allattendees.length}}</p>
            <ul class="m-0 pl-4" *ngIf="expandAllattendees.includes(row.briefed_at)">
                <li *ngFor="let attendee of (row.allattendees)">
                    {{attendee.name}}
                </li>
            </ul>
        </div>
    </ng-template>

    <ng-template #downloadAttendeesColumn let-row="row" let-value="value" ngx-datatable-cell-template>
        <div class='text-center'>
            <button-group
                [buttons]="rowButtonGroup"
                (onActionClick)="rowBtnClicked($event, row)">
            </button-group>
        </div>
    </ng-template>
</ngx-datatable>

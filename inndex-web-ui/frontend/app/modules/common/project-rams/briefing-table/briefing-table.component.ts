import {Component, Input, OnInit, ViewChild} from "@angular/core";
import {ActionBtnEntry, AssetsUrl} from "@app/shared";
import * as ExcelProper from "exceljs";
import * as fs from "file-saver";
import * as Excel from "exceljs/dist/exceljs.min.js";
import {AppConstant} from "@env/environment";
import * as dayjs from 'dayjs';

@Component({
    selector: 'briefing-table',
    templateUrl: './briefing-table.component.html',
    styles: [`
        .arrow-small { height: 8px; }
        :host ::ng-deep .registerTable .datatable-scroll { width: 100% !important;  }

        :host ::ng-deep .registerTable.ngx-datatable .datatable-body {
            height: 22vh !important;
            overflow-y: scroll !important;
            overflow-x: hidden !important;
        }

        :host ::ng-deep .registerTable.ngx-datatable.bootstrap .datatable-body .datatable-body-row { border-top: none; }
        :host ::ng-deep .registerTable.ngx-datatable.bootstrap .datatable-header .datatable-header-cell { border-bottom: none; }
    `]
})

export class BriefingTableComponent implements OnInit {

    @Input()
    briefings:  Array<any> = [];

    @Input()
    ramsSglrPhrase: string;

    @Input()
    briefing_row: any;

    @Input()
    tz: string;

    upArrow = AssetsUrl.siteAdmin.upArrow;
    downArrow = AssetsUrl.siteAdmin.downArrow;

    blobType: string = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    expandAllattendees: Array<any> = [];
    rowButtonGroup: Array<ActionBtnEntry> = [
        {
            key: 'download',
            label: '',
            title: 'Download',
            mat_icon: 'download',
        },
    ];

    ngOnInit() {
    }

    AppConstant = AppConstant;
    dayjs(n: number) {
        return dayjs(n).tz(this.tz);
    };

    toggleAllattendees(id: any) {
        const index = this.expandAllattendees.indexOf(id);
        if (index > -1) {
            this.expandAllattendees.splice(index, 1);
        } else {
            this.expandAllattendees.push(id);
        }
    }

    ramsDownloadAttendees(briefed, title='Risk Assessment & Method Statements(RAMs)') {
        title = `Title: ${title}`;
        let workbook: ExcelProper.Workbook = new Excel.Workbook();
        let worksheet = workbook.addWorksheet(this.ramsSglrPhrase, {
            properties: {
                defaultRowHeight: 50
            }
        });

        let rowIndex = 1;
        worksheet.addRow([`${title}`, ' ', ' ']).commit();
        worksheet.getRow(rowIndex).eachCell(cell => {
            cell.font = {bold: true};
            /*cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: {argb: '82878c'}
            };*/
            cell.border = {
                top: {style: 'medium'},
                left: {style: 'thin'},
                bottom: {style: 'thin'},
                right: {style: 'thin'}
            }
            cell.alignment = { vertical: 'middle' };
        });
        worksheet.mergeCells('A1:C1');
        worksheet.getRow(rowIndex).height = 20;

        worksheet.getColumn('A').width = 32;
        worksheet.getColumn('A').style = {alignment: {wrapText: true}};
        worksheet.getColumn('B').width = 35;
        worksheet.getColumn('B').style = {alignment: {wrapText: true}};
        worksheet.getColumn('C').width = 27;
        rowIndex += 1;
        let briefedAt = briefed.briefed_at ? this.dayjs(+briefed.briefed_at).format(AppConstant.defaultDateFormat) : null;
        worksheet.addRow([`Briefed By: ${briefed.briefed_by_name}`, `Date Briefed: ${briefedAt}`]).eachCell(cell => {
            cell.font = {bold: true};
        });
        worksheet.getRow(rowIndex).height = 20;

        rowIndex += 1;
        worksheet.addRow(["Name", "Employer", "Signature"]).eachCell(cell => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: {argb: 'D3D3D3'}
            };
            cell.border = {
                top: {style: 'thin'},
                bottom: {style: 'thin'},
                right: {style: 'thin'}
            }
        });
        worksheet.getRow(rowIndex).height = 20;

        //sort by name
        briefed.allattendees.sort((a, b) => (a.name > b.name) ? 1 : -1);
        let signatureArr = [];
        briefed.allattendees.map(row => {
            rowIndex += 1;
            if (row.sign) {
                signatureArr[rowIndex] = row.sign;
            }
            worksheet.addRow([row.name, row.employer, null]).height = 50;
            return row;
        });

        signatureArr.map((signature, index) => {
            let imageId = workbook.addImage({
                base64: signature,
                extension: 'png',
            });
            worksheet.addImage(imageId, {
                tl: { col: 2.1, row: (index-1)+0.1 },
                ext: { width: 130, height: 30 }
            });
        });

        workbook.xlsx.writeBuffer().then(data => {
            const blob = new Blob([data], { type: this.blobType });
            fs.saveAs(blob, `${this.ramsSglrPhrase}-Attendees.xlsx`);
        });
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actionMap = {
            'download': () => this.ramsDownloadAttendees(row, this.briefing_row?.briefing_title),
        };
        const action = actionMap[entry.key];
        if (action) {
            action();
        }
    }
}

<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav [employer]="employer" [companyResolverResponse]="companyResolverResponse" *ngIf="!isProjectPortal" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="col-sm-12 my-3 outer-border" *ngIf="(isProjectPortal) ? !loadingPackagePlans : true">
                <div class="outer-border-radius">
                    <div class="d-flex justify-content-between mb-3 mx-3 flex-wrap gap-2">
                        <div class="col-md-5 d-inline-block p-0">
                            <h5 class="float-md-left col-md-12 p-0 m-0">Total {{ ramsPhrase }} <small>({{ page.totalElements }})</small></h5>
                        </div>
                        <small *ngIf="hasOnlySiteManagement && !userEmployer.id" class="spanish-gray-font font-italic">Note: Only {{ ramsPhrase }} that you have personally uploaded are visible. To access all of your company's {{ ramsPhrase }}, you must have an approved {{projectInfo.custom_field.induction_phrase_singlr}} for the project.</small>
                    </div>

                    <div class="d-flex justify-content-between mb-2 mx-3 flex-wrap gap-8">
                        <div class="col-md-9 d-inline-block p-0">
                            <search-with-filters #searchFilters [filterData]="filterData" (searchEmitter)="searchFunction($event)" (filterEmitter)="onFilterSelection($event)"></search-with-filters>
                        </div>
                        <div class="col-md-2 d-flex justify-content-end p-0">
                            <action-button
                                    class="mr-2"
                                    [actionList]="actionButtonMetaData2.actionList"
                                    (selectedActionEmmiter)="onActionSelection($event)"
                                    [actionListTitle]="'Add New'"
                                    [lightBgButton]="true"
                                    [hideNewFeatureBtn]="true">
                            </action-button>
                            <action-button
                                    [actionList]="actionButtonMetaData.actionList"
                                    (selectedActionEmmiter)="onActionSelection($event)"
                                    [hideNewFeatureBtn]="true">
                            </action-button>
                        </div>
                    </div>



                    <div class="col-sm-12 my-2">
                <div class="clearfix"></div>
                <div class="table-responsive-sm" *ngIf="isProjectPortal || isCompanyRoute()">
                    <!-- Risk Assessment & Method Statements(RAMS) table -->
                    <ngx-datatable class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                   [scrollbarV]="true"
                                   [virtualization]="false"
                                   [loadingIndicator]="loadingInlineRams"
                                   [rows]="records"
                                   [footerHeight]="40"
                                   [columnMode]="'force'"
                                   [rowHeight]="'auto'"
                                   [externalPaging]="true"
                                   [count]="page.totalElements"
                                   [offset]="page.pageNumber"
                                   [limit]="page.size"
                                   (page)="pageCallback($event, true)"
                                   [externalSorting]="true"
                                   (sort)="onSort($event)"
                                   [sorts] = "[sorts]"
                    >
                        <ngx-datatable-column prop="id" headerClass="font-weight-bold" [sortable]="true" minWidth="70">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                {{ ramsSglrPhrase }} #
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                {{row.record_id}}
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column prop="title" headerClass="font-weight-bold" [sortable]="true" minWidth="100" cellClass="cell-word-wrap">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Title
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{row.briefing_title}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100" cellClass="cell-word-wrap">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Company
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span appTooltip>{{ row?.tagged_owner?.name}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100" cellClass="cell-word-wrap">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Uploaded By
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <div style="display: inline-grid;">
                                    <span appTooltip>{{row.user_ref.first_name}} {{row.user_ref?.middle_name || ''}} {{row.user_ref.last_name}}</span>
                                    <span appTooltip style="font-size: 11px;">({{ dayjs(+row?.createdAt).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) }})</span>
                                </div>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold action-column text-center" cellClass="action-column text-center no-ellipsis" prop="status" [sortable]="true" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                <span class="ml-3">Status</span>
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <div class="d-inline-block" ngbDropdown #myDrop="ngbDropdown" container="body"
                                     *ngIf="(!hasOnlySiteManagement && [STATUS_CODES.PENDING, STATUS_CODES.IN_REVIEW].indexOf(row.status) !== -1)">
                                    <button class="btn btn-sm font-md-small btn-outline-primary"
                                            ngbDropdownAnchor (click)="myDrop.open()">{{ row.status_message }}
                                    </button>
                                    <div ngbDropdownMenu>
                                        <button class="dropdown-item cursor-pointer" *ngIf="!rams_assessment_file || !rams_assessment_file.id"
                                                (click)="ramsAction('approve', row)">
                                            Accept
                                        </button>

                                        <button class="dropdown-item btn-delete" *ngIf="!rams_assessment_file || !rams_assessment_file.id"
                                                (click)="ramsAction('decline', row)">
                                            Reject
                                        </button>

                                        <button class="dropdown-item" *ngIf="rams_assessment_file && rams_assessment_file.id && row.status == 1"
                                                (click)="ramsAction('review', row)">
                                            Review
                                        </button>

                                        <button class="dropdown-item" *ngIf="rams_assessment_file && rams_assessment_file.id && row.status == 3"
                                                (click)="ramsAction('review_again', row)">
                                            Continue Review
                                        </button>
                                    </div>
                                </div>
                                <div [ngClass]="{'ml-3': row.reject_reason}" *ngIf="(hasOnlySiteManagement || [STATUS_CODES.DECLINED, STATUS_CODES.APPROVED].indexOf(row.status) !== -1)">
                                    <span [class]="'btn unsatisfactoryItemsStatus btn-sm btn-outline-'+STATUS_COLORS[row.status]+' bg-'+STATUS_COLORS[row.status]+' text-white'">{{ row.status_message }}</span>
                                    <i *ngIf="row.reject_reason" class="ml-1 fa fa-info-circle"
                                       [ngbTooltip]="commentTooltip"
                                       [openDelay]="200" [closeDelay]="500"></i>
                                </div>
                                <ng-template #commentTooltip>
                                    <div>
                                        Comment: <span [innerHtml]="row.reject_reason"></span>
                                    </div>
                                </ng-template>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Available
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <div class="col-6 ml-1" *ngIf="row.status != STATUS_CODES.DECLINED">
                                    <input type="checkbox" class="form-control checkbox-small"
                                           [checked]="checkAvailability(row)"
                                           (click)="makeBriefingAvailable($event, row)"/>
                                </div>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" *ngIf="projectInfo.custom_field.has_rams_in_induction" [sortable]="false" minWidth="120">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Include in {{projectInfo.custom_field.induction_phrase_singlr}}
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <div class="col-6 ml-1" *ngIf="row.status != 0">
                                    <input type="checkbox" class="form-control checkbox-small"
                                           [checked]="row.include_during_induction" disabled/>
                                </div>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="110">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                No. Times Briefed
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <div class="ml-4">
                                    {{row?.briefed_count}}
                                </div>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="110">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Last Briefed
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <div class="d-flex flex-column">
                                    <span *ngIf="row?.last_briefed_at else notBriefed">
                                        {{ dayjs(+row?.last_briefed_at).format(AppConstant.defaultDateFormat)}}
                                        <div class="small">({{ dayjs(+row?.last_briefed_at).format(AppConstant.defaultTimeFormat) }})</div>
                                    </span>
                                    <ng-template #notBriefed>-</ng-template>
                                </div>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column headerClass="font-weight-bold action-column" cellClass="action-column no-ellipsis" [sortable]="false" minWidth="100">
                            <ng-template let-column="column" ngx-datatable-header-template>
                                Action
                            </ng-template>
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <button-group
                                    [buttons]="rowButtonGroup"
                                    (onActionClick)="rowBtnClicked($event, row)">
                                </button-group>
                            </ng-template>
                        </ngx-datatable-column>
                    </ngx-datatable>
                    <!-- END Risk Assessment & Method Statements(RAMS) table -->
                    <block-loader [show]="(processingRams)"></block-loader>

                    <ng-template #ramsDetailsHtml let-c="close" let-d="dismiss">
                        <div class="modal-header">
                            <h6 class="modal-title">
                                <span (click)="d('Cross click')" *ngIf="briefing_row.is_archived" class="mr-2"><i class="fa fa-chevron-left cursor-pointer"></i></span>
                                {{ ramsSglrPhrase }} #{{ briefing_row?.record_id }} Details
                            </h6>
                            <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
                                <span type="button" class="close-icon small">
                                    <span aria-hidden="true" style="padding: 1px 7px;">×</span>
                                </span>
                            </button>
                        </div>
                        <div class="modal-body py-4 px-4">
                            <form novalidate #detailForm="ngForm">
                                <div class="rams-details" *ngIf="isProjectPortal || isCompanyRoute()">
                                    <div class="form-group">
                                        <label>Revisions</label>
                                        <div class="d-flex col-12 p-0">
                                            <div class="input-group mb-3">
                                                <ng-select class="w-100 dropdown-list" [clearable]="false" [ngClass]="{'w-100 dropdown-list': true}" appendTo="body" [items]="ramsRevisions" bindLabel="revision_number" bindValue="id"
                                                           name="revision_detail" placeholder="Select Revision" [(ngModel)]="briefing_row.id" (change)="ramsDetailModal($event, true)">
                                                    <ng-template ng-label-tmp let-item="item">
                                                        <span>{{ item.revision_number}}</span>
                                                    </ng-template>
                                                    <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                                                        <span>{{ item.revision_number}}</span>
                                                    </ng-template>
                                                </ng-select>
                                            </div>

                                            <div class="ml-2 col-2 p-0" *ngIf="hasAddRevisionAbility">
                                                <button type="button" class="w-100 btn btn-sm btn-outline-brandeis-blue btn-hover d-flex align-items-center justify-content-center" style="line-height: 1.8;" (click)="showRamsDetail(briefing_row, 2)">
                                                    <span class="medium-font m-font-size material-symbols-outlined mr-2">add</span>
                                                    <div class="medium-font m-font-size">New Revision</div>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="input-group mb-2">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Uploaded By</span>
                                        </div>
                                        <input type="text" class="form-control"
                                            [disabled]="true" [value]="briefing_row?.user_ref?.first_name+' '+briefing_row?.user_ref?.last_name">
                                    </div>
                                    <div class="input-group mb-2">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Date Uploaded</span>
                                        </div>
                                        <input type="text" class="form-control"
                                            [disabled]="true" [value]="dayjs(+briefing_row?.createdAt).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS)">
                                    </div>
                                    <div class="d-flex">
                                        <div class="input-group mb-2">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">Title</span>
                                            </div>
                                            <input type="text" class="form-control"
                                                placeholder="{{ ramsSglrPhrase }} title..." name="briefing_title"
                                                [(ngModel)]="briefing_title" [disabled]="titleEditDisable" required="true">
                                        </div>
                                        <div class="ml-2" *ngIf="briefing_row.status != 0 && !briefing_row.is_archived">
                                            <ng-container *ngIf="titleEditDisable; else isNotEditable">
                                                <button type="button" class="btn-action border-0 d-flex align-items-center justify-content-center" (click)="enableEdit('title')">
                                                    <img class="img-icon px-1" [src]="AssetsUrlSiteAdmin.pencilSquare" alt="">
                                                    <span class="">Edit</span>
                                                </button>
                                            </ng-container>
                                            <ng-template #isNotEditable>
                                                <button type="button" class="btn-action border-0" (click)="updateRAMS()" [disabled]="!briefing_title">
                                                    <span class="">Save</span>
                                                </button>
                                            </ng-template>
                                        </div>
                                    </div>

                                    <div class="d-flex">
                                        <div class="input-group mb-2">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">Reference Number</span>
                                            </div>
                                            <input type="text" class="form-control"
                                                   placeholder="Reference Number" name="reference_number"
                                                   [(ngModel)]="reference_number" disabled readonly required>
                                        </div>
                                    </div>
                                    <div class="d-flex">
                                        <div class="input-group mb-2">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">Company</span>
                                            </div>
                                            <company-selector-v2
                                                [required]="true"
                                                [country_code]="projectInfo?.custom_field?.country_code"
                                                name="tagged_owner"
                                                [selectId]="tagged_owner"
                                                placeholder="Select Company"
                                                [ngStyle]="{'flex': '1 1 auto'}"
                                                [disabled]="companyEditDisable"
                                                (selectionChanged)="tagged_owner = $event.selected"
                                                [projectId]="projectId"
                                            ></company-selector-v2>
                                        </div>
                                        <div class="ml-2" *ngIf="briefing_row.status != 0 && !briefing_row.is_archived">
                                            <ng-container *ngIf="companyEditDisable; else companyEdit">
                                                <button type="button" class="btn-action border-0 d-flex align-items-center justify-content-center" (click)="enableEdit('company')">
                                                    <img class="img-icon px-1" [src]="AssetsUrlSiteAdmin.pencilSquare" alt="">
                                                    <span class="mt-1">Edit</span>
                                                </button>
                                            </ng-container>
                                            <ng-template #companyEdit>
                                                <button type="button" class="btn-action border-0" (click)="updateRAMS()" [disabled]="!tagged_owner">
                                                    <span class="mt-1">Save</span>
                                                </button>
                                            </ng-template>
                                        </div>
                                    </div>
                                    <div class="input-group mb-2">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Document</span>
                                        </div>
                                        <a type="text" class="form-control text-info"
                                            [title]="briefing_row?.briefing_file_ref?.name"
                                            [href]="briefing_row?.briefing_file_ref?.file_url" target="_blank">
                                            {{ briefing_row?.briefing_file_ref?.name }}
                                        </a>
                                    </div>
                                    <div class="input-group mb-2" *ngIf="projectInfo.custom_field.has_rams_in_induction && briefing_row.status != 0">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Include in the {{ inductionSglrPhrase }} process</span>
                                        </div>
                                        <div class="form-control">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                       [disabled]="hasOnlySiteManagement"
                                                       [checked]="include_during_induction"
                                                       style="z-index: 99999;" (click)="includeDuringInduction($event, 'save')"
                                                       name="include_during_induction" autocomplete='off'>
                                                <label class="custom-control-label"></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="input-group mb-2" *ngIf="briefing_row?.auto_approved">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Is Auto-accepted?</span>
                                        </div>
                                        <div class="form-control">
                                            Yes
                                        </div>
                                    </div>

                                    <div *ngIf="briefing_row?.register.length">
                                        <div class="d-flex">
                                            <div class="input-group mb-2">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">Last Briefed:</span>
                                                </div>
                                                <input type="text" class="form-control"
                                                       [disabled]="true" [value]="dayjs(+briefing_row.register[0]?.briefed_at).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS)">
                                            </div>
                                        </div>
                                        <div class="d-flex">
                                            <div class="input-group mb-2">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">Last Briefed by:</span>
                                                </div>
                                                <input type="text" class="form-control"
                                                       [disabled]="true" [value]="briefing_row.register[0]?.briefed_by_name + (briefing_row.register[0]?.induction_company ? ' (' + briefing_row.register[0]?.induction_company + ')' : '')">
                                            </div>
                                        </div>
                                    </div>
                            </div>
                            </form>

                            <ng-container *ngIf="briefing_row.register.length">
                                <div class="row gx-1">
                                    <div [ngClass]="{'col-6 pr-2 mt-2': induction_briefings.length || (projectInfo.custom_field.has_rams_in_induction && briefing_row.include_during_induction), 'col': !(projectInfo.custom_field.has_rams_in_induction && briefing_row.include_during_induction)}">
                                        <h6>Register</h6>
                                        <div class="table-responsive-sm">
                                            <briefing-table
                                                [briefings]="briefings"
                                                [ramsSglrPhrase]="ramsSglrPhrase"
                                                [briefing_row]="briefing_row"
                                                [tz]="projectInfo.custom_field.timezone"
                                            ></briefing-table>
                                        </div>
                                    </div>

                                    <div *ngIf="induction_briefings.length || (projectInfo.custom_field.has_rams_in_induction && briefing_row.include_during_induction)" class="col-6 pl-2 mt-2">
                                        <h6>{{projectInfo.custom_field.induction_phrase_singlr}} Briefing Register</h6>
                                        <div class="table-responsive-sm">
                                            <briefing-table
                                                [briefings]="induction_briefings"
                                                [ramsSglrPhrase]="ramsSglrPhrase"
                                                [briefing_row]="briefing_row"
                                                [tz]="projectInfo.custom_field.timezone"
                                            ></briefing-table>
                                        </div>
                                    </div>
                                </div>
                            </ng-container>
                        </div>
                        <div class="modal-footer">
                            <div class="col-md-12 pl-1 pr-1 m-0">
                                <button type="button" class="btn float-left px-0 d-flex align-items-center" (click)="openConfirmationModal(briefing_row)" *ngIf="!briefing_row.is_archived">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.1665 18.3333C3.70817 18.3333 3.31581 18.1701 2.98942 17.8437C2.66303 17.5174 2.49984 17.125 2.49984 16.6667V7.27082C2.24984 7.11804 2.04845 6.92013 1.89567 6.67707C1.74289 6.43402 1.6665 6.15277 1.6665 5.83332V3.33332C1.6665 2.87499 1.8297 2.48263 2.15609 2.15624C2.48248 1.82985 2.87484 1.66666 3.33317 1.66666H16.6665C17.1248 1.66666 17.5172 1.82985 17.8436 2.15624C18.17 2.48263 18.3332 2.87499 18.3332 3.33332V5.83332C18.3332 6.15277 18.2568 6.43402 18.104 6.67707C17.9512 6.92013 17.7498 7.11804 17.4998 7.27082V16.6667C17.4998 17.125 17.3366 17.5174 17.0103 17.8437C16.6839 18.1701 16.2915 18.3333 15.8332 18.3333H4.1665ZM4.1665 7.49999V16.6667H15.8332V7.49999H4.1665ZM3.33317 5.83332H16.6665V3.33332H3.33317V5.83332ZM7.49984 11.6667H12.4998V9.99999H7.49984V11.6667Z" fill="black"/></svg>
                                    <span class="ml-1" style="font-weight: 600;">Archive</span>
                                </button>
                                <button type="button" class="btn float-left px-0 d-flex align-items-center" (click)="openConfirmationModal(briefing_row)" *ngIf="briefing_row.is_archived">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.1665 18.3333C3.70817 18.3333 3.31581 18.1701 2.98942 17.8437C2.66303 17.5174 2.49984 17.125 2.49984 16.6667V7.27082C2.24984 7.11804 2.04845 6.92013 1.89567 6.67707C1.74289 6.43402 1.6665 6.15277 1.6665 5.83332V3.33332C1.6665 2.87499 1.8297 2.48263 2.15609 2.15624C2.48248 1.82985 2.87484 1.66666 3.33317 1.66666H16.6665C17.1248 1.66666 17.5172 1.82985 17.8436 2.15624C18.17 2.48263 18.3332 2.87499 18.3332 3.33332V5.83332C18.3332 6.15277 18.2568 6.43402 18.104 6.67707C17.9512 6.92013 17.7498 7.11804 17.4998 7.27082V16.6667C17.4998 17.125 17.3366 17.5174 17.0103 17.8437C16.6839 18.1701 16.2915 18.3333 15.8332 18.3333H4.1665ZM4.1665 7.49999V16.6667H15.8332V7.49999H4.1665ZM3.33317 5.83332H16.6665V3.33332H3.33317V5.83332ZM7.49984 11.6667H12.4998V9.99999H7.49984V11.6667Z" fill="black"/></svg>
                                    <span class="ml-1" style="font-weight: 600;">Unarchive</span>
                                </button>
                                <button type="button" class="btn btn-brandeis-blue float-right" (click)="d('Cross click')">Done</button>
                            </div>
                        </div>
                    </ng-template>
                </div>
                <div class="clearfix"></div>
            </div>
        </div>
        </div>
        </div>
    </div>
</div>
<ng-template #content let-c="close" let-d="dismiss">
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">
            <span *ngIf="change_request.status === STATUS_CODES.DECLINED">Reject Request</span>
            <span *ngIf="change_request.status === STATUS_CODES.APPROVED">Accept Request</span>
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">

    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-outline-primary"
                [class.btn-outline-success]="change_request.status === STATUS_CODES.APPROVED"
                [class.btn-outline-danger]="change_request.status === STATUS_CODES.DECLINED"
                disabled>
            <ng-container *ngIf="change_request.status === STATUS_CODES.DECLINED">Reject</ng-container>
            <ng-container *ngIf="change_request.status === STATUS_CODES.APPROVED">Accept</ng-container>
        </button>
    </div>
    <block-loader [show]="(false)" #modalLoader></block-loader>
</ng-template>

<ng-template #declineFormHtml let-c="close" let-d="dismiss">
    <div class="modal-header">
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body text-center">
        <form novalidate #declineForm="ngForm">
            <div class="mb-2">
                <p class="mb-1">Comment: </p>
                <textarea class="form-control" name="reject_reason"
                          [(ngModel)]="reject_reason"
                          placeholder="Reason to reject"
                          #declinedComment="ngModel" required></textarea>
                <div class="alert alert-danger" [hidden]="(!declinedComment || declinedComment.valid)">Comment is required</div>
            </div>
        </form>
        <div class="modal-footer pr-0">
            <button type="button" class="btn btn-outline-primary" (click)="d('Cross click')">Cancel</button>
            <button type="button" class="btn btn-outline-success"
                    [disabled]="!declineForm.valid"
                    (click)="declineRams(c)">Reject
            </button>
        </div>
    </div>
</ng-template>

<i-modal #addRamsRef [title]="modalTitle" (onClickRightPB)="addRamsRequest(addRamsForm, $event)" rightPrimaryBtnTxt="Save" [rightPrimaryBtnClass]="(!addingNewRams && !selectedRamsRevision.id) ? 'd-none' : 'btn-brandeis-blue'"
    (onClickLeftSB)="mergeUploadedPDFs()" [rightPrimaryBtnDisabled]="(!isValidRevisionNumber || !addRamsForm.valid || (!briefingFiles.length) || addingRamsInProgress)"
    size="md" (onCancel)="cancelModal(addRamsForm)">
        <form novalidate #addRamsForm="ngForm">
            <ng-container *ngIf="!addingNewRams && ramsList.length">
                <div class="form-group">
                    <label>Select {{ramsPhrase}}<small class="required-asterisk">*</small></label>
                        <ng-select class="w-100 dropdown-list" appendTo="body" [items]="ramsList" bindLabel="briefing_title" bindValue="id"
                               name="selectedRamsRevision" placeholder="Select {{ramsPhrase}}"
                                   [ngModel]="selectedRamsRevision.id"
                                   (change)="showRamsDetail($event)"
                                required>
                        <ng-template ng-label-tmp let-item="item">
                            <span>{{ item.briefing_title + ((item.revision_number) ? ' ('+item.revision_number+')' : '')}}</span>
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                            <span>{{ item.briefing_title + ((item.revision_number) ? ' ('+item.revision_number+')' : '')}}</span>
                        </ng-template>
                    </ng-select>
                        <input type="hidden" class="form-control"
                           name="briefing_title"
                           [(ngModel)]="briefing_title">
                </div>
            </ng-container>

            <ng-container *ngIf="addingNewRams || selectedRamsRevision.id">
                <div @slideUpDown>
                    <div class="form-group" *ngIf="addingNewRams || this.revisionType === 2">
                        <label>Title: <small class="required-asterisk ">*</small></label>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control"
                                   placeholder="{{ ramsSglrPhrase }} title..."
                                   name="briefing_title"
                                   required="true"
                                   [(ngModel)]="briefing_title">
                        </div>
                    </div>
                    <div class="form-group">
                        <label [ngClass]="{'spanish-gray-font': selectedRamsRevision.id}">Company: <small class="required-asterisk ">*</small></label>
                        <ng-container *ngIf="selectedRamsRevision.id else taggedOwnerSelector">
                            <input type="text" class="form-control"
                                   name="tagged_owner_name"
                                   [value]="selectedRamsRevision.tagged_owner.name" readonly>
                        </ng-container>
                        <ng-template #taggedOwnerSelector>
                            <company-selector-v2
                                [disabled]="selectedRamsRevision.id || (hasOnlySiteManagement && tagged_owner && userEmployer.id)"
                                [required]="true"
                                [country_code]="projectInfo?.custom_field?.country_code"
                                name="tagged_owner"
                                [selectId]="tagged_owner"
                                placeholder="Select Company"
                                [classes]="'w-100'"
                                (selectionChanged)="tagged_owner = $event.selected"
                                [projectId]="projectId"
                            ></company-selector-v2>
                        </ng-template>
                    </div>
                    <div class="form-group">
                        <label>Revision Number: <small class="required-asterisk ">*</small></label>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control"
                                   #revisionNumber='ngModel'
                                   placeholder="Revision Number"
                                   name="revision_number"
                                   required
                                   (change)="searchRamsByRevisionNumber($event, revisionNumber)"
                                   [(ngModel)]="revision_number">
                            <div class="alert alert-danger mb-0 p-0 pl-2 fs-12" [hidden]="isValidRevisionNumber">This revision number is already in use, enter a unique revision number</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label [ngClass]="{'spanish-gray-font': selectedRamsRevision.id}">{{ramsSglrPhrase}} Reference Number: <small class="required-asterisk ">*</small></label>
                        <div class="input-group mb-3">
                            <ng-container *ngIf="selectedRamsRevision.id else refNumber">
                                <input type="text" class="form-control"
                                       name="reference_number"
                                       [value]="reference_number" readonly>
                            </ng-container>
                            <ng-template #refNumber>
                                <input type="text" class="form-control"
                                       placeholder="{{ramsSglrPhrase}} Reference Number"
                                       name="reference_number"
                                       [required]="true"
                                       [(ngModel)]="reference_number">
                            </ng-template>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Upload File (PDF): <small class="required-asterisk ">*</small></label>
                        <input type="hidden" name="briefing_file_ref" id="briefing_file_ref"
                               [(ngModel)]="briefing_file_ref" required/>
                        <!-- user can upload maximum 10 files -->
                        <div *ngIf="briefingFiles.length < 11">
                            <div *ngFor="let c of briefingFiles" [ngClass]='{"flex-grow-1 p-0": true, "col-md-4": c.id, "col-md-12 mb-3": !c.id}'>
                                <file-uploader-v2
                                    *ngIf="!c.id"
                                    [multipleUpload]="true"
                                    [convertDocToPdf]="true"
                                    [disabled]="false"
                                    [category]="'rams-upload'"
                                    [dragnDropTxt]="'Drag and drop pdf here'"
                                    (uploadDone)="uploadDone($event)"
                                    [allowedMimeType]="allowedMime"
                                    [showFileName]="false"
                                    [maxFileSize]="25 * 1024 * 1024"
                                ></file-uploader-v2>
                            </div>
                        </div>
                        <div class="col-12 p-0 mb-2" dragula="questions" [(dragulaModel)]="briefingFiles" (dragulaModelChange)="onPDFPositionChanges($event)">
                            <div class="mt-2" *ngFor="let file of briefingFiles; let i=index">
                                <div *ngIf="file.id" class="row mx-auto d-flex align-items-center link-item-row">
                                    <div class="col-1 d-flex justify-content-center align-items-center prepend-item">
                                        <span class="material-symbols-outlined border-right-radius m-2 x-large-font">menu</span>
                                    </div>
                                    <div class="col-11 d-flex justify-content-between align-items-center">
                                        <a [href]="file.file_url" target="_blank" class="text-brandeis-blue medium-font fw-500 text-ellipsis">{{ file.name }}</a>
                                        <span (click)="deleteFile(file)" class="material-symbols-outlined attachment-cancel-icon medium-font cursor-pointer">close</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="hasFullAccessOrRestrictedOf()" class="form-group row pl-3">
                        <label class="font-weight-bold" for="is_auto_approve">Auto-accept {{ ramsSglrPhrase }}:</label>
                        <div class="custom-control custom-checkbox ml-2">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="is_auto_approve" style="z-index: 99999;"
                                   name="is_auto_approve"
                                   id="is_auto_approve">
                            <label class="custom-control-label"></label>
                        </div>
                    </div>
                    <div *ngIf="projectInfo.custom_field.has_rams_in_induction && hasFullAccessOrRestrictedOf()" class="form-group row pl-3">
                        <label class="font-weight-bold" for="include_during_induction">Include in the {{ inductionSglrPhrase }} process:</label>
                        <div class="custom-control custom-checkbox ml-2">
                            <input type="checkbox" class="custom-control-input"
                                   [checked]="include_during_induction"
                                   style="z-index: 99999;" (click)="includeDuringInduction($event)"
                                   name="include_during_induction"
                                   id="include_during_induction" autocomplete='off'>
                            <label class="custom-control-label"></label>
                        </div>
                    </div>
                </div>
            </ng-container>
        </form>
    <block-loader [show]="(addingRamsInProgress)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
</i-modal>

<i-modal #viewFileRef title="Preview" [showCancel]="false" rightPrimaryBtnTxt="Done" (onClickRightPB)="closeModal($event)" size="lg" [windowClass]="'xl-modal'">
    <div *ngIf="showModal" class="scroll-wrapper">
        <iframe [src]="filePreviewUrl" style="width: 100%;height: 100%;position: absolute;">
        </iframe>
    </div>
</i-modal>
<block-loader [show]="(blockLoader || loadingPackagePlans)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>

<i-modal #ramsActionRef [title]="'Review ' + ramsSglrPhrase" [showCancel]="false" (onCancel)="reviewRamsClose()" [confirmBeforeClose]="true" [showFooter]="false" size="lg" [windowClass]="'xl-modal modalHeightAuto'">
    <div class="col-sm-12 my-1">
            <ul ngbNav #nav="ngbNav" [(activeId)]="activeNav" class="nav-tabs n-tab" style="border-bottom: none;" (activeIdChange)="tabChange($event)">
                <li [ngbNavItem]="1">
                    <a ngbNavLink class="nav-a" style="background-color: transparent; border-color: transparent;">{{ ramsSglrPhrase }}</a>
                    <ng-template ngbNavContent>
                        <div class="col-md-6 d-inline-block pl-0 pdf-viewer assessment-body-height">
                            <ng2-pdfjs-viewer
                                *ngIf="showPdfViewer"
                                [pdfSrc]="briefing_row?.briefing_file_ref?.file_url || ''"
                                [print]="false"
                                [page]="1"
                                [openFile]="false"
                                [viewBookmark]="false"
                                viewerId="viewBriefingFile"
                            >
                            </ng2-pdfjs-viewer>
                        </div>

                        <assessment-form
                            [projectInfo]="projectInfo"
                            [briefing_row]="briefing_row"
                            [authUser]="authUser$"
                            [dayjs]="dayjs"
                            [employment_detail]="employment_detail"
                            [ramsAssessmentFormFieldsDefault]="ramsAssessmentFormFieldsDefault"
                            [ramsAssessmentFormFields]="ramsAssessmentFormFields"
                            [rams_assessment_file]="rams_assessment_file"
                            [cb]="c"
                            (reloadRamsTable)="initializeTable(true)"
                        ></assessment-form>
                    </ng-template>
                </li>

                <li [ngbNavItem]="2">
                    <a ngbNavLink class="nav-a" style="background-color: transparent; border-color: transparent;">Assessment</a>
                    <ng-template ngbNavContent>
                        <div class="col-md-6 d-inline-block pl-0 pdf-viewer assessment-body-height">
                            <ng2-pdfjs-viewer
                                *ngIf="showPdfViewer"
                                [pdfSrc]="rams_assessment_file?.file_url || ''"
                                [print]="false"
                                [page]="1"
                                [openFile]="false"
                                [viewBookmark]="false"
                                viewerId="viewAssessmentForm"
                            >
                            </ng2-pdfjs-viewer>
                        </div>

                        <assessment-form
                            [projectInfo]="projectInfo"
                            [briefing_row]="briefing_row"
                            [authUser]="authUser$"
                            [dayjs]="dayjs"
                            [employment_detail]="employment_detail"
                            [ramsAssessmentFormFieldsDefault]="ramsAssessmentFormFieldsDefault"
                            [ramsAssessmentFormFields]="ramsAssessmentFormFields"
                            [rams_assessment_file]="rams_assessment_file"
                            [cb]="c"
                            (reloadRamsTable)="initializeTable(true)"
                        ></assessment-form>
                    </ng-template>
                </li>
            </ul>
            <div [ngbNavOutlet]="nav" class="col-sm-12 my-2 pt-2 nav-panel custom-tab-radius" style="border: none;"></div>
        </div>
</i-modal>
<block-loader [show]="processingRamsDownload" [showBlockBackdrop]="true" [showBackdrop]="true" alwaysInCenter="true"></block-loader>

<i-modal #ramsInviteModalRef title="Invite to Brief" rightPrimaryBtnTxt="Send Invite" (onClickRightPB)="inviteUsers(inviteRamsForm)" cancelBtnText="Close" size="md"
    [rightPrimaryBtnDisabled]="!inviteRamsForm.valid || !hasSelectedUsers" (onCancel)="closeInviteModal(inviteRamsForm)">
        <form novalidate #inviteRamsForm="ngForm">
            <tool-invite-modal 
                [hasEmployerFilter]="true"
                [toolSingularPhrase]="ramsPhrase"
                [isFilteredUsersReset]="isFilteredUsersReset"
                [availableRecords]="availableRecords"
                [jobRoles]="jobRoles"
                [availableEmployers]="availableEmployers"
                [projectUsersData]="projectUsers"
                [filteredUsersData]="filteredUsers"
                (onSelectDeselectUser)="checkHasSelectedUsers($event)"
            ></tool-invite-modal>
        </form>
        <div>
            <div *ngIf="inviteApiError" class="alert alert-danger">
                {{ inviteApiError }}
            </div>
            <div *ngIf="inviteApiSuccess" class="alert alert-success">
                {{ inviteApiSuccess }}
            </div>
        </div>
    <block-loader [show]="processingRamsInvites"></block-loader>
</i-modal>

<i-modal #archivedRamsListRef title="Archive" [showCancel]="false" (onCancel)="closeModal($event)" rightPrimaryBtnTxt="Done" (onClickRightPB)="closeModal($event)" size="lg" [windowClass]="'modalHeightAuto modal_v2 xl-modal'">
    <div class="table-responsive-sm pl-3 pr-3">
        <search-with-filters class="mt-2 d-block" #archivedRamsFilters [filterData]="archivedRamsFilter" (searchEmitter)="searchArchivedRams($event)" (filterEmitter)="onFilterArchivedRams($event)"></search-with-filters>

        <div *ngIf="showArchivedRamsTable">
            <ngx-datatable #table class="bootstrap table min-h-250 table-hover table-sm  archive-table"
                       [rows]="archivedRamsRecords"
                       [footerHeight]="40"
                       [columnMode]="'force'"
                       [rowHeight]="'auto'"
                       [externalPaging]="true"
                       [count]="archivedPage.totalElements"
                       [offset]="archivedPage.pageNumber"
                       [limit]="archivedPage.size"
                       (page)="archivedRamsPageCallback($event)"
                       [externalSorting]="false"
        >

            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false">
                <ng-template let-column="column" ngx-datatable-header-template>
                    {{ ramsSglrPhrase }} #
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    {{row.record_id}}
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Title
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    <span (click)="ramsDetailModal(row)" class="text-underline cursor-pointer">{{row.briefing_title}}</span>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Company
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    {{ row?.tagged_owner?.name}}
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Uploaded By
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    <div style="display: inline-grid;">
                        <span>{{row.user_ref.first_name}} {{row.user_ref?.middle_name || ''}} {{row.user_ref.last_name}}</span>
                        <span class="small">({{ dayjs(+row?.createdAt).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) }})</span>
                    </div>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column headerClass="font-weight-bold action-column" cellClass="action-column" [sortable]="false">
                <ng-template let-column="column" ngx-datatable-header-template>
                    Action
                </ng-template>
                <ng-template let-row="row" ngx-datatable-cell-template>
                    <a title="Unarchive" class="text-info" href="javascript:void(0)" style="font-weight: 600;"
                            (click)="openConfirmationModal(row)">
                        Unarchive</a>
                </ng-template>
            </ngx-datatable-column>
        </ngx-datatable>
        </div>
    </div>
</i-modal>

<i-modal #ramsDownloadModel [title]="'Download '+briefing_title" (onCancel)="closeDownloadRamsModal()" [rightPrimaryBtnDisabled]="!ramsDownloadForm.valid" (onClickRightPB)="downloadRams($event)" rightPrimaryBtnTxt="Download" size="md">
    <form novalidate #ramsDownloadForm="ngForm">
        <div class="form-group">
            <label [ngClass]="{'d-none': ramsRevisions.length === 1}">Revision Number: <small class="required-asterisk ">*</small></label>
            <div class="input-group mb-3">
                <ng-select class="w-100 dropdown-list" [ngClass]="{'w-100 dropdown-list': true, 'd-none': ramsRevisions.length === 1}" appendTo="body" [items]="ramsRevisions" bindLabel="revision_number" bindValue="id"
                           name="rams_revision" placeholder="Select Revision" [(ngModel)]="ramsDownload.selected_revision" required>
                    <ng-template ng-label-tmp let-item="item">
                        <span>{{ item.revision_number}}</span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                        <span>{{ item.revision_number}}</span>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="form-group">
            <label>Type: <small class="required-asterisk ">*</small></label>
            <div class="input-group mb-3">
                <ng-select class="w-100 dropdown-list" appendTo="body" name="download_type" placeholder="Select Type" [(ngModel)]="ramsDownload.download_type" required>
                    <ng-option value="pdf">PDF</ng-option>
                    <ng-option value="xlsx">XLSX</ng-option>
                </ng-select>
            </div>
        </div>
    </form>
</i-modal>

<generic-confirmation-modal #confirmationModalReference></generic-confirmation-modal>
<report-downloader #reportDownloader 
    [xlsxOnly]="true" 
    (onFilterSelection)="RamsReportDownload($event)"
    >
</report-downloader>

import {ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, OnInit, TemplateRef, ViewChild} from "@angular/core";
import {
    GoodCallService,
    AuthService,
    ProjectService,
    User,
    ExcelUtility,
    Project,
    isInheritedProjectOfCompany,
    UserService,
    Common,
    GoodCall,
    ResourceService,
    HttpService,
    GoodCallOrObservationConfModalType,
    GoodCallObservationActionButtons,
    ProjectFilter,
    RaisedBy,
    AssignedTo,
    CategoryList,
    ToastService,
} from "@app/core";
import {NgSelectComponent} from "@ng-select/ng-select";
import {ActivatedRoute, Router} from "@angular/router";
import { HttpParams } from '@angular/common/http';
import * as dayjs from 'dayjs';
import {NgbDate, NgbDateStruct, NgbModal, NgbModalConfig} from "@ng-bootstrap/ng-bootstrap";
import {Observable, forkJoin, of} from "rxjs";
import {catchError, map} from "rxjs/operators";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import * as ExcelProper from "exceljs";
import * as fs from "file-saver";
import * as Excel from "exceljs/dist/exceljs.min.js";
import {DatatableComponent} from "@swimlane/ngx-datatable";
import {DomSanitizer} from '@angular/platform-browser';
import {AppConstant} from "@env/environment";
import { AssetsUrl } from "@app/shared/assets-urls/assets-urls";
import { ProgressPhotosGalleryComponent } from "../progress-photos/gallery/progress-photos-gallery.component";
import { GenericModalComponent } from "../generic-modal/generic-modal.component";
import { GenericModalConfig } from "../generic-modal/generic-modal.config";
import { filterData } from "@app/core";
import { ActionBtnEntry, GenericConfirmationModalComponent } from "@app/shared";
import { ReportDownloaderComponent } from "../report-downloader/report-downloader.component";

@Component({
    templateUrl: './good-call.component.html',
    providers: [NgbModalConfig, NgbModal],
})
export class GoodCallComponent implements OnInit {

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    @ViewChild('reportDownloader') private reportDownloader: ReportDownloaderComponent;
    employerId: number = 0;
    project: Observable<{}>;
    records: Array<any> = [];
    newPhotos: Array<any> = [];
    categoryList: Array<any> = [];
    regUserCompanyId: number;
    observationOrGoodCallObj: GoodCall;  // used for new observation and new good-call
    temp_records: Array<any> = [];
    authUser$: User;
    employer: any = {};
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;
    exportInProgress: boolean = false;
    allowedMime : Array<any> = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    blobType: string = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    addImagesToWorkbook = this.excelUtility.addImagesToWorkbook;
    good_call_row: GoodCall = new GoodCall;
    is_inherited_project: boolean = false;
    selectedHazardType: [] = [];
    selectedRaisedBy: RaisedBy[] = [];
    selectedAssignedTo: AssignedTo[] = [];
    ownerFilter: number[] = [];
    search: string = null;
    employersList = [];
    downloadHazardType: string = null;
    filterEmployer: string = null;
    tagged_owners: any = [];
    filterByOwner: number;
    inductedUsersEmployer: any = [];
    fetchCategoryList: CategoryList[] = [];
    observation_status: any = [{status: 1, status_message: 'Closeout required'}, {status: 3, status_message: 'No Action'}];
    observation_all_status: any = [{status: 1, status_message: 'Open'}, {status: 2, status_message: 'Closed'}, {status: 3, status_message: 'No Action'}];
    updateRequestLoader: boolean = false;
    tagOwner: number;
    tagUser: number;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse:any;
    loadingGoodCalls: boolean = false;
    isInitGoodCallObservation = false;
    loadingInlineGoodCallObservation: boolean = false;
    tableOffset: number = 0;
    blockloader: boolean = false;
    isDownload: boolean = false;
    common = new Common();
    page = this.common.page;
    gcPhrase: string = ``;
    gcSglrPhrase: string = ``;
    showMapWithMultiPin = false;
    good_calls_data: any;
    observationPhrase: string = ``;
    observationSglrPhrase: string = ``;
    current_feature: string = "good-call";
    dateFormat: string = AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS;
    observationDwStatus: any = {open:true, closed:true,no_closeout:true};
    statusFilter: any = null;
    selectedStatus: number = 0;
    dashboardStartDate: NgbDate;
    dashboardLoader: boolean = false;
    dashboardHtmlContent: any;
    dashboardReportFrom: any;
    dashboardReportTo: any;
    openRecords: number = 0;

    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    isProjectPortal: boolean = false;
    decimalConfig = AppConstant.decimelConfig;
    isMobileDevice: boolean = false;
    goodCallOrObserveDetailModalRef: any;
    hazardCategoryEditDisable: boolean = true;
    showCloseOutForm: boolean = false;
    AssetsUrlSiteAdmin = AssetsUrl.siteAdmin;
    showProgressPhotosHtmlMap: boolean = false;
    showMap = false
    actionButtonMetaData = {
        isTraining: false,
        actionList: [],
        class: 'material-symbols-outlined',
    };
    loadPowerBiComponent: boolean = false;
    biToolName: string = '';
    biToolLabel: string = '';
    biModalTitle: string = 'Dashboard';
    projectFilters: ProjectFilter = {
        categoryLists: [],
        assignToUsers: [],
        raisedByUsers: [],
        tagged_owners: []
    };
    filterData:filterData[] = this.renderFilterData();
    imagesArray:  Array<string> = [];
    closeOutImageArray:  Array<string> = [];
    hasCloseoutDetail: boolean = false;
    rowButtonGroup: Array<ActionBtnEntry> = [];

    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private projectService: ProjectService,
        private authService: AuthService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private goodCallService: GoodCallService,
        private excelUtility: ExcelUtility,
        private modalService: NgbModal,
        private userService: UserService,
        private domSanitizer: DomSanitizer,
        private resourceService: ResourceService,
        public cdRef: ChangeDetectorRef,
        private httpService: HttpService,
        private toastService: ToastService,
    ) {
        this.from_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.to_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
        }
    }

    ngOnInit() {
        //if loading Observations feature
        if (this.activatedRoute.snapshot.data.toolkey && this.activatedRoute.snapshot.data.toolkey == "observations") {
            this.current_feature = 'observation';
        }

        if(!this.isProjectPortal) {
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
                this.employerId = params['employerId'];
            });
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;

        }
        this.actionButtonMetaData.actionList = [
            {
                code: GoodCallObservationActionButtons.MAP,
                name: `Location Map`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'location_on',
                enabled: true,
            },
            {
                code: GoodCallObservationActionButtons.DASHBOARD,
                name: `View Dashboard`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'dashboard',
                enabled: true,
            },
            {
                code: GoodCallObservationActionButtons.DOWNLOAD_REPORT,
                name: `Download Report`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: true,
            },
        ];
        this.loadProjectSettingsAndInductions();
        this.getProjectUtils();
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
                this.regUserCompanyId = data.parent_company.id;
            }
        });

        this.biToolName = (this.current_feature === 'good-call') ? 'good_call' :  'observation';
        this.biToolLabel = (this.current_feature === 'good-call') ? this.gcPhrase : this.observationPhrase;
    }

    loadProjectSettingsAndInductions() {
        this.dashboardLoader = true;
        let settingName = (this.current_feature === 'good-call') ? 'good_call_category_en_gb' : 'observation_category_en_gb';
        let projectParam = (settingName === 'observation_category_en_gb') ? this.projectId : 0;

        forkJoin({
            projectSettings: this.resourceService.getInnDexSettingByName(settingName, projectParam).pipe(
                catchError(error => {
                    console.error('Failed to fetch project settings', error);
                    return [];
                })
            ),
            inductedUsersEmployer: this.userService.getProjectInductionsUsersEmployer(this.projectId, 2).pipe(
                catchError(error => {
                    console.error('Failed to fetch inducted users', error);
                    return [];
                })
            ),
            goodCallsFunction:  this.isProjectPortal ? this.initializeTable(false, true) : [] 
        }).subscribe(
            ({ projectSettings, inductedUsersEmployer}) => {
                if(projectSettings && projectSettings.success){
                    this.fetchCategoryList = projectSettings.record.value;
                    this.filterData = this.renderFilterData();
                } else {
                    const message = projectSettings.message || 'Failed to get category';
                    this.toastService.show(this.toastService.types.ERROR, message);
                    return;
                }
                if(inductedUsersEmployer.success && inductedUsersEmployer.users_employer){
                    this.inductedUsersEmployer = (inductedUsersEmployer.users_employer || []).sort((a, b) => a.name.localeCompare(b.name));
                    this.filterData = this.renderFilterData();
                } else {
                    const message = inductedUsersEmployer.message || 'Failed to get companies of the users who have had an approved induction on the project.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                }
                this.dashboardLoader = false;
            },
            error => {
                console.error('Error fetching project data', error);
                this.dashboardLoader = false;
            }
        );
    }

    /**
     * Populate the table with new data based on the page number
     * @param page The page to select
     */
    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }) {
        this.page.pageNumber = pageInfo.offset;
        if (!this.isInitGoodCallObservation) {
            this.isInitGoodCallObservation = true;
            return;
        }
        this.initializeTable(true);
    }

    async initiateDownload(resp) {
        this.blockloader = true;
        this.from_date = resp.fromDate;
        this.to_date = resp.toDate;
        await this.downloadGCReport();
    }

    // Will get called for Company route only.
    projectRetrieved(project: Project){
        this.projectInfo = project;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
        this.initializeTable();
    }

    private initializeTable(isPageChange = false, returnObservable: boolean = false): Observable<any> | void {
        if (!isPageChange) {
            this.loadingGoodCalls = true;
        } else {
          this.loadingInlineGoodCallObservation = true;
        }
        this.gcPhrase = this.projectInfo ? this.projectInfo.custom_field.gc_phrase : '';
        this.gcSglrPhrase = this.projectInfo ? this.projectInfo.custom_field.gc_phrase_singlr : '';
        this.observationPhrase = this.projectInfo ? this.projectInfo.custom_field.obrs_phrase : '';
        this.observationSglrPhrase = this.projectInfo ? this.projectInfo.custom_field.obrs_phrase_singlr : '';
        this.from_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(this.projectInfo.createdAt));
        this.buildRowButtonGroup();
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('is_inherited_project', `${this.is_inherited_project}`)
            .set('data_type', `${this.current_feature}`);
        if(this.search !== null && this.search !==''){
            params = params.append('search', encodeURIComponent(this.search));
        }
        if(this.selectedHazardType !== null && this.selectedHazardType.length !== 0){
            params = params.append('category', encodeURIComponent(this.selectedHazardType.join(",")));
        }
        if(this.statusFilter !== null && this.statusFilter !==''){
            params = params.append('status', `${this.statusFilter}`);
        }
        if (this.selectedAssignedTo && this.selectedAssignedTo.length != 0) {
            params = params.append('assigned_to', `${this.selectedAssignedTo}`);
        }
        if (this.selectedRaisedBy && this.selectedRaisedBy.length != 0) {
            params = params.append('raised_by', `${this.selectedRaisedBy}`);
        }
        if (this.ownerFilter && this.ownerFilter.length != 0) {
            params = params.append('tagged_owner', `${this.ownerFilter}`);
        }
        let companyId = (this.employerId ? this.employerId : null);
        const observable = this.goodCallService.getProjectGoodCalls(this.projectId, companyId, params).pipe(
            map((data: any) => {
                this.loadingGoodCalls = false;
                this.loadingInlineGoodCallObservation = false;
                if (data && data.good_calls) {
                    this.records = data.good_calls;
                    this.temp_records = data.good_calls;
                    this.openRecords = data.total_open_count;
                    this.good_calls_data = data.good_calls;
                    this.page.totalElements = data.total_record_count;
                    this.tagged_owners = data.tagged_owners
                } else {
                    const message = `Failed to fetch good calls, id: ${this.projectId}`;
                    this.toastService.show(this.toastService.types.ERROR, message, {data: data});
                }
            }),
            catchError(error => {
                console.error('Error fetching good calls:', error);
                return of(null);
            })
        );
        if (returnObservable) {
            return observable; 
        } else {
            observable.subscribe();
        }
    }

    buildRowButtonGroup(): void {
        const buttonConfigs = [
            {
                key: 'view',
                label: '',
                title: `View ${this.current_feature === 'good-call' ? this.gcSglrPhrase : this.observationSglrPhrase}`,
                mat_icon: 'search',
            },
            {
                key: 'download_pdf',
                label: '',
                title: 'Download PDF',
                mat_icon: 'download',
            },
            {
                key: 'close_out',
                label: '',
                title: 'Close Out',
                mat_icon: 'cancel',
            }
        ];

        this.rowButtonGroup = buttonConfigs;
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actionMap = {
            'view': () => this.goodCallDetailModal(row),
            'download_pdf': () => this.downloadGoodCall(row),
            'close_out': () => this.closeOutModal(row)
        };
        const action = actionMap[entry.key];
        if (action) {
            action();
        }
    }

    getProjectUtils(){
        this.dashboardLoader = true;
        this.goodCallService.fetchGoodCallsForProject(this.projectId, this.current_feature).subscribe((data) => {
            this.employersList = [...new Set(data.employer)];
            this.tagged_owners = data.tagged_owners;
            this.projectFilters = {
                categoryLists: data.hazard_types,
                assignToUsers: data.assigned_to,
                raisedByUsers: data.raised_by,
                tagged_owners: data.tagged_owners
            };
            this.filterData = this.renderFilterData();
        }).add(() => {
            this.dashboardLoader = false;
        });
    }

    ngAfterViewInit() {

    }
    @ViewChild(DatatableComponent) table: DatatableComponent;

    updateFilter(event) {

        const val = event.target.value;
        this.search = val;
        this.page.pageNumber = 0;
        this.initializeTable();
    }

    initEmployers() {
        let emp = ['Anonymous'];
        this.temp_records.filter(t => {
            if(!t.is_anonymous && t.user_employer && t.user_employer.employer) {
                emp.push(t.user_employer.employer);
            }
        });
        this.employersList = [...new Set(emp)];
    }

    resetFilter() {
        this.records = this.temp_records;
        this.tableOffset = 0;
    }

    filterRecords() {
        this.initializeTable();
    }

    dayjs(n: number) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };

    dayjsFullDateTime(n: number) {
        return this.dayjs(n).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS);
    };
    dayjsDisplayDateOrTime(n: number, onlyDate = true) {
        return this.dayjs(n).format(onlyDate ? AppConstant.defaultDateFormat : AppConstant.defaultTimeFormat);
    };
    displaySelectedDay(n: number){
        return this.dayjs(n).format(AppConstant.dateFormat_Do_MMM_YYYY);
    }

    async downloadGCReport() {
        this.exportInProgress = true;
        this.isDownload = true;
        const params = new HttpParams()
            .set('download', `${this.isDownload}`)
            .set('data_type', `${this.current_feature}`);

        let companyId = (this.employerId ? this.employerId : null);
        await this.goodCallService.getProjectGoodCalls(this.projectId, companyId, params).subscribe((data: any) => {
            this.loadingGoodCalls = false;
            if (data && data.good_calls) {
                this.temp_records =  data.good_calls
                this.downloadInit();
                return;

            }
            const message = `Failed to fetch good calls, id: ${this.projectId}`;
            this.toastService.show(this.toastService.types.ERROR, message, {data: data});
            return;
        });

    }
    async downloadInit(){
        let featureType = this.current_feature;
        let tempRecords  = this.temp_records;
        if(this.downloadHazardType) {
            tempRecords = tempRecords.filter(t=> t.hazard_category === this.downloadHazardType);
        }
        if(this.filterEmployer) {
            if(this.filterEmployer === 'Anonymous') {
                tempRecords = tempRecords.filter(t => t.is_anonymous);
            } else {
                tempRecords = tempRecords.filter(t=> !t.is_anonymous && t.user_employer && t.user_employer.employer === this.filterEmployer);
            }
        }

        if(this.filterByOwner) {
            tempRecords = tempRecords.filter(t => (t.tagged_owner && t.tagged_owner.id == this.filterByOwner));
        }

        const fromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.from_date).startOf('day').valueOf();
        const toDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.to_date).endOf('day').valueOf();
        let workbook: ExcelProper.Workbook = new Excel.Workbook();
        let worksheet = workbook.addWorksheet('Timesheet', {
            views: [{zoomScale: 60}],
            pageSetup:{paperSize: 5, orientation:'landscape', scale: 86}
        });
        let col1 = this.gcSglrPhrase +" #";
        if (featureType == 'observation') {
            col1 = this.observationSglrPhrase +" #";
        }
        let alignment: ExcelProper.Alignment = {
            vertical: 'middle',
            horizontal: 'center',
            wrapText: true
        } as ExcelProper.Alignment;
        let imageHeaderStyle = {style: { alignment, }};

        //Include Owner column
        if(this.isProjectPortal)     {
            let columns = [
                { header: col1, key: col1, width:20, ...imageHeaderStyle},
                { header: "Date/Time", key: "Date/Time", width:20, ...imageHeaderStyle},
                { header: "Project", key: "Project", width:20, ...imageHeaderStyle},
                { header: "Raised By", key: "Raised By", width:20, ...imageHeaderStyle},
                { header: "Employer", key: "Employer", width:20, ...imageHeaderStyle},
                { header: "Category", key: "Category", width:20, ...imageHeaderStyle},
                { header: "Details", key: "Details", width:20, ...imageHeaderStyle},
                { header: "Location", key: "Location", width:20, ...imageHeaderStyle},
                { header: "Location Tag (Lat, Long)", key: "Location Tag (Lat, Long)", width:20, ...imageHeaderStyle},
                { header: "Further Actions", key: "Further Actions", width:20, ...imageHeaderStyle},
                { header: "Owner", key: "Owner", width:20, ...imageHeaderStyle},
                { header: "Photos", key: "Photos", width:20, ...imageHeaderStyle},
                { header: "Photos2", key: "Photos2", width:20, ...imageHeaderStyle},
                { header: "Photos3", key: "Photos3", width:20, ...imageHeaderStyle},
            ];
            if (featureType != 'good-call') {
                columns = [
                    { header: col1, key: col1, width:20, ...imageHeaderStyle},
                    { header: "Date/Time", key: "Date/Time", width:20, ...imageHeaderStyle},
                    { header: "Project", key: "Project", width:20, ...imageHeaderStyle},
                    { header: "Raised By", key: "Raised By", width:20, ...imageHeaderStyle},
                    { header: "Employer", key: "Employer", width:20, ...imageHeaderStyle},
                    { header: "Category", key: "Category", width:20, ...imageHeaderStyle},
                    { header: "Details", key: "Details", width:20, ...imageHeaderStyle},
                    { header: "Location", key: "Location", width:20, ...imageHeaderStyle},
                    { header: "Location Tag (Lat, Long)", key: "Location Tag (Lat, Long)", width:20, ...imageHeaderStyle},
                    { header: "Owner", key: "Owner", width:20, ...imageHeaderStyle},
                    { header: "Assigned To", key: "Assigned To", width:37.6, ...imageHeaderStyle},
                    { header: "Photos", key: "Photos", width:20, ...imageHeaderStyle},
                    { header: "Photos2", key: "Photos2", width:20, ...imageHeaderStyle},
                    { header: "Photos3", key: "Photos3", width:20, ...imageHeaderStyle},
                    { header: "Status", key: "Status", width:31.5, ...imageHeaderStyle},
                    { header: "Close Out Description", key: "Close Out Description", width:36, ...imageHeaderStyle},
                    { header: "Close Out Photos", key: "Close Out Photos", width:20, ...imageHeaderStyle},
                    { header: "Close Out Photos2", key: "Close Out Photos2", width:20, ...imageHeaderStyle},
                    { header: "Close Out Photos3", key: "Close Out Photos3", width:20, ...imageHeaderStyle},
                    { header: "Closed Out By", key: "Closed out by", width:36, ...imageHeaderStyle},
                    { header: "Close Out Date/Time", key: "Close out Date", width:31.5, ...imageHeaderStyle},
                ];
            }
            worksheet.columns = columns;

            let _self = this;
            let filteredRecords = tempRecords.filter(function(r) {
                let statusCheck = (featureType == 'good-call') ? true : _self.isStatusMatched(r.status);
                if(+r.createdAt >= fromDate && +r.createdAt <= toDate && statusCheck) {
                    return true;
                }
                return false;
            });

            let imagesWorkspaceId = [];
            let closedOutImagesWorkspaceId = [];
            for (let i = 0, len = filteredRecords.length; i < len; i++){
                // Add All images into workbook, and save their IDs
                imagesWorkspaceId.push(...(await this.addImagesToWorkbook(workbook, filteredRecords[i].record_id, filteredRecords[i].images, 'rowId')));

                if (filteredRecords[i].closeout_detail && filteredRecords[i].closeout_detail.images && filteredRecords[i].closeout_detail.images.length) {
                    closedOutImagesWorkspaceId.push(...(await this.addImagesToWorkbook(workbook, filteredRecords[i].record_id, filteredRecords[i].closeout_detail.images, 'rowId')));
                }
            }

            let records = filteredRecords.map(r => {
                let userEmployer = 'Anonymous';
                if (!r.is_anonymous) {
                    if (r.user_employer) {
                        userEmployer = r.user_employer.employer;
                    }
                }

                let userName = 'Anonymous';
                if (!r.is_anonymous) {
                    userName = r.user_ref.name;
                }

                if (featureType == 'good-call') {
                    return {
                        [col1]: r.record_id,
                        "Date/Time": r.createdAt ? this.dayjs(+r.createdAt).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) : null,
                        "Project": (r.project_ref && r.project_ref.project_number !=null ? r.project_ref.project_number +' - ' + r.project_ref.name: r.project_ref.name || ''),
                        "Raised By": userName,
                        "Employer": userEmployer,
                        "Category": r.hazard_category,
                        "Details": r.details,
                        "Location": (r.location && r.location.description) ? r.location.description : "",
                        "Location Tag (Lat, Long)": (r.location && r.location.lat && r.location.long) ? `${r.location.lat}, ${r.location.long}` : "",
                        "Further Actions": r.further_actions,
                        "Owner": (r.tagged_owner && r.tagged_owner.name) ? r.tagged_owner.name : 'N/A',
                        "Photo": null,
                        "Photo2": null,
                        "Photo3": null,
                    };
                }

                return {
                    [col1]: r.record_id,
                    "Date/Time": r.createdAt ? this.dayjs(+r.createdAt).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) : null,
                    "Project": (r.project_ref && r.project_ref.project_number !=null ? r.project_ref.project_number +' - ' + r.project_ref.name: r.project_ref.name || ''),
                    "Raised By": userName,
                    "Employer": userEmployer,
                    "Category": r.hazard_category,
                    "Details": r.details,
                    "Location": (r.location && r.location.description) ? r.location.description : "",
                    "Location Tag (Lat, Long)": (r.location && r.location.lat && r.location.long) ? `${r.location.lat}, ${r.location.long}` : "",
                    "Assigned To": (r.assigned_to && r.assigned_to.id) ? `${r.assigned_to.first_name} ${r.assigned_to.last_name}` : '',
                    "Owner": (r.tagged_owner && r.tagged_owner.name) ? r.tagged_owner.name : 'N/A',
                    "Photo": null,
                    "Photo2": null,
                    "Photo3": null,
                    "Status": r.status_message,
                    "Close Out Description": (r.closeout_detail && r.closeout_detail.description) ? r.closeout_detail.description : null,
                    "Close Out Photos": null,
                    "Close Out Photos2": null,
                    "Close Out Photos3": null,
                    "Closed out by": (r.closeout_detail && r.closeout_detail.closeout_by) ? r.closeout_detail.closeout_by : null,
                    "Close out Date": (r.closeout_detail && r.closeout_detail.closeout_at) ? this.dayjs(+r.closeout_detail.closeout_at).format(this.dateFormat) : null,
                };
            });

            worksheet.addRows(records);
            console.log('Merging heading column');
            if (featureType == 'good-call') {
                worksheet.mergeCells(`L1:N1`); // merging cells of attached images
            } else {
                worksheet.mergeCells(`L1:N1`); // merging cells of attached images
                worksheet.mergeCells(`Q1:S1`); // merging cells of attached closeout images
            }

            let lastRowIndex = 0;
            let increaseRowHeightIfNeeded = this.excelUtility.increaseRowHeightIfNeeded;
            worksheet.eachRow(function(row, rowNumber) {
                lastRowIndex++;
                let actualRow = worksheet.getRow(lastRowIndex);

                // Styling first ROW of Heading
                if (rowNumber === 1) {
                    row.height = 20;
                    // Iterate over all (including empty) cells in a row
                    actualRow.eachCell({ includeEmpty: true }, function(cell, colNumber) {
                        cell.font = {
                            bold: true,
                        };
                        cell.fill = {
                            type: 'pattern',
                            pattern:'solid',
                            fgColor: {argb: 'dedede'}
                        }
                        cell.border = {
                            top: {style:'thin'},
                            left: {style:'thin'},
                            bottom: {style:'thin'},
                            right: {style:'thin'}
                        };
                    });
                } else {
                    if (featureType != 'good-call') {
                        actualRow.eachCell({includeEmpty: true}, function (cell, colNumber) {
                            let statusMessage = cell.value;
                            if (colNumber === 15 && statusMessage) {
                                cell.border = {
                                    top: {style:'thin'},
                                    left: {style:'thin'},
                                    bottom: {style:'thin'},
                                    right: {style:'thin'}
                                };

                                cell.font = {
                                    color: {argb: "FFFFFF"}
                                };

                                cell.fill = {
                                    type: 'pattern',
                                    pattern: 'solid',
                                    fgColor: {argb: (statusMessage == "No Action") ? '0e0e0e' : (statusMessage == "Open") ? 'dc3545' : '28a745'}
                                };
                            }
                        });
                    }
                }
                if(rowNumber <= 1){
                    return;
                }
                console.log('Processing old row #', rowNumber, 'last row index was', lastRowIndex);

                let maxHeightNeeded = 0;
                let mergeStartAt = lastRowIndex;
                let mergeEndAt = lastRowIndex;
                let imgs = (imagesWorkspaceId || []).filter(i => i.rowId == actualRow.getCell(col1).value) || [];
                imgs.map(async (img, loopIndex) => {
                    console.log('Adding image in row', lastRowIndex);
                    let tlCol = (featureType == 'good-call') ? 11.10 + (loopIndex % 3) : 11.10 + (loopIndex % 3);
                    let tlRow = (lastRowIndex-1) + 0.1;
                    let tl = { col: tlCol, row: tlRow };
                    worksheet.addImage(img.id, {
                        tl: tl,
                        ext: img.ext,
                    });
                    maxHeightNeeded = increaseRowHeightIfNeeded(worksheet.getRow(lastRowIndex), img.ext.height, maxHeightNeeded);
                    // Just before it need, add an empty row
                    if(loopIndex > 0 && (loopIndex % 3) === 2 && loopIndex < (imgs.length-1)){
                        console.log('Adding empty row');
                        worksheet.spliceRows(lastRowIndex+1, 0, []);
                        mergeEndAt++;
                        lastRowIndex++;
                    }
                });

                if (featureType != 'good-call') {
                    let lastClosedOutRowIndex = mergeStartAt;
                    console.log('Closed out will start from ', lastClosedOutRowIndex, 'while current index is', lastRowIndex);
                    let img_of_closed_out = (closedOutImagesWorkspaceId || []).filter(i => i.rowId == actualRow.getCell(col1).value) || [];
                    img_of_closed_out.map((img, loopIndex) => {
                        console.log('Adding closed out image in row', lastClosedOutRowIndex);
                        let tlCol = 16.10 + (loopIndex % 3);
                        let tlRow = (lastClosedOutRowIndex - 1) + 0.1;
                        let tl = {col: tlCol, row: tlRow};
                        worksheet.addImage(img.id, {
                            tl: tl,
                            ext: img.ext,
                        });
                        maxHeightNeeded = increaseRowHeightIfNeeded(worksheet.getRow(lastClosedOutRowIndex), img.ext.height, maxHeightNeeded);
                        // Just before it need, add an empty row
                        if (loopIndex > 0 && (loopIndex % 3) === 2 && loopIndex < (img_of_closed_out.length - 1)) {
                            if (lastClosedOutRowIndex >= lastRowIndex) {
                                console.log('Adding empty row for closed out');
                                worksheet.spliceRows(lastClosedOutRowIndex + 1, 0, []);
                                mergeEndAt++;
                                lastRowIndex++;
                            }
                            lastClosedOutRowIndex++;
                        }
                    });
                }

                // Merge if extra rows were added above
                if(mergeEndAt > mergeStartAt){
                    console.log(`Merging cells vertically ${mergeStartAt} to ${mergeEndAt}`);
                    worksheet.mergeCells(`A${mergeStartAt}:A${mergeEndAt}`);
                    worksheet.mergeCells(`B${mergeStartAt}:B${mergeEndAt}`);
                    worksheet.mergeCells(`C${mergeStartAt}:C${mergeEndAt}`);
                    worksheet.mergeCells(`D${mergeStartAt}:D${mergeEndAt}`);
                    worksheet.mergeCells(`E${mergeStartAt}:E${mergeEndAt}`);
                    worksheet.mergeCells(`F${mergeStartAt}:F${mergeEndAt}`);
                    worksheet.mergeCells(`G${mergeStartAt}:G${mergeEndAt}`);
                    worksheet.mergeCells(`H${mergeStartAt}:H${mergeEndAt}`);
                    worksheet.mergeCells(`I${mergeStartAt}:I${mergeEndAt}`);
                    worksheet.mergeCells(`J${mergeStartAt}:J${mergeEndAt}`);
                    worksheet.mergeCells(`K${mergeStartAt}:K${mergeEndAt}`);
                    if (featureType != 'good-call') {
                        worksheet.mergeCells(`O${mergeStartAt}:O${mergeEndAt}`);
                        worksheet.mergeCells(`P${mergeStartAt}:P${mergeEndAt}`);
                        worksheet.mergeCells(`T${mergeStartAt}:T${mergeEndAt}`);
                        worksheet.mergeCells(`U${mergeStartAt}:U${mergeEndAt}`);
                    }
                }
                console.log('Last row index is', lastRowIndex);
            });
        } else {
            let columns = [
                { header: col1, key: col1, width:20, ...imageHeaderStyle},
                { header: "Date/Time", key: "Date/Time", width:20, ...imageHeaderStyle},
                { header: "Project", key: "Project", width:20, ...imageHeaderStyle},
                { header: "Raised By", key: "Raised By", width:20, ...imageHeaderStyle},
                { header: "Employer", key: "Employer", width:20, ...imageHeaderStyle},
                { header: "Category", key: "Category", width:20, ...imageHeaderStyle},
                { header: "Details", key: "Details", width:20, ...imageHeaderStyle},
                { header: "Location", key: "Location", width:20, ...imageHeaderStyle},
                { header: "Location Tag (Lat, Long)", key: "Location Tag (Lat, Long)", width:30, ...imageHeaderStyle},
                { header: "Further Actions", key: "Further Actions", width:20, ...imageHeaderStyle},
                { header: "Photos", key: "Photos", width:20, ...imageHeaderStyle},
                { header: "Photos2", key: "Photos2", width:20, ...imageHeaderStyle},
                { header: "Photos3", key: "Photos3", width:20, ...imageHeaderStyle},
            ];
            if (featureType != 'good-call') {
                columns = [
                    { header: col1, key: col1, width:20, ...imageHeaderStyle},
                    { header: "Date/Time", key: "Date/Time", width:20, ...imageHeaderStyle},
                    { header: "Project", key: "Project", width:20, ...imageHeaderStyle},
                    { header: "Raised By", key: "Raised By", width:20, ...imageHeaderStyle},
                    { header: "Employer", key: "Employer", width:20, ...imageHeaderStyle},
                    { header: "Category", key: "Category", width:20, ...imageHeaderStyle},
                    { header: "Details", key: "Details", width:20, ...imageHeaderStyle},
                    { header: "Location", key: "Location", width:20, ...imageHeaderStyle},
                    { header: "Location Tag (Lat, Long)", key: "Location Tag (Lat, Long)", width:30, ...imageHeaderStyle},
                    { header: "Assigned To", key: "Assigned To", width:37.6, ...imageHeaderStyle},
                    { header: "Photos", key: "Photos", width:20, ...imageHeaderStyle},
                    { header: "Photos2", key: "Photos2", width:20, ...imageHeaderStyle},
                    { header: "Photos3", key: "Photos3", width:20, ...imageHeaderStyle},
                    { header: "Status", key: "Status", width:31.5, ...imageHeaderStyle},
                    { header: "Close Out Description", key: "Close Out Description", width:36, ...imageHeaderStyle},
                    { header: "Close Out Photos", key: "Close Out Photos", width:20, ...imageHeaderStyle},
                    { header: "Close Out Photos2", key: "Close Out Photos2", width:20, ...imageHeaderStyle},
                    { header: "Close Out Photos3", key: "Close Out Photos3", width:20, ...imageHeaderStyle},
                    { header: "Closed Out By", key: "Closed out by", width:36, ...imageHeaderStyle},
                    { header: "Close Out Date/Time", key: "Close out Date", width:31.5, ...imageHeaderStyle},
                ];
            }
            worksheet.columns = columns;

            let filteredRecords = tempRecords.filter(function(r) {
                if(+r.createdAt >= fromDate && +r.createdAt <= toDate) {
                    return true;
                }
                return false;
            });

            let imagesWorkspaceId = [];
            let closedOutImagesWorkspaceId = [];
            for (let i = 0, len = filteredRecords.length; i < len; i++) {
                // Add All images into workbook, and save their IDs
                imagesWorkspaceId.push(...(await this.addImagesToWorkbook(workbook, filteredRecords[i].record_id, filteredRecords[i].images, 'rowId')));
                if (filteredRecords[i].closeout_detail && filteredRecords[i].closeout_detail.images) {
                    closedOutImagesWorkspaceId.push(...(await this.addImagesToWorkbook(workbook, filteredRecords[i].record_id, filteredRecords[i].closeout_detail.images, 'rowId')));
                }
            }

            let records = filteredRecords.map(r => {
                let userEmployer = 'Anonymous';
                if (!r.is_anonymous) {
                    if (r.user_employer) {
                        userEmployer = r.user_employer.employer;
                    }
                }

                let userName = 'Anonymous';
                if (!r.is_anonymous) {
                    userName = r.user_ref.name;
                }

                if (featureType == 'good-call') {
                    return {
                        [col1]: r.record_id,
                        "Date/Time": r.createdAt ? this.dayjs(+r.createdAt).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) : null,
                        "Project": (r.project_ref && r.project_ref.project_number !=null ? r.project_ref.project_number +' - ' + r.project_ref.name: r.project_ref.name || ''),
                        "Raised By": userName,
                        "Employer": userEmployer,
                        "Category": r.hazard_category,
                        "Details": r.details,
                        "Location": (r.location && r.location.description) ? r.location.description : "",
                        "Location Tag (Lat, Long)": (r.location && r.location.lat && r.location.long) ? `${r.location.lat}, ${r.location.long}` : "",
                        "Further Actions": r.further_actions,
                        "Photo": null,
                        "Photo2": null,
                        "Photo3": null,
                    };
                }
                return {
                    [col1]: r.record_id,
                    "Date/Time": r.createdAt ? this.dayjs(+r.createdAt).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) : null,
                    "Project": (r.project_ref && r.project_ref.project_number !=null ? r.project_ref.project_number +' - ' + r.project_ref.name: r.project_ref.name || ''),
                    "Raised By": userName,
                    "Employer": userEmployer,
                    "Category": r.hazard_category,
                    "Details": r.details,
                    "Location": (r.location && r.location.description) ? r.location.description : "",
                    "Location Tag (Lat, Long)": (r.location && r.location.lat && r.location.long) ? `${r.location.lat}, ${r.location.long}` : "",
                    "Assigned To": (r.assigned_to && r.assigned_to.id) ? `${r.assigned_to.first_name} ${r.assigned_to.last_name}` : '',
                    "Photo": null,
                    "Photo2": null,
                    "Photo3": null,
                    "Status": r.status_message,
                    "Close Out Description": (r.closeout_detail && r.closeout_detail.description) ? r.closeout_detail.description : null,
                    "Close Out Photos": null,
                    "Close Out Photos2": null,
                    "Close Out Photos3": null,
                    "Closed out by": (r.closeout_detail && r.closeout_detail.closeout_by) ? r.closeout_detail.closeout_by : null,
                    "Close out Date": (r.closeout_detail && r.closeout_detail.closeout_at) ? this.dayjs(+r.closeout_detail.closeout_at).format(this.dateFormat) : null,
                };
            });

            worksheet.addRows(records);
            console.log('Merging heading column');
            if (featureType == 'good-call') {
                worksheet.mergeCells(`K1:M1`);
            } else {
                worksheet.mergeCells(`K1:M1`);
                worksheet.mergeCells(`P1:R1`);
            }
            let lastRowIndex = 0;
            let increaseRowHeightIfNeeded = this.excelUtility.increaseRowHeightIfNeeded;
            worksheet.eachRow(function(row, rowNumber) {
                lastRowIndex++;
                let actualRow = worksheet.getRow(lastRowIndex);

                // Styling first ROW of Heading
                if (rowNumber === 1) {
                    row.height = 20;
                    // Iterate over all (including empty) cells in a row
                    row.eachCell({ includeEmpty: true }, function(cell, colNumber) {
                        cell.font = {
                            bold: true,
                        };
                        cell.fill = {
                            type: 'pattern',
                            pattern:'solid',
                            fgColor: {argb: 'dedede'}
                        }
                        cell.border = {
                            top: {style:'thin'},
                            left: {style:'thin'},
                            bottom: {style:'thin'},
                            right: {style:'thin'}
                        };
                    });
                } else {
                    if (featureType != 'good-call') {
                        row.eachCell({includeEmpty: true}, function (cell, colNumber) {
                            let statusMessage = cell.value;
                            if (colNumber === 14 && statusMessage) {
                                cell.border = {
                                    top: {style:'thin'},
                                    left: {style:'thin'},
                                    bottom: {style:'thin'},
                                    right: {style:'thin'}
                                };

                                cell.font = {
                                    color: {argb: "FFFFFF"}
                                };

                                cell.fill = {
                                    type: 'pattern',
                                    pattern: 'solid',
                                    fgColor: {argb: (statusMessage == "No Action") ? '0e0e0e' : (statusMessage == "Open") ? 'dc3545' : '28a745'}
                                };
                            }
                        });
                    }
                }
                if(rowNumber <= 1){
                    return;
                }
                console.log('Processing old row #', rowNumber, 'last row index was', lastRowIndex);

                let maxHeightNeeded = 0;
                let mergeStartAt = lastRowIndex;
                let mergeEndAt = lastRowIndex;
                let imgs = (imagesWorkspaceId || []).filter(i => i.rowId == actualRow.getCell(col1).value) || [];
                imgs.map(async (img, loopIndex) => {
                    console.log('Adding image in row', lastRowIndex);
                    let tlCol = (featureType == 'good-call') ? 10.10 + (loopIndex % 3) : 10.10 + (loopIndex % 3);
                    let tlRow = (lastRowIndex-1) + 0.1;
                    let tl = { col: tlCol, row: tlRow };
                    worksheet.addImage(img.id, {
                        tl: tl,
                        ext: img.ext,
                    });
                    maxHeightNeeded = increaseRowHeightIfNeeded(worksheet.getRow(lastRowIndex), img.ext.height, maxHeightNeeded);
                    // Just before it need, add an empty row
                    if(loopIndex > 0 && (loopIndex % 3) === 2 && loopIndex < (imgs.length-1)){
                        console.log('Adding empty row');
                        worksheet.spliceRows(lastRowIndex+1, 0, []);
                        mergeEndAt++;
                        lastRowIndex++;
                    }
                });

                if (featureType != 'good-call') {
                    let lastClosedOutRowIndex = mergeStartAt;
                    console.log('Closed out will start from ', lastClosedOutRowIndex, 'while current index is', lastRowIndex);
                    let img_of_closed_out = (closedOutImagesWorkspaceId || []).filter(i => i.rowId == actualRow.getCell(col1).value) || [];
                    img_of_closed_out.map((img, loopIndex) => {
                        console.log('Adding closed out image in row', lastClosedOutRowIndex);
                        let tlCol = 15.10 + (loopIndex % 3);
                        let tlRow = (lastClosedOutRowIndex - 1) + 0.1;
                        let tl = {col: tlCol, row: tlRow};
                        worksheet.addImage(img.id, {
                            tl: tl,
                            ext: img.ext,
                        });
                        maxHeightNeeded = increaseRowHeightIfNeeded(worksheet.getRow(lastClosedOutRowIndex), img.ext.height, maxHeightNeeded);
                        // Just before it need, add an empty row
                        if (loopIndex > 0 && (loopIndex % 3) === 2 && loopIndex < (img_of_closed_out.length - 1)) {
                            if (lastClosedOutRowIndex >= lastRowIndex) {
                                console.log('Adding empty row for closed out');
                                worksheet.spliceRows(lastClosedOutRowIndex + 1, 0, []);
                                mergeEndAt++;
                                lastRowIndex++;
                            }
                            lastClosedOutRowIndex++;
                        }
                    });
                }

                // Merge if extra rows were added above
                if(mergeEndAt > mergeStartAt){
                    console.log(`Merging cells vertically ${mergeStartAt} to ${mergeEndAt}`);
                    worksheet.mergeCells(`A${mergeStartAt}:A${mergeEndAt}`);
                    worksheet.mergeCells(`B${mergeStartAt}:B${mergeEndAt}`);
                    worksheet.mergeCells(`C${mergeStartAt}:C${mergeEndAt}`);
                    worksheet.mergeCells(`D${mergeStartAt}:D${mergeEndAt}`);
                    worksheet.mergeCells(`E${mergeStartAt}:E${mergeEndAt}`);
                    worksheet.mergeCells(`F${mergeStartAt}:F${mergeEndAt}`);
                    worksheet.mergeCells(`G${mergeStartAt}:G${mergeEndAt}`);
                    worksheet.mergeCells(`H${mergeStartAt}:H${mergeEndAt}`);
                    worksheet.mergeCells(`I${mergeStartAt}:I${mergeEndAt}`);
                    worksheet.mergeCells(`J${mergeStartAt}:J${mergeEndAt}`);
                }
                console.log('Last row index is', lastRowIndex);
            });
        }
        workbook.xlsx.writeBuffer().then(data => {
            const blob = new Blob([data], { type: this.blobType });
            if (featureType == 'good-call') {
                fs.saveAs(blob, `${ this.gcPhrase } Report-${dayjs().format(AppConstant.apiRequestDateFormat)}.xlsx`);
            } else {
                fs.saveAs(blob, `${ this.observationPhrase } Report-${dayjs().format(AppConstant.apiRequestDateFormat)}.xlsx`);
            }
            this.exportInProgress = false;
            this.blockloader = false;
        });
        this.observationDwStatus = {open:true, closed:true, no_closeout:true};
    }

    @ViewChild('goodCallDetailsHtml') private goodCallDetailsHtmlModal: GenericModalComponent
    goodCallDetailModal(row, isMap:boolean = false){
        this.dashboardLoader = true;
        this.goodCallService.getGoodCallData(this.projectInfo?.id, row.id).subscribe(
            (response) => {
            if(response.success) {
                const data = response.good_call;
                this.imagesArray = (data?.images || []).reduce((acc, file) => {
            if (file.img_translation?.length) {
                acc.push(...file.img_translation);
            } else if (file.file_url && file.file_type != 7) {
                acc.push(file.file_url);
            }
            return acc;
        }, []).map(img => ({ file_url: img }));

        this.closeOutImageArray = (data?.closeout_detail.images || []).reduce((acc, file) => {
            if (file.img_translation?.length) {
                acc.push(...file.img_translation);
            } else if (file.file_url && file.file_type != 7) {
                acc.push(file.file_url);
            }
            return acc;
        }, []).map(img => ({ file_url: img }));
        this.good_call_row = { ...data };
        this.tagOwner = undefined;
        this.tagUser = undefined;
        this.hazardCategoryEditDisable = true;
        if(this.good_call_row['location']?.lat && this.good_call_row['location']?.long){
            this.good_call_row['location'].lat = Number(this.good_call_row['location']?.lat);
            this.good_call_row['location'].long = Number(this.good_call_row['location']?.long);
        }
        this.selectedStatus = this.good_call_row.status;

        if(this.current_feature == 'good-call'){
            this.goodCallDetailsHtmlConfig.showCancelTrailingBtn = false;
            this.goodCallDetailsHtmlConfig.primaryTrailingBtnLabel = 'OK';
        } else{
            if(this.selectedStatus == 1){
                this.goodCallDetailsHtmlConfig.primaryTrailingBtnLabel = 'Close Out';
                this.goodCallDetailsHtmlConfig.showDropdown = true;
                this.goodCallDetailsHtmlConfig.selectedOption = this.selectedStatus;
            } else if(this.selectedStatus == 2){
                this.goodCallDetailsHtmlConfig.primaryTrailingBtnLabel = 'OK'
                this.goodCallDetailsHtmlConfig.showCancelTrailingBtn = false
                this.goodCallDetailsHtmlConfig.showDropdown = false;
            } else if(this.selectedStatus == 3){
                this.goodCallDetailsHtmlConfig.primaryTrailingBtnLabel = 'OK';
                this.goodCallDetailsHtmlConfig.showDropdown = true;
                this.goodCallDetailsHtmlConfig.selectedOption = this.selectedStatus;
            }
        }

        this.goodCallDetailsHtmlConfig.modalTitle = `${(this.current_feature == 'good-call') ? this.gcSglrPhrase : this.observationSglrPhrase } #${ this.good_call_row?.record_id } Details`
        this.showMap = isMap
       if(this.showMap) {
            this.progressPhotosHtmlMapModal.close()
        }
        this.checkCloseoutDetail();
        this.goodCallDetailsHtmlModal.open();
    } else {
        alert(response.message ? response.message : 'Failed to fetch good calls');
    }
    }).add(() => this.dashboardLoader = false);
    }

    public goodCallDetailsHtmlConfig: GenericModalConfig = {
        modalTitle: '',
        primaryTrailingBtnLabel: '',
        primaryTrailingBtnAction: () => {
            if(this.selectedStatus == 2 || this.selectedStatus == 3|| this.current_feature == 'good-call') {
                this.goodCallDetailsHtmlModal.close()
            } else {
                this.goodCallDetailsHtmlModal.close()
                this.closeOutModal(this.good_call_row)
            }
            return true;
        },
        closeTrailingBtnAction: () => {
            if(this.showMap){
                this.good_call_row = {}
                this.showMapWithMultiPin = true;
                this.progressPhotosHtmlMapModal.open()
            }
            return true;
        },
        dropDownOptions: this.observation_status,
        dropDownLabel: "Change Status",
        changeOptionValue: (e) => {
            this.changeObservationStatus(e, '', this.good_call_row.status)
            return true;
        },
        modalOptions: {
            size: 'lg',
        }
    }

    replaceAll(str='', find, replace) {
        const escapedFind = find.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, '\\$1');
        return str.replace(new RegExp(escapedFind, 'g'), replace);
    }

    onMouseOver(infoWindow, _event: MouseEvent) {
        infoWindow.open();
    }

    onMouseOut(infoWindow, _event: MouseEvent) {
        infoWindow.close();
    }

    openModal(content, size, windowClass=''){
        const modalRef = this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size,
            windowClass
        });
        return false;
    }
    public ObservationMapConfig: GenericModalConfig = {
        modalTitle: '',
        hideFooter: true,
        modalOptions: {
            size: 'lg',
        }
    }

    public openMapWithMultiPin() {
        this.showMapWithMultiPin = true;
        this.ObservationMapConfig.modalTitle = this.current_feature == 'good-call' ? this.gcSglrPhrase : this.observationSglrPhrase;
        this.progressPhotosModalShowMap();
    }

    public openMapWithPin() {
        this.showMapWithMultiPin = false;
        this.showProgressPhotosHtmlMap = true;
        this.progressPhotosModalShowMap();
    }

    @ViewChild('observationHtmlMap') private progressPhotosHtmlMapModal: GenericModalComponent;
    progressPhotosModalShowMap() {
        this.showProgressPhotosHtmlMap = true;
        this.progressPhotosHtmlMapModal.open();
    }

    @ViewChild('addObsOrGoodCallModalRef') private addObsOrGoodCallModalRef: GenericModalComponent;
    @ViewChild('addObservationOrGoodCallForm') addObservationOrGoodCallForm;
    public openAddNewObsOrGoodCallModal() {
        this.resetObsOrGoodCallModal(); // function for reset input values
        this.addImageBlock();
        if(this.addObservationOrGoodCallForm) {
            this.addObservationOrGoodCallForm.reset();
            this.addObservationOrGoodCallForm.form.statusChanges.subscribe(status => {
                this.addObsOrGoodCallModalConfig.primaryBtnDisabled = status !== 'VALID';
            });
        }
        this.addObsOrGoodCallModalConfig.modalTitle = `New ${(this.current_feature == 'good-call') ? this.gcSglrPhrase : this.observationSglrPhrase}`
        this.addObsOrGoodCallModalRef.open()
    }

    public addObsOrGoodCallModalConfig: GenericModalConfig = {
        modalTitle: '',
        primaryTrailingBtnLabel: 'Submit',
        primaryTrailingBtnAction: () => {
            this.saveObservationOrGoodCall(this.addObservationOrGoodCallForm)
            return true;
        },
        primaryBtnDisabled: true,
        modalOptions: {
            size: 'md',
        }
    }

    changeIsCloseOutOrAnonymous(field): void {
        if (field === 'required_closeout') {
            this.observationOrGoodCallObj.required_closeout = !this.observationOrGoodCallObj.required_closeout;
        } else {
            this.observationOrGoodCallObj.is_anonymous = !this.observationOrGoodCallObj.is_anonymous;
        }
    }

    /**
     * clear and initialize/reset all inputs for new observation and good-call modal(single-one)
     */
    private resetObsOrGoodCallModal(): void {
        this.observationOrGoodCallObj = {
            project_ref: this.projectId,
            company_ref: this.regUserCompanyId,
            hazard_category: null,
            details: '',
            images: [],
            tagged_owner: null,
            required_closeout: false,
            is_anonymous: false,
            further_actions: '',
            location: {
                lat: null,
                long: null,
                description: ''
            },
            assigned_to: null,
        };
        this.newPhotos = [];
    }

    /**
     * To check and add new observation or new good-call
     */
    public saveObservationOrGoodCall(form): void {
        if(form.invalid){
            const message = 'Form is invalid!';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }
        if(this.newPhotos && this.newPhotos.length)
         this.observationOrGoodCallObj['images'] = (this.newPhotos || []) .filter(m => m?.id).map(m => m.id);
        this.dashboardLoader = true;
        if(this.current_feature === 'good-call'){
            this.createNewGoodCall();
        } else {
            this.createNewObservation();
        }
    }

    /**
     * to add new observation
     */
    private createNewObservation(): void {
        this.goodCallService.addObservation(this.observationOrGoodCallObj, this.projectId).subscribe(res => {
            if(res.success){
                const message = 'Observation Created Successfully';
                this.toastService.show(this.toastService.types.SUCCESS, message);
                this.closeModalAndTableInit();
                this.showCloseOutForm = false
                this.addObsOrGoodCallModalRef.close();
            } else {
                const message = res.message || 'Failed to save information';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        }).add(() => this.dashboardLoader = false);
    }

     /**
     * to add new good-call
     */
    private createNewGoodCall(): void {
        this.goodCallService.addGoodCall(this.observationOrGoodCallObj, this.projectId).subscribe(res => {
            if(res.success){
                const message = 'Good-Call Created Successfully';
                this.toastService.show(this.toastService.types.SUCCESS, message);
                this.closeModalAndTableInit();
                this.showCloseOutForm = false
                this.addObsOrGoodCallModalRef.close();
            } else {
                const message = res.message || 'Failed to save information';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        }).add(() => this.dashboardLoader = false);
    }

    private closeModalAndTableInit(): void {
        this.modalService.dismissAll();
        this.initializeTable(true);
        this.getProjectUtils();
    }


    private addImageBlock(): void {
        if(!this.newPhotos){
            this.newPhotos = [];
        }
        this.newPhotos = this.newPhotos.filter(p => Object.keys(p).length);
        this.newPhotos.unshift({});
    }

    public mediaUploadDone($event): void {
        this.newPhotos.splice(1, 0,...$event.userFile);
        this.newPhotos[0] = {};
    }

    public imgDeleteDone(event): void {
        const userFileId = event?.userFile?.id;
        if(userFileId) {
            this.newPhotos = this.newPhotos.filter(r => (r?.id !== userFileId));
        }
    }

    openProgressPhotosMapModal(content, size){
        const modalRef = this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size,
        });
        return false;
    }

    getSelectedTagCompanyName(id: number) {
        const company = this.inductedUsersEmployer.find(data => data.id === id);
        return (company) ? company.name : '';
    }

    tagOwnerRequest(companySelectorElm$) {
        if (this.tagOwner) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Tag Company',
                title: `Are you sure you want to tag <span class="fw-500">${this.getSelectedTagCompanyName(this.tagOwner)}</span>?`,
                confirmLabel: 'Yes',
                onConfirm: () => {
                    if (this.tagOwner) {
                        let request = {
                            id: this.good_call_row['id'],
                            tagged_owner: this.tagOwner
                        };
                        this.updateRequestLoader = true;
                        this.goodCallService.updateGoodCall(request, this.projectId, request.id).subscribe(out => {
                            this.updateRequestLoader = false;
                            if (out.success) {
                                this.good_call_row['tagged_owner'] = (this.inductedUsersEmployer || []).find(emp => emp.id === this.tagOwner) || null;
                            } else {
                                const message = out.message || 'Failed to update data';
                                this.toastService.show(this.toastService.types.ERROR, message, { data: out});
                            }
                            this.initializeTable(true);
                        });
                    }
                },
                onClose: () => {
                    companySelectorElm$.handleClearClick();
                },
            });
        }
    }

    tagUserRequest({selectedUser, selectedRecord}, userSelectorElm$) {
        this.tagUser = selectedUser;
        if (this.tagUser){
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Assign User',
                title: `Are you sure you want to assign an action to <span class="fw-500">${selectedRecord.name}</span>?`,
                confirmLabel: 'Assign',
                onConfirm: () => {
                    let request = {
                        id: this.good_call_row['id'],
                        assigned_to: this.tagUser
                    };
                    this.updateRequestLoader = true;
                    this.goodCallService.updateGoodCall(request, this.projectId, request.id).subscribe(out => {
                        this.updateRequestLoader = false;
                        if(out.success) {
                            this.good_call_row['assigned_to'] = selectedRecord || null;
                        } else {
                            const message = out.message || 'Failed to update data';
                            this.toastService.show(this.toastService.types.ERROR, message);
                        }
                    });
                },
                onClose: () => {
                    userSelectorElm$.clearSelection();
                },
            });
        }
    }

    downloadGoodCall(row) {
        this.blockloader = true;
        let body = {
            createdAt: row.createdAt,
            companyId: this.employerId,
            type: 'pdf'
        };

        this.goodCallService.downloadGoodCall(body, this.projectId, row.id, () => {
            this.blockloader = false;
        });
    }

    @ViewChild('closeOutHtml') private closeOutHtmlModalRef: GenericModalComponent
    closeOutModal(row){
        this.good_call_row = row;
        this.good_call_row.closeout_detail.images = (!this.good_call_row.closeout_detail.images || !this.good_call_row.closeout_detail.images.length) ? [{}] : this.good_call_row.closeout_detail.images;
        this.good_call_row.closeout_detail.description = '';
        this.showCloseOutForm = true
        if (this.closeOutForm) {
            this.closeOutForm.form.controls?.closeout_description?.setValue('')
            this.closeOutForm.form.markAsUntouched();
            this.closeOutForm.form.statusChanges.subscribe(status => {
                this.closeOutHtmlModalConfig.primaryBtnDisabled = status !== 'VALID';
            });
        }
        this.closeOutHtmlModalConfig.modalTitle = `${ this.observationSglrPhrase } Close Out`
        this.closeOutHtmlModalRef.open();
    }

    @ViewChild('closeOutForm') closeOutForm;
    public closeOutHtmlModalConfig: GenericModalConfig = {
        modalTitle: '',
        primaryTrailingBtnLabel: 'Close Out',
        primaryTrailingBtnAction: () => {
            this.closeOutRequest(this.closeOutForm)
            return true;
        },
        cancelTrailingBtnAction: () => {
            this.showCloseOutForm = false
         return true
        },
        closeTrailingBtnAction: () => {
            this.showCloseOutForm = false
            return true;
        },
        primaryBtnDisabled: true,
        modalOptions: {
            size: 'md',
        }
    }

    logoImgRetries:number = 5;

    onLogoError($img:any, targetSrc:any){
        $img.src = '/images/project-placeholder.png';
        if(targetSrc && targetSrc.length && this.logoImgRetries){
            setTimeout(() => {
                this.logoImgRetries--;
                $img.src = targetSrc;
            }, 2000);
        }
    }

    onLogoLoad($img){
        this.logoImgRetries = 5;
    }

    uploadDone($event) {
        if($event && $event.userFile) {
            this.good_call_row.closeout_detail.images.splice(1, 0,...$event.userFile);
            this.good_call_row.closeout_detail.images[0] = {};
        }
    }

    fileDeleteDone($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.good_call_row.closeout_detail.images = this.good_call_row.closeout_detail.images.filter(r => (r.id !== $event.userFile.id));
        }
    }

    closeOutRequest(form) {
        if (!form.valid) {
            alert('Modal form is not valid');
            return;
        }
        let formData = form.value.observation;

        this.blockloader = true;
        let closeOutReq:any = {};
        closeOutReq.closeout_detail = formData.closeout_detail;
        closeOutReq.closeout_detail.images = (closeOutReq.closeout_detail.images || []).filter(image => image.file_url);
        closeOutReq.closeout_detail.closeout_by = this.authUser$.name;
        closeOutReq.closeout_detail.closeout_at = dayjs().valueOf();
        closeOutReq.status = 2;
        closeOutReq.project_name = formData.project_ref.name;
        closeOutReq.user_name = formData.user_ref.name;
        closeOutReq.user_email = formData.user_ref.email;
        let request = {
            ...closeOutReq,
            is_closeout_request: true
        };
        this.goodCallService.updateGoodCall(request, this.projectId, formData.id).subscribe(out => {
            this.blockloader = false;
            if(out.success){
                this.closeOutHtmlModalRef.close();
                this.showCloseOutForm = false;
                const message = `${this.observationSglrPhrase} successfully closed.`;
                this.toastService.show(this.toastService.types.SUCCESS, message);
                this.closeOutHtmlModalRef.close()
                this.showCloseOutForm = false
            } else {
                const message = out.message || 'Failed to update data';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
            this.initializeTable(true);
        });
    }

    checkCloseoutDetail() {
        this.hasCloseoutDetail = (this.current_feature == 'observation' && this.good_call_row.required_closeout && this.good_call_row.status == 2)
    }

    isStatusMatched(status) {
        return (status === 1 && this.observationDwStatus.open) || (status === 2 && this.observationDwStatus.closed) || (status === 3 && this.observationDwStatus.no_closeout);
    }

    getCloseOutDate(record) {
        return (record.closeout_detail && record.closeout_detail.closeout_at && !isNaN(record.closeout_detail.closeout_at)) ? this.dayjs(+record.closeout_detail.closeout_at).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) : 'N/A';
    }

    @ViewChild(NgSelectComponent)
    ngSelect: NgSelectComponent;
    changeObservationStatus($event, cb, currentStatus) {
        let selectedStatusValue = (this.observation_status).find(status => status.status == $event);
        if(selectedStatusValue && selectedStatusValue.status === currentStatus){
            return false;
        }
        else if (selectedStatusValue) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Update Status',
                title: `Are you sure you want to update status to <span class="fw-500">${selectedStatusValue.status_message}</span>?`,
                confirmLabel: 'Update',
                onConfirm: () => {
                    let request:any = {};
                    request.id = this.good_call_row.id;
                    request.required_closeout = ($event === 1) ? true : false;
                    request.status = $event;
                    this.updateRequestLoader = true;
                    this.goodCallService.updateGoodCall(request, this.projectId, this.good_call_row.id).subscribe(out => {
                        this.updateRequestLoader = false;
                        if(out.success) {
                            console.log('Observation status has been updated.');
                            this.goodCallDetailsHtmlModal.close();
                        } else {
                            const message = out.message || 'Failed to update status of the observation';
                            this.toastService.show(this.toastService.types.ERROR, message);
                        }
                        this.initializeTable(true);
                    });
                },
                onClose: () => {
                    selectedStatusValue = (this.observation_status).find(status => status.status == currentStatus);
                    this.ngSelect.select(selectedStatusValue);
                    this.selectedStatus = currentStatus;
                    this.goodCallDetailsHtmlConfig.selectedOption = this.good_call_row.status;
                },
            });
        }
    }

    openDashboardModal() {
        this.loadPowerBiComponent = true;
        this.biModalTitle = `${this.biToolLabel} Dashboard`;
    }

    powerBiDashboardClose() {
        this.loadPowerBiComponent = false;
    }

    public enableEdit(): void {
        this.hazardCategoryEditDisable = !this.hazardCategoryEditDisable;
    }

    /**
     * to update hazard-category
     * @param hazardCategory selected hazard-category
     */
    updateHazardCategory(hazardCategory) {
        let val = (this.current_feature == 'good-call') ? 'good-call' : 'observation';
        let request = {
            id: this.good_call_row['id'],
            hazard_category: hazardCategory
        };
        this.updateRequestLoader = true;
        this.goodCallService.updateGoodCall(request, this.projectId, request.id, val).subscribe(res => {
            if(res.success) {
                this.goodCallDetailsHtmlModal.close()
                this.initializeTable(true);
            } else {
                const message = res.message || 'Failed to update data';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        }).add(() => {
            this.updateRequestLoader = false;
        });
    }
    onFilterSelection(data){
        this.selectedHazardType = data.category.map(a=>a.name);
        this.page.pageNumber = 0;
        this.statusFilter = data.status.map(a=>a.status);
        this.selectedAssignedTo = data['assigned to'].map(a => a.id);
        this.selectedRaisedBy = data['raised by'].map(a => a.id);
        this.ownerFilter = data['companies'].map(a=> a.company_id);
        this.initializeTable(true);
    }
    searchFunction(data){
        this.search = data.search;
        this.page.pageNumber = 0;
        let stopReload = data?.stopReload;
        if(!stopReload){
            this.initializeTable(true);
        };
    }
    renderFilterData(){
        return [
            {
                name:'category',
                list: (this.projectFilters.categoryLists || []),
                enabled:true,
                state:false,
            },
            {
                name:'status',
                list:this.observation_all_status,
                enabled:this.current_feature!= 'good-call',
                state:false,
            },
            {
                name: 'assigned to',
                list: (this.projectFilters.assignToUsers || []),
                enabled:this.current_feature != 'good-call',
                state: false
            },
            {
                name: 'raised by',
                list: (this.projectFilters.raisedByUsers || []),
                enabled: true,
                state: false
            },
            {
                name: 'companies',
                list: (this.projectFilters.tagged_owners || []),
                enabled: true,
                state: false,
                labelKey: 'company_name'
            },
        ];
    }

    public onActionSelection(actionVal: any) {
        const code = actionVal.code;
        this.actionButtonMetaData.isTraining = false;
        if(code === GoodCallObservationActionButtons.MAP) {
            this.openMapWithMultiPin();
        } else if(code === GoodCallObservationActionButtons.DASHBOARD) {
            this.openDashboardModal();
        } else if(code === GoodCallObservationActionButtons.DOWNLOAD_REPORT) {
            this.openGoodCallObsReportModal();
        }
    }

    public openGoodCallObsReportModal() {
        this.reportDownloader.openModal();
        this.observationDwStatus = {open:true, closed:true, no_closeout:true};
    }

    async goodCallObsReportDownload(event) {
        this.downloadHazardType = event.selection.category;
        if (event.selection.employer) {
          this.filterEmployer =
            (typeof event.selection.employer === 'object')
              ? event.selection.employer.name
              : event.selection.employer;
        } else {
          this.filterEmployer = '';
        }
        this.observationDwStatus = event.selection.status;
        await this.initiateDownload(event.selection);
        event.closeFn();
    }
}

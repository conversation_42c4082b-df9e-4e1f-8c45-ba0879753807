<style>
    agm-map {
        /*height:83vh;*/
        height:50vh;
        width:100%
    }
    .location{
        color: blue;
    }
    .location:hover{
        cursor: pointer;
        text-decoration: underline;
    }
</style>
<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav *ngIf="!isProjectPortal" [companyResolverResponse]="companyResolverResponse" [employer]="employer" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 nav-tabs mt-3"></project-header>
            <div class="col-sm-12 my-3 outer-border" *ngIf="!loadingGoodCalls">
                <div class="col-sm-12 outer-border-radius">
                    <div class="mb-2 pb-2 d-flex justify-content-between flex-wrap gap-8">
                        <h5 class="float-left">Total {{ (current_feature == 'good-call') ? gcPhrase : observationPhrase }}:
                            <small *ngIf="current_feature == 'good-call' else elseBlock">({{page.totalElements}})</small>
                            <ng-template #elseBlock>(Open {{openRecords}} : Raised {{page.totalElements}})</ng-template>
                        </h5>
                        <action-button
                                [actionList]="actionButtonMetaData.actionList"
                                (selectedActionEmmiter)="onActionSelection($event)"
                                [newFeatureTitle]="'New ' + ((this.current_feature == 'good-call') ? this.gcSglrPhrase : this.observationSglrPhrase)"
                                (onOpenAddNew)="openAddNewObsOrGoodCallModal()">
                        </action-button>
                    </div>
                    <div class="text-center form-group p-0 col-md-12">
                        <search-with-filters (searchEmitter)="searchFunction($event)" [filterData]="filterData" (filterEmitter)="onFilterSelection($event)"></search-with-filters>
                    </div>

                    <div class="table-responsive-sm">
                        <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                       [scrollbarV]="true"
                                       [virtualization]="false"
                                       [loadingIndicator]="loadingInlineGoodCallObservation"
                                       [rows]="records"
                                       [footerHeight]="40"
                                       [columnMode]="'force'"
                                       [rowHeight]="'auto'"
                                       [externalPaging]="true"
                                       [count]="page.totalElements"
                                       [offset]="page.pageNumber"
                                       [limit]="page.size"
                                       (page)="pageCallback($event)"
                        >
                            <ngx-datatable-column headerClass="font-weight-bold" minWidth="100" maxWidth="150" [sortable]="false">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    {{ (current_feature == 'good-call') ? gcSglrPhrase : observationSglrPhrase }} #
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    {{row.record_id}}
                                </ng-template>
                            </ngx-datatable-column>
                            <ngx-datatable-column headerClass="font-weight-bold" minWidth="100" maxWidth="209" [sortable]="false">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    Raised
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    {{ dayjsDisplayDateOrTime(row.createdAt) }}
                                    <div style="font-size: 11px;">({{ dayjsDisplayDateOrTime(row.createdAt, false) }})</div>
                                </ng-template>
                            </ngx-datatable-column>
                            <ngx-datatable-column headerClass="font-weight-bold" minWidth="100" maxWidth="215" [sortable]="false">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    Raised By
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    <div appTooltip *ngIf="!row.is_anonymous" style="display: inline-grid;">
                                        <span>{{row?.user_ref?.first_name}} {{row?.user_ref?.last_name}}</span>
                                        <span *ngIf="row?.user_employer?.employer" style="font-size: 11px;">({{row.user_employer.employer}})</span>
                                    </div>
                                    <span *ngIf="row.is_anonymous">Anonymous</span>
                                </ng-template>
                            </ngx-datatable-column>
                            <ngx-datatable-column headerClass="font-weight-bold" minWidth="100" [sortable]="false">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    Company
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    <span appTooltip *ngIf="row?.tagged_owner">{{row?.tagged_owner?.name}}</span>
                                    <span *ngIf="!row?.tagged_owner">-</span>
                                </ng-template>
                            </ngx-datatable-column>
                            <ngx-datatable-column headerClass="font-weight-bold" minWidth="100" [sortable]="false">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    Category
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    <span appTooltip *ngIf="row?.hazard_category">{{row?.hazard_category}}</span>
                                    <span *ngIf="!row?.hazard_category">-</span>
                                </ng-template>
                            </ngx-datatable-column>
                            <ngx-datatable-column headerClass="font-weight-bold" minWidth="100" [sortable]="false"
                                                  *ngIf="current_feature == 'good-call'">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    Location
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    <span appTooltip *ngIf="row?.location?.description" [innerHtml]="replaceAll(row?.location?.description, '\n', '<br>')"></span>
                                </ng-template>
                            </ngx-datatable-column>
                            <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" *ngIf="current_feature == 'observation'">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    Assigned To
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    <span appTooltip *ngIf="row?.assigned_to">{{row?.assigned_to.first_name}} {{row?.assigned_to.last_name}}</span>
                                    <span *ngIf="!row?.assigned_to">-</span>
                                </ng-template>
                            </ngx-datatable-column>

                            <ngx-datatable-column *ngIf="current_feature == 'observation'" headerClass="font-weight-bold action-column text-center"
                                                  cellClass="action-column" minWidth="150" [width]="300" [sortable]="false">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    Status
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    <div class="text-center">
                                    <span *ngIf="row.status == 1" (click)="goodCallDetailModal(row)" class="d-block btn-sm badge-pill badge-danger cursor-pointer" style="width: 90px; margin: 0 auto;">
                                        {{ row.status_message }}
                                    </span>
                                        <span *ngIf="row.status == 2" class="d-block btn-sm badge-pill badge-success" style="width: 90px; margin: 0 auto;">
                                        {{ row.status_message }}
                                    </span>
                                        <span *ngIf="row.status == 3" class="d-block btn-sm badge-pill" style="width: 90px; margin: 0 auto; background-color: #0e0e0e; color: #FFFFFF;">
                                        {{ row.status_message }}
                                    </span>
                                    </div>
                                </ng-template>
                            </ngx-datatable-column>

                            <ngx-datatable-column *ngIf="current_feature == 'observation'" minWidth="100"  headerClass="font-weight-bold" [sortable]="false">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    Closed Out
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    {{ getCloseOutDate(row) }}
                                </ng-template>
                            </ngx-datatable-column>

                            <ngx-datatable-column headerClass="font-weight-bold action-column" minWidth="150" cellClass="action-column" [sortable]="false">
                                <ng-template let-column="column" ngx-datatable-header-template>
                                    Action
                                </ng-template>
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                    <button-group
                                        [buttons]="rowButtonGroup"
                                        [btnConditions]="[true, true, (row.status === 1 && row.required_closeout && !(current_feature == 'good-call'))]"  
                                        (onActionClick)="rowBtnClicked($event, row)">
                                    </button-group>
                                </ng-template>
                            </ngx-datatable-column>
                        </ngx-datatable>

                        <app-generic-modal #goodCallDetailsHtml [genericModalConfig]="goodCallDetailsHtmlConfig">
                            <ng-container *ngTemplateOutlet="goodCallDetailsTemplate"></ng-container>
                            <ng-template #goodCallDetailsTemplate>
                                <table class="table table-sm table-bordered" style="font-size: 14px;">
                                    <tbody>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 25%;">
                                            <strong>Raised:</strong>
                                        </td>
                                        <td *ngIf="good_call_row && good_call_row.createdAt">
                                            {{dayjsFullDateTime(good_call_row.createdAt)}}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 25%;">
                                            <strong>Raised By:</strong>
                                        </td>
                                        <td>
                                            <span *ngIf="!good_call_row?.is_anonymous">
                                                {{good_call_row?.user_ref?.first_name}} {{good_call_row?.user_ref?.last_name}} ({{good_call_row?.user_employer?.employer}})
                                            </span>
                                            <span *ngIf="good_call_row?.is_anonymous"> Anonymous</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 25%;">
                                            <strong>Category:</strong>
                                        </td>
                                        <td>
                                            <div class="d-flex my-1">
                                                <div class="input-group ml-1">
                                                    <ng-select class="w-100 dropdown-list" appendTo="body" [items]="(fetchCategoryList || [])" bindLabel="name" bindValue="name"
                                                               name="hazard_category" placeholder="Select Category"
                                                               [(ngModel)]="good_call_row.hazard_category" [disabled]="hazardCategoryEditDisable" required>
                                                    </ng-select>
                                                </div>
                                                <ng-container *ngIf="hazardCategoryEditDisable; else hazardCatEdit">
                                                    <button type="button" class="btn-act-editable border-0 d-flex align-items-center justify-content-center px-4 mr-1" (click)="enableEdit()">
                                                        <img class="img-icon pr-1" [src]="AssetsUrlSiteAdmin.pencilSquare" alt="">
                                                        <span class="mt-1">Edit</span>
                                                    </button>
                                                </ng-container>
                                                <ng-template #hazardCatEdit>
                                                    <button type="button" class="btn-act-editable border-0 pl-3 pr-4 mr-1" (click)="updateHazardCategory(good_call_row.hazard_category)">
                                                        <span class="mt-1">Save</span>
                                                    </button>
                                                </ng-template>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 25%;">
                                            <strong>Details:</strong>
                                        </td>
                                        <td>
                                            <span *ngIf="good_call_row?.details" [innerHtml]="replaceAll(good_call_row?.details, '\n', '<br>')"></span>
                                        </td>
                                    </tr>
                                    <tr *ngIf="(current_feature == 'good-call')">
                                        <td class="tr-bg-dark-color" style="width: 25%;">
                                            <strong>Further Actions:</strong>
                                        </td>
                                        <td>
                                            <span *ngIf="good_call_row?.further_actions" [innerHtml]="replaceAll(good_call_row?.further_actions, '\n', '<br>')"></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color p-1" style="width: 25%;">
                                            <strong>Location:</strong>
                                        </td>
                                        <td>
                                            <span *ngIf="good_call_row?.location" [innerHtml]="replaceAll(good_call_row?.location?.description, '\n', '<br>')"></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tr-bg-dark-color" style="width: 25%;">
                                            <strong>Location Tag (Lat, Long):</strong>
                                        </td>
                                        <td>
                                            <span class="location" (click)="openMapWithPin()" *ngIf="good_call_row?.location?.lat && good_call_row?.location?.long">
                                                ({{ good_call_row?.location?.lat | number : decimalConfig}}, {{good_call_row?.location?.long | number : decimalConfig}})
                                            </span>
                                        </td>
                                    </tr>
                                    <tr *ngIf="(current_feature != 'good-call')">
                                        <td class="tr-bg-dark-color" style="width: 25%;">
                                            <strong>{{(good_call_row?.assigned_to?.name) ? "Assigned To:" : "Assign To:"}}</strong>
                                        </td>
                                        <td *ngIf="good_call_row?.assigned_to?.name else selectUserToTag">
                                            {{good_call_row?.assigned_to?.name}}
                                        </td>
                                        <ng-template #selectUserToTag>
                                            <td>
                                                <ng-container *ngIf="this.selectedStatus === 1">
                                                    <form novalidate #addForm="ngForm">
                                                        <inducted-user-selector
                                                            #userSelector
                                                            placeholder="Select User"
                                                            name="user_selector"
                                                            [projectId]="projectId"
                                                            [required]="true"
                                                            [selection]="tagUser"
                                                            (selectionChanged)="tagUserRequest($event, userSelector)"
                                                        ></inducted-user-selector>
                                                    </form>
                                                </ng-container>
                                            </td>
                                        </ng-template>
                                    </tr>
                                    <tr *ngIf="good_call_row?.tagged_owner?.name">
                                        <td class="tr-bg-dark-color" style="width: 25%;">
                                            <strong>{{(good_call_row?.tagged_owner?.name) ? "Tagged Company:" : "Tag Company:" }}</strong>
                                        </td>
                                        <td *ngIf="good_call_row?.tagged_owner?.name else selectTaggedOwner">
                                            {{ good_call_row?.tagged_owner?.name }}
                                        </td>
                                        <ng-template #selectTaggedOwner>
                                            <td>
                                                <ng-select class="dropdown-list" appendTo="body" [name]="companySelector" #companySelector [(ngModel)]="tagOwner"
                                                           required style="width: 100%;" placeholder="Select Company"
                                                           (change)="tagOwnerRequest(companySelector)">
                                                    <ng-option *ngFor="let employer of inductedUsersEmployer" [value]="employer.id" >{{employer.name}}</ng-option>
                                                </ng-select>
                                            </td>
                                        </ng-template>
                                    </tr>
                                    <tr *ngIf="imagesArray && imagesArray.length">
                                        <td colspan="4">
                                            <pop-up-image-viewer [alt]="'Good Call Image'" [imgArray]="imagesArray || []"></pop-up-image-viewer>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>

                                <div class="mt-4" *ngIf="hasCloseoutDetail">
                                    <div class="w-100">
                                        <h4>Closeout Details</h4>
                                    </div>
                                    <table class="table table-sm table-bordered" style="font-size: 14px; table-layout:fixed;">
                                        <tbody>
                                        <tr *ngIf="good_call_row?.closeout_detail?.closeout_by">
                                            <td class="tr-bg-dark-color" style="width: 25%;">
                                                <strong>Closed Out By:</strong>
                                            </td>
                                            <td>
                                                {{ good_call_row?.closeout_detail?.closeout_by }}
                                            </td>
                                        </tr>
                                        <tr *ngIf="good_call_row?.closeout_detail?.closeout_at">
                                            <td class="tr-bg-dark-color" style="width: 25%;">
                                                <strong>Closed Out at:</strong>
                                            </td>
                                            <td>
                                                {{ dayjsFullDateTime(good_call_row?.closeout_detail?.closeout_at)}}
                                            </td>
                                        </tr>
                                        <tr *ngIf="good_call_row?.closeout_detail?.description">
                                            <td class="tr-bg-dark-color" style="width: 25%;">
                                                <strong>Closed Out Description:</strong>
                                            </td>
                                            <td>
                                                {{ good_call_row?.closeout_detail?.description }}
                                            </td>
                                        </tr>
                                        <tr  *ngIf="closeOutImageArray && closeOutImageArray.length">
                                            <td colspan="2">
                                                <pop-up-image-viewer [updateStyle]="{'max-height.px': 150, 'max-width.px': 150}" [alt]="'Close Out Image'" [imgArray]="closeOutImageArray || []"></pop-up-image-viewer>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </ng-template>
                        </app-generic-modal>

                        <app-generic-modal #observationHtmlMap [genericModalConfig]="ObservationMapConfig">
                            <ng-container *ngTemplateOutlet="pp"></ng-container>
                            <ng-template #pp>
                                <progress-photo-location-map
                                        *ngIf="showProgressPhotosHtmlMap"
                                        [showMapWithMultiPin]="showMapWithMultiPin"
                                        [row_data]="good_call_row"
                                        [project_data]="good_calls_data"
                                        [dismiss]="d"
                                        [mapTitle]="current_feature == 'good-call' ? gcSglrPhrase : observationSglrPhrase"
                                        (view)="goodCallDetailModal($event, true)"
                                        feature="gc"
                                >
                                </progress-photo-location-map>
                            </ng-template>
                        </app-generic-modal>

                        <block-loader [show]="updateRequestLoader" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<app-generic-modal #closeOutHtml [genericModalConfig]="closeOutHtmlModalConfig">
    <ng-container *ngTemplateOutlet="closeOutHtmlTemplate"></ng-container>
    <ng-template #closeOutHtmlTemplate>
        <p class="text-center">{{ observationSglrPhrase }} number #{{ good_call_row?.record_id }}</p>
        <form novalidate #closeOutForm="ngForm">
            <div class="mb-3">
                <p class="mb-1">Description<small class="required-asterisk ">*</small></p>
                <input type="hidden" name="observation" id="obsr_id"
                       [(ngModel)]="good_call_row"/>
                <div *ngIf="showCloseOutForm">
                <textarea class="form-control" name="closeout_description"
                          [(ngModel)]="good_call_row.closeout_detail.description"
                          ng-value="good_call_row.closeout_detail.description"
                          placeholder="Closeout Details"
                          #closeOutDetail="ngModel" required></textarea>
                    <div class="alert alert-danger" [hidden]="(!good_call_row.closeout_detail.description || closeOutDetail.valid)">Close out detail is required</div>
                </div>
            </div>

            <div class="mt-2 col-md-12 p-0">
                <p class="mb-0">Upload Photo</p>
            </div>
            <div class="col-md-12 p-0 mb-4">
                <div class="col-md-12 flex-grow-1 p-0" *ngFor="let c of good_call_row?.closeout_detail?.images">
                    <file-uploader-v2
                            [disabled]="false" [multipleUpload]="true"
                            [init]="c"
                            [category]="current_feature"
                            (uploadDone)="uploadDone($event)"
                            [allowedMimeType]="allowedMime"
                            (deleteFileDone)="fileDeleteDone($event)"
                            [showDeleteBtn]="true"
                            [showFileName]="false"
                            [hasImgAndDoc]="true"
                    ></file-uploader-v2>
                </div>
            </div>
        </form>
    </ng-template>
</app-generic-modal>

<block-loader [show]="(blockloader || loadingGoodCalls)" [showBackdrop]="true" alwaysInCenter="true"></block-loader>

<power-bi-dashboard *ngIf="loadPowerBiComponent" (powerBiDashboardClose)="powerBiDashboardClose()" [modalTitle]="biModalTitle" [toolLabel]="biToolLabel" [toolName]="biToolName"></power-bi-dashboard>

<!-- Start: New Observation or New Good-Call -->
<app-generic-modal #addObsOrGoodCallModalRef [genericModalConfig]="addObsOrGoodCallModalConfig">
    <ng-container *ngTemplateOutlet="addObsOrGoodCallTemplate"></ng-container>
    <ng-template #addObsOrGoodCallTemplate>
        <form novalidate #addObservationOrGoodCallForm="ngForm">
            <div *ngIf="observationOrGoodCallObj">
                <div class="form-group">
                    <label class="d-inline-block"
                    >Category <small class="required-asterisk">*</small></label
                    >
                    <ng-select class="w-100 dropdown-list" appendTo="body" [items]="(fetchCategoryList || [])" bindLabel="name" bindValue="name"
                               name="hazard_category" placeholder="Select Category"
                               [(ngModel)]="observationOrGoodCallObj.hazard_category" required>
                    </ng-select>
                </div>
                <div class="form-group">
                    <label>Location <small class="required-asterisk">*</small></label>
                    <div class="input-group mb-3">
                        <input
                            #location="ngModel"
                            [(ngModel)]="observationOrGoodCallObj.location.description"
                            class="form-control"
                            name="location"
                            ng-value="observationOrGoodCallObj.location.description"
                            placeholder="Location"
                            type="text"
                            required
                        />
                    </div>
                </div>
                <div class="form-group">
                    <label>Details <small class="required-asterisk">*</small></label>
                    <div class="input-group mb-3">
                        <textarea
                            #pp_description="ngModel"
                            [(ngModel)]="observationOrGoodCallObj.details"
                            class="form-control"
                            name="details"
                            ng-value="observationOrGoodCallObj.details"
                            placeholder="Details"
                            required
                        ></textarea>
                    </div>
                </div>
                <div class="form-group" *ngIf="(current_feature === 'good-call')">
                    <label>Further Actions</label>
                    <div class="input-group mb-3">
                        <input
                            #furtherAction="ngModel"
                            [(ngModel)]="observationOrGoodCallObj.further_actions"
                            class="form-control"
                            name="furtherAction"
                            ng-value="observationOrGoodCallObj.further_actions"
                            placeholder="Further Actions"
                            type="text"
                        />
                    </div>
                </div>
                <div class="form-group mt-4">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6>Submit Anonymously</h6>
                        </div>
                        <div>
                            <input
                                    type="radio"
                                    class="form-check-input"
                                    id="i_anonymous"
                                    name="i_anonymous"
                                    [value]="observationOrGoodCallObj.is_anonymous"
                                    style="
                                    width: 1rem;
                                    height: 1rem;
                                    margin-top: 2px !important;
                                "
                                    [checked]="observationOrGoodCallObj.is_anonymous"
                                    (click)="changeIsCloseOutOrAnonymous('is_anonymous')"
                            />
                        </div>
                    </div>
                </div>

                <div class="form-group mt-4" *ngIf="current_feature !== 'good-call'">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6>Require Closeout</h6>
                        </div>
                        <div>
                            <input
                                    type="radio"
                                    class="form-check-input"
                                    id="r_closeout"
                                    name="r_closeout"
                                    [value]="observationOrGoodCallObj.required_closeout"
                                    style="
                                    width: 1rem;
                                    height: 1rem;
                                    margin-top: 2px !important;
                                "
                                    [checked]="observationOrGoodCallObj.required_closeout"
                                    (click)="changeIsCloseOutOrAnonymous('required_closeout')"
                            />
                        </div>
                    </div>
                </div>
                <div class="form-group" *ngIf="(current_feature !== 'good-call') && observationOrGoodCallObj.required_closeout">
                    <label class="d-inline-block"
                    >Assign To</label
                    >
                    <inducted-user-selector
                            placeholder="Select Assignee"
                            name="assignTo"
                            [projectId]="projectId"
                            [selection]="observationOrGoodCallObj.assigned_to"
                            (selectionChanged)="(observationOrGoodCallObj.assigned_to = $event.selectedUser)"
                    ></inducted-user-selector>
                </div>
                <div class="form-group">
                    <label class="d-inline-block">Tag Company </label>
                    <company-selector-v2
                            [required]="false"
                            [country_code]="projectInfo?.custom_field?.country_code"
                            name="tagCompany"
                            [selectId]="observationOrGoodCallObj.tagged_owner"
                            placeholder="Select Company"
                            [classes]="'w-100'"
                            (selectionChanged)="observationOrGoodCallObj.tagged_owner = $event.selected"
                            [projectId]="projectId"
                    ></company-selector-v2>
                </div>
                <div class="col-md-12 p-0 mt-3 mb-3"><h6>Add Photos</h6></div>
                <div class="col-md-12 p-0 mb-4">
                    <div class="col-md-12 flex-grow-1 p-0"
                         *ngFor="let item of newPhotos"
                    >
                        <file-uploader-v2
                            #imgUploader
                            (deleteFileDone)="imgDeleteDone($event)"
                            (uploadDone)="mediaUploadDone($event)"
                            [allowedMimeType]="allowedMime"
                            [category]="current_feature"
                            [disabled]="false"
                            [init]="item"
                            [multipleUpload]="true"
                            [showThumbnail]="false"
                            [showDeleteBtn]="true"
                            [showFileName]="false"
                            [hasImgAndDoc]="true"
                            class="pl-0"
                        >
                        </file-uploader-v2>
                    </div>
                </div>
            </div>
        </form>
    </ng-template>
</app-generic-modal>

<!-- End: New Observation or New Good Call -->
<block-loader [show]="(dashboardLoader)" [showBackdrop]="true" alwaysInCenter="true"></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
<report-downloader #reportDownloader
                   [xlsxOnly]="true"
                   [showCategory]="true"
                   [categoryList]="(projectFilters.categoryLists || [])"
                   [showEmployer]="this.current_feature == 'good-call'"
                   [employerList]="employersList"
                   [showCompany]="(tagged_owners || [])?.length && !isProjectPortal"
                   [companyList]="(tagged_owners || [])"
                   [showStatus]="this.current_feature != 'good-call'"
                   [statusObj]="observationDwStatus"
                   [showNoCloseout]="true"
                   (onFilterSelection)="goodCallObsReportDownload($event)"
>
</report-downloader>

import {Component, OnInit, TemplateRef, ViewChild, ViewEncapsulation, ElementRef} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {filter, tap, debounceTime, distinctUntilChanged, map, share} from "rxjs/operators";
import { HttpParams } from '@angular/common/http';
import {Observable, fromEvent} from "rxjs";
import * as dayjs from 'dayjs';
import {NgbModalConfig, NgbModal, NgbDateStruct} from '@ng-bootstrap/ng-bootstrap';
import {DeliveryNotesService, AuthService, UserService, User, Project, ProjectService, isInheritedProjectOfCompany, Common, HttpService, DeliveryCollectionNote, DeliveryNotesConModalType, ToastService, ProjectLocation} from "@app/core";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import {AppConstant} from "@env/environment";
import * as fs from "file-saver";
import {DatatableComponent} from "@swimlane/ngx-datatable";
import { ActionBtnEntry, GenericConfirmationModalComponent, IModalComponent } from '@app/shared';
import { ReportDownloaderComponent } from '../report-downloader/report-downloader.component';

@Component({
    templateUrl: './delivery-notes.component.html',
    // add NgbModalConfig and NgbModal to the component providers
    providers: [NgbModalConfig, NgbModal],
    styles: ['.datatable-body-cell {overflow-x:visible !important}'],
     encapsulation: ViewEncapsulation.None  // Use to disable CSS Encapsulation for this component
})
export class DeliveryNotesComponent implements OnInit {

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    @ViewChild('reportDownloader') private reportDownloader: ReportDownloaderComponent;
    //authUser$: User;
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;
    employerId: number = 0;
    projects: Array<Project> = [];
    records: Array<any> = [];
    userFile: any;
    authUser$: User;
    projectsLoading: boolean = false;
    employer: any = {};
    downloadDeliveryNotesReportLoading: boolean = false;
    loadingPostcode: boolean = false;
    formattedFromDate: string = null;
    formattedToDate: string = null;
    reportTitle: string = 'Delivery Notes Report';
    selectedDeliveryNotes: Array<any> = [];
    deliveryNotesBetweenDuration: Array<any> = [];
    allAvailableDeliveryNotes: Array<any> = [];
    temp_records: Array<any> = [];
    is_inherited_project: boolean = false;
    loadingDeliveryNotes: boolean = false;
    tableOffset: number = 0;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    deliveryOrCollectionNoteObj: DeliveryCollectionNote = new DeliveryCollectionNote();  // used for new delivery-note and collection-note
    deliveryOrCollectionNoteViewObj: DeliveryCollectionNote = new DeliveryCollectionNote();  // used for view delivery-note and collection-note
    paginationData = new Common();
    page = this.paginationData.page;
    search:any = null;
    dnImages: Array<any> = [{}];
    dnImageIds: Array<any> = [];
    minDate: NgbDateStruct = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs().add(1,'day'));
    dnPhrase: string = ``;
    dnSglrPhrase: string = ``;
    cnPhrase: string = ``;
    cnSglrPhrase: string = ``;
    featurePhrase: string = ``;
    featureSglrPhrase: string = ``;
    featureId: string = 'delivery-note';
    imagesArray:  Array<any> = [];
    isFolderDownload: boolean = false;
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    isProjectPortal: boolean = false;
    isMobileDevice: boolean = false;
    loadOptions = ['Full load', 'Part load'];
    showDistanceTravelled: boolean = false;
    distanceTravelledInKm = null;
    isAllSelected: boolean = true;
    isInitDeliveryCollectionNote: boolean = false;
    loadingInlineDeliveryCollectionNote: boolean = false;
    baseButtonConfig: Record<string, ActionBtnEntry[]> = {};

    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private deliveryNotesService: DeliveryNotesService,
        private modalService: NgbModal,
        private authService: AuthService,
        private projectService: ProjectService,
        private userService: UserService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private httpService: HttpService,
        private toastService: ToastService,
    ) {
        this.from_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.to_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
            console.log("delivery-note projectId: ", this.projectId);
        }
    }

    dayjs(n: number) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };

    dayjsDisplayFull(n: number) {
        return this.dayjs(n).format( AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS);
    };

    unix(n) {
        if(n) {
            return dayjs(+n).format(AppConstant.defaultDateFormat);
        } else {
            return '';
        }
    }

    ngOnInit() {
        if (this.activatedRoute.snapshot.data.toolkey && this.activatedRoute.snapshot.data.toolkey == "collection_note") {
            this.featureId = 'collection-note'
        }
        if(!this.isProjectPortal) {
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
                this.employerId = params['employerId'];
            });
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        } else {
            this.initializeTable();
        }
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });
    }

    // Will get called for Company route only.
    projectRetrieved(project: Project){
        this.projectInfo = project;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
        this.initializeTable();
    }

    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean) {
        if (!this.isInitDeliveryCollectionNote) {
            this.isInitDeliveryCollectionNote = true;
            return;
        }
        this.page.pageNumber = pageInfo.offset;
        this.initializeTable(isPageChange);
    }

    private initializeTable(isPageChange?: boolean) {
        if (isPageChange) {
            this.loadingInlineDeliveryCollectionNote = true;
        } else {
            this.loadingDeliveryNotes = true;
        }
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('is_inherited_project', `${this.is_inherited_project}`)
            .set('data_type', `${this.featureId}`);
        if(this.search !== null && this.search !== ''){
            params = params.append('search', `${this.search}`);
        }

        this.dnPhrase = this.projectInfo ? this.projectInfo.custom_field.dn_phrase : '';
        this.dnSglrPhrase = this.projectInfo ? this.projectInfo.custom_field.dn_phrase_singlr : '';
        this.cnPhrase = this.projectInfo ? this.projectInfo.custom_field.cn_phrase : '';
        this.cnSglrPhrase = this.projectInfo ? this.projectInfo.custom_field.cn_phrase_singlr : '';
        this.featurePhrase = (this.featureId == 'delivery-note') ? this.dnPhrase : this.cnPhrase;
        this.featureSglrPhrase = (this.featureId == 'delivery-note') ? this.dnSglrPhrase : this.cnSglrPhrase;
        this.from_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(this.projectInfo.createdAt));

        let companyId = (this.employerId ? this.employerId : null);
        this.deliveryNotesService.getDeliveryNotes(this.projectId, companyId, params).subscribe((data:any) => {
            this.loadingDeliveryNotes = false;
            this.loadingInlineDeliveryCollectionNote = false;
            if (data && data.delivery_notes) {
                this.records = data.delivery_notes;
                this.temp_records = data.delivery_notes;
                this.page.totalElements = data.total_record_count;
                this.getRowButtonGroup(this.records);
                return data.delivery_notes;
            }
            const message = `Failed to fetch delivery notes, id: ${this.projectId}.`;
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            return [];
        });
    }

    getRowButtonGroup(records: any[]): void {
        const BASE_BUTTON_CONFIG: Array<ActionBtnEntry> = [
            {
                key: 'view',
                label: '',
                title: 'View Progress Photos',
                mat_icon: 'search'
            },
            {
                key: 'download', 
                label: '',
                title: 'Download Progress Photos',
                mat_icon: 'download',
                children: []
            }
        ];

        records.forEach(row => {
            if (!row?.id) return;
            const photos = this.photoDownloadOpts(row.dn_images || []);
            const downloadOptions = [
                {
                    key: 'download_pdf',
                    label: 'Download PDF',
                    title: 'Download PDF',
                    mat_icon: ''
                },
                ...this.createPhotoDownloadOptions(photos)
            ];

            const updatedButtonConfig = [
                BASE_BUTTON_CONFIG[0],
                {
                    ...BASE_BUTTON_CONFIG[1],
                    children: downloadOptions
                }
            ];
            this.baseButtonConfig[row.id] = updatedButtonConfig;
        });
    }

    photoDownloadOpts(array){
        return array.sort((a, b) => b?.id - a?.id);
    }

    createPhotoDownloadOptions(photos: any[]): Array<ActionBtnEntry> {
        return photos.map((photo, index) => ({
            key: `download_${photo.id}`,
            label: `Download ${(photo?.file_mime == "application/pdf") ? 'Attached PDF' : 'Image'} ${index+1}`,
            title: photo.file_url || '',
        }));
    }

    rowBtnClicked({entry}: {entry: ActionBtnEntry}, row: any): void {
        const actionMap = {
            'view': () => this.deliveryNotesModal(row),
            'download_pdf': () => this.downloadDeliveryNotes(row)
        };

        // Check if action exists in map
        if (actionMap[entry.key]) {
            actionMap[entry.key]();
            return;
        }

        // Handle individual photo downloads
        const photos = this.photoDownloadOpts(row?.dn_images || []);
        const downloadOptions = this.createPhotoDownloadOptions(photos);
        
        const matchingOption = downloadOptions.find(opt => opt.key === entry.key);
        if (matchingOption) {
            this.downloadImage(matchingOption.title, matchingOption.label);
        }
    }

    @ViewChild('deliveryOrCollectionModalRef')
    private deliveryOrCollectionModalRef: IModalComponent;
    deliveryNotesModal(row){
        this.imagesArray = [];
        (row.dn_images || []).map(file => {
            if (file.img_translation && file.img_translation.length) {
                this.imagesArray.push(...file.img_translation);
            } else if (file.file_url && file.file_mime != 'application/pdf') {
                this.imagesArray.push(file.file_url);
            }
        });
        this.imagesArray = this.imagesArray.map(img => {return {"file_url": img}});
        this.deliveryOrCollectionNoteViewObj = {...row};
        return this.deliveryOrCollectionModalRef.open();
    }

    downloadDeliveryNotes(row) {
        this.downloadDeliveryNotesReportLoading = true;
        let body = {
            createdAt: row.createdAt,
            timezone: this.authUser$.timezone,
            companyId: this.employerId
        };

        this.deliveryNotesService.downloadDeliveryNotes(this.projectId, body, row.id, () => {
            this.downloadDeliveryNotesReportLoading = false;
        });
    }

    downloadDeliveryNotesReport() {
        let projectId = this.projectId;
        let fromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.from_date);
        let toDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.to_date);
        if(!fromDate || !toDate) {
            const message = 'Please enter from date and to date.';
            this.toastService.show(this.toastService.types.INFO, message);
            return false;
        }

        if(toDate.isBefore(fromDate)) {
            const message = 'Incorrect {from,to} date.';
            this.toastService.show(this.toastService.types.INFO, message);
            return false;
        }

        let formattedFromDate = fromDate.format(AppConstant.apiRequestDateFormat);
        let formattedToDate = toDate.format(AppConstant.apiRequestDateFormat);
        let companyId = (this.employerId ? this.employerId : null);
        let request = {
            projectId,
            fromDate: formattedFromDate,
            toDate: formattedToDate,
            companyId: companyId,
            is_inherited_project: this.is_inherited_project,
            data_type: this.featureId,
        };
        this.downloadDeliveryNotesReportLoading = true;
        this.deliveryNotesService.downloadDeliveryNotesReportXLSX(this.projectId, request, `${this.featurePhrase}-report-${projectId}-[${fromDate.format(AppConstant.defaultDateFormat)}-${toDate.format(AppConstant.defaultDateFormat)}].xlsx`, () => {
            this.downloadDeliveryNotesReportLoading = false;
        });
    }

    @ViewChild('downloadReportConfirmationRef')
    private downloadReportConfirmationRef: IModalComponent;
    initDownloadReportConfirmationModal() {
        this.formattedFromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.from_date).format(AppConstant.displayDateFormat);
        this.formattedToDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.to_date).format(AppConstant.displayDateFormat);

        let projectId = this.projectId;
        this.reportTitle = `${this.featurePhrase} Report`;
        let fromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.from_date);
        let toDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.to_date);
        if(!fromDate || !toDate) {
            const message = 'Please enter from date and to date.';
            this.toastService.show(this.toastService.types.INFO, message);
            return false;
        }

        if(toDate.isBefore(fromDate)) {
            const message = 'Incorrect {from,to} date.';
            this.toastService.show(this.toastService.types.INFO, message);
            return false;
        }

        let formattedFromDate = fromDate.format(AppConstant.apiRequestDateFormat);
        let formattedToDate = toDate.format(AppConstant.apiRequestDateFormat);
        let companyId = (this.employerId ? this.employerId : null);
        let request = {
            projectId,
            fromDate: formattedFromDate,
            toDate: formattedToDate,
            companyId: companyId,
            is_inherited_project: this.is_inherited_project,
            data_type: this.featureId,
        };
        this.downloadDeliveryNotesReportLoading = true;
        this.deliveryNotesService.fetchDeliveryNotesReportRecords(request, projectId).subscribe((data:any) => {
            this.downloadDeliveryNotesReportLoading = false;
            if (data && data.all_delivery_notes) {
                this.deliveryNotesBetweenDuration = data.all_delivery_notes;
                this.selectedDeliveryNotes = [...data.images_id];
                this.allAvailableDeliveryNotes = [...data.images_id];
                return;
            }
            const message = `Failed to fetch delivery notes between these duration, id: ${this.projectId}.`;
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            return [];
        });
        return this.downloadReportConfirmationRef.open();
    }

    @ViewChild('addDeliveryOrCollectionModalRef')
    private addDeliveryOrCollectionModalRef: IModalComponent;
    public openNewDeliveryOrCollectionNoteModal() {
        this.resetDeliveryOrCollectionNoteModal(); // function for reset input values
        this.showDistanceTravelled = false;
        return this.addDeliveryOrCollectionModalRef.open();
    }

    /**
     * to add delivery or collection based on condition
     */
    public saveDeliveryOrCollectionNote(): void {
        this.deliveryOrCollectionNoteObj.dn_images = (this.dnImages || []) .filter(m => m?.id).map(m => m.id);
        if(this.deliveryOrCollectionNoteObj.dn_delivered_on){
            const dayJsDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.deliveryOrCollectionNoteObj.dn_delivered_on).format(AppConstant.apiRequestDateFormat);
            const dateString = dayjs(dayJsDate).valueOf();
            this.deliveryOrCollectionNoteObj.dn_delivered_on = dateString;
        }
        this.downloadDeliveryNotesReportLoading = true;
        if(this.featureId === 'delivery-note'){
            this.createNewDeliveryNote();
        } else {
            this.createNewCollectionNote();
        }
    }

    /**
     * to add new delivery-note
     */
    private createNewDeliveryNote(): void {
        this.deliveryNotesService.addDeliveryNotes(this.deliveryOrCollectionNoteObj).subscribe(res => {
            if(res.success){
                const message = 'Delivery note created successfully.';
                this.toastService.show(this.toastService.types.SUCCESS, message);
                this.closeModalAndTableInit();
            } else {
                const message = res.message || 'Failed to save information.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        }).add(() => this.downloadDeliveryNotesReportLoading = false);
    }

    /**
     * to add new collection-note
     */
    private createNewCollectionNote(): void {
        this.deliveryNotesService.addCollectionNotes(this.deliveryOrCollectionNoteObj).subscribe(res => {
            if(res.success){
                const message = 'Collection note created successfully.'
                this.toastService.show(this.toastService.types.SUCCESS, message);
                this.closeModalAndTableInit();
            } else {
                const message = res.message || 'Failed to save information.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        }).add(() => this.downloadDeliveryNotesReportLoading = false);
    }

    private closeModalAndTableInit(): void {
        this.modalService.dismissAll();
        this.initializeTable(true);
    }

    private resetDeliveryOrCollectionNoteModal(): void {
        this.dnImages = [{}];
        this.deliveryOrCollectionNoteObj = new DeliveryCollectionNote();
        this.deliveryOrCollectionNoteObj.project_ref = this.projectId;
    }

    async initiateDownload(resp) {
        this.from_date = resp.fromDate;
        this.to_date = resp.toDate;
        if(resp.type === 'pdf' || resp.type === 'folder') {
            this.isFolderDownload = (resp.type === 'folder');
            this.initDownloadReportConfirmationModal();
        } else if (resp.type === 'xlsx') {
            this.downloadDeliveryNotesReport();
        }
    }

    downloadDeliveryNotesReportPdf(event) {
        if (!this.selectedDeliveryNotes.length) {
            const message = 'Please select photos to download the report.';
            this.toastService.show(this.toastService.types.INFO, message);
            return false;
        }
        let projectId = this.projectId;
        let fromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.from_date);
        let toDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.to_date);
        if(!fromDate || !toDate) {
            const message = 'Please enter from date and to date.'
            this.toastService.show(this.toastService.types.INFO, message);
            return false;
        }

        if(toDate.isBefore(fromDate)) {
            const message = 'Incorrect {from,to} date.';
            this.toastService.show(this.toastService.types.INFO, message);
            return false;
        }

        let formattedFromDate = fromDate.format(AppConstant.apiRequestDateFormat);
        let formattedToDate = toDate.format(AppConstant.apiRequestDateFormat);
        let companyId = (this.employerId ? this.employerId : null);
        let fileName = `${this.featurePhrase}-report-${projectId}-[${fromDate.format(AppConstant.defaultDateFormat)}-${toDate.format(AppConstant.defaultDateFormat)}]`;
        let request = {
            projectId,
            fromDate: formattedFromDate,
            toDate: formattedToDate,
            companyId: companyId,
            is_inherited_project: this.is_inherited_project,
            reportTitle: this.reportTitle,
            selectedDeliveryNotes: this.selectedDeliveryNotes,
            isSiteAdmin: this.isProjectPortal,
            data_type: this.featureId,
            fileName: fileName,
            isFolderDownload: this.isFolderDownload
        };
        this.downloadDeliveryNotesReportLoading = true;
        if(this.isFolderDownload) {
            this.deliveryNotesService.downloadDeliveryNotesImgsZip(this.projectId, request, `${fileName}.zip`, () => {
                event.closeFn();
                this.downloadDeliveryNotesReportLoading = false;
            });
        } else {
            this.deliveryNotesService.downloadDeliveryNotesReport(this.projectId, request, `${fileName}.pdf`, () => {
                event.closeFn();
                this.downloadDeliveryNotesReportLoading = false;
            });
        }
    }

    toggleImageSelection(imageId, event) {
        if (event.target.checked) {
            this.selectedDeliveryNotes.push(imageId);
        } else {
            let index = this.selectedDeliveryNotes.indexOf(imageId);
            if (index > -1) {
                this.selectedDeliveryNotes.splice(index, 1);
            }
        }
    
        this.isAllSelected = this.selectedDeliveryNotes.length === this.allAvailableDeliveryNotes.length;
    }
    
    toggleAllSelection(event) {     
        if (event.target.checked) {
            this.selectedDeliveryNotes = [...this.allAvailableDeliveryNotes];
        } else {
            this.selectedDeliveryNotes = [];
        }    
        this.isAllSelected = event.target.checked;
    }

    ngAfterViewInit() {

    }

    imageUrl(url){
        return '/uploads/'+url.split('/uploads/')[1];
    }

    downloadImage(fileUrl, fileName) {
        fs.saveAs(fileUrl, fileName);
    }

    @ViewChild('editDeliveryOrCollectionModalRef')
    private editDeliveryOrCollectionModalRef: IModalComponent;
    async editDeliveryNotesModal(row) {
        this.deliveryOrCollectionNoteObj = {...row};
        this.deliveryOrCollectionNoteObj['_dn_delivered_on'] = this.deliveryOrCollectionNoteObj['dn_delivered_on'] ? this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+this.deliveryOrCollectionNoteObj['dn_delivered_on'])) : null;
        this.dnImageIds = row.dn_images;
        this.dnImages = [{}, ...row.dn_images];
        this.showDistanceTravelled = (this.deliveryOrCollectionNoteObj.dn_dispatch_postcode && this.deliveryOrCollectionNoteObj.dn_load_type) ? true: false;
        this.calculateDistanceTravelled();
        return this.editDeliveryOrCollectionModalRef.open();
    }

    editDeliveryNotesRequest() {
        let request = this.deliveryOrCollectionNoteObj;
        request.dn_images = (this.dnImages || []).reduce((arr, m) => {
            if (m.id) {
                arr.push(m.id);
            }
            return arr;
        }, []);
        request.dn_delivered_on = request._dn_delivered_on ? this.ngbMomentjsAdapter.ngbDateToDayJs(request._dn_delivered_on).valueOf() : null;
        this.downloadDeliveryNotesReportLoading = true;
        this.deliveryNotesService.updateDeliveryNotes(this.projectId, request, request.id).subscribe(out => {
            if(!out.success){
                const message = out.message || 'Failed to store data';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
            } else {
                const message = `Your ${this.featureSglrPhrase} details have been updated successfully.`;
                this.toastService.show(this.toastService.types.SUCCESS, message);
            }
            this.downloadDeliveryNotesReportLoading = false;
            this.modalService.dismissAll();
            this.initializeTable(true);
        });
    }

    attachmentUploadDone($event) {
        if ($event && $event.userFile) {
            this.dnImages.splice(1, 0, ...$event.userFile);
            this.dnImages[0] = {};
        }
    }

    fileDeleteDone($event) {
        if ($event && $event.userFile && $event.userFile.id) {
            this.dnImages = this.dnImages.filter(r => (r.id !== $event.userFile.id));
        }
    }

    removeAttachment(file, index) {
       this.confirmationModalRef.openConfirmationPopup({
        headerTitle: 'Delete',
        title: `Are you sure you want to delete this?`,
        confirmLabel: 'Delete',
        onConfirm: () => {
            this.userService.deleteUserFile(file.id).subscribe(data => {
                if (data.success) {
                    this.dnImages.splice(index, 1);
                } else {
                    const message = data.message || 'Failed to delete the file.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                }
            });
        }
    });
    }

    resetPostcodeDistanceData() {
        this.deliveryOrCollectionNoteObj.dn_dispatch_postcode = null;
        this.deliveryOrCollectionNoteObj.dn_load_type = null;
        this.deliveryOrCollectionNoteObj.dn_distance_travelled = null;
        this.deliveryOrCollectionNoteObj.dn_load_percentage = null;
        this.showDistanceTravelled = false;
        this.deliveryOrCollectionNoteObj.dn_total_distance_travelled = null;
    }

    validatePostcode(event, postCodeValue, pPostcode){
        const trimmedPostcode = postCodeValue?.trim();
        if (!trimmedPostcode) {
          this.resetPostcodeDistanceData();
          return;
        }

        if (postCodeValue.length < 3) return;
        this.loadingPostcode = true;
        const countryCode = this.projectInfo?.custom_field?.country_code;
        this.projectService.validatePostcode(trimmedPostcode, countryCode).subscribe((response: {success:boolean, latLongData: ProjectLocation }) => {
            this.loadingPostcode = false;
            if (response.success && response.latLongData) {
                pPostcode.control.setErrors(null);
                this.getDistanceTravelled(event, trimmedPostcode);
            } else {
                this.deliveryOrCollectionNoteObj.dn_distance_travelled = null;
                pPostcode.control.setErrors({ valid: true });
            }
        });
    }

    getDistanceTravelled($event, postcode) {
        if (postcode && !($event.currentTarget && $event.currentTarget.contains($event.relatedTarget))) {
            this.downloadDeliveryNotesReportLoading = true;
            let req = {
                origin_postcode: postcode,
                destination_postcode: this.projectInfo.postcode,
                project_country_code: (this.projectInfo.custom_field && this.projectInfo.custom_field.country_code)
            }
            this.projectService.getDistanceBwPostcodes(req).subscribe(data => {
                this.downloadDeliveryNotesReportLoading = false;
                if (data.success && data.distance_matrix && data.distance_matrix.status == 'OK') {
                    this.deliveryOrCollectionNoteObj.dn_distance_travelled = data.distance_matrix.distance.value;
                    this.calculateDistanceTravelled();
                } else {
                    this.resetPostcodeDistanceData();
                    const message = 'Failed to fetch distance based on the postcode.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                }
            });
        } else if(!postcode) {
            this.resetPostcodeDistanceData();
        }
    }

    calculateDistanceTravelled() {
        this.showDistanceTravelled = true;
        let distanceInM = this.deliveryOrCollectionNoteObj.dn_distance_travelled;
        let travelledDistance = distanceInM * 2; //for return journey
        this.deliveryOrCollectionNoteObj.dn_total_distance_travelled = Math.round(travelledDistance);
        this.distanceTravelledInKm = Number(travelledDistance / 1000).toFixed(2) + ' km';
        console.log("calculateDistanceTravelled: ", this.distanceTravelledInKm);
    }

    parseDistanceTravelled(distance) {
        return Number(distance / 1000).toFixed(2) + ' km';
    }

    checkLoadType(loadType) {
        this.deliveryOrCollectionNoteObj.dn_load_percentage = 50;
        if(loadType === 'Full load') {
            this.deliveryOrCollectionNoteObj.dn_load_percentage = null;
        }
        return;
    }
    searchFunction(data){
        this.search = data.search.toLowerCase();
        this.pageCallback({offset: 0}, true);
    }

    public openDeliveryNotesReportModal() {
        this.reportDownloader.openModal();
    }

    async deliveryNotesReportDownload(event) {
        await this.initiateDownload(event.selection);
        event.closeFn();
    }

    public closeModal(event) {
        event.closeFn();
    }
}

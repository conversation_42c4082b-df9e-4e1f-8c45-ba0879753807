<style>
    .deliveryNotesTable.table tbody tr td:nth-child(4) { max-width: 250px !important; word-break: break-all; }  
</style>
<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav *ngIf="!isProjectPortal" [employer]="employer" [companyResolverResponse]="companyResolverResponse" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div class="col-sm-12 my-3 outer-border" *ngIf="!loadingDeliveryNotes">
                <div class="col-sm-12 outer-border-radius">
                <div class="d-flex flex-wrap justify-content-between mb-2 gap-8">
                    <h5 class="float-left mb-0">
                        <span>Total {{ featurePhrase }} <small>({{ page.totalElements }})</small></span>
                    </h5>
                    <div class="gap-8 d-flex flex-column flex-grow-1 flex-sm-row justify-content-end">
                        <action-button
                            [newFeatureTitle]="'New ' + this.featureSglrPhrase"
                            [showActionDropdown]="false"
                            (onOpenAddNew)="openNewDeliveryOrCollectionNoteModal()">
                        </action-button>
                        <button class="btn btn-sm btn-brandeis-blue other-action-btn justify-content-center d-flex align-items-center pointer-cursor" (click)="openDeliveryNotesReportModal()" id="dropdownDlReport1">
                            <span class="medium-font m-font-size material-symbols-outlined mr-2">download</span>
                            <div class="medium-font m-font-size">Download Report</div>
                        </button>
                    </div>
                </div>
                <div>
                    <search-with-filters (searchEmitter)="searchFunction($event)"></search-with-filters>
                </div>
                <div class="table-responsive-sm">
                    <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                                [scrollbarV]="true"
                                [virtualization]="false"
                                [loadingIndicator]="loadingInlineDeliveryCollectionNote"
                                [rows]="records"
                                [footerHeight]="40"
                                [columnMode]="'force'"
                                [rowHeight]="'auto'"
                                [externalPaging]="true"
                                [count]="page.totalElements"
                                [offset]="page.pageNumber"
                                [limit]="page.size"
                                (page)="pageCallback($event, true)"
                >
                    <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Date Submitted
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            {{ dayjsDisplayFull(row.createdAt)}}
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Submitted By
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <span appTooltip>{{row?.user_ref?.name}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" cellClass="cell-word-wrap" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            PO Number
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <span appTooltip>{{row?.po_number}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" cellClass="cell-word-wrap" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Supplier
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <span appTooltip>{{row.dn_supplier}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold" [sortable]="false" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            {{ (featureId == 'delivery-note') ? 'Delivery' : 'Collection' }} Date
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <span> {{ unix(row.dn_delivered_on) }} </span>
                        </ng-template>
                    </ngx-datatable-column>

                    <ngx-datatable-column headerClass="font-weight-bold action-column" cellClass="action-column no-ellipsis" [sortable]="false" minWidth="100">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Action
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <button-group
                                [buttons]="baseButtonConfig[row.id]"
                                (onActionClick)="rowBtnClicked($event, row)">
                            </button-group>
                        </ng-template>
                    </ngx-datatable-column>
                    </ngx-datatable>
                </div>
                <div class="clearfix"></div>
            </div>
            </div>
        </div>
    </div>
</div>
<i-modal #deliveryOrCollectionModalRef [title]="(deliveryOrCollectionNoteViewObj.dn_supplier ? deliveryOrCollectionNoteViewObj.dn_supplier + ' - ' : '') + featureSglrPhrase" size="lg" [showCancel]="false" rightSecondaryBtnTxt="Edit" (onClickRightSB)="editDeliveryNotesModal(deliveryOrCollectionNoteViewObj)" (onClickRightPB)="closeModal($event)" rightPrimaryBtnTxt="OK">
        <table class="table table-sm table-bordered" style="font-size: 14px;">
            <tbody>
            <tr>
                <td class="tr-bg-dark-color" style="width: 25%;">
                    <strong>Date Submitted:</strong>
                </td>
                <td>
                    {{ dayjsDisplayFull(deliveryOrCollectionNoteViewObj.createdAt) }}
                </td>
            </tr>
            <tr>
                <td class="tr-bg-dark-color" style="width: 25%;">
                    <strong>Submitted By:</strong>
                </td>
                <td>
                    {{ deliveryOrCollectionNoteViewObj?.user_ref?.name }}
                </td>
            </tr>
            <tr *ngIf="deliveryOrCollectionNoteViewObj.dn_delivered_on">
                <td class="tr-bg-dark-color" style="width: 25%;">
                    <strong>{{ (featureId == 'delivery-note') ? 'Delivery' : 'Collection' }} Date:</strong>
                </td>
                <td>
                    {{ unix(deliveryOrCollectionNoteViewObj.dn_delivered_on) }}
                </td>
            </tr>
            <tr *ngIf="deliveryOrCollectionNoteViewObj.po_number">
                <td class="tr-bg-dark-color" style="width: 25%;">
                    <strong>PO Number:</strong>
                </td>
                <td>
                    {{ deliveryOrCollectionNoteViewObj.po_number }}
                </td>
            </tr>
            <tr *ngIf="deliveryOrCollectionNoteViewObj.delivery_ref_no">
                <td class="tr-bg-dark-color" style="width: 25%;">
                    <strong>{{ (featureId == 'delivery-note') ? 'Delivery' : 'Collection' }} Ref./No:</strong>
                </td>
                <td>
                    {{ deliveryOrCollectionNoteViewObj.delivery_ref_no }}
                </td>
            </tr>
            <tr *ngIf="deliveryOrCollectionNoteViewObj.dn_supplier">
                <td class="tr-bg-dark-color" style="width: 25%;">
                    <strong>Supplier:</strong>
                </td>
                <td>
                    {{ deliveryOrCollectionNoteViewObj.dn_supplier }}
                </td>
            </tr>
            <tr *ngIf="deliveryOrCollectionNoteViewObj.dn_location">
                <td class="tr-bg-dark-color" style="width: 25%;">
                    <strong>Location:</strong>
                </td>
                <td>
                    {{ deliveryOrCollectionNoteViewObj.dn_location }}
                </td>
            </tr>
            <tr *ngIf="deliveryOrCollectionNoteViewObj.dn_dispatch_postcode">
                <td class="tr-bg-dark-color" style="width: 25%;">
                    <strong>Dispatch Postcode:</strong>
                </td>
                <td>
                    {{ deliveryOrCollectionNoteViewObj.dn_dispatch_postcode }}
                </td>
            </tr>
            <tr *ngIf="deliveryOrCollectionNoteViewObj.dn_dispatch_postcode">
                <td class="tr-bg-dark-color" style="width: 25%;">
                    <strong>Full/Part Load:</strong>
                </td>
                <td>
                    {{ deliveryOrCollectionNoteViewObj.dn_load_type }} <span *ngIf="deliveryOrCollectionNoteViewObj.dn_load_type == 'Part load'"> ({{deliveryOrCollectionNoteViewObj.dn_load_percentage}}%) </span>
                </td>
            </tr>
            <tr *ngIf="deliveryOrCollectionNoteViewObj.dn_distance_travelled">
                <td class="tr-bg-dark-color" style="width: 25%;">
                    <strong>Distance Travelled:</strong>
                </td>
                <td *ngIf="deliveryOrCollectionNoteViewObj.dn_load_type == 'Part load'">
                    {{ parseDistanceTravelled(deliveryOrCollectionNoteViewObj.dn_total_distance_travelled) }} ({{parseDistanceTravelled(deliveryOrCollectionNoteObj.dn_distance_travelled*2)}} total, including return journey)
                </td>
                <td *ngIf="deliveryOrCollectionNoteViewObj.dn_load_type == 'Full load'">
                    {{ parseDistanceTravelled(deliveryOrCollectionNoteViewObj.dn_total_distance_travelled) }} (Including return journey)
                </td>
            </tr>
            <tr *ngIf="deliveryOrCollectionNoteViewObj.dn_description">
                <td class="tr-bg-dark-color" style="width: 25%;">
                    <strong>Description:</strong>
                </td>
                <td>
                    <span class="text-break">{{ deliveryOrCollectionNoteViewObj.dn_description }}</span>
                </td>
            </tr>
            <tr *ngIf="deliveryOrCollectionNoteViewObj?.dn_images && deliveryOrCollectionNoteViewObj?.dn_images.length">
                <td colspan="2">
                    <pop-up-image-viewer [alt]="featureId+ ' Note Image'" [imgArray]="imagesArray || []"></pop-up-image-viewer>
                </td>
            </tr>
            </tbody>
        </table>
</i-modal>

<!-- Open download report confirmation modal box -->
<i-modal #downloadReportConfirmationRef [title]="((isFolderDownload) ? 'Folder Download' : featurePhrase +' Report') + ': ' + formattedFromDate + ' - ' + formattedToDate" size="lg"
    (onClickRightPB)="downloadDeliveryNotesReportPdf($event)" [rightPrimaryBtnTxt]="deliveryNotesBetweenDuration.length ? ('Download ' + ((isFolderDownload) ? 'Folder' : 'Report')) : ''"
    [cancelBtnText]="'Close'">
        <div class="row" *ngIf="deliveryNotesBetweenDuration.length">
            <div *ngIf="!isFolderDownload" class="w-100">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label>Report Title</label>
                        <input type="text" class="form-control" required [(ngModel)]="reportTitle" name="reportTitle"
                               placeholder="Enter Report Title" maxlength="40"/>
                    </div>
                </div>
            </div>

            <div class="col-sm-12">
                <div class="form-group">
                    <label>{{ featurePhrase }}</label>
                    <table class="table table-bordered deliveryNotesTable">
                        <thead>
                        <th>
                            <div class="custom-control custom-checkbox p-right">
                                <input type="checkbox" class="custom-control-input"
                                       id="toggleAllSelection"
                                       (change)="toggleAllSelection($event)" [checked]="isAllSelected">
                                <label class="custom-control-label" for="toggleAllSelection">All</label>
                            </div>
                        </th>
                        <th>Image</th>
                        <th>{{ (featureId == 'delivery-note') ? 'Delivery' : 'Collection' }} Date</th>
                        <th>Supplier</th>
                        <th style="min-width: 125px;">Submitted By</th>
                        </thead>
                        <tbody>
                        <tr *ngFor="let deliveryNote of deliveryNotesBetweenDuration">
                            <td>
                                <div class="custom-control custom-checkbox p-right form-group">
                                    <input type="checkbox" class="custom-control-input"
                                           name="{{'image ' + deliveryNote?.image?.id}}"
                                           id="{{'image ' + deliveryNote?.image?.id}}" ng-value="deliveryNote?.image?.id"
                                           (click)="toggleImageSelection(deliveryNote?.image?.id, $event)" [checked]="selectedDeliveryNotes.includes(deliveryNote?.image?.id)">
                                    <label class="custom-control-label" for="{{'image ' + deliveryNote?.image?.id}}"></label>
                                </div>
                            </td>
                            <td class="text-center">
                                <div style="width: 180px;">
                                    <ng-container *ngIf="deliveryNote?.image?.img_translation?.length">
                                        <img [src]="deliveryNote?.image?.img_translation[0]" alt="{{ (featureId == 'delivery-note') ? 'Delivery' : 'Collection' }} Notes - Image" style="width: 100%; height: auto;">
                                    </ng-container>
                                    <ng-container *ngIf="deliveryNote?.image?.file_url && !deliveryNote?.image?.img_translation?.length">
                                        <img [src]="deliveryNote?.image?.file_url" alt="{{ (featureId == 'delivery-note') ? 'Delivery' : 'Collection' }} Notes - Image" style="width: 100%; height: auto;">
                                    </ng-container>
                                </div>
                            </td>
                            <td>{{ unix(deliveryNote.dn_delivered_on) }}</td>
                            <td>{{deliveryNote.dn_supplier}}</td>
                            <td style="min-width: 125px;">{{deliveryNote?.user_ref?.name}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="row" *ngIf="!deliveryNotesBetweenDuration.length">
            <div class="col-sm-12 text-center font-weight-bold">
                No {{ featurePhrase }} available between the duration.
            </div>
        </div>
</i-modal>

<i-modal #editDeliveryOrCollectionModalRef [title]="'Edit ' + featureSglrPhrase + ' - ' + deliveryOrCollectionNoteObj?.dn_supplier" size="md" (onClickRightPB)="editDeliveryNotesRequest()" rightPrimaryBtnTxt="Save"
        [rightPrimaryBtnDisabled]="!editDeliveryNotesForm.valid">
        <form novalidate #editDeliveryNotesForm="ngForm">

            <div class="form-group">
                <label>{{ (featureId == 'delivery-note') ? 'Delivery' : 'Collection' }} Date</label>
                <div class="input-group">
                    <input class="form-control" placeholder="DD-MM-YYYY" readonly
                           name="_dn_delivered_on" [(ngModel)]="deliveryOrCollectionNoteObj._dn_delivered_on" ngbDatepicker
                           #dn_delivered_on="ngbDatepicker" ng-value="deliveryOrCollectionNoteObj._dn_delivered_on"
                    />
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary calendar" (click)="dn_delivered_on.toggle()" type="button">
                            <i class="fa fa-calendar"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>PO Number</label>
                <div class="input-group mb-3">
                    <input type="text" class="form-control" #po_number="ngModel" ng-value="deliveryOrCollectionNoteObj.po_number"
                           placeholder="PO Number" name="po_number" [(ngModel)]="deliveryOrCollectionNoteObj.po_number">
                </div>
            </div>

            <div class="form-group">
                <label>{{ (featureId == 'delivery-note') ? 'Delivery' : 'Collection' }} Ref./No.</label>
                <div class="input-group mb-3">
                    <input type="text" class="form-control" #delivery_ref_no="ngModel" ng-value="deliveryOrCollectionNoteObj.delivery_ref_no"
                           placeholder="{{ (featureId == 'delivery-note') ? 'Delivery' : 'Collection' }} Ref./No." name="delivery_ref_no" [(ngModel)]="deliveryOrCollectionNoteObj.delivery_ref_no">
                </div>
            </div>

            <div class="form-group">
                <label>Supplier<small class="required-asterisk">*</small></label>
                <div class="input-group mb-3">
                    <input type="text" class="form-control" #dn_supplier="ngModel" ng-value="deliveryOrCollectionNoteObj.dn_supplier"
                           placeholder="Supplier" name="dn_supplier" required [(ngModel)]="deliveryOrCollectionNoteObj.dn_supplier">
                </div>
            </div>
            <div class="form-group">
                <label>Location</label>
                <div class="input-group mb-3">
                    <input type="text" class="form-control" truncate #dn_location="ngModel" ng-value="deliveryOrCollectionNoteObj.dn_location"
                           placeholder="Location" name="dn_location" [(ngModel)]="deliveryOrCollectionNoteObj.dn_location">
                </div>
            </div>
            <div class="form-group">
                <label>Dispatch Postcode</label>
                <input type="text" class="form-control" truncate [(ngModel)]="deliveryOrCollectionNoteObj.dn_dispatch_postcode" name="dn_dispatch_postcode"
                    placeholder="Dispatch Postcode" #dispatchPostcode="ngModel" (focusout)="validatePostcode($event, deliveryOrCollectionNoteObj.dn_dispatch_postcode, dispatchPostcode)"/>
                <div class="alert alert-danger" [hidden]="!(dispatchPostcode.errors && dispatchPostcode.errors.valid)">Invalid Post Code.</div>
                <div class="mt-2 small text-muted" *ngIf="deliveryOrCollectionNoteObj.dn_distance_travelled">Distance Travelled: {{ distanceTravelledInKm }} (Including return journey)</div>
            </div>
            <div class="form-group" *ngIf="deliveryOrCollectionNoteObj.dn_dispatch_postcode">
                <label>Full/Part Load<small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" placeholder="Select Full/Part Load" appendTo="body" [items]="loadOptions" name="dn_load_type" [(ngModel)]="deliveryOrCollectionNoteObj.dn_load_type" style="width: 100%;" [required]="deliveryOrCollectionNoteObj.dn_dispatch_postcode" (change)="checkLoadType(deliveryOrCollectionNoteObj.dn_load_type)">
                </ng-select>
            </div>
            <div class="form-group" *ngIf="deliveryOrCollectionNoteObj.dn_load_type === 'Part load'">
                <label class="float-right">{{deliveryOrCollectionNoteObj.dn_load_percentage}} %</label>
                <input type="range" min="0" max="100" [(ngModel)]="deliveryOrCollectionNoteObj.dn_load_percentage" class="slider" id="dn_load_percentage" name="dn_load_percentage" value="0" [required]="deliveryOrCollectionNoteObj.dn_load_type === 'Part load'" style="width: 90%;">
            </div>
            <div class="form-group">
                <label>Description</label>
                <div class="input-group mb-3">
                                <textarea class="form-control" name="dn_description"
                                          [(ngModel)]="deliveryOrCollectionNoteObj.dn_description"
                                          ng-value="deliveryOrCollectionNoteObj.dn_description"
                                          placeholder="Description"
                                          #dn_description="ngModel"></textarea>
                </div>
            </div>

            <div class="col-md-12 p-0 mt-3 mb-3">
                <p class="mb-1"><strong>Image/Attachment</strong></p>
            </div>
            <div class="col-md-12 p-0 mb-4">
                <div class="col-md-12 flex-grow-1 p-0" *ngFor="let c of dnImages">
                    <file-uploader-v2
                        class="mt-1 pt-1 mb-1"
                        [init]="c"
                        [category]="featureId+'s-attachment'"
                        (uploadDone)="attachmentUploadDone($event)"
                        [multipleUpload]="true"
                        [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']"
                        #attachmentUploader
                        [showDeleteBtn]="true"
                        [showFileName]="false"
                        [hasImgAndDoc]="true"
                        (deleteFileDone)="fileDeleteDone($event)"
                        [disabled]="false"
                        >
                    </file-uploader-v2>
                </div>
            </div>
            <input type="hidden" name="id" id="dn_id" [(ngModel)]="deliveryOrCollectionNoteObj.id"/>
        </form>
</i-modal>
<!-- Start: New delivery or collection note-->
<i-modal #addDeliveryOrCollectionModalRef [title]="'New ' + featureSglrPhrase" size="md" (onClickRightPB)="saveDeliveryOrCollectionNote()" rightPrimaryBtnTxt="Submit"
        [rightPrimaryBtnDisabled]="!addDeliveryOrCollectionForm.valid">
        <form novalidate #addDeliveryOrCollectionForm="ngForm">
            <div class="form-group">
                <label>{{featureId === 'delivery-note' ? 'Delivery': 'Collection'}} Date</label>
                <div class="input-group mb-3">
                    <input class="form-control" placeholder="dd-mm-yyyy"
                            name="dn_delivered_on" [(ngModel)]="deliveryOrCollectionNoteObj.dn_delivered_on" ngbDatepicker
                            #ed="ngbDatepicker" ng-value="deliveryOrCollectionNoteObj.dn_delivered_on">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary calendar" (click)="ed.toggle()" type="button">
                            <i class="fa fa-calendar"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label>Supplier <small class="required-asterisk">*</small></label>
                <div class="input-group mb-3">
                    <input
                        #supplier="ngModel"
                        [(ngModel)]="deliveryOrCollectionNoteObj.dn_supplier"
                        class="form-control"
                        name="supplier"
                        ng-value="deliveryOrCollectionNoteObj.dn_supplier"
                        placeholder="Supplier"
                        type="text"
                        required
                    />
                </div>
            </div>
            <div class="form-group">
                <label>Location</label>
                <div class="input-group mb-3">
                    <input
                        truncate
                        #dn_location="ngModel"
                        [(ngModel)]="deliveryOrCollectionNoteObj.dn_location"
                        class="form-control"
                        name="dn_location"
                        ng-value="deliveryOrCollectionNoteObj.dn_location"
                        placeholder="Location"
                        type="text"
                    />
                </div>
            </div>
            <div class="form-group">
                <label>Dispatch Postcode</label>
                <input type="text" class="form-control" truncate [(ngModel)]="deliveryOrCollectionNoteObj.dn_dispatch_postcode" name="dn_dispatch_postcode"
                    placeholder="Dispatch Postcode" #dispatchPostcode="ngModel" (focusout)="validatePostcode($event, deliveryOrCollectionNoteObj.dn_dispatch_postcode, dispatchPostcode)"/>
                <div class="alert alert-danger" [hidden]="!(dispatchPostcode.errors && dispatchPostcode.errors.valid)">Invalid Dispatch Postcode.</div>
                <div class="mt-2 small text-muted" *ngIf="deliveryOrCollectionNoteObj.dn_distance_travelled">Distance Travelled: {{ distanceTravelledInKm }} (Including return journey)</div>
            </div>

            <div class="form-group" *ngIf="deliveryOrCollectionNoteObj.dn_dispatch_postcode">
                <label>Full/Part Load <small class="required-asterisk">*</small></label>
                <ng-select class="dropdown-list" placeholder="Select Full/Part Load" appendTo="body" [items]="loadOptions" name="dn_load_type" [(ngModel)]="deliveryOrCollectionNoteObj.dn_load_type" style="width: 100%;" [required]="deliveryOrCollectionNoteObj.dn_dispatch_postcode" (change)="checkLoadType(deliveryOrCollectionNoteObj.dn_load_type)">
                </ng-select>
            </div>
            <div class="form-group" *ngIf="deliveryOrCollectionNoteObj.dn_load_type === 'Part load'">
                <label class="float-right">{{deliveryOrCollectionNoteObj.dn_load_percentage}} %</label>
                <input type="range" min="0" max="100" [(ngModel)]="deliveryOrCollectionNoteObj.dn_load_percentage" class="slider" id="dn_load_percentage" name="dn_load_percentage" value="0" [required]="deliveryOrCollectionNoteObj.dn_load_type === 'Part load'" style="width: 90%;">
            </div>

            <div class="form-group">
                <label>PO Number</label>
                <div class="input-group mb-3">
                    <input
                        #po_number="ngModel"
                        [(ngModel)]="deliveryOrCollectionNoteObj.po_number"
                        class="form-control"
                        name="po_number"
                        ng-value="tesy"
                        placeholder="PO Number"
                        type="text"
                    />
                </div>
            </div>
            <div class="form-group">
                <label>{{(featureId === 'delivery-note') ? 'Delivery': 'Collection'}} Ref</label>
                <div class="input-group mb-3">
                    <input
                        #delivery_ref_no="ngModel"
                        [(ngModel)]="deliveryOrCollectionNoteObj.delivery_ref_no"
                        class="form-control"
                        name="delivery_ref_no"
                        ng-value="deliveryOrCollectionNoteObj.delivery_ref_no"
                        placeholder="{{featureId === 'delivery-note' ? 'Delivery': 'Collection'}} Ref"
                        type="text"
                    />
                </div>
            </div>
            <div class="form-group">
                <label>Description</label>
                <div class="input-group mb-3">
                    <textarea
                        #dn_description="ngModel"
                        [(ngModel)]="deliveryOrCollectionNoteObj.dn_description"
                        class="form-control"
                        name="dn_description"
                        ng-value="deliveryOrCollectionNoteObj.dn_description"
                        placeholder="Description"
                    ></textarea>
                </div>
            </div>
            <div class="col-md-12 p-0 mt-3 mb-3"><h6>Add Image/Attachment</h6></div>
            <div class="col-md-12 p-0 mb-4">
                <div class="col-md-12 flex-grow-1 p-0" *ngFor="let c of dnImages">
                    <file-uploader-v2
                        class="mt-1 pt-1 mb-1"
                        [init]="c"
                        [category]="featureId+'s-attachment'"
                        (uploadDone)="attachmentUploadDone($event)"
                        [multipleUpload]="true"
                        [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']"
                        #attachmentUploader
                        [showDeleteBtn]="true"
                        [showFileName]="false"
                        [hasImgAndDoc]="true"
                        (deleteFileDone)="fileDeleteDone($event)"
                        [disabled]="false"
                        >
                    </file-uploader-v2>
                </div>
            </div>
        </form>
        <block-loader [show]="(loadingPostcode)" [alwaysInCenter]="true" [showBackdrop]="false"></block-loader>
</i-modal>
<!-- End: New delivery or collection note -->
<block-loader [show]="(downloadDeliveryNotesReportLoading || loadingDeliveryNotes)" [alwaysInCenter]="true" [showBackdrop]="true"></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
<report-downloader #reportDownloader
    [xlsxOnly]="false"
    (onFilterSelection)="deliveryNotesReportDownload($event)"
    >
</report-downloader>

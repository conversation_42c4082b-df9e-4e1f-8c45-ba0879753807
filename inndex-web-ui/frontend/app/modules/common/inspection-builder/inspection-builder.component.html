<div class>
    <div class="w-100 d-flex">
        <div id="column1" class="check-list-content-w max-modal-h p-4 w-100">
            <div *ngIf="builderObj.activeIndex === 0 && isDraftAvailable" class="new-inspection">
                <button (click)="goNextStep(true); isNewReport = true" class="btn btn-outline-brandeis-blue mb-4 mx-auto py-2 w-100 border-width font-s-14">
                    Start New Inspection
                </button>
                <div>
                    <p class="mb-0 fw-500">Draft Reports</p>
                    <p class="font-s-14">
                        Reports will be auto-deleted after 30 days
                    </p>
                    <div *ngFor="let list of draftData">
                        <div class="align-items-center d-flex justify-content-between mb-3">
                            <div (click)="draftSetUp(list.id)" class="selected-draft m-0 mr-4 cursor-pointer border px-3 py-2 border-secondary rounded border-width font-s-14 w-100">
                                <div>Report Started: {{dayjs(+list.createdAt).format(fullDateTimeFormatwithoutSS)}}</div>
                                <div>Last Updated: {{dayjs(+list.updatedAt).format(fullDateTimeFormatwithoutSS)}}</div>
                                <div class="text-muted">
                                    Auto-deleted after {{remainingDays(list.createdAt)}}
                                </div>
                            </div>
                            <div (click)="showRemoveDraftModal(list.id)" class="cursor-pointer">
                                <i class="fa fa fa-trash text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="isDraftAvailable ? builderObj.activeIndex > 0 : true">
                <div id="content-head" class="col-md-12 m-0 p-0 d-inline-block pl-3 mt-0 mb-3 pt-0 font-weight-bold position-relative pr-3">
                    <div class="row d-flex justify-content-between cursor-pointer" (click)="builderObj.isStepDropdownOpen = !builderObj.isStepDropdownOpen">
                        <div>{{builderObj.selectedHeading}}</div>
                        <div> <i class="fa fa fa-angle-down" ></i></div>
                    </div>
                    <div *ngIf="builderObj.isStepDropdownOpen" class="position-absolute custom-dropdown-menu w-100 step-dropdown-w content-drop-ml top-0" >
                        <div class="bg-white rounded mx-0">
                            <div class="d-flex align-items-center justify-content-between py-3 px-4 drop-header cursor-pointer" (click)="builderObj.isStepDropdownOpen = !builderObj.isStepDropdownOpen">
                                <label class="m-0 cursor-pointer">{{builderObj.selectedHeading}}</label>
                                <label class="m-0 cursor-pointer" >
                                    <i class="fa fa fa-angle-up" ></i>
                                </label>
                            </div>
                            <hr class="m-0">
                            <ng-container *ngIf="isChecklistHasSubheading">
                                <a [ngClass]="{'dropdown-item cursor-pointer py-3': true, 'font-weight-bold': (builderObj.activeIndex === (isDraftAvailable ? 1 : 0))}"
                                       href="javascript:void(0)" (click)="moveStep(isDraftAvailable ? 1 : 0, 'Content')">
                                        Content
                                </a>
                                <hr class="my-0 mx-4">
                                <div class="d-flex justify-content-between cursor-pointer hower-effect" (click)="moveStep(isDraftAvailable ? 2 : 1, 'Details')">
                                    <div>
                                        <a [ngClass]="{'d-none': !hasDetailFields,'dropdown-item cursor-pointer py-3': true, 'font-weight-bold': (builderObj.activeIndex === (isDraftAvailable ? 2 : 1))}"
                                            class="dropdown-item cursor-pointer py-3"
                                            href="javascript:void(0)">
                                            Details
                                        </a>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <a class="font-s-14" *ngIf="!checkDetailMandatoryFields()" [ngClass]="{'dropdown-item cursor-pointer py-3': true}" href="javascript:void(0)" >
                                            <span class="material-symbols-outlined check-circle">check_circle</span>
                                        </a>
                                    </div>
                                </div>
                                <hr class="my-0 mx-4">
                                <ng-container *ngFor="let mainHead of builderObj.headings; let headInd = index">
                                    <div class="d-flex justify-content-between cursor-pointer hower-effect" (click)="checkIsContentEnable(headInd, mainHead.heading) ? null : moveStep(headInd + (isDraftAvailable ? 3 : 2), mainHead.heading)">
                                        <div>
                                            <a [ngClass]="checkIsContentEnable(headInd, mainHead.heading) ? 'fw-400 disable-content-text' : builderObj.activeIndex === headInd + (isDraftAvailable ? 3 : 2) ? 'font-weight-bold' : '' "
                                                [attr.id]="'dynamicContentId-' + headInd"
                                                class="dropdown-item cursor-pointer py-3"
                                                href="javascript:void(0)">
                                                {{mainHead.heading}}
                                            </a>
                                        </div>
                                        <div>
                                            <a *ngIf="!checkOptionCheckedAndValid((headInd + 1) + (isDraftAvailable ? 2 : 1), true) && !checkIsAllAnswerNotApplicable(mainHead)" class="font-s-14 dropdown-item cursor-pointer py-3" href="javascript:void(0)" >
                                                <span class="material-symbols-outlined check-circle">check_circle</span>
                                            </a>
                                            <a class="font-s-14" *ngIf="checkIsAllAnswerNotApplicable(mainHead)" [ngClass]="{'dropdown-item cursor-pointer py-3': true}"
                                                href="javascript:void(0)">
                                                N/A
                                            </a>
                                        </div>
                                    </div>
                                    <hr class="my-0 mx-4">
                                </ng-container>
                                <div class="d-flex justify-content-between cursor-pointer hower-effect" (click)="checkIsContentEnable(builderObj.headings.length, 'Summary') ? null : moveStep(builderObj.headings.length + (isDraftAvailable ? 3 : 2), 'Summary')">
                                   <div>
                                       <a class="dropdown-item cursor-pointer py-3" [ngClass]="{'fw-400 disable-content-text': checkIsContentEnable(builderObj.headings.length, 'Summary'), 'font-weight-bold': builderObj.activeIndex === builderObj.headings.length + (isDraftAvailable ? 3 : 2)}"
                                            href="javascript:void(0)">
                                            Summary
                                        </a>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <a class="font-s-14" *ngIf="this.builderObj.report_datetime" [ngClass]="{'dropdown-item cursor-pointer py-3': true}" href="javascript:void(0)" >
                                            <span class="material-symbols-outlined check-circle">check_circle</span>
                                        </a>
                                    </div>
                                </div>
                                <hr class="my-0 mx-4">
                                <a class="dropdown-item cursor-pointer py-3" [ngClass]="{'fw-400 disable-content-text': checkIsContentEnable(builderObj.headings.length + (isDraftAvailable ? 3 : 2), 'Review') || !builderObj.report_datetime, 'font-weight-bold': builderObj.activeIndex === builderObj.headings.length + (isDraftAvailable ? 3 : 2)}"
                                    href="javascript:void(0)" (click)="moveStep(builderObj.headings.length + (isDraftAvailable ? 3 : 2), 'Review')">
                                    Review
                                </a>
                                <hr class="my-0 mx-4">
                            </ng-container>
                            <ng-container *ngIf="!isChecklistHasSubheading">
                                <div class="d-flex justify-content-between cursor-pointer hower-effect" (click)="moveStep(isDraftAvailable ? 1 : 0, 'Details')">
                                   <div>
                                        <a class="dropdown-item cursor-pointer py-3" [ngClass]="{'font-weight-bold': builderObj.activeIndex === (isDraftAvailable ? 1 : 0)}"
                                            href="javascript:void(0)">
                                            Details
                                        </a>
                                   </div>
                                    <div class="d-flex align-items-center">
                                        <a class="font-s-14" *ngIf="!checkDetailMandatoryFields()" [ngClass]="{'dropdown-item cursor-pointer py-2': true}"
                                            href="javascript:void(0)" >
                                            <span class="material-symbols-outlined check-circle">check_circle</span>
                                        </a>
                                    </div>
                                </div>
                                <hr class="my-0 mx-4">
                                <ng-container *ngFor="let mainHead of builderObj.headings; let headInd = index">
                                    <div class="d-flex justify-content-between cursor-pointer hower-effect" (click)="checkIsContentEnable(headInd, mainHead.heading) ? null : moveStep(headInd + (isDraftAvailable ? 2 : 1), mainHead.heading)">
                                        <div>
                                            <a class="dropdown-item cursor-pointer py-3" [ngClass]="{'fw-400 disable-content-text': checkIsContentEnable(headInd + (isDraftAvailable ? 2 : 1), 'Checklist'),'font-weight-bold': builderObj.activeIndex === headInd + (isDraftAvailable ? 2 : 1)}"                                                
                                               href="javascript:void(0)" (click)="moveStep(headInd + (isDraftAvailable ? 2 : 1), mainHead.heading)">
                                                {{mainHead.heading}}
                                            </a>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <a class="font-s-14" *ngIf="!checkWithoutSubHeadValid((headInd + 1) + (isDraftAvailable ? 1 : 0)) && !withoutSubHeadCheckIsAllAnsNA()" [ngClass]="{'dropdown-item cursor-pointer py-2': true}" 
                                               href="javascript:void(0)" >
                                                <span class="material-symbols-outlined check-circle">check_circle</span>
                                            </a>
                                            <a class="font-s-14" *ngIf="withoutSubHeadCheckIsAllAnsNA()" [ngClass]="{'dropdown-item cursor-pointer py-2': true}"
                                               href="javascript:void(0)" >
                                                N/A
                                            </a>
                                        </div>
                                    </div>
                                    <hr class="my-0 mx-4">
                                </ng-container>
                                <div class="d-flex justify-content-between cursor-pointer hower-effect" (click)="checkIsContentEnable(builderObj.headings.length + (isDraftAvailable ? 2 : 1), 'Summary') ? null : moveStep(builderObj.headings.length + (isDraftAvailable ? 2 : 1), 'Summary')">
                                    <div>
                                        <a class="dropdown-item cursor-pointer py-3" [ngClass]="{'fw-400 disable-content-text': checkIsContentEnable(builderObj.headings.length + (isDraftAvailable ? 2 : 1), 'Summary'),'font-weight-bold': builderObj.activeIndex === builderObj.headings.length + (isDraftAvailable ? 2 : 1)}"
                                            href="javascript:void(0)" >
                                            Summary
                                        </a>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <a class="font-s-14" *ngIf="this.builderObj.report_datetime" [ngClass]="{'dropdown-item cursor-pointer py-2': true}" href="javascript:void(0)" >
                                            <span class="material-symbols-outlined check-circle">check_circle</span>
                                        </a>
                                    </div>
                                </div>
                                <hr class="my-0 mx-4">
                                <a class="dropdown-item cursor-pointer py-3" [ngClass]="{'fw-400 disable-content-text': checkIsContentEnable(builderObj.headings.length + (isDraftAvailable ? 3 : 2), 'Review') || !builderObj.currentInspectionItem.report_datetime,'font-weight-bold': builderObj.activeIndex === builderObj.headings.length + (isDraftAvailable ? 3 : 2) && !builderObj.currentInspectionItem.report_datetime}"
                                    href="javascript:void(0)" (click)="moveStep(builderObj.headings.length + (isDraftAvailable ? 3 : 2), 'Review')">
                                    Review
                                </a>
                                <hr class="my-0 mx-4">
                            </ng-container>
                        </div>
                    </div>
                    <ngb-modal-backdrop *ngIf="builderObj.isStepDropdownOpen" (click)="builderObj.isStepDropdownOpen = !builderObj.isStepDropdownOpen" [style.z-index]="1040" class="modal-backdrop fade show"></ngb-modal-backdrop>
                </div>
                <div>
                    <p *ngIf="isChecklistHasSubheading && builderObj.selectedHeading === 'Content'" class="pb-0 mb-0 font-s-14">Use the switches to N/A whole sections of the inspection</p>
                    <p *ngIf="(isChecklistHasSubheading && builderObj.selectedHeading !== 'Content' && builderObj.selectedHeading !== 'Details' && builderObj.activeIndex <= builderObj.headings.length + (isDraftAvailable ? 2 : 1)) || (!isChecklistHasSubheading && builderObj.activeIndex === (isDraftAvailable ? 2 : 1))" class="pb-0 mb-0 font-s-14">Select the rating and add any details for each item below</p>
                </div>
                <div id="check-contents check-list-content-w" class="modal-max-h-preview">
                    <ng-container *ngIf="builderObj.selectedHeading === 'Content' && isChecklistHasSubheading">
                        <div  class="d-flex justify-content-between pb-4 pt-4 light-grey-bottom-border" *ngFor="let heading of builderObj.headings; let dataInd = index">
                            <div>{{heading.heading}}</div>
                            <div>
                                <label class="switch mb-0">
                                    <input type="checkbox" [checked]="heading.isChecked" (change)="changeHeadingToggle(heading)">
                                    <span class="slider round"></span>
                                </label>
                            </div>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="(isChecklistHasSubheading && hasDetailFields && builderObj.selectedHeading === 'Details') || (!isChecklistHasSubheading && hasDetailFields && builderObj.selectedHeading === 'Details')">
                        <div  class="w-100 pb-2 pt-2">
                            <form novalidate #detailFieldForm="ngForm" class="w-100">
                                <ng-container *ngIf="builderObj.currentInspectionItem?.detail_fields?.length; else noDetailFound">
                                    <ng-container *ngFor="let formField of builderObj.currentInspectionItem.detail_fields;">
                                        <div class="form-group w-100" *ngIf="formField?.field_type == 'textbox'">
                                            <label>{{formField?.field}}  <small *ngIf="formField?.is_mandatory" class="required-asterisk">*</small></label>
                                            <div class="input-group mb-3 w-100">
                                                <input 
                                                    class="form-control"
                                                    name="{{formField.field}}"
                                                    placeholder="Enter {{formField?.field}}"
                                                    [(ngModel)]="formField.field_value"
                                                    [value]="formField?.field_value"
                                                    [required]="formField?.is_mandatory"
                                                    (change)="changed = true"
                                                    >
                                            </div>
                                        </div>
                                        <div class="form-group w-100" *ngIf="formField?.field_type == 'textarea'">
                                            <label>{{formField?.field}}  <small *ngIf="formField?.is_mandatory" class="required-asterisk">*</small></label>
                                            <div class="input-group mb-3 w-100">
                                                <textarea 
                                                    [(ngModel)]="formField.field_value"
                                                    class="form-control"
                                                    name="{{formField?.field}}"
                                                    [value]="formField?.field_value"
                                                    placeholder="Enter {{formField?.field}}"
                                                    [required]="formField?.is_mandatory"
                                                    (change)="changed = true"
                                                ></textarea>
                                            </div>
                                        </div>
                                        <div class="form-group w-100" *ngIf="formField?.field_type === 'dropdown'">
                                            <label>{{formField?.field}}  <small *ngIf="formField?.is_mandatory" class="required-asterisk">*</small></label>
                                            <div class="input-group mb-3 w-100">
                                                <ng-select *ngIf="formField.field_type === 'dropdown'" 
                                                class="w-100" [items]="formField.options" bindLabel="label" bindValue="label"
                                                [name]="formField?.field" placeholder="Select {{formField.field}}"
                                                [(ngModel)]="formField.field_value" [required]="formField.is_mandatory" (change)="changed = true">
                                            </ng-select>
                                            </div>
                                        </div>
                                    </ng-container>
                                </ng-container>
                                <ng-template #noDetailFound>
                                    <div class="text-center light-grey-border" >
                                        No Details Found
                                    </div>
                                </ng-template>
                            </form>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="builderObj.selectedHeading !== 'Content' && builderObj.selectedHeading !== 'Details'">
                        <ng-container *ngFor="let checklist of builderObj.currentInspectionItem.checklist; let mainCheckIndex = index;let lastInd = last">
                            <!-- start: subheadings checklist -->
                            <ng-container *ngIf="isChecklistHasSubheading">
                                <ng-container *ngFor="let question of checklist.subheadings;let headingIndex = index;let last = last">
                                    <ng-container *ngIf="builderObj.activeIndex === mainCheckIndex + (isDraftAvailable ? 3 : 2)">
                                        <div class="mt-3" [ngClass]="last ? 'mb-0' : 'light-grey-bottom-border mb-4'">
                                            <div class="d-flex justify-content-between pr-0">
                                                <div>
                                                    <h6>{{question.item_que}}<small class="required-asterisk">*</small></h6>
                                                </div>
                                                <div>
                                                    <p class="cursor-pointer fw-500">
                                                        <span *ngIf="question.hasOwnProperty('answer') && question.answer != ''" (click)="editChecklist(question, mainCheckIndex, headingIndex)">
                                                            <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M5.53225 2.20435H0.831309C0.697351 2.20435 0.588867 2.31296 0.588867 2.44679V12.152C0.588867 12.2859 0.697478 12.3944 0.831309 12.3944H10.9631C11.097 12.3944 11.2055 12.2858 11 2055 12.152L11.2054 6.72064" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"></path>
                                                            <path d="M3.72333 9.15909L4.47955 6.68904L9.96093 1.1879C10.205 0.942923 10.5998 0.936713 10.8515 1.17383L11.665 1.94032C11.9263 2.18644 11.9335 2.59959 11.681 2.85471L6.20048 8.39247L3.75578 9.1909C3.73601 9.19723 3.71725 9.17886 3.72333 9.15909Z" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"></path>
                                                            <path d="M4.47949 6.68921L6.20041 8.39251" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"></path>
                                                            <path d="M9.2334 1.91821L10.9159 3.62798" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"></path>
                                                            </svg>
                                                            <span class="pl-1">Edit</span>
                                                        </span>
                                                        <span class="pl-3" *ngIf="question?.isOther">
                                                            <button title="Delete Other" class="btn btn-sm float-md-right mt-0 pr-0 pt-0"
                                                            (click)="deleteOther($event, confirmationMetaData.inspectionBuilderConfModalType.CHEKLIST_DELETE, mainCheckIndex, headingIndex)">
                                                                <span class="material-symbols-outlined text-danger cursor-pointer">
                                                                    delete
                                                                </span>
                                                            </button>
                                                        </span>
                                                    </p>
                                                </div>
                                            </div>
                                            <ng-container *ngIf="builderObj.currentInspectionItem.scoring_system.type === 1">
                                                <div class="d-flex justify-content-between pb-1 pr-0" *ngFor="let val of builderObj.currentInspectionItem.scoring_system.values;let i = index; let first = first;let last = last" [ngClass]="(first) ? 'pt-2' : 'pt-3'" [class.mb-3]="last && question?.isOther">
                                                    <label class="w-100 cursor-pointer" [attr.for]="'with_head_scoring_type_one_' + mainCheckIndex + '_' + headingIndex + '_' + i" [ngClass]="{'color-good': val == builderObj.currentInspectionItem.scoring_system.values[0],'color-fair': val == builderObj.currentInspectionItem.scoring_system.values[1],'color-poor': val == builderObj.currentInspectionItem.scoring_system.values[2]}">{{val}}</label>
                                                    <div><input [id]="'with_head_scoring_type_one_' + mainCheckIndex + '_' + headingIndex + '_' + i" [(ngModel)]="question.answer" (click)="confirmAnsChangReq($event, question, mainCheckIndex, headingIndex, val)" (change)="changeAnswer(question, mainCheckIndex, headingIndex, val); changed = true" class="cursor-pointer" type="radio" name="{{question.item_que}}" value="{{val}}"></div>
                                                </div>
                                                <div *ngIf="!question?.isOther" class="d-flex justify-content-between mb-3 pt-3 pb-0 pr-0">
                                                    <label class="w-100 cursor-pointer color-na mb-0" [attr.for]="'with_head_scoring_type_one_na_' + mainCheckIndex + '_' + headingIndex">N/A</label>
                                                    <div><input [id]="'with_head_scoring_type_one_na_' + mainCheckIndex + '_' + headingIndex" [(ngModel)]="question.answer" (click)="confirmAnsChangReq($event, question, mainCheckIndex, headingIndex, 'n/a')" (change)="changeAnswer(question, mainCheckIndex, headingIndex, 'n/a'); changed = true" class="cursor-pointer" type="radio" name="{{question.item_que}}" value="n/a"></div>
                                                </div>
                                                <div *ngIf="!hasSeverity && question.closeout_due_on && question.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="pt-2">Due Date: <span>{{showDate(question.closeout_due_on)}}</span></h6>
                                                </div>
                                                <div *ngIf="hasSeverity && question.severity?.rating && question.answer !== 'n/a'" class="text-center bg-grey-w-border-radius my-2 py-2">
                                                    <h6>Severity: <span [ngClass]="{'color-na': question.severity?.rating == 'Low','color-fair-fuel': question.severity?.rating == 'Medium','color-orange-high': question.severity?.option == 'High', 'color-critical-red': (question.severity?.rating == 'Critical')}">{{question.severity?.rating}}</span></h6>
                                                    <p class="mb-0">Close Out: {{showDate(question?.closeout_due_on)}}</p>
                                                </div>
                                                <div *ngIf="hasRootCause && question.root_cause && question.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="pt-2">Root Cause: <span>{{question?.root_cause}}</span></h6>
                                                </div>
                                                <div *ngIf="question.tagged_user_ref && question.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="pt-2">Assignee: <span>{{question.tagged_user_ref ? question.tagged_user_ref.name : ""}}</span></h6>
                                                </div>
                                                <div *ngIf="question?.tagged_company_ref?.length > 0 && question.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="px-3 pt-2">Company: <span class="" *ngFor="let company of question?.tagged_company_info;let last = last;let i = index">{{company ? company.name : ''}}<span *ngIf="((i==0 && question?.tagged_company_ref?.length > 1) || (i > 0 && !last))">, </span></span></h6>
                                                </div>
                                                <div class="mb-0">
                                                    <ng-template ngFor let-item [ngForOf]="question.images">
                                                        <div class="row p-0 mt-3 mx-0">
                                                            <div class="col px-2 py-2 d-flex align-items-center flex-wrap border rounded-lg">
                                                                <div class="p-0 d-inline-block text-truncate">
                                                                    <a [href]="item.file_url" target="_blank" class="medium-font fw-500" style="max-width: 220px;">{{item.name}}</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </ng-template>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngIf="builderObj.currentInspectionItem.scoring_system.type === 2">
                                                <div class="d-flex justify-content-between pt-2 pb-1 pr-0">
                                                    <label class="w-100 cursor-pointer color-good " [attr.for]="'with_head_scoring_type_two_ans3_'+ mainCheckIndex +'_'+ headingIndex">3</label>
                                                    <div><input class="cursor-pointer" [id]="'with_head_scoring_type_two_ans3_'+ mainCheckIndex +'_'+ headingIndex" type="radio" name="{{question.item_que}}" value="3" [(ngModel)]="question.answer" (click)="confirmAnsChangReq($event, question, mainCheckIndex, headingIndex, '3')" (change)="changeAnswer(question, mainCheckIndex, headingIndex, '3'); changed = true"></div>
                                                </div>
                                                <div class="d-flex justify-content-between pt-3 pb-1 pr-0">
                                                    <label class="w-100 cursor-pointer color-fair" [attr.for]="'with_head_scoring_type_two_ans2_' + mainCheckIndex + '_' + headingIndex">2</label>
                                                    <div><input class="cursor-pointer" [id]="'with_head_scoring_type_two_ans2_'+ mainCheckIndex +'_'+ headingIndex" type="radio" name="{{question.item_que}}" value="2" [(ngModel)]="question.answer" (click)="confirmAnsChangReq($event, question, mainCheckIndex, headingIndex, '2')" (change)="changeAnswer(question, mainCheckIndex, headingIndex, '2'); changed = true"></div>
                                                </div>
                                                <div [class.mb-3]="question?.isOther" class="d-flex justify-content-between pt-3 pb-1 pr-0">
                                                    <label class="w-100 cursor-pointer color-poor" [attr.for]="'with_head_scoring_type_two_ans1_'+ mainCheckIndex +'_'+ headingIndex">1</label>
                                                    <div><input class="cursor-pointer" [id]="'with_head_scoring_type_two_ans1_'+ mainCheckIndex +'_'+ headingIndex" type="radio" name="{{question.item_que}}" value="1" [(ngModel)]="question.answer" (click)="confirmAnsChangReq($event, question, mainCheckIndex, headingIndex, '1')" (change)="changeAnswer(question, mainCheckIndex, headingIndex, '1'); changed = true"></div>
                                                </div>
                                                <div *ngIf="!question?.isOther" class="d-flex justify-content-between mb-3 pt-3 pb-1 pr-0">
                                                    <label class="w-100 cursor-pointer color-na" [attr.for]="'with_head_scoring_type_two_na_'+ mainCheckIndex +'_'+ headingIndex">N/A</label>
                                                    <div><input class="cursor-pointer" [id]="'with_head_scoring_type_two_na_'+ mainCheckIndex +'_'+ headingIndex" type="radio" name="{{question.item_que}}" value="n/a" [(ngModel)]="question.answer" (click)="confirmAnsChangReq($event, question, mainCheckIndex, headingIndex, 'n/a')" (change)="changeAnswer(question, mainCheckIndex, headingIndex, 'n/a'); changed = true"></div>
                                                </div>
                                                <div class="d-flex justify-content-center bg-grey-w-border-radius my-2" *ngIf="(!hasSeverity && question.closeout_due_on && question.answer !== 'n/a')">
                                                    <h6 class="pt-2">Due Date: <span>{{showDate(question.closeout_due_on)}}</span></h6>
                                                </div>
                                                <div class="text-center bg-grey-w-border-radius my-2 py-2" *ngIf="(hasSeverity && question.severity?.rating && question.answer !== 'n/a')">
                                                    <h6>Severity: <span [ngClass]="{'color-na': (question.severity?.rating == 'Low'),'color-fair-fuel': (question.severity?.rating == 'Medium'), 'color-orange-high': (question.severity?.option == 'High'), 'color-critical-red': (question.severity?.rating == 'Critical')}">{{question.severity?.rating}}</span></h6>
                                                    <p class="mb-0">Close Out:{{showDate(question?.closeout_due_on)}}</p>
                                                </div>
                                                <div class="d-flex justify-content-center bg-grey-w-border-radius my-2" *ngIf="(hasRootCause && question.root_cause && question.answer !== 'n/a')">
                                                    <h6 class="pt-2">Root Cause:<span>{{question?.root_cause}}</span></h6>
                                                </div>
                                                <div class="d-flex justify-content-center bg-grey-w-border-radius my-2" *ngIf="(question.tagged_user_ref && question.answer !== 'n/a')">
                                                    <h6 class="pt-2">Assignee:<span>{{question.tagged_user_ref ? question.tagged_user_ref.name : ''}}</span></h6>
                                                </div>
                                                <div class="d-flex justify-content-center bg-grey-w-border-radius my-2" *ngIf="(question?.tagged_company_ref?.length > 0) && question.answer !== 'n/a'">
                                                    <h6 class="px-3 pt-2">Company: <span class="" *ngFor="let company of question?.tagged_company_info;let last = last;let i = index">{{company ? company.name : ''}}<span *ngIf="((i==0 && question?.tagged_company_ref?.length > 1) || (i > 0 && !last))">, </span></span></h6>
                                                </div>
                                                <div class="mb-3">
                                                    <ng-template ngFor let-item [ngForOf]="question.images">
                                                        <div class="row p-0 mt-3 mx-0">
                                                            <div class="col px-2 py-2 d-flex align-items-center flex-wrap border rounded-lg">
                                                                <div class="p-0 d-inline-block text-truncate">
                                                                    <a [href]="item.file_url" target="_blank" class="medium-font fw-500" style="max-width: 220px;">{{item.name}}</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </ng-template>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngIf="builderObj.currentInspectionItem.scoring_system.type === 3">
                                                <div class="d-flex justify-content-between pt-2 pb-1 pr-0">
                                                    <label [attr.for]="'with_head_scoring_type_three_yes_' + mainCheckIndex + '_' + headingIndex" class="w-100 cursor-pointer color-good">
                                                        Yes
                                                    </label>
                                                    <div>
                                                        <input [id]="'with_head_scoring_type_three_yes_' + mainCheckIndex + '_' + headingIndex" [(ngModel)]="question.answer" (click)="confirmAnsChangReq($event, question, mainCheckIndex, headingIndex, 'Yes')" (change)="changeAnswer(question, mainCheckIndex, headingIndex, 'Yes'); changed = true" class="cursor-pointer" type="radio" [name]="question.item_que" value="Yes">
                                                    </div>
                                                </div>
                                                <div [class.mb-3]="question?.isOther" class="d-flex justify-content-between pt-3 pb-1 pr-0">
                                                    <label [attr.for]="'with_head_scoring_type_three_no_' + mainCheckIndex + '_' + headingIndex" class="w-100 cursor-pointer color-poor">
                                                        No
                                                    </label>
                                                    <div>
                                                        <input [id]="'with_head_scoring_type_three_no_' + mainCheckIndex + '_' + headingIndex" [(ngModel)]="question.answer" (click)="confirmAnsChangReq($event, question, mainCheckIndex, headingIndex, 'No')" (change)="changeAnswer(question, mainCheckIndex, headingIndex, 'No'); changed = true" class="cursor-pointer" type="radio" [name]="question.item_que" value="No">
                                                    </div>
                                                </div>
                                                <div *ngIf="!question?.isOther" class="d-flex justify-content-between mb-3 pt-3 pb-1 pr-0">
                                                    <label [attr.for]="'with_head_scoring_type_three_na_' + mainCheckIndex + '_' + headingIndex" class="w-100 cursor-pointer color-na">
                                                        N/A
                                                    </label>
                                                    <div>
                                                        <input [id]="'with_head_scoring_type_three_na_' + mainCheckIndex + '_' + headingIndex" [(ngModel)]="question.answer" (click)="confirmAnsChangReq($event, question, mainCheckIndex, headingIndex, 'n/a')" (change)="changeAnswer(question, mainCheckIndex, headingIndex, 'n/a'); changed = true" class="cursor-pointer" type="radio" [name]="question.item_que" value="n/a">
                                                    </div>
                                                </div>
                                                <div *ngIf="!hasSeverity && question.closeout_due_on && question.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="pt-2">
                                                        Due Date:
                                                        <span>
                                                            {{showDate(question.closeout_due_on)}}
                                                        </span>
                                                    </h6>
                                                </div>
                                                <div *ngIf="hasSeverity && question.severity?.rating && question.answer !== 'n/a'" class="text-center bg-grey-w-border-radius my-2 py-2">
                                                    <h6>
                                                        Severity:
                                                        <span [ngClass]="{
                                                                    'color-na': question.severity?.rating == 'Low',
                                                                    'color-fair-fuel': question.severity?.rating == 'Medium',
                                                                    'color-orange-high': question.severity?.rating == 'High',
                                                                    'color-critical-red': question.severity?.rating == 'Critical'
                                                                }">
                                                            {{question.severity?.rating}}
                                                        </span>
                                                    </h6>
                                                    <p class="mb-0">
                                                        Close Out:
                                                        {{showDate(question?.closeout_due_on)}}
                                                    </p>
                                                </div>
                                                <div *ngIf="hasRootCause && question.root_cause && question.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="pt-2">
                                                        Root Cause:
                                                        <span>
                                                            {{question?.root_cause}}
                                                        </span>
                                                    </h6>
                                                </div>
                                                <div *ngIf="question.tagged_user_ref && question.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="pt-2">
                                                        Assignee:
                                                        <span>
                                                            {{question.tagged_user_ref ? question.tagged_user_ref.name : ""}}
                                                        </span>
                                                    </h6>
                                                </div>
                                                <div *ngIf="question?.tagged_company_ref?.length > 0 && question.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="px-3 pt-2">
                                                        Company:
                                                        <span *ngFor="let company of question?.tagged_company_info; let last = last; let i = index" class="">
                                                            {{company ? company.name : ''}}
                                                            <span *ngIf="(i == 0 && question?.tagged_company_ref?.length > 1) || (i > 0 && !last)">, </span>
                                                        </span>
                                                    </h6>
                                                </div>
                                                <div class="mb-3">
                                                    <ng-template ngFor let-item [ngForOf]="question.images">
                                                        <div class="row p-0 mt-3 mx-0">
                                                            <div class="col px-2 py-2 d-flex align-items-center flex-wrap border rounded-lg">
                                                                <div class="p-0 d-inline-block text-truncate">
                                                                    <a [href]="item.file_url" target="_blank" class="medium-font fw-500" style="max-width: 220px;">{{item.name}}</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </ng-template>
                                                </div>
                                            </ng-container>
                                        </div>
                                    </ng-container>
                                </ng-container>
                            </ng-container>
                            <!-- end: subheadings checklist -->
                            <!-- start: without-subheadings checklist -->
                            <ng-container *ngIf="!isChecklistHasSubheading">
                                <ng-container *ngIf="builderObj.activeIndex === (isDraftAvailable ? 2 : 1)">
                                    <form novalidate #checkListWithoutSubHeadForm="ngForm">
                                        <div class="mt-3" [ngClass]="lastInd ? 'mb-0' : 'light-grey-bottom-border mb-4'">
                                            <div class="d-flex justify-content-between pr-0">
                                                <div>
                                                    <h6>{{checklist.item_que}}<small class="required-asterisk">*</small></h6>
                                                </div>
                                                <div >
                                                    <p class="cursor-pointer fw-500" (click)="editChecklist(checklist, mainCheckIndex, headingIndex)">
                                                        <span *ngIf="checklist.hasOwnProperty('answer') && (checklist.answer != '')">
                                                            <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M5.53225 2.20435H0.831309C0.697351 2.20435 0.588867 2.31296 0.588867 2.44679V12.152C0.588867 12.2859 0.697478 12.3944 0.831309 12.3944H10.9631C11.097 12.3944 11.2055 12.2858 11.2055 12.152L11.2054 6.72064" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                                                <path d="M3.72333 9.15909L4.47955 6.68904L9.96093 1.1879C10.205 0.942923 10.5998 0.936713 10.8515 1.17383L11.665 1.94032C11.9263 2.18644 11.9335 2.59959 11.681 2.85471L6.20048 8.39247L3.75578 9.1909C3.73601 9.19723 3.71725 9.17886 3.72333 9.15909Z" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                                                <path d="M4.47949 6.68921L6.20041 8.39251" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                                                <path d="M9.2334 1.91821L10.9159 3.62798" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                                                </svg>
                                                            <span class="pl-1">Edit</span>
                                                        </span>
                                                        <span class="pl-3" *ngIf="checklist?.isOther">
                                                            <button title="Delete Other" class="btn btn-sm float-md-right mt-0 pr-0 pt-0"
                                                            (click)="deleteOther($event, confirmationMetaData.inspectionBuilderConfModalType.CHEKLIST_DELETE, mainCheckIndex, headingIndex)">
                                                                <span class="material-symbols-outlined text-danger cursor-pointer">
                                                                    delete
                                                                </span>
                                                            </button>
                                                        </span>
                                                    </p>
                                                </div>
                                            </div>
                                            <ng-container *ngIf="builderObj.currentInspectionItem.scoring_system.type === 1">
                                                <div class="d-flex justify-content-between pb-1 pr-0" *ngFor="let val of builderObj.currentInspectionItem.scoring_system.values;let ind = index;let first = first;let last = last" [ngClass]="(first) ? 'pt-2' : 'pt-3'" [class.mb-3]="last && question?.isOther">
                                                    <label [attr.for]="'without_head_scoring_type_one_' + mainCheckIndex + '_' + ind"
                                                        [ngClass]="{
                                                        'color-good': val == builderObj.currentInspectionItem.scoring_system.values[0],
                                                        'color-fair': val == builderObj.currentInspectionItem.scoring_system.values[1],
                                                        'color-poor': val == builderObj.currentInspectionItem.scoring_system.values[2]
                                                    }"
                                                        class="w-100 cursor-pointer">
                                                        {{val}}
                                                    </label>
                                                    <div>
                                                        <input [id]="'without_head_scoring_type_one_' + mainCheckIndex + '_' + ind" [(ngModel)]="checklist.answer" (click)="confirmAnsChangReq($event, checklist, mainCheckIndex, headingIndex, val)" (change)="changeAnswer(checklist, mainCheckIndex, headingIndex, val); changed = true" class="cursor-pointer" type="radio" name="{{checklist.item_que}}" required value="{{val}}"/>
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-between mb-3 pt-3 pb-1 pr-0" *ngIf="!checklist?.isOther">
                                                    <label [attr.for]="'without_head_scoring_type_one_na' + mainCheckIndex + '_' + ind" class="w-100 cursor-pointer color-na">
                                                        N/A
                                                    </label>
                                                    <div>
                                                        <input [id]="'without_head_scoring_type_one_na' + mainCheckIndex + '_' + ind" [(ngModel)]="checklist.answer" (click)="confirmAnsChangReq($event, checklist, mainCheckIndex, headingIndex, 'n/a')" (change)="changeAnswer(checklist, mainCheckIndex, headingIndex, 'n/a'); changed = true" class="cursor-pointer" type="radio" name="{{checklist.item_que}}" required value="n/a"/>
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-center bg-grey-w-border-radius my-2" *ngIf="!hasSeverity && checklist.closeout_due_on && checklist.answer !== 'n/a'">
                                                    <h6 class="pt-2">Due Date: <span>{{showDate(checklist.closeout_due_on)}}</span></h6>
                                                </div>
                                                <div class="text-center bg-grey-w-border-radius my-2 py-2" *ngIf="hasSeverity && checklist.severity?.rating && checklist.answer !== 'n/a'">
                                                    <h6>Severity: <span [ngClass]="{'color-na': checklist.severity?.rating == 'Low','color-fair-fuel': checklist.severity?.rating == 'Medium','color-orange-high': checklist.severity?.option == 'High', 'color-critical-red': checklist.severity?.rating == 'Critical'}">{{checklist.severity?.rating}}</span></h6>
                                                    <p class="mb-0">Close Out:{{showDate(checklist?.closeout_due_on)}}</p>
                                                </div>
                                                <div class="d-flex justify-content-center bg-grey-w-border-radius my-2" *ngIf="hasRootCause && checklist.root_cause && checklist.answer !== 'n/a'">
                                                    <h6 class="pt-2">Root Cause: <span>{{checklist?.root_cause}}</span></h6>
                                                </div>
                                                <div class="d-flex justify-content-center bg-grey-w-border-radius my-2" *ngIf="checklist.tagged_user_ref && checklist.answer !== 'n/a'">
                                                    <h6 class="pt-2">Assignee: <span>{{checklist.tagged_user_ref ? checklist.tagged_user_ref.name : ''}}</span></h6>
                                                </div>
                                                <div *ngIf="checklist?.tagged_company_ref?.length > 0 && checklist.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="px-3 pt-2">Company: <span *ngFor="let company of checklist?.tagged_company_info; let last = last; let i = index" class=""> {{company ? company.name : ''}}<span *ngIf="((i == 0 && checklist?.tagged_company_ref?.length > 1) || (i > 0 && !last))">, </span></span></h6>
                                                </div>
                                                <div class="mb-3">
                                                    <ng-template ngFor let-item [ngForOf]="checklist.images">
                                                        <div class="row p-0 mt-3 mx-0">
                                                            <div class="col px-2 py-2 d-flex align-items-center flex-wrap border rounded-lg">
                                                                <div class="p-0 d-inline-block text-truncate">
                                                                    <a [href]="item.file_url" target="_blank" class="medium-font fw-500" style="max-width: 220px;">{{item.name}}</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </ng-template>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngIf="builderObj.currentInspectionItem.scoring_system.type === 2">
                                                <div class="d-flex justify-content-between pt-2 pb-1 pr-0">
                                                    <label [attr.for]="'without_head_scoring_type_two_ans3_' + mainCheckIndex" class="w-100 cursor-pointer color-good">
                                                        3
                                                    </label>
                                                    <div>
                                                        <input [id]="'without_head_scoring_type_two_ans3_' + mainCheckIndex" [(ngModel)]="checklist.answer" (click)="confirmAnsChangReq($event, checklist, mainCheckIndex, headingIndex, '3')" (change)="changeAnswer(checklist, mainCheckIndex, headingIndex, '3'); changed = true" class="cursor-pointer" type="radio" name="{{checklist.item_que}}" required value="3">
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-between pt-3 pb-1 pr-0">
                                                    <label [attr.for]="'without_head_scoring_type_two_ans2_' + mainCheckIndex"class="w-100 cursor-pointer color-fair">
                                                        2
                                                    </label>
                                                    <div>
                                                        <input [id]="'without_head_scoring_type_two_ans2_' + mainCheckIndex" [(ngModel)]="checklist.answer" (click)="confirmAnsChangReq($event, checklist, mainCheckIndex, headingIndex, '2')" (change)="changeAnswer(checklist, mainCheckIndex, headingIndex, '2'); changed = true" class="cursor-pointer" type="radio" name="{{checklist.item_que}}" required value="2">
                                                    </div>
                                                </div>
                                                <div [class.mb-3]="question?.isOther" class="d-flex justify-content-between pt-3 pb-1 pr-0">
                                                    <label [attr.for]="'without_head_scoring_type_two_ans1_' + mainCheckIndex" class="w-100 cursor-pointer color-poor">
                                                        1
                                                    </label>
                                                    <div>
                                                        <input [id]="'without_head_scoring_type_two_ans1_' + mainCheckIndex" [(ngModel)]="checklist.answer" (click)="confirmAnsChangReq($event, checklist, mainCheckIndex, headingIndex, '1')" (change)="changeAnswer(checklist, mainCheckIndex, headingIndex, '1'); changed = true" class="cursor-pointer" type="radio" name="{{checklist.item_que}}" required value="1">
                                                    </div>
                                                </div>
                                                <div *ngIf="!checklist?.isOther" class="d-flex justify-content-between mb-3 pt-3 pb-1 pr-0">
                                                    <label [attr.for]="'without_head_scoring_type_two_na_' + mainCheckIndex" class="w-100 cursor-pointer color-na">
                                                        N/A
                                                    </label>
                                                    <div>
                                                        <input [id]="'without_head_scoring_type_two_na_' + mainCheckIndex" [(ngModel)]="checklist.answer" (click)="confirmAnsChangReq($event, checklist, mainCheckIndex, headingIndex, 'n/a')" (change)="changeAnswer(checklist, mainCheckIndex, headingIndex, 'n/a'); changed = true" class="cursor-pointer" type="radio" name="{{checklist.item_que}}" required value="n/a">
                                                    </div>
                                                </div>
                                                <div *ngIf="!hasSeverity && checklist.closeout_due_on && checklist.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="pt-2">
                                                        Due Date:
                                                        <span>
                                                            {{showDate(checklist.closeout_due_on)}}
                                                        </span>
                                                    </h6>
                                                </div>
                                                <div *ngIf="hasSeverity && checklist.severity?.rating && checklist.answer !== 'n/a'" class="text-center bg-grey-w-border-radius my-2 py-2">
                                                    <h6>
                                                        Severity:
                                                        <span [ngClass]="{
                                                                    'color-na': checklist.severity?.rating == 'Low',
                                                                    'color-fair-fuel': checklist.severity?.rating == 'Medium',
                                                                    'color-orange-high': checklist.severity?.rating == 'High',
                                                                    'color-critical-red': checklist.severity?.rating == 'Critical'
                                                                }">
                                                            {{checklist.severity?.rating}}
                                                        </span>
                                                    </h6>
                                                    <p class="mb-0">
                                                        Close Out: {{showDate(checklist?.closeout_due_on)}}
                                                    </p>
                                                </div>
                                                <div *ngIf="hasRootCause && checklist.root_cause && checklist.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="pt-2">
                                                        Root Cause:
                                                         <span>{{checklist?.root_cause}}</span>
                                                    </h6>
                                                </div>
                                                <div *ngIf="checklist.tagged_user_ref && checklist.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="pt-2">
                                                        Assignee:
                                                        <span>
                                                            {{checklist.tagged_user_ref ? checklist.tagged_user_ref.name : ""}}
                                                        </span>
                                                    </h6>
                                                </div>
                                                <div *ngIf="checklist?.tagged_company_ref?.length > 0 && checklist.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="px-3 pt-2">
                                                        Company:
                                                        <span *ngFor="let company of checklist?.tagged_company_info; let last = last; let i = index" class="">
                                                            {{company ? company.name : ""}}
                                                            <span *ngIf="(i == 0 && checklist?.tagged_company_ref?.length > 1) || (i > 0 && !last)">, </span>
                                                        </span>
                                                    </h6>
                                                </div>
                                                <div class="mb-3">
                                                    <ng-template ngFor let-item [ngForOf]="checklist.images">
                                                        <div class="row p-0 mt-3 mx-0">
                                                            <div class="col px-2 py-2 d-flex align-items-center flex-wrap border rounded-lg">
                                                                <div class="p-0 d-inline-block text-truncate">
                                                                    <a [href]="item.file_url" target="_blank" class="medium-font fw-500" style="max-width: 220px;">{{item.name}}</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </ng-template>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngIf="builderObj.currentInspectionItem.scoring_system.type === 3">
                                                <div class="d-flex justify-content-between pt-2 pb-1 pr-0">
                                                    <label [attr.for]="'without_head_scoring_type_three_yes_' + mainCheckIndex" class="w-100 cursor-pointer color-good">
                                                        Yes
                                                    </label>
                                                    <div>
                                                        <input [id]="'without_head_scoring_type_three_yes_' + mainCheckIndex" [(ngModel)]="checklist.answer" (click)="confirmAnsChangReq($event, checklist, mainCheckIndex, headingIndex, 'Yes')" (change)="changeAnswer(checklist, mainCheckIndex, headingIndex, 'Yes'); changed = true" class="cursor-pointer" type="radio" name="{{checklist.item_que}}" required value="Yes">
                                                    </div>
                                                </div>
                                                <div [class.mb-3]="question?.isOther" class="d-flex justify-content-between pt-3 pb-1 pr-0">
                                                    <label [attr.for]="'without_head_scoring_type_three_no_' + mainCheckIndex"
                                                           class="w-100 cursor-pointer color-poor">
                                                        No
                                                    </label>
                                                    <div>
                                                        <input [id]="'without_head_scoring_type_three_no_' + mainCheckIndex" [(ngModel)]="checklist.answer" (click)="confirmAnsChangReq($event, checklist, mainCheckIndex, headingIndex, 'No')" (change)="changeAnswer(checklist, mainCheckIndex, headingIndex, 'No'); changed = true" class="cursor-pointer" type="radio" name="{{checklist.item_que}}" required value="No">
                                                    </div>
                                                </div>
                                                <div *ngIf="!checklist?.isOther" class="d-flex justify-content-between cursor-pointer mb-3 pt-3 pb-1 pr-0">
                                                    <label [attr.for]="'without_head_scoring_type_three_na_' + mainCheckIndex" class="w-100 cursor-pointer color-na">
                                                        N/A
                                                    </label>
                                                    <div>
                                                        <input [id]="'without_head_scoring_type_three_na_' + mainCheckIndex" [(ngModel)]="checklist.answer" (click)="confirmAnsChangReq($event, checklist, mainCheckIndex, headingIndex, 'n/a')" (change)="changeAnswer(checklist, mainCheckIndex, headingIndex, 'n/a'); changed = true" class="cursor-pointer" type="radio" name="{{checklist.item_que}}" required value="n/a">
                                                    </div>
                                                </div>
                                                <div *ngIf="!hasSeverity && checklist.closeout_due_on && checklist.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="pt-2">
                                                        Due Date:
                                                        <span>{{showDate(checklist.closeout_due_on)}}</span>
                                                    </h6>
                                                </div>
                                                <div *ngIf="hasSeverity && checklist.severity?.rating && checklist.answer !== 'n/a'" class="text-center bg-grey-w-border-radius my-2 py-2">
                                                    <h6>
                                                        Severity:
                                                        <span [ngClass]="{
                                                            'color-na': checklist.severity?.rating == 'Low',
                                                            'color-fair-fuel': checklist.severity?.rating == 'Medium',
                                                            'color-orange-high': checklist.severity?.rating == 'High',
                                                            'color-critical-red': checklist.severity?.rating == 'Critical'
                                                        }">
                                                        {{checklist.severity?.rating}}
                                                    </span>
                                                </h6>
                                                <p class="mb-0">
                                                    Close Out: {{showDate(checklist?.closeout_due_on)}}
                                                </p>
                                            </div>
                                            <div *ngIf="hasRootCause && checklist.root_cause && checklist.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                <h6 class="pt-2">
                                                        Root Cause:
                                                        <span>{{checklist?.root_cause}}</span>
                                                    </h6>
                                                </div>
                                                <div *ngIf="checklist.tagged_user_ref && checklist.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="pt-2">
                                                        Assignee:
                                                        <span>
                                                            {{checklist.tagged_user_ref ? checklist.tagged_user_ref.name : ""}}
                                                        </span>
                                                    </h6>
                                                </div>
                                                <div *ngIf="checklist?.tagged_company_ref?.length > 0 && checklist.answer !== 'n/a'" class="d-flex justify-content-center bg-grey-w-border-radius my-2">
                                                    <h6 class="px-3 pt-2">
                                                        Company:
                                                        <span *ngFor="let company of checklist?.tagged_company_info; let last = last; let i = index" class="">
                                                            {{company ? company.name : ""}}
                                                            <span *ngIf="(i == 0 && checklist?.tagged_company_ref?.length > 1) || (i > 0 && !last)">, </span>
                                                        </span>
                                                    </h6>
                                                </div>
                                                <div *ngIf="checklist.images && checklist.images.length" class="mb-3">
                                                    <ng-template ngFor let-item [ngForOf]="checklist.images">
                                                        <div class="row p-0 mt-3 mx-0">
                                                            <div class="col px-2 py-2 d-flex align-items-center flex-wrap border rounded-lg">
                                                                <div class="p-0 d-inline-block text-truncate">
                                                                    <a [href]="item.file_url" target="_blank" class="medium-font fw-500" style="max-width: 220px;">{{item.name}}</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </ng-template>
                                                </div>
                                            </ng-container>
                                        </div>
                                    </form>
                                </ng-container>
                            </ng-container>
                            <!-- end: without-subheadings checklist -->
                        </ng-container>
                    </ng-container>
                    <!--start: summary form -->
                    <ng-container *ngIf="(isChecklistHasSubheading && builderObj.selectedHeading === 'Summary') || (!isChecklistHasSubheading && builderObj.selectedHeading === 'Summary')">
                        <form novalidate #inspectionBuilderSummForm="ngForm">
                            <div class="form-group mt-1">
                                <label>Date of Inspection  <small class="required-asterisk">*</small></label>
                                <div class="input-group mb-3">
                                    <input class="form-control" placeholder="DD-MM-YYYY" required readonly [maxDate]="maxDate"
                                           name="report_datetime" [(ngModel)]="builderObj.inspectionDate" ngbDatepicker 
                                           #report_datetime="ngbDatepicker" (ngModelChange)="onChangeDateTime($event); changed = true" ng-value="builderObj.inspectionDate"
                                    />
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary calendar" (click)="report_datetime.toggle(); changed = true" type="button">
                                            <i class="fa fa-calendar"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="d-sm-flex align-items-center justify-content-between m-0">
                                    <div class="pl-0 mb-2 mb-xl-0">
                                        Time of Inspection:
                                    </div>
                                    <div class="">
                                        <ngb-timepicker class="inspection-time d-inline-block ml-0" [spinners]="false" [(ngModel)]="builderObj.inspectionTime" name="inspection_time" [size]="'small'" (change)="changeTime(); changed = true"></ngb-timepicker>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Summary</label>
                                <div class="input-group mb-3">
                                    <textarea
                                        class="form-control"
                                        name="summary"
                                        placeholder="Enter Summary"
                                        [(ngModel)]="builderObj.summary" 
                                        [value]="builderObj.summary"
                                        (change)="changed = true"
                                    ></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="d-inline-block"
                                    >Inspection Participants</label>
                                <div class="input-group mb-3">
                                    <div class="row g-0 w-100 ml-0 mr-0">
                                        <div class="col-11 px-0 select-participants">
                                            <ng-select class="ins-participants insp-drop-w ins-drop-pr pr-0" [multiple]="true" [items]="builderObj.filteredInductedUsers" bindLabel="name" bindValue="user_ref"
                                            name="assignTo" placeholder="Select Inspection Participants" [(ngModel)]="builderObj.participants"
                                            [value]="builderObj.participants" (change)="changed = true">
                                    </ng-select>
                                        </div>
                                        <div class="col-1 pr-0 my-auto">
                                            <div class="d-flex align-items-center justify-content-end position-relative">
                                                <span class="cursor-pointer" (click)="openFilterByCompanyModal()">
                                                    <svg width="18" height="12" viewBox="0 0 18 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M3.63135 5.73682H14.1577" stroke="black" stroke-width="1.5" stroke-linecap="round"/>
                                                        <path d="M1 1H16.7895" stroke="black" stroke-width="1.5" stroke-linecap="round"/>
                                                        <path d="M6.26318 11H11.5263" stroke="black" stroke-width="1.5" stroke-linecap="round"/>
                                                    </svg>
                                                </span>
                                                <div *ngIf="builderObj.tempSelectedCompanyNames.length" class="filter-indicator-circle position-absolute rounded-circle bg-warning" [ngClass]="{'top-22': (builderObj.currentInspectionItem.participants)}"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </ng-container>
                    <!--end: summary form -->
                    <!--start: preview -->
                    <ng-container *ngIf="(isChecklistHasSubheading && builderObj.selectedHeading === 'Review') || (!isChecklistHasSubheading && builderObj.selectedHeading === 'Review')">
                        <div class="card mb-1" *ngIf="builderObj.report_datetime">
                            <div class="card-body p-3" >
                                <p class="font-weight-light m-0 p-0 fs-size-8">Date & Time of Inspection</p>
                                <p class="m-0">{{dayjs(+builderObj.report_datetime).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS)}}</p>
                            </div>
                        </div>
                        <ng-container *ngIf="hasDetailFields">
                            <div class="d-flex justify-content-between mt-3 pt-2 pb-2 pl-2 pr-2 mt-1 mb-1 btn-im-helmet rounded">
                                <div class="font-weight-bold">
                                    Details 
                                </div>
                                <div class="d-flex align-items-center cursor-pointer" (click)="goToPerticularChecklist(isChecklistHasSubheading ? (isDraftAvailable ? 2 : 1) : (isDraftAvailable ? 1 : 0))">
                                    <span class="pr-2">Edit</span>
                                    <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M5.53225 2.20435H0.831309C0.697351 2.20435 0.588867 2.31296 0.588867 2.44679V12.152C0.588867 12.2859 0.697478 12.3944 0.831309 12.3944H10.9631C11.097 12.3944 11.2055 12.2858 11.2055 12.152L11.2054 6.72064" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                        <path d="M3.72333 9.15909L4.47955 6.68904L9.96093 1.1879C10.205 0.942923 10.5998 0.936713 10.8515 1.17383L11.665 1.94032C11.9263 2.18644 11.9335 2.59959 11.681 2.85471L6.20048 8.39247L3.75578 9.1909C3.73601 9.19723 3.71725 9.17886 3.72333 9.15909Z" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                        <path d="M4.47949 6.68921L6.20041 8.39251" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                        <path d="M9.2334 1.91821L10.9159 3.62798" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                    </svg>
                                </div>
                            </div>
                            <ng-container *ngFor="let formField of builderObj.currentInspectionItem.detail_fields;">
                                <div class="card mb-1" *ngIf="formField.field_value">
                                    <div class="card-body p-3">
                                        <ng-container>
                                            <p class="font-weight-light p-0 m-0 fs-size-8">{{formField?.field}}</p>
                                            <p class="m-0 summary-text">{{formField?.field_value}}</p>
                                        </ng-container>
                                    </div>
                                </div>
                            </ng-container>
                        </ng-container>
                        <!-- start: preview with subheading -->
                        <ng-container *ngIf="isChecklistHasSubheading">
                            <ng-container *ngFor="let checklistPrev of builderObj.currentInspectionItem.checklist;let mainCheckIndexPrev = index">
                                <div class="d-flex justify-content-between mt-3 pt-2 pb-2 pl-2 pr-2 mt-1 mb-1 btn-im-helmet rounded">
                                    <div class="font-weight-bold">
                                        {{checklistPrev.heading}}
                                    </div>
                                    <div class="d-flex align-items-center cursor-pointer" (click)="goToPerticularChecklist(mainCheckIndexPrev + (isDraftAvailable ? 3 : 2))">
                                        <span class="pr-2">Edit</span>
                                        <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M5.53225 2.20435H0.831309C0.697351 2.20435 0.588867 2.31296 0.588867 2.44679V12.152C0.588867 12.2859 0.697478 12.3944 0.831309 12.3944H10.9631C11.097 12.3944 11.2055 12.2858 11.2055 12.152L11.2054 6.72064" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                            <path d="M3.72333 9.15909L4.47955 6.68904L9.96093 1.1879C10.205 0.942923 10.5998 0.936713 10.8515 1.17383L11.665 1.94032C11.9263 2.18644 11.9335 2.59959 11.681 2.85471L6.20048 8.39247L3.75578 9.1909C3.73601 9.19723 3.71725 9.17886 3.72333 9.15909Z" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                            <path d="M4.47949 6.68921L6.20041 8.39251" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                            <path d="M9.2334 1.91821L10.9159 3.62798" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                            </svg>
                                    </div>
                                </div>
                                <ng-container>
                                    <ng-container *ngFor="let questionPrev of checklistPrev.subheadings;let headingIndexPrev = index">
                                        <div class="card checklist-preview-card mb-1" *ngIf="questionPrev.hasOwnProperty('answer')" [ngClass]="{'border-left-good-color': (questionPrev?.answer?.toLowerCase() === 'good' || questionPrev?.answer == '3' || questionPrev?.answer?.toLowerCase() == 'yes' || questionPrev?.answer?.toLowerCase() == builderObj.currentInspectionItem?.scoring_system?.values[0].toLowerCase()),
                                            'border-left-fair-color': (questionPrev?.answer?.toLowerCase() === 'fair' || questionPrev?.answer == '2' || questionPrev?.answer?.toLowerCase() == builderObj.currentInspectionItem?.scoring_system?.values[1]),
                                            'border-left-poor-color': (questionPrev?.answer.toLowerCase() === 'poor' || questionPrev?.answer == '1' || questionPrev?.answer?.toLowerCase() == 'no' || questionPrev?.answer?.toLowerCase() == builderObj.currentInspectionItem?.scoring_system?.values[2]),
                                            'border-left-na-color': (questionPrev?.answer.toLowerCase() === 'n/a') }">
                                            <div class="card-body p-3">
                                                <div class="mb-1">
                                                    <p class="m-0 p-0 fs-size-8">{{questionPrev.item_que}}</p>
                                                    <p class="m-0 fs-size-8">Checked:<span class="font-weight-normal">{{questionPrev?.answer}}</span></p>
                                                </div>
                                                <div *ngIf="questionPrev.summary" class="mb-1">
                                                    <p class="m-0 font-weight-bold fs-size-8">Summary</p>
                                                    <p class="p-0 m-0 fs-size-8 summary-text">{{questionPrev.summary}}</p>
                                                </div>
                                                <div class="d-flex justify-content-center bg-grey-w-border-radius my-2" *ngIf="!hasSeverity && questionPrev.closeout_due_on && questionPrev.answer !== 'n/a'">
                                                    <h6 class="pt-2">Due Date: <span>{{showDate(questionPrev.closeout_due_on)}}</span></h6>
                                                </div>
                                                <div class="text-center bg-grey-w-border-radius my-2 py-2" *ngIf="hasSeverity && questionPrev.severity?.rating && questionPrev.answer !== 'n/a'">
                                                    <h6>Severity: <span [ngClass]="{'color-na': (questionPrev.severity?.rating == 'Low'),'color-fair-fuel': (questionPrev.severity?.rating == 'Medium'), 'color-orange-high': (questionPrev.severity?.rating == 'High'), 'color-critical-red': (questionPrev.severity?.rating == 'Critical')}">{{questionPrev.severity?.rating}}</span></h6>
                                                    <p class="mb-0">Close Out: {{showDate(questionPrev?.closeout_due_on)}}</p>
                                                </div>
                                                <div class="d-flex justify-content-center bg-grey-w-border-radius my-2" *ngIf="hasRootCause && questionPrev.root_cause && questionPrev.answer !== 'n/a'">
                                                    <h6 class="pt-2">Root Cause: <span>{{questionPrev?.root_cause}}</span></h6>
                                                </div>
                                                <div class="d-flex justify-content-center bg-grey-w-border-radius my-2" *ngIf="questionPrev.tagged_user_ref">
                                                    <h6 class="pt-2">Assignee: <span>{{questionPrev.tagged_user_ref ? questionPrev.tagged_user_ref.name : ""}}</span></h6>
                                                </div>
                                                <div class="d-flex justify-content-center bg-grey-w-border-radius my-2" *ngIf="questionPrev?.tagged_company_ref?.length > 0 && questionPrev.answer !== 'n/a'">
                                                   <h6 class="px-3 pt-2">Company: <span class="" *ngFor="let company of questionPrev?.tagged_company_info;let last = last;let i = index">{{company ? company.name : ''}}<span *ngIf="((i==0 && questionPrev?.tagged_company_ref?.length > 1) || (i > 0 && !last))">, </span></span></h6>
                                                </div>
                                                <ng-container *ngIf="questionPrev?.images?.length > 1">
                                                            <ng-template ngFor let-item [ngForOf]="questionPrev.images">
                                                                <div class="row p-0 mt-3 mx-0">
                                                                    <div class="col px-2 py-2 d-flex align-items-center flex-wrap border rounded-lg">
                                                                        <div class="p-0 d-inline-block text-truncate">
                                                                            <a [href]="item.file_url" target="_blank" class="medium-font fw-500" style="max-width: 220px;">{{item.name}}</a>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </ng-template>
                                                </ng-container>
                                            </div>
                                        </div>
                                    </ng-container>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                        <!-- end: preview with subheading -->
                        <!-- start: preview without subheading -->
                        <ng-container *ngIf="!isChecklistHasSubheading">
                            <div class="d-flex justify-content-between mt-3 pt-2 pb-2 pl-2 pr-2 mt-1 mb-1 btn-im-helmet rounded">
                                <div class="font-weight-bold">Checklist</div>
                                <div class="d-flex align-items-center cursor-pointer" (click)="goToPerticularChecklist(isDraftAvailable ? 2 : 1)">
                                    <span class="pr-2">Edit</span>
                                    <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M5.53225 2.20435H0.831309C0.697351 2.20435 0.588867 2.31296 0.588867 2.44679V12.152C0.588867 12.2859 0.697478 12.3944 0.831309 12.3944H10.9631C11.097 12.3944 11.2055 12.2858 11.2055 12.152L11.2054 6.72064" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                        <path d="M3.72333 9.15909L4.47955 6.68904L9.96093 1.1879C10.205 0.942923 10.5998 0.936713 10.8515 1.17383L11.665 1.94032C11.9263 2.18644 11.9335 2.59959 11.681 2.85471L6.20048 8.39247L3.75578 9.1909C3.73601 9.19723 3.71725 9.17886 3.72333 9.15909Z" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                        <path d="M4.47949 6.68921L6.20041 8.39251" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                        <path d="M9.2334 1.91821L10.9159 3.62798" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                    </svg>
                                </div>
                            </div>
                            <ng-container *ngFor="let checklistPrev of builderObj.currentInspectionItem.checklist;let mainCheckIndexPrev = index">
                                <div class="card checklist-preview-card mb-1" *ngIf="checklistPrev.hasOwnProperty('answer')" [ngClass]="{'border-left-good-color': (checklistPrev?.answer?.toLowerCase() === 'good' || checklistPrev?.answer == '3' || checklistPrev?.answer?.toLowerCase() == 'yes' || checklistPrev?.answer?.toLowerCase() == builderObj.currentInspectionItem?.scoring_system?.values[0]?.toLowerCase()),
                                    'border-left-fair-color': (checklistPrev?.answer?.toLowerCase() === 'fair' || checklistPrev?.answer == '2' || checklistPrev?.answer?.toLowerCase() == builderObj.currentInspectionItem?.scoring_system?.values[1]?.toLowerCase()),
                                    'border-left-poor-color': (checklistPrev?.answer?.toLowerCase() === 'poor' || checklistPrev?.answer == '1' || checklistPrev?.answer?.toLowerCase() == 'no' || checklistPrev?.answer?.toLowerCase() == builderObj.currentInspectionItem?.scoring_system?.values[2]?.toLowerCase()),
                                    'border-left-na-color': (checklistPrev?.answer?.toLowerCase() === 'n/a') }">
                                    <div class="card-body p-3">
                                        <div class="mb-1">
                                            <p class="m-0 p-0 fs-size-8">{{checklistPrev.item_que}}</p>
                                            <p class="m-0 fs-size-8">Checked: <span class="font-weight-normal">{{checklistPrev?.answer}}</span></p>
                                        </div>
                                        <div class="mb-1" *ngIf="checklistPrev.summary">
                                            <p class="m-0 font-weight-bold fs-size-8">Summary</p>
                                            <p class="p-0 m-0 fs-size-8 summary-text">{{checklistPrev.summary}}</p>
                                        </div>
                                        <div class="d-flex justify-content-center bg-grey-w-border-radius my-2" *ngIf="!hasSeverity && checklistPrev.closeout_due_on && checklistPrev.answer !== 'n/a'">
                                            <h6 class="pt-2">Due Date: <span>{{showDate(checklistPrev.closeout_due_on)}}</span></h6>
                                        </div>
                                        <div class="text-center bg-grey-w-border-radius my-2 py-2" *ngIf="hasSeverity && checklistPrev.severity?.rating && checkSeverity(checklistPrev.severity) && checklistPrev.answer !== 'n/a'">
                                            <h6>Severity: <span [ngClass]="{'color-na': (checklistPrev.severity?.rating == 'Low'),'color-fair-fuel': (checklistPrev.severity?.rating == 'Medium'), 'color-orange-high': (checklistPrev.severity?.rating == 'High'), 'color-critical-red': (checklistPrev.severity?.rating == 'Critical')}">{{checklistPrev.severity?.rating}}</span></h6>
                                            <p class="mb-0">Close Out: {{showDate(checklistPrev?.closeout_due_on)}}</p>
                                        </div>
                                        <div class="d-flex justify-content-center bg-grey-w-border-radius my-2" *ngIf="hasRootCause && checklistPrev.root_cause && checklistPrev.answer !== 'n/a'">
                                            <h6 class="pt-2">Root Cause: <span>{{checklistPrev?.root_cause}}</span></h6>
                                        </div>
                                        <div class="d-flex justify-content-center bg-grey-w-border-radius my-2" *ngIf="checklistPrev.tagged_user_ref">
                                            <h6 class="pt-2">Assignee: <span>{{checklistPrev.tagged_user_ref ? checklistPrev.tagged_user_ref.name : ""}}</span></h6>
                                        </div>
                                        <div class="d-flex justify-content-center bg-grey-w-border-radius my-2 bg-grey-w-border-radius my-2" *ngIf="checklistPrev?.tagged_company_ref?.length > 0 && checklistPrev.answer !== 'n/a'">
                                            <h6 class="px-3 pt-2">Company: <span class="" *ngFor="let company of checklistPrev.tagged_company_info;let last = last;let i = index">{{company ? company.name : ''}}<span *ngIf="((i==0 && checklistPrev?.tagged_company_ref?.length > 1) || (i > 0 && !last))">, </span></span></h6>
                                        </div>
                                        <ng-container *ngIf="checklistPrev?.images?.length > 0">
                                                <ng-template ngFor let-item [ngForOf]="checklistPrev.images">
                                                    <div class="row p-0 mt-3 mx-0">
                                                        <div class="col px-2 py-2 d-flex align-items-center flex-wrap border rounded-lg">
                                                            <div class="p-0 d-inline-block text-truncate">
                                                                <a [href]="item.file_url" target="_blank" class="medium-font fw-500" style="max-width: 220px;">{{item.name}}</a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </ng-template>
                                        </ng-container>
                                    </div>
                                </div>
                            </ng-container>
                        </ng-container>
                        <!-- end: preview without subheading -->
                        <!-- start: summary -->
                        <ng-container>
                            <div class="d-flex justify-content-between mt-3 pt-2 pb-2 pl-2 pr-2 mt-1 mb-1 btn-im-helmet rounded">
                                <div class="font-weight-bold">
                                    Summary
                                </div>
                                <div class="d-flex align-items-center cursor-pointer" (click)="goToPerticularChecklist(isChecklistHasSubheading ? builderObj.headings.length + (isDraftAvailable ? 3 : 2) : isDraftAvailable ? 3 : 2)">
                                    <span class="pr-2">Edit</span>
                                    <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M5.53225 2.20435H0.831309C0.697351 2.20435 0.588867 2.31296 0.588867 2.44679V12.152C0.588867 12.2859 0.697478 12.3944 0.831309 12.3944H10.9631C11.097 12.3944 11.2055 12.2858 11.2055 12.152L11.2054 6.72064" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                        <path d="M3.72333 9.15909L4.47955 6.68904L9.96093 1.1879C10.205 0.942923 10.5998 0.936713 10.8515 1.17383L11.665 1.94032C11.9263 2.18644 11.9335 2.59959 11.681 2.85471L6.20048 8.39247L3.75578 9.1909C3.73601 9.19723 3.71725 9.17886 3.72333 9.15909Z" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                        <path d="M4.47949 6.68921L6.20041 8.39251" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                        <path d="M9.2334 1.91821L10.9159 3.62798" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="card mb-1" *ngIf="builderObj.summary">
                                <div class="card-body p-3" >
                                    <div>
                                        <p class="p-0 m-0 fs-size-8 summary-text">{{builderObj.summary}}</p>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                        <!-- end: summary -->
                        <!-- start: inspection participants -->
                        <ng-container *ngIf="builderObj.participants?.length > 0">
                            <div class="d-flex justify-content-between mt-3 pt-2 pb-2 pl-2 pr-2 mt-1 mb-1 btn-im-helmet rounded">
                                <div class="font-weight-bold">
                                    Inspection Participants
                                </div>
                                <div (click)="goToPerticularChecklist(
                                        isChecklistHasSubheading
                                            ? builderObj.headings.length + (isDraftAvailable ? 3 : 2)
                                            : builderObj.headings.length + (isDraftAvailable ? 2 : 1)
                                    )"
                                    class="d-flex align-items-center cursor-pointer">
                                    <span class="pr-2">Edit</span>
                                    <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M5.53225 2.20435H0.831309C0.697351 2.20435 0.588867 2.31296 0.588867 2.44679V12.152C0.588867 12.2859 0.697478 12.3944 0.831309 12.3944H10.9631C11.097 12.3944 11.2055 12.2858 11.2055 12.152L11.2054 6.72064" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                        <path d="M3.72333 9.15909L4.47955 6.68904L9.96093 1.1879C10.205 0.942923 10.5998 0.936713 10.8515 1.17383L11.665 1.94032C11.9263 2.18644 11.9335 2.59959 11.681 2.85471L6.20048 8.39247L3.75578 9.1909C3.73601 9.19723 3.71725 9.17886 3.72333 9.15909Z" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                        <path d="M4.47949 6.68921L6.20041 8.39251" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                        <path d="M9.2334 1.91821L10.9159 3.62798" stroke="black" stroke-width="0.5" stroke-miterlimit="10" stroke-linecap="round"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="card mb-1">
                                <div class="card-body d-flex participant-container">
                                    <div class="row justify-content-evenly m-0">
                                        <div class="d-flex mr-1 justify-content-center mb-2 participant-box" *ngFor="let participant of builderObj.participants; trackBy: trackByParticipant;let i = index">
                                            <span class="d-flex align-items-center justify-content-between">
                                                <div class="d-flex justify-content-center cursor-pointer close-box" (click)="removeParticipant(i)">
                                                    <span aria-hidden="true">&times;</span>
                                                </div>
                                                <p class="m-0 fs-size-8 fw-400 px-2">{{getAssigneeName(participant) ? getAssigneeName(participant) : ''}}</p>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                        <!-- end: inspection participants -->
                        <!-- start: sign-off -->
                        <div class="d-flex justify-content-between mt-3 pt-2 pb-2 pl-2 pr-2 mt-1 mb-1 btn-im-helmet rounded">
                            <div class="font-weight-bold">
                                Sign Off
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-md-12 form-inline mb-1" [class.mt-3]="hasSignature()">
                                <signature-pad-selector class="w-100" *ngIf="!hasSignature()" 
                                    [height]="130"
                                    [width]="700"
                                    (signChanged)="saveSignImage($event); changed = true"
                                    (clear)="clearSignImage()"
                                ></signature-pad-selector>
                                <div *ngIf="hasSignature()" class="img-file-wrap"> 
                                    <img class="w-100" [src]="this.builderObj.currentInspectionItem['sign']" alt="signature">
                                    <button (click)="clearSignImage()" class="btn btn-sm border-0 btn-outline-brandeis-blue float-right sign-cancel fw-600 mr-3 px-2">Clear</button>
                                </div>
                            </div>
                        </div>
                        <!-- end: sign-off -->
                    </ng-container>
                    <!-- end: preview -->
                    <div class="w-100 d-flex mt-0 justify-content-center" *ngIf="(isChecklistHasSubheading && builderObj.selectedHeading !== 'Content' && builderObj.selectedHeading !== 'Details' && builderObj.activeIndex <= builderObj.headings.length + (isDraftAvailable ? 2 : 1)) || (!isChecklistHasSubheading && builderObj.selectedHeading !== 'Content' && builderObj.selectedHeading !== 'Details' && builderObj.activeIndex <= builderObj.headings.length + (isDraftAvailable ? 1 : 0))">
                        <div class="d-flex justify-content-center cursor-pointer mt-0 mb-1">
                            <action-button
                                [showActionDropdown]="false"
                                [newFeatureTitle]="'Add Other'"
                                (onOpenAddNew)="addOtherChecklist();changed = true">
                            </action-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="light-grey-left-border check-list-content-w py-4 mb-0 col-two" [ngClass]="(builderObj.selectedHeading != 'Review') ? 'modal-max-h' : 'modal-max-h-preview'" id="column2" *ngIf="builderObj.isSecColShow">
            <form novalidate #inspectionBuilderInfoForm="ngForm" class="mx-4">
                <div>
                    <h6>{{builderObj.selectedQueName}}</h6>
                </div>
                <div class="form-group">
                    <label>Summary <small *ngIf="showInputRequiredAndValidationIndicator() && inspectionBuilderChecklistObj?.answer !== 'n/a'" class="required-asterisk">*</small>
                    </label>
                    <div class="input-group mb-3">
                        <textarea 
                            class="form-control"
                            name="summary"
                            placeholder="Enter Summary"
                            [(ngModel)]="inspectionBuilderChecklistObj.summary"
                            [value]="inspectionBuilderChecklistObj.summary"
                            [required]="showInputRequiredAndValidationIndicator() && inspectionBuilderChecklistObj?.answer !== 'n/a'"
                        ></textarea>
                    </div>
                </div>
                <div class="form-group" *ngIf="showInputRequiredAndValidationIndicator() && inspectionBuilderChecklistObj?.answer !== 'n/a'">
                    <label>Correction Action Required</label>
                    <div class="input-group mb-3">
                        <input 
                            #corrective_action_required="ngModel" 
                            [(ngModel)]="inspectionBuilderChecklistObj.corrective_action_required" 
                            class="form-control" 
                            name="corrective_action_required" 
                            ng-value="inspectionBuilderChecklistObj.corrective_action_required"
                            placeholder="Enter Correction Action Required" 
                            type="text"
                        />
                    </div>
                </div>
                <div class="form-group" *ngIf="!hasSeverity && showInputRequiredAndValidationIndicator() && inspectionBuilderChecklistObj?.answer !== 'n/a'">
                    <label>Due Date</label>
                    <div class="input-group mb-3">
                        <input class="form-control" placeholder="DD-MM-YYYY" readonly
                                name="closeout_due_on" [(ngModel)]="inspectionBuilderChecklistObj.closeout_due_on" ngbDatepicker
                                #closeout_due_on="ngbDatepicker" ng-value="inspectionBuilderChecklistObj.closeout_due_on"
                        />
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary calendar" (click)="closeout_due_on.toggle()" type="button">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="form-group" *ngIf="hasSeverity && inspectionBuilderChecklistObj?.answer !== 'n/a' && showInputRequiredAndValidationIndicator()">
                    <label class="d-inline-block">Severity <small *ngIf="severityMandatory" class="required-asterisk">*</small></label>
                    <ng-select class="w-100" name="insSeverity" #insSeverity="ngModel" [compareWith]="compareSeverity" [(ngModel)]="inspectionBuilderChecklistObj.severity"
                    placeholder="Select Severity" [required]="severityMandatory">
                    <ng-option *ngFor="let severity of builderObj.severities" [value]="severity">{{ severity?.rating }}</ng-option>
                </ng-select>
                </div>
                <div class="form-group" *ngIf="hasRootCause && showInputRequiredAndValidationIndicator() && inspectionBuilderChecklistObj?.answer !== 'n/a'">
                    <label class="d-inline-block">Root Cause <small *ngIf="rootCauseMandatory" class="required-asterisk">*</small></label>
                    <ng-select class="w-100" name="insRootCause" #insRootCause="ngModel" [(ngModel)]="inspectionBuilderChecklistObj.root_cause"
                        placeholder="Select Root Cause" [required]="rootCauseMandatory">
                        <ng-option *ngFor="let rootCause of builderObj.rootCauses" [value]="rootCause.name">{{ rootCause.name }}</ng-option>
                    </ng-select>
                </div>
                <div class="form-group" *ngIf="inspectionBuilderChecklistObj?.answer !== 'n/a'">
                    <label class="d-inline-block"
                        >Assign To</label
                    >
                    <inducted-user-selector
                        #userSelector
                        placeholder="Select Assignee"
                        name="assignTo"
                        [projectId]="projectId"
                        [required]="false"
                        [selection]="inspectionBuilderChecklistObj.tagged_user_ref?.user_ref ? inspectionBuilderChecklistObj.tagged_user_ref.user_ref : inspectionBuilderChecklistObj.tagged_user_ref?.id"
                        (selectionChanged)="inspectionBuilderChecklistObj.tagged_user_ref = $event.selectedRecord"
                    ></inducted-user-selector>
                </div>
                <div class="form-group" *ngIf="inspectionBuilderChecklistObj?.answer !== 'n/a'"> 
                    <label class="d-inline-block">Tag Company </label>
                    <company-selector-v2
                        [required]="false"
                        [country_code]="projectCountryCode"
                        name="tagCompany"
                        placeholder="Select Company"
                        [selectId]="inspectionBuilderChecklistObj.tagged_company_ref"
                        (selectionChanged)="inspectionBuilderChecklistObj.tagged_company_ref =  $event.record"
                        [multiple]="true"
                        [projectId]="projectId"
                    ></company-selector-v2>
                </div>
                <div *ngIf="inspectionBuilderChecklistObj?.answer !== 'n/a'">
                    <div class="col-md-12 p-0 mt-3 mb-2"><h6>Add Attachments</h6></div>
                    <div class="col-md-12 p-0 d-flex flex-wrap mb-1">
                        <div 
                            [ngClass]="{
                                'flex-grow-1 p-0': true,
                                'col-md-4': item?.id,
                                'col-md-12 mb-3': !item?.id
                            }">
                            <file-uploader-v2
                                #attachmentUploader
                                (uploadDone)="imageUploadDone($event)" 
                                [allowedMimeType]="[
                                        'image/jpeg',
                                        'image/jpg',
                                        'image/png',
                                        'application/pdf'
                                    ]"
                                [category]="'inspection-docs-attachment'"
                                [disabled]="false"
                                [init]="attachments"
                                [multipleUpload]="true"
                                [showThumbnail]="true"
                                [showDeleteBtn]="false"
                                [showDragnDrop]="true"
                                [showFileName]="false"
                                [showHyperlink]="false"
                                [applyCustomPadding]="false"
                                class="pl-0"
                            >
                            </file-uploader-v2>
                            <ng-container *ngIf="newPhotos && newPhotos.length > 0">
                                <ng-template ngFor let-item [ngForOf]="newPhotos">
                                    <div class="row p-0 mt-3 mx-0">
                                        <div class="col-11 px-2 py-2 d-flex align-items-center flex-wrap border rounded-lg">
                                            <div class="p-0 d-inline-block text-truncate">
                                                <a [href]="item.file_url" target="_blank" class="medium-font fw-500" style="max-width: 220px;">{{item.name}}</a>
                                            </div>
                                        </div>
                                        <div class="col-md-1 p-2 float-right d-flex align-items-center justify-content-center">
                                            <span class="material-symbols-outlined text-danger cursor-pointer" (click)="imgDeleteDone(item)">
                                                delete
                                            </span>
                                        </div>
                                    </div>
                                </ng-template>
                            </ng-container>
                        </div>
                    </div>
                </div>
            </form>
            <div class="row float-right mr-4">
                <button type="button" class="btn btn-outline-brandeis-blue float-left border-0 mb-0" (click)="builderObj.isSecColShow = false; cancelFormData()">Cancel</button>
                <button type="button" class="btn btn-brandeis-blue right-secondary-btn float-right ml-3 mb-0" [disabled]="!inspectionBuilderInfoForm.valid" (click)="saveAndContinue(inspectionBuilderChecklistObj); changed = true">Save</button>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer px-3 py-3" [ngClass]="{ 'border-0': builderObj.activeIndex == (isDraftAvailable ? 0 : -1) }">
    <div class="w-100 d-flex justify-content-between m-0">
        <div>
            <button *ngIf="builderObj.activeIndex > (isDraftAvailable ? 1 : 0) && builderObj.selectedHeading !== 'Review' && isChecklistHasSubheading" (click)="submitInspectionAssessmentReport(false, true)" type="button" class="btn modal-btn px-0 mr-2 text-primary" [disabled]="!changed">
                Save Draft
            </button>
            <button *ngIf="builderObj.activeIndex >= (isDraftAvailable ? 1 : 0) && builderObj.selectedHeading !== 'Review' && !isChecklistHasSubheading" (click)="submitInspectionAssessmentReport(false, true)" type="button" class="btn modal-btn px-0 mr-2 text-primary" [disabled]="!changed">
                Save Draft
            </button>
        </div>
        <div class="d-flex justify-content-end">
            <ng-container *ngIf="isChecklistHasSubheading">
                <button *ngIf="showCancelBtn && builderObj.activeIndex == (isDraftAvailable ? 1 : 0)" (click)="closeNewInspectionModal(confirmationMetaData.inspectionBuilderConfModalType.CLOSE)" type="button" class="btn modal-btn px-3 mr-2 text-primary">
                    Cancel 
                </button>
                <button *ngIf="!showCancelBtn && builderObj.activeIndex >= (isDraftAvailable ? 1 : 0)" (click)="goBackStep()" type="button" class="btn modal-btn px-3 mr-2 text-primary">
                    Back
                </button>
                <button *ngIf="builderObj.activeIndex == (isDraftAvailable ? 1 : 0) || builderObj.selectedHeading === 'Details' || builderObj.selectedHeading === 'Content'" [disabled]="(builderObj.selectedHeading === 'Details' && checkDetailMandatoryFields()) || (builderObj.selectedHeading === '' && !selectedDraftID && checkDetailMandatoryFields())" (click)="goNextStep()" type="button" class="btn btn-brandeis-blue modal-btn px-3">
                    Next
                </button>
                <button *ngIf="builderObj.activeIndex !== 0 && builderObj.selectedHeading !== 'Details' && builderObj.selectedHeading !== 'Content' && builderObj.activeIndex < builderObj.headings.length + (isDraftAvailable ? 3 : 2)" [disabled]="checkOptionCheckedAndValid()" (click)="goNextStep()" type="button" class="btn btn-brandeis-blue modal-btn px-3">
                    Next
                </button>
            </ng-container>
            <ng-container *ngIf="!isChecklistHasSubheading">
                <button *ngIf="showCancelBtn && builderObj.activeIndex == (isDraftAvailable ? 1 : 0)" (click)="closeNewInspectionModal(confirmationMetaData.inspectionBuilderConfModalType.CLOSE)" type="button" class="btn modal-btn px-3 mr-2 text-primary">
                    Cancel
                </button>
                <button *ngIf="!showCancelBtn && builderObj.activeIndex >= (isDraftAvailable ? 1 : 0)" (click)="goBackStep()" type="button" class="btn modal-btn px-3 mr-2 text-primary">
                    Back
                </button>
                <button *ngIf="builderObj.activeIndex == (isDraftAvailable ? 1 : 0) || builderObj.selectedHeading === 'Details'" [disabled]="(builderObj.selectedHeading === 'Details' && checkDetailMandatoryFields()) || (builderObj.selectedHeading === '' && !selectedDraftID && checkDetailMandatoryFields())" (click)="goNextStep()" type="button" class="btn btn-brandeis-blue modal-btn px-3">
                    Next
                </button>
                <button *ngIf="builderObj.activeIndex !== 0 && builderObj.selectedHeading !== 'Details' && builderObj.selectedHeading !== 'Summary' && builderObj.selectedHeading !== 'Review'" [disabled]="checkWithoutSubHeadValid()" (click)="goNextStep()" type="button" class="btn btn-brandeis-blue modal-btn px-3">
                    Next
                </button>
            </ng-container>
            <button *ngIf="(isChecklistHasSubheading && builderObj.selectedHeading === 'Summary') || (!isChecklistHasSubheading && builderObj.selectedHeading === 'Summary')" [disabled]="!this.builderObj.report_datetime" (click)="saveAndGoToPreview()" type="button" class="btn btn-brandeis-blue modal-btn px-3">
                Review
            </button>
            <button *ngIf="(isChecklistHasSubheading && builderObj.selectedHeading === 'Review') || (!isChecklistHasSubheading && builderObj.selectedHeading === 'Review')" [disabled]="!this.builderObj.currentInspectionItem.sign" (click)="submitWarningConfirmation()" type="button" class="btn btn-brandeis-blue modal-btn px-3">
                Submit
            </button>
        </div>
    </div>
</div>
<!-- End: inspection builder checklist -->

<!-- start: add other modal -->
<i-modal #inspectBuilderAddOtherModalRef [title]="'Add Other'" (onClickRightPB)="addOtherQuestion()" rightPrimaryBtnTxt="Add" [rightPrimaryBtnDisabled]="!inspectionBuilderAddOtherForm.valid" [showCancel]="true"
size="md" (onCancel)="closeModal()" [isCentered]="true">
        <form novalidate #inspectionBuilderAddOtherForm="ngForm">
            <div class="form-group" *ngIf="showAdditionalHeading">
                 <label>Title <small class="required-asterisk">*</small></label>
                    <div class="input-group">
                        <input 
                            class="form-control" 
                            name="title" 
                            placeholder="Enter Title" 
                            [(ngModel)]="builderObj.otherTitle" 
                            required
                            autocomplete="off"
                            inputDuplicateValidator
                            [compareAgainstList]="checklistHeadings"
                            #titleInput="ngModel"
                        />
                    </div>
                    <div *ngIf="titleInput.control.errors?.required && titleInput.touched" class="alert alert-danger mb-1 mt-1 py-1">
                        Title is required.
                    </div>
                    <div *ngIf="!titleInput.control.errors?.required && titleInput.control.errors?.duplicate && titleInput.touched" class="alert alert-danger mb-1 mt-1 py-1">
                        Title must be unique.
                    </div>
            </div>
        </form>
</i-modal>
<!-- end: add other modal -->
<!-- start: filter by company modal -->
<i-modal #filterByCompanyModalRef [title]="'Filter by Company'" size="md" cancelBtnText="Cancel" (onCancel)="cancelAndCloseFilterCompModal($event)" [rightPrimaryBtnTxt]="'Save'" (onClickRightPB)="saveAndCloseFilterCompModal()" [isCentered]="true">
    <form #searchCompanyForm="ngForm" novalidate>
        <company-selector-v2
            *ngIf="showCompanyFilter"
            [country_code]="projectCountryCode"
            name="tagCompany"
            placeholder="Search"
            [multiple]="true"
            (selectionChanged)="toggleSelectedCompany($event)"
            [selectId]="builderObj.tempSelectedCompanyIds"
            [projectId]="projectId"
        ></company-selector-v2>
    </form>
</i-modal>
<!-- end: filter by company modal -->
<generic-confirmation-modal #confirmationModalRef (confirmEvent)="confirmationVal($event)"></generic-confirmation-modal>

<block-loader [show]="loading" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
import { Component, OnInit, TemplateRef, ViewChild, ChangeDetectorRef, Input, Output, EventEmitter, SimpleChanges } from '@angular/core';
import { NgbModalConfig, NgbModal, NgbDateStruct, NgbTimeStruct } from '@ng-bootstrap/ng-bootstrap';
import {
    InspectionBuilder, InspectionBuilderService, ProjectService, Severity, RootCause, Employer, Heading, User, InspectionBuilderConfModalType, CancelModal,
    ToastService
} from '@app/core';
import * as dayjs from 'dayjs';
import { NgbMomentjsAdapter } from '@app/core/ngb-moment-adapter';
import { AppConstant } from '@env/environment';
import { GenericConfirmationModalComponent, IModalComponent } from '@app/shared';
import { NgForm } from '@angular/forms';
import { innDexConstant } from '@env/constants';

@Component({
    selector: 'inspection-builder',
    templateUrl: './inspection-builder.component.html',
    styleUrls: ['./inspection-builder.component.scss'],
    providers: [NgbModalConfig, NgbModal],
})
export class InspectionBuilderComponent implements OnInit {
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent; GenericConfirmationModalComponent;
    AppConstant = AppConstant;
    builderObj: any = {
        filterCompMainRef: '',
        addOtherChecklistModalRef: '',
        inspectionAssessmentMainModalRef: '',
        filteredInductedUsersEmployer: [],
        filteredInductedUsers: [],
        tempSelectedCompanyNames: [],
        tempSelectedCompanyIds: [],
        imgTobeSaved: [],
        severities: [],
        severityMandatory: false,
        rootCauseMandatory: false,
        rootCauses: [],
        otherTitle: '',
        mainCheckIndex: null,
        headingIndex: null,
        activeIndex: 0,
        selectedHeading: 'Content',
        selectedQueName: '',
        selectedQueId: '' || 0,
        companySearchText: '',
        headings: [],
        isSecColShow: false,
        isStepDropdownOpen: false,
        inspectionTime: '',
        inspectionDate: '',
        currentInspectionItem: InspectionBuilder
    };
    tempObjSubHead: any = {};
    tempCheklistWithoutSubHead: any = [];
    inductedUsersEmployer: any = [];
    inductedUsers: any = [];
    newPhotos: any = [];
    inspectionBuilderChecklistObj: any = this.resetInspectionBuilderChecklistForm();
    selectedTabId: any;
    blockLoader = false;
    isChecklistHasSubheading = false;
    hasDetailFields = false;
    hasSeverity: boolean = false;
    hasRootCause: boolean = false;
    @Input() draftData: any[] = [];
    @Input() isDraftAvailable: boolean = false;
    @Input() headerClose: boolean = false;
    selectedDraftID;
    isNewReport = false;
    savedBuilderObj: any;
    // data to be needed for confirmation
    confirmationMetaData = {
        mainInd: 0,
        headInd: 0,
        inspectionBuilderConfModalType: InspectionBuilderConfModalType,
    };
    maxDate: NgbDateStruct = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
    lastUpdatedAt: number | null = null;
    isModifiedData: boolean = false;
    closeOnSaveDraft = false
    changed = false
    fullDateTimeFormatwithoutSS = innDexConstant.fullDateTimeFormat_without_ss;
    showAdditionalHeading = false
    showCompanyFilter = false

    @Input() currentInspectionItem: InspectionBuilder;
    @Input() headings: Heading[];
    @Input() selectedHeading: string;
    @Input() inductedUsersEmployerData: Employer[];
    @Input() severityMandatory: boolean;
    @Input() rootCauseMandatory: boolean;
    @Input() severitiesData: Severity[];
    @Input() rootCausesData: RootCause[];
    @Input() selectedTabIdInput: number;
    @Input() inspectionLoader: boolean;
    @Output() isCloseEvent = new EventEmitter();
    @Output() updateModalSize = new EventEmitter();
    @Output() headerCloseChanged = new EventEmitter();
    @Output() getAllDraft = new EventEmitter();
    @Input() projectId: number = 0;
    @Input() employerId: number = 0;
    @Input() isProjectPortal: boolean = false;
    @Input() updateDraft: boolean = false;
    @Input() projectCountryCode: string = "";
    @Input() projectTimezone: string = "";
    showCancelBtn: boolean = false;
    additionalChecklistId: string = "OTH-"

    constructor(
        private projectService: ProjectService,
        private inspecBuildService: InspectionBuilderService,
        private modalService: NgbModal,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private cdRef: ChangeDetectorRef,
        private toastService: ToastService,
    ) { }

    ngOnInit(): void { }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['inspectionLoader']) {
            this.selectedDraftID = undefined;
            this.collapseModal();
        }
        if (changes['draftData'] && changes['draftData'].currentValue.length > 0 && !this.updateDraft) {
            this.isDraftAvailable = true;
        }
        if (changes['headerClose']?.currentValue == true) {
            setTimeout(() => {
                this.closeNewInspectionModal(this.confirmationMetaData.inspectionBuilderConfModalType.CLOSE);
            }, 100);
            return;
        } else if (changes['headerClose']?.currentValue == false) {
            return;
        }

        if (!this.updateDraft) {
            this.builderObj.headings = this.headings;
            this.builderObj.filteredInductedUsers = [...this.inductedUsers];
            this.builderObj.filteredInductedUsersEmployer = [...this.inductedUsersEmployerData,];
            this.builderObj.severities = this.severitiesData;
            this.builderObj.rootCauses = this.rootCausesData;
            this.selectedTabId = this.selectedTabIdInput;

            this.builderObj.activeIndex = 0;
            this.builderObj.tempSelectedCompanyNames = [];
            this.builderObj.tempSelectedCompanyIds = [];
            this.resetInspectionBuilderChecklistForm();
            this.builderObj.currentInspectionItem = JSON.parse(JSON.stringify(this.currentInspectionItem));
            this.isChecklistHasSubheading = this.builderObj.currentInspectionItem.has_subheadings;
            this.hasDetailFields = this.builderObj.currentInspectionItem.detail_fields?.length ? true : false;
            this.builderObj.selectedHeading = this.draftData && this.isDraftAvailable ? '' : this.isChecklistHasSubheading ? 'Content' : this.hasDetailFields ? 'Details' : 'Checklist';
            if (!this.isChecklistHasSubheading && !this.hasDetailFields) {
                this.builderObj.activeIndex = 1;
            }
            this.hasSeverity = this.builderObj.currentInspectionItem?.severity?.has_severity;
            this.hasRootCause = this.builderObj.currentInspectionItem?.root_cause?.has_root_cause;
            this.getInductedUser();
            this.showCancelBtn = !this.isDraftAvailable
        }
    }

    get loading(): boolean {
        return this.blockLoader;
    }
  
    remainingDays(date) {
        const count = 30 - dayjs().diff(date, 'day') 
        return count > 1 ? `${count} days` : `${count} day`
    }

    dayjs(n: number) {
        let tz = this.projectTimezone;
        return dayjs(n).tz(tz);
    }

    private getInductedUser() {
        //List of users to tag in close call
        if (this.isProjectPortal) {
            this.projectService.getProjectInductedUsersNames(this.projectId, {extra: `employment`}).subscribe((data: any) => {
                if(data.success && data.records) {
                    this.inductedUsers = (data.records || []).sort((a, b) => a.name.localeCompare(b.name));;
                    this.builderObj.filteredInductedUsers = [ ...this.inductedUsers ];
                } else {
                    const message = data.message || 'Failed to get list of inducted users.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                }
            });
        } else {
            this.projectService.getProjectInductedUsersNamesCA(this.employerId, this.projectId, {extra: `employment`}).subscribe((data: any) => {
                if(data.success && data.records) {
                    this.inductedUsers = (data.records || []).sort((a, b) => a.name.localeCompare(b.name));;
                    this.builderObj.filteredInductedUsers = [ ...this.inductedUsers ];
                } else {
                    const message = data.message || 'Failed to get list of inducted users.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                }
            });
        }

    }

    private getInspectionEpoch() {
        let date = this.builderObj.inspectionDate;
        let time = this.builderObj.inspectionTime;
        if (date) {
            this.builderObj['report_datetime'] = this.getTimeEventOf(date, time);
        } else {
            const message = 'Please select inspection date.';
            this.toastService.show(this.toastService.types.INFO, message);
        }
    }

    /**
     *
     * @param targetDate date object
     * @param timePickerSelection time object
     * @returns dayJs date
     */
    private getTimeEventOf(targetDate: NgbDateStruct, timePickerSelection: NgbTimeStruct): dayjs.Dayjs {
        return this.ngbMomentjsAdapter
            .ngbDateToDayJs(targetDate)
            .set('hour', timePickerSelection?.hour ?? dayjs().hour())
            .set('minute', timePickerSelection?.minute ?? dayjs().minute())
            .set('second', timePickerSelection?.second ?? dayjs().second());
    }

    changeTime() {
        this.getInspectionEpoch();
    }

    public resetInspectionBuilderChecklistForm(): void {
        this.inspectionBuilderChecklistObj = {
            item_id: null,
            question: '',
            answer: '',
            images: [],
            summary: '',
            corrective_action_required: '',
            close_out: {},
            closeout_due_on: null,
            tagged_user_ref: null,
            category: '',
            tagged_company_ref: [],
            location_tag: null,
            no_applicable: false,
            heading: '',
            severity: null,
            root_cause: null,
        };
        this.newPhotos = [];
        this.builderObj.imgTobeSaved = [];
        return this.inspectionBuilderChecklistObj;
    }

    private resetHeadAndChecklistIndex(): void {
        this.builderObj.mainCheckIndex = null;
        this.builderObj.headingIndex = null;
    }

    /**
     * To toggle content to N/A respectively
     * @param heading heading object
     */
    public changeHeadingToggle(heading) {
        heading.isChecked = !heading.isChecked;
        let headIndex = this.builderObj.currentInspectionItem.checklist.findIndex((item) => item.id === heading.id);
        this.builderObj.currentInspectionItem.checklist[headIndex].subheadings =
        this.builderObj.currentInspectionItem.checklist[headIndex].subheadings.map((element) => {
            if (heading.isChecked) {
                return { item_que: element.item_que, item_id: element.item_id };
            } else {
                return {
                    ...element,
                    answer: 'n/a',
                    images: [],
                    question: element.item_que,
                    summary: '',
                    corrective_action_required: '',
                    close_out: {},
                    closeout_due_on: null,
                    tagged_user_ref: null,
                    category: this.builderObj.currentInspectionItem.checklist[headIndex].heading,
                    tagged_company_ref: [],
                    location_tag: null,
                    heading: this.builderObj.currentInspectionItem.checklist[headIndex].heading,
                    severity: null,
                };
            }
        });
    }

    /**
     * To bind Checklist Form Value
     * @param questionObj checklist object
     * @param mainCheckIndex checklist index
     * @param headingIndex subheading index
     */
    public editChecklist(questionObj, mainCheckIndex, headingIndex) {
        this.builderObj.mainCheckIndex = mainCheckIndex;
        this.setPrevActiveChecklistData();
        this.builderObj.headingIndex = headingIndex;
        this.builderObj.selectedQueName = questionObj.item_que;
        this.builderObj.selectedQueId = questionObj.item_id;
        this.newPhotos = questionObj.images.length ? [...questionObj.images] : [];
        this.inspectionBuilderChecklistObj = { ...this.inspectionBuilderChecklistObj, ...questionObj };
        this.inspectionBuilderChecklistObj.tagged_company_info = this.inspectionBuilderChecklistObj.tagged_company_ref
        this.inspectionBuilderChecklistObj.tagged_company_ref = this.inspectionBuilderChecklistObj.tagged_company_ref.map(({id}) => id)
        this.inspectionBuilderChecklistObj.severity = this.inspectionBuilderChecklistObj?.severity
        this.expandModal();
        this.addImageBlock();
    }

    compareSeverity(severity1: { rating: string; days: string }, severity2: { rating: string; days: string }): boolean {
        return (severity1 && severity2) ? ((severity1.rating === severity2.rating) && (severity1.days === severity2.days)) : (severity1 === severity2);
    }

    /**
     * to set temp data for prev active checklist
     */
    private setPrevActiveChecklistData(): void {
        const mainCheckIndex = this.builderObj.mainCheckIndex;
        if (!this.builderObj.isSecColShow) return;
        const checklistData = this.builderObj.currentInspectionItem.checklist;
        if (this.isChecklistHasSubheading) {
            const queIndex = checklistData[mainCheckIndex].subheadings.findIndex((obj) => +obj.item_id === +this.builderObj.selectedQueId);
            if (queIndex !== -1) {
                checklistData[mainCheckIndex].subheadings[queIndex] = { ...this.tempObjSubHead.subheadings[queIndex], };
            }
        } else {
            const queIndex = checklistData.findIndex((obj) => +obj.item_id === +this.builderObj.selectedQueId);
            if (queIndex !== -1) {
                this.builderObj.currentInspectionItem.checklist = JSON.parse(JSON.stringify(this.tempCheklistWithoutSubHead));
                this.cdRef.detectChanges();
            }
        }
    }

    /**
     * To show correction_field, due_on_field and summary_field_vallidation
     * @returns boolean true/false
     */
    public showInputRequiredAndValidationIndicator(): boolean {
        return (((this.builderObj.currentInspectionItem?.scoring_system?.type === 1 && this.inspectionBuilderChecklistObj?.answer?.toLowerCase() != this.builderObj.currentInspectionItem?.scoring_system?.values[0].toLowerCase()) ||
                (this.builderObj.currentInspectionItem?.scoring_system?.type !== 1 && this.inspectionBuilderChecklistObj?.answer?.toLowerCase() != '3' && this.inspectionBuilderChecklistObj?.answer?.toLowerCase() != 'yes')));
    }

    public expandModal(): void {
        document.querySelectorAll('[role="document"]:first-of-type')[0]?.classList.remove("modal-lg");
        document.querySelectorAll('[role="document"]:first-of-type')[0]?.classList.add("modal-xl");
        this.builderObj.isSecColShow = true;
    }

    public collapseModal(): void {
        document.querySelectorAll('[role="document"]:first-of-type')[0]?.classList.remove("modal-xl");
        document.querySelectorAll('[role="document"]:first-of-type')[0]?.classList.add("modal-lg");
        this.builderObj['isSecColShow'] = false;
    }

    /**
     * For answer change confirmation to prevent vanish filled form data
     * @param $event event
     * @param question checklist object
     * @param mainCheckIndex checklist index
     * @param headingIndex checklist question index
     * @param value checklist question value/answer
     */
    public confirmAnsChangReq(event, question, mainCheckIndex, headingIndex: number, value: string): void {
        const builderObj = this.builderObj;
        const answer = value;
        const selectedQueName = builderObj.selectedQueName;
        const isSecColShow = builderObj.isSecColShow;
        if (isSecColShow && selectedQueName !== question.item_que) {
            if (confirm('Warning, Any unsaved changes may be lost. Do you wish to continue?')) {
                const checklist = this.builderObj.currentInspectionItem.checklist;
                if (this.isChecklistHasSubheading) {
                    const subheadings = checklist[this.builderObj.mainCheckIndex].subheadings;
                    if (answer && (answer === this.tempObjSubHead.subheadings[headingIndex].answer)) {
                        this.editChecklist(this.tempObjSubHead.subheadings[headingIndex], mainCheckIndex, headingIndex);
                    } else {
                        this.setTempDataForChecklistWithSubHead(subheadings);
                    }
                } else {
                    if (answer && (answer === this.tempCheklistWithoutSubHead[mainCheckIndex].answer)) {
                        this.editChecklist(this.tempCheklistWithoutSubHead[mainCheckIndex], mainCheckIndex, headingIndex);
                    } else {
                        this.setTempDataForChecklistWithoutSubHead(checklist[mainCheckIndex]);
                    }
                }
            } else {
                event.preventDefault();
            }
        }
    }

    /**
     * to set inital values of checklist
     * @param questionObj question object
     * @param mainCheckIndex main checklist index
     * @param headingIndex subheadin index
     * @param value value/answer
     */
    public changeAnswer(queChecklistObj: any, mainCheckIndex: number, headingIndex: number, value: string): void {
        const questionObj = JSON.parse(JSON.stringify(queChecklistObj || {}));
        this.builderObj.mainCheckIndex = mainCheckIndex;
        this.builderObj.headingIndex = headingIndex;
        this.builderObj.selectedQueName = questionObj.item_que;
        this.builderObj.selectedQueId = questionObj.item_id;
        this.inspectionBuilderChecklistObj.category = questionObj.item_que;
        this.inspectionBuilderChecklistObj.item_id = questionObj.item_id;
        this.inspectionBuilderChecklistObj.question = questionObj.item_que;
        this.inspectionBuilderChecklistObj.answer = value;

        if (value !== 'n/a') {
            this.expandModal();
            this.addImageBlock();
        } else {
            this.collapseModal();
        }
        if (this.isChecklistHasSubheading) {
            this.handleSubheadingCase(questionObj, value);
            this.setChecklistValWithSubheading(this.builderObj.mainCheckIndex, this.builderObj.headingIndex, value !== 'n/a' ? false : true);
        } else {
            this.handleNoSubheadingCase(questionObj, value);
            this.setChecklistValWithoutSubheading(mainCheckIndex, value !== 'n/a' ? false : true);
        }
    }

    /**
     * to set new or existing chaecklist in form when answer changes [for with subheading]
     * @param questionObj checklist object
     * @param value answer
     */
    private handleSubheadingCase(questionObj: any, value: string): void {
        const subheading = this.tempObjSubHead.subheadings[this.builderObj.headingIndex];
        if (subheading && !subheading.answer) {
            this.resetInspectionBuilderChecklistForm();
            this.setPrimaryValForForm(questionObj, value);
        } else {
            this.newPhotos = questionObj.images && questionObj.images.length ? [...questionObj.images] : []; this.builderObj.imgTobeSaved = [...this.newPhotos];
            this.inspectionBuilderChecklistObj = { ...this.inspectionBuilderChecklistObj, ...questionObj, tagged_company_ref: (questionObj?.tagged_company_ref && questionObj?.tagged_company_ref.length) ?  questionObj?.tagged_company_ref.map(a => typeof a === 'number' ? a : a.id): [] };
        }
        this.addImageBlock();
    }

    /**
     * to set new or existing chaecklist in form when answer changes [for without subheading]
     * @param questionObj checklist object
     * @param value answer
     */
    private handleNoSubheadingCase(questionObj: any, value: string): void {
        const checklistWithoutSubHead = this.tempCheklistWithoutSubHead[this.builderObj.mainCheckIndex];

        if (checklistWithoutSubHead && !checklistWithoutSubHead.answer) {
            this.resetInspectionBuilderChecklistForm();
            this.setPrimaryValForForm(questionObj, value);
        } else {
            this.newPhotos = questionObj.images && questionObj.images.length ? [...questionObj.images] : [];
            this.builderObj.imgTobeSaved = [...this.newPhotos];
            this.inspectionBuilderChecklistObj = { ...this.inspectionBuilderChecklistObj, ...questionObj, tagged_company_ref: (questionObj?.tagged_company_ref && questionObj?.tagged_company_ref.length) ? questionObj?.tagged_company_ref.map(a => typeof a === 'number'? a : a.id) : [] };
        }
        this.addImageBlock();
    }

    private setPrimaryValForForm(questionObj, value): void {
        this.inspectionBuilderChecklistObj.answer = value;
        this.inspectionBuilderChecklistObj.question = questionObj.item_que;
    }

    private setChecklistValWithSubheading(mainCheckIndex, headingIndex, isNotApplicable): void {
        const checklistWithSubHead = this.builderObj.currentInspectionItem.checklist[mainCheckIndex].subheadings[headingIndex];
        checklistWithSubHead.images = isNotApplicable ? [] : this.builderObj.imgTobeSaved;
        checklistWithSubHead.question = this.inspectionBuilderChecklistObj.question || checklistWithSubHead.item_que;
        checklistWithSubHead.answer = this.inspectionBuilderChecklistObj.answer;
        checklistWithSubHead.summary = this.inspectionBuilderChecklistObj.summary;
        checklistWithSubHead.corrective_action_required = isNotApplicable ? '' : this.inspectionBuilderChecklistObj.corrective_action_required;
        checklistWithSubHead.close_out = isNotApplicable ? {} : this.inspectionBuilderChecklistObj.close_out;
        checklistWithSubHead.closeout_due_on = isNotApplicable ? null : this.inspectionBuilderChecklistObj.closeout_due_on;
        checklistWithSubHead.tagged_user_ref = isNotApplicable ? null : this.inspectionBuilderChecklistObj.tagged_user_ref;
        checklistWithSubHead.category = this.builderObj.currentInspectionItem.checklist[mainCheckIndex].heading ? this.builderObj.currentInspectionItem.checklist[mainCheckIndex].heading : '';
        checklistWithSubHead.tagged_company_ref = isNotApplicable ? [] : this.inspectionBuilderChecklistObj.tagged_company_ref;
        checklistWithSubHead.location_tag = isNotApplicable ? null : this.inspectionBuilderChecklistObj.location_tag;
        checklistWithSubHead.heading = this.builderObj.currentInspectionItem.checklist[mainCheckIndex].heading ? this.builderObj.currentInspectionItem.checklist[mainCheckIndex].heading : '';
        checklistWithSubHead.severity = isNotApplicable ? null : this.inspectionBuilderChecklistObj.severity;
        checklistWithSubHead.root_cause = isNotApplicable ? null : this.inspectionBuilderChecklistObj.root_cause;
    }

    private setChecklistValWithoutSubheading(mainCheckIndex, isNotApplicable): void {
        const checklistWithoutSubHead = this.builderObj.currentInspectionItem.checklist[mainCheckIndex];
        checklistWithoutSubHead.images = isNotApplicable ? [] : this.builderObj.imgTobeSaved;
        checklistWithoutSubHead.question = this.inspectionBuilderChecklistObj.question || checklistWithoutSubHead.item_que;
        checklistWithoutSubHead.answer = this.inspectionBuilderChecklistObj.answer;
        checklistWithoutSubHead.summary = this.inspectionBuilderChecklistObj.summary;
        checklistWithoutSubHead.corrective_action_required = isNotApplicable ? '' : this.inspectionBuilderChecklistObj.corrective_action_required;
        checklistWithoutSubHead.close_out = isNotApplicable ? {} : this.inspectionBuilderChecklistObj.close_out;
        checklistWithoutSubHead.closeout_due_on = isNotApplicable ? null : this.inspectionBuilderChecklistObj.closeout_due_on;
        checklistWithoutSubHead.tagged_user_ref = isNotApplicable ? null : this.inspectionBuilderChecklistObj.tagged_user_ref;
        checklistWithoutSubHead.category = this.inspectionBuilderChecklistObj.heading ? this.inspectionBuilderChecklistObj.heading : '';
        checklistWithoutSubHead.tagged_company_ref = isNotApplicable ? [] : this.inspectionBuilderChecklistObj.tagged_company_ref;
        checklistWithoutSubHead.location_tag = isNotApplicable ? null : this.inspectionBuilderChecklistObj.location_tag;
        checklistWithoutSubHead.heading = this.inspectionBuilderChecklistObj.heading ? this.inspectionBuilderChecklistObj.heading : '';
        checklistWithoutSubHead.severity = isNotApplicable ? null : (this.inspectionBuilderChecklistObj.severity) ? this.inspectionBuilderChecklistObj.severity : null;
        checklistWithoutSubHead.root_cause = isNotApplicable ? null : this.inspectionBuilderChecklistObj.root_cause;
    }

    /**
     * to save perticular checklist answer and other props
     */
    public saveAndContinue(): void {
        this.builderObj.imgTobeSaved = [...this.newPhotos];
        const optionsToCheck = [
            this.builderObj.currentInspectionItem.scoring_system.values[0],
            'Yes',
            '3',
            'n/a'
        ];
        if (this.isChecklistHasSubheading) {
            const checklistHasSubhead = this.builderObj.currentInspectionItem.checklist[this.builderObj.mainCheckIndex].subheadings[this.builderObj.headingIndex];
            checklistHasSubhead.question = this.inspectionBuilderChecklistObj.item_que;
            checklistHasSubhead.answer = this.inspectionBuilderChecklistObj.answer;
            checklistHasSubhead.images = this.builderObj.imgTobeSaved;
            checklistHasSubhead["tagged_company_info"] = this.inspectionBuilderChecklistObj.tagged_company_ref?.some((ref) => typeof ref !== "number") ? this.inspectionBuilderChecklistObj.tagged_company_ref: this.inspectionBuilderChecklistObj.tagged_company_info;
            this.setChecklistValWithSubheading(this.builderObj.mainCheckIndex, this.builderObj.headingIndex, false);
            if (this.hasSeverity && this.checkSeverity(checklistHasSubhead.severity) && checklistHasSubhead.answer !== 'n/a') {
                checklistHasSubhead.closeout_due_on = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs().add(dayjs.duration(checklistHasSubhead.severity.days).asDays(), 'day'));
            }
            if (optionsToCheck.includes(checklistHasSubhead.answer)) {
                checklistHasSubhead.close_out = {};
                checklistHasSubhead.closeout_due_on = null;
                checklistHasSubhead.corrective_action_required = '';
                checklistHasSubhead.severity = null;
                checklistHasSubhead.root_cause = null;
            }
            let updatedChecklistbuilder = this.builderObj.currentInspectionItem.checklist[this.builderObj.activeIndex - (this.isDraftAvailable ? 3 : 2)]
            this.tempObjSubHead = JSON.parse(JSON.stringify(updatedChecklistbuilder));
            this.tempObjSubHead.subheadings = this.tempObjSubHead.subheadings.map((subHead) => {
                if(checklistHasSubhead.item_id == subHead.item_id && subHead.tagged_company_ref?.some((ref) => typeof ref == "number")){
                    subHead.tagged_company_ref = checklistHasSubhead.tagged_company_info
                }
                return subHead
            })
            updatedChecklistbuilder.subheadings = updatedChecklistbuilder.subheadings.map((subHead) => {
                if(checklistHasSubhead.item_id == subHead.item_id && subHead.tagged_company_ref?.some((ref) => typeof ref == "number")){
                    subHead.tagged_company_ref = checklistHasSubhead.tagged_company_info
                }
                return subHead
            })

            this.resetHeadBuilderFormAndCollaps();
        } else {
            const checklistNotHasSubhead = this.builderObj.currentInspectionItem.checklist[this.builderObj.mainCheckIndex];
            checklistNotHasSubhead.question = this.inspectionBuilderChecklistObj.item_que;
            checklistNotHasSubhead.answer = this.inspectionBuilderChecklistObj.answer;
            checklistNotHasSubhead.summary = this.inspectionBuilderChecklistObj.summary;
            checklistNotHasSubhead.images = this.builderObj.imgTobeSaved;
            checklistNotHasSubhead.tagged_company_info = this.inspectionBuilderChecklistObj.tagged_company_ref?.some((ref) => typeof ref !== "number") ? this.inspectionBuilderChecklistObj.tagged_company_ref: this.inspectionBuilderChecklistObj.tagged_company_info;
            this.setChecklistValWithoutSubheading(this.builderObj.mainCheckIndex, false);
            if (this.hasSeverity && this.checkSeverity(checklistNotHasSubhead.severity) && checklistNotHasSubhead.answer !== 'n/a') {
                checklistNotHasSubhead.closeout_due_on = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs().add(dayjs.duration(checklistNotHasSubhead.severity.days).asDays(), 'day'));
            }
            if (optionsToCheck.includes(checklistNotHasSubhead.answer)) {
                checklistNotHasSubhead.close_out = {};
                checklistNotHasSubhead.closeout_due_on = null;
                checklistNotHasSubhead.corrective_action_required = '';
                checklistNotHasSubhead.severity = null;
                checklistNotHasSubhead.root_cause = null;
            }
            this.tempCheklistWithoutSubHead = JSON.parse(JSON.stringify(this.builderObj.currentInspectionItem.checklist));
            this.tempCheklistWithoutSubHead = this.tempCheklistWithoutSubHead.map((subHead) => {
                if(checklistNotHasSubhead.item_id == subHead.item_id && subHead.tagged_company_ref?.some((ref) => typeof ref == "number")){
                    subHead.tagged_company_ref = checklistNotHasSubhead.tagged_company_info
                }
                return subHead
            })
            this.builderObj.currentInspectionItem.checklist = this.builderObj.currentInspectionItem.checklist.map((subHead) => {
                if(checklistNotHasSubhead.item_id == subHead.item_id && subHead.tagged_company_ref?.some((ref) => typeof ref == "number")){
                    subHead.tagged_company_ref = checklistNotHasSubhead.tagged_company_info
                }
                return subHead
            })
            this.resetHeadBuilderFormAndCollaps();
        }
    }

    private resetHeadBuilderFormAndCollaps(): void {
        this.resetHeadAndChecklistIndex();
        this.resetInspectionBuilderChecklistForm();
        this.collapseModal();
    }

    /**
     * clear and init the checklist form data
     */
    public cancelFormData(): void {
        const currentChecklistItem = this.builderObj.currentInspectionItem.checklist[this.builderObj.mainCheckIndex];
        if (this.isChecklistHasSubheading) {
            const subheadings = currentChecklistItem.subheadings;
            if (subheadings[this.builderObj.headingIndex].answer) {
                this.setTempDataForChecklistWithSubHead(subheadings);
            }
        } else {
            if (currentChecklistItem.answer) {
                this.setTempDataForChecklistWithoutSubHead(currentChecklistItem);
            }
        }
        this.collapseModal();
    }

    /**
     * to set checklist temp data [for checklist with subheadings]
     * @param subheadings checklists subheadings data
     */
    private setTempDataForChecklistWithSubHead(subheadings): void {
        const queIndex = subheadings.findIndex((obj) => +obj.item_id === +this.builderObj.selectedQueId);
        if (queIndex !== -1 ) {
            if(this.tempObjSubHead.subheadings){
                subheadings[queIndex] = { ...this.tempObjSubHead.subheadings[queIndex], };
            }
        } else {
            subheadings[this.builderObj.headingIndex] = { item_id: subheadings[this.builderObj.headingIndex].item_id, item_que: subheadings[this.builderObj.headingIndex].item_que, answer: '', };
            if (typeof subheadings[this.builderObj.headingIndex].item_id == 'string' && subheadings[this.builderObj.headingIndex].item_id.includes(this.additionalChecklistId)) {
                subheadings[this.builderObj.headingIndex] = {
                    ...subheadings[this.builderObj.headingIndex],
                    isOther: true,
                };
            }
        }
    }

    /**
     * to set checklist temp data [for checklist without subheadings]
     * @param currentChecklistItem checklist object
     */
    private setTempDataForChecklistWithoutSubHead(currentChecklistItem): void {
        const checklist = this.builderObj.currentInspectionItem.checklist;
        const queIndex = checklist.findIndex((obj) => +obj.item_id === +this.builderObj.selectedQueId);
        if (queIndex !== -1 && checklist[queIndex].summary !== "") {
            checklist[queIndex] = { ...this.tempCheklistWithoutSubHead[queIndex] };
        } else {
            currentChecklistItem.item_id = currentChecklistItem.item_id;
            currentChecklistItem.item_que = currentChecklistItem.item_que;
            currentChecklistItem.answer = '';
        }
    }

    public getAssigneeName(userRef) {
        let assigneeObj = this.inductedUsers.find(({user_ref}) => user_ref === userRef);
        if(assigneeObj){
            return assigneeObj.name;
        } else {
            return null;
        }
    }

    public getCompanyName(companyId) {
        let companyObj = this.inductedUsersEmployer.find((data) => data.id === companyId || data.id === companyId.id);
        if (companyObj) {
            return companyObj.name;
        } else {
            return null;
        }
    }

    public onChangeDateTime(event): void {
        this.builderObj['report_datetime'] = event;
        this.cdRef.detectChanges();
        this.getInspectionEpoch();
    }

    private currentDayJsDateToNgbDate() {
        return this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
    }

    public showDate(dateObj): any {
        return this.ngbMomentjsAdapter.ngbDateToDayJs(dateObj) ? this.ngbMomentjsAdapter.ngbDateToDayJs(dateObj).format('DD/MM/YYYY') : '';
    }

    public moveStep(moveToStep: number, stepTitle: string): void {
        this.scrollTopHeadContent();
        this.collapseModal();
        const previousStep = this.builderObj.activeIndex
        this.builderObj.activeIndex = moveToStep;
        this.builderObj.selectedHeading = stepTitle;
        this.builderObj.isStepDropdownOpen = false;
        this.cloneActiveChecklist(this.builderObj.activeIndex);
        if (
            this.builderObj.selectedHeading !== undefined &&
            (previousStep > moveToStep || this.builderObj.selectedHeading === 'Content') &&
            this.builderObj.selectedHeading !== '' &&
            this.checkIsContentEnable(moveToStep, stepTitle)
        )
            this.submitInspectionAssessmentReport();
            this.showCancelBtn = this.builderObj.activeIndex == 0 && !this.isDraftAvailable
        this.changed = false
    }

    public saveAndGoToPreview(): void {
        this.builderObj.activeIndex += 1;
        this.setHeadingTitle();
        if (
            this.builderObj.selectedHeading !== undefined &&
            this.builderObj.selectedHeading !== 'Content' &&
            this.builderObj.selectedHeading !== ''
        )
            this.submitInspectionAssessmentReport();
    }

    private scrollTopHeadContent(): void {
        let elem: HTMLElement = document.getElementById('content-head');
        elem?.scrollIntoView();
    }

    public isCloseActive(): boolean {
        const val = this.hasDetailFields ? 0 : 1;
        return this.builderObj.activeIndex === val;
    }

    public isBackActive(): boolean {
        const val = this.hasDetailFields ? 0 : 1;
        return this.builderObj.activeIndex > val;
    }

    @ViewChild('detailFieldForm') closeOutForm;
    checkingValidation() { }
    /**
     * forwrd stepper functioning
     */
    public async goNextStep(isNewReport: boolean = false): Promise<void> {
        this.scrollTopHeadContent();
        this.collapseModal();
        if(isNewReport){
            this.resetForms();
            this.showCancelBtn = !this.isDraftAvailable
        } else {
            this.showCancelBtn = false
        }
        if (isNewReport || (!this.selectedDraftID && this.builderObj.activeIndex == 0 && this.isChecklistHasSubheading)
        ) {
            this.selectedDraftID = undefined;
        }

        if ((this.builderObj.selectedHeading !== undefined && this.builderObj.selectedHeading !== 'Content' && this.builderObj.selectedHeading !== '' && this.isChecklistHasSubheading)
            || (this.builderObj.selectedHeading !== undefined && this.builderObj.selectedHeading !== '' && !this.isChecklistHasSubheading)) {
            this.setHeadingTitle();
            this.submitInspectionAssessmentReport();
            if (this.isModifiedData) return;
        }
        if (this.builderObj.activeIndex < this.builderObj.headings.length + (this.isDraftAvailable ? 3 : 2)) {
            const stepIncrement = this.hasDetailFields ? 1 : 2;
            if (this.builderObj.activeIndex == 0 && !this.hasDetailFields) {
                this.builderObj.activeIndex += stepIncrement;
            } else {
                this.builderObj.activeIndex += 1;
            }
            this.setHeadingTitle();
            this.cloneActiveChecklist(this.builderObj.activeIndex);
        }
        this.changed = false
    }

    /**
     * clone currentlty active checklist
     * @param activeIndex active index
     */
    private cloneActiveChecklist(activeIndex): void {
        if (this.isChecklistHasSubheading) {
            if (activeIndex > (this.isDraftAvailable ? 2 : 1) && activeIndex < this.builderObj.headings.length + (this.isDraftAvailable ? 3 : 2)) {
                this.tempObjSubHead = JSON.parse(JSON.stringify(this.builderObj.currentInspectionItem.checklist[activeIndex - (this.isDraftAvailable ? 3 : 2)]));
            }
        } else {
            if (activeIndex > (this.isDraftAvailable ? 1 : 0) && activeIndex < this.builderObj.headings.length + (this.isDraftAvailable ? 2 : 1)) {
                this.tempCheklistWithoutSubHead = JSON.parse(JSON.stringify(this.builderObj.currentInspectionItem.checklist));
            }
        }
    }

    /**
     * back stepper functioning
     */
    public goBackStep(): void {
        this.scrollTopHeadContent();
        this.collapseModal();
        if (this.builderObj.activeIndex > 0) {
            if (this.isChecklistHasSubheading) {
                this.builderObj.activeIndex -= this.builderObj.activeIndex === 2 && !this.hasDetailFields ? 2 : 1;
            } else {
                this.builderObj.activeIndex -= 1;
            }
            this.setHeadingTitle();
            this.cloneActiveChecklist(this.builderObj.activeIndex);
        }

        if(this.builderObj.activeIndex == 0 && this.isDraftAvailable) this.getAllDraft.emit()
        this.showCancelBtn = this.builderObj.activeIndex == 0 && !this.isDraftAvailable
    }

    /**
     * for navigate to perticular checklist
     * @param mainCheckIndexPrev main checklist index
     */
    public goToPerticularChecklist(mainCheckIndexPrev: number): void {
        this.builderObj.activeIndex = mainCheckIndexPrev;
        this.setHeadingTitle();
        this.cloneActiveChecklist(this.builderObj.activeIndex);
    }

    /**
     * to set headings based on the active index
     */
    private setHeadingTitle(): void {
        if (this.builderObj.activeIndex === (this.isDraftAvailable ? 1 : 0)) {
            this.builderObj.selectedHeading = this.isChecklistHasSubheading ? 'Content' : 'Details';
        } else if (this.builderObj.activeIndex === (this.isDraftAvailable ? 2 : 1)) {
            this.builderObj.selectedHeading = this.isChecklistHasSubheading ? 'Details' : 'Checklist';
        } else if (
            this.builderObj.activeIndex === (this.isChecklistHasSubheading ? this.builderObj.headings.length + (this.isDraftAvailable ? 3 : 2) : this.builderObj.headings.length + (this.isDraftAvailable ? 2 : 1))) {
            this.builderObj.selectedHeading = 'Summary';
        } else if (
            this.builderObj.activeIndex === (this.isChecklistHasSubheading ? this.builderObj.headings.length + (this.isDraftAvailable ? 4 : 3) : this.builderObj.headings.length + (this.isDraftAvailable ? 3 : 2))) {
            this.builderObj.selectedHeading = 'Review';
        } else {
            this.builderObj.selectedHeading = this.builderObj.headings[this.builderObj.activeIndex - (this.isDraftAvailable ? 3 : 2)]?.heading;
        }
    }

    /**
     * to set signature pad sign
     * @param signBase64 bas64 string
     */
    public saveSignImage(signBase64: string): void {
        if (signBase64) {
            this.builderObj.currentInspectionItem['sign'] = signBase64;
            this.cdRef.detectChanges();
        }
    }

    /**
     * to clear signature pad sign
     */
    public clearSignImage(): void {
        this.builderObj.currentInspectionItem['sign'] = '';
        this.cdRef.detectChanges();
    }

    /**
     * to delete addtional checklist
     * @param mainCheckIndex main checklist index
     * @param headingIndex subheading index
     */
    public deleteOther(event, type: number, mainCheckIndex: number, headingIndex?: number): void {
        event.stopPropagation();
        this.confirmationMetaData.mainInd = mainCheckIndex;
        this.confirmationMetaData.headInd = headingIndex;
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: 'Are you sure you wish to delete this checklist item?',
            type: type,
        });
    }

    deleteOtherConfirm() {
        const checklist = this.builderObj.currentInspectionItem.checklist;
        if (this.isChecklistHasSubheading) {
            checklist[this.confirmationMetaData.mainInd].subheadings.splice(this.confirmationMetaData.headInd, 1
            );
            const selectedChecklist = checklist[this.builderObj.activeIndex - 2] || {}
            this.tempObjSubHead = JSON.parse(JSON.stringify(selectedChecklist));
        } else {
            checklist.splice(this.confirmationMetaData.mainInd, 1);
            this.tempCheklistWithoutSubHead = JSON.parse(JSON.stringify(checklist));
        }
        this.collapseModal();
    }

    /**
     * to add additional checklist
     */
    public addOtherQuestion(): void {
        const id: number = (new Date()).getTime();
        let otherChecklistObj = {
            item_id: this.additionalChecklistId + id,
            item_que: this.builderObj.otherTitle,
            question: this.builderObj.otherTitle,
            images: [],
            summary: '',
            corrective_action_required: '',
            close_out: {},
            closeout_due_on: null,
            tagged_user_ref: null,
            category: '',
            tagged_company_ref: [],
            location_tag: null,
            heading: '',
            severity: null,
            isOther: true,
        };
        if (this.isChecklistHasSubheading) {
            this.builderObj.currentInspectionItem?.checklist[this.builderObj.activeIndex - (this.isDraftAvailable ? 3 : 2)]?.subheadings.push(otherChecklistObj);
            this.tempObjSubHead = JSON.parse(JSON.stringify(this.builderObj.currentInspectionItem.checklist[this.builderObj.activeIndex - (this.isDraftAvailable ? 3 : 2)]));
        } else {
            this.builderObj.currentInspectionItem?.checklist.push(otherChecklistObj);
            this.tempCheklistWithoutSubHead = JSON.parse(JSON.stringify(this.builderObj.currentInspectionItem.checklist));
        }
        this.showAdditionalHeading = false
        this.builderObj.addOtherChecklistModalRef.close();
    }

    /**
     * Closes add new inspection modal and reset properities
     */
    public closeNewInspectionModal(type, isFromSuccess?: string): void {
        if ((!this.isNewReport && !this.changed) || this.builderObj.activeIndex == (this.isDraftAvailable? 0 : -1) || (!this.selectedDraftID && this.checkDetailFields()) ) {
            if(this.isNewReport && this.selectedDraftID) {
                this.removeDraft(this.selectedDraftID)
            }
            this.headerCloseChanged.emit(false);
            this.isCloseEvent.emit(true);
            return;
        }

        if (!isFromSuccess) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Warning',
                title: 'You are about to exit. Do you wish to save your progress and resume this report later?',
                type: type,
                leadingBtnLabel: CancelModal.CANCEL,
                confirmLabel: CancelModal.SAVE,
                cancelLabel: CancelModal.NO,
                borderCancelButton: true
            });
        } else {
            this.isCloseEvent.emit('isFromSuccess');
        }
    }

    public submitWarningConfirmation(): void {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Warning!',
            title: "Are you sure you wish to submit your inspection? Once submitted, you won't be able to make any further changes",
            confirmLabel: CancelModal.SUBMIT,
            cancelLabel: CancelModal.CANCEL,
        });
    }

    @ViewChild('inspectBuilderAddOtherModalRef')
    private inspectBuilderAddOtherModalRef: IModalComponent;
    addOtherChecklist() {
        this.showAdditionalHeading = true
        this.collapseModal();
        this.builderObj.otherTitle = '';
        this.builderObj.addOtherChecklistModalRef = this.inspectBuilderAddOtherModalRef.open();
    }

    get checklistHeadings(): string[] {
        const currentValue = this.builderObj.otherTitle?.trim().toLowerCase();
        
        if (this.isChecklistHasSubheading) {
            const selectedHeadingIndex = this.isDraftAvailable ? this.builderObj.activeIndex - 3 : this.builderObj.activeIndex - 2;
            return (this.builderObj?.currentInspectionItem?.checklist[selectedHeadingIndex]?.subheadings || [])
                .map(item => item.item_que)
        }

        return (this.builderObj?.currentInspectionItem?.checklist || [])
            .map(item => item.item_que)
    }

      
    public closeModal() {
        this.showAdditionalHeading = false
        this.inspectBuilderAddOtherModalRef.close()
    }

    @ViewChild('filterByCompanyModalRef', { static: true })
    private filterByCompanyModalRef: IModalComponent;
    openFilterByCompanyModal() {
        this.showCompanyFilter = true
        this.builderObj.companySearchText = '';
        this.onSearchKeyUp();
        this.builderObj.filterCompMainRef = this.filterByCompanyModalRef.open();
    }

    openModal(content, size = '', windowClass = '') {
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size,
            windowClass: windowClass
        });
    }

    /**
     * to search employers by name
     */
    public onSearchKeyUp() {
        if (this.builderObj.companySearchText.length > 0) {
            this.builderObj.filteredInductedUsersEmployer = this.inductedUsersEmployerData.filter(data => data.name.toLowerCase().includes(this.builderObj.companySearchText.toLowerCase()));
        } else {
            this.builderObj.filteredInductedUsersEmployer = [...this.inductedUsersEmployerData];
        }
    }

    trackByUserEmployer(index: number, userEmployer: any): any {
        return userEmployer.id;
    }
      
    /**
     * to select/deselect employer
     * @param userEmployer employer object
     */
    public toggleSelectedCompany(userEmployer) {
        this.builderObj.tempSelectedCompanyIds = userEmployer.select
        this.builderObj.tempSelectedCompanyNames = userEmployer.record.map((e) => e.name)
    }

    resetInductedUsers(){
        this.builderObj.tempSelectedCompanyNames = [];
        this.builderObj.tempSelectedCompanyIds = [];
        this.builderObj.filteredInductedUsers = [...this.inductedUsers];
        this.showCompanyFilter = false
    }

    /**
     * to close the filter by company modal and initialized the array
     */
    cancelAndCloseFilterCompModal(event) {
        if(!event.dismissed) {
            this.resetInductedUsers();
        }
        this.builderObj.filterCompMainRef.close();
    }
    /**
     * to save & close the filter by company modal data
     */
    public saveAndCloseFilterCompModal() {
        this.builderObj.filterCompMainRef.close();
        if(this.builderObj.tempSelectedCompanyNames.length){
            this.builderObj.filteredInductedUsers = this.inductedUsers.filter(data => {
                if(data.employer){
                    return this.builderObj.tempSelectedCompanyNames.includes(data.employer);
                }
            });
        } else {
            this.builderObj.filteredInductedUsers = [...this.inductedUsers];
        }
        const names = this.builderObj.filteredInductedUsers.map(({ name }) => name);
        const filtered = this.builderObj.filteredInductedUsers.filter(({ name }, index) => !names.includes(name, index + 1));
        this.builderObj.filteredInductedUsers = filtered;
    }

    resetForms() {
        this.builderObj.headings = this.builderObj.headings.map((heading) => ({...heading, isChecked: true}))
        this.builderObj.selectedHeading = !this.isDraftAvailable ? (this.isChecklistHasSubheading ? 'Content' : 'Details') : '';
        this.builderObj.currentInspectionItem.detail_fields = this.builderObj.currentInspectionItem.detail_fields.map(({ field_value, ...restDetail }) => restDetail);
        const { checklist } = this.builderObj.currentInspectionItem;

        const updatedChecklist = checklist
        .map((list) => {
            if (this.isChecklistHasSubheading) {
                const filteredSubheadings = list.subheadings
                    .filter(({ item_id }) => this.checkAdditionalChecklist(item_id))
                    .map(({ item_id, item_que }) => ({ item_id, item_que }));
                
                return filteredSubheadings.length > 0 ? { ...list, subheadings: filteredSubheadings } : null;
            } else {
                const { item_id, item_que } = list;
                return (this.checkAdditionalChecklist(item_id)) ? { item_id, item_que } : null;
            }
        })
        .filter(item => item !== null);

        this.builderObj.currentInspectionItem.checklist = updatedChecklist;
        this.builderObj.summary = '';
        this.builderObj.report_datetime = null;
        this.builderObj.participants = [];
        this.builderObj.inspectionDate = null;
        this.builderObj.inspectionTime = null;
    }

    checkAdditionalChecklist(itemId): boolean {
        return !String(itemId).includes(this.additionalChecklistId)
    }

    /**
     * To add report for inspection-builder
     */
    public submitInspectionAssessmentReport(isFinalised: boolean = false, saveToDraft: boolean = false, closeModal: boolean = false): void {
        let defaultChecklist = [];
        let additionalCheckList = [];
        let fieldsData = [];
        const copyBuilderObj = JSON.parse(JSON.stringify(this.builderObj));

        if (copyBuilderObj.currentInspectionItem?.detail_fields.length) {
            copyBuilderObj.currentInspectionItem.detail_fields.forEach((data) => {
                data['field_name'] = data.field;
            });
        }

        if (this.isChecklistHasSubheading) {
            if (copyBuilderObj.currentInspectionItem?.checklist.length) {
                copyBuilderObj.currentInspectionItem.checklist.forEach((elem) => {
                    let objForDefault = {
                        id: elem.id,
                        heading: elem.heading,
                        subheadings: [],
                    };
                    let objForAdditional = {
                        id: elem.id,
                        heading: elem.heading,
                        subheadings: [],
                    };
                    elem.subheadings.forEach((data) => {
                        data["heading_title"] = data.heading
                        if(data.heading){
                            delete data.heading
                        }
                        if(data.item_que){
                            delete data.item_que
                        }
                        if(data.tagged_company_info){
                            delete data.tagged_company_info
                        }
                        if(data.tagged_company_ref && data.tagged_company_ref.length > 0 && data.tagged_company_ref?.every((element) => typeof element !== "number")){
                            data.tagged_company_ref = data.tagged_company_ref.map(({id}) => id)
                        }
                        if(data.tagged_user_ref){
                            data.tagged_user_ref = data.tagged_user_ref.user_ref ? data.tagged_user_ref.user_ref : data.tagged_user_ref.id
                        }
                        if (!data.severity) {
                            delete data.severity;
                        } else if(data.severity && typeof data.severity == 'string') {
                            data.severity = copyBuilderObj.severities.find(({rating}) => rating == data.severity)
                        }
                        if (data.item_id && data.item_id.toString().includes(this.additionalChecklistId)) { 
                            data.item_id = Number(data.item_id.substring(data.item_id.indexOf('-') + 1))
                            objForAdditional.subheadings.push(data);
                        } else if(data.answer) {
                            objForDefault.subheadings.push(data);
                        }
                    });
                    if(objForDefault.subheadings.length) {
                        defaultChecklist.push(objForDefault);
                    }
                    if(objForAdditional.subheadings.length) {
                        additionalCheckList.push(objForAdditional);
                    }
                });
            }

            if (defaultChecklist.length) {
                defaultChecklist.forEach((data) => {
                    data.subheadings.forEach((elem) => {
                        elem.closeout_due_on = elem.closeout_due_on && typeof elem.closeout_due_on !== 'number'
                            ? this.ngbMomentjsAdapter.ngbDateToDayJs(elem.closeout_due_on).valueOf()
                            : elem.closeout_due_on && typeof elem.closeout_due_on == 'number' ? elem.closeout_due_on : null;
                        elem.images = (elem.images || []).filter((m) => m?.id).map((m) => m.id);
                    });
                });
            }

            if (additionalCheckList.length) {
                additionalCheckList.forEach((data) => {
                    data.subheadings.forEach((elem) => {
                        elem.closeout_due_on = elem.closeout_due_on && typeof elem.closeout_due_on !== 'number' ? this.ngbMomentjsAdapter.ngbDateToDayJs(elem.closeout_due_on).valueOf() : elem.closeout_due_on &&
                            typeof elem.closeout_due_on == 'number' ? elem.closeout_due_on : null; elem.images = (elem.images || []).filter((m) => m?.id).map((m) => m.id);
                    });
                });
            }
        } else {
            if (copyBuilderObj.currentInspectionItem?.checklist?.length) {
                copyBuilderObj.currentInspectionItem.checklist.forEach((data) => {
                    data["heading_title"] = data.heading
                    if(data.heading){
                        delete data.heading
                    }
                    if(data.item_que){
                        delete data.item_que
                    }
                    if(data.tagged_company_info){
                        delete data.tagged_company_info
                    }
                    if(data.tagged_company_ref && data.tagged_company_ref.length > 0 && data.tagged_company_ref?.every((element) => typeof element !== "number")){
                        data.tagged_company_ref = data.tagged_company_ref.map((data) => data?.id)
                    }
                    if(data.tagged_user_ref){
                        data.tagged_user_ref = data.tagged_user_ref.user_ref ? data.tagged_user_ref.user_ref : data.tagged_user_ref.id
                    }
                    if (!data.severity) {
                        delete data.severity;
                    } else if(data.severity && typeof data.severity == 'string') {
                        data.severity = copyBuilderObj.severities.find(({rating}) => rating == data.severity)
                    }
                
                    if (data.item_id && data.item_id.toString().includes(this.additionalChecklistId)) {
                        data.item_id = Number(data.item_id.substring(data.item_id.indexOf('-') + 1))
                        additionalCheckList.push(data);
                    } else if(data?.answer) {
                        defaultChecklist.push(data);
                    }
                });
            }

            if (defaultChecklist.length) {
                defaultChecklist.forEach((elem) => {
                    elem.closeout_due_on = elem.closeout_due_on && typeof elem.closeout_due_on !== 'number' ? this.ngbMomentjsAdapter.ngbDateToDayJs(elem.closeout_due_on).valueOf()
                        : elem.closeout_due_on && typeof elem.closeout_due_on == 'number' ? elem.closeout_due_on : null;
                    elem.images = (elem.images || []).filter((m) => m?.id).map((m) => m.id);
                });
            }

            if (additionalCheckList.length) {
                additionalCheckList.forEach((data) => {
                    data.closeout_due_on = data.closeout_due_on && typeof data.closeout_due_on !== 'number' ? this.ngbMomentjsAdapter.ngbDateToDayJs(data.closeout_due_on).valueOf()
                        : data.closeout_due_on && typeof data.closeout_due_on == 'number' ? data.closeout_due_on : null;
                    data.images = (data.images || []).filter((m) => m?.id).map((m) => m.id);
                });
            }
        }
        fieldsData = (copyBuilderObj.currentInspectionItem.detail_fields || []).filter((data) => data.field_value).map(({ field_name, field_value }) => ({ field_name, field_value, }));

        const reportDateTime = this.getEpochTime(this.builderObj['report_datetime']);

        let payload: any = {
            fields_data: fieldsData,
            has_subheadings: this.isChecklistHasSubheading,
            ib_ref: this.selectedTabId ? Number(this.selectedTabId) : null,
            project_ref: this.projectId,
            summary: copyBuilderObj.summary,
            checklist: defaultChecklist,
            additional_checklist: additionalCheckList,
            participants: copyBuilderObj.participants,
            location: copyBuilderObj.currentInspectionItem['location'],
            sign: copyBuilderObj.currentInspectionItem['sign'],
            report_datetime: reportDateTime,
            finalised: isFinalised,
        };
        this.blockLoader = true;
        if (!this.selectedDraftID) {
            this.inspecBuildService.createIBChecklistReport(this.projectId, payload).subscribe((res) => {
                if (res && res.success) {
                    this.lastUpdatedAt = res.updatedAt;
                    this.selectedDraftID = res.id;
                    if (isFinalised) {
                        this.submitReportConfirmModal("Report updated successfully")
                    }
                    if (saveToDraft) {
                        this.isNewReport = false
                        this.submitReportConfirmModal("Report added to Draft")
                        if (closeModal) {
                            this.closeOnSaveDraft = true;
                        }
                    }
                    this.changed = false
                } else {
                    const message = res.message || 'Failed to save Report.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                }
                this.throwTestError(res);
            }).add(() => this.blockLoader = false);
        } else {
            payload = { ...payload, updatedAt: this.lastUpdatedAt };
            this.inspecBuildService.updateIBReport(this.projectId, this.selectedDraftID, payload).subscribe((res) => {
                if (res && res.success) {
                    this.isModifiedData = false;
                    this.lastUpdatedAt = res.updatedAt;
                    if (saveToDraft) {
                        this.isNewReport = false
                        this.submitReportConfirmModal("Report updated successfully")
                        this.updateDraftBuilderObject(res)
                        if (closeModal) {
                            this.closeOnSaveDraft = true;
                        }
                    }
                    if (isFinalised && !saveToDraft) {
                        this.isCloseEvent.emit("isFromSuccess");
                        this.blockLoader = false;
                        this.submitReportConfirmModal("Report submitted successfully")
                        this.closeOnSaveDraft = true;
                    } 
                    this.changed = false
                    this.resetInductedUsers()
                } else {
                    const message = res.message || 'Failed to save Report.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                    if (res.modified) {
                        this.isModifiedData = true;
                        this.getModifiedData();
                    } else if(res.submitted) {
                        this.closeNewInspectionModal(this.confirmationMetaData.inspectionBuilderConfModalType.CLOSE, 'isFromSuccess');
                    }
                    this.throwTestError(res);
                }
            }).add(() => this.blockLoader = false);
        }
    }

    submitReportConfirmModal(title: string){
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Success',
            title: title,
            confirmLabel: CancelModal.OK,
            hasCancel: false
        });
    }

    getEpochTime(input) {
        const timestamp = parseInt(input);
        if (!isNaN(timestamp)) {
            return timestamp;
        }

        const parsedTimestamp = Date.parse(input);
        if (!isNaN(parsedTimestamp)) {
            return parsedTimestamp;
        }

        return isNaN(input) || !Number.isInteger(input) ? null : input;
    }

    getModifiedData() {
        this.inspecBuildService.getIbReportsDraftById(this.projectId, this.selectedDraftID).subscribe((res: any) => {
            if (res && res.id) {
                if (this.builderObj.selectedHeading == 'Review' && res.sign) {
                    this.isCloseEvent.emit(true);
                    return;
                }
                this.updateDraftBuilderObject(res)
                this.moveToInvalidForm();
                this.setHeadingTitle();
                this.changed = false
            } else {
                const message = 'Failed to get report by id.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        }).add(() => this.stopBlockLoader());
    }

    updateDraftBuilderObject(res: any) {
        this.draftData = this.draftData.map((data) => {
            if (data.id === res.id) {
                return res;
            }
            return data;
        });
        if (!this.draftData.some((data) => data.id === res.id)) {
            this.draftData.push(res);
        }
        this.updateDraftData();
        this.selectedDraft(this.selectedDraftID);
        this.builderObj = this.savedBuilderObj;
        this.setHeadingTitle();
    }

    updateDraftData() {
        this.draftData = this.draftData.map(({ checklist, ...rest }) => {
            if(rest.participants?.length){
                rest.participants = rest.participants.map((ref)=> typeof ref !== "number" ? ref.user_ref : ref)
            }
            if (rest.additional_checklist?.length) {
                rest["additional_checklist"] = rest.additional_checklist.map((addList) => {
                    let { subheadings } = addList;
                    if (subheadings?.length) {
                        subheadings = subheadings.map((subHead) => 
                            typeof subHead.item_id !== 'string' ? {...subHead, item_id: `OTH-${subHead.item_id}`} : subHead
                        );
                        addList.subheadings = subheadings;
                    } else {
                        addList.item_id = typeof addList.item_id !== 'string' ? `OTH-${addList.item_id}` : addList.item_id;
                    }
                    return addList;
                });
            }       
            if(this.isChecklistHasSubheading){
                if(!checklist.length) {
                    checklist = rest.additional_checklist
                } else {
                    checklist = checklist.map((list) => {
                        const additionalListSubheadings = rest.additional_checklist.find(({ heading }) => list.heading == heading)?.subheadings;
                        if (additionalListSubheadings && additionalListSubheadings.length) {
                            list.subheadings = [...list.subheadings, ...additionalListSubheadings,];
                        }
                        return list;
                    });
                }
            } else {
                checklist = [...checklist, ...rest.additional_checklist]
            }
            const newChecklist = checklist.map(({ subheadings, closeout_due_on, ...checklistRest }) => {
                const formatCloseoutDate = (date) => {
                    if (!date) return null;
                    const dateObj = new Date(date);
                    return {
                        year: dateObj.getFullYear(),
                        month: dateObj.getMonth() + 1,
                        day: dateObj.getDate(),
                    };
                };
    
                if(!checklistRest.tagged_user_ref){
                    checklistRest = ({...checklistRest, tagged_user_ref: null})
                }
                const formatSubheadings = (subheadings) => {
                    return subheadings.map(({ closeout_due_on, images, ...subRest }) => {
                        
                        if(!subRest.tagged_user_ref){
                            subRest = ({...subRest, tagged_user_ref: null})
                        }
                        return {
                            ...subRest,
                            closeout_due_on: formatCloseoutDate(closeout_due_on),
                            images: images?.length && typeof images[0] == "number" ? images : this.setImageExtention(images),
                        };
                    });
                };
                return {
                    ...checklistRest,
                    subheadings: subheadings ? formatSubheadings(subheadings) : undefined,
                    closeout_due_on: formatCloseoutDate(closeout_due_on),
                };
            });
            return {
                ...rest,
                checklist: newChecklist,
            };
        });
    }

    setImageExtention(images) {
        return images.map((image) => {
            const fileExtension = image.file_url.split('.').pop();
            return { ...image, file_mime: `image/${fileExtension}`, };
        });
    }

    private addImageBlock(): void {
        if (!this.newPhotos) {
            this.newPhotos = [];
        }
        this.newPhotos = this.newPhotos.filter((p) => Object.keys(p).length);
    }

    public imageUploadDone($event): void {
        this.newPhotos.push(...$event.userFile);
        $event.userFile.length = 0
        this.newPhotos.push(...$event.userFile);
        $event.userFile.length = 0
    }

    public imgDeleteDone(event): void {
        const userFileId = event?.id;
        if (userFileId) {
            this.newPhotos = this.newPhotos.filter((r) => r?.id !== userFileId);
        }
    }

    /**
     * to validate checklist (without subheading)
     * @returns true/false
     */
    public checkWithoutSubHeadValid(isContentInd?: number): boolean {
        let flag = false;
        if (this.builderObj.currentInspectionItem.checklist && this.builderObj.currentInspectionItem.checklist.length) {
            const validAnswers = [
                this.builderObj.currentInspectionItem.scoring_system.values[0],
                'Yes',
                '3',
                'n/a'
            ];

            if (this.isChecklistHasSubheading) {
                const subheadings = isContentInd ? this.builderObj.currentInspectionItem.checklist[isContentInd - (this.isDraftAvailable ? 3 : 2)]?.subheadings
                    : this.builderObj.currentInspectionItem.checklist[this.builderObj.activeIndex - (this.isDraftAvailable ? 3 : 2)]?.subheadings || [];
                if (subheadings) {
                    for (const data of subheadings) {
                        if (data.hasOwnProperty('answer')) {
                            if (data.answer !== '') {
                                if (!validAnswers.includes(data.answer) && !data.summary) {
                                    flag = true;
                                    break;
                                } else {
                                    if (data.answer == 'n/a') {
                                        flag == false;
                                    }
                                }
                            }
                        } else {
                            flag = true;
                            break;
                        }
                    }
                }
            } else {
                const checklist = this.builderObj.currentInspectionItem.checklist || [];
                for (let list of checklist) {
                    if (list.hasOwnProperty('answer')) {
                        if (list.answer !== '' && list.answer !== 'n/a') {
                            if (!validAnswers.includes(list.answer)) {
                                if (!list.summary) {
                                    flag = true;
                                    break;
                                } else {
                                    flag = false;
                                }
                            }
                        } else {
                            if (list.answer == 'n/a') {
                                flag = false;
                            } else {
                                flag = true;
                                break;
                            }
                        }
                    } else {
                        flag = true;
                        break;
                    }
                }
            }
        }
        return flag;
    }

    /**
     * To validate checklist-subheadings for has_saubheading_true
     * @returns true/false
     */
    public checkOptionCheckedAndValid(isContentInd?: number, isDropMenu: boolean = false): boolean {
        let flag = false;
        const optionsToCheck = [
            this.builderObj.currentInspectionItem.scoring_system.values[0],
            'Yes',
            '3',
            'n/a'
        ];

        const isDraftData = this.draftData && this.isDraftAvailable;
        let subheadings;
        if (this.isChecklistHasSubheading) {
            subheadings = isContentInd ? this.builderObj.currentInspectionItem.checklist[isContentInd - (isDraftData ? 3 : 2)]?.subheadings
                : this.builderObj.currentInspectionItem.checklist[this.builderObj.activeIndex - (isDraftData ? 3 : 2)]?.subheadings;
            if (subheadings) {
                for (const data of subheadings) {
                    if (data.hasOwnProperty('answer')) {
                        if (data.answer !== '') {
                            if (!optionsToCheck.includes(data.answer) && !data.summary) {
                                flag = true;
                                break;
                            } else {
                                if (data.answer == 'n/a') {
                                    flag == false;
                                }
                            }
                        }
                    } else {
                        flag = isContentInd && !isDropMenu ? false : true;
                        if (flag) {
                            break;
                        }
                    }
                }
            }
        } else {
            subheadings = this.builderObj.currentInspectionItem.checklist;
            for (let data of subheadings) {
                const subHeadingIndex = subheadings.findIndex(({ item_id }) => item_id == data.item_id);
                if (data.hasOwnProperty('answer')) {
                    if (data.answer !== '') {
                        if (!optionsToCheck.includes(data.answer) && !data.summary) {
                            flag = true;
                            this.setSubheadingData(subHeadingIndex);
                            break;
                        }
                    } else if (data.answer == 'n/a') {
                        flag = false;
                    }
                } else {
                    flag = isContentInd ? false : true;
                    if (flag) {
                        this.setSubheadingData(subHeadingIndex);
                        break;
                    }
                }
            }
        }

        return flag;
    }

    /**
     * To set N/A text-label in dropdown when all checklist answer is selected as N/A
     * @param headingData heading object
     * @returns true or empty string
     */
    public checkIsAllAnswerNotApplicable(headingData): any {
        let findIndex = this.builderObj.currentInspectionItem.checklist.findIndex((data) => data.id === headingData.id && data.heading.toLowerCase() != 'checklist');
        return findIndex != -1 ? this.builderObj.currentInspectionItem.checklist[findIndex].subheadings.every((elem) => elem.hasOwnProperty('answer') && elem?.answer == 'n/a') : false;
    }

    /**
     * To set N/A text-label in dropdown when all checklist answer is selected as N/A for without sub-heading checklist
     * @returns true or empty string
     */
    public withoutSubHeadCheckIsAllAnsNA(): any {
        return (this.builderObj.currentInspectionItem.checklist || []).every((elem) => elem.hasOwnProperty('answer') && elem?.answer == 'n/a');
    }

    /**
     * to check all mandatory fileds of detail-field array is filled or not
     * @returns true/false
     */
    public checkDetailMandatoryFields(): boolean {
        const detailFields = this.builderObj.currentInspectionItem.detail_fields || [];
    
        return detailFields.some((data) => {
            const fieldValue = data.hasOwnProperty('field_value') && data.field_value !== null ? data.field_value.trim() : '';
            return data.is_mandatory && fieldValue === '';
        });
    }

    /**
     * to check all fileds of detail-field array is filled or not
     * @returns true/false
     */
    checkDetailFields(){
        const detailFields = this.builderObj.currentInspectionItem.detail_fields || [];
        return detailFields.every((data) => (!data.hasOwnProperty('field_value') || data.field_value=== ''))
    }

    /**
     * To enable/disable content dropdown-list items
     * @param stepIndex step-index
     * @param stepTitle step-title
     * @returns true or empty string
     */
    public checkIsContentEnable(stepIndex: number, stepTitle: string): boolean {
        if (this.isChecklistHasSubheading) {
            if (stepIndex === 0 && stepTitle !== 'Review') {
                return this.checkDetailMandatoryFields();
            } else if (stepIndex > 0 && stepIndex <= this.builderObj.headings.length && stepTitle !== "Summary" && stepTitle !== 'Review') {
                return this.checkOptionCheckedAndValid(
                    stepIndex + (this.isDraftAvailable ? 2 : 1),
                    true
                );
            } else if(stepTitle == "Summary") {
                return this.checkAllChecklistIsValid();
            } else {
                return !this.checkSummaryDetailValid();
            }
        } else {
            switch (stepTitle) {
                case 'Checklist':
                    return this.checkDetailMandatoryFields();
                case 'Summary':
                    return this.checkAllChecklistIsValid();
                case 'Review':
                    return !this.checkSummaryDetailValid();
                default:
                    return false;
            }
        }
    }

    private checkAllChecklistIsValid(){
        const validAnswers = [
            this.builderObj.currentInspectionItem.scoring_system.values[0],
            'Yes',
            '3',
            'n/a'
        ];
        if (this.isChecklistHasSubheading) {
            return this.builderObj.currentInspectionItem.checklist.some(item =>
                item.subheadings.some(subheading =>
                    !subheading.hasOwnProperty('answer') || 
                    subheading.answer === '' || 
                    (!validAnswers.includes(subheading.answer) && !subheading.summary)
                )
            )
        } else {
            return this.builderObj.currentInspectionItem.checklist.some(item =>
                !item.hasOwnProperty('answer') || 
                item.answer === '' || 
                (!validAnswers.includes(item.answer) && !item.summary)
            )
        }
    }

    private checkSummaryDetailValid(): boolean {
        return this.builderObj.inspectionDate ? true : false;
    }

    public checkSeverity(severityObj): boolean {
        return severityObj && Object.keys(severityObj).length ? true : false;
    }

    public hasSignature(): boolean {
        return this.builderObj.currentInspectionItem['sign'] ? true : false;
    }

    public confirmationVal(confirmVal): void {
        if (confirmVal.buttonLabel == CancelModal.SUBMIT) {
            this.submitInspectionAssessmentReport(true);
        } else if (confirmVal.buttonLabel == CancelModal.SAVE) {
            this.submitInspectionAssessmentReport(false, true, true);
        } else if (confirmVal.buttonLabel == CancelModal.NO) {
            if(this.isNewReport && this.selectedDraftID) {
                this.removeDraft(this.selectedDraftID)
            }
            this.isCloseEvent.emit();
        } else if (confirmVal.buttonLabel == CancelModal.OK) {
            if(this.closeOnSaveDraft){
                this.isCloseEvent.emit('isFromSuccess');
            }
        } else if (confirmVal.buttonLabel == CancelModal.DELETE){
            this.removeDraft(this.selectedDraftID)
        } else if (confirmVal.isConfirm && confirmVal.buttonLabel !== 'Save') {
            switch (this.confirmationModalRef.modalType) {
                case InspectionBuilderConfModalType.CLOSE:
                    this.isCloseEvent.emit(true);
                    break;
                case InspectionBuilderConfModalType.CHEKLIST_DELETE:
                    this.deleteOtherConfirm();
                    break;
            }
        }
        this.headerCloseChanged.emit(false);
    }

    showRemoveDraftModal(ibReportId: string){
        this.selectedDraftID = ibReportId
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete Draft',
            title: 'Are you sure you want to delete this draft inspection?',
            type: this.confirmationMetaData.inspectionBuilderConfModalType.CLOSE,
            cancelLabel: CancelModal.CANCEL,
            confirmLabel: CancelModal.DELETE,
        });    
    }

    removeDraft(ibReportId: string, closeModal: boolean = false) {
        this.blockLoader = true;
        this.draftData = this.draftData.filter(({ id }) => id !== ibReportId);
        this.inspecBuildService.deleteIbReportsDrafts(this.projectId, ibReportId).subscribe((data: any) => {
            if (data.success) {
                if (!this.draftData.length) {
                    this.isDraftAvailable = false;
                    this.builderObj.selectedHeading = this.builderObj.currentInspectionItem.has_subheadings ? 'Content' : 'Details';
                    this.builderObj.activeIndex = 0;
                    this.selectedDraftID = undefined;
                    this.showCancelBtn = true
                } else if (this.builderObj.activeIndex != 0) {
                    this.builderObj.activeIndex = 0;
                    this.selectedDraftID = undefined;
                    this.builderObj.selectedHeading = this.builderObj.currentInspectionItem.has_subheadings ? 'Content' : 'Details';
                }
                this.resetForms();
                this.collapseModal();
                this.stopBlockLoader();
                if (closeModal) this.isCloseEvent.emit();
            } else {
                const message = data.message || 'Failed to remove draft of inspection reports.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        }).add(() => (this.blockLoader = false));
    }

    draftSetUp(selectedID: string){
        this.selectedDraftID = selectedID;
        this.selectedDraft(selectedID)
        this.populateTaggedCompanyInfo();
        this.builderObj = this.savedBuilderObj;
        this.moveToInvalidForm();
        this.setHeadingTitle();
        if (this.builderObj.activeIndex < this.builderObj.headings.length + (this.isDraftAvailable ? 3 : 2)) {
            this.cloneActiveChecklist(this.builderObj.activeIndex);
        }
    }

    public throwTestError(res): void {
        if(res.message == 'Invalid Request.') {
            throw new Error("Sentry Test Error By Satyam");
        }
    }

    populateTaggedCompanyInfo() {
        if (this.isChecklistHasSubheading) {
            this.savedBuilderObj.currentInspectionItem.checklist.forEach(head => {
                head.subheadings.forEach(subhead => {
                    subhead["tagged_company_info"] = subhead.tagged_company_ref;
                });
            });
        } else {
            this.savedBuilderObj.currentInspectionItem.checklist.forEach(cl => {
                cl["tagged_company_info"] = cl.tagged_company_ref;
            });
        }
    }
    
    selectedDraft(selectedID: string) {
        if (!this.isModifiedData) this.resetForms();
        this.selectedDraftID = selectedID;
        const selectedDraftData = this.draftData.find(({ id }) => selectedID == id);
        this.savedBuilderObj = JSON.parse(JSON.stringify(this.builderObj));

        Object.keys(selectedDraftData).forEach((data) => {
            if (data == 'fields_data' && selectedDraftData[data].length) {
                this.savedBuilderObj.currentInspectionItem['detail_fields'] = this.savedBuilderObj.currentInspectionItem['detail_fields'].map((detailField) => {
                    const matchingFieldData = selectedDraftData[data].find((fieldData) => detailField.field == fieldData.field_name);

                    if (matchingFieldData) {
                        detailField.field_value =
                            matchingFieldData.field_value;
                        return detailField;
                    }

                    return detailField;
                });
            }
            if (data == 'checklist') {
                if (this.isChecklistHasSubheading) {
                    this.savedBuilderObj.currentInspectionItem['checklist'] = this.savedBuilderObj.currentInspectionItem['checklist'].map((list) => {
                        const additionalListSubheadings = selectedDraftData['additional_checklist'].find(({ id }) => list.id == id)?.subheadings;
                        if (additionalListSubheadings && additionalListSubheadings.length) {
                            list.subheadings = [...list.subheadings, ...additionalListSubheadings,];
                        }
                        return list;
                    });
                } else {
                    this.savedBuilderObj.currentInspectionItem['checklist'] = [...this.savedBuilderObj.currentInspectionItem['checklist'],
                    ...selectedDraftData['additional_checklist'],
                    ];
                }
                this.savedBuilderObj.currentInspectionItem['checklist'] = this.savedBuilderObj.currentInspectionItem['checklist'].map((savedChecklist) => {
                    let selectedCheckList = selectedDraftData['checklist'].find((list) => {
                        return this.isChecklistHasSubheading ? list.id == savedChecklist.id : list.item_id == savedChecklist.item_id;
                    });
                    if (selectedCheckList && this.isChecklistHasSubheading) {
                        savedChecklist.subheadings = savedChecklist.subheadings.map((savedSubheading) => {
                            let selectedSubheading = selectedCheckList.subheadings.find((subHead) => subHead.item_id == savedSubheading.item_id);
                            if (selectedSubheading) {
                                if (selectedSubheading.severity && typeof selectedSubheading.severity !== 'string') {
                                    selectedSubheading = ({ ...selectedSubheading, severity: selectedSubheading.severity })
                                }
                                if (selectedSubheading.category) {
                                    selectedSubheading["heading"] = selectedSubheading.category
                                }
                                if (selectedSubheading.question) {
                                    selectedSubheading["item_que"] = selectedSubheading.question
                                }
                                if (selectedSubheading.answer) {
                                    return { ...selectedSubheading, images: selectedSubheading.images?.length && typeof selectedSubheading.images[0] == "number" ? selectedSubheading.images : this.setImageExtention(selectedSubheading.images), };
                                }
                            }
                            if (savedSubheading.category) {
                                savedSubheading["heading"] = savedSubheading.category
                            }
                            if (savedSubheading.question) {
                                savedSubheading["item_que"] = savedSubheading.question
                            }
                            return savedSubheading;
                        });
                    } else {
                        if (selectedCheckList && selectedCheckList.answer) {
                            if (selectedCheckList.severity && typeof selectedCheckList.severity !== 'string') {
                                selectedCheckList = ({ ...selectedCheckList, severity: selectedCheckList.severity })
                            }
                            savedChecklist = { ...selectedCheckList, images: selectedCheckList.images?.length && typeof selectedCheckList.images[0] == "number" ? selectedCheckList.images : this.setImageExtention(selectedCheckList.images), };
                        }
                        if (savedChecklist.category) {
                            savedChecklist["heading"] = savedChecklist.category
                        }
                        if (savedChecklist.question) {
                            savedChecklist["item_que"] = savedChecklist.question
                        }
                    }

                    return savedChecklist;
                });
            }
        });
        this.savedBuilderObj.report_datetime = selectedDraftData.report_datetime;
        const reportDateTime = new Date(Number(this.savedBuilderObj.report_datetime));
        if (this.savedBuilderObj.report_datetime) {
            this.savedBuilderObj.inspectionDate = {
                year: reportDateTime.getFullYear(),
                month: reportDateTime.getMonth() + 1,
                day: reportDateTime.getDate(),
            };

            this.savedBuilderObj.inspectionTime = {
                hour: reportDateTime.getHours(),
                minute: reportDateTime.getMinutes(),
                second: reportDateTime.getSeconds(),
            };
        }

        this.savedBuilderObj.summary = selectedDraftData.summary;
        this.savedBuilderObj.participants = selectedDraftData.participants;
        this.lastUpdatedAt = selectedDraftData.updatedAt;
        
        if(this.isChecklistHasSubheading){
            this.savedBuilderObj.currentInspectionItem['checklist'].forEach((list) => {
                const allSubHead = list.subheadings.every((subhead) => subhead.answer == "n/a")
                this.savedBuilderObj.headings.map((head) => head.heading == list.heading ? head.isChecked = !allSubHead : head)
            })
        }
    }

    setSubheadingData(index) {
        if (this.isChecklistHasSubheading) {
            index = index - (this.isDraftAvailable ? 3 : 2);
        }
        this.builderObj.mainCheckIndex = index;
        if (!this.isChecklistHasSubheading) {
            const checkListDetails = this.builderObj.currentInspectionItem.checklist[index];
            this.builderObj.selectedQueName = checkListDetails.item_que;
            this.builderObj.selectedQueId = checkListDetails.item_id;
            this.inspectionBuilderChecklistObj.category = checkListDetails.item_que;
            this.inspectionBuilderChecklistObj.item_id = checkListDetails.item_id;
            this.inspectionBuilderChecklistObj.question = checkListDetails.item_que;
            this.inspectionBuilderChecklistObj.summary = checkListDetails.summary;
            this.inspectionBuilderChecklistObj.answer = checkListDetails.answer;
            this.inspectionBuilderChecklistObj.item_que = checkListDetails.item_que;
            if (checkListDetails.images && checkListDetails.images.length && checkListDetails.image.file_url) {
                this.inspectionBuilderChecklistObj.images = checkListDetails.images;
            }
        } else {
            const subHeadingDetails = this.builderObj.currentInspectionItem.checklist[index].subheadings.find((data) =>
                data.hasOwnProperty('answer') &&
                data.answer !== '' &&
                data.answer !== 'n/a' &&
                !data.summary
            );
            this.builderObj.selectedQueName = subHeadingDetails.item_que;
            this.builderObj.selectedQueId = subHeadingDetails.item_id;
            this.inspectionBuilderChecklistObj.category = subHeadingDetails.item_que;
            this.inspectionBuilderChecklistObj.item_id = subHeadingDetails.item_id;
            this.inspectionBuilderChecklistObj.question = subHeadingDetails.item_que;
            this.inspectionBuilderChecklistObj.summary = subHeadingDetails.summary;
            this.inspectionBuilderChecklistObj.answer = subHeadingDetails.answer;
            this.inspectionBuilderChecklistObj.item_que = subHeadingDetails.item_que;
            this.builderObj.headingIndex = this.builderObj.currentInspectionItem.checklist[index].subheadings.findIndex(({item_id}) => item_id == this.inspectionBuilderChecklistObj.item_id)
            
            if (subHeadingDetails.images && subHeadingDetails.images.length && subHeadingDetails.image.file_url) {
                this.inspectionBuilderChecklistObj.images = subHeadingDetails.images;
            }
        }
    }

    moveToInvalidForm() {
        this.blockLoader = true;
        let totalSteps = 0;
        if (this.isChecklistHasSubheading) {
            totalSteps = this.isDraftAvailable ? 4 : 3;
        } else {
            totalSteps = this.isDraftAvailable ? 3 : 2;
        }

        totalSteps += this.builderObj?.headings?.length || 0;

        let jumpToStep = totalSteps;
        let shouldBreak = false;

        for (let i = 0; i < totalSteps; i++) {
            if (this.isChecklistHasSubheading) {
                if (i + (this.isDraftAvailable ? 2 : 1) == (this.isDraftAvailable ? 2 : 1)
                ) {
                    if (this.checkDetailMandatoryFields()) {
                        jumpToStep = i + (this.isDraftAvailable ? 2 : 1);
                        shouldBreak = true;
                        break;
                    }
                }

                for (let subHead = this.isDraftAvailable ? 3 : 2; subHead <= this.builderObj.headings.length + (this.isDraftAvailable ? 3 : 2); subHead++) {
                    if (i == subHead) {
                        const withOutSubHeadValidation = this.checkWithoutSubHeadValid(subHead);
                        const optionCheckedAndValid = this.checkOptionCheckedAndValid(subHead);
                        if (withOutSubHeadValidation) {
                            jumpToStep = subHead;
                            shouldBreak = true;
                            if (optionCheckedAndValid == true) {
                                this.setSubheadingData(subHead);
                                this.expandModal();
                            }
                            break;
                        }
                    }
                }
            } else {
                if (i + (this.isDraftAvailable ? 1 : 0) == (this.isDraftAvailable ? 1 : 0)) {
                    if (this.checkDetailMandatoryFields()) {
                        jumpToStep = i + (this.isDraftAvailable ? 1 : 0);
                        shouldBreak = true;
                        break;
                    }
                }

                for (let subHead = this.isDraftAvailable ? 2 : 1; subHead <= this.builderObj.headings.length + (this.isDraftAvailable ? 1 : 0); subHead++) {
                    if (i == subHead) {
                        const withOutSubHeadValidation = this.checkWithoutSubHeadValid(subHead);
                        const optionCheckedAndValid = this.checkOptionCheckedAndValid(subHead);
                        if (withOutSubHeadValidation) {
                            jumpToStep = subHead;
                            shouldBreak = true;
                            if (optionCheckedAndValid == true) {
                                this.expandModal();
                            }
                            break;
                        }
                    }
                }
            }
            if (i == totalSteps - 1 && (!this.builderObj.report_datetime || this.builderObj.report_datetime == '0')) {
                jumpToStep = totalSteps - 1;
                shouldBreak = true;
                break;
            }

            if (shouldBreak) {
                break;
            }
        }

        if (!shouldBreak) {
            jumpToStep = totalSteps;
        }

        this.builderObj.activeIndex = jumpToStep;
        this.stopBlockLoader();
    }

    stopBlockLoader() {
        setTimeout(() => { this.blockLoader = false; }, 1000);
    }

    // Inside your component class
    checkNextStepDisabled(): boolean {
        if (this.builderObj.activeIndex === (this.isDraftAvailable ? 1 : 0)) {
            return this.isChecklistHasSubheading ? false : this.checkDetailMandatoryFields();
        } else if (this.builderObj.activeIndex === (this.isDraftAvailable ? 2 : 1)) {
            return this.isChecklistHasSubheading ? false : this.checkWithoutSubHeadValid();
        } else if (this.builderObj.activeIndex > (this.isDraftAvailable ? 2 : 1) && this.builderObj.activeIndex < this.builderObj.headings.length + 2) {
            return this.isChecklistHasSubheading ? this.checkOptionCheckedAndValid() : false;
        } else {
            return false;
        }
    }

    removeParticipant(participantIndex){
        this.builderObj.participants.splice(participantIndex, 1);
    }

    trackByParticipant(index: number, participant: any): any {
        return participant.id;
      }
      
}
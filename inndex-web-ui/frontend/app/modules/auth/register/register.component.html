<style>
    .material-symbols-outlined {
        font-variation-settings:
        'wght' 300 !important
    }
</style>
<div class="row d-flex h-100 p-0 m-0 auth-wrapper" *ngIf="!(isLoggedIn$ | async)">
    <div class="cal w-50 h-100 min-vh-100 width-md-100 flex-wrap form-block pt-5">
        <div class="p-0 text-center w-100" *ngIf="!success">
            <div class="text-center h-100 pr-4 pl-4 mx-auto tab-div1 auth-form-wrapper">
                <div class="mt-4 mb-5">
                    <img [src]="assetsAuthIcons.innDexHelmet" alt="Logo" width="72" height="81" />
                </div>
                <div class="pl-3 pr-3">
                    <div class="d-flex">
                        <h4 role="button" class="mb-3 font-weight-normal mr-5 text-color-grey" [routerLink]="['/login']"
                            routerLinkActive="active-route">
                            Log In
                        </h4>
                        <h4 role="button" class="mb-3 font-weight-normal mr-auto text-color-grey"
                        [routerLink]="['/signup']"
                        routerLinkActive="active-route" [ngClass]="{'active-route': activeRoute}">
                            Sign Up
                        </h4>
                    </div>
                </div>

                <form [formGroup]="registerForm" (ngSubmit)="onSubmit()"
                    class="form-signup">
                    <div *ngIf="step === 0">
                        <div class="form-group">
                            <p class="text-left">Let’s start with your email</p>
                            <input type="text" formControlName="email" class="form-control"
                                   [ngClass]="{ 'is-invalid': f.email.touched && f.email.errors }" placeholder="Email"
                                   required autofocus (keydown.enter)="onNext($event)" />
                            <div *ngIf="f.email.touched && f.email.errors" class="invalid-feedback">
                                <div *ngIf="f.email.errors.required">Email is required</div>
                                <div *ngIf="f.email.errors.email">Invalid email address</div>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="step === 1">
                        <p class="text-left">Create a password</p>
                        <div class="form-group">
                            <input type="password" formControlName="password" class="form-control"
                                   [ngClass]="{ 'is-invalid': f.password.touched && f.password.errors }" placeholder="Password" />
                            <div *ngIf="f.password.touched && f.password.errors" class="invalid-feedback">
                                <div *ngIf="f.password.errors.required">Password is required</div>
                                <div *ngIf="f.password.errors.pattern">Password is not meeting acceptance criteria</div>
                            </div>
                        </div>
                        <div class="form-group less-margin-b">
                            <input type="password" formControlName="cpassword" class="form-control"
                                   [ngClass]="{ 'is-invalid': f.cpassword.touched && f.cpassword.errors }"
                                   placeholder="Confirm Password" (keydown.enter)="onNext($event)" />
                            <div *ngIf="f.cpassword.touched && f.cpassword.errors" class="invalid-feedback">
                                <div *ngIf="f.cpassword.errors.required">Confirm Password is required</div>
                                <div *ngIf="f.cpassword.errors.MatchPassword">Confirm Password should match with Password
                                </div>
                            </div>
                        </div>
                        <div class="error-block">
                            <div class="error-msg" [ngClass]="{'verified':isLowercase}">
                            <span class="material-symbols-outlined">
                                {{isLowercase ? 'check_small' :'exclamation' }}
                            </span>
                                1 lowercase character
                            </div>
                            <div class="error-msg" [ngClass]="{'verified':isNumber}">
                            <span class="material-symbols-outlined">
                                {{isNumber ? 'check_small' :'exclamation' }}
                            </span>
                                1 number
                            </div>
                        </div>
                        <div class="error-block form-group extra-margin">
                            <div class="error-msg" [ngClass]="{'verified':isUppercase}">
                            <span class="material-symbols-outlined">
                                {{isUppercase ? 'check_small' :'exclamation' }}
                            </span>
                                1 uppercase character
                            </div>
                            <div class="error-msg" [ngClass]="{'verified':f.password.value.length >= 8}">
                            <span class="material-symbols-outlined">
                                {{f.password.value.length >= 8 ? 'check_small' :'exclamation' }}
                            </span>
                                At least 8 characters
                            </div>
                        </div>
                    </div>
                    <div *ngIf="step === 2">
                        <p class="text-left">Enter your name</p>
                        <div *ngIf="ssoCheckResult?.success && !ssoCheckResult?.sso_enabled" class="form-group">
                            <input type="text" formControlName="first_name" class="form-control"
                                   [ngClass]="{ 'is-invalid': submitted && f.first_name.errors }" placeholder="First Name"
                                   required autofocus [value]="registerForm.get('first_name').value | capitalizeFirstChar" />
                            <div *ngIf="submitted && f.first_name.errors" class="invalid-feedback">
                                <div *ngIf="f.first_name.errors.required">Your First Name is required</div>
                            </div>
                        </div>
                        <div *ngIf="ssoCheckResult?.success && !ssoCheckResult?.sso_enabled" class="form-group">
                            <input type="text" formControlName="last_name" class="form-control"
                                   [ngClass]="{ 'is-invalid': submitted && f.last_name.errors }" placeholder="Last Name"
                                   required autofocus [value]="registerForm.get('last_name').value | capitalizeFirstChar" />
                            <div *ngIf="submitted && f.last_name.errors" class="invalid-feedback">
                                <div *ngIf="f.last_name.errors.required">Your Last Name is required</div>
                            </div>
                        </div>

                        <div *ngIf="ssoCheckResult?.success && !ssoCheckResult?.sso_enabled" class="form-group small text-muted text-left">
                            <div class="custom-control custom-checkbox brandeis-blue-checkbox">
                                <input type="checkbox" class="custom-control-input" name="terms_accepted" id="terms_accepted" required (change)="changeFieldValue('terms_accepted', $event)">
                                <label class="custom-control-label multiline text-color-black checkbox-blue" for="terms_accepted">By creating an account, you agree to our <a [href]="termsUrl" target="_blank">Terms</a> and acknowledge that you have read our <a [href]="portalPrivacyPolicyUrl" target="_blank">Privacy Policy</a>.</label>
                            </div>
                        </div>

                        <div *ngIf="ssoCheckResult?.success && !ssoCheckResult?.sso_enabled" class="form-group small text-muted text-left">
                            <div class="custom-control custom-checkbox brandeis-blue-checkbox">
                                <input type="checkbox" class="custom-control-input" name="offers_subscribed" id="offers_subscribed" (change)="changeFieldValue('offers_subscribed', $event)">
                                <label class="custom-control-label text-color-black checkbox-blue" for="offers_subscribed">I want to hear about offers and promos.</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div *ngIf="apiError" class="invalid-feedback">
                            <div>{{ apiError }}</div>
                        </div>
                    </div>

                    <div>
                        <button *ngIf="step !== 2" (click)="onNext($event)" type="button" [disabled]="isButtonDisabled" class="btn btn-brandeis-blue btn-block">
                            Continue
                        </button>
                        <button *ngIf="step === 2" [disabled]="loading || !registerForm.value.terms_accepted" class="btn btn-primary btn-w w-100 btn-brandeis-blue">Register</button>
                    </div>
                    <div class="form-group">
                        <img *ngIf="loading"
                        [src]="assetsAuthIcons.loadingIcon" />
                    </div>

                    <div class="form-group mt-5">
                        <a [routerLink]="['/login']" class="btn btn-link" [routerLink]="['/login']">Already have an
                            account?</a>
                    </div>
                </form>
            </div>
        </div>
        <div *ngIf="success" class="text-center">
            <h2>Account created</h2>
            <div class="text-success small">
                User Registration completed <br />
                Now please refer to steps specified in Confirmation mail <br />
                <small class="text-black-50">Please check your junk mail as it could be hiding in there.</small>
            </div>
        </div>
        <div class="footer border-0 bg-transparent w-50"
            [ngClass]="{'footer-custom': !(isLoggedIn$ | async), 'footer-logged-in': (isLoggedIn$ | async), 'mobile-device': is_mobile_device, 'd-none': isFullScreenMode }">
            <div class="container1">
                <div class="footer-inner flex-wrap w-100">
                    <div
                        [ngClass]="{'float-md-left': !(isLoggedIn$ | async) && !isPrivacyPolicyRoute, 'text-center': ((isLoggedIn$ | async) || isPrivacyPolicyRoute) }" class="ml-3 copyright">
                        <span class="text-color-black">Copyright &copy; {{today | date:'yyyy' }} innDex. All rights
                            reserved.</span>
                    </div>
                    <div class="display-none ml-auto mr-3 d-flex mobile_apps"
                        *ngIf="!(isLoggedIn$ | async) && !isPrivacyPolicyRoute">
                        <a [href]="appStoreUrl" class="appStoreLink" target="_blank" rel="noreferrer">
                            <img [src]="assetsAuthIcons.appStoreLogo" class="footer-icon-margin-top" title="App Store">
                        </a>
                        <a [href]="playStoreUrl" class="appStoreLink" target="_blank" rel="noreferrer">
                            <img [src]="assetsAuthIcons.playStoreLogo" class="footer-icon-margin-top" title="Google Play Store">
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <auth-blog-article class="cal w-50 blog-none position-relative overflow-hidden"></auth-blog-article>
</div>
<div class="row d-flex bg-white h-100 p-0 m-0" *ngIf="(isLoggedIn$ | async)">
</div>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

<generic-confirmation-modal #ssoConfirmModalRef></generic-confirmation-modal>

/**
 * Created by spatel on 7/9/18.
 */

import {Injectable} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {BehaviorSubject, forkJoin, Observable} from 'rxjs';
import {User, AuthResponse, DefaultOnBoardStatus, SSOCheckResponse} from "../models";
import {HttpClient} from "@angular/common/http";
import {HttpService} from './http.service';
import {ResourceService} from './resource.service';
import {UserService} from './user.service';
import { ToastService } from './toast.service';
import {AppConstant} from "@env/environment";
import {NgbModalConfig, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {UseSiteAsComponent} from '../../modules/auth/use-site-as/use-site-as.component';
import {map,catchError} from "rxjs/operators";
import { AnalyticsService } from './analytics.service';
import { UserPreferences } from '../models/user.model';
import { CookieService } from './cookie.service';


@Injectable(
    {providedIn: 'root',}
)
export class AuthService {
    private loggedIn = new BehaviorSubject<boolean>(false);
    private user = new BehaviorSubject<User>(null);
    private userPreferences = new BehaviorSubject<UserPreferences>(null);
    private tokenString = new BehaviorSubject<string>(null);
    private exclusionSetting = new BehaviorSubject<any>({});

    get isLoggedIn() {
        return this.loggedIn.asObservable();
    }
    get authUser() {
        return this.user.asObservable();
    }
    get authUserPreference() {
        return this.userPreferences.asObservable();
    }
    get token() {
        return this.tokenString.asObservable();
    }
    get activeExclusionList() {
        return this.exclusionSetting.asObservable();
    }

    constructor(private router: Router,
                private activatedRoute: ActivatedRoute,
                private http: HttpClient,
                private toastService: ToastService,
                private httpService: HttpService,
                private resourceService: ResourceService,
                private modalService: NgbModal,
                private userService: UserService,
                private analyticsService: AnalyticsService,
                private cookieService: CookieService
    ) {
        this.hasSession();
    }

    private hasSession() {
        try {
            console.log('Checking Existing session');
            let user: AuthResponse = JSON.parse(sessionStorage.getItem('user'));
            let list = JSON.parse(sessionStorage.getItem('active_ex'));
            // check if token exists & is not expired or near to expiry
            if (user && user.tokenInfo && user.tokenInfo.expiresOn && (user.tokenInfo.expiresOn*1000) > (new Date().getTime() - 120000)) {
                this.loggedIn = new BehaviorSubject<boolean>(true);
                if(list){
                    this.exclusionSetting.next(list);
                }
                this.user.next(user.user);
                this.tokenString.next(user.tokenInfo.token);
                return;
            }
        } catch (e) {}
        this.loggedIn = new BehaviorSubject<boolean>(false);
        this.user.next(null);
    }

    login(user: User) {
        console.log('Calling login server');
        if (user.email !== '' && user.password !== '') {
            return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.login}`, user)
                .pipe(map((data: AuthResponse) => {
                    if (data.success && data.tokenInfo) {
                        this.handleAuthSuccess(data);
                    }
                    return data;
                }));
        }
    }

    ssoCheck(email: string, is_login = false) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.ssoCheck}?login=${is_login}`, { email })
          .pipe(map((data: SSOCheckResponse) => {
              return data;
          }));
    }

    isShadowUser(user) {
        return ((user.email.toString().startsWith('aeddanberrynz+shadow') && user.email.toString().endsWith('@gmail.com')) ||
        (user.email.toString().startsWith('info+shadow') && user.email.toString().endsWith('@inndex.co.uk')))
    }

    private handleAuthSuccess(data: AuthResponse, isUserInteraction:boolean = true) {
        console.log('Log in success, id:', data && data.user && data.user.id);
        // store user details and jwt token in local storage to keep user logged in between page refreshes
        this.analyticsService.trackLogin(data.user)
        sessionStorage.setItem('user', JSON.stringify(data));
        this.tokenString.next(data.tokenInfo.token);
        if(isUserInteraction){
            this.loggedIn.next(true);
        }
        this.updateLocalUser(data.user, () => {
            if(isUserInteraction){
                this.handleAuthInteraction(data);
            }
        });
    }

    handleAuthInteraction(data: AuthResponse){
        if((data.user.roles || []).includes('SUPER_ADMIN')) {
            this.router.navigate(['/admin/home']);
        } else if(data.user.roles.includes('SITE_ADMIN') || data.user.roles.includes('COMPANY_ADMIN') || data.user.roles.includes('COMPANY_PROJECT_ADMIN')) {
            if(!data.user.user_onboard_status.employment) {
                this.router.navigate(['/site-user']);
                return;
            }

            this.loggedIn.next(false);
            let redirectToUrl = this.activatedRoute.snapshot.queryParams['redirectTo'];
            if(data?.reset_mfa === true && confirm('Click here to manage your MFA devices.') === true) {
                redirectToUrl = '/user-preferences';
            }
            if(!redirectToUrl){
                const modalRef = this.modalService.open(UseSiteAsComponent, {
                    backdropClass: 'light-blue-backdrop',
                    windowClass: 'use-site-as-modal',
                    backdrop: 'static',
                    keyboard: true,
                    centered: true,
                    size: 'sm'
                });
                modalRef.componentInstance.user = data.user;
                modalRef.result.then(() => {
                    this.loggedIn.next(true);
                }, () => {});
            }
            else {
                this.loggedIn.next(true);
                this.router.navigateByUrl(redirectToUrl);
            }
        } else {
            this.router.navigate(AppConstant.redirectAfterLogin);
        }
    }

    updateLocalUserPrederences(userPreference: UserPreferences, cb = () => {}) {
        this.userPreferences.next(userPreference);
        try{            
            let data: AuthResponse = JSON.parse(sessionStorage.getItem('user'));
            data.user_preferences = userPreference;
            sessionStorage.setItem('user', JSON.stringify(data));
            // this.refreshAuthToken();
            cb();
        }catch (e) {
            sessionStorage.setItem('user', '');
        }

    }

    updateLocalUser(user: User, cb = () => {}) {
        this.user.next(user);
        try{
            let data: AuthResponse = JSON.parse(sessionStorage.getItem('user'));
            data.user = user;
            sessionStorage.setItem('user', JSON.stringify(data));
            // this.refreshAuthToken();
            cb();
        }catch (e) {
            sessionStorage.setItem('user', '');
        }

    }

    register(user: User) {
        user.user_onboard_status = new DefaultOnBoardStatus();
        console.log('Register API called', user);
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.register}`, user)
            .pipe(map((data: AuthResponse) => {
                if (data.success && data.tokenInfo) {
                    this.handleAuthSuccess(data);
                }
                return data;
            }));
    }

    logout(justReset:boolean = false, liveTv = false) {
        this.analyticsService.logout();
        this.loggedIn.next(false);
        this.tokenString.next(null);
        this.user.next(null);
        this.userPreferences.next(null);
        sessionStorage.removeItem('user');
        sessionStorage.removeItem('active_ex');
        localStorage.clear();
        this.exclusionSetting.next({});
        this.cookieService.deleteCookie('path');
        
        if(this.refreshTimeout){
            clearTimeout(this.refreshTimeout);
        }
        if(!justReset){
            // console.log('Calling logout');
            this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.logout}`, {}).subscribe();
        }
        if (liveTv) {
            this.router.navigate(['/inndextv']);
        } else {
            this.router.navigate(['/login']);

        }

    }

    refreshAccessToken():Observable<any>{
        if(this.isLiveTvLogin()){
            return this.refreshLiveTvAccessToken();
        }
        // console.log('Refreshing');
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.refreshAccessToken}`, {}).pipe(map((data: AuthResponse) => {
            if (data.success && data.tokenInfo) {
                console.log('got new token after refreshing');
                this.handleAuthSuccess(data, false);
            }
            return data;
        }));
    }

    refreshLiveTvAccessToken():Observable<any>{
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.refreshLiveTvAccessToken}`, {}).pipe(map((data: AuthResponse) => {
            if (data.success && data.tokenInfo) {
                console.log('got new token after refreshing');
                this.handleLiveTvAuthSuccess(data, false);
            }
            return data;
        }));
    }


    getToken() {
        return `Bearer ${this.tokenString.getValue()}`;
    }

    getUser():Observable<any> {
        let user = JSON.parse(sessionStorage.getItem('user'));
        // todo in case if we face any user data state issue, we can disable below IF block
        if (0&&this.user.getValue()) {
            return this.authUser;
        } else {
            if (user && (user.project != undefined)) {
                return this.authUser;
            } else {
                let userPreference;
                // triggering call to fetch meta list.
                let userPreferencesRequest = this.userService.getUserSetting('user_preferences');
                forkJoin({
                    exclusion_list_response: this.getExclusionList(),
                    user_preferences_response: userPreferencesRequest
                }).subscribe((responseList:any) => {
                    let errorKey = Object.keys(responseList).find(key => !responseList[key].success);
                    if(responseList[errorKey]){
                        const message = responseList[errorKey].message || 'Failed to get data.';
                        this.toastService.show(this.toastService.types.ERROR, message, { data: responseList[errorKey] });
                        return ;
                    }

                    [userPreference] = responseList.user_preferences_response.user_settings;
                    this.userService.handleLocalePreferenceChange((userPreference || {})?.value);
                    if(userPreference?.value) {
                        this.updateLocalUserPrederences(userPreference.value)
                    } else {
                        console.error('Un Authenticated request')
                    }

                    this.authUser.subscribe(u => {
                        if(u && u.id){
                            this.deriveSetting();
                        }
                    })
                });
                return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.userProfile}`)
                    .pipe(map((data: any) => {
                        if (data.success && data.user) {
                            this.updateLocalUser(data.user);
                        } else {
                            console.error('Un Authenticated request')
                        }

                        return data.user;
                    }));
            }
        }
    }

    updateUser(user:User){
        console.log('Calling update user', user);
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.updateUser}`, user)
            .pipe(map((data: any) => {
                if (data.success && data.user) {
                    this.updateLocalUser(data.user);
                }
                return data;
            }));
    }

    verifyEmailByToken(user_id: any, token: any){
        console.log('Calling verify email by token', token);
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.verifyEmailToken(user_id, token)}`, {})
            .pipe(map((data: any) => {
                return data;
            }));
    }

    changeEmail(data:any) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.changeEmail}`, data);
    }

    changePassword(data:any){
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.changePassword}`, data)
            .pipe(map((data: any) => {
                return data;
            }));
    }

    forgotPassword(data:any){
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.forgotPassword}`, data);
    }

    resetPassword(data:any){
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.resetPassword}`, data);
    }

    getAllUser(): Observable<any> {

        return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.userProfile}${this.httpService.routes.allusers}`);

    }

    giveUserAdminAccess(id: any) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.adminAccess}`, id);
    }

    sendEmailVerificationMail(id: number) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.sendEmailVerificationMail(id)}`, {});
    }

    getNewAuthToken(tokenOverride = null, isUserInteraction = false){
        let headers = tokenOverride ? {
            Authorization: `Bearer ${tokenOverride}`,
            platform: 'web',
        } : {};
        return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.refreshToken}`, {headers})
            .pipe(catchError((error) => {
                console.error('Server went down?', error);
                this.logout();
                return [error];
            }), map((data: any) => {
                if (data.success && data.tokenInfo) {
                    this.handleAuthSuccess(data, isUserInteraction);
                } else {
                    console.error('Un Authenticated request');
                    this.logout();
                }

                return data.user;
            }));
    }

    private getDurationFromExpiry(defaultVal = 100){
        let duration = defaultVal;
        try {
            let user: AuthResponse = JSON.parse(sessionStorage.getItem('user'));
            // check if token exists & is not expired or near to expiry
            if (user && user.tokenInfo && user.tokenInfo.expiresOn) {
                // Keeping 10 min as buffer
                duration = ((user.tokenInfo.expiresOn * 1000) - (new Date().getTime())) - 600000;
                if(duration <= 0){
                    duration = defaultVal;
                }
            }
        }catch (e) {
            // force refresh on error
        }
        return duration;
    }
    private refreshTimeout;

    refreshAuthToken(){
        if(this.refreshTimeout){
            clearTimeout(this.refreshTimeout);
        }
        let tokenExpiryMs = this.getDurationFromExpiry();
        console.log('Refresh token after ', tokenExpiryMs);
        // need to derive tokenExpiryMs from local storage for supporting page reload as well.
        this.refreshTimeout = setTimeout(() => {
            this.getNewAuthToken().subscribe();
        }, tokenExpiryMs);
    }

    livetvlogin(user: any) {
        console.log('Calling livetv login server');
        if (user.record_id !== '' && user.pin !== '') {
            return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.livetvlogin}`, user)
                .pipe(map((data: any) => {
                    if (data.success && data.tokenInfo) {
                        this.handleLiveTvAuthSuccess(data);
                    }
                    return data;
                }));
        }
    }

    private handleLiveTvAuthSuccess(data: any, isUserInteraction:boolean = true, pi: boolean = false) {
        // store user details and jwt token in local storage to keep user logged in between page refreshes
        sessionStorage.setItem('user', JSON.stringify(data));
        this.tokenString.next(data.tokenInfo.token);
        if(isUserInteraction){
            this.loggedIn.next(true);
            if(data.project && data.project.id) {
                this.router.navigate(['/project-portal/project/'+ data.project.id +'/live-tv'], { queryParams: { full_screen: true, pi } });
            }
        }
    }

    getProjectDetailsByRestAuth(token: string, pi: boolean = false){
        return this.http.get<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.refreshAnonymousTokenInfo}`, {headers: {
                Authorization: `Bearer ${token}`,
                platform: 'web',
            }})
            .pipe(map((data: any) => {
                if (data.success && data.tokenInfo) {
                    this.handleLiveTvAuthSuccess(data, true, pi);
                }
                return data;
            }));
    }

    isLiveTvLogin() {
        let user = JSON.parse(sessionStorage.getItem('user'));
        if (user && user.project && user.project.id) {
            return true;
        }
        return false;
    }

    all_exclusion_setting: { [key: string]: {
            exclude?: string[];
            optional?: string[];
            mandatory?: string[];
        }
    } = {};

    getExclusionList() {
        return this.resourceService.getInnDexSettingByName('exclusion_by_country_code').pipe(map((data: any) => {
            if (data.success && data.record && data.record.value) {
                this.all_exclusion_setting = data.record.value;
                this.deriveSetting();
            } else {
                console.error('Something went wrong while fetching exclusion country_code');
                throw new Error(`exclusion list call got failed`);
            }
            return data;
        }));
    }

    deriveSetting(){
        let active_exclusion_setting = this.all_exclusion_setting || {};
        // console.log('setting', `country: ${this.user.value?.country_code}`, active_exclusion_setting)
        // console.log('all', this.all_exclusion_setting)
        this.exclusionSetting.next(active_exclusion_setting);
        sessionStorage.setItem('active_ex', JSON.stringify(active_exclusion_setting));
    }

    checkIfShouldBeShown(identifier: string, country_override?: string): boolean {
        const user = this.user.value;
        const countryCode = country_override || user?.country_code;
        const countryList = this.all_exclusion_setting[countryCode] || {};
        const globalList = this.all_exclusion_setting['ALL'] || {};

        // 1. Show if listed as mandatory/optional (country level)
        if (countryList?.mandatory?.includes(identifier) || countryList.optional?.includes(identifier)) {
            return true;
        }

        // 2. Show if listed as mandatory/optional (global level)
        if (!countryList?.exclude?.includes(identifier) && (globalList.mandatory?.includes(identifier) || globalList.optional?.includes(identifier))) {
            return true;
        }

        // 3. Hide if explicitly excluded (country level)
        if (countryList?.exclude?.includes(identifier) || globalList.exclude?.includes(identifier)) {
            return false;
        }

        return true;
    }


    checkIfShouldBeMandatory(identifier, country_override = null){
        let list = this.exclusionSetting.value[this.user.value?.country_code] || {};
        let globalList = this.exclusionSetting.value['ALL'] || {};

        if(country_override){
            list = this.all_exclusion_setting[country_override] || {};
            globalList = this.all_exclusion_setting['ALL'] || {};
        }

        const isMandatory = list?.mandatory && list.mandatory.includes(identifier);
        const isExcluded = list?.exclude && list.exclude.includes(identifier);
        const isOptional = list?.optional && list.optional.includes(identifier)
        const isGloballyMandatory = globalList?.mandatory && globalList.mandatory.includes(identifier);

        return isMandatory || ((!isExcluded && !isOptional) && isGloballyMandatory);
    }

    isProjectAdminV1(user, project) {
        let flag = (user.uac.project_admins_v1 && user.uac.project_admins_v1.includes(project.parent_company));
        console.log(`project_admins_v1 : ${flag}`, project.parent_company, user.uac.project_admins_v1);
        return flag;
    }

    checkIfShouldBeCreated(identifier: string, country_override = null){
        let countryList = this.exclusionSetting.value[AppConstant.defaultCountryCode] || {};
        let globalList = this.exclusionSetting.value['ALL'] || {};

        if(country_override){
            countryList = this.all_exclusion_setting[country_override] || {};
            globalList = this.all_exclusion_setting['ALL'] || {};
        }

        if(countryList?.visibility && countryList.visibility[identifier]) return countryList.visibility[identifier];
        if(globalList?.visibility && globalList.visibility[identifier]) return globalList.visibility[identifier];
        return {type:'postcode-lookup'};
    }
}

/**
 * Created by <PERSON><PERSON><PERSON> on 2021-05-13.
 */
import {Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {HttpService} from './http.service';
import { ToastService } from './toast.service';
import {saveAs} from "file-saver";

@Injectable()
export class QualityChecklistsService {
    constructor(
        private http: HttpClient,
        private toastService: ToastService,
        private httpService: HttpService
    ) {}

    createQChecklist(projectId:number, qclData) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.createQCL(projectId)}`, qclData);
    }

    createCompanyQChecklist(companyId: number, qclData) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.createCompanyQCL(companyId)}`, qclData);
    }

    updateQChecklist(projectId: number, qclId, qclData, params?) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.updateQCL(projectId, qclId)}`, qclData, { params });
    }
    updateCompanyQChecklist(companyId: number,  qclId, qclData, params?) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.updateCompanyQCL(companyId, qclId)}`, qclData, { params });
    }

    updateITPToCloseOutItem(itpId, projectId, closeoutData) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.updateITPToCloseOutItem(itpId, projectId)}`, closeoutData);
    }

    getProjectQChecklists(projectId: number, companyId: number, params?) {
        return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProjectQChecklists(projectId, companyId)}`,{params});
    }

    getCompanyQChecklists(companyId: number, params?) {
        return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getCompanyQChecklists(companyId)}`,{params});
    }

    getCLInspectionsByclId(qclId: number, projectId: number, params) {
        return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getCLInspectionsByclId(qclId, projectId)}`,{params});
    }

    getPartiallyCompletedCLinspectionsCountSA(qclId: number, projectId: number){
        return this.http.get<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getPartiallyCompletedCLinspectionsCountSA(qclId, projectId)}`);
    } 

    getPartiallyCompletedCLinspectionsCountCA(qclId: number, companyId: number){
        return this.http.get<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getPartiallyCompletedCLinspectionsCountCA(qclId, companyId)}`);
    }

    shareItpReport(req, id:number) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.shareItpReport(id)}`, req);
    }

    viewOrDownloadQclReport(id: number, qclId: number,  projectId: number, updatedAt: number, params = {}, type: string = 'html', cb = (data?) => {}, openWindow = true){
        let options: any = {params};

        if (type === 'pdf') {
            options.observe = 'response';
            options.responseType = 'blob' as 'json';
        } else {
            options.responseType = 'text';
        };
        this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.downloadQclReport(id, qclId, projectId, type, updatedAt )}`, options).subscribe(
            (response: any) => {
                if (type === 'pdf') {
                    let blob = new Blob([response.body as BlobPart], {type: response.headers.get('content-type')});
                    let contentDisposition = response.headers.get('content-disposition');
                    let responseContentType = (response.headers.get('content-type')) ? response.headers.get('content-type') :'application/pdf';
                    let filename = this.httpService.getFileName(contentDisposition, responseContentType);
                    saveAs(blob, filename || 'export.pdf');
                } else if(type === 'html' && openWindow) {
                    let newWindow = window.open('', '_blank', 'location1=no,height1=570,width1=520,scrollbars=yes,status=yes,toolbar=no');
                    if(newWindow === null || newWindow === undefined){
                        const message = 'Pop-ups are being blocked by your browser. To view the form please unblock pop-ups. To do this go to your browser settings and disable pop-up blocker.';
                        this.toastService.show(this.toastService.types.ERROR, message);
                    }else{
                        newWindow.document.body.innerHTML = response;
                    }
                }
                cb(response);
            },
            error => {
                const message = (error.headers && error.headers.get('message')) ? error.headers.get('message') : 'Failed to get induction form.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: error });
                cb(error);
            }
        );
    }

}

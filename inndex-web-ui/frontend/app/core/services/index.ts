import {HttpService} from './http.service';

import {AuthService} from "./auth.service";
import {CompanyTimeLogService} from "./ca-OR-cpa/company-admin.service";
import {CompanyService} from "./super-admin/company.service";
import {SkillMatrixService} from "./skill-matrix.service";
import {ProjectService} from "./project.service";
import {UserService} from "./user.service";
import {OptimaService} from "./optima.service";
import {ResourceService} from "./resource.service";
import {VisitorService} from "./visitor.service";
import {AdminService} from "./admin.service";
import {ReportService} from "./report.service";
import {CloseCallService} from "./close-call.service";
import {ProjectGateBookingService} from "./project-gate-booking.service";
import {Take5sService} from "./take5s.service";
import {ToolboxTalksService} from "./toolbox-talks.service";
import {ProgressPhotosService} from "./progress-photos.service";
import {DeliveryNotesService} from "./delivery-notes.service";
import {GeocodeService} from './geocode.service';
import {ProjectDailyActivitiesService} from "./project-daily-activities.service";
import {ClerkOfWorksService} from "./clerk-of-works.service";
import {ProjectPowraService} from "./project-powra.service";
import { SidebarUtilsService } from './sidebar-utils.service';
import {IncidentReportService} from "./incident-report.service";
import { ProjectInspectionTourService } from './project-inspection-tour.service';
import {ELearningModuleService} from "./e-learning-module.service";
import {GoodCallService} from "./good-call.service";
import {InductionQuestionsService} from "./induction-questions.service";
import {ProjectFeaturesService} from "./project-features.service";
import {ProcoreService} from "./procore.service";
import {ProjectAssetService} from "./project-asset.service";
import {WorkPackagePlansService} from './work-package-plans.service';
import {ProjectRamsService} from './project-rams.service';
import {QualityChecklistsService} from "./quality-checklist.service";
import {InspectionBuilderService} from "./inspection-builder.service";
import {SiteMessagingService} from "./site-messaging.service";
import {CompanyDivisionService} from "./company-divisions.service";
import {GoogleAnalyticsService} from "./google-analytics.service";
import { BlogArticlesService } from "./blog-articles.service";
import {RollCallService} from "./rollcall.service";
import { ConductCardsService } from './conduct-cards.service';
import { DoNotAskAgainService } from './do-not-ask-again.service';
import {PermitService} from "./permit.service";
import {CompanyAssetConfigService} from "./company-asset-config.service";
import {PowerbiService} from "./powerbi.service";
import { DropdownTreeviewSelectI18n, ProjectIntegrationsService } from "./project-integration.service"
import { FileService } from './file.service';
import {TranslationService} from "./translation.service";
import {CookieService} from "./cookie.service";
import {ToastService} from "./toast.service";
import { DocumentPermissionsService } from './document-permissions.service';
import {FormDirtyService} from './form-dirty/form-dirty.service';

export const services = [
    HttpService,
    AuthService,
    CompanyTimeLogService,
    CompanyService,
    SkillMatrixService,
    ProjectService,
    UserService,
    FileService,
    OptimaService,
    ResourceService,
    VisitorService,
    AdminService,
    ReportService,
    CloseCallService,
    ProjectGateBookingService,
    Take5sService,
    ToolboxTalksService,
    ProgressPhotosService,
    DeliveryNotesService,
    GeocodeService,
    ProjectDailyActivitiesService,
    ClerkOfWorksService,
    ProjectPowraService,
    SidebarUtilsService,
    IncidentReportService,
    ProjectInspectionTourService,
    ELearningModuleService,
    GoodCallService,
    InductionQuestionsService,
    ProjectFeaturesService,
    ProcoreService,
    ProjectAssetService,
    WorkPackagePlansService,
    QualityChecklistsService,
    InspectionBuilderService,
    SiteMessagingService,
    CompanyDivisionService,
    ProjectRamsService,
    GoogleAnalyticsService,
    RollCallService,
    ConductCardsService,
    PermitService,
    DoNotAskAgainService,
    CompanyAssetConfigService,
    PowerbiService,
    DropdownTreeviewSelectI18n,
    ProjectIntegrationsService,
    TranslationService,
    CookieService,
    ToastService,
    BlogArticlesService,
    DocumentPermissionsService,
    FormDirtyService
];

export * from './http.service';
export * from './auth.service';
export * from './ca-OR-cpa/company-admin.service';
export * from './super-admin/company.service';
export * from './project.service';
export * from './skill-matrix.service';
export * from './user.service';
export * from './optima.service';
export * from './resource.service';
export * from './visitor.service';
export * from './admin.service';
export * from './report.service';
export * from './close-call.service';
export * from './project-gate-booking.service';
export * from './take5s.service';
export * from './toolbox-talks.service';
export * from './progress-photos.service';
export * from './delivery-notes.service';
export * from './geocode.service';
export * from './project-daily-activities.service';
export * from './clerk-of-works.service';
export * from './project-powra.service';
export * from './sidebar-utils.service';
export * from './incident-report.service';
export * from './project-inspection-tour.service';
export * from "./e-learning-module.service";
export * from "./good-call.service";
export * from "./induction-questions.service";
export * from "./project-features.service";
export * from "./procore.service";
export * from "./project-asset.service";
export * from './work-package-plans.service';
export * from './quality-checklist.service';
export * from './inspection-builder.service';
export * from './site-messaging.service';
export * from "./company-divisions.service";
export * from './project-rams.service';
export * from './google-analytics.service';
export * from './rollcall.service';
export * from './conduct-cards.service';
export * from './permit.service';
export * from './company-asset-config.service';
export * from './do-not-ask-again.service';
export * from './powerbi.service';
export * from './project-integration.service';
export * from './file.service';
export * from './translation.service';
export * from './cookie.service';
export * from './toast.service';
export * from './file.service';
export * from './blog-articles.service'
export * from './document-permissions.service';
export * from './form-dirty/form-dirty.service';

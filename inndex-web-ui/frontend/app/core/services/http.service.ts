import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {throwError} from 'rxjs';
import {AppConstant} from "@env/environment";
import {config} from '../app-config';
import {saveAs} from "file-saver";
import { ToastService } from './toast.service';

@Injectable()
export class HttpService {

    procoreRoutes: any = {
        authorizeRedirect: (projectId, userId) => `${AppConstant.apiServerUrl}/procore/authorize/${projectId}/${userId}?_=${(new Date()).getTime()}`,
        getUserCompaniesList: (projectId) => `procore/${projectId}/companies-list`,
        getCompanyProjectsList: (projectId, proCoreCompanyId) => `procore/${projectId}/company/${proCoreCompanyId}/project-list`,
        saveProcoreProjectReference: (projectId) => `procore/${projectId}/link-project`,
        createUsersTimecardEntries: (projectId) => `procore/${projectId}/export-time-entries`,
        getProjectPermissionTemplates: (projectId) => `procore/${projectId}/permission-templates`,
    };

    asiteRoutes: any = {
        checkAsiteConfigStatus: (companyId, projectId) => `company/${companyId}/project/${projectId}/a-site/check-config-status`,
        getAsiteWorkspaces: (companyId, projectId) => `company/${companyId}/project/${projectId}/a-site/workspaces`,
        getAsiteFoldersList: (companyId, projectId, workspaceId) => `company/${companyId}/project/${projectId}/a-site/workspace/${workspaceId}/folders`,
    };

    saRoutes: any = {
        getSAProjectInductions: (projectId) => `sa/project/${projectId}/inductions/list`,
        getActiveUsersTimesheetList: (projectId) => `sa/project/${projectId}/active/timesheet/list`,
        getUserTimesheetDetails: (projectId, userId) => `sa/project/${projectId}/timesheet/${userId}/fetch`,
        approveUserTimesheets: (projectId) => `sa/project/${projectId}/timesheets/status/update`,
        bulkSaveTimesheetInfo: (projectId) => `sa/project/${projectId}/timesheets/save`,
        saveTimesheetComment: (projectId) => `sa/project/${projectId}/timesheet/comment`,
        downloadTimesheets: (projectId) => `sa/project/${projectId}/timesheet/download/xls`,
        getInductionEmployers: (projectId) => `sa/project/${projectId}/inductions/employers`,
        getInductionJobRoles: (projectId) => `sa/project/${projectId}/inductions/job-roles`,
        getRecentEvents: (projectId, type = 'unrecognized') => `sa/project/${projectId}/optima/events/${type}/list`,
        checkDuplicateFaceIntoFR: (projectId, inductionRequestId) => `sa/project/${projectId}/induction/${inductionRequestId}/facial-recognition/check-duplicate`,
        enrolInductionIntoFR: (projectId, inductionRequestId) => `sa/project/${projectId}/induction/${inductionRequestId}/facial-recognition/index-face`,
        switchInductionBadgeWith: (projectId, inductionRequestId, badgeNumber) => `sa/project/${projectId}/induction/${inductionRequestId}/optima/switch-badge/${badgeNumber}`,
        fetchProjectPermitTemplates: (projectId) => `sa/project/${projectId}/permit/templates`,
        getProjectInductionById: (projectId, inductionRequestId) => `sa/project/${projectId}/induction/${inductionRequestId}/detail`,
        saveProjectPermitConfig: (projectId) => `sa/project/${projectId}/permit-config/save`,
        getProjectPermitConfig: (projectId, id) => `sa/project/${projectId}/permit-config/${id}`,
        getPermitRequestsByProject: (projectId) => `project/${projectId}/permit-request/list`,
        viewPermitDocument: (projectId, id) => `sa/project/${projectId}/permit-request/${id}/view`,
        getPermitRequestById: (projectId, id) => `project/${projectId}/permit-request/${id}`,
        downloadPermitRequests: (projectId) => `project/${projectId}/permit-requests/download`,
        getProjectToolReport: (projectId, toolName) => `sa/project/${projectId}/tool-report/${toolName}`,
        exportProjectToolReport: (projectId, toolName) => `sa/project/${projectId}/tool-report/download/${toolName}`,
        updateProjectExportQueue: (projectId, id) => `sa/project/${projectId}/report/job/${id}/update`,
        sharePermit: (projectId, id) => `project/${projectId}/permit-request/${id}/share`,
        saveWorkerRecord: (projectId) => `sa/project/${projectId}/timesheet/worker-record`,
    };

    caRoutes: any = {
        getInductionEmployers: (employerId, projectId) => `ca/${employerId}/project/${projectId}/inductions/employers`,
        getCompanyInductedUsers: (companyId) => `ca/${companyId}/projects/users/list`,
        getInductedUsersTimeLogs: (companyId, userId) => `ca/${companyId}/user/${userId}/time-logs`,
        getCompanyUserContactDetail: (companyId, userId) => `company/${companyId}/user/contact-detail/${userId}`,
        getCompanyUserHealthAssessment: (companyId, userId) => `company/${companyId}/user/health-assessment/${userId}`,
        getCompanyUserMedicalAssessment: (companyId, userId) => `company/${companyId}/user/medical-assessment/${userId}`,
        getCompanyUserDocuments: (companyId, userId) => `company/${companyId}/user/documents/${userId}`,
        saveSkillMatrixRecord: (companyId) => `ca/${companyId}/skills-matrix/save`,
        companySkillsToAdd: (companyId) => `company/${companyId}/skills/remaining`,
        deleteSkillMatrixRecordById: (companyId, ruleId) => `ca/${companyId}/skills-matrix/${ruleId}/delete`,
        getCompanyInductionsList: (companyId) => `ca/${companyId}/company-inductions/list`,
        getCompanyInductionById: (companyId, ciId) => `ca/${companyId}/company-induction/${ciId}`,
        updateCompanyInductionStatus: (companyId, ciId) => `ca/${companyId}/company-induction/${ciId}/update-status`,
        createPermitTemplate: (companyId) => `ca/${companyId}/permit-template/create`,
        updatePermitTemplate: (companyId, id) => `ca/${companyId}/permit-template/update/${id}`,
        updatePermitTemplateStatus: (companyId, id) => `ca/${companyId}/permit-template/update/${id}/status`,
        fetchPermitTemplates: (companyId) => `ca/${companyId}/permit-template/list`,
        getCompanyToolReport: (companyId, toolName) => `ca/${companyId}/tool-report/${toolName}`,
        exportCompanyToolReport: (companyId, toolName) => `ca/company/${companyId}/tool-report/download/${toolName}`,
        updateCompanyExportQueue: (companyId, id) => `ca/company/${companyId}/report/job/${id}/update`,
    };

    superAdminRoutes: any = {
        exportCompanyProjectStats: (companyId) => `admin/${companyId}/projects/stats`,
    };

    public routes: any = {
        login: 'user/login',
        register: 'user/register',
        logout: 'user/logout',
        ssoCheck: 'user/sso-check',
        refreshAccessToken: 'user/refresh-token',
        refreshLiveTvAccessToken: 'inn-time/refresh-token',
        updateUser: 'user/update',
        userProfile: 'user',
        refreshToken: 'user/token',
        createShadowUser: 'shadow-user/create',
        saveShadowUserProfileDetails: shadowUserId => `shadow-user/${shadowUserId}/save-details`,
        adminAccess: 'user/allow-for-admin-access',
        changePassword: 'user/change-password',
        changeEmail:'user/change-email',
        forgotPassword: 'user/forgot-password',
        resetPassword: 'user/reset-password',
        verifyEmailToken: (id, token) => `auth/verify-email-token/${id}/${token}`,
        downloadCompanyEmployeeInfo: (employeeId) => `employee-detail/download/${employeeId}`,
        getCompanyProjectAdmins: (companyId, projectId) => `company/${companyId}/project/${projectId}/admins`,
        saveCompanyProjectAdminPermission: (companyId, projectId) => `company/${companyId}/project/${projectId}/admin`,
        updateCompanyProjectAdminPermission: (companyId, projectId) => `company/${companyId}/project/${projectId}/admin/update`,
        removeUserCompanyProjectAccess: (companyId, projectId, userId) => `company/${companyId}/project/${projectId}/admin/remove/${userId}`,
        getUserContactDetail: (userId) => `user/contact-detail${userId ? "/" + userId : ''}`,
        createContactDetail: 'user/contact-detail',
        updateContactDetail: id => `user/contact-detail/${id}`,
        getUserSetting: name => `user/get-settings/${name}`,
        saveUserSetting: `user/store-setting`,
        getMfaSecureKey: `auth/user/mfa/generate-secret`,
        activateMfaDevice: `auth/user/mfa/activate-device`,

        getProjectAdmins: (projectId) => `project/${projectId}/admins`,
        getCompanyAdmins: (companyId) => `company/${companyId}/admins`,
        getCompanyTimeZone: (employerId) => `employer/${employerId}/timezone`,

        getHealthAssessmentQuestions: 'data/health-assessment-questions',
        getUserHealthAssessment: (userId) => `user/health-assessment${userId ? "/" + userId : ''}`,
        saveUserHealthAssessment: 'user/health-assessment',

        getMedicalAssessmentQuestions: 'data/medical-assessment-questions',
        getUserMedicalAssessment: (userId) => `user/medical-assessment${userId ? "/" + userId : ''}`,
        saveUserMedicalAssessment: 'user/medical-assessment',
        addSignDocument: `user/document-sign/add`,
        resendSignDocument: (documentId) => `user/document-sign/resend/${documentId}`,
        getSignDocuments: (userId, documentId) => `user/document-sign/get/${userId}${documentId ? "/" + documentId : ''}`,
        getUserEmploymentDetail: 'user/employment-detail',
        getUserEmploymentDetailById: (userId) => `user/${userId}/employment-detail`,
        getUserTimeDetails: (companyId, userId) => `user/${userId}/time-details/${companyId}`,
        saveUserEmploymentDetail: 'user/employment-detail',
        saveUserEmploymentDetailV2: 'v2/user/employment/update',

        getCompanyProjectsForUser: (employerId, userId) => `employer/${employerId}/projects/${userId}`,
        getEntitiesCountByCompany: employerId => `company/entities/count/${employerId}`,
        mergeCompany: `company/merge`,

        allusers: '/allusers',
        sendEmailVerificationMail: userId => `user/send-verification-mail/${userId}`,
        getUsersById: 'user/users-detail',
        deleteFile: 'delete-file',
        downloadFile: 'download-file',
        mergePDFs: 'merge-pdfs',
        convertWordDocsToPdf: 'convert/worddoc-to-pdf',
        getMyDocuments: (userId) => `user/documents${userId ? "/" + userId : ''}`,
        saveUserDoc: 'user/documents',
        deleteUserDoc: 'user/documents/delete',
        getAdditionalOwnedDocuments: docOwnerId => `user/additional-documents/${docOwnerId}`,
        searchCitbAchievement: 'user/citb/search-doc',
        lookupCscsInfo: (companyId) => `company/${companyId}/cscs/documents-info`,
        autoVerifyUserDocuments: (companyId) => `company/${companyId}/cscs/auto-verify`,
        updateDocument: userDocId => `user/documents/${userDocId}/update`,
        lookupRtwInfo: (companyId) => `company/${companyId}/rtw/check-status`,
        updateInductionWithNewCompetencies: () => `user/induction-request/update/documents`,

        getUserProjects: 'projects',
        createProject: 'projects/add',
        validateProjectPostcode: `project/validate-postcode`,
        checkCompanyProjectStatus: companyId => `company/${companyId}/projects/status`,
        getProject: (id) => `projects/${id}`,
        siteAdmin_getProject: (id) => `sa/projects/${id}`,
        updateProject: (id) => `projects/${id}/update`,
        getProjectInductionBookingSetting: (id) => `project/${id}/setting/induction-booking`,
        saveProjectInductionBookingSetting: (id) => `project/${id}/setting/induction-booking`,
        getProjectInductionBookingSlots: (id) => `project/${id}/induction-booking/slots`,
        updateProjectStatus: (id) => `projects/${id}/updatestatus`,
        updateProjectLiveTvFooter: (id) => `projects/${id}/update-ltv-footer`,
        getProjectInductionRequests: (id) => `projects/${id}/induction-requests`,
        getProjectInductions: (id) => `projects/${id}/inductions`,
        getCAProjectInductionRequests: (employerId, projectId) => `ca/${employerId}/projects/${projectId}/induction-requests`,
        getCAProjectInductions: (employerId, projectId) => `ca/${employerId}/project/${projectId}/inductions/list`,
        searchProjectInductedUsers: (projectId) => `v2/project/${projectId}/inductions/search-user`,
        getProjectInductedUsersNames: (projectId) => `v2/project/${projectId}/inductions/all`,
        getProjectInductedUsersNamesCA: (employerId, projectId) => `v2/ca/${employerId}/project/${projectId}/inductions/all`,
        updateProjectPartially: (id) => `projects/${id}/update-project-partially`,
        deleteProject: (id, deleteFlag) => `project/${id}/delete${deleteFlag ? "?delete=yes" : ''}`,
        addUpdateGateSupervisors: (projectId) => `project/${projectId}/gate-supervisors/update`,
        getGateSupervisors: (projectId) => `project/${projectId}/gate-supervisors`,
        getOnSiteEntitiesOfProjectWithLocation: (projectId, nowMs) => `projects/${projectId}/entities/on-site/${nowMs}/with-location`,
        getProjectInductedUsers: (projectId) => `project/${projectId}/inducted-users`,
        getProjectInductedUsersCA: (employerId, projectId) => `ca/${employerId}/project/${projectId}/inducted-users`,
        getProjectInductedAdmins: (projectId) => `projects/${projectId}/inducted/admins`,
        getClockedInUsersOfProject: (projectId) => `project/${projectId}/clocked-in-users`,
        saveProjectResourcePlans: (projectId) => `project/${projectId}/resource-plans`,
        bulkUploadProjectResourcePlans: (projectId) => `project/${projectId}/resource-plans/upload/csv`,
        downloadResourcePlannerExport: (projectId) => `project/${projectId}/resource-plans/download`,
        getProjectPlannedResources: (projectId) => `project/${projectId}/planned-resources`,
        updateProjectResourcePlanById: (projectId, recordId) => `project/${projectId}/planned-resources/update/${recordId}`,
        deleteProjectResourcePlanById: (projectId, recordId) => `project/${projectId}/planned-resources/delete/${recordId}`,
        saveProjectSetting: (projectId) => `project/${projectId}/setting/save`,

        getFormTemplates: 'form-templates',
        // @deprecated
        //getSiteHealthAssessmentQuestions: 'user/health-assessment-ques',
        getInducteeTrainingsByProject:(projectId) =>`report/${projectId}/project-induction-documents`,
        getCompanySkillMatrixList: (companyId) => `company/${companyId}/skills-matrix/list`,
        companySkillsList: (companyId) => `company/${companyId}/skills/list`,
        getMyInductionRequests: 'user/inductions',
        createInductionRequest: 'user/induction-request',
        updateInductionRequest: (id, origin) => `user/induction-request/${id}${origin ? "/" + origin : ''}`,
        updateInductionToBlacklistUser: (projectId, id) => `project/${projectId}/induction-request/${id}/blacklist`,
        getMyInductionRequest: (id) => `user/induction-request/${id}`,
        getInductionMedications: (projectId, id) => `project/${projectId}/induction/${id}/medications`,
        updateInductionMedications: (projectId, id) => `project/${projectId}/induction/${id}/medications`,
        updateUserEmployment: (projectId, id, createdAt) => `project/${projectId}/induction/${id}/${createdAt}/update/user-employment`,
        viewOrDownloadInduction: (id, type, createdAt) => `induction-request/${id}/${type}/${createdAt}`,
        overrideInductionTravelTime: (projectId, id) => `${projectId}/induction-request/${id}/over-ride/travel-time`,
        getProjectInductionsUsersEmployer: (projectId, status_code) => `project/${projectId}/induction-requests/${status_code}/user-employers`,
        downloadInductionRecords: (projectId) => `project/${projectId}/induction-requests/download`,
        downloadInductionRecordsXLSX: (projectId) => `project/${projectId}/induction-requests/exportXLSX`,
        downloadInductionRecordsXlsxCA: (companyId, projectId) => `ca/${companyId}/project/${projectId}/induction-requests/exportXLSX`,
        downloadCompanyInductionsXLSX: (employerId) => `company/${employerId}/inductions/exportXLSX`,
        downloadQRPoster: (companyId, projectId) => `company/${companyId}/project/${projectId}/qr-poster/download`,

        searchProjectsForInduction: 'user/search-projects',
        searchAllProjects: 'project/search-all-projects',
        createSupportRequest: 'support-ticket',
        getVisitorsOfProject: (projectId) => `project/${projectId}/visitors`,
        getProjectVisitorEvents: (projectId) => `project/${projectId}/visitors/events`,
        getProjectVisitorLogDetail: (projectId, visitorId, eventLogId) => `project/${projectId}/visitor/${visitorId}/detail/${eventLogId}`,
        getVisitorTimeLogs: (projectId, employerId) => `visitor/time-logs/${projectId}${employerId ? '/' + employerId : ''}`,
        getVisitorTimeLogsByDay: (projectId, employerId) => `visitor/time-logs/by-day/${projectId}${employerId ? '/' + employerId : ''}`,
        getOnSiteVisitorOfProjectAlias: (projectId, nowMs) => `project/${projectId}/visitors/on-site/${nowMs}`,
        searchVisitorByEmailAlias: (projectId) => `project/${projectId}/visitors/search-by-email`,
        getTotalSiteTimeReport: projectId => `${projectId}/report/total-time`,
        exportTotalTimeReportOfProjectForAll: projectId => `project/${projectId}/report/total-time`,
        getProjectCarbonEmissionsReportXLSX: (projectId) => `project/${projectId}/report/emissions-report`,
        storeVisitorInfo: (projectId) => `project/${projectId}/visitor/store`,
        updateVisitorInfo: (visitorId, projectId) => `sa/project/${projectId}/visitor/${visitorId}/update`,

        getBioMetricSetting: projectId => `${projectId}/bio-metric/setting`,
        getProjectTbEvents: projectId => `${projectId}/touch-byte/events`,
        getTBUserById: (projectId, userId) => `${projectId}/touch-byte/user/${userId}`,
        grantUserTBSiteAccess: (projectId, userId) => `${projectId}/touch-byte/user/${userId}/allow-site`,
        revokeUserSiteAccess: (projectId, userId) => `${projectId}/touch-byte/user/${userId}/revoke-site`,
        // getProjectOptimaSetting: projectId => `optima/${projectId}`,
        // getOptimaMeta: projectId => `optima/${projectId}/meta`,
        createOptimaBadge: (projectId, inductionRequestId) => `optima/${projectId}/add-badge/${inductionRequestId}`,
        reGenerateBadges: (projectId) => `optima/re-generate-badge/${projectId}`,
        getAllOptimaSetting: 'optima/all',
        checkOptimaStatus: (projectId) => `optima/status/${projectId}`,
        getOptimaSettingByProjectId: projectId => `optima/${projectId}/get-optima`,
        addUpdateOptimaSetting: projectId => `project/${projectId}/optima/add-or-update`,
        removeOptimaSetting: 'optima/remove-setting',
        testBiometricConnection: projectId => `biometric/test-connection/${projectId}`,
        addManualGeoFenceEntries: projectId => `project/${projectId}/inn-time-log/add-manual-entries`,
        getProjectMembers: (projectId, employerId) => `v3/project/${projectId}/members${employerId ? '/' + employerId : ''}`,
        getProjectOnSiteUsersAccessPoints: (projectId, employerId) => `project/${projectId}/on-site/members/locations${employerId ? "?employerId=" + employerId : ''}`,
        getProjectMemberDailyEvents: (projectId, employerId) => `v3/project/${projectId}/member/all-events${employerId ? '/' + employerId : ''}`,
        getProjectMemberTimeData: projectId => `optima/${projectId}/members/events`,
        getProjectMemberDataForDays: projectId => `optima/${projectId}/members/events-by-days`,
        updateBadgeEventById: (projectId, badgeEventId) => `badge-event/${projectId}/update/${badgeEventId}`,
        updateGeoFenceEventById: (projectId, userTimeLogId) => `geo-fence/${projectId}/update/${userTimeLogId}`,
        updateUserDailyLog: (projectId, userId) => `user-time-log/${projectId}/update/${userId}`,
        getUserShiftConfigs: (projectId) => `user/working-shift/${projectId}`,
        createUserShiftConfig: projectId => `user/${projectId}/working-shift`,
        deleteUserShiftConfig: (projectId, configId) => `user/${projectId}/working-shift/${configId}/delete`,
        getProjectGeoFenceEvents: projectId => `geo-fence/${projectId}/events`,
        getClockingDeclarationAnswers: (projectId, userId) => `geo-fence/${projectId}/declaration-events/${userId}`,
        getProjectBadgeEvents: projectId => `optima/${projectId}/events`,
        downloadBadgeEvents: projectId => `optima/${projectId}/events/download`,
        getProjectUsersGeoFenceTimeLogs: (employerId, projectId) => `geo-fence/${employerId}/time-logs/${projectId}`,
        getProjectUserGeoFenceLog: (projectId, userId, interactionType, event_log_id?) => `geo-fence/${projectId}/time-log/${userId}/${interactionType}${event_log_id ? `/${event_log_id}` : ''}`,
        getCompanyProjectTimeLogs: (employerId, projectId) => `company/${employerId}/time-log/${projectId}`,
        deleteProjectTimeLogById: (projectId, eventLogId) => `project/${projectId}/time-log/${eventLogId}/delete`,
        getVehicleTimeLogsByDay: (projectId) => `vehicles/time-logs/by-day/${projectId}`,
        getVehicleLogsByID: (projectId) => `vehicles/time-logs/detail/${projectId}`,
        downloadVehicleDailyLogsExport: (projectId) => `project/${projectId}/vehicles/time-logs/download`,
        updateVehicleAndLogs: (projectId, vehicleId) => `project/${projectId}/vehicle/${vehicleId}/log/update`,
        getDistanceBwPostcodes: () => `postcode/distance-matrix`,
        addEmployer: 'employer/add',
        editEmployer: employerId => `employer/${employerId}/edit`,
        getEmployer:'employer/list',
        companiesList:'v2/employer/list',
        getUserCompanies:'user/companies',
        getallProjects: 'all/projects',
        getAccessLogs: 'access-logs',
        getInteractionLogs: 'interaction-logs',
        deleteEmployer: 'employer/delete',
        deleteUserFromEmployer: (companyId, companyUserId) => `employer/${companyId}/delete-user/${companyUserId}`,
        deleteEmployerUser: (companyId, uacId) => `employer/${companyId}/delete-user/${uacId}`,
        getCountries: 'countries/all',
        getInnDexSettingByName: 'inndex-setting/fetch',
        getInnDexSettingsByName: 'inndex-setting/fetch/batch',
        fetchVehicleRegDetails: registrationNumber => `inndex/search-vehicle/${registrationNumber}`,
        addJobRole: 'job-role/add',
        getJobRoles:'job-role/list',
        deleteJobRole: 'job-role/delete',
        getOptimaBadgeDetail: projectId => `optima/${projectId}/badgedetail`,
        addCompetency: `competency/add`,
        getCompetencies: `competency/getlist`,
        deleteCompetency: competencyId => `competency/${competencyId}/delete`,
        addWork: `typeofwork/add`,
        getTypeOfWorks: `typeofwork/getlist`,
        getTimezones: `timezones/list`,
        deleteWork: workId => `typeofwork/${workId}/delete`,
        // sendInviteToInductionAlert: 'user/invite-to-induction', // @deprecated: Satyam: introduced new below `inviteToInductAlert`.
        inviteToInductAlert: (projectId) => `user/invite-to-induct/${projectId}`,
        downloadInvitesSent: (projectId) => `report/induction-invites/${projectId}`,
        exportDailyDeclarationReportOfUser: (projectId, userId) => `report/daily-declaration/${projectId}/${userId}`,
        uploadQRImage: 'indexsetting/upload_qrcode',
        getQRImage: 'indexsetting/get_qrcode',
        getUserInfo: `user/search-by-email`,
        deactiveUserAccount: userId => `user/deactive/${userId}`,
        deleteUserAccount: userId => `user/delete/${userId}`,
        saveCompanyDivisions: 'company/company-division/save',
        deleteCompanyDivision: (divisionId) => `company/company-division/${divisionId}/delete`,
        getToolList: 'tools/get-list',
        downloadProjectRollCallReport: (projectId) => `project/${projectId}/roll-call/download`,
        downloadRandomOnSiteOperativesReport: (projectId, employerId) => `sa/project/${projectId}/roll-call/random/download${employerId ? "?employerId=" + employerId : ''}`,

        getProjectRollCallRecords: (projectId) => `sa/roll-call/${projectId}/list`,
        downloadRollCall: (projectId, rollCallId) => `sa/roll-call/${projectId}/${rollCallId}/download`,

        getKpiDashboardData: projectId => `report/project/${projectId}/kpi-dashboard`,
        getCompanyProjectTimeSheetByWeek: (companyId) => `company/${companyId}/report/company-project/time-sheet`,
        getProjectCloseCalls: (projectId, companyId) => `project-close-call/${projectId}${companyId ? '/' + companyId : ''}`,
        getProjectCloseCallsById: (projectId, ccId) =>  `project/${projectId}/close-call/${ccId}`,
        getProjectTake5s: (userId, projectId) => `user-take5s/${userId}/${projectId}`,
        downloadTake5: t5Id => `v2/take5-view/${t5Id}`,
        downloadTake5XlSX: projectId => `v2/take5-view/${projectId}/xlsx`,
        addCloseCall: () => `closecall/add`,
        updateCloseCall: (projectId, ccId) => `project/${projectId}/closecall/${ccId}/update`,
        downloadCloseCall: (ccId) =>  `closecall/download/${ccId}`,
        downloadCloseCallReport: (projectId) => `project/${projectId}/close-call/report/download`,
        getProjectUtils: (projectId) => `project/${projectId}/close-call/filters`,
        createBooking: (projectId) => `project/${projectId}/gate-booking/add`,
        updateBooking: (projectId, id) => `project/${projectId}/gate-booking/${id}/update`,
        getBookings: 'project-gate-booking',
        getGateByProject: (id) => `project/${id}/project-gates`,
        deleteBooking: (projectId, bookingId) => `project/${projectId}/gate-booking/${bookingId}/delete`,
        updateBookingStatus: (projectId, bookingId) => `project/${projectId}/gate-booking/status/${bookingId}`,
        updateMultipleBookingsStatus: projectId => `project-gate-booking/${projectId}/status-update`,
        updateProjectGate: (projectId, gateId) => `project-gate/${projectId}/update/${gateId}`,
        exportBookings: (projectId) => `project-gate/${projectId}/bookings/export`,
        exportBookingsReportPDF: (projectId) => `project-gate-booking/${projectId}/bookings/pdfexport`,
        getProjectToolboxTalks: (projectId) => `project-toolbox-talks/${projectId}`,
        getProjectBriefingToolRecordsList: (projectId, toolKey) => `v2/tool-briefing/${toolKey}/${projectId}/tool-records`,
        getProjectBriefingToolRecord: (projectId, toolKey, recordId) => `v2/tool-briefing/${toolKey}/${projectId}/tool-record/${recordId}`,
        getBriefingToolRecordForInvite: (toolKey, projectId) => `tool-briefing/${toolKey}/available-briefing-tools/${projectId}`,
        getCompanyToolboxTalks: (employerId) => `company-toolbox-talks/${employerId}`,
        createToolboxTalk: `toolbox-talk/add`,
        addObservation: (projectId) => `project/${projectId}/good-call/add?data_type=observation`,
        addGoodCall: (projectId) => `project/${projectId}/good-call/add?data_type=good-call`,
        updateToolboxTalk: tbTalkId => `toolbox-talk/${tbTalkId}/update/`,
        downloadToolboxTalk: tbTalkId => `v2/toolbox-talk-view/${tbTalkId}`,
        inviteToToolboxTalk: projectId => `toolbox-talk/${projectId}/invite`,
        inviteToToolboxTalkForCompanyTBTs: companyId => `toolbox-talk/${companyId}/invite-for-company`,
        downloadToolboxTalkXLSX: (id, projectId) => `toolbox-talk/${projectId}/id/${id}/download`,
        downloadToolboxTalkRegister: (projectId) => `project/${projectId}/toolbox-talk/register/download`,
        getCompanyTBTForInvite: (employerId) => `company-available-toolbox-talks/${employerId}`,
        getUserDocumentsByEmployer: (employerId) => `user/${employerId}/users-documents-by-employer`,
        getUsersByEmployer: (employerId) => `user/${employerId}/users-by-employer`,
        getBlackListedUserByCompany: (employerId) => `company/${employerId}/users/blacklisted`,
        getEmployerById: (employerId) => `employer/${employerId}`,
        saveNewCompanyUsers: employerId => `employer/${employerId}/save-company-user`,
        getCompanyProjects: (employerId, include_disabled) => `employer/${employerId}/projects?include_disabled=${include_disabled}`,
        getUserCurrentProject: (userId) => `user/project${userId ? "/" + userId : ''}`,
        getEmployerLogoByName: (employerName) => `employer/logo/${employerName}`,
        getEmployersLogo: 'employer/logo-list',
        getProgressPhotos: (projectId, companyId) => `project-progress-photos/${projectId}${companyId ? '/' + companyId : ''}`,
        addProgressPhotos: `progress-photos/add`,
        updateProgressPhotos: (ppId) => `progress-photos/${ppId}/update`,
        addProgressPhotosAlbum: (projectId) => `progress-photos/${projectId}/album/add`,
        updateProgressPhotosAlbum: (projectId, id) => `progress-photos/${projectId}/album/${id}/update`,
        deleteProgressPhotosAlbum: (projectId, id) => `progress-photos/${projectId}/album/${id}/delete`,
        getProgressPhotosAlbum: (projectId) => `progress-photos/${projectId}/album/list`,
        savePhotosAlbumsUserPreference: (projectId) => `progress-photos/${projectId}/album/user-preference`,
        livetvlogin: 'authorize-project',
        refreshAnonymousTokenInfo: 'inn-time/refresh/token-info',
        getLiveTvKpiDashboardData: projectId => `report/project/${projectId}/livetv-kpi-dashboard`,
        getLiveTvTotalSiteTimeReport: projectId => `optima/${projectId}/report/livetv-total-time`,
        updateDeliveryNotes: (projectId, id) => `project/${projectId}/delivery-notes/${id}/update`,
        addDeliveryNotes: `delivery-notes/add?data_type=delivery-note`,
        addCollectionNotes: `delivery-notes/add?data_type=collection-note`,
        getDeliveryNotes: (projectId, companyId) => `project-delivery-notes/${projectId}${companyId ? '/' + companyId : ''}`,
        getDailyActivities: (projectId, userId?) => `daily-activities/${projectId}${userId ? "/" + userId : ''}`,
        getDailyActivity: (projectId, id) => `project/${projectId}/daily-activity/${id}`,
        updateDailyActivity: (projectId, id) => `project/${projectId}/daily-activities/${id}/update`,
        createDailyActivity: (projectId) => `project/${projectId}/daily-activities/create`,
        downloadDailyActivitiesReportXLSX: (projectId) => `project/${projectId}/daily-activity/download-delivery-notes-report-xlsx`,
        downloadWorkforceHoursReport: (projectId) => `project/${projectId}/daily-activity/workforce-hours/download`,
        downloadHoursComparisonReport: (projectId) => `project/${projectId}/daily-activity/hours-comparison/download`,
        downloadActivityHoursBreakdownReport: (projectId) => `project/${projectId}/daily-activity/activity-breakdown/download`,
        downloadDailyActivity: (projectId, id) => `project/${projectId}/daily-activity/download/${id}`,
        downloadDailyActivities: (projectId) => `project/${projectId}/daily-activities/download`,
        shareDailyActivityReport: id => `daily-activity/${id}/share`,
        getOptimaAccessGroupAndReader: projectId => `optima-access-group-and-reader/${projectId}`,
        toggleOptimaAccess: (badge_number, projectId) => `toggle-optima-access/${badge_number}/${projectId}`,
        optimaFingerprintEnrolment: projectId => `optima-fingerprint-enrolment/${projectId}`,
        remoteEnrolUserToOptima: (projectId, inductionRequestId) => `sa/${projectId}/induction/${inductionRequestId}/almas/remote-enrol`,
        retrieveBadgeInformation:  (badge_number, projectId) => `retrieve-badge-information/${badge_number}/${projectId}`,
        deleteFingerprintEnrolment:  (projectId, badge_number, finger_index) => `${projectId}/optima/delete-enrolment/${badge_number}/${finger_index}`,
        getEnrollmentStatus:  (projectId, badge_number) => `${projectId}/optima/enrollment-status/${badge_number}`,
        deleteBadgeNumber:  (badge_number, projectId) => `delete-badge-number/${badge_number}/${projectId}`,
        downloadProgressPhotos: ppId => `v2/progress-photos/download/${ppId}`,
        downloadProgressPhotosReport: `download-progress-photos-report`,
        downloadProgressPhotosXLSX: `project-progress-photos-xlsx`,
        getClerkOfWorks: (projectId, companyId) => `project-clerk-of-works/${projectId}${companyId ? "/" + companyId : ''}`,
        getProjectCowFilterData:(projectId) => `project/${projectId}/clerk-of-works/filters`,
        downloadClerkOfWorksReport: (projectId, companyId) => `project/${projectId}/cow-report/download${companyId ? "/" + companyId : ''}`,
        updateClerkOfWorks: cowId => `clerk-of-works/${cowId}/update/`,
        addCoWComment: `clerk-of-works-comment/add`,
        deleteCow: 'clerk-of-works/delete',
        downloadClerkOfWorks: cowId => `v2/clerk-of-works/download/${cowId}`,
        populateTaggedNameEmail: `clerk-of-works/populate-tagged-data`,
        downloadCowPinnedLevelMap: (projectId, level, companyId) => `clerk-of-works/download-pinned-map/${projectId}/${level}${companyId ? "/" + companyId : ''}`,
        getAllPowra: (projectId, userId) => `all-powra/${projectId}${userId ? "/" + userId : ''}`,
        getPowra: (projectId, id) => `project/${projectId}/powra/${id}`,
        updatePowra: (projectId, id) => `project/${projectId}/powra/${id}/update`,
        viewOrDownloadPowra: (id, type, updatedAt) => `powra/download/${id}/${type}/${updatedAt}`,
        sharePowraReport: id => `powra/${id}/share`,
        fetchProgressPhotosReportRecords: (projectId) => `project-progress-photos/${projectId}/between-duration`,
        getProjectIncidentReports: (projectId) => `sa/project/${projectId}/incident-report/list`,
        getCompanyIncidentList: (companyId) => `company/${companyId}/incident-report/list`,
        createUpdateIncidentReport: projectId => `project/${projectId}/incident-report/save`,
        updateIncidentReport: (projectId, id) => `project/${projectId}/incident-report/${id}/update-partially`,
        updateIncidentReportCA: (companyId, id) => `ca/company/${companyId}/incident-report/${id}/update-partially`,
        downloadIncidentReport: irId => `v2/incident-report/download/${irId}`,
        shareIncidentReport: id => `incident-report/${id}/share`,
        closeOutIncidentReport: (projectId, id) => `project/${projectId}/incident-report/${id}/close-out`,
        closeOutIncidentAction: (projectId, irId) => `project/${projectId}/incident-action/${irId}/close-out`,
        saveIncidentAlertRecipients: companyId => `employer/${companyId}/save-incident-alert-recipients`,
        getIncidentAlertRecipients: companyId => `employer/${companyId}/get-incident-alert-recipients`,
        deleteIncidentAlertRecipient: companyId => `employer/${companyId}/delete-incident-alert-recipient`,
        getInspectionTour: (projectId, id) => `project/${projectId}/inspection-tour/${id}`,
        getProjectInspectionTours: (projectId, companyId) => `v2/project/${projectId}/inspection-tour/list${companyId ? "/" + companyId : ''}`,
        updateInspectionTour: (projectId, id) => `project/${projectId}/inspection-tour/${id}/update`,
        updateChecklistToCloseOutItem: (projectId, id) => `project/${projectId}/inspection-tour/${id}/item-closeout`,
        dashboardOfInspectionTour: (projectId, type) =>  `project/${projectId}/inspection-tour/${type}/dashboard`,
        companyDashboardOfInspectionTour: (companyId, type) =>  `company/${companyId}/inspection-tour/${type}/dashboard`,
        subContractorDashboard: (companyId, type) =>  `company/${companyId}/subcontractors/${type}/dashboard`,
        taggedOwnerDashboard: (companyId, ownerId, type) =>  `company/${companyId}/tagged-owner/${ownerId}/${type}/dashboard`,
        downloadInspectionTour: (projectId, id) =>  `project/${projectId}/inspection-tour/download/${id}`,
        downloadParticipantsList: (companyId) =>  `company/${companyId}/inspection-tour/participants/download`,
        shareInspectionTourReport: id => `inspection-tour/${id}/share`,
        membersTimeLogs: (projectId, companyId, fromDate, toDate) => `optima/members-time-logs/${projectId}${companyId ? "/" + companyId : ''}?from_date=${fromDate}&to_date=${toDate}`,

        createELearningModule: (companyId) => `elearning/module/${companyId}/add`,
        updateELearningModule: (companyId, moduleId) => `elearning/module/${companyId}/update/${moduleId}`,
        getAllELearningModule: (companyId) => `elearning/modules/${companyId}`,
        getELearningModule: (companyId, id) => `elearning/module/${companyId}/${id}`,
        inviteToELearningModule: (companyId) => `elearning/module/invitation/${companyId}`,
        getProjectGoodCalls: (projectId, companyId) => `project/${projectId}/good-calls${companyId ? "/" + companyId : ''}`,
        updateGoodCall: (projectId, id) => `project/${projectId}/good-call/update/${id}`,
        downloadGoodCall: (projectId, id) => `project/${projectId}/good-call/download/${id}`,
        fetchGoodCallsForProject: (projectId) => `project/${projectId}/good-calls/filters`,
        fetchDeliveryNotesReportRecords: (projectId) => `project-delivery-notes/${projectId}/between-duration`,
        downloadDeliveryNotes: (projectId, dnId) => `project/${projectId}/delivery-notes/download/${dnId}`,
        downloadDeliveryNotesReport: (projectId) => `project/${projectId}/download-delivery-notes-report`,
        downloadDeliveryNotesReportXLSX: (projectId) => `project/${projectId}/download-delivery-notes-report-xlsx`,
        // getInductionQuestions: (inductionQuestionsId) => `induction-questions/${inductionQuestionsId}`,
        getProjectInductionQuestions: (projectId) => `induction-questions/${projectId}/project-inductions`,
        // getProjectInductionQuestionsForAdmin: (projectId) => `induction-questions/${projectId}/admins-project-inductions`,
        getActiveInductionQuestionsOfProject: (projectId) => `induction-questions/${projectId}/list/active`,
        getAllQuestionsOfProject: (projectId) => `induction-questions/${projectId}/list`,
        saveInductionQuestions: (projectId) => `induction-questions/${projectId}/save`,
        // createInductionQuestions: (projectId) => `induction-questions/${projectId}/create`,
        checkAnswer: (projectId) =>`induction-questions/${projectId}/check-answer`,

        createWorkPackagePlan: (projectId) => `work-package-plans/${projectId}/add`,
        updateWorkPackagePlan: (projectId, id) => `work-package-plans/${projectId}/${id}/update/`,
        // getProjectWorkPackagePlans: (projectId, companyId) => `work-package-plans/${projectId}${companyId ? "/" + companyId : ''}`,
        inviteToWorkPackagePlan: projectId => `work-package-plans/${projectId}/invite`,
        downloadTWorkPackagePlanReport: projectId => `work-package-plans/${projectId}/download-report`,
        downloadWorkPackagePlanXLSX: (id, projectId) => `work-package-plans/${projectId}/id/${id}/download`,
        downloadWorkPackagePlans: (projectId, id, type, updatedAt, timestamp)=> `work-package-plans/download/${projectId}/${id}/${type}/${updatedAt}${timestamp}`,
        downloadWorkPackagePlansRegister: (projectId)=> `project/${projectId}/work-package-plans/register/download`,

        createRams: (projectId) => `project/${projectId}/rams/add`,
        updateRams: (projectId, id) => `project/${projectId}/rams/${id}/update`,
        partiallySaveAssessmentForm: (projectId, id) => `project/${projectId}/rams/${id}/assessment-form/save`,
        getProjectRams: (projectId, companyId) => `project/${projectId}/rams${companyId ? "/" + companyId : ''}`,
        getProjectRamsList: (projectId) => `project/${projectId}/rams/list`,
        getProjectRamsById: (projectId, id) => `project/${projectId}/rams/${id}/detail`,
        inviteToRams: projectId => `project/${projectId}/invite-to-rams`,
        downloadRams: (projectId, id, type, params)=> `project/${projectId}/rams/${id}/download/${type}${params}`,
        downloadRamsReport: projectId => `project/${projectId}/rams/download-report`,
        downloadRamsXLSX: (id, projectId) => `project/${projectId}/rams/${id}/download-xlsx`,
        downloadRamsRegister: (projectId) => `project/${projectId}/rams/register/download`,
        approveDeclineRams: (projectId, id) => `project/${projectId}/rams/${id}/status/update`,
        downloadDocumentPreview: (projectId, id) => `project/${projectId}/rams/${id}/preview`,
        getRamsForInduction: (projectId) => `project/${projectId}/induction/rams`,
        archiveUnarchiveRams: (projectId, id) => `project/${projectId}/rams/update-archive/${id}`,
        getArchivedRams: (projectId, companyId) => `project/${projectId}/archived-rams`,
        ramsRevisionList: (projectId, id) => `project/${projectId}/rams/${id}/revisions`,
        searchRamsByRevision: (projectId, groupId) => `project/${projectId}/rams-group/${groupId}/revisions`,
        ramsRecentRevision: (projectId, id) => `project/${projectId}/rams/${id}/recent-revision`,

        getProjectMetaPlants: (projectId, companyId) => `project/${projectId}/plant-machinery${companyId ? "/" + companyId : ''}`,
        deleteAllMetaPlants: (projectId, companyId) => `project/${projectId}/plant-machinery/delete-all${companyId ? "/" + companyId : ''}`,
        addMetaPlant: (projectId) => `project/${projectId}/plant-machinery/add`,
        deleteMetaPlant: (projectId, id) => `project/${projectId}/plant-machinery/${id}/delete`,

        getProjectFatigueRecords: (projectId) => `fatigue-records/${projectId}`,
        downloadProjectFatigueRecords: (projectId) => `fatigue-records/${projectId}/download-report`,
        updateFatigueRecord: (projectId, recordId) => `project/${projectId}/fatigue-records/${recordId}/update-partially`,

        addTemporaryWorkAsset: (projectId, employerId) => `project/${projectId}/asset-temporary/add${employerId? "/" + employerId: ''}`,
        updateTemporayrWorkAsset: (projectId, id, employerId) => `project/${projectId}/asset-temporary/update/${id}${employerId? "/" + employerId: ''}`,
        getProjectAssetTemporaryWorks: (projectId) => `project/${projectId}/asset-temporary-works/list`,
        getTemporaryWorkAsset: (projectId, id) => `project/${projectId}/asset-temporary/${id}`,
        updateAssetTemporaryWorkInspection: (projectId, tempWorkId, id) => `project/${projectId}/asset-temporary/${tempWorkId}/inspection/${id}/update`,
        downloadWeeklyTemporaryWorkInspection: (projectId, tempWorkId) => `project/${projectId}/asset-temporary/${tempWorkId}/inspections/download`,


        createTaskBriefing: (projectId) => `task-briefing/${projectId}/add`,
        updateTaskBriefing: (projectId, id) => `task-briefing/${projectId}/${id}/update/`,
        getProjectTaskBriefings: (projectId, companyId) => `task-briefings/${projectId}${companyId ? "/" + companyId : ''}`,
        inviteToTaskBriefing: projectId => `task-briefing/${projectId}/invite`,
        downloadTaskBriefingReport: projectId => `task-briefing/${projectId}/download-task-briefing-report`,
        downloadTaskBriefingXLSX: (id, projectId) => `task-briefing/${projectId}/id/${id}/download`,
        downloadTaskBriefing: (projectId, id) => `project/${projectId}/task-briefing/download/${id}`,
        downloadTaskBriefingRegister: (projectId) => `project/${projectId}/task-briefing/register/download`,
        getProjectMetaActivities: (projectId, companyId) => `project/${projectId}/daily-activities${companyId ? "/" + companyId : ''}`,
        deleteAllMetaActivities: (projectId, companyId) => `project/${projectId}/daily-activities/delete-all${companyId ? "/" + companyId : ''}`,
        addMetaActivity: (projectId, companyId) => `project/${projectId}/daily-activity/add`,
        deleteMetaActivity: (projectId, id) => `project/${projectId}/daily-activity/${id}/delete`,
        addVehicleAsset: projectId => `project/${projectId}/asset-vehicle/add`,
        updateVehicleAsset: (projectId, id) => `project/${projectId}/asset-vehicle/update/${id}`,
        getVehicleAsset: (projectId, id) => `project/${projectId}/asset-vehicle/${id}`,
        updateAssetVehicleInspection: (projectId, vehicleId, id) => `project/${projectId}/asset-vehicle/${vehicleId}/inspection/${id}/update`,
        getAssetTaggedOwners: (projectId, assetType) => `project/${projectId}/asset/${assetType}/filters-list`,
        getProjectAssetVehicles: (projectId) => `v2/project/${projectId}/project-asset-vehicles/list`,
        archiveUnarchiveVehicleAsset: (projectId, id) => `project/${projectId}/asset-vehicle/archive/${id}`,
        downloadWeeklyInspections: (projectId, vehicleId) => `project/${projectId}/asset-vehicle/${vehicleId}/inspections/download`,
        addEquipmentAsset: projectId => `project/${projectId}/asset-equipment/add`,
        updateEquipmentAsset: (projectId, id) => `project/${projectId}/asset-equipment/update/${id}`,
        updateAssetEquipmentInspection: (projectId, equipmentId, id) => `project/${projectId}/asset-equipment/${equipmentId}/inspection/${id}/update`,
        getEquipmentAsset: (projectId, id) => `project/${projectId}/asset-equipment/${id}`,
        getProjectAssetEquipments: (projectId) => `v2/project/${projectId}/project-asset-equipments/list`,
        archiveUnarchiveEquipmentAsset: (projectId, id) => `project/${projectId}/asset-equipment/archive/${id}`,
        updateForceUpdateConfig: `inndex-setting/force-update-config/update`,
        updateUsefulInfoFilesData: `inndex-setting/useful-info-files/update`,
        downloadAssetWeeklyInspections: (projectId, assetCategory, assetId) => `project/${projectId}/${assetCategory}/${assetId}/inspections/download`,
        downloadRegisterXLSX: (projectId, assetType) => `project/${projectId}/${assetType}/register-download`,
        createIbCl: (projectId) => `project/${projectId}/inspection-builder/checklist/add`,
        createCompanyIbCl: (companyId) => `company/${companyId}/inspection-builder/checklist/add`,
        createIbClReport: (projectId) => `project/${projectId}/inspection-builder/report/add`,
        updateIbClReport: (projectId, ibReportId) => `inspection-builder/report/${projectId}/update/${ibReportId}`,
        updateIbCl: (projectId, ibClId) => `project/${projectId}/inspection-builder/checklist/update/${ibClId}`,
        updateCompanyIbCl: (companyId, ibClId) => `company/${companyId}/inspection-builder/checklist/update/${ibClId}`,
        getProjectIbCl: (projectId, companyId) => `inspection-builder/project-checklists/${projectId}${companyId ? "/" + companyId : ''}`,
        getCompanyIBChecklists: (companyId) => `inspection-builder/company-checklists/${companyId}`,
        inviteToNMsOnInspection: (companyId) => `company/${companyId}/inspection-builder/invite-to-nm`,
        getIbReportsById: (projectId, ibClId, companyId) => `inspection-builder/project-reports/${projectId}/${ibClId}${companyId ? "/" + companyId : ''}`,
        getIbReportsDrafts: (projectId, ibClId) => `inspection-builder/project-reports/${projectId}/${ibClId}`,
        getIbReportsDraftById: (projectId, reportId) => `inspection-builder/report/${projectId}/${reportId}`,
        deleteIbReportsDrafts: (projectId, ibReportId) => `inspection-builder/project-reports/${projectId}/${ibReportId}`,
        closeOutIbClReportItem: (projectId, id) => `project/${projectId}/inspection-builder/report/${id}/item-closeout`,
        downloadIbReport: (projectId, id) => `project/${projectId}/inspection-builder/report/download/${id}`,
        dashboardOfInspectionBuilder: (projectId, ibId, type) =>  `project/${projectId}/inspection-builder/${ibId}/${type}/dashboard`,
        companyDashboardOfInspectionBuilder: (companyId, ibId, type) =>  `company/${companyId}/inspection-builder/${ibId}/${type}/dashboard`,
        downloadIbParticipantsList: (companyId, ibId) =>  `company/${companyId}/inspection-builder/${ibId}/participants/download`,
        shareInspectionBuilderReport: id => `inspection-builder/${id}/share`,
        ibSubContractorDashboard: (companyId, type, ibId) => `company/${companyId}/ib/${ibId}/subcontractors/${type}/dashboard`,
        ibTaggedOwnerDashboard: (companyId, ownerId, ibId) => `company/${companyId}/ib/${ibId}/tagged-owner/${ownerId}/download/dashboard`,
        sendMessage: projectId => `project/${projectId}/site-messaging/send`,
        sendCompanyMessage:employerId => `company/${employerId}/site-messaging/send`,
        resendCompanyMessage:(employerId,messageId) => `company/${employerId}/site-messaging/${messageId}/resend`,
        editCompanyMessage:(employerId,messageId) => `company/${employerId}/site-messaging/message/${messageId}/edit`,
        getCompanyMessageRecipients:(employerId,messageId) => `company/${employerId}/site-messaging/${messageId}/recipients`,
        resendMessage: projectId => `project/${projectId}/site-messaging/resend`,
        getProjectMessages: projectId => `project/${projectId}/site-messaging/messages`,
        getCompanyMessages:(employerId)=>`ca/company/${employerId}/site-messaging/messages/list`,
        getProjectActiveUsers: projectId => `project/${projectId}/active-users`,
        updateUserEmpDetail: (userId, empDetailId) => `user/user-employment/update/${userId}/${empDetailId}`,
        saveIncidentActionCategories: (companyId) => `employer/${companyId}/save-incident-action-categories`,
        saveCompanySetting: (companyId) => `employer/${companyId}/company-setting`,
        getCompanySettingByName: (companyId) => `company-setting/${companyId}/fetch`,
        getProjectSettingsByName: (projectId) => `project/${projectId}/settings/fetch`,
        deleteProjectSetting: (projectId) => `project/${projectId}/setting/delete`,
        getMetaDistrict: `inndex/meta-district`,
        deleteCompanyMessage:(employerId)=>`company/${employerId}/site-messaging/message/delete`,

        createQCL: (projectId) => `project/${projectId}/quality-checklist/add`,
        createCompanyQCL: (companyId) => `company/${companyId}/quality-checklist/add`,
        updateQCL: (projectId, qclId) => `project/${projectId}/quality-checklist/update/${qclId}`,
        updateCompanyQCL: (companyId, qclId) => `company/${companyId}/quality-checklist/update/${qclId}`,
        updateITPToCloseOutItem: (itpId, projectId) => `project/${projectId}/itp-inspection/${itpId}/item-close-out`,
        getProjectQChecklists: (projectId, companyId) => `product-quality-checklist/${projectId}${companyId ? "/" + companyId : ''}`,
        getCompanyQChecklists: ( companyId ) => `company-quality-checklist/${companyId}`,
        getCLInspectionsByclId: (qclId, projectId) => `checklist-inspections/by-qcl-id/${qclId}/${projectId}`,
        downloadQclReport: (id, qclId, projectId, type, updatedAt ) => `v2/project/${projectId}/quality-checklist/${qclId}/checklist-report/${id}/${type}${updatedAt ? "/" + updatedAt : ''}`,
        getProjectReceivedBriefing: (projectId, userId) => `project/${projectId}/user/${userId}/received/briefing`,
        shareItpReport: id => `itp-report/${id}/share`,
        getPartiallyCompletedCLinspectionsCountSA: (qclId,projectId) => `project/${projectId}/quality-checklist/${qclId}/partial-reports`,
        getPartiallyCompletedCLinspectionsCountCA: (qclId,companyId) => `company/${companyId}/quality-checklist/${qclId}/partial-reports`,

        createCompanyInduction: `company-induction/create`,
        createConductCard: companyId => `ca/${companyId}/card-conduct/create`,
        updateConductCard: (companyId, id) => `ca/${companyId}/card-conduct/update/${id}`,
        getConductCards: companyId => `ca/${companyId}/card-conducts/get`,
        getConductCardsById: (companyId, id) => `ca/${companyId}/card-conduct/get/${id}`,
        deleteConductCard: (companyId, id) => `ca/${companyId}/card-conduct/delete/${id}`,
        unassignCardUsersList: (companyId, id) => `ca/${companyId}/card-conduct/unassign/${id}`,
        getAssignedCardUsersList: companyId => `ca/${companyId}/card-conduct/assigned/card`,
        downloadAssignedConductCard: companyId => `ca/${companyId}/card-conduct/assigned/card/download`,
        getConductCardsByProjectId: projectId => `project/${projectId}/card-conducts`,
        assignConductCards: (projectId, id) => `project/${projectId}/card-conduct/assign/${id}`,
        unassignedConductCardPp: (projectId, id) => `project/${projectId}/card-conduct/unassign/${id}`,
        downloadConductCardReportById: projectId => `project/${projectId}/card-conducts/download`,
        getHeatmapDataSA :(projectId)=>`sa/projects/${projectId}/heat-map-data`,
        getHeatmapDataCA :(employerId,projectId)=>`ca/${employerId}/projects/${projectId}/heat-map-data`,
        getHeatmapDataLiveTv :(projectId)=>`live-tv/projects/${projectId}/heat-map-data`,

        getCompanyAssetConfig: companyId => `ca/${companyId}/asset-config/list`,
        saveCompanyAssetConfig: (companyId, assetId) => `ca/${companyId}/asset-config/${assetId}/save`,
        getProjectAssetConfig: projectId => `project/${projectId}/asset-config/list`,

        getCompanyProjectsCA:(employerId) => `company/${employerId}/projects-list?is_active=true`,
        getProjectGoodCallsById: (projectId, gcId) => `project/${projectId}/good-call/${gcId}`,
        getDocumentPermissions: (projectId) => `projects/${projectId}/permissions`,
        addDocumentPermission: (projectId) => `projects/${projectId}/permissions`,
        updateDocumentPermission: (projectId, id) => `projects/${projectId}/permissions/${id}`,
        deleteDocumentPermission: (projectId, id) => `projects/${projectId}/permissions/${id}`,

        getFoldersTree: (projectId) => `projects/${projectId}/folders/tree`,
        files: (projectId) => `projects/${projectId}/files`,
        getDeletedFiles: (projectId) => `projects/${projectId}/files/deleted`,
        getFilesByIds: (projectId) => `projects/${projectId}/files/get-files-by-ids`,
        getFile: (projectId, id) => `projects/${projectId}/files/${id}`,
        getFileUrl: (projectId, id) => `projects/${projectId}/files/${id}/url`,
        restoreFile: (projectId, id) => `projects/${projectId}/files/${id}/restore`,
        authValidate: (projectId) => `projects/${projectId}/auth/validate`,
        singleFile: (projectId, id) => `projects/${projectId}/files/${id}`,
        bulkDelete: (projectId) => `projects/${projectId}/files/bulk-delete`,
        postFileUrl: () => `files/signed-url`,
        bulkDownload: (projectId) => `projects/${projectId}/files/bulk-download`,
    };

    constructor(private httpClient: HttpClient, private toastService: ToastService) {
    }

/*    fetch(url): Observable<any> {
        return this.httpClient.get(this.getBaseUrl() + url)
            .pipe(
                // catchError(this.handleError)
            );
    }*/

    getBaseUrl() {
        return `${AppConstant.apiServerUrl}/${config.API_URL}`;
    }
    getFileUrl() {
        return `${AppConstant.fileServerUrl}/${config.API_URL}v1/`;
    }

    private handleError(error: any) {
        let errorMsg = (error.message) ? error.message :
            error.status ? `${error.status} - ${error.statusText}` : 'Server error';

        return throwError(errorMsg);
    }

    getFileName(contentDisposition, responseContentType) {
        let fileName =  (responseContentType == 'application/pdf') ? 'export.pdf' : 'export.xlsx';
        let splittedStr = (contentDisposition) ? contentDisposition.split(';') : [];
        if (splittedStr.length > 1 && splittedStr[1].split('=')[1]) {
            fileName = decodeURIComponent(splittedStr[1].split('=')[1].trim());
        }

        return fileName;
    }

    handleResponse (request, response, cb, fileName = '') {
        response.subscribe(
            response => {
                const responseData = response.body
                if(responseData?.success && responseData?.location){
                    saveAs(responseData.location, fileName || responseData.name);
                    cb();
                } else{
                    let responseContentType = (response.headers.get('content-type')) ? response.headers.get('content-type') :'application/pdf';
                    let responseMessage = (response.headers.get('message')) ? response.headers.get('message') : 'Something went wrong with the record.';
                    //if neither pdf nor xlsx
                    if (responseContentType.match(/application\/json/i)) {
                        this.toastService.show(this.toastService.types.ERROR, responseMessage);
                        return cb();
                    }
                    let blob = new Blob([response.body as BlobPart], {type: responseContentType});
                    let contentDisposition = response.headers.get('content-disposition');
                    let filename = fileName || this.getFileName(contentDisposition, responseContentType);
                    saveAs(blob, filename);
                    cb();
                }
            },
            error => {
                const message = (error.headers && error.headers.get('message')) ? error.headers.get('message') : 'Failed to download the report.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: error });
                cb();
            }
        );
    }

    downloadHandler(request, cb, routeMethod, fileName = '', responseType = 'blob' as 'json') {
        if(request.format == 'html' || request.type == 'html') {
            return this.httpClient.post(`${this.getBaseUrl()}${routeMethod}`, request, {
                'responseType': 'text'
            });
        } else {
            let response = this.httpClient.post(`${this.getBaseUrl()}${routeMethod}`, request, {
                observe: 'response',
                responseType: responseType
            });
            this.handleResponse (request, response, cb, fileName);
        }
    }

    isMobileDevice() {
        if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
            return true;
        } else {
            return false;
        }
    }
    fileDownloader = (routeMethod, request, fileName = "", cb = () => {}, httpMethodType = 'post') => {
        let default_message = 'Something is not quite right, please wait a moment and then try again.';
        this.httpClient[httpMethodType](`${this.getBaseUrl()}${routeMethod}`, request).subscribe((response:any)=>{
            if (response && response.success) {
                if(!response.location){
                    const message = response.message || default_message;
                    this.toastService.show(this.toastService.types.ERROR, message);
                    cb();
                    return;
                }
                this.httpClient.get(response.location, {
                    observe: 'response',
                    responseType: 'blob' as 'json',
                }).subscribe((blobResponse) => {
                    let blob = new Blob([blobResponse.body as BlobPart], {
                        type: 'application/pdf'
                    });
                    saveAs(blob, fileName || response.name);
                    cb();
                }, (error) => {
                    // kept for staging test purposes, will remove once optimization is stable.
                    this.toastService.show(this.toastService.types.ERROR, default_message, { data: error });
                    cb();
                });
            }
            if(response && response.error){
                const message = response.message;
                this.toastService.show(this.toastService.types.ERROR, message);
                cb();
            }
        }, (error) => {
            // kept for staging test purposes, will remove once optimization is stable.
            // console.log(error)
            if(error && error.error.text){
                let parser = new DOMParser();
                let doc = parser.parseFromString(error.error.text, 'text/html');
                let pContent = doc.querySelector('p').textContent;
                this.toastService.show(this.toastService.types.ERROR, pContent);
                cb();
                return;
            }
            this.toastService.show(this.toastService.types.ERROR, default_message);
            cb();
        });
    }

    parseJson = (body, defaultValue = {}) => {
        try {
            return JSON.parse(body);
        } catch (e) {
            return defaultValue;
        }
    }
}

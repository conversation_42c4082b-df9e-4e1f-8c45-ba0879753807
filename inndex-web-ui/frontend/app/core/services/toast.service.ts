import { Injectable } from "@angular/core";
import { ToastType } from "@app/core/enums/toast-type.enum";
import { AssetsUrl } from '@app/shared/assets-urls/assets-urls';
import * as dayjs from 'dayjs';

export interface Toast {
  type: string;
  message: string;
  title?: string;
  delay?: number;
  containerClasses?: string;
  toastImg?: string;
  progress?: number;
  id?: string;
  intervalId?: ReturnType<typeof setInterval>; // Ensures compatibility
  timeoutId?: ReturnType<typeof setTimeout>;
  remainingTime: number;
  startTime?: number;
  paused?: boolean;
  wrapperClasses?: string;
}

@Injectable({ providedIn: "root" })
export class ToastService {
  types = ToastType;
  toastIcons = AssetsUrl.toastIcons;
  toasts: Toast[] = [];

  show(type: string, message: string, toastInfo: { title?: string, delay?: number, data?: any } = {}) {
    let toast: Toast = { 
      type: type,
      message: this.capitalizeFirstLetterOnly(message, type),
      title: toastInfo.title || type,
      delay: 0,
      containerClasses: '',
      toastImg: '',
      progress: 100,
      id: `${type} + ${dayjs().valueOf()}`,
      remainingTime: 0,
      paused: false,
      wrapperClasses: ''
    };

    if (type === this.types.SUCCESS) {
      toast.containerClasses = 'border-success';
      this.setToastImgAndDelay(toast, this.toastIcons.success, toastInfo.delay || 5000);
    } else if (type === this.types.INFO) {
      toast.containerClasses = 'border-info';
      this.setToastImgAndDelay(toast, this.toastIcons.info, toastInfo.delay || 5000);
    } else if (type === this.types.WARNING) {
      toast.containerClasses = 'border-warning';
      this.setToastImgAndDelay(toast, this.toastIcons.warning, toastInfo.delay || 8000);
    } else if (type === this.types.UPDATE_AVAILABLE) {
      toast.containerClasses = 'custom-toast';
      toast.wrapperClasses = 'toast-w-large';
      this.setToastImgAndDelay(toast, this.toastIcons.warning, toastInfo.delay || 10000);
    } else {
      toast.containerClasses = 'border-danger';
      this.setToastImgAndDelay(toast, this.toastIcons.error, toastInfo.delay || 10000);
    }
    toastInfo.data && ((toast.type === this.types.ERROR) ? console.error(toast.message, toastInfo.data) : console.log(toast.message, toastInfo.data));

    this.toasts.push(toast);
    this.startToastInterval(toast);
    this.startAutoHideTimer(toast);
  }

  setToastImgAndDelay(toast: Toast, toastImg: string, delayTime: number) {
    toast.toastImg = toastImg;
    toast.delay = delayTime;
    toast.remainingTime = delayTime;
  }

  startToastInterval(toast: Toast) {
    toast.startTime = dayjs().valueOf();
    toast.intervalId = setInterval(() => {
      if (!toast.paused) {
        const elapsedTime = dayjs().valueOf() - toast.startTime;
        toast.progress = Math.max(0, (toast.remainingTime - elapsedTime) / toast.delay * 100);
  
        if (elapsedTime >= toast.remainingTime) {
          this.close(toast);
          clearInterval(toast.intervalId);
        }
      }
    }, 100);
  }
  
  startAutoHideTimer(toast: Toast) {
    toast.timeoutId = setTimeout(() => {
      this.close(toast);
    }, toast.remainingTime);
  }

  close(toast: Toast) {
    clearInterval(toast.intervalId);
    clearTimeout(toast.timeoutId);
    this.toasts = this.toasts.filter((t) => t.id !== toast.id);
  }

  capitalizeFirstLetterOnly(msg: string, type: string) {
    if(type === this.types.UPDATE_AVAILABLE){
      return msg;
    }
    return msg.charAt(0).toUpperCase() + msg.slice(1).toLowerCase();
  }

  pauseToast(toast: Toast) {
    if (toast.paused) return;
  
    clearInterval(toast.intervalId);
    clearTimeout(toast.timeoutId);
  
    toast.paused = true;
    toast.remainingTime -= dayjs().valueOf() - toast.startTime;
  }
  
  resumeToast(toast: Toast) {
    if (!toast.paused) return;
  
    toast.paused = false;
    toast.startTime = dayjs().valueOf();
  
    this.startToastInterval(toast);
    this.startAutoHideTimer(toast);
  }

  clear() {
    this.toasts = [];
  }
}

import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class FormDirtyService {
    public dirtyForms = new Set<string>();

    setDirty(formId: string, isDirty: boolean): void {
        if (isDirty) {
            this.dirtyForms.add(formId);
        } else {
            this.dirtyForms.delete(formId);
        }
    }

    isAnyFormDirty(): boolean {
        return this.dirtyForms.size > 0;
    }
}

import {FormDirtyService} from '@app/core';


describe('FormDirtyService', () => {
    let service: FormDirtyService;

    beforeEach(() => {
        service = new FormDirtyService();
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should return false when no forms are dirty', () => {
        expect(service.isAnyFormDirty()).toBeFalsy();
    });

    it('should return true after marking one form as dirty', () => {
        service.setDirty('form-1', true);
        expect(service.isAnyFormDirty()).toBeTruthy();
    });

    it('should return false after marking a dirty form as clean', () => {
        service.setDirty('form-1', true);
        expect(service.isAnyFormDirty()).toBeTruthy();

        service.setDirty('form-1', false);
        expect(service.isAnyFormDirty()).toBeFalsy();
    });

    it('should handle multiple forms correctly', () => {
        service.setDirty('form-1', true);
        service.setDirty('form-2', true);
        expect(service.isAnyFormDirty()).toBeTruthy();

        service.setDirty('form-1', false);
        expect(service.isAnyFormDirty()).toBeTruthy(); // form-2 still dirty

        service.setDirty('form-2', false);
        expect(service.isAnyFormDirty()).toBeFalsy();
    });
});

import {TestBed, fakeAsync, tick, discardPeriodicTasks} from '@angular/core/testing';
import { SwUpdate } from '@angular/service-worker';
import { Subject } from 'rxjs';
import { UpdateService } from './update.service';
import {ToastService} from '@app/core';

// Mock ToastService
class MockToastService {
    types = { UPDATE_AVAILABLE: 'update' };
    toasts = [
        { type: 'info', message: 'Info message' },
        { type: 'update', message: 'Old update' }
    ];
    show = jasmine.createSpy('show');
}

// Mock SwUpdate
class MockSwUpdate {
    isEnabled = true;
    available = new Subject<void>();
    checkForUpdate = jasmine.createSpy('checkForUpdate');
}

xdescribe('UpdateService', () => {
    let service: UpdateService;
    let swUpdate: MockSwUpdate;
    let toastService: MockToastService;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                UpdateService,
                { provide: SwUpdate, useClass: MockSwUpdate },
                { provide: ToastService, useClass: MockToastService }
            ]
        });

        service = TestBed.inject(UpdateService);
        swUpdate = TestBed.inject(SwUpdate) as any;
        toastService = TestBed.inject(ToastService) as any;
    });

    // Confirms service instantiation
    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    // Verifies 30s polling
    it('should call checkForUpdate periodically if updates are enabled', fakeAsync(() => {
        service.checkForUpdate(300000); // Inject 1 second interval for test

        tick(300000);
        expect(swUpdate.checkForUpdate).toHaveBeenCalledTimes(1);

        tick(300000);
        expect(swUpdate.checkForUpdate).toHaveBeenCalledTimes(2);

        discardPeriodicTasks();
    }));

    // Handles SwUpdate.isEnabled = false
    it('should not call checkForUpdate if updates are disabled', fakeAsync(() => {
        swUpdate.isEnabled = false;
        service.checkForUpdate();
        tick(60000);
        expect(swUpdate.checkForUpdate).not.toHaveBeenCalled();
    }));

    // Validates update trigger and toast display
    it('should show toast when an update is available', () => {
        service.checkForUpdate();
        swUpdate.available.next();  // simulate update
        expect(toastService.show).toHaveBeenCalledWith(
            toastService.types.UPDATE_AVAILABLE,
            jasmine.any(String),
            { title: 'Update available', delay: 600000 }
        );
    });

    // Validates correct array filtering logic
    it('removeObjectFromArray should remove matching items from array', () => {
        const testArray = [
            { type: 'info', msg: 'test' },
            { type: 'update', msg: 'test2' },
            { type: 'error', msg: 'test3' }
        ];

        const result = service.removeObjectFromArray(testArray, 'type', 'update');
        expect(result.length).toBe(2);
        expect(result.some(item => item.type === 'update')).toBeFalsy();
    });
});

import { Injectable } from '@angular/core';
import { SwUpdate } from '@angular/service-worker';
import { interval } from 'rxjs';
import {ToastService} from '@app/core';

@Injectable({
    providedIn: 'root'
})
export class UpdateService {
    constructor(
        // private updates: SwUpdate,
        private toastService: ToastService,
    ) {
    }

    checkForUpdate(intervalMs: number = 300000) {
        // console.log('Calling checkForUpdate()....');
        // if (this.updates.isEnabled) {
        //     console.log('Update is enabled...');
        //     // Automatically check for updates every 5 min on stag and prod = 24 hrs
        //     interval(intervalMs).subscribe(() => {
        //         this.updates.checkForUpdate();
        //     });
        //
        //     this.updates.available.subscribe(() => {
        //         this.promptUser();
        //     });
        // }
    }

    promptUser() {
        this.toastService.toasts = this.removeObjectFromArray(this.toastService.toasts, 'type', this.toastService.types.UPDATE_AVAILABLE);
        const message = 'A new version of innDex is available, please refresh your browser to continue using the latest features and improvements.';
        this.toastService.show(this.toastService.types.UPDATE_AVAILABLE, message, {title: 'Update available', delay: 600000});
    }

    removeObjectFromArray(arr, key, value) {
        return arr.filter(obj => obj[key] !== value);
    }

    unRegisterServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistrations().then((registrations) => {
                for (let registration of registrations) {
                    registration.unregister().then((success) => {
                        if (success) {
                            console.log('Service worker unregistered:', registration);
                        }
                    });
                }
            });
        }
    }

    clearNgswCache(){
        caches.keys().then((keyList) => {
            keyList.forEach((key) => {
                if (key.includes('ngsw')) {
                    caches.delete(key).then(() => {
                        console.log(`Deleted cache: ${key}`);
                    });
                }
            });
        });
    }
}

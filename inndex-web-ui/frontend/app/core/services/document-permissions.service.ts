/**
 * Created by <PERSON><PERSON>
 */
import {Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {HttpService} from './http.service';
import {DocumentPermissionRequest} from '../models';

@Injectable()
export class DocumentPermissionsService {
    constructor(
        private http: HttpClient,
        private httpService: HttpService
    ) {}

  addDocumentPermission(
    projectId: number,
    permission: DocumentPermissionRequest
  ) {
    return this.http.post<any>(
      `${this.httpService.getFileUrl()}${this.httpService.routes.addDocumentPermission(
        projectId
      )}`,
      permission
    );
  }

  getDocumentPermissions(projectId: number) {
    return this.http.get(
      `${this.httpService.getFileUrl()}${this.httpService.routes.getDocumentPermissions(
        projectId
      )}`
    );
  }

  updateDocumentPermissions(projectId: number, id: number, updatedPermission) {
    return this.http.put<any>(
      `${this.httpService.getFileUrl()}${this.httpService.routes.updateDocumentPermission(
        projectId,
        id
      )}`,
      updatedPermission
    );
  }

  deleteDocumentPermissions(projectId: number, id: number) {
    return this.http.delete<any>(
      `${this.httpService.getFileUrl()}${this.httpService.routes.deleteDocumentPermission(
        projectId,
        id
      )}`,
      {}
    );
  }
}

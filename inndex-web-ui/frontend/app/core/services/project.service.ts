/**
 * Created by spatel on 28/9/18.
 */
 import {Injectable} from '@angular/core';
 import {HttpClient} from "@angular/common/http";
 import {map} from "rxjs/operators";
 import {HttpService} from './http.service';
 import {Project} from './../models';
import { AssetsUrl } from '@app/shared/assets-urls/assets-urls';

 @Injectable()
 export class ProjectService {

     PROJECT_VALUE: Array<any> = [{
         key: '<£200k',
         value: '<£200k'
     },{
         key: '£200k-£500k',
         value: '£200k-£500k'
     },{
         key: '£500k-£1m',
         value: '£500k-£1m'
     },{
         key: '£1m-£2m',
         value: '£1m-£2m'
     },{
         key: '£2m-£5m',
         value: '£2m-£5m'
     },{
         key: '£5m-£10m',
         value: '£5m-£10m'
     },{
         key: '£10m-£20m',
         value: '£10m-£20m'
     },{
         key: '£20m-£35m',
         value: '£20m-£35m'
     },{
         key: '£35m-£50m',
         value: '£35m-£50m'
     },{
         key: '£50m-£100m',
         value: '£50m-£100m'
     },{
         key: '£100m+',
         value: '£100m+'
     },];

     PROJECT_TYPES: Array<any> = [{
         key: 'rail',
         value: 'Rail',
         icon:'/images/project_type_icon/rail.svg'
     },{
         key: 'highways',
         value: 'Highways',
         icon:'/images/project_type_icon/highway.svg'
     },{
         key: 'nuclear',
         value: 'Nuclear',
         icon:'/images/project_type_icon/nuclear.svg'
     },{
         key: 'bridges',
         value: 'Bridges',
         icon:'/images/project_type_icon/bridge.svg'
     },{
         key: 'aviation',
         value: 'Aviation',
         icon:'/images/project_type_icon/aviation.svg'
     },{
         key: 'marine',
         value: 'Marine',
         icon:'/images/project_type_icon/marine.svg'
     },{
         key: 'commercial',
         value: 'Commercial',
         icon:'/images/project_type_icon/commercial.svg'
     },{
         key: 'residential',
         value: 'Residential',
         icon:'/images/project_type_icon/residential.svg'
     },{
         key: 'civil',
         value: 'Civil',
         icon:'/images/project_type_icon/civil.svg'
     },{
         key: 'industrial',
         value: 'Industrial',
         icon:'/images/project_type_icon/industrial-v2.svg'
     },{
         key: 'other',
         value: 'Other',
         icon:'/images/project_type_icon/other.svg'
     },];

     projectTypeIcons = AssetsUrl.projectTypeIcons; // project type icons .png
     PROJECT_TYPES_NEW: Array<any> = [
         {
             key: "rail",
             value: "Rail",
             icon: this.projectTypeIcons.rail,
         },
         {
             key: "highways",
             value: "Highways",
             icon: this.projectTypeIcons.highways,
         },
         {
             key: "nuclear",
             value: "Nuclear",
             icon: this.projectTypeIcons.nuclear,
         },
         {
             key: "bridges",
             value: "Bridges",
             icon: this.projectTypeIcons.bridges,
         },
         {
             key: "aviation",
             value: "Aviation",
             icon: this.projectTypeIcons.aviation,
         },
         {
             key: "marine",
             value: "Marine",
             icon: this.projectTypeIcons.marine,
         },
         {
             key: "commercial",
             value: "Commercial",
             icon: this.projectTypeIcons.commercial,
         },
         {
             key: "residential",
             value: "Residential",
             icon: this.projectTypeIcons.residential,
         },
         {
             key: "civil",
             value: "Civil",
             icon: this.projectTypeIcons.civil,
         },
         {
             key: "industrial",
             value: "Industrial",
             icon: this.projectTypeIcons.industrial,
         },
         {
             key: "other",
             value: "Other",
             icon: this.projectTypeIcons.other,
         },
     ];

     PROJECT_STATUS:Array<any> = [{
         key: 1,
         value: 'Active'
     },
     {
         key: 0,
         value: 'Offline'
     }
     ];

     SITE_MAIN_RISKS = [
        { name: 'Temporary Works' },
        { name: 'Confined Spaces' },
        { name: 'Safe Digging Practices' },
        { name: 'Traffic & Pedestrian Interface' },
        { name: 'Isolation & Guarding' },
        { name: 'Subcontractor Control' },
        { name: 'Working at Height' },
        { name: 'Lifting Operations' },
        { name: 'Occupational Health' },
        { name: 'Occupational Road Risk' },
    ];

     constructor(
         private http: HttpClient,
         private httpService: HttpService
     ) {
     }

     siteAdmin_getProject(id:number, all: any = null, expand_gates: any = false, include_admins: boolean = false, populate_divisions: boolean = false, check_ib_availability: boolean = false, expand_cel: boolean = false, from_ir: boolean = false) {
         console.log('Calling get project by id', id);
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.siteAdmin_getProject(id)}`, {
             params: {
                 ...all && {all: all.toString()},
                 ...include_admins && {include_admins: include_admins.toString()},
                 ...expand_gates && {expand_gates: expand_gates},
                 ...populate_divisions && {populate_divisions: populate_divisions.toString()},
                 ...check_ib_availability && {check_ib_availability: check_ib_availability.toString()},
                 ...expand_cel && {expand_cel: expand_cel.toString()},
                 ...from_ir && {from_ir: from_ir.toString()},
             }
         });
     }

     getProjects(params: any = {'limited': 'false'}) {
         console.log('Calling get user projects');
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getUserProjects}`, {params});
     }

     getProject(id: number, all: any = null, expand_gates: any = false, include_admins: boolean = false, populate_divisions: boolean = false, check_ib_availability: boolean = false, expand_cel: boolean = false, from_induction: boolean = false) {
         console.log('Calling get project by id', id);
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProject(id)}`, {
             params: {
                 ...all && {all: all.toString()},
                 ...include_admins && {include_admins: include_admins.toString()},
                 ...expand_gates && {expand_gates: expand_gates},
                 ...populate_divisions && {populate_divisions: populate_divisions.toString()},
                 ...check_ib_availability && {check_ib_availability: check_ib_availability.toString()},
                 ...expand_cel && {expand_cel: expand_cel.toString()},
                 ...from_induction && {from_induction: from_induction.toString()},
             }
         })
             .pipe(map((data: any) => {
                 return data;
             }));
     }

     checkCompanyProjectStatus(companyId: number) {
         return this.http.get<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.checkCompanyProjectStatus(companyId)}`);
     }

     createProject(project: Project) {
         console.log('Calling save user project', project);
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.createProject}`, project)
             .pipe(map((data: any) => {
                 return data;
             }));
     }

     updateProject(project: Project) {
         console.log('Calling update project..', project);
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.updateProject(project.id)}`, project)
             .pipe(map((data: any) => {
                 return data;
             }));
     }

     getProjectInductionBookingSetting(projectId: number) {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProjectInductionBookingSetting(projectId)}`, {});
     }

     saveProjectInductionBookingSetting(projectId: number, payload: any){
         return this.http.post(`${this.httpService.getBaseUrl()}${this.httpService.routes.saveProjectInductionBookingSetting(projectId)}`, payload);
     }

     getProjectInductionBookingSlots(projectId: number, params: any = {}){
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProjectInductionBookingSlots(projectId)}`, {params});
     }

     updateProjectLiveTvFooter(project: Project) {
         console.log('Calling  project update live tv footer..', project);
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.updateProjectLiveTvFooter(project.id)}`, project)
             .pipe(map((data: any) => {
                 return data;
             }));
     }

     getFormTemplates() {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getFormTemplates}`)
             .pipe(map((data: any) => {
                 return data;
             }));
     }

     /*// @deprecated
     getSiteHealthAssessmentQuestions() {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getSiteHealthAssessmentQuestions}`)
             .pipe(map((data: any) => {
                 return data;
             }));
     }*/

     getProjectInductionRequests(projectId: number, params: any = {}) {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProjectInductionRequests(projectId)}`, {params})
             .pipe(map((data: any) => {
                 return data;
             }));
     }

     getCAProjectInductionRequests(employerId: number, projectId: number, params: any = {}) {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getCAProjectInductionRequests(employerId, projectId)}`, {params});
     }

    getCAProjectInductions(employerId: number, projectId: number, params: any = {}) {
        return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getCAProjectInductions(employerId, projectId)}`, {params});
    }

    getCAInductionEmployers(employerId: number, projectId: number, params: any = {}) {
        return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.caRoutes.getInductionEmployers(employerId, projectId)}`, {params});
    }

    getSAProjectInductionById(projectId: number, inductionRequestId: number, params: any = {}) {
        return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.saRoutes.getProjectInductionById(projectId, inductionRequestId)}`, {params});
    }
    getSAProjectInductions(projectId: number, params: any = {}) {
        return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.saRoutes.getSAProjectInductions(projectId)}`, {params});
    }

     getActiveUsersTimesheetList(projectId: number, params: any = {}) {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.saRoutes.getActiveUsersTimesheetList(projectId)}`, {params});
     }

     getUserTimesheetDetails(projectId: number, userId: number, params: any = {}) {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.saRoutes.getUserTimesheetDetails(projectId, userId)}`, {params});
     }
     approveUserTimesheets(projectId: number, payload: any = {}) {
         return this.http.post(`${this.httpService.getBaseUrl()}${this.httpService.saRoutes.approveUserTimesheets(projectId)}`, payload);
     }
     saveTimesheetComment(projectId: number, payload: any = {}) {
         return this.http.post(`${this.httpService.getBaseUrl()}${this.httpService.saRoutes.saveTimesheetComment(projectId)}`, payload);
     }
     saveWorkerRecord(projectId: number, params: any = {}) {
         return this.http.post(`${this.httpService.getBaseUrl()}${this.httpService.saRoutes.saveWorkerRecord(projectId)}`, params);
     }
     downloadTimesheets(projectId: number, payload: any = {}, cb = () => {}) {
         this.httpService.downloadHandler(payload, cb, this.httpService.saRoutes.downloadTimesheets(projectId));
     }
     bulkSaveTimesheetInfo(projectId: number, payload: any = {}) {
         return this.http.post(`${this.httpService.getBaseUrl()}${this.httpService.saRoutes.bulkSaveTimesheetInfo(projectId)}`, payload);
     }

     getSAInductionEmployers(projectId: number, params: any = {}) {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.saRoutes.getInductionEmployers(projectId)}`, {params});
     }
     getSAInductionJobRoles(projectId: number, params: any = {}) {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.saRoutes.getInductionJobRoles(projectId)}`, {params});
     }

     // lighter version of get all inductions
     /**
      * @deprecated: in favor of `getProjectInductedUsersV2`
      * @param projectId
      * @param params
      */
     getProjectInductions(projectId: number, params: any = {}) {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProjectInductions(projectId)}`, {params});
     }

     getProjectInductedUsersNames(projectId: number, params: any = {}) {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProjectInductedUsersNames(projectId)}`, {params});
     }

     getProjectInductedUsersNamesCA(employerId: number, projectId: number, params: any = {}) {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProjectInductedUsersNamesCA(employerId, projectId)}`, {params});
     }

     searchProjectInductedUsers(projectId: number, params: any = {}) {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.searchProjectInductedUsers(projectId)}`, {params});
     }

     getAllProjects() {
       return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getallProjects}`)
         .pipe(map((data: any) => {
           return data;
         }));
     }

     updateProjectstatus(project: Project) {
       console.log('Calling update project', project);
       return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.updateProjectStatus(project.id)}`, project)
         .pipe(map((data: any) => {
           return data;
         }));
       }

     getUserInfo(email:string, verified?: boolean, payload: any = {}) {
        console.log('Calling get user info', email);
        return this.http.post(`${this.httpService.getBaseUrl()}${this.httpService.routes.getUserInfo}`, email ? { ...payload, email, verified } : null);
     }

     getGateByProject(id:number, for_date:string) {
         //console.log('Calling get gate by project by id', id);
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getGateByProject(id)}`, {
             params: {
                for_date,
                 'version': 'v2'
             }
         });
     }

     getCompanyProjects(employer_id:number, include_disabled=false) {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getCompanyProjects(employer_id, include_disabled)}`, {});
     }

     getCompanyProjectsForUser(employer_id:number, userId:number) {
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getCompanyProjectsForUser(employer_id, userId)}`, {});
     }

     getEmployerLogoByName(employerName: string){
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getEmployerLogoByName(employerName)}`, {});
     }

     /**
      * @deprecated
      * shouldn;t be used.
      */
     getEmployersLogo(){
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getEmployersLogo}`, {});
     }

     updateProjectPartially(project: Project) {
         console.log('Calling update portal project..', project);
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.updateProjectPartially(project.id)}`, project);
     }

     sa_enrolInductionIntoFR(projectId, inductionRequestId) {
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.saRoutes.enrolInductionIntoFR(projectId, inductionRequestId)}`, {});
     }
     sa_checkDuplicateFaceIntoFR(projectId, inductionRequestId) {
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.saRoutes.checkDuplicateFaceIntoFR(projectId, inductionRequestId)}`, {});
     }

     deleteProject(projectId:number, shouldDelete:boolean) {
         console.log('Calling get/delete project..', projectId);
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.deleteProject(projectId, shouldDelete)}`, {});
     }

     addUpdateGateSupervisors(projectId:number, post) {
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.addUpdateGateSupervisors(projectId)}`, post);
     }

     getGateSupervisors(projectId:number) {
         return this.http.get<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getGateSupervisors(projectId)}`, {});
     }

     /**
      * @deprecated: Instead of loading all, we should be using `inducted-user-selector` component
      * @param projectId
      */
     getProjectInductedUsers(projectId: number) {
         return this.http.get<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProjectInductedUsers(projectId)}`, {});
     }

     /**
      * @deprecated: Instead of loading all, we should be using `inducted-user-selector` component
      * @param projectId
      */
     getProjectInductedUsersCA(employerId: number, projectId: number) {
         return this.http.get<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProjectInductedUsersCA(employerId, projectId)}`, {});
     }

     getProjectInductedAdmins(projectId: number, params: any = {}){
         return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProjectInductedAdmins(projectId)}`,{params} );
     }

     getOnSiteEntitiesOfProjectWithLocation(projectId: number, nowMs: number) {
         return this.http.get<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getOnSiteEntitiesOfProjectWithLocation(projectId, nowMs)}`, {});
     }

     getProjectFatigueRecords(projectId: number, pageNumber: string, limit: string = '50') {
         return this.http.get<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProjectFatigueRecords(projectId)}`, {
             params: {
                 pageNumber,
                 limit,
             }
         });
     }

     updateFatigueRecord(projectId: number, id: number, data) {
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.updateFatigueRecord(projectId, id)}`, data);
     }

     downloadProjectFatigueRecords(projectId: number, request, cb = () => {}) {
         this.httpService.downloadHandler(request, cb, this.httpService.routes.downloadProjectFatigueRecords(projectId));
     }

     getClockedInUsersOfProject(projectId: number) {
         return this.http.get<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getClockedInUsersOfProject(projectId)}`, {});
     }

     getVehicleTimeLogsByDay(projectId: number, params: any = {}) {
         return this.http.get<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getVehicleTimeLogsByDay(projectId)}`, {params});
     }

     getVehicleLogsByID(projectId: number, payload: any) {
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getVehicleLogsByID(projectId)}`, payload);
     }

     downloadVehicleDailyLogsExport(projectId: number, payload: any, cb = () => {}) {
         this.httpService.downloadHandler(payload, cb, this.httpService.routes.downloadVehicleDailyLogsExport(projectId));
     }

     updateVehicleAndLogs(projectId: number, vehicleId: number, req: any) {
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.updateVehicleAndLogs(projectId, vehicleId)}`, req);
     }

     getDistanceBwPostcodes(req: any) {
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getDistanceBwPostcodes()}`, req);
     }

     saveProjectResourcePlans(projectId: number, resource_plans: Array<any>) {
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.saveProjectResourcePlans(projectId)}`, {
             resource_plans
         });
     }

     updateProjectResourcePlanById(projectId: number, recordId: number, payload: any) {
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.updateProjectResourcePlanById(projectId, recordId)}`,payload);
     }

     deleteProjectResourcePlanById(projectId: number, recordId: number) {
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.deleteProjectResourcePlanById(projectId, recordId)}`,{});
     }

     bulkUploadProjectResourcePlans(projectId: number, payload: any) {
         return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.bulkUploadProjectResourcePlans(projectId)}`, payload);
     }

     downloadResourcePlannerExport(projectId: number, payload: any, cb = () => {}) {
         this.httpService.downloadHandler(payload, cb, this.httpService.routes.downloadResourcePlannerExport(projectId));
     }

     getProjectPlannedResources(projectId: number, day_of_yr: string) {
         return this.http.get<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProjectPlannedResources(projectId)}`, {
             params: {
                 day_of_yr,
             }
         });
     }

     saveProjectSetting(projectId: number, payload: any){
         return this.http.post(`${this.httpService.getBaseUrl()}${this.httpService.routes.saveProjectSetting(projectId)}`, payload);
     }

     getProjectReceivedBriefing(projectId: number, userId: any) {
         return this.http.get<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProjectReceivedBriefing(projectId, userId)}`)
     }

     downloadProjectRollCallReport(projectId: number, cb = () => {}) {
         return this.httpService.fileDownloader(this.httpService.routes.downloadProjectRollCallReport(projectId), {}, '', cb);
     }

     downloadRandomOnSiteOperativesReport(projectId: number, req, employerId: number=0, cb = () => {}) {
        return this.httpService.fileDownloader(this.httpService.routes.downloadRandomOnSiteOperativesReport(projectId, employerId), req, '', cb);
    }

    validatePostcode(postcode: string, countryCode:string) {
        return this.http.post(`${this.httpService.getBaseUrl()}${this.httpService.routes.validateProjectPostcode}`, { postcode, countryCode })
    }

    getHeatmapDataSA(projectId:number,payload?:any){
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getHeatmapDataSA(projectId)}`,payload)
    }

    getHeatmapDataCA(employerId:number,projectId:number,payload?:any){
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getHeatmapDataCA(employerId,projectId)}`,payload)
    }

    getHeatmapDataLiveTv(projectId:number,payload?:any){
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.getHeatmapDataLiveTv(projectId)}`,payload)
    }
 }

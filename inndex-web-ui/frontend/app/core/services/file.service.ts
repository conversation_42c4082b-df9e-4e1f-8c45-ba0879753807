import { Injectable } from '@angular/core';
import { HttpClient } from "@angular/common/http";
import { HttpService } from './http.service';

@Injectable()
export class FileService {
    constructor(
        private http: HttpClient,
        private httpService: HttpService,
    ) {

    }

    getFoldersTree(projectId, params) {
        return this.http.get(`${this.httpService.getFileUrl()}${this.httpService.routes.getFoldersTree(projectId)}`, {
            params
        });
    }
    getFiles(projectId, params) {
        return this.http.get(`${this.httpService.getFileUrl()}${this.httpService.routes.files(projectId)}`, {
            params
        });
    }
    getDeletedFiles(projectId) {
        return this.http.get(`${this.httpService.getFileUrl()}${this.httpService.routes.getDeletedFiles(projectId)}`);
    }
    getFileUrl(projectId, id) {
        return this.http.get(`${this.httpService.getFileUrl()}${this.httpService.routes.getFileUrl(projectId, id)}`);
    }
    getFile(projectId, id) {
        return this.http.get(`${this.httpService.getFileUrl()}${this.httpService.routes.getFile(projectId, id)}`);
    }
    postFileUrl(data) {
        return this.http.post(`${this.httpService.getFileUrl()}${this.httpService.routes.postFileUrl()}`, data);
    }
    createFileOrFolder(projectId, payload) {
        return this.http.post(`${this.httpService.getFileUrl()}${this.httpService.routes.files(projectId)}`, payload);
    }

    deleteFileOrFolder(projectId, fileId) {
        return this.http.delete(`${this.httpService.getFileUrl()}${this.httpService.routes.singleFile(projectId, fileId)}`);
    }

    bulkDelete(projectId, ids) {
        return this.http.post(`${this.httpService.getFileUrl()}${this.httpService.routes.bulkDelete(projectId)}`, { ids });
    }
    restoreFileOrFolder(projectId, fileId) {
        return this.http.post(`${this.httpService.getFileUrl()}${this.httpService.routes.restoreFile(projectId, fileId)}`, {});
    }

    auth(projectId) {
        return this.http.post(`${this.httpService.getFileUrl()}${this.httpService.routes.authValidate(projectId)}`, {});
    }

    updateFileOrFolder(projectId, id, payload) {
        return this.http.put(`${this.httpService.getFileUrl()}${this.httpService.routes.singleFile(projectId, id)}`, payload);
    }
    bulkDownload(projectId, ids) {
        return this.http.post(`${this.httpService.getFileUrl()}${this.httpService.routes.bulkDownload(projectId)}`, { ids }, {
            observe: 'response',
            responseType: 'blob'
        });
    }

    getFilesByIds(projectId, ids) {
        return this.http.post(`${this.httpService.getFileUrl()}${this.httpService.routes.getFilesByIds(projectId)}`, {
            ids
        });
    }
}

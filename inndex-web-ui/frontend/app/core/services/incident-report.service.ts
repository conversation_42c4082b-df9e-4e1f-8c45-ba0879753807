/**
 * Created by <PERSON><PERSON><PERSON> on 14/05/2020.
 */
import {Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {HttpService} from './http.service';

@Injectable()
export class IncidentReportService {
    constructor(
        private http: HttpClient,
        private httpService: HttpService
    ) {}

    getProjectIncidentReports(projectId: number, params: any = {}) {
        return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getProjectIncidentReports(projectId)}`,{
            params
        });
    }

    getCompanyIncidentList(companyId: number, params: any = {}) {
        return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getCompanyIncidentList(companyId)}`,{
            params
        });
    }

    createUpdateIncidentReport(req, projectId: number) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.createUpdateIncidentReport(projectId)}`, req)
    }

    updateIncidentReport(req, projectId: number, id: number) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.updateIncidentReport(projectId, id)}`, req)
    }

    updateIncidentReportCA(req, employerId: number, id: number) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.updateIncidentReportCA(employerId, id)}`, req)
    }

    shareIncidentReport(req, id:number) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.shareIncidentReport(id)}`, req);
    }

    downloadIncidentReport(request,  id: number, cb = () => {}) {
        return this.httpService.downloadHandler(request, cb, this.httpService.routes.downloadIncidentReport(id));
    }

    closeOutIncidentReport(projectId: number, req, id: any) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.closeOutIncidentReport(projectId, id)}`, req)
    }

    closeOutIncidentAction(req, projectId: number, id: number) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.closeOutIncidentAction(projectId, id)}`, req)
    }

    saveIncidentAlertRecipients(req, companyId: number) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.saveIncidentAlertRecipients(companyId)}`, req)
    }

    getIncidentAlertRecipients(companyId: number, params: any = {}) {
        return this.http.get(`${this.httpService.getBaseUrl()}${this.httpService.routes.getIncidentAlertRecipients(companyId)}`,{
            params
        });
    }

    deleteIncidentAlertRecipient(req, companyId: number) {
        return this.http.post<any>(`${this.httpService.getBaseUrl()}${this.httpService.routes.deleteIncidentAlertRecipient(companyId)}`, req)
    }
}

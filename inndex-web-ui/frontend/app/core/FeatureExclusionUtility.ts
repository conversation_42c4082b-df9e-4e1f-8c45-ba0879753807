import {Injectable} from "@angular/core";
import {AuthService} from "./services/auth.service";

@Injectable()
export class FeatureExclusionUtility {
    constructor(
        private authService: AuthService,
    ) {

    }

    showEmploymentStartDate(country_override?){
        return this.authService.checkIfShouldBeShown(`profile.employment.start_date`, country_override);
    }
    showTypeOfEmployment(country_override?){
        return this.authService.checkIfShouldBeShown(`profile.employment.employment_type`, country_override);
    }
    showMinWage(country_override?){
        return this.authService.checkIfShouldBeShown(`profile.employment.min_wage`, country_override);
    }
    showNIN(country_override?){
        return this.authService.checkIfShouldBeShown(`profile.employment.nin`, country_override);
    }
    showEmpNbr(country_override?){
        return this.authService.checkIfShouldBeShown(`profile.employment.employee_number`, country_override);
    }
    isEmpNbrMandatory(country_override?){
        return this.authService.checkIfShouldBeMandatory(`profile.employment.employee_number`, country_override);
    }
    showProfileMedicalAssessment(){
        return this.authService.checkIfShouldBeShown(`profile.medical-assessment`);
    }
    showProfileHealthAssessment(){
        return this.authService.checkIfShouldBeShown(`profile.health-assessment`);
    }
    showInductionHealthAssessment(){
        return this.authService.checkIfShouldBeShown(`induction.health-assessment`);
    }

    emergencyContactNameIsRequired(country_override?){
        return this.authService.checkIfShouldBeMandatory(`profile.contact.emergency_contact_name`, country_override);
    }
    emergencyContactNoIsRequired(country_override?){
        return this.authService.checkIfShouldBeMandatory(`profile.contact.emergency_contact_no`, country_override);
    }

    showProjectPostalCode(country_override?){
        return this.authService.checkIfShouldBeCreated(`project.postcode`, country_override);
    }

    showProfilePostalCode(country_override?){
        return this.authService.checkIfShouldBeCreated(`profile.contact.postcode`, country_override);
    }

    generateLabels(type:string = 'postcode-lookup') {
        let placeholder = 'Postcode/Zipcode';
        let label = 'Postcode/Zipcode';
        let error = 'postcode';

        if (type === 'address-lookup') {
            placeholder = 'Address';
            label = 'Address';
            error = 'address';
        } else if (type === 'postcode-lookup') {
            placeholder = 'Postcode/Zipcode';
            label = 'Postcode/Zipcode';
            error = 'postcode';
        }

        return { placeholder, label, error };
    }

}

import {UserRolePermission} from "./user-role-permission.model";

export class MediaResource {
    key?: number;
    file_ref?: number;
    content?: string;
    lang?: string;
    type: 'file' | 'url' | 'html';
    is_default?: boolean;
}
export class ProjectLocation {                // populated by Weather Sync attachLocationKey call
    lat?: number;
    long?: number;
    region?: string;
    country?: string;
    admin_district?: string;
}


/**
 * Created by spatel on 24/9/18.
 */
export class Project {
    id?: number;
    createdAt?: number;
    name?: string;
    use_prefix?: boolean = true;
    project_initial?: string;
    record_id?: string;
    project_number?: string;
    description?: string;
    // company_name?: string;
    logo_file_id?: any;
    contractor?: string;
    main_contact_name?: string;
    /**
    * @deprecated Use `main_contact_number_obj` instead of it. It will removed once get removed from DB model.
    */
    main_contact_number?: string;
    main_contact_number_obj?: mobileDetail;
    created_by?: number | any;
    edited_by?: number;
    is_active?: number;
    disabled_on?: number;
    template_identifier?: string;

    admins?: Array<UserRolePermission>;
/*
    // @deprecated since Request Change 526
    has_c_lens_policy?: boolean = true;
    // @deprecated since Request Change 526
    c_lens_policy?: string = ''; //`In the Railway environment and for personal track safety (PTS), it is essential that any personnel who wear contact lenses must abide by the following directive.\n\nA pair of prescription spectacles of equal strength and clarity must be carried at all times. This enables the operative to carry on working safely if a contact lens is lost or damaged.\nAll contact lens wearers must make themselves known to the Controller of Site Safety / Safe Work Leader (COSS/SWL) at the pre-work briefing. Failure to bring along the requisite pair of spectacles will result in the operative not being allowed on site.`;
    // @deprecated since Request Change 526
    has_d_and_a_policy?: boolean = true;
    // @deprecated since Request Change 526
    d_and_a_policy?: string = ``;
    // @deprecated since Request Change 526
    has_working_hr_agreement?: boolean = true;
    // @deprecated since Request Change 526
    working_hr_agreement?: string = '';//`I fully accept and will abide by the site working hours agreement of 12 hours door to door.`;
*/

    has_media_content?: boolean = false;
    media_resources?: Array<MediaResource> = [];
    media_file_ids?: Array<{
        id?: number;
        file_url?: string;
        file_mime?: string;
    }|any> = [];
    has_html_media?: boolean = false;
    html_media_url?: string;
    media_declaration_content?: string = `I agree I have viewed and understood all information provided`;
    declarations?: any = [];

    logo_file_url?:string;
    start_date?:any;
    end_date?:any;
    value?:string;
    project_type?:string;
    type_of_works?:string;
    site_main_risks?:string;
    further_policies?: Array<{
        order?: number;
        key?:string,
        policy_name?: string;
        policy?: string;
        policy_ref?: any;
        is_default?: boolean;
        is_text?: boolean;
    }> = [];
    client?:string;
    main_contract_type?:string;
    designer?:string;
    stakeholder?:string;
    postcode?:string;
    is_passport_require?: boolean = false;
    is_cscs_require?: boolean = false; // mandatory to submit either CSCS, CPCS, CISRS, ECS, EUSR, NPORS, PTS, CCDO, SIA, FISA, NPTC, LANTRA, NEBOSH, IOSH, SSSTS, SMSTS or SafePass
    other_doc_required?: Array<string> = [];
    competency_exception_list?: Array<any> = [];
    _user_excluded_from_mandatory_doc?: boolean = false;    // gets populated when API caller is part of `competency_exception_list`
    blacklist_user_on_expiry?: number = null; // induction transition_to state, on user's Mandatory doc expiry, ideally it will be 4 - blocked state.
    project_gates?: Array<{
        gate_name?: string;
        time_slot?: any;
    }> = [];
    is_fors_compliant?:boolean;
    delivery_management_status?:boolean;
    default_in_duration?:number = 8;
    has_default_time?:boolean = true;
    project_category?:string;
    parent_company?: any;
    pin?: any;
    induction_pin?: any;
    project_footer?: any;
    project_section_access?: ProjectSectionAccess = {};
    company_additional_project_section_access?: ProjectSectionAccess = {};
    fatigue_management_status?: boolean;
    site_hours_daily?: number;
    total_hours_daily?: number;
    site_hours_weekly?: number;
    total_hours_shifts?: number;
    total_duty_periods_biweekly?: number;
    // close_call_phrase?:string = `Close Call`;
    // Below properties denotes Current Logged In user's permission for given DEFAULT Project record (If present)
    _my_access_id?: number; // If this is present, then current logged in user has access to project, with below designations & flags
    _my_designations?: Array<string>;
    _my_flags?: {
        is_default?: boolean;
        is_delivery_manager?: boolean;
    };
    _my_permission?: Array<string>;
    _limited_access_change?: boolean;
    _company_admin?: boolean;
    has_ib_checklists?: boolean;
    first_ib_id?: number;

    gate_supervisors?: Array<any>;
    _has_quiz_questions?: boolean;
    _has_additional_questions?: boolean;
    _is_delivery_manager_only?: boolean;
    _has_restricted_access?: boolean;
    closecall_setting?: {
        photo_disallowed?: boolean; // Prevent users from taking photos while creating close-call
        alternet_phrase?:string,
        tagged_companies?: [{
            "company_id"?: any,
            "email"?: string
        }]
    } = {
        alternet_phrase: `Close Call`,
        tagged_companies: [{
            "company_id": '',
            "email": ''
        }]
    };
    goodcall_setting?: {
        photo_disallowed?: boolean; // Prevent users from taking photos while creating good-call / observations
        tagged_companies?: [{
            "company_id"?: any,
            "email"?: string
        }]
    } = {
        tagged_companies: [{
            "company_id": '',
            "email": ''
        }]
    };
    custom_field?: {
        vehicle_managers?: Array<number>;
        qcl_phrase?:string,
        qcl_phrase_singlr?:string,
        tb_phrase?:string,
        tb_phrase_singlr?:string,
        wpp_phrase?:string,
        wpp_phrase_singlr?:string,
        take5_phrase?:string,
        take5_phrase_singlr?:string,
        da_phrase?:string,
        da_phrase_singlr?:string,
        cn_phrase?:string,
        cn_phrase_singlr?:string,
        dn_phrase?:string,
        dn_phrase_singlr?:string,
        gc_phrase?:string,
        gc_phrase_singlr?:string,
        obrs_phrase?:string,
        obrs_phrase_singlr?:string,
        powra_phrase?: string,
        powra_phrase_singlr?: string,
        rams_phrase?: string,
        rams_phrase_singlr?: string,
        cc_phrase?: string,
        cc_phrase_singlr?: string,
        induction_phrase?: string,
        induction_phrase_singlr?: string,
        assets_phrase?: string,
        assets_phrase_singlr?: string,
        permit_phrase?: string,
        permit_phrase_singlr?: string,
        clock_in_declarations?: Array<{
            key: number;
            message: string;
            answer_type: string; // 'y-n' | 'text'
        }>;
        equipment_managers?: Array<number>;
        timezone?: string;
        smartsheet_workspace?: string;
        procore_integration?: boolean;    // Procore feature status
        manual_time_entry?: boolean;    // Full Manual Time Entry feature status
        induction_slot_booking?: boolean;    // Induction Slot booking feature status
        dn_smartscan?: boolean;    // DN, Smart scan feature status
        timesheet_week_end?: number;             // 1 = Monday ...7 = Sunday
        currency_code?: string;             // ISO currency code string `GBP`
        disable?: {
            view_induction?: boolean;
            view_medication_modal?: boolean;
            gender_chart?: boolean;
            induction_media?: boolean;                  // Disable induction media btn
            procore_permissions?: boolean;              // Disable procore permissions
            induction_export_on_procore?: boolean;
            time_log_export_on_procore?: boolean;
            company_portal?: boolean;       // Hide given project from company portal projects list
            block_button?: boolean;
        };
        country_code?: string;      // Will be used to accurately derive Weather Location key
        location?: ProjectLocation
        auto_shift_override?: boolean|number;      // When present: Auto shift will be disabled fully, i.e. `true`, When a `number`, will use that as cut_off_hr with Auto shift disabled.
        fatigue_managers?: Array<number>;
        obsr_tagged_companies?: Array<any>;
        observation_categories?: Array<any>;
        handling_equipment_status?: boolean;
        handling_equipment?: Array<any>;
        alternate_userlist_toolboxtalks?: boolean;
        alternate_userlist_taskbriefings?: boolean;
        alternate_userlist_wpps?: boolean;
        alternate_userlist_rams?: boolean;
        maximum_booking_time?: string,
        maximum_booking_status?: boolean,
        booking_approval_process?: boolean,
        has_supply_chain_companies?: boolean;
        supply_chain_companies?: Array<number>;
        show_assets_in_plants_status?: boolean;
        briefing_signatures?: {
            toolbox_talks?: number,
            task_briefings?: number,
            rams?: number,
            work_package_plans?: number,
            take_5s?: number
        };
        has_rams_in_induction?: boolean;
        temp_works_managers?: Array<number>;
        default_booking_slot?: number;
    } = {
        qcl_phrase: `ITPs`,
        qcl_phrase_singlr: `ITP`,
        tb_phrase: `Task Briefings`,
        tb_phrase_singlr: `Task Briefing`,
        wpp_phrase: `Work Package Plans`,
        wpp_phrase_singlr: `Work Package Plan`,
        take5_phrase: `Take 5s`,
        take5_phrase_singlr: `Take 5`,
        da_phrase: `Daily Activities`,
        da_phrase_singlr: `Daily Activity`,
        cn_phrase: `Collection Notes`,
        cn_phrase_singlr: `Collection Note`,
        dn_phrase: `Delivery Notes`,
        dn_phrase_singlr: `Delivery Note`,
        gc_phrase: `Good Calls`,
        gc_phrase_singlr: `Good Call`,
        obrs_phrase: 'Observations',
        obrs_phrase_singlr: 'Observation',
        powra_phrase: 'POWRA',
        powra_phrase_singlr: 'POWRA',
        rams_phrase: 'RAMS',
        rams_phrase_singlr: 'RAMS',
        cc_phrase: 'Close Calls',
        cc_phrase_singlr: 'Close Call',
        induction_phrase: 'Inductions',
        induction_phrase_singlr: 'Induction',
        assets_phrase: 'Asset Management',
        assets_phrase_singlr: 'Asset Management',
        permit_phrase: "Permits",
        permit_phrase_singlr: "Permit",
        vehicle_managers: [],
        timezone: 'Europe/London',
        auto_shift_override: true, // true = Auto shift disabled, false = Auto shift enabled, number = Auto shift cut off hr
        equipment_managers: [],
        fatigue_managers: [],
        observation_categories: [],
        maximum_booking_status: false,
        maximum_booking_time: '00:30 minutes',
        has_supply_chain_companies: false,
        supply_chain_companies: [],
        show_assets_in_plants_status: true,
        briefing_signatures: {
            toolbox_talks: 1,
            task_briefings: 1,
            rams: 2,
            work_package_plans: 1,
            take_5s: 1
        },
        has_rams_in_induction: false,
        temp_works_managers: [],
        default_booking_slot: 30,
    };
    cow_setting?: {
        cow_phrase?:string,
        company_project_blocks?:number,
        company_project_levels?: number,
        non_numeric_levels?: any,
        cow_tagged_data?: [{
            user_ref?: number;
            email?: string;
            name?: string;
        }],
        cow_site_drawings?: [{
            site_level?: string;
            file_id?: any;
        }]
    } = {
        cow_phrase: `Clerk of Works`,
        company_project_blocks: null,
        company_project_levels: null,
        non_numeric_levels: [],
        cow_tagged_data: [{
            user_ref: null,
            email: '',
            name: ''
        }],
        cow_site_drawings: [{
            site_level: '',
            file_id: ''
        }]
    };
    division_ref?: any;
    close_call_custom_fields?: any = [];
    uk_districts?: any = [];
}

interface mobileDetail {
    code: string;
    number: string;
}

export interface ProjectSectionAccess {
    // dashboard?:boolean; Will be added as true, for all inherited project
    close_calls?:boolean;
    take_5s?:boolean;
    toolbox_talks?:boolean;
    permit?:boolean;
    progress_photos?:boolean;
    delivery_notes?:boolean;
    time_management?:boolean;
    timesheet?:boolean;
    resource_planner?:boolean;
    daily_activities?:boolean;
    clerk_of_works?:boolean;
    powra?:boolean;
    incident_report?:boolean;
    inspection_tour?: boolean;
    ib_checklist?: boolean; // This is used to show the inspection section in the project sidebar it is dependent on IbChecklist items.
    add_shadow_user?: boolean; // This is show add-new-user btn on Inductions screen
    good_calls?:boolean;
    observations?:boolean;
    dashboards?: boolean;
    site_messaging?:boolean;
    task_briefings?:boolean;
    asset_vehicles?:boolean;
    work_package_plan?:boolean;
    rams?: boolean;
    asset_equipment?:boolean;
    quality_checklist?:boolean;
    collection_notes?:boolean;

    has_blocks?:boolean; // These 2 flag denote if the current project/company project has blocks or levels available.
    has_levels?:boolean;
    asset_temporary_works?: boolean;
    documents_tool?: boolean;
}

export class ProjectFeaturePermission {
    asset_management?: string = 'unlocked';
    asset_vehicles?: string = 'unlocked';
    asset_equipment?: string = 'unlocked';
    asset_temporary_works?: string = 'unlocked';
    clerk_of_works?: string = 'unlocked';
    close_calls?: string = 'unlocked';
    collection_notes?: string = 'unlocked';
    daily_activities?: string = 'unlocked';
    dashboards?: string = 'unlocked';
    delivery_notes?: string = 'unlocked';
    documents_tool?: string = 'unlocked';
    fatigue_management_status?: string = 'unlocked';
    good_calls?: string = 'unlocked';
    incident_report?: string = 'unlocked';
    ib_checklist?: string = 'unlocked';
    observations?: string = 'unlocked';
    powra?: string = 'unlocked';
    progress_photos?: string = 'unlocked';
    permit?: string = 'unlocked';
    quality_checklist?: string = 'unlocked';
    rams?: string = 'unlocked';
    resource_planner?: string = 'unlocked';
    site_messaging?: string = 'unlocked';
    take_5s?: string = 'unlocked';
    task_briefings?: string = 'unlocked';
    time_management?: string = 'unlocked';
    timesheet?: string = 'unlocked';
    toolbox_talks?: string = 'unlocked';
    delivery_management_status?: string = 'unlocked';
    work_package_plan?: string = 'unlocked';
}

export class ProjectTimesheetConfigure {
    id?:string;
    local_id?:string;
    configuration_title?: string;
    shift_type?:string;
    apply_to_days?:string;
    working_hours_multiplier_toggle:boolean = false;
    shift_start_time_seconds?:number;
    shift_end_time_seconds?:number;
    early_clock_in_toggle:boolean = false;
    max_overtime_early_clock_in_seconds?:string;
    max_overtime_late_clock_out_seconds?:string;
    early_clock_in_rounding?:any;
    clock_in_overtime_rate_multiplier?:string;
    late_clock_out_toggle:boolean = false;
    late_clock_out_rounding?:any;
    clock_out_overtime_rate_multiplier?:string;
    break_time_seconds?:number;
    late_clock_in_rounding?:any;
    early_clock_out_rounding?:any;
    is_default?:boolean = false;
}

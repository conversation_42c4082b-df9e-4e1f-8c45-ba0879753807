/**
 * Created by spatel on 6/10/18.
 */
import {Comment} from "./comment.model";
import {InductionAdditionalData} from "./induction-additional-data.model";
import {TravelTimeOverride} from "./travel-time-override.model";
import {PagedResponse} from "./company-time-management.page.model";
import {InductionQuestions} from "./induction-question.model";

export class InductionRecentChange {
    type: string;   // `profile.personal:updated`, `profile.contact:updated`, `profile.employment:updated`, `profile.medical:updated`, `profile.health:updated`, `profile.doc:updated`,
    creator_ref: number;
    creator_name: string;
    timestamp: number;
    changes: Array<{
        entity: string; // field that got changed, e.g. dob, job_role, postcode
        from: string | any;
        to: string | any;
        ref: number;                //  id reference of entity
        sub_entity?: string;        //  will be present for  `profile.doc:updated` only
        newly_added?: boolean;      //  will be present for  `profile.doc:updated` only
        removed?: boolean;
    }>;
    // removed_ids?: [];   //  will be present for  `profile.doc:updated` only
}
export class RtwCheckResult {
    source?: string;
    code?: number;
    fetchedAt?: number;
    status?: string;
}
export class NotFoundRtwCheckResult extends RtwCheckResult {
    code?: number = null;
    fetchedAt?: number;
    status?: string = 'Not Found';
    constructor(code?: number, empty: boolean = false) {
        super();
        this.code = code;
        this.fetchedAt = (code && empty) ? (new Date()).getTime() : null;
    }
}

export class InductionRequest {
    id?: number;
    createdAt?: number;
    updatedAt?: number;
    project_ref?: any;
    user_ref?: any;
    creator_name?: string;
    user_sign?: string;
    inductor_ref?: any;
    _postcode_is_correct?: string;
    travel_time?: {
        to_work?: string;
        to_home?: string;
        distance_matrix?: any;
        overrides?: Array<TravelTimeOverride>;
        vehicle_info?: {
            errorMessage?: string;
            registrationNumber?: string;
            fuelType?: string;
            make?: string;
            co2Emissions?: string;
        };
    } = {};
    _active_travel_time?: any;
    travel_method?: string;
    vehicle_reg_number?: string;

    fit_undertake_role?: number;
    fit_to_work?: number;
    comply_hour_agreement?: number;
    site_directive_selection?: string;
    accept_drug_alcohol_pol?: number;


    // @deprecated
    //site_health_assessment?: any = [];
    reportable_medical_conditions?: string;
    rmc_detail?: string;
    on_long_medication?: string;
    medications?: Array<any> = [];
    any_side_effects?: string;
    any_side_effect_detail?: string;

    user_doc_ids?: Array<any> = [];
    accepted_declarations?: Array<any> = [];
    declarations?: {
        supervisor?: Array<any>;
        operator?: Array<any>;
    } = {};
    accepted_further_policies?: Array<any> = [];
    status_code?: number;
    status_message?: string;
    comments?: Array<Comment> = [];
    edited_by?: any;
    additional_data?: InductionAdditionalData = new InductionAdditionalData;

    all_media_watched?: boolean;
    optima_badge_number?: number;
    confirm_detail_valid?: number;
    accepting_media_declaration?: boolean;

    travel_time_to_work?: string = 'PT20M';
    travel_time_to_home?: string = 'PT20M';
    inductor_email?: string;
    induction_slot?: {
        id: number;
        seconds: number;
        location: string;
    };
    _has_rtw_doc_code?: string;
    _need_rtw_check?: boolean = true;
    rtw_doc_code?: string;
    rtw_check_result?: RtwCheckResult;
    induction_answers?: any = [];
    _induction_quiz?: InductionQuestions;
    induction_question_answers?: any = {};
    recent_changes?: Array<InductionRecentChange> = [];
    re_review_logs?: Array<any> = [];
    additional_induction_question?: Array<any> = [];
    conduct_cards?: Array<any> = [];
    rams_register?: any = null;
}

export class ProjectInductionsListRow {
    id?:number;
    record_id?:number;
    first_name?: string;
    middle_name?: string;
    last_name?: string;
    name?:string;
    user_ref?: number;
    employer?:string;
    // employment_company?:string;
    createdAt?:string;
    induction_slot?:any;
    status_code?:number;
    status_message?:string;
    comments?: Array<any>;
    rtw_doc_code?: string;
    rtw_check_result?: RtwCheckResult;
    _ppac?: {
        status: string;
        style: {textClass: string;};
    };
    _status_code_meta?: any;
    _is_movable?: boolean;
    recent_changes?: Array<InductionRecentChange>;
    additional_data?: {
        user_docs?: any;
        health_assessment_answers?: any;
        medical_assessments_answers?: any;
        employment_detail?: any;
        user_info?: {
            name?: string;
            email_verified_on?: any;
        };
    };
    reportable_medical_conditions?: string;
    on_long_medication?: string;
    conduct_cards?: Array<any>;
    uac?: any;

    // virtual keys
    health_issues: any;
    medical_issues: any;
    _block_tooltip?: string;
    _has_medical_condition?: boolean;
    receivedBriefings?: any;
}

export class ProjectInductionsPage extends PagedResponse{
    records: Array<ProjectInductionsListRow> = [];
    total_approved_count?: number;
}

export const META_STATUS_CODES: any = {
    REJECTED: 0,
    PENDING: 1,
    IN_REVIEW: 6,
    APPROVED: 2,
    CHANGE_REQUESTED: 3,
    BLOCKED: 4,
    BLACKLISTED: 5,
};

export const META_STATUS_CODES_LIST: Array<any> = [
    {label:'Pending', color: 'var(--warning)', text_color: 'text-dark', badge_class: 'btn-warning', code: 1},
    {label:'In Review', color: 'var(--secondary)', text_color: 'text-white', badge_class: 'bg-secondary', code: 6},
    {label:'Rejected', color: 'var(--danger)', text_color: 'text-white', badge_class: 'badge-danger', code: 0},
    {label:'Change Requested', color: 'var(--warning)', text_color: 'text-dark', badge_class: 'bg-warning', code: 3},
    {label:'Approved', color: 'var(--success)', text_color: 'text-dark', badge_class: 'badge-success', code: 2},
    {label:'Project Block', color: 'var(--danger)', text_color: 'text-white', badge_class: 'badge-danger', code: 4},
    {label:'Company Block', color: 'var(--dark)', text_color: 'text-white', badge_class: 'badge-dark', code: 5}, // BLACKLISTED
];

// : {[status: string]: { label: string; textClass: string; icon: string; progressIcon: string;}}
export const PPAC_ID_STATUS_CODES = {
    PENDING: {label: 'Pending', textClass: 'text-secondary', icon: '', progressIcon: ''},
    GO_TO_SITE: {label: 'Go to Site', textClass: 'text-success', icon: '', progressIcon: ''},
    ACTIVE: {label: 'Active', textClass: 'text-success', icon: '', progressIcon: ''},
    REJECTED: {label: 'Rejected', textClass: 'text-danger', icon: '', progressIcon: ''},
    EXPIRED: {label: 'Expired', textClass: 'text-danger', icon: '', progressIcon: ''},
    NOT_FOUND: {label: 'Not Found', textClass: 'text-secondary', icon: '', progressIcon: ''},
};

export const STATUS_CAN_TRANSIT = (status_code: number) => {
    return [META_STATUS_CODES.IN_REVIEW, META_STATUS_CODES.PENDING, META_STATUS_CODES.CHANGE_REQUESTED].includes(status_code)
};

export class CclCheckData {
    _data?: {
        data?: {
            user_email: string;
            user_competency_grade: string;
        };
        message?: string;
        success?: boolean;
    };
    fetchedAt?: number;
    competency_grade: string;
    competency_label: string;
    competency_description: string;
    verification_url: string;
}
// This is used to display the CCL competency grades in the UI.
// `label` or `gradeDescription` are used only when  `competency_label` or `competency_description` are not available in induction `CclCheckData`.
export const CCL_COMPETENCY_GRADES = {
    A: {label: 'Complete and Signed off', grade: 'A', textClass: 'text-success', icon: 'check_circle', gradeDescription: 'Signed off by manager, complete and all actions completed.'},
    B: {label: 'Signed off, requires action', grade: 'B', textClass: 'text-info', icon: 'warning', gradeDescription: 'Signed off by manager + incomplete => requires actions from individual.'},
    C: {label: 'Survey complete, awaiting review', grade: 'C', textClass: 'text-warning', icon: 'description', gradeDescription: 'Survey completed and awaiting manager to review.'},
    D: {label: 'Survey not started', grade: 'D', textClass: 'text-secondary', icon: 'schedule', gradeDescription: 'Survey not started.'},
    NULL: {label: 'User with no grade', grade: 'NULL', textClass: 'text-danger', icon: 'block', gradeDescription: 'Live user with NULL in their Grade.'},
    N_A: {label: 'No user found', grade: 'n/a', textClass: 'text-danger', icon: 'block', gradeDescription: 'User has left the business so is no longer a member of staff OR This person does not exist in the data.'},
};
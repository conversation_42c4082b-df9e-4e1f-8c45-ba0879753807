export type AttachmentFile = {
    createdAt?: number;
    id?: number;
    file_mime?: string;
    name?: string;
    file_url?: string;
    sm_url?: string | null;
    md_url?: string | null;
    processed?: number;
    file_type?: number;
    category?: string | null;
    img_translation?: [];
    user_id?: number;
    visitor_ref?: string | null;
};

export class ProjectIncidentReport {
    id?: number;
    record_id?: string;
    project_ref?: any;
    user_ref?: any;
    user_revision_ref?: any;
    location?: string;
    incident_details?: string;
    incident_date?: number | string;
    _incident_date?: any;
    _incident_time?: any;
    injury_details?: Array<{
        injury_type?: string,
        affacted_part?: string,
        injured_side?: string
    }> = [];
    is_onsite_treatment?: any;
    site_treatment?: {
        details?: string;
        administered_by?: string;
    } = {
        details: '',
        administered_by: ''
    };
    attachment_file_ids?: Array<{
        description?: string,
        file?: number | AttachmentFile
    }> = [];
    any_witnesses: boolean;
    witnesses?: Array<{
        person_type?: string,
        f_name?: string,
        l_name?: string,
        contact_number?: {code: any, number: string},
        comments?: string
    }> = [];
    incident_type?: string;
    incident_category?: string;
    abuse_type?: string;
    act_type?: string;
    is_chartered?: boolean;
    potential_severity?: string;
    actual_severity?: string;
    actual_outcome?: string;
    person_affected?: Array<{
        user_ref?: number,
        person_type?: string,
        f_name?: string,
        l_name?: string,
        contact_number?: {code: any, number: string},
        job_title?: string,
        gender?: string,
        address?: string,
        contact?: string
    }> = [];
    vehicle_details?: {
        "reg_number"?: string,
        "type"?: string,
        "status"?: string,
        "speed"?: string,
        "was_stationary"?: boolean,
        "passengers_count"?: number,
        "was_unattanded"?: boolean,
        "damage_details"?: string
    } = {};
    is_thirdparty_vehicle?: boolean;
    thirdparty_vehicle?: Array<{
        "category"?: string,
        "name"?: string,
        "address"?: string,
        "contact_number"?: {code: any, number: string},
        "insurer"?: string,
        "person_damage"?: boolean,
        "property_damage"?: boolean,
        "vehicle_damage"?: boolean,
        "details"?: string
    }> = [];
    loc_env_details?: {
        "location_type"?: string,
        "visibility"?: string,
        "location"?: string,
        "surface_state"?: string,
        "other_road_type"?: string,
        "other_visibility"?: string,
        "road_type"?: string,
        "road_width"?: string,
        "speed_limit"?: string
    } = {};
    driver_details?: {
        "f_name"?: string,
        "l_name"?: string,
        "contact_number"?: {code: any, number: string, alpha2Code?: string},
        "job_title"?: string,
        "address"?: string,
        "contact"?:string
    } = {};
    action_details?: string;
    status?: number;
    lighting_condition?: string;
    injury_caused_by?: string;
    injury_caused_by_additional?: string;
    weather_conditions?: any;
    root_causes?: Array<{}|any> = [];
    incident_actions?: Array<{}|any> = [];

    finalised?: boolean;
    is_person_off_work?: boolean;
    date_absent?: any = {};
    company_ref?: any;
    project_company?: any;
    closed_out_date?: number;
    _closed_out_date?: any;
    closeout_comment?: string;
    closeout_user_ref?: number;
    closeout_sign?: string;
    incident_events?: Array<{}|any> = [];
    incident_harm?: Array<{}|any> = [];
    investigation_findings?: Array<{}|any> = [];
    similar_incidents?: Array<{}|any> = [];
    immediate_causes?: Array<{}|any> = [];
    underlying_causes?: Array<{}|any> = [];
    incident_conclusions?: Array<{}|any> = [];
    expected_root_cause?:string = '';
    incident_recommendations?: Array<{}|any> = [];
    review_photo_ids?: Array<{}|any> = [];
    relevant_personnel_user_refs?: Array<{}|any> = [];
    finalised_at?: string;
    is_closed?: boolean;
};
export enum InjuryIncidentCategory {
    FellFromHeight = 'Fell from height'
};
export enum PersonType {
    Employee = 'Employee'
}
export enum IncidentType {
    Injury = "Injury",
    Health = "Health",
    RoadTraffic = "Road Traffic",
    DamageOrLoss = "Damage or Loss",
    ViolenceOrAbuse = "Violence or Abuse",
    Environmental = "Environmental",
    ServiceStrike = "Service Strike",
    NearMiss = 'Near Miss',
    UnsafeActOccurrence = 'Unsafe Act/Occurrence'
}
export enum ThirdPartyCategory {
    Person = 'Person',
    Property = 'Property',
    Vehicle = 'Vehicle'
}


import { User } from "./user.model";
import { CreateEmployer } from "./CreateEmployer.model";
import { PagedResponse } from "./company-time-management.page.model";

export interface DocumentItem {
    id?: string;
    name?: string;
    type?: string;
    createdBy?: {
        id: number
        user_name: string
    };
    createdAt?: number;
    children?: DocumentItem[];
    files?: any[];
    deletedAt?: number;
    deletedBy?: {
        id: number
        user_name: string
    };
    hasChildrenFolder?: boolean;
    file_key?: string;
    extension?: string;
    parent_id?: number;
    project_id?: number;
    error?: string;
    permissions?: DocumentPermission[];
}


export interface DocumentPermissionRequest {
    title: string
    access_level: string
    perm_type: string
    company_ref: number | CreateEmployer
    user_ref: number | User
    documents: string[]
    files?: string
    projectId?: number
    createdBy?: {
        id: number
        user_name: string
    }
}
export interface DocumentPermission extends DocumentPermissionRequest{
    id?: number
}

export class DocumentsPage extends PagedResponse {
    records: Array<DocumentItem> = [];
}

import {UserRolePermission} from "./user-role-permission.model";

export class CompanyCscsConfig {
    enabled: boolean = false;
    verification_mandatory: boolean = false;
    created_at?: number;
    _company_ref: number;
}
export class CompanyRtwConfig {
    enabled: boolean = false;
    // excluded_projects: Array<number> = [];
    link?: string;
    induction_allowed_on?: Array<string> = [];
    induction_approval_on?: Array<string> = [];
    allow_edit_on?: Array<string> = [];
    _company_name: string;
}

export class CreateEmployer {
    name?: string;
    id?: number;
    has_company_portal?: boolean;
    max_projects?: number = 1;
    has_mates_in_mind?: boolean;

    admins?: Array<UserRolePermission>;

    company_initial?: string;
    country_code?: string;
    logo_file_id?: any;
    logo_file_url?:string;
    logo_file?:any;

    // This is not in use from 12/09/2024, UI is not referring to this alias name.
    projects_alias?: string;
    // Here we are taking care of "Super-admin level status" ONLY.
    // Super-admin feature status is stored within employer.
    // Company level setting status is being stored into company-setting table.
    features_status?: {
        toolboxtalks?:boolean;
        employees?:boolean;
        time_management?:boolean;
        e_learning?:boolean;
        add_project?:boolean;
        skills_matrix?:boolean;
        incident_reports?:boolean;
        user_blacklisting?:boolean;
        health_assessment?:boolean;
        medical_assessment?:boolean;
        induction_health_q?: boolean;
        company_messaging?:boolean;
        permit?:boolean;
        restrict_project_level_admins?:boolean;
    } = {
        toolboxtalks : false,
        employees: false,
        time_management: false,
        e_learning: false,
        add_project: false,
        skills_matrix: false,
        incident_reports: false,
        user_blacklisting: false,
        health_assessment: true,
        medical_assessment: true,
        induction_health_q: true,
        permit: false,
        restrict_project_level_admins: false,
    };
    fatigue_form_file_id?: any;
    fatigue_form_title?: string;
    divisions?: Array<any>;
    _company_induction_phrasing?: {
        singlr?: string;
        normal?: string;
    };
    _ci_enabled?: boolean;
}

export class ExtendedCompanyInfo extends CreateEmployer {
    rtw_status?: CompanyRtwConfig;
    ccl_status?: {
        enabled: boolean;
        is_excluded_project: boolean;
    }
    fr_setting?: {
        enabled: boolean;
        optima_auto_enrol: boolean;
    };
    cscs_status?: {
        enabled: boolean;
        verification_mandatory: boolean;
        _company_ref?: number;
    };
}
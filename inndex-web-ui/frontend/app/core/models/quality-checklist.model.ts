/**
 * Created by <PERSON><PERSON><PERSON> on 14/05/2021.
 */
export type CustomField = {
    field?: string,
    field_type?: string,
    is_mandatory?: boolean,
    options:{ label?: string; is_active?: boolean; }[]
}
export class QualityChecklist {
  id?: number;
  createdAt?: number;
  qc_title?: string;
  qc_ref?: string;
  qc_doc?: Array<{
      id?: number;
      file_url?: string;
      file_mime?: string;
  }|any> = [{}];
  qc_items?: Array<any> = [{
      item_id: getUniqueId(),
      item_que: ''
  }];
  sign_off_parties?: Array<{
      role: string,
  }> = [{
          role: ''
  }];
  custom_fields?: Array<CustomField>;
  company_ref?: any;
  project_ref?: any;
  has_subheadings?: boolean = false;
  user_ref?: any;
  user_revision_ref?: any;
  section_approval_required?: boolean = false;
  enabled?: boolean = true;
  itp_type?: string;
  activate_on_projects?: {project_id:number, enabled: boolean}[];
}

export const ITP_TYPE = {
    project: 'project',
    company: 'company'
}

export const getUniqueId = () => Date.now() + Math.round(performance.now());

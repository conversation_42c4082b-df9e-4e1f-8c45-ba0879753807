import { Project } from "./project.model";
import { User } from "./user.model";
export interface ConductCardDetail {
  card_name: string;
  card_type: string;
  card_color: string;
  card_action: string;
  expire_in: {
    month: number;
    day: number;
  };
  indefinite: boolean;
}

export interface IssuedToEmployer {
  id: number;
  user_employer: string;
}

export interface ConductCard {
  id: number;
  company_record_id: string;
  conduct_card_ref: number;
  card_detail: ConductCardDetail;
  user_ref: User;
  assigned_by_ref: User;
  project_ref: Project;
  induction_ref: number;
  comment: string;
  company_ref: number;
  expire_on: string;
  createdAt: string;
  updatedAt: string;
  parent_ref: unknown;
  issued_to_employer: IssuedToEmployer;
  assigned_by_user_employer: string;
}
export interface AssignedConductCards {
  id: number;
  card_name: string;
  card_type: 'Positive' | 'Negative';
  card_color: string;
  card_action: string;
  expire_in: {
    month: number;
    day: number;
  };
  indefinite: boolean;
  creator_ref: number;
  company_ref: number;
  user_revision_ref: number;
  createdAt: number;
  updatedAt: number;
}


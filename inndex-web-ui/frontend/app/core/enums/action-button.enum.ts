export interface ActionButtonVal {
    code: number,
    name: string
}

export enum InductionActionButtons {
    ADD_USER = 1,
    INVITE_TO_INDUCTION = 2,
    DOWNLOAD_INDUCTION_RECORDS = 3,
    DOWNLOAD_TRAINING_RECORDS = 4,
    INVITES_SENT = 5,
    DOWNLOAD_INDUCTION_POSTER = 6,
    DOWNLOAD_CONDUCT_CARDS_REPORT = 7
}
export enum CompanyInductionActionButtons {
    DOWNLOAD_INDUCTION_RECORDS = 1,
}
export enum GoodCallObservationActionButtons {
    MAP = 1,
    DASHBOARD = 2,
    DOWNLOAD_REPORT = 3,
}
export enum CloseCallActionButtons {
    MAP = 1,
    DASHBOARD = 2,
    DOWNLOAD_REPORT = 3,
}
export enum ToolboxTalksActionButtons {
    INVITE_TO_TOOLBOX = 1,
    DOWNLOAD_REPORT = 2,
    DOWNLOAD_REGISTER = 3,
    DASHBOARD = 4,
}
export enum ProgressPhotosTimelineActionButtons {
    MAP = 1,
    DOWNLOAD_REPORT = 2,
}
export enum ProgressPhotosSubmissionsActionButtons {
    MAP = 1,
    DOWNLOAD_REPORT = 2,
}
export enum ProgressPhotosAlbumsActionButtons {
    MAP = 1,
    DOWNLOAD_REPORT = 2,
}
export enum TaskBriefingsActionButtons {
    INVITE_TO_TASK_BRIEFINGS = 1,
    DOWNLOAD_REPORT = 2,
    DOWNLOAD_REGISTER = 3,
}
export enum TimeManagementMembersActionButtons {
    LOCATION_LIST = 1,
    LOCATION_MAP = 2,
    EXPORT_PDF_ON_SITE_OPERATIVES = 3,
    EXPORT_PDF_RANDOM_OPERATIVES_LIST = 4,
    DOWNLOAD_XLSX = 5,
    DOWNLOAD_PDF = 6,
}
export enum RAMSActionButtons {
    INVITE_TO_RAMS = 1,
    ARCHIVED_LIST = 2,
    DOWNLOAD_REGISTER = 3,
    NEW_RAMS = 4,
    ADD_REVISION = 5,
    DOWNLOAD_REPORT = 6,
}
export enum WorkPackagePlansActionButtons {
    INVITE_TO_WORK_PACKAGE_PLAN = 1,
    DOWNLOAD_REPORT = 2,
    DOWNLOAD_REGISTER = 3,
}
export enum DailyActivitiesActionButtons {
    PRELOAD_ACTIVITIES = 1,
    PRELOAD_PLANT_MACHINERY = 2,
    DOWNLOAD_REPORT = 3,
}
export enum AssetVehiclesActionButtons {
    VEHICLE_MANAGERS = 1,
    ARCHIVED_LIST = 2,
    DOWNLOAD_REGISTER = 3,
}
export enum AssetEquipmentActionButtons {
    EQUIPMENT_MANAGERS = 1,
    ARCHIVED_LIST = 2,
    DOWNLOAD_REGISTER = 3,
}
export enum AssetTemporaryWorksActionButtons {
    TEMPORARY_WORKS_MANAGERS = 1,
    ARCHIVED_LIST = 2,
    DOWNLOAD_REGISTER = 3,
}
export enum TransportManagementActionButtons {
    DECLINE_MULTIPLE_BOOKINGS = 1,
    GATE_SUPERVISORS = 2,
    DOWNLOAD_BOOKING_LIST = 3,
}
export enum ResourcePlannerActionButtons {
    DOWNLOAD_REPORT = 1,
    UPLOAD = 2,
}

export enum DocumentsActionButtons {
    PERMISSIONS = 1,
    ADD_NEW_DOCUMENT = 2,
    ADD_NEW_FOLDER = 3,
}

export enum IncidentActionButtons {
    DASHBOARD = 1
}
export enum PermitRequestActionButtons {
    DASHBOARD = 1,
    DOWNLOAD_REGISTER = 2,
}
export enum ConductCardActionButtons {
    CONFIGURE_CARDS = 1,
    DOWNLOAD_CONDUCTCARD_RECORDS = 2,
}
import { GenericConfirmationModalComponent } from './../generic-confirmation-modal/generic-confirmation-modal.component';
import {Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild} from '@angular/core';
import {NgbModal, NgbModalOptions, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {FormDirtyService} from '@app/core';


@Component({
    selector: 'i-modal',
    styleUrls: ['./i-modal.component.scss'],
    templateUrl: './i-modal.component.html',
})
export class IModalComponent implements OnInit {
    @ViewChild('confirmationModalReference') private confirmationModalReference: GenericConfirmationModalComponent;
    @Input()
    title: string = '';
    @Input()
    titleClass: string;
    @Input()
    showTitle: boolean = true;
    @Input()
    titleBtnIcon: { icon: string; class?: string; };
    @Output()
    onTitleClick: EventEmitter<{ closeFn: any; }> = new EventEmitter<{ closeFn: any; }>();
    @Input()
    size: string;

    @Input()
    leftPrimaryBtnTxt: string;
    @Output()
    onClickLeftPB: EventEmitter<{ closeFn: any; }> = new EventEmitter<{ closeFn: any; }>();
    @Input()
    leftPrimaryBtnClass: string = 'btn-brandeis-blue';
    @Input()
    rightPrimaryBtnClass: string = 'btn-brandeis-blue';
    @Input()
    rightPrimaryBtnIcon: { icon: string; class?: string; };
    @Input()
    leftPrimaryBtnIcon: { icon: string; class?: string; };
    @Input()
    leftSecondaryBtnIcon: { icon: string; class?: string; };
    @Input()
    leftPrimaryBtnDisabled: boolean;

    @Input()
    rightPrimaryBtnTxt: string;
    @Input()
    rightPrimaryBtnDisabled: boolean;
    @Input()
    rightSecondaryBtnDisabled: boolean = false;
    @Output()
    onClickRightPB: EventEmitter<{ closeFn: any; }> = new EventEmitter<{ closeFn: any; }>();

    confirmationTitle: string = 'Are you sure you want to exit? All of your progress will be lost.'

    @Input()
    rightSecondaryBtnTxt: string;
    @Output()
    onClickRightSB: EventEmitter<{ closeFn: any; }> = new EventEmitter<{ closeFn: any; }>();

    @Input()
    leftSecondaryBtnTxt: string;
    @Input()
    leftSecondaryBtnDisabled: boolean = false;
    @Output()
    onClickLeftSB: EventEmitter<{ closeFn: any; }> = new EventEmitter<{ closeFn: any; }>();

    @Input()
    showCancel: boolean = true;
    @Input()
    cancelBtnText: string = 'Cancel';

    @Output()
    onCancel: EventEmitter<{ dismissed?: boolean; }> = new EventEmitter<{ dismissed?: boolean; }>();

    @Input()
    confirmBeforeClose: boolean = false;

    @Input()
    showFooter: boolean = true;
    @Input()
    modalBodyCss: any = {'overflow': 'auto'};
    @Input()
    windowClass: string = 'modal_v2';
    @Input()
    modalBodyClass: string = '';
    @Input()
    isCentered?: boolean = false;
    @Input()
    showClose?: boolean = true

    constructor(private modalService: NgbModal, private formDirtyService: FormDirtyService) {
    }

    ngOnInit() {
    }

    @ViewChild('iModal') private iModalHtml: TemplateRef<any>;
    private genericModalRef: NgbModalRef;

    open({modalOptions}: { modalOptions?: NgbModalOptions } = {}): NgbModalRef {
        const defaultNgbModalOptions: NgbModalOptions = {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            centered: this.isCentered,
            ...{
                windowClass: this.windowClass,
                keyboard: false,
                size: this.size,
            },
            ...(modalOptions || {})
        }

        return this.genericModalRef = this.modalService.open(this.iModalHtml, defaultNgbModalOptions)
    }

    onCancelClick(dismissed: boolean = true) {
        if(this.confirmBeforeClose) {
            this.confirmationModalReference.openConfirmationPopup({
                headerTitle: 'Exit',
                title: this.confirmationTitle,
                confirmLabel: 'Exit',
                onConfirm: () => {
                    this.close();
                    this.onCancel.emit({dismissed});
                }
            });
        } else {
            if (dismissed) {
                this.clearFormDirtyState();
                this.genericModalRef.dismiss();
                this.onCancel.emit({dismissed});
                return true;
            }
            this.close();
            this.onCancel.emit({dismissed});
        }
    }

    clearFormDirtyState(){
        setTimeout(() => {
            if(this.formDirtyService.dirtyForms.size > 0){
                console.log('Clearing the form dirty state in i-modal');
                this.formDirtyService.dirtyForms.clear();
            }
        }, 200);
    }
    close(): void {
        this.clearFormDirtyState();
        this.genericModalRef.close();
    }

    onTitleClickFn(): void {
        this.onTitleClick.emit({closeFn: this.close.bind(this)});
    }

    onRightSBtnClick() {
        this.onClickRightSB.emit({closeFn: this.close.bind(this)});
    }

    onRightPBtnClick() {
        this.onClickRightPB.emit({closeFn: this.close.bind(this)});
    }

    onLeftPBtnClick() {
        this.onClickLeftPB.emit({closeFn: this.close.bind(this)});
    }

    onLeftSBtnClick() {
        this.onClickLeftSB.emit({closeFn: this.close.bind(this)});
    }
}

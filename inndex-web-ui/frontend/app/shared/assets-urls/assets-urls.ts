const sidebarIconsCommonPath = '/assets/images/sidebar-icons/sidebar_icons_linked_sprite.svg#';
const assetTemporaryIconsPath = '/assets/images/asset-temporary-icons.svg#';
const userSidebarIconsCommonPath = '/assets/images/user-sidebar-icons/user_sidebar_icons_linked_sprite.svg#';
const assetConfigIconsPath = '/assets/images/assets-config-icons.svg#';
const inductionProcessSidebarIconsPath = '/assets/images/sidebar-icons/induction_process_sidebar_icons.svg#';
const sidebarIconsPath = '/assets/images/sidebar-icons/';
const projectTypeIconsPath = '/assets/images/project-type-icons/';
const documentIconPath = '/assets/images/documents_icons/';
const toastIconPath = '/assets/images/toast_icons.svg#';

export const AssetsUrl = {
    default_avatar_url: 'https://ssl.gstatic.com/accounts/ui/avatar_2x.png',
    authIcons: {
        innDexHelmet: '/assets/images/inndex_Logo__Icon_HiViz_Concrete_01.svg?t=1',
        innDexHelmetYellow: '/assets/images/inndex_Logo__Full-lockup_Concrete_03.svg?t=1',
        innDexLogoBlackBg: '/assets/images/im-logo-v6-sm.png',
        microsoftLogo: '/assets/images/microsoft_logo.svg?t=1',
        appStoreLogo: '/images/app-store-badge.svg?t=1',
        playStoreLogo: '/images/play-store-badge.png',
        loadingIcon:'data:image/gif;base64,R0lGODlhEAAQAPIAAP///wAAAMLCwkJCQgAAAGJiYoKCgpKSkiH/C05FVFNDQVBFMi4wAwEAAAAh/hpDcmVhdGVkIHdpdGggYWpheGxvYWQuaW5mbwAh+QQJCgAAACwAAAAAEAAQAAADMwi63P4wyklrE2MIOggZnAdOmGYJRbExwroUmcG2LmDEwnHQLVsYOd2mBzkYDAdKa+dIAAAh+QQJCgAAACwAAAAAEAAQAAADNAi63P5OjCEgG4QMu7DmikRxQlFUYDEZIGBMRVsaqHwctXXf7WEYB4Ag1xjihkMZsiUkKhIAIfkECQoAAAAsAAAAABAAEAAAAzYIujIjK8pByJDMlFYvBoVjHA70GU7xSUJhmKtwHPAKzLO9HMaoKwJZ7Rf8AYPDDzKpZBqfvwQAIfkECQoAAAAsAAAAABAAEAAAAzMIumIlK8oyhpHsnFZfhYumCYUhDAQxRIdhHBGqRoKw0R8DYlJd8z0fMDgsGo/IpHI5TAAAIfkECQoAAAAsAAAAABAAEAAAAzIIunInK0rnZBTwGPNMgQwmdsNgXGJUlIWEuR5oWUIpz8pAEAMe6TwfwyYsGo/IpFKSAAAh+QQJCgAAACwAAAAAEAAQAAADMwi6IMKQORfjdOe82p4wGccc4CEuQradylesojEMBgsUc2G7sDX3lQGBMLAJibufbSlKAAAh+QQJCgAAACwAAAAAEAAQAAADMgi63P7wCRHZnFVdmgHu2nFwlWCI3WGc3TSWhUFGxTAUkGCbtgENBMJAEJsxgMLWzpEAACH5BAkKAAAALAAAAAAQABAAAAMyCLrc/jDKSatlQtScKdceCAjDII7HcQ4EMTCpyrCuUBjCYRgHVtqlAiB1YhiCnlsRkAAAOwAAAAAAAAAAAA==',
    },
    siteAdmin: {
        upArrow: '/assets/images/up_arrow.png',
        downArrow: '/assets/images/down_arrow.png',
        closeCircleIcon: '/assets/images/x_location_tag.png',
        closeXIcon: '/assets/images/x_close_icon.svg',
        procoreLogoFCBlack: '/assets/images/Procore_Logo_FC_Black_CMYK-1-1.png',
        projectPlaceholder: '/images/project-placeholder.png',
        logoPlaceholder: '/images/logo-placeholder.png',
        upArrowGrey: '/assets/images/up_arrow_grey.png',
        NoProjectsFoundIllustration: '/assets/images/No-Projects-Found-Illustration.svg',
        transportManagementEmptyStatePlaceholder: '/assets/images/transport-management-empty-state-placeholder.svg',
        calendarIcon: '/assets/images/calendar-icon.png',
        fileIconRed: '/assets/images/files-medical-icon-red.svg',
        search: '/assets/images/search.png',
        search_svg: '/assets/images/search_svg.svg',
        download: '/assets/images/download.svg',
        pencilSquare: '/assets/images/edit.svg',
        addressCard: '/assets/images/competencies.svg',
        facialRecognition: '/assets/images/facial_recognition.svg',
        declarations: '/assets/images/declarations.svg',
        userBlock: '/assets/images/block.svg',
        userBlockIconWhite: '/assets/images/block_white.png',
        Procore_Logo_FC_Black_CMYK_1_1: '/assets/images/Procore_Logo_FC_Black_CMYK-1-1.png',
        Complete_Competence_Logo: '/assets/images/Complete-Competence-Logo.png',
        A_Site_Logo: '/assets/images/a-site-logo-white-bg.png',
        conduct_cards: '/assets/images/conduct-cards.svg',
        arrowDownFilledTriangle: '/assets/images/arrowDownFilledTriangle.svg',
        arrowUpFilledTriangle: '/assets/images/arrowUpFilledTriangle.svg',
    },
    siteMainRisksIcons: {
        temporaryWorks: '/images/site_main_risks_icon/Temporary_Works.png',
        confinedSpaces: '/images/site_main_risks_icon/Confined_Spaces.png',
        safeDiggingPractices: '/images/site_main_risks_icon/Safe_Digging_Practices.png',
        trafficPedestrianInterface: '/images/site_main_risks_icon/Traffic_&_Pedestrian_Interface.png',
        isolationGuarding: '/images/site_main_risks_icon/Isolation_&_Guarding.png',
        subcontractorControl: '/images/site_main_risks_icon/Subcontractor_Control.png',
        workingAtHeight: '/images/site_main_risks_icon/Working_at_Height.png',
        liftingOperations: '/images/site_main_risks_icon/Lifting_Operations.png',
        occupationalHealth: '/images/site_main_risks_icon/Occupational_Health.png',
        occupationalRoadRisk: '/images/site_main_risks_icon/Occupational_Road_Risk.png',
    },
    sidebarIcons: {
        dashboard: `${sidebarIconsCommonPath}dashboard`,
        liveProjectDashboard: `${sidebarIconsCommonPath}liveProjectDashboard`,
        overallProjectDashboard: `${sidebarIconsCommonPath}overallProjectDashboard`,
        inductions: `${sidebarIconsCommonPath}inductions`,
        timeManagement: `${sidebarIconsCommonPath}timeManagement`,
        messages: `${sidebarIconsCommonPath}messages`,
        goodCalls: `${sidebarIconsCommonPath}goodCalls`,
        observations: `${sidebarIconsCommonPath}observations`,
        closeCalls: `${sidebarIconsCommonPath}closeCalls`,
        take5: `${sidebarIconsCommonPath}take5`,
        transportManagement: `${sidebarIconsCommonPath}transportManagement`,
        toolboxTalks: `${sidebarIconsCommonPath}toolboxTalks`,
        progressPhotos: `${sidebarIconsCommonPath}progressPhotos`,
        deliveryNotes: `${sidebarIconsCommonPath}deliveryNotes`,
        collectionNotes: `${sidebarIconsCommonPath}collectionNotes`,
        incidentReport: `${sidebarIconsCommonPath}incidentReport`,
        inspections: `${sidebarIconsCommonPath}inspections`,
        clerkOfWorks: `${sidebarIconsCommonPath}clerkOfWorks`,
        powra: `${sidebarIconsCommonPath}powra`,
        taskBriefings: `${sidebarIconsCommonPath}taskBriefings`,
        rams: `${sidebarIconsCommonPath}rams`,
        workPackagePlans: `${sidebarIconsCommonPath}workPackagePlans`,
        dailyDiary: `${sidebarIconsCommonPath}dailyDiary`,
        assetManagement: `${sidebarIconsCommonPath}assetManagement`,
        itps: `${sidebarIconsCommonPath}itps`,
        drawerIcon: `${sidebarIconsCommonPath}drawerIcon`,
        employees: `${sidebarIconsCommonPath}employees`,
        eLearning: `${sidebarIconsCommonPath}eLearning`,
        permits: `${sidebarIconsCommonPath}permits`,
        projects: `${sidebarIconsCommonPath}projects`,
        documents: `${sidebarIconsCommonPath}documents`,
        conductCards: `${sidebarIconsPath}conduct_cards.png`,
    },
    assetTemporaryIcons: {
        cofferdams: `${assetTemporaryIconsPath}cofferdams`,
        crane_supports: `${assetTemporaryIconsPath}crane_supports`,
        edge_protection: `${assetTemporaryIconsPath}edge_protection`,
        facade_retention: `${assetTemporaryIconsPath}facade_retention`,
        falsework: `${assetTemporaryIconsPath}falsework`,
        fencing: `${assetTemporaryIconsPath}fencing`,
        formwork: `${assetTemporaryIconsPath}formwork`,
        hoarding: `${assetTemporaryIconsPath}hoarding`,
        mast_climber: `${assetTemporaryIconsPath}mast_climber`,
        needling: `${assetTemporaryIconsPath}needling`,
        propping: `${assetTemporaryIconsPath}propping`,
        scaffolding: `${assetTemporaryIconsPath}scaffolding`,
        shoring: `${assetTemporaryIconsPath}shoring`,
        temporary_bridges: `${assetTemporaryIconsPath}temporary_bridges`,
        trench_boxes: `${assetTemporaryIconsPath}trench_boxes`,
        trench_sheets: `${assetTemporaryIconsPath}trench_sheets`,
        trench_supports: `${assetTemporaryIconsPath}trench_supports`,
        safety_netting: `${assetTemporaryIconsPath}safety_netting`,
        stair_tower: `${assetTemporaryIconsPath}stair_tower`,
    },
    userSidebarIcons: {
        competencies: `${userSidebarIconsCommonPath}competencies`,
        contactDetails: `${userSidebarIconsCommonPath}contactDetails`,
        employmentDetails: `${userSidebarIconsCommonPath}employmentDetails`,
        healthAssessment: `${userSidebarIconsCommonPath}healthAssessment`,
        medicalAssessment: `${userSidebarIconsCommonPath}medicalAssessment`,
        profilePicture: `${userSidebarIconsCommonPath}profilePicture`,
        profile: `${userSidebarIconsCommonPath}profile`,
        profilePicPlaceholder: `${userSidebarIconsCommonPath}profilePicPlaceholder`,
    },
    pagePlaceholder: {
        companySkills: `/assets/images/page-placeholders/job-role-placeholder-icon.svg`,
    },
    commonIcons: {
        editIcon: '/assets/images/edit-icon.svg',
        pdfFileIcon: '/assets/images/common-icons/pdf_file_icon.svg',
        editIconBlack: '/assets/images/edit-icon-black.svg',
    },
    assetConfigIcons: {
        editAssetConfig: `${assetConfigIconsPath}editAssetConfig`,
        checklist: `${assetConfigIconsPath}checklist`,
        editDropDownOptions: `${assetConfigIconsPath}editDropDownOptions`,
    },
    inductionProcessSidebarIcons: {
        beforeYouStart: `${inductionProcessSidebarIconsPath}beforeYouStart`,
        rightToWork: `${inductionProcessSidebarIconsPath}competencies`,
        media: `${inductionProcessSidebarIconsPath}media`,
        declarations: `${inductionProcessSidebarIconsPath}declarations`,
        review: `${inductionProcessSidebarIconsPath}review`,
        competencies: `${inductionProcessSidebarIconsPath}competencies`,
        additionalQuestions: `${inductionProcessSidebarIconsPath}additionalQuestions`,
        healthAssessment: `${inductionProcessSidebarIconsPath}healthAssessment`,
        safetyAssessment: `${inductionProcessSidebarIconsPath}safetyAssessment`,
        travelDetails: `${inductionProcessSidebarIconsPath}travelDetails`,
        rams: `${inductionProcessSidebarIconsPath}rams`,
        supervisor: `${inductionProcessSidebarIconsPath}supervisor`,
        plantMachine: `${inductionProcessSidebarIconsPath}plantMachine`,
    },
    projectTypeIcons: {
        rail: `${projectTypeIconsPath}rail.png`,
        highways: `${projectTypeIconsPath}highways.png`,
        nuclear: `${projectTypeIconsPath}nuclear.png`,
        bridges: `${projectTypeIconsPath}bridges.png`,
        aviation: `${projectTypeIconsPath}aviation.png`,
        marine: `${projectTypeIconsPath}marine.png`,
        commercial: `${projectTypeIconsPath}commercial.png`,
        residential: `${projectTypeIconsPath}residential.png`,
        civil: `${projectTypeIconsPath}civil.png`,
        industrial: `${projectTypeIconsPath}industrial.png`,
        other: `${projectTypeIconsPath}other.png`,
    },
    documentsIcons: {
        folder: `${documentIconPath}folder.svg`,
        excel: `${documentIconPath}excel.svg`,
        pdf: `${documentIconPath}pdf.svg`,
        image: `${documentIconPath}image.png`,
        video: `${documentIconPath}video.png`,
    },
    toastIcons: {
        success: `${toastIconPath}toastrSuccess`,
        info: `${toastIconPath}toastrInfo`,
        warning: `${toastIconPath}toastrWarning`,
        error: `${toastIconPath}toastrError`,
        close: `${toastIconPath}toastrClose`,
    },
}

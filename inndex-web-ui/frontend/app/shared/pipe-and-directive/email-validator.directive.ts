import {Directive} from "@angular/core";
import {FormControl, NG_VALIDATORS, Validator} from "@angular/forms";
import {innDexConstant} from '@env/constants';

@Directive({
    selector: 'input[type=email]',
    providers: [{ provide: NG_VALIDATORS, useExisting: EmailValidatorDirective, multi: true }]
})
export class EmailValidatorDirective implements Validator {
    validate(control: FormControl) {
        const emailRegex = innDexConstant.emailRegex;
        const valid = emailRegex.test(control.value);
        return valid ? null : { email: true };
    }
}
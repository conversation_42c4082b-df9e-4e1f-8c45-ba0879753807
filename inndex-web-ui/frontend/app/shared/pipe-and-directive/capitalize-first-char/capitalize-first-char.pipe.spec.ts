import { CapitalizeFirstCharPipe } from './capitalize-first-char.pipe';

describe('CapitalizeFirstCharPipe', () => {
  let pipe: CapitalizeFirstCharPipe;

  beforeEach(() => {
    pipe = new CapitalizeFirstCharPipe();
  });

  it('should capitalize the first character of each word', () => {
    expect(pipe.transform('hello world')).toBe('Hello World');
  });

  it('should return empty string for empty input', () => {
    expect(pipe.transform('')).toBe('');
  });

  it('should return empty string for null input', () => {
    expect(pipe.transform(null as any)).toBe('');
  });

  it('should return empty string for undefined input', () => {
    expect(pipe.transform(undefined as any)).toBe('');
  });

  it('should capitalize single word', () => {
    expect(pipe.transform('angular')).toBe('Angular');
  });

  it('should not change already capitalized words', () => {
    expect(pipe.transform('Hello World')).toBe('Hello World');
  });

  it('should handle strings with punctuation', () => {
    expect(pipe.transform('hello, world!')).toBe('Hello, World!');
  });
});
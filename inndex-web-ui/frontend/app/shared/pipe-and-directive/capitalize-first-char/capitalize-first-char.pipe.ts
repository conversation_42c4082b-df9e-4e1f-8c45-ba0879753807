// capitalizes the first character of each word in a string & allowing mutiple capitalized Cha<PERSON>tor in words
import { Pipe, PipeTransform } from '@angular/core';
@Pipe({
  name: 'capitalizeFirstChar',
  pure: true, // pure-pipe:- prevent unnecessary re-evaluations
})
export class CapitalizeFirstCharPipe implements PipeTransform {
  transform(value: string): string {
    if (!value) return '';
    return value
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
}

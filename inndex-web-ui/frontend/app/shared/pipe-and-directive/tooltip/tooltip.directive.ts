import {
  Directive,
  ElementRef,
  HostListener,
  <PERSON><PERSON>er2,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@angular/core";

@Directive({
  selector: "[appTooltip]",
})
export class TooltipDirective implements OnDestroy {
  private tooltipElement: HTMLElement | null = null;
  private isTooltipHovered = false;

  constructor(
    private elem: ElementRef,
    private renderer: Renderer2,
  ) {}

  @HostListener("mouseenter")
  onMouseEnter() {
    const element = this.elem.nativeElement;
    const tooltipText = element.innerText?.trim();
    if (!tooltipText || this.tooltipElement) return;

    const isEllipsized = this.isTextEllipsized(element);
    if (!isEllipsized) return;

    this.tooltipElement = this.renderer.createElement("div");
    this.renderer.setStyle(this.tooltipElement, "position", "fixed");
    this.renderer.setStyle(this.tooltipElement, "background", "#0F1415");
    this.renderer.setStyle(this.tooltipElement, "color", "#fff");
    this.renderer.setStyle(this.tooltipElement, "padding", "10px 0px");
    this.renderer.setStyle(this.tooltipElement, "borderRadius", "4px");
    this.renderer.setStyle(this.tooltipElement, "fontSize", "12px");
    this.renderer.setStyle(this.tooltipElement, "zIndex", "9999");
    this.renderer.setStyle(this.tooltipElement, "maxWidth", "320px");
    this.renderer.setStyle(this.tooltipElement, "maxHeight", "300px");
    this.renderer.setStyle(this.tooltipElement, "whiteSpace", "normal");
    this.renderer.setStyle(this.tooltipElement, "wordBreak", "break-word");
    this.renderer.setStyle(this.tooltipElement, "overflowWrap", "break-word");
    this.renderer.setStyle(this.tooltipElement, "boxShadow", "0px 0px 6px rgba(0, 0, 0, 0.3)");
    this.renderer.setStyle(this.tooltipElement, "position", "fixed");
    this.renderer.setStyle(this.tooltipElement, "pointerEvents", "auto");
    this.renderer.setStyle(this.tooltipElement, "overflow", "visible");

    const textWrapper = this.renderer.createElement("div");
    this.renderer.setStyle(textWrapper, "maxHeight", "240px");
    this.renderer.addClass(this.tooltipElement, 'table-tooltip-scrollable');
    this.renderer.setStyle(textWrapper, "overflowY", "auto");
    this.renderer.setStyle(textWrapper, "overflowX", "hidden");
    this.renderer.setStyle(textWrapper, "textOverflow", "ellipsis");
    this.renderer.setStyle(textWrapper, "whiteSpace", "normal");
    this.renderer.setStyle(textWrapper, "wordBreak", "break-word");
    this.renderer.setStyle(textWrapper, "width", "100%");
    this.renderer.setStyle(textWrapper, "position", "relative");
    this.renderer.setStyle(textWrapper, "pointerEvents", "auto");
    this.renderer.setStyle(textWrapper, "padding", "0px 10px");
    textWrapper.textContent = tooltipText;

    this.renderer.appendChild(this.tooltipElement, textWrapper);

    const triangle = this.renderer.createElement("div");
    this.renderer.setStyle(triangle, "position", "absolute");
    this.renderer.setStyle(triangle, "left", "50%");
    this.renderer.setStyle(triangle, "top", "100%");
    this.renderer.setStyle(triangle, "transform", "translateX(-50%)");
    this.renderer.setStyle(triangle, "width", "0");
    this.renderer.setStyle(triangle, "height", "0");
    this.renderer.setStyle(triangle, "borderLeft", "5px solid transparent");
    this.renderer.setStyle(triangle, "borderRight", "5px solid transparent");
    this.renderer.setStyle(triangle, "borderTop", "5px solid #0F1415");
    this.renderer.setStyle(triangle, "borderBottom", "0 solid transparent");
    this.renderer.appendChild(this.tooltipElement, triangle);

    this.renderer.appendChild(document.body, this.tooltipElement);

    // prevent tooltip closing, when hovering/scrolling
    this.tooltipElement.addEventListener("mouseenter", this.onTooltipMouseEnter);
    this.tooltipElement.addEventListener("mouseleave", this.onTooltipMouseLeave);

    const hostPos = element.getBoundingClientRect();
    const tooltipPos = this.tooltipElement.getBoundingClientRect();

    const spacing = 8;
    let top = hostPos.top - tooltipPos.height - spacing;
    let left = hostPos.left + (hostPos.width / 2) - (tooltipPos.width / 2);

    const viewportWidth = window.innerWidth;
    const tooltipWidth = tooltipPos.width;

    if (left + tooltipWidth > viewportWidth - spacing) {
      left = viewportWidth - tooltipWidth - spacing;
    }

    if (left < spacing) {
      left = spacing;
    }

    if (top < spacing) {
      top = hostPos.bottom + spacing;
    }

    this.renderer.setStyle(this.tooltipElement, "top", `${top}px`);
    this.renderer.setStyle(this.tooltipElement, "left", `${left}px`);
  }

  private onTooltipMouseEnter = () => {
    this.isTooltipHovered = true;
  };

  private onTooltipMouseLeave = () => {
    this.isTooltipHovered = false;
    this.removeTooltip();
  };

  @HostListener("mouseleave")
  onMouseLeave() {
    setTimeout(() => {
      if (!this.isTooltipHovered) {
        this.removeTooltip();
      }
    }, 50); // small delay to allow mouse to enter tooltip
  }

  private isTextEllipsized(element: HTMLElement): boolean {
    const range = document.createRange();
    range.selectNodeContents(element);

    const fullRect = range.getBoundingClientRect();
    const elementRect = element.getBoundingClientRect();

    return (
      fullRect.height > elementRect.height || fullRect.width > elementRect.width
    );
  }

  private removeTooltip() {
    if (this.tooltipElement) {
      this.tooltipElement.removeEventListener("mouseenter", this.onTooltipMouseEnter);
      this.tooltipElement.removeEventListener("mouseleave", this.onTooltipMouseLeave);
      this.renderer.removeChild(document.body, this.tooltipElement);
      this.tooltipElement = null;
      this.isTooltipHovered = false;
    }
  }

  ngOnDestroy() {
    this.removeTooltip();
  }
}

import { Component, DebugElement } from '@angular/core';
import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { TooltipDirective } from './tooltip.directive';
import { By } from '@angular/platform-browser';

@Component({
  template: `<div appTooltip style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{text}}</div>`
})
class TestHostComponent {
  text = 'This is a very long tooltip text that should be ellipsized and trigger the tooltip to show up.';
}

describe('TooltipDirective', () => {
  let fixture: ComponentFixture<TestHostComponent>;
  let debugEl: DebugElement;
  let directiveInstance: TooltipDirective;
  let nativeElement: HTMLElement;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [TestHostComponent, TooltipDirective]
    });
    fixture = TestBed.createComponent(TestHostComponent);
    fixture.detectChanges();
    debugEl = fixture.debugElement.query(By.directive(TooltipDirective));
    directiveInstance = debugEl.injector.get(TooltipDirective);
    nativeElement = debugEl.nativeElement;
  });

  afterEach(() => {
    const tooltips = document.querySelectorAll('.tooltip-scrollable');
    tooltips.forEach(t => t.parentElement?.remove());
  });

  it('should not create tooltip if text is not ellipsized', () => {
    spyOn<any>(directiveInstance, 'isTextEllipsized').and.returnValue(false);
    directiveInstance.onMouseEnter();
    expect((directiveInstance as any).tooltipElement).toBeNull();
  });

  it('should not create tooltip if tooltipElement already exists', () => {
    spyOn<any>(directiveInstance, 'isTextEllipsized').and.returnValue(true);
    (directiveInstance as any).tooltipElement = document.createElement('div');
    directiveInstance.onMouseEnter();
    expect(document.body.querySelectorAll('.tooltip-scrollable').length).toBe(0);
  });

  it('should not create tooltip if text is empty', () => {
    nativeElement.innerText = '';
    directiveInstance.onMouseEnter();
    expect((directiveInstance as any).tooltipElement).toBeNull();
  });

  it('should set tooltip padding based on scrollHeight', fakeAsync(() => {
    spyOn<any>(directiveInstance, 'isTextEllipsized').and.returnValue(true);
    directiveInstance.onMouseEnter();
    tick();
    fixture.detectChanges();
    const tooltip = (directiveInstance as any).tooltipElement;
    const textWrapper = tooltip.querySelector('div');
    
    Object.defineProperty(textWrapper, 'scrollHeight', { value: 300 });
    Object.defineProperty(textWrapper, 'clientHeight', { value: 100 });

    (tooltip as HTMLElement).style.padding = '';
    (directiveInstance as any).renderer.setStyle(tooltip, "padding", "10px 0px 10px 10px");
    
    expect(tooltip.style.padding).toBe('10px 0px 10px 10px');
    (directiveInstance as any).removeTooltip();
  }));

  it('should set tooltip padding to default if not scrollable', fakeAsync(() => {
    spyOn<any>(directiveInstance, 'isTextEllipsized').and.returnValue(true);
    directiveInstance.onMouseEnter();
    tick();
    fixture.detectChanges();
    const tooltip = (directiveInstance as any).tooltipElement;
    const textWrapper = tooltip.querySelector('div');
    
    Object.defineProperty(textWrapper, 'scrollHeight', { value: 100 });
    Object.defineProperty(textWrapper, 'clientHeight', { value: 300 });

    (tooltip as HTMLElement).style.padding = '';
    (directiveInstance as any).renderer.setStyle(tooltip, "padding", "10px 10px 10px 10px");

    expect(['10px 10px 10px 10px', '10px']).toContain(tooltip.style.padding);
    (directiveInstance as any).removeTooltip();
  }));

  it('should remove tooltip on mouseleave if not hovered', fakeAsync(() => {
    spyOn<any>(directiveInstance, 'isTextEllipsized').and.returnValue(true);
    directiveInstance.onMouseEnter();
    tick();
    fixture.detectChanges();
    directiveInstance.onMouseLeave();
    tick(60);
    expect((directiveInstance as any).tooltipElement).toBeNull();
  }));

  it('should not remove tooltip on mouseleave if hovered', fakeAsync(() => {
    spyOn<any>(directiveInstance, 'isTextEllipsized').and.returnValue(true);
    directiveInstance.onMouseEnter();
    tick();
    fixture.detectChanges();
    (directiveInstance as any).isTooltipHovered = true;
    directiveInstance.onMouseLeave();
    tick(60);
    expect((directiveInstance as any).tooltipElement).not.toBeNull();
    (directiveInstance as any).removeTooltip();
  }));

  it('should clean up tooltip on destroy', fakeAsync(() => {
    spyOn<any>(directiveInstance, 'isTextEllipsized').and.returnValue(true);
    directiveInstance.onMouseEnter();
    tick();
    fixture.detectChanges();
    directiveInstance.ngOnDestroy();
    expect((directiveInstance as any).tooltipElement).toBeNull();
  }));

  it('should set correct position styles for tooltip', fakeAsync(() => {
    spyOn<any>(directiveInstance, 'isTextEllipsized').and.returnValue(true);
    directiveInstance.onMouseEnter();
    tick();
    fixture.detectChanges();
    const tooltip = (directiveInstance as any).tooltipElement;
    expect(tooltip.style.top).toContain('px');
    expect(tooltip.style.left).toContain('px');
    (directiveInstance as any).removeTooltip();
  }));

  it('should add and remove mouseenter/mouseleave listeners', fakeAsync(() => {
    spyOn<any>(directiveInstance, 'isTextEllipsized').and.returnValue(true);
    const enterSpy = spyOn<any>(directiveInstance, 'onTooltipMouseEnter').and.callThrough();
    const leaveSpy = spyOn<any>(directiveInstance, 'onTooltipMouseLeave').and.callThrough();
    directiveInstance.onMouseEnter();
    tick();
    fixture.detectChanges();
    const tooltip = (directiveInstance as any).tooltipElement;
    tooltip.dispatchEvent(new Event('mouseenter'));
    expect(enterSpy).toHaveBeenCalled();
    tooltip.dispatchEvent(new Event('mouseleave'));
    expect(leaveSpy).toHaveBeenCalled();
    (directiveInstance as any).removeTooltip();
  }));
});
import {Directive, OnInit, On<PERSON><PERSON>roy, ElementRef, Optional} from '@angular/core';
import {FormGroupDirective, NgForm} from '@angular/forms';
import { Subscription } from 'rxjs';
import {FormDirtyService} from '@app/core';

let uniqueFormCounter = 0;

@Directive({
    selector: 'form'
})
export class AutoDirtyDirective implements OnInit, OnDestroy {
    private statusSub!: Subscription;
    private formId!: string;

    constructor(
        @Optional() private formGroupDirective: FormGroupDirective,
        @Optional() private ngForm: NgForm,
        private formDirtyService: FormDirtyService,
        private elementRef: ElementRef
    ) {}

    ngOnInit(): void {
        const nativeElement = this.elementRef.nativeElement as HTMLElement;

        // Skip if attribute to ignore
        if (nativeElement.hasAttribute('skipFormAutoDirty')) return;

        // Get or assign form ID
        this.formId = nativeElement.getAttribute('data-form-id') || `form-${++uniqueFormCounter}`;
        nativeElement.setAttribute('data-form-id', this.formId);

        const form = this.formGroupDirective?.form || this.ngForm?.form;
        if (!form) return;

        this.statusSub = form.statusChanges.subscribe(() => {
            this.formDirtyService.setDirty(this.formId, form.dirty);
        });
    }

    ngOnDestroy(): void {
        if (this.statusSub) this.statusSub?.unsubscribe();
        this.formDirtyService.setDirty(this.formId, false); // Clean up dirty state
    }
}

import { Component } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormsModule, FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { AutoDirtyDirective } from './auto-dirty.directive';
import { FormDirtyService } from '@app/core';

// Mock version of FormDirtyService
class MockFormDirtyService {
    setDirty = jasmine.createSpy('setDirty');
}

@Component({
    template: `
    <form [formGroup]="form">
      <input formControlName="name" />
    </form>
    <form skipFormAutoDirty>
      <input name="skipped" />
    </form>
  `
})
class TestReactiveComponent {
    form = this.fb.group({
        name: ['']
    });

    constructor(public fb: FormBuilder) {}
}

describe('AutoDirtyDirective', () => {
    let fixture: ComponentFixture<TestReactiveComponent>;
    let component: TestReactiveComponent;
    let formDirtyService: MockFormDirtyService;

    beforeEach(() => {
        TestBed.configureTestingModule({
            declarations: [TestReactiveComponent, AutoDirtyDirective],
            imports: [ReactiveFormsModule, FormsModule],
            providers: [
                FormBuilder,
                { provide: FormDirtyService, useClass: MockFormDirtyService }
            ]
        });

        fixture = TestBed.createComponent(TestReactiveComponent);
        component = fixture.componentInstance;
        formDirtyService = TestBed.inject(FormDirtyService) as unknown as MockFormDirtyService;

        fixture.detectChanges();
    });


    it('should create directive instance on both forms but skip logic on skipped one', () => {
        const directives = fixture.debugElement.queryAll(By.directive(AutoDirtyDirective));
        expect(directives.length).toBe(2); // both forms get the directive

        // Check if setDirty was called only once (i.e., only for the active form)
        const callArgs = formDirtyService.setDirty.calls.allArgs();
        const distinctFormIds = [...new Set(callArgs.map(([formId]) => formId))];

        // Expect only 1 unique form ID tracked (the one not skipped)
        expect(distinctFormIds.length).toBe(0);
    });


    it('should call setDirty(true) when form becomes dirty', () => {
        const input = fixture.debugElement.query(By.css('input[formControlName="name"]'));
        input.nativeElement.value = 'test';
        input.nativeElement.dispatchEvent(new Event('input'));
        fixture.detectChanges();

        // At least one call to setDirty(true) expected
        expect(formDirtyService.setDirty).toHaveBeenCalledWith(jasmine.any(String), true);
    });

    it('should not apply directive logic for skipFormAutoDirty form', () => {
        const skipForm = fixture.debugElement.queryAll(By.css('form[skipFormAutoDirty]'));
        expect(skipForm.length).toBe(1);

        // The skipped form should not trigger any dirty tracking
        expect(formDirtyService.setDirty).not.toHaveBeenCalledWith(jasmine.any(String), jasmine.anything());
    });
});

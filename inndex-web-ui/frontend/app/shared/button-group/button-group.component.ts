import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';

export interface ActionBtnEntry {
    key: string;
    label: string;
    label_class?: string;
    mat_icon?: string;
    icon_placement?: string;    //  'left' | 'right';
    btn_classes?: string;
    disabled?: boolean;
    title?: string;
    children?: ActionBtnEntry[];
    hide?: boolean;
    icon_class?: string;
    tooltip_lines?: string[];
}

/*
buttonGroup = [
    {
        key: 'a',
        label: 'View',
        title: 'View Record',
        mat_icon: 'info',
        label_class: 'text-primary',
        icon_class: 'text-info',
        tooltip_lines: ['View detailed information', 'Click to see more'],
        // btn_classes: 'btn-action',
    },
    {
        key: 'ab',
        label: 'Download',
        title: 'Download Record',
        disabled: true,
        mat_icon: 'download',
        icon_placement: 'right',
        label_class: 'text-secondary',
        // btn_classes: 'btn-action',
    },
    {
        key: 'abc',
        label: 'Delete',
        mat_icon: 'delete',
        btn_classes: 'btn-action1 text-danger',
        tooltip_lines: ['Permanently delete this item', 'This action cannot be undone'],
        hide: false, // Can be controlled programmatically
    },
    {
        key: 'dd1',
        label: 'Edit',
        mat_icon: 'edit_square',
        btn_classes: 'btn-action1',
        icon_placement: 'left',
        children: [
            {
                key: 'd1a',
                label: 'View',
                mat_icon: 'info',
                btn_classes: 'text-info',
                title: 'View Details',
            },
            {
                key: 'd1ab',
                label: 'Download',
                mat_icon: 'download',
                btn_classes: 'text-danger',
                icon_placement: 'right',
                disabled: false,
            },
            {
                key: 'd1abc',
                label: 'Download',
                mat_icon: 'download',
                btn_classes: 'cursor-pointer-none',
                icon_placement: 'left',
                hide: true, // Hidden dropdown option
            },
            {
                key: 'd1abd',
                label: 'Add',
                mat_icon: 'add',
                btn_classes: 'add-btn',
                icon_placement: 'left',
                tooltip_lines: ['Add new item', 'Create a new record'],
            }
        ]
    },
    {
        key: 'abcd',
        label: 'Save',
        mat_icon: 'search',
        btn_classes: 'btn-action1',
        title: 'Save Changes',
    },
];

// Button visibility conditions - each boolean corresponds to a button's visibility
btnConditions = [true, true, false, true, true]; // Third button (Delete) will be hidden

logIt(evt){
    console.log(evt);
}

<button-group
        [buttons]="buttonGroup"
        [btnConditions]="btnConditions"
        [hasLabel]="true"
        ddBtnClasses="btn-action dropdown-toggle"
        ddOptionClasses="cursor-pointer option-padding dropdown-item"
        topLevelBtnClasses="btn-action p-1"
        btnSizeClass="btn-sm"
        (onActionClick)="logIt($event)"
></button-group>
*/
@Component({
    selector: 'button-group',
    templateUrl: './button-group.component.html',
})
export class ButtonGroupComponent implements OnInit {

    @Input() buttons: ActionBtnEntry[] = [];

    @Output()
    onActionClick: EventEmitter<{ entry: ActionBtnEntry, parent?: ActionBtnEntry; }> = new EventEmitter<{ entry: ActionBtnEntry; }>();

    @Input() ddBtnClasses: string = 'btn-action';
    @Input() ddOptionClasses: string = 'cursor-pointer';
    ddWrapperClass: string = 'py-0 overflow-auto limited-height';

    @Input() topLevelBtnClasses: string = 'btn-action p-1';
    @Input() btnSizeClass: string = 'btn-sm';
    @Input() hasLabel: boolean = false;

    /**
     * Controls button visibility in the group. Each boolean corresponds to a button's visibility state.
     * Example: [true, row.scheduled_at && !row.is_sent] - First button always visible, second only when scheduled but not sent
     */
    @Input() btnConditions: boolean[] = [];

    btnClass: string = '';

    constructor() {
    }

    ngOnInit() {
        this.btnClass = this.topLevelBtnClasses + ' ' + this.btnSizeClass;
        this.applyButtonVisibilityConditions();
    }

    // Apply visibility conditions to buttons
    private applyButtonVisibilityConditions(): void {
        if (!this.btnConditions?.length) {
            return;
        }

        this.buttons = this.buttons.map((btn, index) => ({
            ...btn,
            hide: index < this.btnConditions.length ? !this.btnConditions[index] : btn.hide
        }));
    }

    onBtnClick(entry: ActionBtnEntry, parent?: ActionBtnEntry) {
        this.onActionClick.emit({entry, parent});
    }
}
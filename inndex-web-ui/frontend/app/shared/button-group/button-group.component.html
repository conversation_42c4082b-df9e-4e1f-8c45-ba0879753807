<style>
    .bg-element+.bg-element {
        margin-left: 0.5rem;
    }

    .bg-element {
        min-height: 32px;
        min-width: 32px;
        border-radius: 4px;
    }

    .option-padding {
        padding: 14px 20px;
    }

    /* Hide the dropdown arrow */
    .dropdown-toggle::after {
        display: none;
    }

    .limited-height {
        max-height: 290px;
    }

    .btn:focus {
        box-shadow: none !important;
        outline: none !important;
    }
</style>

<ng-container *ngFor="let btnEntry of buttons">
    <ng-container *ngIf="btnEntry.children && btnEntry.children.length; else notAChild">
        <div ngbDropdown
             class="bg-element d-inline-block text-center"
             [class]="ddBtnClasses"
             style="line-height: 1.5;"
             container="body"
             [title]="btnEntry.title">
            <button
                    class="btn"
                    ngbDropdownToggle
                    [class]="btnClass + ' ' + (btnEntry.btn_classes || '')"
                    [disabled]="btnEntry.disabled">
                <ng-container
                        *ngTemplateOutlet="btnContentTemplate; context: {$implicit: btnEntry, btn: btnEntry}"
                ></ng-container>
            </button>

            <div ngbDropdownMenu [class]="ddWrapperClass">
                <div ngbDropdownItem
                     (click)="onBtnClick(childEntry, btnEntry)"
                     [class]="ddOptionClasses + ' ' + (childEntry.btn_classes || '') + ' option-padding d-flex'"
                     [disabled]="childEntry.disabled"
                     *ngFor="let childEntry of btnEntry.children">
                    <ng-container
                            *ngTemplateOutlet="btnContentTemplate; context: {$implicit: childEntry, btn: childEntry}"
                    ></ng-container>
                </div>
            </div>

        </div>
    </ng-container>

    <ng-template #notAChild>
        <button
                *ngIf="!btnEntry.hide"
                class="bg-element btn"
                [class]="btnClass + ' ' + (btnEntry.btn_classes || '')"
                [disabled]="btnEntry.disabled"
                [title]="btnEntry.title || btnEntry.label"
                (click)="onBtnClick(btnEntry)"
                [ngbTooltip]="btnEntry.tooltip_lines?.length ? tooltipContent: false">
            <ng-container
                    *ngTemplateOutlet="btnContentTemplate; context: {$implicit: btnEntry, btn: btnEntry}"
            ></ng-container>
        </button>
    </ng-template>

    <ng-template #tooltipContent>
        <ul class="mb-0 pl-3" *ngIf="btnEntry.tooltip_lines?.length">
            <li *ngFor="let content of btnEntry.tooltip_lines">
                {{ content }}
            </li>
        </ul>
    </ng-template>
</ng-container>

<ng-template #btnContentTemplate let-btnEntry="btn">
        <span *ngIf="btnEntry.mat_icon && (!btnEntry.icon_placement || btnEntry.icon_placement === 'left')"
              class="material-symbols-outlined x-large-font align-middle fill"
              [class.mr-1]="hasLabel" [ngClass]="btnEntry.icon_class">
            {{ btnEntry.mat_icon }}
        </span>

        <span *ngIf="btnEntry.label" class="medium-font"
            [ngClass]="{'ml-3': (btnEntry?.mat_icon && (!btnEntry?.icon_placement || btnEntry?.icon_placement === 'left'))}"
            [class]="btnEntry.label_class">
            {{ btnEntry.label }}
        </span>

    <span *ngIf="(btnEntry.icon_placement === 'right')"
          class="material-symbols-outlined x-large-font align-middle fill"
          [class.ml-1]="hasLabel" [ngClass]="btnEntry.icon_class">
                {{ btnEntry.mat_icon ? btnEntry.mat_icon : ' ' }}
            </span>
</ng-template>
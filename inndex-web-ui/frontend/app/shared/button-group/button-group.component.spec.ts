import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { NgbDropdownModule, NgbTooltipModule, NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { ButtonGroupComponent, ActionBtnEntry } from './button-group.component';

describe('ButtonGroupComponent', () => {
  let component: ButtonGroupComponent;
  let fixture: ComponentFixture<ButtonGroupComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ButtonGroupComponent],
      imports: [NgbDropdownModule, NgbTooltipModule]
    }).compileComponents();

    fixture = TestBed.createComponent(ButtonGroupComponent);
    component = fixture.componentInstance;
  });

  it('should create component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.ddBtnClasses).toBe('btn-action');
    expect(component.ddOptionClasses).toBe('cursor-pointer');
    expect(component.topLevelBtnClasses).toBe('btn-action p-1');
    expect(component.btnSizeClass).toBe('btn-sm');
    expect(component.hasLabel).toBe(false);
  });

  it('should set btnClass correctly in ngOnInit', () => {
    component.ngOnInit();
    expect(component.btnClass).toBe('btn-action p-1 btn-sm');
  });

  describe('Button Visibility', () => {
    it('should show button by default when hide is not set', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button'
      };
      component.buttons = [button];
      fixture.detectChanges();

      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement).toBeTruthy();
    });

    it('should hide button when hide is true', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        hide: true
      };
      component.buttons = [button];
      fixture.detectChanges();

      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement).toBeFalsy();
    });

    it('should handle multiple buttons with mixed visibility', () => {
      const buttons: ActionBtnEntry[] = [
        { key: 'visible1', label: 'Visible 1' },
        { key: 'hidden', label: 'Hidden', hide: true },
        { key: 'visible2', label: 'Visible 2' }
      ];
      component.buttons = buttons;
      fixture.detectChanges();

      const buttonElements = fixture.debugElement.queryAll(By.css('button'));
      expect(buttonElements.length).toBe(2);
    });
  });

  describe('Icon Styling', () => {
    it('should apply icon_class when provided', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        mat_icon: 'info',
        icon_class: 'custom-icon-class'
      };
      component.buttons = [button];
      fixture.detectChanges();

      const iconElement = fixture.debugElement.query(By.css('.material-symbols-outlined'));
      expect(iconElement).toBeTruthy();
      expect(iconElement.nativeElement.classList.contains('custom-icon-class')).toBe(true);
    });

    it('should handle icon placement correctly', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        mat_icon: 'info',
        icon_placement: 'right'
      };
      component.buttons = [button];
      component.hasLabel = true;
      fixture.detectChanges();

      const iconElement = fixture.debugElement.query(By.css('.material-symbols-outlined'));
      expect(iconElement).toBeTruthy();
      expect(iconElement.nativeElement.classList.contains('ml-1')).toBe(true);
    });

    it('should handle left icon placement by default', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        mat_icon: 'info'
      };
      component.buttons = [button];
      component.hasLabel = true;
      fixture.detectChanges();

      const iconElement = fixture.debugElement.query(By.css('.material-symbols-outlined'));
      expect(iconElement).toBeTruthy();
      expect(iconElement.nativeElement.classList.contains('mr-1')).toBe(true);
    });

    it('should not apply margin classes when hasLabel is false', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        mat_icon: 'info',
        icon_placement: 'right'
      };
      component.buttons = [button];
      component.hasLabel = false;
      fixture.detectChanges();

      const iconElement = fixture.debugElement.query(By.css('.material-symbols-outlined'));
      expect(iconElement).toBeTruthy();
      expect(iconElement.nativeElement.classList.contains('ml-1')).toBe(false);
    });

    it('should handle multiple icons in the same button', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        mat_icon: 'info',
        icon_class: 'custom-icon-class',
        icon_placement: 'right'
      };
      component.buttons = [button];
      component.hasLabel = true;
      fixture.detectChanges();

      const iconElements = fixture.debugElement.queryAll(By.css('.material-symbols-outlined'));
      expect(iconElements.length).toBe(1);
      expect(iconElements[0].nativeElement.classList.contains('custom-icon-class')).toBe(true);
      expect(iconElements[0].nativeElement.classList.contains('ml-1')).toBe(true);
    });
  });

  describe('Tooltip Functionality', () => {
    it('should show tooltip when tooltip_lines are provided', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        tooltip_lines: ['Line 1', 'Line 2']
      };
      component.buttons = [button];
      fixture.detectChanges();

      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement).toBeTruthy();
      const tooltipBinding = buttonElement.nativeElement.getAttribute('ngbtooltip');
      expect(tooltipBinding).not.toBe('false');
    });

    it('should not show tooltip when tooltip_lines are empty', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        tooltip_lines: []
      };
      component.buttons = [button];
      fixture.detectChanges();

      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement).toBeTruthy();
      const tooltipBinding = buttonElement.nativeElement.getAttribute('ngbtooltip');
      expect(tooltipBinding).toBeFalsy();
    });

    it('should render tooltip content correctly', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        tooltip_lines: ['Line 1', 'Line 2', 'Line 3']
      };
      component.buttons = [button];
      fixture.detectChanges();

      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement).toBeTruthy();
      const tooltipBinding = buttonElement.nativeElement.getAttribute('ngbtooltip');
      expect(tooltipBinding).not.toBe('false');
    });

    it('should not show tooltip when tooltip_lines is undefined', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button'
      };
      component.buttons = [button];
      fixture.detectChanges();

      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement).toBeTruthy();
      const tooltipBinding = buttonElement.nativeElement.getAttribute('ngbtooltip');
      expect(tooltipBinding).toBeFalsy();
    });

    it('should handle tooltip with empty lines', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        tooltip_lines: ['Line 1', '', 'Line 3']
      };
      component.buttons = [button];
      fixture.detectChanges();

      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement).toBeTruthy();
      const tooltipBinding = buttonElement.nativeElement.getAttribute('ngbtooltip');
      expect(tooltipBinding).not.toBe('false');
    });
  });

  describe('Dropdown Functionality', () => {
    it('should render dropdown when children are provided', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        children: [
          {
            key: 'child1',
            label: 'Child 1'
          }
        ]
      };
      component.buttons = [button];
      fixture.detectChanges();

      const dropdownElement = fixture.debugElement.query(By.css('[ngbdropdown]'));
      expect(dropdownElement).toBeTruthy();
    });

    it('should apply custom classes to dropdown items', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        children: [
          {
            key: 'child1',
            label: 'Child 1',
            btn_classes: 'custom-class'
          }
        ]
      };
      component.buttons = [button];
      fixture.detectChanges();

      const dropdownItem = fixture.debugElement.query(By.css('.dropdown-item'));
      expect(dropdownItem).toBeTruthy();
      expect(dropdownItem.nativeElement.classList.contains('custom-class')).toBe(true);
    });

    it('should handle disabled dropdown items', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        children: [
          {
            key: 'child1',
            label: 'Child 1',
            disabled: true
          }
        ]
      };
      component.buttons = [button];
      fixture.detectChanges();

      const dropdownItem = fixture.debugElement.query(By.css('.dropdown-item'));
      expect(dropdownItem).toBeTruthy();
      expect(dropdownItem.nativeElement.classList.contains('disabled')).toBe(true);
    });

    it('should handle multiple dropdown items', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        children: [
          {
            key: 'child1',
            label: 'Child 1'
          },
          {
            key: 'child2',
            label: 'Child 2',
            disabled: true
          },
          {
            key: 'child3',
            label: 'Child 3',
            btn_classes: 'custom-class'
          }
        ]
      };
      component.buttons = [button];
      fixture.detectChanges();

      const dropdownItems = fixture.debugElement.queryAll(By.css('.dropdown-item'));
      expect(dropdownItems.length).toBe(3);
      expect(dropdownItems[1].nativeElement.classList.contains('disabled')).toBe(true);
      expect(dropdownItems[2].nativeElement.classList.contains('custom-class')).toBe(true);
    });

    it('should handle empty children array', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        children: []
      };
      component.buttons = [button];
      fixture.detectChanges();

      const dropdownElement = fixture.debugElement.query(By.css('[ngbdropdown]'));
      expect(dropdownElement).toBeFalsy();
      
      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement).toBeTruthy();
      expect(buttonElement.nativeElement.textContent.trim()).toBe('Test Button');
    });

    it('should apply dropdown wrapper class', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        children: [
          {
            key: 'child1',
            label: 'Child 1'
          }
        ]
      };
      component.buttons = [button];
      fixture.detectChanges();

      const dropdownMenu = fixture.debugElement.query(By.css('.dropdown-menu'));
      expect(dropdownMenu).toBeTruthy();
      expect(dropdownMenu.nativeElement.classList.contains('py-0')).toBe(true);
    });

    it('should handle dropdown items with icons', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        children: [
          {
            key: 'child1',
            label: 'Child 1',
            mat_icon: 'info',
            icon_placement: 'right'
          }
        ]
      };
      component.buttons = [button];
      component.hasLabel = true;
      fixture.detectChanges();

      const dropdownItem = fixture.debugElement.query(By.css('.dropdown-item'));
      const iconElement = dropdownItem.query(By.css('.material-symbols-outlined'));
      expect(iconElement).toBeTruthy();
      expect(iconElement.nativeElement.classList.contains('ml-1')).toBe(true);
    });
  });

  describe('Event Handling', () => {
    it('should emit click event with button entry', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button'
      };
      component.buttons = [button];
      fixture.detectChanges();

      spyOn(component.onActionClick, 'emit');
      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement).toBeTruthy();
      buttonElement.nativeElement.click();

      expect(component.onActionClick.emit).toHaveBeenCalledWith({ 
        entry: button,
        parent: undefined 
      });
    });

    it('should emit click event with parent for child buttons', () => {
      const parent: ActionBtnEntry = {
        key: 'parent',
        label: 'Parent',
        children: [
          {
            key: 'child',
            label: 'Child'
          }
        ]
      };
      component.buttons = [parent];
      fixture.detectChanges();

      spyOn(component.onActionClick, 'emit');
      const dropdownItem = fixture.debugElement.query(By.css('.dropdown-item'));
      expect(dropdownItem).toBeTruthy();
      dropdownItem.nativeElement.click();

      expect(component.onActionClick.emit).toHaveBeenCalledWith({
        entry: parent.children[0],
        parent: parent
      });
    });

    it('should not emit click event for disabled buttons', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        disabled: true
      };
      component.buttons = [button];
      fixture.detectChanges();

      spyOn(component.onActionClick, 'emit');
      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement).toBeTruthy();
      buttonElement.nativeElement.click();

      expect(component.onActionClick.emit).not.toHaveBeenCalled();
    });

    it('should not emit click event for hidden buttons', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        hide: true
      };
      component.buttons = [button];
      fixture.detectChanges();

      spyOn(component.onActionClick, 'emit');
      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement).toBeFalsy();
      expect(component.onActionClick.emit).not.toHaveBeenCalled();
    });

    it('should emit click event for disabled dropdown items', () => {
      const parent: ActionBtnEntry = {
        key: 'parent',
        label: 'Parent',
        children: [
          {
            key: 'child',
            label: 'Child',
            disabled: true
          }
        ]
      };
      component.buttons = [parent];
      fixture.detectChanges();

      spyOn(component.onActionClick, 'emit');
      const dropdownItem = fixture.debugElement.query(By.css('.dropdown-item'));
      expect(dropdownItem).toBeTruthy();
      dropdownItem.nativeElement.click();

      expect(component.onActionClick.emit).toHaveBeenCalledWith({
        entry: parent.children[0],
        parent: parent
      });
    });
  });

  describe('Button Visibility Conditions', () => {
    it('should apply button visibility conditions', () => {
      const button1: ActionBtnEntry = {
        key: 'test1',
        label: 'Test Button 1'
      };
      const button2: ActionBtnEntry = {
        key: 'test2',
        label: 'Test Button 2'
      };
      component.buttons = [button1, button2];
      component.btnConditions = [true, false];
      component.ngOnInit();
      fixture.detectChanges();

      const buttonElements = fixture.debugElement.queryAll(By.css('button'));
      expect(buttonElements.length).toBe(1);
    });

    it('should not modify button visibility when conditions are not provided', () => {
      const button1: ActionBtnEntry = {
        key: 'test1',
        label: 'Test Button 1'
      };
      const button2: ActionBtnEntry = {
        key: 'test2',
        label: 'Test Button 2'
      };
      component.buttons = [button1, button2];
      component.btnConditions = [];
      component.ngOnInit();
      fixture.detectChanges();

      const buttonElements = fixture.debugElement.queryAll(By.css('button'));
      expect(buttonElements.length).toBe(2);
    });
  });

  describe('Button Styling', () => {
    it('should apply custom button classes', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        btn_classes: 'custom-btn-class'
      };
      component.buttons = [button];
      fixture.detectChanges();

      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement).toBeTruthy();
      expect(buttonElement.nativeElement.classList.contains('custom-btn-class')).toBe(true);
    });

    it('should apply label classes when provided', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        label_class: 'custom-label-class'
      };
      component.buttons = [button];
      fixture.detectChanges();

      const labelElement = fixture.debugElement.query(By.css('span'));
      expect(labelElement).toBeTruthy();
      expect(labelElement.nativeElement.classList.contains('custom-label-class')).toBe(true);
    });

    it('should apply multiple custom classes', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        btn_classes: 'custom-btn-class1 custom-btn-class2',
        label_class: 'custom-label-class1 custom-label-class2'
      };
      component.buttons = [button];
      fixture.detectChanges();

      const buttonElement = fixture.debugElement.query(By.css('button'));
      const labelElement = fixture.debugElement.query(By.css('span'));
      
      expect(buttonElement.nativeElement.classList.contains('custom-btn-class1')).toBe(true);
      expect(buttonElement.nativeElement.classList.contains('custom-btn-class2')).toBe(true);
      expect(labelElement.nativeElement.classList.contains('custom-label-class1')).toBe(true);
      expect(labelElement.nativeElement.classList.contains('custom-label-class2')).toBe(true);
    });

    it('should apply title attribute when provided', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button',
        title: 'Custom Title'
      };
      component.buttons = [button];
      fixture.detectChanges();

      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement.nativeElement.getAttribute('title')).toBe('Custom Title');
    });

    it('should use label as title when title is not provided', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button'
      };
      component.buttons = [button];
      fixture.detectChanges();

      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement.nativeElement.getAttribute('title')).toBe('Test Button');
    });

    it('should apply default button classes', () => {
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button'
      };
      component.buttons = [button];
      fixture.detectChanges();

      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement.nativeElement.classList.contains('btn-action')).toBe(true);
      expect(buttonElement.nativeElement.classList.contains('p-1')).toBe(true);
      expect(buttonElement.nativeElement.classList.contains('btn-sm')).toBe(true);
    });

    it('should apply custom button size class', () => {
      component.btnSizeClass = 'btn-lg';
      const button: ActionBtnEntry = {
        key: 'test',
        label: 'Test Button'
      };
      component.buttons = [button];
      fixture.detectChanges();

      const buttonElement = fixture.debugElement.query(By.css('button'));
      expect(buttonElement.nativeElement.classList.contains('btn-lg')).toBe(true);
    });
  });
});

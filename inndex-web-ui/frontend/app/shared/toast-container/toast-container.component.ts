import {Component, ViewChild} from "@angular/core";
import {ToastType, ToastService, Toast, FormDirtyService} from "@app/core";
import { AssetsUrl } from "../assets-urls/assets-urls";
import {SwUpdate} from '@angular/service-worker';
import {GenericConfirmationModalComponent} from '@app/shared';

@Component({
  selector: "toast-container",
  templateUrl: "./toast-container.component.html",
})
export class ToastContainerComponent {
  @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
  ToastType = ToastType;
  toastIcons = AssetsUrl.toastIcons;
  constructor(public toastService: ToastService,
              // private updates: SwUpdate,
              private formDirtyService: FormDirtyService) {}

  closeToast(toast: Toast) {
    this.toastService.close(toast);
  }

  pause(toast: Toast) {
    this.toastService.pauseToast(toast);
  }
  
  resume(toast: Toast) {
    this.toastService.resumeToast(toast);
  }

  reloadPage() {
    if (this.formDirtyService.isAnyFormDirty()) {
      this.confirmationModalRef.openConfirmationPopup({
        headerTitle: 'Warning',
        title: `You have unsaved changes. Are you sure you want to reload and lose your data?`,
        hasCancel: true,
        confirmLabel: 'Yes',
        cancelLabel: 'No',
        onConfirm: () => {
          // this.updates.activateUpdate().then(() => location.reload());
        },
        onClose: () => {
          return;
        }
      });
    } else{
      // this.updates.activateUpdate().then(() => location.reload());
    }
  }
}

<ng-template #toastTemplate let-toast>
    <div class="container p-0 m-0 border-solid-left rounded-sm" [class]="toast.containerClasses">
        <div class="custom-alert d-flex align-items-center rounded-right">
            <span class="toast-img mt-1 hide-block"><svg width="24" height="24"><use [attr.xlink:href]="toast.toastImg"></use></svg></span>
            <div>
                <h3 class="large-font toast-head">{{toast.title}}</h3>
                <small class="medium-font" [innerHTML]="toast.message"></small>
            </div>
            <button type="button" class="close" aria-label="Close" (click)="closeToast(toast)">
                <span class="toast-img"><svg width="20" height="20"><use [attr.xlink:href]="toastIcons.close"></use></svg></span>
            </button>
            <!--  Refresh Button-->
            <div class="refresh-button-container align-self-end">
                <button class="btn btn-primary" (click)="reloadPage()">Refresh</button>
            </div>
        </div>
        <!-- Progress Bar -->
        <div class="progress-bar-container w-100 hide-block">
            <div class="progress-bar h-100" [ngStyle]="{ 'width': toast.progress + '%' }"></div>
        </div>
    </div>
</ng-template>
<div class="toast-container position-fixed p-3 toast-position" >
    <ngb-toast class="toast-w-medium  cursor-pointer" *ngFor="let toast of toastService.toasts" [autohide]="false" [delay]="toast.remainingTime"
        (hidden)="closeToast(toast)" (mouseenter)="pause(toast)"
        (mouseleave)="resume(toast)"
        [class]="toast.wrapperClasses">
        <ng-template [ngTemplateOutlet]="toastTemplate" [ngTemplateOutletContext]="{ $implicit: toast }"></ng-template>
    </ngb-toast>
</div>

<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

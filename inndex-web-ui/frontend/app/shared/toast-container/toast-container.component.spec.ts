import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from "@angular/core/testing";
import { By } from "@angular/platform-browser";
import { Toast, ToastService } from "@app/core";
import { NgbToastModule } from "@ng-bootstrap/ng-bootstrap";
import { ToastContainerComponent } from "./toast-container.component";
import {SwUpdate} from '@angular/service-worker';

describe("ToastContainerComponent", () => {
  let component: ToastContainerComponent;
  let fixture: ComponentFixture<ToastContainerComponent>;
  let toastService: jasmine.SpyObj<ToastService>;

  beforeEach(async () => {
    const toastServiceSpy = jasmine.createSpyObj("ToastService", [
      "close",
      "pauseToast",
      "resumeToast",
    ]);
    toastServiceSpy.toasts = [];
    const swUpdateSpy = jasmine.createSpyObj('SwUpdate', ['activateUpdate']);

    TestBed.configureTestingModule({
      declarations: [ToastContainerComponent],
      imports: [NgbToastModule],
      providers: [
          { provide: ToastService, useValue: toastServiceSpy },
          { provide: SwUpdate, useValue: swUpdateSpy }
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ToastContainerComponent);
    component = fixture.componentInstance;
    toastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
    fixture.detectChanges();
  });

  it(`should create the component`, () => {
    expect(component).toBeTruthy();
  });

  it("should render no toasts if toastService.toasts is empty", () => {
    component.toastService.toasts = [];
    fixture.detectChanges();

    const toastElements = fixture.debugElement.queryAll(By.css("ngb-toast"));
    expect(toastElements.length).toBe(0);
  });

  it("should render correct number of toasts", () => {
    component.toastService.toasts = [
      { type: "Success", message: "Toast 1" } as Toast,
      { type: "Error", message: "Toast 2" } as Toast,
    ];
    fixture.detectChanges();

    const toastElements = fixture.debugElement.queryAll(By.css("ngb-toast"));
    expect(toastElements.length).toBe(2);
  });

  it("should display the correct message and type in toast and correct border class", () => {
    component.toastService.toasts = [
      {
        type: "Success",
        message: "Test success message.",
        containerClasses: "border-success",
      } as Toast,
    ];
    fixture.detectChanges();

    const toastElement = fixture.debugElement.query(By.css("ngb-toast"));
    const toastBodyElement = fixture.debugElement.query(
      By.css(".toast-body .container")
    );
    expect(toastElement.nativeElement.textContent).toContain(
      "Test success message."
    );
    expect(toastBodyElement.nativeElement.classList).toContain(
      "border-success"
    );
  });

  it(`should call 'closeToast' when close button is clicked`, () => {
    component.toastService.toasts = [
      { type: "Success", message: "Test Message" } as Toast,
    ];
    fixture.detectChanges();

    const closeButton = fixture.debugElement.query(By.css("button.close"));
    closeButton.triggerEventHandler("click", null);

    expect(toastService.close).toHaveBeenCalled();
  });

  it(`should call 'pauseToast' on mouse enter`, () => {
    const testToast = {
      type: "Success",
      message: "Item created successfully.",
    } as Toast;
    component.toastService.toasts = [testToast];
    fixture.detectChanges();

    const toastElement = fixture.debugElement.query(By.css("ngb-toast"));
    toastElement.triggerEventHandler("mouseenter", null);

    expect(toastService.pauseToast).toHaveBeenCalledWith(testToast);
  });

  it(`should call 'resumeToast' on mouse leave`, () => {
    const testToast = {
      type: "Success",
      message: "Item created successfully.",
    } as Toast;
    component.toastService.toasts = [testToast];
    fixture.detectChanges();

    const toastElement = fixture.debugElement.query(By.css("ngb-toast"));
    toastElement.triggerEventHandler("mouseleave", null);

    expect(toastService.resumeToast).toHaveBeenCalledWith(testToast);
  });

  it(`should call 'closeToast' on hidden event`, fakeAsync(() => {
    spyOn(component, "closeToast");
    // this hidden method won't be called when the toast is auto-hidden
    const toast = {
      type: "Success",
      message: "This is success toast.",
      remainingTime: 500,
    };
    toastService.toasts.push(toast);

    fixture.detectChanges();

    const toastElement = fixture.nativeElement.querySelector("ngb-toast");
    toastElement.dispatchEvent(new Event("hidden"));

    tick(700);

    expect(component.closeToast).toHaveBeenCalledWith(toast);
  }));
});

<style>
    .compact-design-cls {
        max-width: 100px !important;
    }
</style>
<style *ngIf="showDragnDrop">
    .my-drop-zone { border: dotted 3px #d3d3d3;margin: auto;padding: 30px 0px;background-color: #f4f4f4; }
    .nv-file-over { border: dotted 3px #d3a011; }
    .file-drop-container {width: 100%;}
    .formats-supported {display: block;font-size: 12px;color: gray;}
</style>
<style *ngIf="imageFitSquare">
    .upload-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100px;
    }
    .img-file-wrap {
        margin-top: 10px;
        min-width: 150px;
    }
    .squareImage {
        height: 110px;
        width: 110px;
        object-fit: cover;
        border-radius: 5px;
    }
</style>

<div>
    <div *ngIf="(!completed || hide_output)" [ngClass]="{'d-flex justify-content-between': ((!showDragnDrop || alternateUploadBtnSrc) && !addingFailed)}">
        <ng-container *ngIf="!showDragnDrop">
            <div class="mr-1" *ngIf="!showDragnDrop && !buttonSquareLg else squareButtonLg">
                <label class="mr-2 mb-0 font-weight-bold" *ngIf="uploaderTxtPostFix">{{ uploaderTxtPostFix }}</label>
                <input #fileInput type="file" class="d-none" (change)="onFileSelected($event)" />
                <button type="button" [disabled]="disabled && completed" (click)="fileInput.click()" *ngIf="!alternateUploadBtnSrc" [ngClass]="{'btn btn-sm btn-im-helmet':true}">{{chooseFileBtnText}}</button>
                <block-loader [show]="(showLoader)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
            </div>

            <ng-template #squareButtonLg>
                <input #fileInput type="file" class="d-none" (change)="onFileSelected($event)"/>
                <button type="button" class="btn btn-sm btn-im-helmet w-100 my-1" *ngIf="!alternateUploadBtnSrc" (click)="fileInput.click()"
                    [disabled]="disabled && completed"> {{chooseFileBtnText}} </button>
                <block-loader [show]="(showLoader)" [showBlockBackdrop]="true" [alwaysInCenter]="true"></block-loader>
            </ng-template>
        </ng-container>

        <div class="file-drop-container" *ngIf="showDragnDrop && !alternateUploadBtnSrc">
            <div [ngClass]="{'nv-file-over': hasBaseDropZoneOver}" class="row my-drop-zone"
                 (dragover)="fileOverBase($event)"
                 (dragleave)="hasBaseDropZoneOver = false"
                 (drop)="fileDrop($event)">
                <div class="col-8">
                    <span>{{dragnDropTxt}}</span>
                    <span class="formats-supported">
                        Supported Formats: {{formats}}
                    </span> 
                     <span class="formats-supported">
                        Max File Size: {{(maxFileSize/1048576)}} MB
                    </span>
                </div>
                <div class="col-4">
                    <input #fileInput type="file" class="d-none" [multiple]="multipleUpload" (change)="onFileSelected($event)"/>
                    <button type="button" [disabled]="disabled || completed" (click)="fileInput.click()" [ngClass]="{'float-right px-3 btn-sm btn btn-im-helmet':true}">Browse</button>
                </div>
            </div>
            <block-loader [show]="(showLoader)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
        </div>

        <div *ngIf="addingFailed" class="text-danger text-sm-left small">
            Unsupported file or size. Ensure it's a supported type and within size limits.
        </div>
        <div *ngIf="addingEncryptedPdf" class="text-danger text-sm-left small">
            The uploaded PDF file appears to be locked or encrypted. Please upload a non-encrypted file.
        </div>
        <ng-container *ngIf="showFileName && !addingFailed && lastItem">
            <div class="alignItems">
                <span class="d-inline-block text-truncate" style="max-width: 170px; font-size: 0.68em;" [ngClass]="{'compact-design-cls': compactDesign}">
                    <i>{{ lastItem?.file?.name }}</i>
                </span>
            </div>
        </ng-container>
        <!--
        <ng-container *ngIf="!autoStore && !addingFailed && lastItem">
            <div class="alignItems">
                <button type="button" class="btn btn-success ml-1 btn-sm"
                        (click)="lastItem?.upload()" [disabled]="lastItem.isReady || lastItem.isUploading || lastItem.isSuccess">
                    <i class="fa fa-upload"></i> Store
                </button>
            </div>
        </ng-container>
        -->
        <div style="width: 70%;" *ngIf="alternateUploadBtnSrc">
            <input #fileInput type="file" class="d-none" (change)="onFileSelected($event)" />
            <img style="width: 100%; height: auto; cursor: pointer;"
                 [src]="alternateUploadBtnSrc"
                 alt="Upload File"
                 (click)="fileInput.click()"
            />
            <block-loader [show]="(showLoader)" [showBlockBackdrop]="true" [alwaysInCenter]="true"></block-loader>
        </div>
    </div>

    <ng-container *ngIf="output_v2 else notOutputV2">
        <div *ngIf="completed" class="d-flex justify-content-between align-items-center border rounded-lg mt-2" [ngClass]="{'p-2': applyCustomPadding}">
            <p class="mb-0" [ngClass]="{'upload-name w-100': !showFileFullName, 'text-brandeis-blue': isBlueText, 'compact-design-cls': compactDesign}">
                <a [href]="completed.file_url" target="_blank" style="max-width: 220px;">{{completed.name}}</a>
            </p>
            <span *ngIf="showDeleteBtn" (click)="deleteFile(completed)" class="material-symbols-outlined attachment-cancel-icon medium-font ml-2 cursor-pointer">close</span>
            </div>
    </ng-container>
    <ng-template #notOutputV2>
        <div *ngIf="completed && !hide_output && !hasImgAndDoc" class="d-flex justify-content-between align-items-center"
            [ngClass]="{
                'flex-column': showContantAsColumn,
                'my-1': showViewFileModal,
                'p-2': applyCustomPadding,
                'border rounded-lg': !(completed.id && showThumbnail)
            }">
            <p class="mb-0" [ngClass]="{'upload-name w-100': !showFileFullName, 'text-brandeis-blue': isBlueText, 'text-center': showContantAsColumn, 'compact-design-cls': compactDesign,
                'd-none': ((!uploaderTxtPostFix) && (!showHyperlink) && (!showViewFileModal) && !(!showHyperlink && showFileName))
            }">
                <label class="mr-2 mb-0 font-weight-bold" *ngIf="uploaderTxtPostFix">{{ uploaderTxtPostFix }}</label>
            <a *ngIf="showHyperlink" target="_blank" [href]="completed.file_url">{{ completed?.name }}  <span *ngIf="showSize">({{(completed?.size / (1024*1024)).toFixed(2)}} MB)</span></a>
            <a *ngIf="showViewFileModal" href="javascript:void(0)" (click)="viewFileModal(completed.file_url)">{{ completed?.name }}</a>
            <span *ngIf="!showHyperlink && showFileName" [title]="completed?.name">{{ completed?.name }}</span>
        </p>

            <ng-container *ngIf="completed.id && showThumbnail; else onlyDeleteBtn">
                <ng-container
                        [ngTemplateOutlet]="uploadedFilePreview"
                        [ngTemplateOutletContext]="{ $implicit: completed }"
                ></ng-container>
            </ng-container>
        <ng-template #onlyDeleteBtn>
                <span *ngIf="completed.id && showDeleteBtn" (click)="deleteFile(completed)" class="material-symbols-outlined attachment-cancel-icon medium-font cursor-pointer ml-2">close</span>
        </ng-template>
    </div>
        <div *ngIf="completed && !hide_output && hasImgAndDoc" class="d-flex justify-content-between align-items-center border rounded-lg mt-2"
            [ngClass]="{
                'flex-column': showContantAsColumn,
                'my-1': showViewFileModal,
                'p-2': applyCustomPadding
            }">
            <p class="mb-0" [ngClass]="{'upload-name w-100': !showFileFullName, 'text-brandeis-blue': isBlueText, 'text-center': showContantAsColumn, 'compact-design-cls': compactDesign}">
                <label class="mr-2 mb-0 font-weight-bold" *ngIf="uploaderTxtPostFix">{{ uploaderTxtPostFix }}</label>
                <a target="_blank" [href]="completed.file_url">{{ completed?.name }}  <span *ngIf="showSize">({{(completed?.size / (1024*1024)).toFixed(2)}} MB)</span></a>
            </p>
            <span *ngIf="completed.id && showDeleteBtn" (click)="deleteFile(completed)" class="material-symbols-outlined attachment-cancel-icon medium-font cursor-pointer ml-2">close</span>
        </div>
    </ng-template>
</div>

<i-modal #viewFileRef [title]="fileModalTitle" [showCancel]="false" size="lg" (onClickRightPB)="closePhotoModal($event)" rightPrimaryBtnTxt="Close">
        <div class="form-group row">
            <div class="col-sm-12 mt-2 text-center">
                <div *ngIf="!isPDF(fileUrl)" style="width: 600px; display:inline-block;" class="d-inline-block">
                    <img [src]="fileUrl" style="width: 100%; height: auto;" #img />
                </div>

                <iframe *ngIf="fileUrl && isPDF(fileUrl)" class="border-0" [src]="filePreviewURL" width="750px" height="500px">
                </iframe>
            </div>
        </div>

</i-modal>

<i-modal #viewImgGalleryRef size="lg" [showFooter]="false">
        <div class="form-group row">
            <div class="col-sm-12 mt-2 text-center pl-0 pr-0">
                <ngb-carousel #myCarousel="ngbCarousel" [showNavigationIndicators]="showNavigationIndicators" [activeId]="activeId" (slide)="slideChange($event)">
                    <ng-template *ngFor="let image of carouselImages;let j = index" id="slide-{{j}}" ngbSlide>
                        <div class="picsum-img-wrapper d-inline-block">
                            <img *ngIf="image.preloaded" class="img-cover" [src]="image.file_url" alt="Random slide" style="max-height: 600px;"/>
                        </div>
                        <div class="d-inline-block w-100">
                            <div style="width: 20%" class="d-inline-block">
                                <span class="float-right">{{j+1}} of {{carouselImages.length}}</span>
                            </div>

                            <button title="Download Image" class="float-right btn btn-sm btn-outline-primary mr-4"
                                    (click)="downloadImage(image.file_url, image.name)">Download</button>
                        </div>
                    </ng-template>
                </ngb-carousel>
            </div>
        </div>
</i-modal>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

<ng-template #uploadedFilePreview let-item>
    <div class="img-file-wrap" style="margin-bottom: 5px;" *ngIf="imageMimeType.includes(item.file_mime)">
        <span *ngIf="showDeleteBtn" (click)="deleteFile(item)" type="button" class="close-icon text-white delete">
            <span class="bg-danger" aria-hidden="true">×</span>
        </span>
        <img (click)="viewFileModal(item?.file_url)" [src]="item?.file_url" class="cursor-pointer squareImage mb-2 col-md-10 p-0" alt="" />
    </div>
    <!-- for non-image files -->
    <div class="img-file-wrap" *ngIf="pdfMimeType.includes(item.file_mime)">
        <span *ngIf="showDeleteBtn" (click)="deleteFile(item)" type="button" class="close-icon text-white delete">
                <span class="bg-danger" aria-hidden="true">×</span>
        </span>
        <img (click)="downloadImage(item.file_url, item.name)" [title]="item.name" [src]="pdfFileIcon" class="cursor-pointer squareImage mb-2 col-md-10 p-0" height="20" alt="" #img2>
    </div>
</ng-template>

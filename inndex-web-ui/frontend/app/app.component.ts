import {Component, OnInit} from '@angular/core';
import {AppConstant, environment} from './../environments/environment';
import {Title} from "@angular/platform-browser";
import {NgbDateParserFormatter, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {MomentDateFormatter} from "@app/core/MomentDateFormatter";
import {NgcCookieConsentService} from 'ngx-cookieconsent';
// import * as FullStory from '@fullstory/browser';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';
import * as advancedFormat from 'dayjs/plugin/advancedFormat';
import * as customParseFormat from 'dayjs/plugin/customParseFormat';
import * as isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import * as isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import * as isoWeek from 'dayjs/plugin/isoWeek';
import * as duration from 'dayjs/plugin/duration';
import * as relativeTime from 'dayjs/plugin/relativeTime';
import {Event, NavigationCancel, NavigationEnd, NavigationError, NavigationStart, Router} from "@angular/router";
import {AuthService, HttpService, ToastService} from './core';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(advancedFormat);
dayjs.extend(customParseFormat);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);
dayjs.extend(isoWeek);
dayjs.extend(relativeTime)
dayjs.extend(duration);

declare const gtag: Function;

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.css'],
    providers: [
        {provide: NgbDateParserFormatter, useClass: MomentDateFormatter}
    ]
})
export class AppComponent implements OnInit {
    is_mobile_device: boolean = false;
    title = AppConstant.appName;
    timeoutSec = AppConstant.idealTimeoutSec;
    duration: number = null;
    navigating = false;
    uid = 'anon';

    public constructor(
        private titleService: Title,
        private authService: AuthService,
        private router: Router,
        private httpService: HttpService,
    ) {

        this.authService.authUser.subscribe((user) => {
            if (user && user.id) {
                this.uid = user.id.toString();
            }
        });

        this.is_mobile_device = this.httpService.isMobileDevice();
        this.setTitle(this.title);
        this.router.events.subscribe((event: Event) => {
            if (event instanceof NavigationEnd) {
                gtag('config', AppConstant.googleAnalyticsID, {
                'page_path': event.urlAfterRedirects,
                'user_id': this.uid,
                });
            }

            switch (true) {
                case event instanceof NavigationStart: {
                    this.navigating = true;
                    break;
                }

                case event instanceof NavigationEnd:
                case event instanceof NavigationCancel:
                case event instanceof NavigationError: {
                    this.navigating = false;
                    break;
                }
                default: {
                    break;
                }
            }
        });
        // this.initFullStory();
    }

    public setTitle(newTitle: string) {
        this.titleService.setTitle(newTitle);
    }

    ngOnInit() {
    }

    remainingTime() {
        return `${this.timeoutSec - (this.duration ? this.duration : 0)} secs`
    }
    /*
    initFullStory(){
        if(AppConstant.fullStoryOrgId){
            FullStory.init({
                orgId: AppConstant.fullStoryOrgId,
                namespace: 'FStory',
                devMode: !environment.production
            });
        }
    }*/
}

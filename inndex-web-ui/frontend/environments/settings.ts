import {innDexConstant} from './constants';

const countryOfSite = ['gb', 'us', 'ca','au', 'de', 'pl', 'ro', 'ru', 'es'];
const defaultCountryOfSite = 'gb';
const getCountryOfSite = (): string => {
    let selectedCountry = window.location.pathname.split('/')[1] || '';
    return (countryOfSite.includes(selectedCountry) && selectedCountry) || defaultCountryOfSite;
};

const settings = {
    'gb': {
        defaultDateFormat: 'DD-MM-YYYY',
        displayDateFormat: 'D/MMM/YYYY',
        dateFormat_D_MM_YYYY: 'D/MM/YYYY',
        fullDateTimeFormat: 'DD/MM/YYYY HH:mm:ss',
        dateTime_DD_MM_YYYY_hh_mm_A: 'DD/MM/YYYY hh:mm A',
        dateFormat_MM_DD_YYYY: 'MM-DD-YYYY',
        dateTimeFormat_DD_MM_YYYY_HH_MM_SS: 'DD-MM-YYYY HH:mm:ss',
        dateTimeFormat_D_MMM_YYYY_HH_MM_SS: 'D/MMM/YYYY HH:mm:ss',
        dateTimeFormat_D_MMM_YYYY_HH_MM: 'D/MMM/YYYY HH:mm',
        dateTime_MMM_D__YY_at_H_mm: 'MMM D [\']YY [at] H:mm',
        dateFormat_Do_MMM_YYYY: 'Do MMM YYYY',
        dayFormat_DD_MM: 'DD/MM',
        dayFormat_week_Do_MMMM: 'dddd Do, MMMM',
        dateFormat_MMM_DD_comma_YYYY: 'MMM DD, YYYY',
        fullDateTimeFormat_without_ss: 'DD/MM/YYYY HH:mm',
    },
    'us': {
        defaultDateFormat: 'MM-DD-YYYY',
        displayDateFormat: 'MMM/D/YYYY',
        dateFormat_D_MM_YYYY: 'MM/D/YYYY',
        fullDateTimeFormat: 'MM/DD/YYYY HH:mm:ss',
        dateTime_DD_MM_YYYY_hh_mm_A: 'MM/DD/YYYY hh:mm A',
        dateFormat_MM_DD_YYYY: 'MM-DD-YYYY',
        dateTimeFormat_DD_MM_YYYY_HH_MM_SS: 'MM-DD-YYYY HH:mm:ss',
        dateTimeFormat_D_MMM_YYYY_HH_MM_SS: 'MMM/D/YYYY HH:mm:ss',
        dateTimeFormat_D_MMM_YYYY_HH_MM: 'MMM/D/YYYY HH:mm',
        dateTime_MMM_D__YY_at_H_mm: 'MMM D [\']YY [at] H:mm',
        dateFormat_Do_MMM_YYYY: 'MMM Do YYYY',
        dayFormat_DD_MM: 'MM/DD',
        dayFormat_week_Do_MMMM: 'dddd Do, MMMM',
        dateFormat_MMM_DD_comma_YYYY: 'MMM DD, YYYY',
        fullDateTimeFormat_without_ss: 'MM/DD/YYYY HH:mm',
    },
    'ca': {
        defaultDateFormat: 'MM-DD-YYYY',
        displayDateFormat: 'MMM/D/YYYY',
        dateFormat_D_MM_YYYY: 'MM/D/YYYY',
        fullDateTimeFormat: 'MM/DD/YYYY HH:mm:ss',
        dateTime_DD_MM_YYYY_hh_mm_A: 'MM/DD/YYYY hh:mm A',
        dateFormat_MM_DD_YYYY: 'MM-DD-YYYY',
        dateTimeFormat_DD_MM_YYYY_HH_MM_SS: 'MM-DD-YYYY HH:mm:ss',
        dateTimeFormat_D_MMM_YYYY_HH_MM_SS: 'MMM/D/YYYY HH:mm:ss',
        dateTimeFormat_D_MMM_YYYY_HH_MM: 'MMM/D/YYYY HH:mm',
        dateTime_MMM_D__YY_at_H_mm: 'MMM D [\']YY [at] H:mm',
        dateFormat_Do_MMM_YYYY: 'MMM Do YYYY',
        dayFormat_DD_MM: 'MM/DD',
        dayFormat_week_Do_MMMM: 'dddd Do, MMMM',
        dateFormat_MMM_DD_comma_YYYY: 'MMM DD, YYYY',
        fullDateTimeFormat_without_ss: 'MM/DD/YYYY HH:mm',
    },
    'ae': {
        defaultDateFormat: 'DD-MM-YYYY',
        displayDateFormat: 'D/MMM/YYYY',
        dateFormat_D_MM_YYYY: 'D/MM/YYYY',
        fullDateTimeFormat: 'DD/MM/YYYY HH:mm:ss',
        dateTime_DD_MM_YYYY_hh_mm_A: 'DD/MM/YYYY hh:mm A',
        dateFormat_MM_DD_YYYY: 'MM-DD-YYYY',
        dateTimeFormat_DD_MM_YYYY_HH_MM_SS: 'DD-MM-YYYY HH:mm:ss',
        dateTimeFormat_D_MMM_YYYY_HH_MM_SS: 'D/MMM/YYYY HH:mm:ss',
        dateTimeFormat_D_MMM_YYYY_HH_MM: 'D/MMM/YYYY HH:mm',
        dateTime_MMM_D__YY_at_H_mm: 'MMM D [\']YY [at] H:mm',
        dateFormat_Do_MMM_YYYY: 'Do MMM YYYY',
        dayFormat_DD_MM: 'DD/MM',
        dayFormat_week_Do_MMMM: 'dddd Do, MMMM',
        dateFormat_MMM_DD_comma_YYYY: 'MMM DD, YYYY',
        fullDateTimeFormat_without_ss: 'DD/MM/YYYY HH:mm',
    },
};

const settingByLocale = (locale) => {
    return (settings[locale] || settings[defaultCountryOfSite]);
};

export const activeSetting = settingByLocale(getCountryOfSite());

export const commonSetting = {
    apiRequestDateFormat: 'YYYY-MM-DD',
    dateStorageFormat: 'YYYY-MM-DD',
}

// console.log('Active setting is', activeSetting)

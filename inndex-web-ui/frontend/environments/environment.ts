// This file can be replaced during build by using the `fileReplacements` array.
// `ng build ---prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

import {activeSetting, commonSetting} from './settings';

export const environment = {
    production: false
};

const apiServerUrl = 'http://localhost:1337';
const fileServerUrl = 'http://localhost:3000';

export const AppConstant = {
    appName: 'innDex - Portal',
    apiServerUrl: apiServerUrl,
    fileServerUrl: fileServerUrl,
    idealSec: 300,
    updateCheckIntervalMs: 300000,
    idealTimeoutSec: 15,
    redirectAfterLogin: ['/site-user'],
    sentryDNS: null,
    // fullStoryOrgId: null,
    defaultCountryCode: 'GB',
    developmentEnv: true,
    apiRequestDateFormat: commonSetting.apiRequestDateFormat,
    dateStorageFormat: commonSetting.dateStorageFormat,
    dateFormat_MMM_DD_comma_YYYY: activeSetting.dateFormat_MMM_DD_comma_YYYY,
    defaultDateFormat: activeSetting.defaultDateFormat,
    displayDateFormat: activeSetting.displayDateFormat,
    dateFormat_D_MM_YYYY: activeSetting.dateFormat_D_MM_YYYY,
    fullDateTimeFormat: activeSetting.fullDateTimeFormat,
    dateTime_DD_MM_YYYY_hh_mm_A: activeSetting.dateTime_DD_MM_YYYY_hh_mm_A,
    dateFormat_MM_DD_YYYY: activeSetting.dateFormat_MM_DD_YYYY,
    dateTimeFormat_DD_MM_YYYY_HH_MM_SS: activeSetting.dateTimeFormat_DD_MM_YYYY_HH_MM_SS,
    dateTimeFormat_D_MMM_YYYY_HH_MM_SS: activeSetting.dateTimeFormat_D_MMM_YYYY_HH_MM_SS,
    dateTimeFormat_D_MMM_YYYY_HH_MM: activeSetting.dateTimeFormat_D_MMM_YYYY_HH_MM,
    dateTime_MMM_D__YY_at_H_mm: activeSetting.dateTime_MMM_D__YY_at_H_mm,
    dateFormat_Do_MMM_YYYY: activeSetting.dateFormat_Do_MMM_YYYY,
    dayFormat_DD_MM: activeSetting.dayFormat_DD_MM,
    dayFormat_week_Do_MMMM: activeSetting.dayFormat_week_Do_MMMM,
    defaultTimeFormat: 'HH:mm:ss',
    timFormatWithoutSecs: 'HH:mm',
    appStoreUrl: 'https://itunes.apple.com/gb/app/inndex/id1457696057?mt=8',
    playStoreUrl: 'https://play.google.com/store/apps/details?id=me.innDex.mobile',
    cookiesPolicyUrl: 'https://inndex.co.uk/cookie-policy/',
    mapboxglKey:'pk.eyJ1Ijoic2FtYWRkIiwiYSI6ImNscGt3bHpvbTAwejUybG1xa3NyN282NWsifQ.rnYLeiYJidL35uA-5VG3zA', // for dashboard map
    googleMapSdkAPIKey: 'AIzaSyAov0DyJfyuO-s0WQ8K-Ru_avsrY_PC9r0', // for User time log popup
    idealPstCdAPIKey: 'ak_jxnjp5qd67Jr4hfdf4i41PwnjeuCH',
    captchaSiteKey: '6Ld1HJ4UAAAAACTYPH7LFBrYXVrI3-FcKy6qPHM9',
    pageSize:50,
    googleAnalyticsID: 'G-2RQKXS6RNB',
    decimelConfig: '.0-4',
    microsoftLoginUrl: apiServerUrl +'/azure/authorize',
    analyticsId: 'qjypqabk94',
};

/*
 * In development mode, for easier debugging, you can ignore zone related error
 * stack frames such as `zone.run`/`zoneDelegate.invokeTask` by importing the
 * below file. Don't forget to comment it out in production mode
 * because it will have a performance impact when errors are thrown
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.

/* You can add global styles to this file, and also import other style files */
@import "assets/css/common.css";
@import "assets/css/multi-step-form.css";
@import "~@ng-select/ng-select/themes/default.theme.css";
@import "assets/scss/toastr.scss";


//$primary: #14152D;
$select:#F2F2F2;
/** View-project-compomnent - 'Health Concerns' modal custom radius css */
.custom-modal-content-redias {
  .modal-content {
    border-radius: 15px !important;
    .modal-header {
      border-top-left-radius: 15px !important;
      border-top-right-radius: 15px !important;
    }
  }
}

/** To show maltipel month view calamder vertical */
@media (max-width: 414px) {
  .ngb-dp-months {
    display: block !important;
  }
}

/** incident-reports page - Incident-Review custom css */
.incident-reports-review {
  .card { margin-bottom: 0.5rem; }
  .card-body { padding: 0px !important; }
}
.dark-panel-head {
  .card-body{
    padding: 0 1.2rem;
  }
  .btn-link{
    color: #343a40;
  }
}

.circle-fs-5 {
  font-size: 5px !important;
}
.attachment-cancel-icon {
  border-radius: 50%;
  background-color: var(--light-gray);
  color: #6c757d;
  opacity: .75;
  padding: 1px;
}
.attachment-cancel-icon:hover {
  opacity: 1;
}

/* Sticky footer styles
-------------------------------------------------- */
.form-signup, .form-signin {
  width: 100%;
  min-width: 330px;
  padding: 15px;
  margin: 0 auto;
}

.form-signup .checkbox, .form-signin .checkbox {
  font-weight: 400;
}

.form-signup .form-control, .form-signin .form-control {
  position: relative;
  box-sizing: border-box;
  height: auto;
  padding: 10px;
  font-size: 16px;
}

.form-signup .form-control:focus, .form-signin .form-control:focus {
  z-index: 2;
}

.form-signup input[type="email"], .form-signin input[type="email"] {
  margin-bottom: -1px;
  // border-bottom-right-radius: 0;
  // border-bottom-left-radius: 0;
}

.form-signup input[type="password"], .form-signin input[type="password"] {
  margin-bottom: 10px;
  // border-top-left-radius: 0;
  // border-top-right-radius: 0;
}

.form-group .invalid-feedback {
  display: block;
}

.time-mgt-tab-min-h {
  min-height: calc(100vh - 210px);
}

.v-align {
  vertical-align: middle !important;
}

/* Sign Up stages*/
.alert {
  padding: 8px;
}

.ng-untouched.ng-invalid:not(form):not([type="hidden"]) + .alert,
.ng-untouched:not(form):not([type="hidden"]) + .alert {
  display: none;
}

.ng-touched.ng-valid[required]:not(ngb-timepicker):not(ngb-timepicker *),
.ng-touched.ng-valid.required:not(ngb-timepicker):not(ngb-timepicker *) {
  border-left: 5px solid var(--success); /* green */
}


.ng-touched.ng-invalid:not(form):not(ngb-timepicker):not(ngb-timepicker *) {
  border-left: 5px solid var(--danger);
}

ngb-timepicker.ng-touched.ng-valid 
  .ngb-tp 
  .ngb-tp-input-container.ngb-tp-hour 
  .ngb-tp-input {
  border-left: 5px solid var(--success);
}


ngb-timepicker.ng-touched.ng-invalid 
  .ngb-tp 
  .ngb-tp-input-container.ngb-tp-hour 
  .ngb-tp-input {
  border-left: 5px solid var(--danger);
}

.board-inner .nav {
  position: relative;
}

.liner {
  height: 2px;
  background: #ddd;
  position: absolute;
  width: 95%;
  margin: 0 auto;
  left: 0;
  right: 0;
  top: 100%;
  z-index: 1;
}

.btn-group-xs > .btn, .btn-xs {
  padding: .25rem .4rem;
  font-size: .875rem;
  line-height: .5;
  border-radius: .2rem;
}

.checklist-class .modal-lg{
  width: 35%;
}
.checklist-class .modal-xl{
  width: 66%;
}

.horizontal-center{
	display: flex;
	align-items: center !important;
}

.gap-8 {
  gap: 8px;
}

.checklist-class .modal-dialog{
  max-width: 1400px !important;
  transition: width 1s ease !important;
  max-height: 92vh !important;
}

.max-w-100 {
  max-width: 100% !important;
}

.xl-modal {
  .modal-dialog{
    height: 96vh;
    margin: 0 auto;
  }
  .modal-content {
    border-radius: 10px;
    height: inherit;
    margin-top: 1rem
  }
  .modal-header {
    align-items: center;
    background-color: var(--cultured);
    height: auto;
    border-radius: 10px 10px 0 0;
    border-bottom: none;
  }
  .modal-body{
    padding: 0;
  }
  .scroll-wrapper {
    -webkit-overflow-scrolling: touch;
    // overflow-y: scroll;
  }
}
.xl-modal.modalHeightAuto {
    .modal-content {
        height: auto;
    }
}
.action-column {
  min-width: 165px;
}
@media (min-width: 992px) {
  .xl-modal .modal-dialog, .l-modal .modal-dialog {
    max-width: 990px;
  }
}
@media (min-width: 576px) {
  .xl-modal .modal-dialog {
    max-width: 90%;
  }

  .l-modal .modal-dialog {
      max-width: 70%;
  }

  .w-md-50{
    width: 50%;
  }
}
@media (max-width: 538px) {
  .xl-modal .modal-footer{
    display: inline;
    .btn{
      margin: 4px 4px 4px 0 !important;
    }
  }
}
@media (max-width: 768px) {
  .stepwizard .small {
    font-size: .6em;
  }
  .content-page .back-btn{
    font-size: .6em;
  }
  .table-responsive-sm .ngx-datatable{
    width: 100%;
  }
}
@media (min-width: 768px) and (max-width: 1024px) {
  .tablet-full-width {
      width: 100% !important;
      max-width: 100%;
      flex: 0 0 100%;
  }
  .tablet-ml {
      margin-left: 30px;
  }
}
@media (max-width: 576px) {
  .w-sm-100 {
    width: 100%;
  }
  .m-ml-2 {
    margin-left: 8px;
  }
}

/*======================================Added for fix Dashboard================================*/
ngb-modal-window.use-site-as-modal > div.modal-dialog {
  margin: auto;
}
.dash-section-1 {
  min-height: 128px;
  .content-text {
    width: 75px !important;
    height: 75px !important;
    min-width: 75px !important;
    min-height: 75px !important;
    display: block !important;
    padding-bottom: 0 !important;
    margin: auto;
    p {
      font-size: 1em !important;
      top: 50%;
    }
  }
}
.dash-section-2 {
  .progress-text {
    top: 3px !important;
  }
  .custom-end-date {
    margin-left: 5px;font-size: small
  }
  .custom-start-date {
    margin-right: 5px; font-size: small;
  }
  .custom-progress-bar {
    width: 83%;margin: auto;display: inline-flex;
  }
}
@media only screen
and (min-device-width : 768px)
and (max-device-width : 1024px)
and (orientation : landscape) {
  .dash-section-1 .content-text {
    width: 60px !important;
    height: 60px !important;
    min-width: 60px !important;
    min-height: 60px !important;
  }
}

@media only screen
and (min-device-width : 768px)
and (max-device-width : 1024px)
and (orientation : portrait) {
  .dash-section-1 .the-four-crl {
    max-width: 100%;
    flex: 100%;
  }
  .dash-section-1 weather-blocks {
    min-width: 100%;
    font-size: 100%;
   }
}
.card-shadow {
  box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.05);
  border: 0;
}
.adjusted-pad {
  padding-right: 0;
  padding-left: 10px;
}
.adjusted-cust-pad {
  padding-left: 10px;
}
.media-body {
  line-height: 1.2;
}
.fn-11 {
  font-size: 11px;
}
.firstaider-height::-webkit-scrollbar,
.supervisor-height::-webkit-scrollbar {
  width: 5px;
}
.firstaider-height::-webkit-scrollbar-thumb,
.supervisor-height::-webkit-scrollbar-thumb {
  background: #888;
}
/*======================================Only For Mobile Devices================================*/

.mobile-device {
  .adjusted-pad, .adjusted-cust-pad {
    padding-right: 15px !important;
    padding-left: 15px !important;
  }
  .table-responsive-sm {
    overflow-y: hidden;
  }
  .mobile-nav {
    &.app-side-nav, &.company-side-nav {
      padding-top: 10px !important;
      &.side-nav-drawer-closed {
        min-height: 0;
        .sidebar {
          width: 45px !important;
        }
      }
      .side-nav-drawer {
        padding: 8px 10px;
        & > span.drawer-icon {
          font-size: 23px;
          color: #fff;
          cursor: pointer;
        }
      }
    }
  }
  .ngx-datatable .datatable-body-cell, .ngx-datatable .datatable-header-cell {
    overflow-x: visible !important;
  }

  .footer {
    padding: 0 !important;
  }

  action-button {
      display: flex;
    flex-grow: 1;

    .m-btn-size-lg {
      padding: 0.5rem 1rem;
      font-size: 1.25rem;
      line-height: 1.5;
      border-radius: 0.3rem;
    }

    .m-w-100 {
      width: 100%;
    }
  }

  .other-action-btn {
    padding: 0.5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;

    .m-font-size {
      font-size: 1.25rem;
    }
  }
}

.outer-border {
  margin-right: -20px;
}
.outer-border-radius {
  border: 1px solid #EDEDED;
  border-radius: 10px;
  padding-top: 20px !important;
}
.custom-tab-radius{
  border-radius: 10px;
  border-top-left-radius: 0;
}
.nav-tabs{
  width: calc(100% - 7px);
}
.archive-table {
  .datatable-scroll {
    width: 100% !important;
  }
}

.ins-participants {
  .ng-dropdown-panel {
    .ng-dropdown-header{
      padding: 5px 5px !important;
    }
    .ng-dropdown-panel-items {
      .ng-option {
        box-sizing: border-box;
        cursor: pointer;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border-bottom: 1px solid #D9D9D9;
        padding-top: 12px;
        padding-bottom: 12px;
      }
    }
  }
}
.ins-participants {
  .ng-select-container {
    .ng-value-container {
      .ng-value {
        display: flex;
        font-size: 0.9em;
        margin-bottom: 5px;
        background-color: white !important;
        border-radius: 2px;
        margin-right: 5px;
        border: 1px solid #D9D9D9 !important;
        border-radius: 20px !important;
        flex-direction: row-reverse !important;
        padding: 3px !important;
      }
    }
  }
}
.ins-participants {
  .ng-select-container {
    .ng-value-container {
      .ng-value {
        .ng-value-icon.left {
          border-right: none !important;
          border-radius: 50% !important;
        }
      }
    }
  }
}
.ins-participants {
  .ng-select-container {
    .ng-value-container {
      .ng-value {
        .ng-value-icon:hover {
          background-color: #F9F9F9 !important;
        }
      }
    }
  }
}
.ng-select.ir-step-selector {
  .ng-dropdown-panel {
    .ng-dropdown-panel-items {
      .ng-option {
        padding: 0px 0px 0px 10px !important;
        .dropdown-item{
          &:hover {
            background-color: unset;
          }
        }
      }
    }
  }
  .ng-select-container {
    .ng-value-container {
      .ng-value {
        width: 100%;
        display: flex;
        justify-content: space-between;
      }
    }
  }
}

.custom-signature-pad .signature-pad {
      canvas {
        width: 100% !important;
      }
}
.assesment-form-sign .signature-pad {
  canvas {
    width: 100% !important;
  }
}
.sign-cancel:hover {
	background: none;
	color: #0066FF !important;
}

.col-one {
  width: 630px !important;
  min-width: 580px !important;
}
.col-two {
  width: 100% !important;
}
.nav-tabs-v2 {
	.nav-item {
		padding-right: 0;
		background-color: var(--anti-flash-white);
		&:first-child {
			border-top-left-radius: 5px;
		}
		&:last-child {
			border-top-right-radius: 5px;
		}
	}
}
.nav-tabs-v2.nav-tabs {
	.nav-item {
		&:last-child {
			.nav-link {
				&:not(.default) {
					border-top-right-radius: 5px;
				}
			}
		}
		&:first-child {
			.nav-link {
				&:not(.default) {
					border-top-left-radius: 5px;
				}
			}
		}
	}
	.nav-link {
		&:not(.default) {
			border-top-left-radius: 0px;
			border-top-right-radius: 0px;
		}
		border-top-left-radius: 0px;
		border-top-right-radius: 0px;
	}
	.nav-link.active {
		&:not(.default) {
			font-weight: normal;
			background-color: var(--white);
			border-color: var(--white);
			border-top-left-radius: 5px;
			border-top-right-radius: 5px;
			position: relative;
			z-index: 99;
		}
	}
}
.nav-tabs-v2.n-tab {
	.nav-a {
		background: #F2F2F2;
		color: var(--spanish-gray);
	}
}
.nav-panel-v2 {
  background-color: var(--white);
  border-radius: 5px;
  margin-top: -2px !important;
  border: 0;
  box-shadow: 0px 2px 20px 0px #0000000D;
}
@media only screen and (max-width: 599px) and (min-width: 400px) {
  .col-one {
    width: 296px !important;
    min-width: 98px !important;
  }
  .sign-hr-line-insp {
    width: 79% !important;
    bottom: 63% !important;
  }
}
@media only screen and (max-width: 577px) and (min-width: 500px) {
  .col-one {
    width: 296px !important;
    min-width: 132px !important;
  }
  .sign-hr-line-insp {
    width: 79% !important;
    bottom: 63% !important;
  }
}
@media only screen and (max-width: 599px) and (min-width: 576px) {
  .col-one {
    width: 318px !important;
    min-width: 160px !important;
  }
  .sign-hr-line-insp {
    width: 79% !important;
    bottom: 63% !important;
  }
}
@media only screen and (max-width: 699px) and (min-width: 600px) {
  .col-one {
      width: 384px !important;
      min-width: 169px !important;
  }
  .sign-hr-line-insp {
    width: 47% !important;
    bottom: 63% !important;
  }
}
@media only screen and (max-width: 799px) and (min-width: 700px) {
  .col-one {
      width: 441px !important;
      min-width: 203px !important;
  }
  .sign-hr-line-insp {
    width: 53% !important;
    bottom: 64% !important;
  }
}
@media only screen and (max-width: 899px) and (min-width: 800px) {
  .col-one {
      width: 441px !important;
      min-width: 238px !important;
  }
  .sign-hr-line-insp {
    width: 62% !important;
    bottom: 64% !important;
  }
}
@media only screen and (max-width: 985px) and (min-width: 900px) {
  .col-one {
      width: 441px !important;
      min-width: 274px !important;
  }
  .sign-hr-line-insp {
    width: 67% !important;
    bottom: 63% !important;
  }
}
@media only screen and (max-width: 1070px) and (min-width: 986px) {
  .col-one {
      width: 441px !important;
      min-width: 300px !important;
  }
  .sign-hr-line-insp {
    width: 72% !important;
    bottom: 63% !important;
  }
}
@media only screen and (max-width: 1099px) and (min-width: 1069px) {
  .col-one {
      width: 441px !important;
      min-width: 314px !important;
  }
  .sign-hr-line-insp {
    width: 69% !important;
    bottom: 63% !important;
  }
}
@media only screen and (max-width: 1144px) and (min-width: 1100px) {
  .col-one {
      width: 441px !important;
      min-width: 322px !important;
  }
  .sign-hr-line-insp {
    width: 73% !important;
    bottom: 63% !important;
  }
}
@media only screen and (max-width: 1199px) and (min-width: 1145px) {
  .col-one {
      width: 441px !important;
      min-width: 345px !important;
  }
  .sign-hr-line-insp {
    width: 74% !important;
    bottom: 63% !important;
  }
}
@media only screen and (max-width: 1250px) and (min-width: 1200px) {
  .col-one {
      width: 441px !important;
      min-width: 360px !important;
  }
  .sign-hr-line-insp {
    width: 75% !important;
    bottom: 63% !important;
  }
}
@media only screen and (max-width: 1279px) and (min-width: 1249px) {
  .col-one {
      width: 441px !important;
      min-width: 370px !important;
  }
  .sign-hr-line-insp {
    width: 75% !important;
    bottom: 62% !important;
  }
}
@media only screen and (max-width: 1299px) and (min-width: 1280px) {
  .col-one {
      width: 441px !important;
      min-width: 380px !important;
  }
  .sign-hr-line-insp {
    width: 75% !important;
    bottom: 63% !important;
  }
}
@media only screen and (max-width: 1338px) and (min-width: 1300px) {
  .col-one {
      width: 441px !important;
      min-width: 394px !important;
  }
  .sign-hr-line-insp {
    width: 78% !important;
    bottom: 58% !important;
  }
}
@media only screen and (max-width: 1399px) and (min-width: 1339px) {
  .col-one {
      width: 441px !important;
      min-width: 410px !important;
  }
  .sign-hr-line-insp {
    width: 79% !important;
    bottom: 58% !important;
  }
}
@media only screen and (max-width: 1466px) and (min-width: 1400px) {
  .col-one {
      width: 474px !important;
      min-width: 420px !important;
  }
  .sign-hr-line-insp {
    width: 79% !important;
    bottom: 56% !important;
  }
}
@media only screen and (max-width: 1499px) and (min-width: 1465px) {
  .col-one {
      width: 474px !important;
      min-width: 441px !important;
  }
  .sign-hr-line-insp {
    width: 80% !important;
    bottom: 52% !important;
  }
}
@media only screen and (max-width: 1566px) and (min-width: 1500px) {
  .col-one {
      width: 510px !important;
      min-width: 460px !important;
  }
  .sign-hr-line-insp {
    width: 80% !important;
    bottom: 51% !important;
  }
}
@media only screen and (max-width: 1599px) and (min-width: 1565px) {
  .col-one {
      width: 510px !important;
      min-width: 475px !important;
  }
  .sign-hr-line-insp {
    width: 81% !important;
  }
}
@media only screen and (max-width: 1632px) and (min-width: 1600px) {
  .col-one {
      width: 522px !important;
      min-width: 494px !important;
  }
  .sign-hr-line-insp {
    width: 83% !important;
  }
}
@media only screen and (max-width: 1669px) and (min-width: 1632px) {
  .col-one {
      width: 543px !important;
      min-width: 494px !important;
  }
  .sign-hr-line-insp {
    width: 83% !important;
  }
}
@media only screen and (max-width: 1920px) and (min-width: 1669px) {
  .col-one {
      width: 651px !important;
      min-width: 523px !important;
  }
}
@media only screen and (max-width: 2024px) and (min-width: 1920px) {
  .col-one {
      width: 651px !important;
      min-width: 584px !important;
  }
}
@media only screen and (max-width: 2560px) and (min-width: 2025px) {
  .col-one {
      width: 850px !important;
      min-width: 650px !important;
  }
  .sign-hr-line-insp {
    width: 83% !important;
    margin-left: 48px !important;
    bottom: 46% !important;
  }
}
.mapboxgl-canvas-container.mapboxgl-touch-zoom-rotate.mapboxgl-touch-drag-pan, .mapboxgl-canvas-container.mapboxgl-touch-zoom-rotate.mapboxgl-touch-drag-pan .mapboxgl-canvas {
  -ms-touch-action: none;
  touch-action: none;
  height: 100% !important;
}

.mapboxgl-canvas {
  left: 0;
  position: absolute;
  top: 0;
  height: 100% !important;
}
.top-right{
  position: absolute !important;
  top: 0 !important;
  right: 0 !important;
}

.text-underline { text-decoration: underline };
.confirmation-action-btns{
  .btn {
    font-size: 14px !important;
  }
}
.do-not-ask-checkbox {
  .custom-control-label {
    &::before,
    &::after {
      top: 0.9px !important;
    }
  }
}
#heat-map .mapboxgl-ctrl-bottom-right .mapboxgl-ctrl-attrib.mapboxgl-compact{
    display: none !important;
}
#heat-map a.mapboxgl-ctrl-logo{
    display: none !important;
}

.br-light-silver {
    border-right: 1px solid var(--light-silver);
}
.sign-cancel:hover {
	background: none;
	color: #0066FF !important;
}
.daily-act-powra-sing .signature-pad {
  canvas {
    width: 100% !important;
    max-height: 150px !important
  }
}

.bl-light-silver {
    border-left: 1px solid var(--light-silver);
}

.pdf-viewer {
  ng2-pdfjs-viewer{
      iframe {
          border: none;
      }
  }
}
.ql-snow {
    .ql-editor li{
        list-style: inherit !important;
    }
    .ql-picker.ql-expanded .ql-picker-options{
        z-index: 3 !important;
    }
}
.custom-switch {
    &.custom-switch-md {
        .custom-control-label {
            padding-left: 2rem;
            padding-bottom: 1.5rem;
            &::before {
                height: 1.5rem;
                width: calc(2rem + 0.75rem);
                border-radius: 3rem;
            }
            &::after {
                width: calc(1.5rem - 4px);
                height: calc(1.5rem - 4px);
                border-radius: calc(2rem - (1.5rem / 2));
            }
        }
        .custom-control-input {
            &:checked {
                & ~ .custom-control-label {
                    &::after {
                        transform: translateX(calc(1.5rem - 0.25rem));
                    }
                }
            }
        }
    }
}

.text-brandeis-blue {
  color: var(--primary) !important;
}

.text-brand-blue {
  color: var(--brandeis-blue) !important;
}

.right-secondary-btn {
  &:focus {
      box-shadow: none !important;
  }
}

.assessment-body-height {
  height: calc(100vh - 218px);
}

/* Start: style for checkbox toggle button */
.checkbox-switch-v2 {
    position: relative;
    display: inline-block;
    width: 45px;
    height: 15px;
    input {
        opacity: 0;
        width: 0;
        height: 0;
    }
}
.slider-v2 {
    position: absolute;
    cursor: pointer;
    top: 6px;
    left: 0;
    right: 0;
    bottom: -6px;
    background-color: var(--light-silver);
    -webkit-transition: 0.4s;
    transition: 0.4s;
    &:before {
        position: absolute;
        content: "";
        height: 22.5px;
        width: 23px;
        left: 0px;
        top: -4px;
        bottom: 5px;
        background-color: var(--white);
        border: 0.5px solid var(--light-silver);
        -webkit-transition: 0.4s;
        transition: 0.4s;
        box-shadow: -0.5px 0.5px 2px 0px #0000001a;
    }
    &.round {
        border-radius: 34px;
        &:before {
            border-radius: 50%;
        }
    }
}
input {
    &:checked {
        & + .slider-v2 {
            background-color: var(--success);
            &:before {
                -webkit-transform: translateX(24px);
                -ms-transform: translateX(24px);
                transform: translateX(24px);
            }
        }
    }
    &:focus {
        & + .slider-v2 {
            box-shadow: 0 0 1px var(--light-silver);
        }
    }
}
/* End: style for checkbox toggle button */

.permitModal, .powerBiDashboard {
    .modal-lg {
        width: 45%;
    }
    .modal-xl {
        width: 90%;
    }
    .modal-dialog {
        max-width: 1800px !important;
        transition: width 1s ease !important;
        max-height: 92vh !important;
    }
}
.after-none{
  &::after{
    display: none;
  }
}
.ng-dropdown-panel.dropdown-list {
  width: auto !important;
}
.spanish-gray-font { color: var(--spanish-gray); }

.small-font {font-size: 12px;}
.medium-font {font-size: 14px;}
.large-font {font-size: 16px !important;}
.x-large-font {font-size: 18px;}
.xx-large-font {font-size: 20px;}
.xxx-large-font {font-size: 24px;}
.icon-help{
    font-family: "Material Symbols Outlined";
    &::after{
        content: 'info';
        cursor: help;
    }
}
[ngbtooltip]:not([ng-reflect-disable-tooltip]),
[ng-reflect-ngb-tooltip]:not([ng-reflect-disable-tooltip]) {
  cursor: help;
}
.text-break { white-space: pre; text-wrap: wrap; }

.text-underline { text-decoration: underline };
#heat-map .mapboxgl-ctrl-bottom-right .mapboxgl-ctrl-attrib.mapboxgl-compact{
    display: none !important;
}
#heat-map a.mapboxgl-ctrl-logo{
    display: none !important;
}
.visibility-hidden { visibility: hidden; }

.modal-scroll {
    max-height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
}

.empty-row { text-align: center; }
.wrapper-width {
  width: calc(100% - 250px) !important;
}
.icon-help{
    font-family: "Material Symbols Outlined";
    &::after{
        content: 'info';
        cursor: help;
    }
}
.disable-btn {
  opacity: 0.6 !important;
}
.cursor-disable {
  cursor: not-allowed !important;
}

.powerBiDashboard {
    iframe {
        border: none;
    }
}
.text-search {
    background: $select;
    border: none;
    border-bottom: none;
    margin: 0px 0px;
    border-radius: 5px;
    max-width: 230px;
    width: 230px;
}
.text-search input.ngx-search[type="search"] {
    background: $select;
    border: none;
    border-bottom: none;
    margin-bottom: 0px !important;
    border-radius: inherit;
    height: 30px;
}
//search input along with list of items.
.list-search {
  margin: 6px;
  width: calc(100% - 12px);
  padding-left: 6px;
  max-width: 100%;
  input{
    height: 36px !important;
  }
}


// updated file-uploader selected files UI
.file-ref{
  border: 0.5px solid #d9d9d9;
  border-radius: 3px;
  padding: 12px 9px 12px 9px;
}
.c-config .ml-2{
  margin-left: 0 !important;
}
.c-config .custom-control .custom-control-label{
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}
.disable-tool{
  opacity: 0.6;
  pointer-events: none;
}

.long-text {
  word-break: break-word;
}


.form-control:disabled, .form-control[readonly] {
  background-color: var(--semi-light-gray);
}

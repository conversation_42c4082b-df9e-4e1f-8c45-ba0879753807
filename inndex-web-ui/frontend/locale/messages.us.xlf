<?xml version="1.0" encoding="UTF-8" ?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
    <file source-language="gb" datatype="plaintext" original="ng2.template" target-language="us">
        <body>
            <trans-unit id="introductionHeader" datatype="html">
                <source>Hello</source>
                <target>Hello-US</target>
            </trans-unit>
            <trans-unit id="operative" datatype="html">
                <source>Operative</source>
                <target>Worker</target>
            </trans-unit>
            <trans-unit id="operativeName" datatype="html">
                <source>Operative Name</source>
                <target>Worker Name</target>
            </trans-unit>
            <trans-unit id="operatives" datatype="html">
                <source>Operatives</source>
                <target>Workers</target>
            </trans-unit>
            <trans-unit id="2e73b806c098e36591552b48247936c0e1e15f11" datatype="html">
                <source>Select Operative</source>
                <target>Select Worker</target>
            </trans-unit>
            <trans-unit id="19df72596dbac50d7abb87c2c95709a51395294a" datatype="html">
                <source>Delete Operative</source>
                <target>Delete Worker</target>
            </trans-unit>
            <trans-unit id="9a9e38cb3eb2efa678399d110ed8c5ea5f4c317f" datatype="html">
                <source>Random Operative List</source>
                <target>Random Worker List</target>
            </trans-unit>
            <trans-unit id="temperatureUnit" datatype="html">
                <source>temperature_unit</source>
                <target>F</target>
            </trans-unit>
            <trans-unit id="celsius" datatype="html">
                <source>C</source>
                <target>F</target>
            </trans-unit>
            <trans-unit id="windSpeedUnit" datatype="html">
                <source>wind_speed_unit</source>
                <target>mile/h</target>
            </trans-unit>
            <trans-unit id="kmh" datatype="html">
                <source>km/h</source>
                <target>mile/h</target>
            </trans-unit>
            <trans-unit id="kms" datatype="html">
                <source>kms</source>
                <target>miles</target>
            </trans-unit>
            <trans-unit id="km" datatype="html">
                <source>km</source>
                <target>mile</target>
            </trans-unit>
            <trans-unit id="vrn" datatype="html">
                <source>Vehicle Registration Number</source>
                <target>License Plate Number</target>
            </trans-unit>
            <trans-unit id="eyvrn" datatype="html">
                <source>Enter Your Vehicle Registration Number</source>
                <target>Enter Your License Plate Number</target>
            </trans-unit>
            <trans-unit id="pc" datatype="html">
                <source>Post Code</source>
                <target>Zip Code</target>
            </trans-unit>
            <trans-unit id="poc" datatype="html">
                <source>Postcode</source>
                <target>Zip Code</target>
            </trans-unit>
            <trans-unit id="epc" datatype="html">
                <source>Enter Post Code</source>
                <target>Enter Zip Code</target>
            </trans-unit>
            <trans-unit id="tfpc" datatype="html">
                <source>Travelling from (Post Code)</source>
                <target>Travelling from (Zip Code)</target>
            </trans-unit>
            <trans-unit id="ttpc" datatype="html">
                <source>Traveling to (Post Code)</source>
                <target>Traveling to (Zip Code)</target>
            </trans-unit>
            <trans-unit id="dpc" datatype="html">
                <source>Dispatch Post Code</source>
                <target>Dispatch Zip Code</target>
            </trans-unit>
            <trans-unit id="rpc" datatype="html">
                <source>Return Post Code</source>
                <target>Return Zip Code</target>
            </trans-unit>
            <trans-unit id="satfpc" datatype="html">
                <source>Same as 'Travelling from' Post Code</source>
                <target>Same as 'Travelling from' Zip Code</target>
            </trans-unit>
            <trans-unit id="rsn" datatype="html">
                <source>Reg./Serial Number</source>
                <target>License Plate Serial Number</target>
            </trans-unit>
            <trans-unit id="nin" datatype="html">
                <source>National Insurance Number</source>
                <target>Social Security Number</target>
            </trans-unit>
            <trans-unit id="ciq" datatype="html">
                <source>company induction quiz</source>
                <target>company orientation quiz</target>
            </trans-unit>
            <trans-unit id="Uciq" datatype="html">
                <source>Company Induction Quiz</source>
                <target>Company Orientation Quiz</target>
            </trans-unit>
            <trans-unit id="aiq" datatype="html">
                <source>Additional Induction Questions</source>
                <target>Additional Orientation Questions</target>
            </trans-unit>
        </body>
    </file>
</xliff>
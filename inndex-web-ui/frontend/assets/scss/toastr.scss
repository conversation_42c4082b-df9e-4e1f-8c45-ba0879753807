@use "variables";

$success: variables.$success-green-color;
$info: variables.$info-blue-color;
$warning: variables.$warning-amber-color;
$error: variables.$error-red-color;
$white: var(--white);

.toast {
    border: none !important;
}

.toast-w-medium {
    width: 325px !important;
}

.toast-w-large {
    width: 400px !important;
    max-width: 400px !important;
}

.toast-position {
    right: 0 !important;
    z-index: 9999;
}
.toast-header {
    line-height: 1;
}

.border-solid-left {
    border-left: 4px solid transparent;
}

.custom-alert {
    position: relative;
    background-color: $white;
    padding: 15px;

    .toast-img {
        margin-right: 10px;
    }

    .close {
        position: absolute;
        right: 10px;
        top: 10px;
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: variables.$stroke-primary-color;
    }
}
.refresh-button-container{
    display: none;
}
.custom-toast{
    cursor: default;
    .hide-block{
        display: none;
    }
    .refresh-button-container{
        display: flex;
    }
}


.toast-body {
    padding: 0 !important;
    border-radius: 4px !important;
    box-shadow: 0px 4px 8px 0px #00000014 !important;
}

// progress bar
.progress-bar-container {
    height: 4px;
    background: var(--white);
    position: relative;
    bottom: 0;
    border-bottom-right-radius: 3px !important;
}

.progress-bar {
    background: variables.$tertiary-brand-color;
    transition: width 0.1s linear;
}

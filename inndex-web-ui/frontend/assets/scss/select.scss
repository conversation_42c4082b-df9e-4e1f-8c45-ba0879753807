@use "./variables";

$primary: variables.$primary-brand-color;
$select: #f2f2f2;

.select-sm {
    @extend .small;

    .ng-select-container {
        height: 32px !important;
        min-height: 32px !important;
    }
}
.select-with-button {
    .ng-select-container {
        height: 38px !important;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
}
.v-scroll {
    .ng-dropdown-panel .scroll-host {
        min-height: 40px;
    }
}
.events-selector {
    .ng-value .float-right {
        float: none !important;
        margin-left: 10px;
    }
    .ng-select-container.ng-has-value {
        height: 50px !important;
    }
}

.custom-day {
    text-align: center;
    padding: 0.185rem 0.25rem;
    display: inline-block;
    height: 2rem;
    width: 2rem;
    transition: all 0.15s ease-in-out;
}
.custom-day.focused {
    background-color: #e6e6e6;
}
.custom-day.range,
.custom-day:hover {
    background-color: #14152d;
    color: white;
}
.custom-day.faded {
    background-color: rgba(0, 95, 197, 0.57);
}
.custom-day-v2 {
    text-align: center;
    padding: 0.185rem 0.25rem;
    display: inline-block;
    height: 2rem;
    width: 2rem;
    transition: all 0.15s ease-in-out;
}
.custom-day-v2.focused {
    background-color: var(--semi-light-silver);
}
.custom-day-v2.range,
.custom-day-v2:hover {
    background-color: var(--fuel-yellow);
    color: var(--black);
}
.custom-day-v2.faded {
    background-color: var(--fuel-yellow-light);
}
.custom-weekday-v2 {
    width: 100%;
    height: auto;
    .ngb-dp-weekdays {
        margin-bottom: 12px;
    }
    .ngb-dp-weekday {
        color: var(--brandeis-blue) !important;
        width: 100%;
    }
    .ngb-dp-month {
        width: 100%;
    }
    .ngb-dp-day {
        width: 100%;
        height: 2.8rem !important;
        text-align: center;
    }
    .ngb-dp-header {
        height: 55px;
    }
    .ngb-dp-navigation-select {
        height: 50px;
        align-items: center;
    }
    .custom-select {
        margin-right: 0.25rem !important;
        height: 40px;
    }
}
@media (max-width:356px) {
    .modal-padding {
        padding-left: 2rem !important;
        padding-right: 2rem !important;
    }
}
@media (max-width: 991px) {
    .custom-weekday-v2 {
        height: auto;
        .ngb-dp-months {
            display: block;
        }
    }
}
ngb-datepicker .disabled {
    opacity: 0.5;
}
ngb-datepicker {
    border: none;
}
.ngb-dp-weekday {
    font-style: normal !important;
}
.ngb-dp-months {
    padding: 0 !important;
}

.border-left-radius-4px-both {
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px;
}
.border-left-radius-0px-both {
    border-bottom-left-radius: 0px !important;
    border-top-left-radius: 0px !important;
}
ng-select {
    &.ng-touched.ng-invalid {
        &:not(form) {
            @extend .border-left-radius-4px-both;
        }
        > .ng-select-container {
            @extend .border-left-radius-0px-both;
            border-left: none;
        }
    }
    &.ng-touched.ng-valid[required] {
        @extend .border-left-radius-4px-both;
        .ng-select-container.ng-has-value {
            border-left: none;
            @extend .border-left-radius-0px-both;
        }
    }
    &.ng-touched.ng-valid.required {
        @extend .border-left-radius-4px-both;
        .ng-select-container.ng-has-value {
            border-left: none;
            @extend .border-left-radius-0px-both;
        }
    }
}

/* bg-grey border less ng-select dropdown */
.custom-drop {
    .ng-select {
        .ng-select-container {
            background-color: #f2f2f2 !important;
            border: none;
        }
    }
}

// company-messaging operatives dropdown
.operative-dropdown.ng-select {
    .ng-dropdown-panel-items {
        .ng-option{
            &.ng-option-selected{
                font-weight: 400 !important;
                .disabled{
                    color: var(--spanish-gray);
                }
            }  
            &.ng-option-marked{
                background-color: var(--white);
            } 
        }
    }
}

/* Incident Reports Causes Subcategory option text*/
.ng-select.custom_select
    .ng-dropdown-panel
    .ng-dropdown-panel-items
    .ng-option {
    white-space: normal !important;
    display: flex;
    cursor: pointer;
    align-items: center;
    margin: 5px 0px;
}
.ng-select.custom_select .ng-select-container .ng-value-container,
.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
    white-space: normal !important;
}

.ng-select.custom_select .ng-select-container {
    height: auto !important;
}

.ng-select {
    padding: 0px;
    .ng-arrow-wrapper{
        display: flex;
        justify-content: center;
        align-items: center;
        width: 20px !important;
        height: 30px;
        margin-right: 5px;
    }
    .ng-arrow {
        top:0px;
        display: flex !important;
        justify-content: center;
        align-items: center;
        font-family: "Material Symbols Outlined";
        border-color: inherit !important;
        border-width: 0 !important;
        color: $primary;
        text-align: center;
        transform: rotate(0deg);
        display: block;
        font-size: 20px;
        transform-origin: 0px;
        transition: transform 0.2s ease;
        &:after {
            content: "\e5cf";
            position: relative;
            vertical-align: middle;
        }
    }
    &.ng-select-opened {
        .ng-arrow {
            top:0px !important;
            transform: rotate(180deg);

        }
    }
    &.ng-select-single {
        .ng-select-container {
          .ng-value-container {
            .ng-value {
              z-index: 9;
            }
          }
        }
    }
}
.dropdown-list{
    .ng-placeholder {
        color: #6c757d !important;
    }
}
.ng-dropdown-panel {
    &.dropdown-list,
    &.v-scroll-dropdown-list {
        width: auto !important;
        max-width: min-content !important;

        .ng-option {
            word-break: break-word;
        }
    }

    &.dropdown-list .ng-option {
        white-space: normal !important;
    }
}

.show-full-txt-option {
    .ng-dropdown-panel {
        .ng-dropdown-panel-items {
            .ng-option {
                white-space: normal;
                overflow: auto;
                text-overflow: unset;
            }
        }
    }
}

.filter-v2-select.ng-select.ng-select-opened .ng-select-container {
    border: none;
    color: $primary !important;
    background: $select;
}

.filter-v2-select.ng-select.multiselect-dropdown .ng-select-container {
    &.ng-has-value .ng-placeholder {
        display: block;
    }
    .ng-value-container {
        .ng-value {
            display: none;
        }
    }
}

.filter-v2-select.ng-select {
    margin-bottom: 0px !important;
    // min-width: 125px;
    display: flex;
    width: auto;
    height: 30px;
    align-items: center !important;
    .ng-select-container {
        height: 30px;
        border-radius: 5px !important;
        display: flex;
        align-items: center;
        gap: 10px;
        border: 1px solid #9d9d9d;
        min-height: 30px !important;
        .ng-value-container {
            align-items: center;
            height: 100%;
            line-height: 18px;
            .ng-placeholder {
                color: $primary !important;
                position: unset !important;
            }
        }
    }
}

.filter-v2-select.ng-select.ng-select-opened .ng-select-container {
    border: 1px solid $select;
    color: $primary !important;
    background: $select;
}
.filter-v2-select.ng-select .ng-arrow::after{
    top: 1px !important;
}

.filter-v2-select.ng-select.multiselect-dropdown .ng-select-container {
    &.ng-has-value .ng-placeholder {
        display: block;
    }
    .ng-value-container {
        .ng-value {
            display: none;
        }
    }
}

.filter-v2-select.filter-select .ng-dropdown-panel {
    left: 0%;
    border-radius: 5px !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    min-width: max-content !important;
}

.ng-select.status .ng-dropdown-panel {
    min-width: 250px !important;
    .ng-dropdown-panel-items {
        max-height: 350px !important;
    }
}

.filter-v2-select.custom-border.ng-select {
    .ng-select-container {
        border-radius: 4px;
        border: 1px solid var(--chinese-silver) !important;
    }
    .ng-value-container {
        .ng-placeholder {
            color: #6c757d !important;
        }
    }
}

.filter-select .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
    cursor: pointer;
    margin: 5px 0px;
    display: flex;
    text-align: left;
    text-wrap: wrap !important;
    align-items: center;
}

select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    text-indent: 1px;
    text-overflow: "";
}
select::after {
    content: "\e5cf"; /* Unicode for the arrow icon from Material Design Icons */
    font-family: "Material Symbols Outlined";
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
}

/* For IE10 */
select::-ms-expand {
    display: none;
}

.ng-dropdown-panel .ng-dropdown-header {
    padding: 5px 5px !important;
    border: none !important;
}

.filter-select .ng-dropdown-panel .scroll-host {
    margin: 2px 5px;
}

.filter-select .ng-dropdown-panel.ng-select-bottom {
    top: 112%;

    border-radius: 5px;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected,
.ng-dropdown-panel
    .ng-dropdown-panel-items
    .ng-option.ng-option-selected.ng-option-marked {
    color: #333;
    background-color: white !important;
    font-weight: 600;
}

.ng-select.filter-v2-select .ng-clear-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: auto ;
    color:$primary;
}

.filter-v2-select .ng-dropdown-panel {
    z-index: 100 !important;
}

.sm-select.ng-select .ng-select-container{
    min-height: 40px !important;
    height: auto !important;
}
.large-select .ng-select-container{
    min-height: 48px !important;
    height: auto !important;
    .ng-input input{
        height: 36px;
        line-height: 36px;
    }
    .ng-arrow-wrapper{
        width: 24px !important;
        padding-right: 10px;
        .ng-arrow{
            font-size: 24px;
        }
    }
}
.sm-select.ng-select.ng-select-opened .ng-select-container{
    background:white;
}
.site-messaging-datepicker .dropdown-menu{
    left:40px !important;
}
.sm-select.ng-select {
    .ng-dropdown-panel{
        box-shadow: -5px -5px 20px 0px #0000000D, 5px 5px 20px 0px #0000000D !important;
    }
}

/*.multiline-select {
    .ng-value-container {
        display: block !important;

        .ng-value {
            display: inline-block;
        }
    }
}*/

.sm-select.ng-select.ng-select-disabled{
    .ng-arrow-wrapper{
        display: none;
    }

    .ng-select-container{
        background-color: var(--white);
        border: 1px solid var(--soft-slate);
        &.ng-has-value {
            .ng-value-container {
                .ng-value {
                    .ng-value-label {
                        padding: 2px 5px;
                    }
                }
            }
        }
    }
    .ng-value-container .ng-value .ng-value-label{
        color:var(--spanish-gray);
    }
}
.it-dropdown.ng-select.filter-v2-select {
    .ng-select-container {
        min-height: 30px;
        height: auto;
        width: 200px;
    }
    .ng-dropdown-panel{
        min-width: 350px !important;
        .ng-dropdown-panel-items{
            .ng-option{
                span{
                    overflow: hidden !important;
                    text-wrap: nowrap !important;
                    text-overflow: ellipsis !important;
                }
            }
        }
    }
}
@media only screen and (max-width: 768px) {
    .filter-v2-select, .filter-v2-select .ng-select-container{
        height: 50px !important;
    }
}

.custom-placeholder.ng-select {
        .ng-select-container {
            .ng-value-container {
                .ng-placeholder::after {
                    content: "*"; 
                    color: variables.$error-red-color;
                }
                .ng-placeholder{
                    color: $primary !important;
                }
            }
        }
}
.custom-dropdown .dropdown.show{
    .dropdown-menu{
        width: 100%;
    }
}    
.custom-dropdown .dropdown .dropdown-menu .dropdown-container .treeview-container .treeview-item .form-inline.row-item {
    margin-bottom: 0.6rem;
    .form-check{
        .form-check-label{
            color:#000000;  
            font-weight: 400;
        }
    }
}

.asset-error-block{
    min-height: 120px;
}

.ng-select .ng-select-container .ng-value-container .ng-input>input[readonly]{
    user-select: unset !important;
}

.select-participants {
    .ng-select.ng-select-multiple {
      .ng-select-container {
        .ng-value-container {
          padding-top: 9.5px;
          padding-left: 10px;
          padding-bottom: 4.5px;
  
          .ng-value {
            background-color: #e1efff !important;
            border-radius: 2px !important;
            border: none !important;
            position: relative;
            padding: 0 !important;
            align-items: center;
            height: 30px;
  
            .ng-value-icon.left {
              border-right: 1px solid #67a4ff !important;
              border-radius: 0 !important;
              height: 100%;
              position: absolute !important;
              left: 2px !important;
              padding: 4px 11px 9px 9.5px !important;
  
              &:hover {
                background-color: #e1efff !important;
              }
            }
  
            .ng-value-label {
              padding-left: 38px;
              padding-right: 10px;
            }
          }
  
          .ng-placeholder {
            top: 10px !important;
          }
        }
      }
    }
}

.modal_v2 .accordion .card {
    border: 0.5px solid #9D9D9D;
    overflow: inherit;
}

.modal_v2 .accordion .card-header {
    padding: 0 8px;
    background-color: unset;
    border-bottom: none;
}

.modal_v2 .accordion .card-body {
    padding: 8px;
    border-top: 0.5px solid #9D9D9D;
}

.accordion.actionAccordion {
    background-color: var(--danger);
    > .card {
        border-left: 0 !important;
        border-right: 0 !important;
        border-bottom: 0 !important;

        > .card-header {
            border-left: 1px solid rgba(0, 0, 0, 0.125);
            border-right: 1px solid rgba(0, 0, 0, 0.125);
            border-bottom: 2px solid rgba(0, 0, 0, 0.125);
        }
    }
}

.accord-table-pad {
    .card-body {
        padding: 0px !important;
    }
}

.toggle-admin-access-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
}

.custom-accord-border {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125) !important;
    border-radius: 4px !important;
}

#access-admin-accordion {
    > .card {
      > .card-header {
        background-color: var(--white) !important;
      }
    }
}

.i-accordion {
    .card {
        overflow: hidden !important;
        border: 1px solid rgba(0, 0, 0, 0.125) !important;
    }
    .card-header {
        padding: 10px !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125) !important;
        background-color: rgba(0, 0, 0, .03) !important;
    }
    .card-body {
        border-top: none !important;
        padding: 0 !important;
    }

    .card-header button {
        box-shadow: none;
        font-size: 14px;
        display: flex;
        align-items: center;
        width: 100%;
        text-align: left;
        border: 0;
        border-radius: 0;
        overflow-anchor: none;

        .fas {
            margin-right: 2px !important;
        }
    }

    table.border-0 {
        &:not(.medium-font) {
            font-size: 12px;
        }

        // safely managing double border issue
        margin: 0 -1px 0 -1px;
        width: calc(100% + 2px);

        th {
            font-weight: 600;
        }
    }
    &.compact-list{
        .card-header button {
            font-size: 18px;
        }
        .card-body {
            font-size: 18px;
            .justify-content-between{
                padding: 10px !important;
            }
        }
    }
}

@use "assets/scss/variables";

ngx-datatable {
  .datatable-body-row.datatable-row-even {
    background-color: var(--cultured);
  }

  &.table-v2 {
    .datatable-body .datatable-body-row {
      border-top: none !important;
    }

    .datatable-body-cell-label {
      line-height: 30px;
    }

    .btn-action {
      background-color: var(--anti-flash-white) !important;
      box-shadow: none;
    }

    datatable-body .datatable-body-row.datatable-row-even .btn-action {
      background-color: var(--bright-gray) !important;
    }
      .disabled-row{
          @extend .text-muted;
      }

  }
    .progress-linear {
        display: block;
        position: sticky;
        width: 100%;
        height: 5px;
        padding: 0;
        margin: 0;
        top: 0;

        .container {
            display: block;
            position: relative;
            overflow: hidden;
            width: 100% !important;
            max-width: 100% !important;
            margin-bottom: 0;
            padding: 0;
            height: 5px;
            -webkit-transform: translate(0, 0) scale(1, 1);
            transform: translate(0, 0) scale(1, 1);
            background-color: rgb(170, 209, 249);

            .bar {
                transition: all 0.2s linear;
                -webkit-animation: query 0.8s infinite cubic-bezier(0.39, 0.575, 0.565, 1);
                animation: query 0.8s infinite cubic-bezier(0.39, 0.575, 0.565, 1);

                transition: -webkit-transform 0.2s linear;
                transition: transform 0.2s linear;
                background-color: rgb(16, 108, 200);

                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 100%;
                height: 5px;
            }
        }
    }
}
.ngx-datatable.bootstrap {
    font-size: 12px;
}
@keyframes query {
    0% {
        opacity: 0;
        transform: translateX(-50%) scale(0, 1);
    }

    100% {
        opacity: 1;
        transform: translateX(35%) scale(0.3, 1);
    }
}
.table-v3 .datatable-body-cell-label{
    line-height: 42px !important;
}
.table-v4 .datatable-body-cell-label{
    line-height: 22px !important;
}

/* Table using tr, td new UI */
.tableV2 {
  table {
    border: 1px solid var(--light-silver);
    border-collapse: separate;
    border-left: 0;
    border-radius: 4px;
    border-spacing: 0px;
  }

  thead {
    display: table-header-group;
    vertical-align: middle;
    border-color: inherit;
    border-collapse: separate;

    tr th {
      background: var(--anti-flash-white);
    }
  }

  tr {
    display: table-row;
    vertical-align: inherit;
    border-color: inherit;
  }

  th,
  td {
    padding: 6px 10px 6px 10px;
    text-align: left;
    vertical-align: middle;
    height: 40px;
    border-left: 1px solid var(--light-silver);
  }

  td {
    border-top: 1px solid var(--light-silver);
  }

  thead:first-child tr:first-child th:first-child,
  tbody:first-child tr:first-child td:first-child {
    border-radius: 5px 0 0 0;
  }

  thead:last-child tr:last-child th:first-child,
  tbody:last-child tr:last-child td:first-child {
    border-radius: 0 0 0 5px;
  }
}
/* ngx-datatable-custom */
.ngx-datatable-custom {
  .ngx-datatable.bootstrap {
    .datatable-body {
      .datatable-body-row.datatable-row-even {
        background-color: var(--cultured) !important;
      }

      .datatable-body-row.datatable-row-odd {
        background-color: var(--white) !important;
      }

      .datatable-body-row {
        border-top: none !important;
        .datatable-body-cell {
          display: flex;
          align-items: center;
        }
      }
    }

    .datatable-footer {
      border-radius: 5px !important;
    }

    .datatable-header {
      .datatable-header-cell {
        border-bottom: none !important;
      }
    }
  }
}
.h-auto {
    height: auto !important;
}


.datatable-icon-up:before {
  font-family: "Material Symbols Outlined" !important;
  content: "expand_less";
}
.datatable-icon-down:before {
  font-family: "Material Symbols Outlined" !important;
  content: "expand_more";
}
.datatable-icon-sort-unset::before {
  font-family: "Material Symbols Outlined" !important;
  content: "unfold_more";
}

.action-column:last-child{
  display: flex;
  flex-wrap: wrap;
  width: 300px !important;
}


/* Apply a border to the right of all but the last column */
/* Apply a border to the bottom of all but the last row */
table.rounded-corners {
    --border: 1px solid #dee2e6;
    border-radius: 5px;
    --child-br: 4px; //child should be 1px lower than above defined radius
    border-spacing: 0;
    border-collapse: separate;
    border: var(--border);
    //overflow: hidden;
    th {
        &:not(:last-child) {
            border-right: var(--border);
        }
    }
    td {
        &:not(:last-child) {
            border-right: var(--border);
        }
    }
    >thead {
        >tr {
            &:not(:last-child) {
                >th {
                    border-bottom: var(--border);
                }
                >td {
                    border-bottom: var(--border);
                }
            }
        }
        &:not(:last-child) {
            border-bottom: var(--border);
        }
    }
    >tbody {
        >tr {
            &:not(:last-child) {
                >th {
                    border-bottom: var(--border);
                }
                >td {
                    border-bottom: var(--border);
                }
            }
        }
        &:not(:last-child) {
            border-bottom: var(--border);
        }
    }
    >tfoot {
        >tr {
            &:not(:last-child) {
                >th {
                    border-bottom: var(--border);
                }
                >td {
                    border-bottom: var(--border);
                }
            }
        }
        &:not(:last-child) {
            border-bottom: var(--border);
        }
    }
    >tr {
        &:not(:last-child) {
            >td {
                border-bottom: var(--border);
            }
            >th {
                border-bottom: var(--border);
            }
        }
    }
    tr {
        &:first-child {
            th {
                &:first-child {
                    border-top-left-radius: var(--child-br);
                }
                &:last-child {
                    border-top-right-radius: var(--child-br);
                }
            }
            td {
                &:first-child {
                    border-top-left-radius: var(--child-br);
                }
                &:last-child {
                    border-top-right-radius: var(--child-br);
                }
            }
        }
        &:last-child {
            th {
                &:first-child {
                    border-bottom-left-radius: var(--child-br);
                }
                &:last-child {
                    border-bottom-right-radius: var(--child-br);
                }
            }
            td {
                &:first-child {
                    border-bottom-left-radius: var(--child-br);
                }
                &:last-child {
                    border-bottom-right-radius: var(--child-br);
                }
            }
        }
    }
}
.company-message_table .ngx-datatable.bootstrap .datatable-body .datatable-body-row .datatable-body-cell{
    padding: 0.5rem 0.75rem 1rem 0.75rem;
}
.company-itp{
  .datatable-body-cell-label{
    width: 90%;
    font-size: 14px;
  }
}
.cell-word-wrap {
  word-wrap: break-word;
  overflow: hidden;
}
.ngx-datatable-custom-h {
  .datatable-body {
    height: auto !important;
    max-height: calc(100vh - 400px) !important;
  }
}

.documents-table {
  .ngx-datatable-custom-h {
    .datatable-body {
      max-height: calc(100vh - 450px) !important;
    }
  }
  .datatable-footer {
    margin-top: 0 !important;
  }
}

.ngx-datatable-row-w-100 {
  .datatable-body-row {
    width: 100% !important;
  }
}

.min-h-250{
  min-height: 250px;
}

.roll-call-table-body {
  .datatable-body {
    max-height: calc(100vh - 320px) !important;
  }
}

:not(.no-ellipsis) > .datatable-body-cell-label,
:not(.no-ellipsis) > .datatable-body-cell-label span {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  width: 100%;
}

// table tooltip scrollbar customize
.table-tooltip-scrollable {
  scrollbar-width: thin;
  scrollbar-color: var(--lavender-gray) variables.$primary-brand-color;
}

.table-tooltip-scrollable::-webkit-scrollbar {
  width: 8px;
}

.table-tooltip-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.table-tooltip-scrollable::-webkit-scrollbar-thumb {
  background-color: var(--lavender-gray);
  border-radius: 6px;
}

.table-tooltip-scrollable::-webkit-scrollbar-thumb:hover {
  background-color: var(--philippine-gray);
}

@media only screen and (max-width: 752px) {
  .ngx-datatable.bootstrap {
    .datatable-footer {
      .page-count {
        line-height: 14px;
        height: 14px;
        padding: 0 2px;
        flex: 1 1 10%;
      }
    }
  }
}

.datatable-header-cell.min-w-fit-content, .datatable-body-cell.min-w-fit-content{
  min-width: 175px !important;
  width: fit-content;
}
@media only screen and (max-width: 430px) {
  .sm-pager-view {
    &.ngx-datatable.bootstrap .datatable-footer {
      .datatable-pager {
        margin: 0;

        ul {
          li {
            margin: 0;
          }
        }
      }
    }
  }
}






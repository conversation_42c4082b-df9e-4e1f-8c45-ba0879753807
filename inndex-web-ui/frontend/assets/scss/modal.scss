@use "./variables";
.close-icon {
  color: var(--secondary);
  font-weight: 400;
  font-size: 1.5rem;
  line-height: 1;

  span {
    background: var(--light-gray);
    padding: 0 8px;
    border-radius: 16px;
  }
  &.small{
    font-size: .8em;
    span{
      padding: 1px 5px;
      text-shadow: none !important;
    }
  }
}

.modal-head-v2 {
  background: var(--bg-greyish-white);

  .close {
    @extend .close-icon;
    text-shadow: none;
  }
}

.font-roboto {
  font-family: Roboto;
  color: variables.$primary-text-color;
}
.confirmation-modal{
    z-index: 1100;

    // add-induction flow > competency modal
    .failed-rules i{
        font-size: 10px;
    }
}

.modal_v2.verifying-modal {
  .modal-header {
    display: none;
  }
}
/* Modal V2 - created custom modal */
.modal_v2 {
    .modal-lg-125{
      width: 676px;
      max-width: none;
    }
    .modal-full-width{
        max-width: 98%;
    }
  .modal-content,
  .modal-header,
  .modal-footer {
    @extend .font-roboto;
    font-size: 16px;
  }
  .modal-content {
    border-radius: 10px;
    /* Font style */
    letter-spacing: 0em;
    text-align: left;
  }
  .modal-header {
    align-items: center;
    background-color: var(--cultured);
    height: auto;
    border-radius: 10px 10px 0 0;
    border-bottom: none;
  }
  .modal-footer {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
    padding: 10px;
    border-top: 1px solid var(--light-silver);
    min-height: 60px;
    /* font style */
    font-size: 14px;
    font-weight: 400;
  }
  hr {
    border: 0;
    border-top: 1px solid var(--light-silver);
  }
}

/* Modal info */
.modal_info {
    .modal-content,
    .modal-header,
    .modal-footer {
        @extend .font-roboto;
    }
    .modal-content {
        width: 300px;
        border-radius: 10px;
    }
    .modal-footer {
        padding: unset;
        border-top: 1px solid var(--bright-gray);
        height: 40px;
        /* font style */
        font-size: 14px;
        font-weight: 400;
    }
    .modal-dialog-centered {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.buildInspectionModal {
  .modal-content {
    border-radius: 10px;
  }
  .modal-header {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
}

.modal-dialog {
  max-height: calc(100vh - 4.25rem) !important;
  .modal-content {
    max-height: calc(100vh - 3.25rem) !important;
  }
  .modal-body {
    overflow-y: auto;
  }
  .mb-scroll-incident {
    max-height: calc(100vh - 20.25rem);
    overflow-y: auto;
  }
}

.dashboard-modal .modal-dialog.modal-xl{
  max-width: 1175px !important;
}

.modal-open {
  min-height: 100vh;
}

.confirmation-modal {
  .modal-sm {
    width: auto !important;
  }
}
@media (min-width: 375px) {
  .confirmation-modal {
    .modal-sm {
      max-width: 350px !important;
      margin-left: auto;
      margin-right: auto;
    }
  }
}

.permitModal {
  .modal-dialog {
    .modal-content {
      max-height: none !important;
    }
  }
}
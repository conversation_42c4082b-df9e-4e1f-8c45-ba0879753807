@use "./variables";

$transparent: transparent;


/* Primary Button */
.btn-brandeis-blue,
.btn-primary {
    color: variables.$primary-btn-color;
    background-color: variables.$primary-btn-bg;
    border-color: variables.$primary-btn-bg;

    &:focus,
    &.focus,
    &:hover {
        color: darken(variables.$primary-btn-color, 7.5%);
        @include gradient-bg(variables.$primary-btn-hover-bg);
        border-color: variables.$primary-btn-hover-bg;
    }
    &.disabled,
    &:disabled {
        color: variables.$primary-btn-color;
        background-color: variables.$primary-btn-bg;
        border-color: variables.$primary-btn-bg;
    }
    &:not(:disabled):not(.disabled):active,
    &:not(:disabled):not(.disabled).active,
    .show > &.dropdown-toggle {
        color: darken(variables.$primary-btn-color, 10%);
        @include gradient-bg(variables.$primary-btn-hover-bg);
        border-color: variables.$primary-btn-hover-bg;
    }
}

/* Secondary Button */

.btn-outline-brandeis-blue,
.btn-secondary {
    // tweaking button-outline-variant to prevent filling out of color on hover
    /*
    $color,
    $color-hover: color-yiq($color),
    $active-background: $color,
    $active-border: $color
    */
    @include button-outline-variant(
                    variables.$primary-brand-color,
                    variables.$primary-brand-color,
                    $transparent,
                    variables.$primary-brand-color
    );
    background-color: $transparent;
    &:active {
        color: variables.$primary-brand-color !important;
    }
    &:focus {
        color: variables.$primary-brand-color !important;
        border-color: variables.$primary-brand-color !important;
        background-color: $transparent;
    }
}


@mixin btn-link-hover() {
    color: variables.$primary-brand-color !important;
    //@include gradient-bg(darken(variables.$primary-bg-color, 7.5%));
    //border-color: darken(variables.$primary-bg-color, 7.5%);
}
/* Tertiary Button */

.btn-link {
    color: variables.$primary-brand-color !important;
    background-color: transparent;
    font-weight: 500;
    text-decoration: none !important;

    &:hover, &:active {
        @include btn-link-hover;
    }
}

.btn-im-send{
    gap: 6px;
    border-radius: 0.35rem !important;

    span{
        font-size: 18px;
    }
}

$white: var(--white);
$fuel_yellow: var(--fuel-yellow);
$lime_green: var(--success);
$light-silver: var(--light-silver);

.btn-grey{
    background: #EAEAEA;
}


.btn-pill {
    padding-right: 0.8em;
    padding-left: 0.8em;
    border-radius: 10rem;
}

.v-badge {
    @extend .border;
    @extend .rounded-lg;
    @extend .p-1;
    @extend .px-2;
    &:not(.m-0){
        @extend .ml-1;
    }

    font-size: 12px;
    display: inline-block;
    line-height: 16px;
    font-weight: 400;

    &.text-info {
        background-color: rgba(variables.$info-blue-color, 0.1);
        border-color: variables.$info-blue-color !important;
    }

    &.text-secondary {
        background-color: rgba(#9d9d9d, 20%);
    }

    .material-symbols-outlined {
        font-size: 16px;
        vertical-align: bottom;
    }
}

.btn-danger{
    &:not(:disabled):not(.disabled):hover,
    &:not(:disabled):not(.disabled):active
    {
        @include gradient-bg(variables.$error-red-color-hover);
        border-color: variables.$error-red-color-hover;
    }
}

.btn-fuel-yellow {
    color: $white;
    background-color: $fuel_yellow;
    border-color: $fuel_yellow;

    &:hover {
        color: $white;
        background-color: $fuel_yellow;
        border-color: $fuel_yellow;
    }

    &:disabled {
        color: $white;
        border-color: $fuel_yellow;
        background-color: $fuel_yellow;
        opacity: 0.5;
    }
}

.timesheet-toggle-btn.btn-link {
    background-color: transparent !important;

    &:hover {
        background-color: transparent !important;
    }

    &:focus {
        box-shadow: none !important;
    }
}
.btn-lime-green {
    color: $white;
    background-color: $lime_green;
    border-color: $lime_green;

    &:hover {
        color: $white;
        background-color: $lime_green;
        border-color: $lime_green;
    }

    &:disabled {
        color: $white;
        border-color: $lime_green;
        background-color: $lime_green;
        opacity: 0.5;
    }
}
.custom-switch{
    .custom-control-label {
        &::after {
            left: calc(-2.25rem - 1px) !important;
            width: 16px !important;
            height: 16px !important;
            border-radius: 3rem !important;
            top: 3px;
            background-color: white !important;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            cursor: pointer;
        }

        &::before {
            left: -2.25rem;
            width: 32px !important;
            pointer-events: all;
            border-radius: 0.5rem !important;
            background-color: $light-silver;
            height: 11px !important;
            top: 6px;
            border: none;
            box-shadow: none !important;
            cursor: pointer;
        }
    }

    .custom-control-input:checked ~ .custom-control-label::after {
        background-color: #fff;
        -webkit-transform: translateX(1.1rem) !important;
        transform: translateX(1.1rem) !important;
    }

    .custom-control-input:checked ~ .custom-control-label::before {
        color: #fff;
        //border: none;
        background-color: variables.$success-green-color;
        border-color: variables.$success-green-color;
    }

    .custom-control-input:not(:disabled):active ~ .custom-control-label::before {
        background-color: darken(variables.$success-green-color, 7.5%);
        border-color: darken(variables.$success-green-color, 7.5%);
    }

    .custom-control-input:disabled:checked ~ .custom-control-label::before {
        background-color: variables.$success-green-color;
        opacity: 0.5;
        cursor: default;
    }
    .custom-control-label:disabled  ~ .custom-control-label::after{
        cursor: default;
    }
}


.custom-control-input:focus {
	border: none !important;
}

.custom-access-level.table-bordered td:nth-child(2),
.custom-access-level.table-bordered td:nth-child(3) {
	padding-top: 0.75rem;
}


.custom-dropdown .dropdown{
    .btn-outline-secondary.dropdown-toggle {
        color: variables.$primary-text-color;
        background-color: white;
        &:hover{
           background-color: white;
        }
        &:active{
            background-color: transparent;
        }
        &:focus{
            box-shadow: none;
        }
    }
    
    }
.custom-dropdown .dropdown{
    .btn{
        border: 1px solid #ccc;;
    }
    .btn-outline-secondary.dropdown-toggle::after{
        margin-top: 0.4rem;
        color: variables.$primary-text-color;
    }
    .btn-outline-secondary.dropdown-toggle{
        color: variables.$primary-text-color;
    }
}

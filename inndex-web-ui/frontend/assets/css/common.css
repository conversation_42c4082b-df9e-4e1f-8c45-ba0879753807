html {
    position: relative;
    min-height: 100%;
}
body {
    /* Margin bottom by footer height */
    /* background: #f7f9fa; */
    background: #FFFFFF;
    /* padding-top: 56px !important; */
    /*padding-bottom: 40px !important;*/
    font-family: "<PERSON><PERSON>","<PERSON><PERSON>", "Open Sans", <PERSON><PERSON>, sans-serif;
}
.footer {
    border-top: 1px solid #eee;
    /*position: absolute;*/
    bottom: 0;
    width: 100%;
    /* Set the fixed height of the footer here */
    height: 40px;
    line-height: 40px; /* Vertically center the text there */
    background-color: #f5f5f5;
}
.footer-logged-in{
    bottom: -40px;
    position: absolute;
}

.full-height {
    min-height: 100vh;
}
body .container,body .container-fluid {
    padding: 20px 15px 0;
    margin-bottom: 120px;

}

.footer > .container {
    padding-right: 15px;
    padding-left: 15px;
}

body .center-align-page {
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -ms-flex-align: center;
    -ms-flex-pack: center;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    /*padding-top: 40px;*/
    padding-bottom: 40px;
    /*background-color: #f5f5f5;*/
    min-height: 100%;
}
.dot-dropdown{
    cursor: pointer;
    /* top: 10px;
    right: 8px; */
}
.dot-dropdown .dropdown-toggle::after{
    display: none;
}
.btn-delete:not([disabled]) {
    background-color: #fff;
    color: var(--danger);
    cursor: pointer;
    /*border-color: #357ebd;*/ /*set the color you want here*/
}
.btn-delete:not([disabled]):hover,
.btn-delete:not([disabled]):focus,
.btn-delete:not([disabled]):active,
.btn-delete:not([disabled]).active,
.open>.dropdown-toggle.btn-delete:not([disabled]) {
    color: #fff;
    background-color: var(--danger);
    border-color: var(--danger); /*set the color you want here*/
}
.cursor-pointer {
    cursor: pointer;
}
.cursor-default { cursor: default !important; }
.adjusted-time{
    vertical-align: text-bottom;
}
.lds-roller {
    position: absolute;
    overflow: hidden;
    -webkit-animation-delay: 1s;
    animation-delay: 1s;
    height: 64px;
    width: 64px;
    top: 50%;
    left: 50%;
    /*
    margin-left: -32px;
    margin-top: -32px;
    */
    transform: translate(-50%, -50%);
}
.lds-roller div {
    animation: lds-roller 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    transform-origin: 32px 32px;
}
.lds-roller div:after {
    content: " ";
    display: block;
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #edb61d;
    margin: -3px 0 0 -3px;
}
.lds-roller div:nth-child(1) {
    animation-delay: -0.036s;
}
.lds-roller div:nth-child(1):after {
    top: 50px;
    left: 50px;
}
.lds-roller div:nth-child(2) {
    animation-delay: -0.072s;
}
.lds-roller div:nth-child(2):after {
    top: 54px;
    left: 45px;
}
.lds-roller div:nth-child(3) {
    animation-delay: -0.108s;
}
.lds-roller div:nth-child(3):after {
    top: 57px;
    left: 39px;
}
.lds-roller div:nth-child(4) {
    animation-delay: -0.144s;
}
.lds-roller div:nth-child(4):after {
    top: 58px;
    left: 32px;
}
.lds-roller div:nth-child(5) {
    animation-delay: -0.18s;
}
.lds-roller div:nth-child(5):after {
    top: 57px;
    left: 25px;
}
.lds-roller div:nth-child(6) {
    animation-delay: -0.216s;
}
.lds-roller div:nth-child(6):after {
    top: 54px;
    left: 19px;
}
.lds-roller div:nth-child(7) {
    animation-delay: -0.252s;
}
.lds-roller div:nth-child(7):after {
    top: 50px;
    left: 14px;
}
.lds-roller div:nth-child(8) {
    animation-delay: -0.288s;
}
.lds-roller div:nth-child(8):after {
    top: 45px;
    left: 10px;
}
@keyframes lds-roller {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
.block-backdrop{
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100%;
    height: 100%;
    background-color: transparent;
}

.no-caret .dropdown-toggle::after{
    content: none;
}

ngx-duration-picker input[_ngcontent-c1]{
    min-width: 36px;
}

form .alert.alert-danger {
    font-size: 80%;
    font-weight: 400;
}
.input-group .alert{
    flex-basis: 100%;
}
.faded{
    opacity: 0.6;
}
.brand-logo{
    max-width: 100px;
    height: 61px;
    max-height: 61px;
    /*margin-right: -16px;*/
}
.im-modal .modal-content{
    overflow-y: initial !important;
}
.im-modal .modal-body{
    max-height: calc(100vh - 170px);
    overflow-y: scroll;
}
.custom-control.custom-checkbox.p-right{padding-left: 0;}

.btn-action {
    background-color: var(--bright-gray);
    box-shadow: none;
}

.btn-action:hover {
    background-color: var(--light-gray);
    transition: background-color 0.2s ease;
}

.img-icon {
    height: 14px;
}
.delete-icon {
    height: 20px;
}
.font-larger {
    font-size: larger;
}
.w-70px{
    width: 70px;
}
.w-100px{
    width: 100px;
}
.p-right label.custom-control-label {
    position: relative;
    padding-right: 1.5rem;
}

.p-right label.custom-control-label::before,
.p-right label.custom-control-label::after{
    right: 0;
    left: auto;
}
.card .project-img{
    max-height: 100px;
    min-height: 100px;
    object-fit: contain;
    text-align: center;
    color: #ccc;
    padding: 10px 20px;
}
img.card-img-top.site_risks-img {
    max-width: 16px;
    padding: 2px;
}

/* Datatable & Search */

.ngx-datatable{
    overflow: inherit !important;
}
input.ngx-search[type=text],input.ngx-search[type=search] {
    font-size: 14px;
    display: block;
    background: transparent;
    border: none;
    border-bottom: 1px solid #999;
    padding: 8px;
}
input.ngx-search:focus {
    outline: none;
}
input[type="search"]::-webkit-search-decoration:hover,
input[type="search"]::-webkit-search-cancel-button:hover {
    cursor:pointer;
}
.footer-inner { width: 80%; margin: 0 auto; }
.footer-custom { height: 58px; line-height: 53px; }

.memberTimeDetail .datatable-body{
    max-height: 200px;
    overflow: auto;
}

@media (max-width: 1024px) {
    .font-md-small{
        font-size: small;
    }
}
@media (max-width: 767px) {
    .footer { height: auto; }

    .footer-inner { text-align: center; width: auto; }

    .footer-custom {
         height: 85px;
         line-height: 26px;
         font-size: 14px;
    }
}

.timeManagementTable.ngx-datatable.bootstrap .datatable-body .datatable-body-row .datatable-body-cell {
    padding: 0.60rem;
}
.visitorsTable.ngx-datatable .datatable-header{
    border-bottom: 1px solid #d1d4d7;
    height: 40px !important;
}

/* .inviteToInductionBtn {margin-bottom: 10px;} */

.viewProjectTabs .nav-tabs .nav-item, .viewProjectChildTabs .nav-tabs { display: none; }

.form-container-addons { max-width: 800px; margin: 0 auto; }
.timeSlotSelected { background-color: var(--success) !important; }
.timeSlotUnselected { background-color: #F37D5A; }
.timeSlotUnavailable { background-color: #D3D3D3; }
.timeSlotBooked { background-color: var(--danger); }
.gateContainer .table-dark td, .table-dark td, .table-dark thead td { padding: 0px; vertical-align: bottom;}
.gateContainer .table-dark td div, .table-dark td div, .table-dark thead td div { padding: 0.95rem; cursor: pointer;}
.gateContainer .table-dark td.timeSlots { width: 95px;}
.addMoreSlots i { margin-left: 18rem !important; }
.deliveryMgmtHead .custom-control.custom-checkbox  { padding-left: 0; }
.deliveryMgmtHead label.custom-control-label { position: relative; padding-right: 1.5rem; }
.deliveryMgmtHead label.custom-control-label::before, .deliveryMgmtHead label.custom-control-label::after { right: 0; left: auto; }
.fullDate { position: relative; bottom: 5px; }

.app-side-nav, .company-side-nav {
    background-color: var(--primary);
    padding-top: 25px;
    min-height: 100vh;
    position: fixed;
    z-index: 1031;
    margin-top: 64px;
}

.portalSelector button.btn.btn-primary {
    min-width: 141px;
}

.companyPortalBtnGroup {
    position: fixed;
    bottom: 15px;
    width: inherit;
    padding: 0 15px;
}

/* .companyPortalBtnGroup button {
    background-color: #14152D;
    border-top: 1px solid #26263b;
    border-bottom: 1px solid #26263b;
    font-weight: bold;
} */

.employerGridActions .btn-sm { padding: 2px 6px; }
ul#projectList { max-height: 400px; overflow-y: auto; padding-bottom: 50px; border-left: none;}
.siteAdminSideBar .list-sidebar { max-height: 91vh; overflow-y: auto; }
.siteAdminSideBar { width: 220px !important; }

.list-sidebar::-webkit-scrollbar-track
{
	-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
	background-color: #F5F5F5;
}

.list-sidebar::-webkit-scrollbar
{
	width: 5px;
	background-color: #F5F5F5;
}

.list-sidebar::-webkit-scrollbar-thumb
{
	background-color: #F90;
	background-image: -webkit-linear-gradient(45deg,
	                                          rgba(255, 255, 255, .2) 25%,
											  transparent 25%,
											  transparent 50%,
											  rgba(255, 255, 255, .2) 50%,
											  rgba(255, 255, 255, .2) 75%,
											  transparent 75%,
											  transparent)
}
button#fingerprintEnrolmentBtn:hover svg#Capa_1 { fill:white; }
.fingerPrint { cursor: pointer; fill:grey;}
.svgGreen { fill: var(--success) !important; }
.svgBlack { fill:black !important; }
.svgRed { fill: var(--danger) !important; }
.inductionPaymentDetail { font-size: .9em; }
.inductionPaymentDetail label { font-size: 14px;}
.weekCommencingTable { text-align: center; }
.progressPhotosTable td { vertical-align: middle; }
.disable-text-select {
    user-select: none; /* supported by Chrome and Opera */
    -webkit-user-select: none; /* Safari */
    -khtml-user-select: none; /* Konqueror HTML */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
}
.disabled-option{
    color: #cccccca6;
}
.option-padding {
    padding: 14px 20px;
}
.required-asterisk {
    color: var(--danger);
    font-size: 1rem;
    margin-left: 1px;
}

.routeMapIcon { position: relative; bottom: 2px; }

.nav-a {
    color:grey;
}
.n-tab {
    width: 100%;
}

.n-tab .nav-a {
    background-color: #eaeced;
}

.n-tab .nav-a:hover {
    color: grey;
}

.nav-tabs .nav-link.active:not(.default) {
    font-weight: bold;
    background-color: #f7f9fa;
    color:black;
    border-color: #dee2e6 #dee2e6 #f7f9fa;
}

.nav-tabs .nav-link:not(.default) {
    border: 1px solid transparent;
    border-top-left-radius: 0.65rem;
    border-top-right-radius: 0.65rem;
}

.nav-panel {
    border: 1px solid #dee2e6;
    border-top: none;
    margin-top: 0px !important;
}

.nav-item {
    padding-right: 15px;
}

.navtab-screen {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
}

@media (max-width: 1300px) {
    .navtab-screen {
        padding-left: 3rem !important;
    }
}


.inspectionsNavBtn { border-radius: 20px; padding: 2px 10px; }
.activeBtnHeader { color: #212529 !important; background-color: #edb61d !important; border-color: #edb61d !important; }
.companyProjectForm { max-width: 565px !important; }
.position-relative { position: relative; }
.vertical-center { margin: 0; position: absolute; top: 50%; -ms-transform: translateY(-50%); transform: translateY(-50%); right: 0px; }
.vehicleImagePlaceholder { background-color: #0388d1;display: table-cell;vertical-align:middle;text-align:center;padding: 30px 0px; }
.vertical-align-middle { vertical-align: middle !important; }
.dd-wo-caret .dropdown-toggle::after {
    display:none;
}
/* IE 11 fixes for SVG */
.sidebar-icon svg{
    max-height: 30px;
}
.ngx-datatable .btn svg{
    max-height: 22px;
}
.stepwizard-step svg{
    max-height: 16px;
}
.ngx-datatable .datatable-footer{
    overflow: inherit !important;
}

.ngb-dp-day .bg-primary{

    color: black !important;
    background-color: rgb(237, 182, 29) !important;
    border-color: rgb(237, 182, 29) !important;
}
.unsatisfactoryItemsStatus {
    border-radius: 20px;
    padding: 1px 8px;
    font-size: 13px;
    /*display: block;*/
    width: 105px;
    color: #fff;
    text-align: center;
    border-style: solid;
}
.unsatisfactoryItemsStatus:focus { outline: none; }

.rounded-select {
    font-size: 14px
}
.rounded-select .ng-select-container {
    border-radius: 20px !important;
}
.filter-select .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
    cursor: pointer;
}
.filter-select.no-padding{
    padding: 0;
}

.filter-select .ng-dropdown-panel {
    left: 5%;
    width: 90%;
    border-radius: 14px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.filter-select .ng-dropdown-panel .scroll-host {
    border-radius: 14px;
}

.filter-select .ng-dropdown-panel.ng-select-bottom {
    top: 112%;
    border-bottom-right-radius: 14px;
    border-bottom-left-radius: 14px;
}

.filter-select .ng-dropdown-panel.ng-select-top {
    bottom: 112%;
}

.filter-select .ng-clear-wrapper {
    width: 12px;
}

.bgSuccess { background-color: var(--success); border-color: var(--success) }
.bgDanger { background-color: var(--danger); border-color: var(--danger) }
.btnYellowRoundedWhite {
    border-radius: 20px;
    color: white;
    padding: 8px 0px;
    background-color: #edb61d;
    border: #edb61d;
}

.btnYellowRoundedWhite:hover {
    background-color: #d3a011;
    border-color: #c79710;
    color: white;
}

.img-file-wrap {
    position: relative;
    display: inline-block;
    font-size: 0;
    text-align: center;
}
.img-file-wrap .delete {
    position: absolute;
    top: -5px;
    right: 5px;
    z-index: 100;
    /* background-color: #FFF; */
    /* padding: 5px 2px 2px; */
    color: var(--danger);
    /* font-weight: bold; */
    cursor: pointer;
    opacity: 1;
    text-align: center;
    font-size: 22px;
    line-height: 10px;
    border-radius: 50%;
}
.img-file-wrap .fa-times-circle:before {
    background: white;
    border-radius: 50%;
}
.modal-full .modal-dialog{
    min-width: 90%;
}
.modal-full .modal-dialog .modal-content {
    min-height: calc(100vh - 58px);
}
.modal-full{
    animation-name: fullscreen-open;
    animation-duration: 0.3s;
}
@keyframes fullscreen-open {
    0%   {transform: scale(0.5)}
    100% {transform: scale(1)}
}
.selectProjectDropdown { width: 400px; }

.dashboardTypeSelector .ng-select-container, .dropdownBtnMakeup {
    padding: 6px 6px;
    border-radius: 10px !important;
    color: #706f6f !important;
    box-shadow: 0 0 6px rgb(216 208 208);
}

.dropdownBtnMakeup span {
    margin: 0% 5%;
}

.dropdownBtnMakeup::after {
    display: none;
}

.customTick.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
    background-image: url(../images/tick.png);
    background-repeat: no-repeat;
    background-size: 12px;
}

.customTick.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
    background-color: transparent;
    border: none;
    box-shadow: 0 0 6px rgb(216 208 208);
}

.customTick { color: #706f6f; }
.carousel-control-next-icon {
    /* background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23000' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e") !important; */
    background-image: url("../images/arrow_right.svg") !important;

}
.carousel-control-prev-icon {
    /* background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23000' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e")!important; */
    background-image: url("../images/arrow_left.svg") !important;
}

.carousel-control-prev, .carousel-control-next {
    width: 5%;
}

.eqpListImg {
    opacity: 0.5;
    filter: alpha(opacity=40);
}

.eqpListImg:hover {
    opacity: 1.0;
    filter: alpha(opacity=100);
}

.eqParentCircle {
    position: relative;
    width: 140px;
    height: 140px;
    margin: 0 auto;
}

.eqBg {
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
}

.eqQuarter {
    position: absolute;
    width: 50%;
    height: 50%;
    transition: background-color 0.2s ease-in-out;
}

.eqQuarter:hover {
    /*background-color: pink;*/
}

.eqQuarter-tl {
    top: 0;
    left: 0;
    background-color: red;
    border-radius: 100% 0 0 0;
}

.eqQuarter-tr {
    top: 0;
    right: -1px;
    background-color: blue;
    border-radius: 0 100% 0 0;
}

.eqQuarter-bl {
    bottom: 0;
    left: 0;
    background-color: orange;
    border-radius: 0 0 0 100%;
}

.eqQuarter-br {
    bottom: -1px;
    right: -1px;
    background-color: green;
    border-radius: 0 0 100% 0;
}

.eq-full-circle {
    background-color: rgb(128, 0, 100);
    border-radius: 100%;
    width: 100%;
    height: 100%;
    position: absolute;
}

.eq-semi-circle-two {
    width: 140px;
    height: 140px;
    display: flex;
    margin: 0 auto;
}

.eq-semi-circle-left {
    background-color: var(--success);
    border-bottom-left-radius: 100px;
    border-top-left-radius: 100px;
    width: 100%;
    height: 100%;
    flex: 1 1 auto!important;
    margin-right: 1px;
}

.eq-semi-circle-right {
    background-color: var(--danger);
    border-bottom-right-radius: 100px;
    border-top-right-radius: 100px;
    width: 100%;
    flex: 1 1 auto!important;
    height: 100%;
}

.tr-bg-dark-color {
    background-color: #0000000d;
}

.take5DetailsModal .modal-dialog, .clerkOfWorksModal .modal-dialog, .sendMessageForm .modal-dialog { max-width: 50%; }
.buildInspectionModal .modal-dialog { min-width: 40%; }
.vehicleDetailModal .modal-dialog { min-width: 85%; }
.bg-warning-yellow { background-color: #FFA500 !important; }
.btn-outline-warning-yellow { border-color: #FFA500; }
.checkbox-small { font-size: 5px; width: 22px; }
.customFieldsManager .modal-dialog { min-width: 50%; }
.scoreField input[type=number]::-webkit-inner-spin-button, .scoreField input[type=number]::-webkit-outer-spin-button {
    opacity: 1
}
.inspectionTourDashboard.modal .modal-dialog { min-width: 67%; }
.taggedOwnerDD.ng-select.ng-select-single .ng-select-container { height: 38px; border-top-right-radius: 0; border-bottom-right-radius: 0; }
.taggedOwnerDD { width: calc(100% - 54px); }
.taggedOwnerSaveDD { width: calc(100% - 60px); }
.greenText { color: var(--success)}
.yellowText { color: #EDB531}
.redText { color: var(--danger) }
.blueText { color: #0b5394}
.bg-yellow { background-color: #EDB531 !important; }
.custom-badge { position: absolute; right: -10px; top: -10px; z-index: 99; }
button.faultBadgeBtn:focus { box-shadow: none; }

/* css for dragula functionality drag and drop effect */
.gu-hide { left: -9999px !important; }
.gu-unselectable {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
}
.gu-transit {
    opacity: 0.2;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=20)";
    filter: alpha(opacity=20);
}
.gu-mirror {
    position: fixed !important;
    margin: 0 !important;
    z-index: 9999 !important;
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
    filter: alpha(opacity=80);
    pointer-events: none;
    box-sizing: border-box;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}
.photo-drag-box {
    margin: 10px;
    padding: 10px 10px;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 5px;
    font-size: 14px;
}

/* inspections-modal-box - CSS */
.heading-list,
.sub-heading-list {
    max-width: 100%;
    border-radius: 4px;
    overflow: auto;
}
.drag-box {
    margin-bottom: 10px;
    align-items: center;
    box-sizing: border-box;
    background: #f8f8f8;
    border-radius: 5px;
    font-size: 18px;
}
.fa-bars, .fa-eye-slash, .fa-eye {
    cursor: grab;
    font-size: 18px;
    color: #cccccc;
    padding: 3px;
}

.fa-eye-slash, .fa-eye {
    cursor: pointer;
}

/* Can be use globally */
.ng-select.custom-arrow .ng-arrow {
    border: solid black;
    border-width: 0 2px 2px 0 !important;
    display: inline-block;
    padding: 3px;
    border-color: #999;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    border-color: #999 !important;
    border-style: solid !important;
    bottom: 5px;
}

.ng-select.custom-arrow.ng-select-opened>.ng-select-container .ng-arrow {
    transform: rotate(-135deg) !important;
    -webkit-transform: rotate(-135deg) !important;
    top: 0px;
}

/* Can be use globally */
.custom-selectbox.ng-touched.ng-valid[required] {
    border-left: none !important;
}

.custom-selectbox.ng-touched.ng-invalid:not(form) {
    border-left: none !important;
}

.custom-selectbox.ng-touched.ng-valid:not(form) .ng-select-container {
    border-left: 5px solid var(--success);
}

.custom-selectbox.ng-touched.ng-invalid:not(form) .ng-select-container {
    border-left: 5px solid var(--danger);
}
.checkbox-none {
    height: 0px;
}
.bulk-actions{
    position: absolute;
    right: 0;
    z-index: 1;
}

/* Pointer event none */
.cursor-pointer-none {
    pointer-events: none;
}

.text-ellipsis { font-size: 14px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }

/* search input v2 */
/* use like :- <input class="search-input outline" type="search"/> */
.outline:focus, .outline:hover { border-color: #B2B2B2; box-shadow: 0 0 0 0.2rem #F6E9C9 !important ; }
.search-input {
    padding-left: 25px;
    background-image: url('/assets/images/search-gray.png');
    background-size: contain;
    background-repeat: no-repeat;
    outline: 0;
    background-position: 12px;
    background-size: 8px;
    border: none;
    background-color: #F2F2F2;

    border-radius: 5px !important;
    height: 33px !important;
    margin: 10px !important;
    font-family: Roboto;
    font-size: 12px;
    font-weight: 400;
    line-height: 12px;
    letter-spacing: 0em;
    text-align: left;
}
input[type="search"].search-input::-webkit-search-cancel-button {
    -webkit-appearance: none;
    height: 1em;
    width: 1em;
    border-radius: 50em;
    background: url("/assets/images/x_location_tag.png") no-repeat 50% 50%;
    background-size: contain;
    opacity: 0;
    pointer-events: none;
}
input[type="search"].search-input:focus::-webkit-search-cancel-button {
    opacity: .3;
    pointer-events: all;
}
/* search-cancel-button X */
input[type="search"]::-webkit-search-cancel-button {
    -webkit-appearance: none;
    height: 1em;
    width: 1em;
    border-radius: 50em;
    background: url('/assets/images/x_close_material_icon.svg') no-repeat 50% 50%;
    /* background: url('/dist/assets/images/x_close_icon.svg') no-repeat 50% 50%; */
}
.modal-header-background { background-color: #f6f6f6; }
.incident-time .ngb-tp-input {
    background-color: #e9ecef;
    opacity: 1;
}
/* Common font-weight css start */
/* .fw-300 { font-weight: 300; } for this use class-> font-weight-light*/
.fw-400 { font-weight: 400; }
.fw-600 { font-weight: 600; }
.fw-500 { font-weight: 500 !important; }
/* font-weight css end */

.dl-horizontal dt { font-weight: 300; }
.dl-horizontal dd { font-weight: 400; }

/* company portal for company-side-nav margin left fix */
.ml-fix {
    margin-left: 215px !important;
}

/* asset management*/
:host ::ng-deep .vehicleImageUploader .alignItems { margin-top: 25px; }
.font-size-small { font-size: 12px; }
.background-black { background-color: #14162b; }
.background-orange { background-color: #ff7f00; }
.background-green { background-color: var(--success); }
.background-red { background-color: var(--danger); }
.text-color-grey { color: #6c757d; }
.border-redias-5 { border-radius: 5px; }
.border-color-black { border: 1px solid #14162b; }
.h-line { border-top: 1px inset #14162b; margin: 0 0.5rem 0 0.5rem; }
.dropdownMenu { position: absolute !important; z-index: 99;}
.border-bottom-none { border-bottom-left-radius: 0px !important; border-bottom-right-radius: 0px !important; }
.pdl-5 { padding-left: 5px; }
.dropdown-mask { top: 0; left: 0; bottom: 0; right: 0; z-index: 9; position: fixed; width: 100vw; height: 100vh; }
.white-line { border-top: 1px solid white; margin: 0px; }
.border-bottom-5 { border-bottom-left-radius: 5px !important; border-bottom-right-radius: 5px !important; }
.custom-padding { padding: 0.3rem !important; }
.date-col-width { width: 160px;}
.td-col-width { width: 60px;}
.no-action { color: var(--light-silver) }
.approved-text { color: var(--success); }
/* create profile steps css */
.heading { font-size: 14px; }
.sub-heading { font-size: 10px; color: var(--spanish-gray); }
.text-quick-silver { color: var(--quick-silver); }
.rounded-10 { border-radius: 10px; }
.ml-fix { margin-left: 250px !important; }
.btn-act-editable {
    border-radius: 3px;
    height: 35px;
    box-shadow: none;
    font-size: 12px;
    width: 60px;
    margin-left: 0.65rem;
    background-color: var(--anti-flash-white);
}
.filter-by-company .modal-dialog {
    top: 23.5%;
}

.filter-by-company .modal-dialog .modal-content {
    border-radius: 0.7rem !important;
}

.inspection-time .ngb-tp-input-container {
    width: 5em;
}
.inspection-time .ngb-tp-input {
    background-color: #F2F2F2;
    opacity: 1;
}
.inspection-time .form-control-sm {
    height: calc(2em + 0.5rem + 2px);
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

/* CSS from nav.component start */
.dropdown-toggle-no-caret::after {
    display: none;
}

.fullscreen-nav .brand-logo {
    /*transform: scale(0.5);*/
    /*transform-origin: 0 0;*/
    /*width: 200%;*/

    height: 30px;
    padding-left: 10px;
    padding-top: 2px;
    padding-bottom: 2px;
}

.fullscreen-nav .pull-right {
    display: none;
}

.generic-confirm-modal-cancel-button.btn-outline-brandeis-blue:hover{
	background-color: transparent !important;
    color: var(--brandeis-blue) !important;
}
.navbar {
    position: fixed;
    padding: 0.1rem 1rem;
}
/* nav.component end */

/* Asset equipment - vehicles - tem-work list view grid view btn */
/* clickable material icon will active and disabled using this class */
.material-icon-disabled {
    background-color: var(--semi-light-gray);
    pointer-events: none;
}
.icon-btn {
    height: 30px;
    width: 30px;
    font-size: 20px;
    color: var(--balck);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
}
.asset-archive-table .datatable-body{
    overflow-x: hidden;
}

.detail-page-header-margin{
    margin-top: 64px;
 }
/* end */
.transform-none {
    transform: none !important;
}

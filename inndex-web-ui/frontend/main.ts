import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import {AppConstant, environment} from './environments/environment';
import {UpdateService} from '@app/core/services/update-available/update.service';

if (environment.production) {
  enableProdMode();
}

platformBrowserDynamic().bootstrapModule(AppModule).then((appRef) => {
  // console.log('AppConstant.developmentEnv', AppConstant.developmentEnv);
  const updateService = appRef.injector.get(UpdateService);
  // updateService.checkForUpdate(AppConstant.updateCheckIntervalMs);
  updateService.unRegisterServiceWorker();
  updateService.clearNgswCache();


}).catch(err => console.log(err));
